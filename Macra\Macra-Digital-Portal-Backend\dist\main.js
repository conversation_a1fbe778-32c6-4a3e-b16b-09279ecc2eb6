"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const app_module_1 = require("./app.module");
const http_exception_filter_1 = require("./common/filters/http-exception.filter");
async function bootstrap() {
    try {
        const app = await core_1.NestFactory.create(app_module_1.AppModule);
        app.enableCors({
            origin: [
                process.env.FRONTEND_URL || 'http://localhost:3000',
                'http://localhost:3002',
                'http://localhost:3003'
            ],
            credentials: true,
            methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
            allowedHeaders: [
                'Content-Type',
                'Authorization',
                'Accept',
                'Origin',
                'X-Requested-With'
            ],
            preflightContinue: false,
            optionsSuccessStatus: 204
        });
        app.useGlobalFilters(new http_exception_filter_1.HttpExceptionFilter());
        app.useGlobalPipes(new common_1.ValidationPipe({
            whitelist: true,
            forbidNonWhitelisted: true,
            transform: true,
            forbidUnknownValues: true,
            exceptionFactory: (errors) => {
                const messages = errors.flatMap(err => {
                    if (err.constraints)
                        return Object.values(err.constraints);
                    return [`Validation failed on property ${err.property}`];
                });
                console.error('Validation failed:', messages);
                return new common_1.BadRequestException(messages.join(', '));
            },
        }));
        const config = new swagger_1.DocumentBuilder()
            .setTitle('MACRA Digital Portal API')
            .setDescription('API documentation for MACRA Digital Portal - Authentication and User Management System')
            .setVersion('1.0')
            .addBearerAuth({
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT',
            name: 'JWT',
            description: 'Enter JWT token',
            in: 'header',
        }, 'JWT-auth')
            .build();
        const document = swagger_1.SwaggerModule.createDocument(app, config, {
            deepScanRoutes: true,
            extraModels: [],
        });
        swagger_1.SwaggerModule.setup('api/docs', app, document, {
            customSiteTitle: 'MACRA API Documentation',
            customfavIcon: '/favicon.ico',
            customCss: '.swagger-ui .topbar { display: none }',
            swaggerOptions: {
                persistAuthorization: true,
                docExpansion: 'none',
                filter: true,
                showExtensions: true,
                showCommonExtensions: true,
                deepLinking: true,
            },
        });
        const port = process.env.PORT || 3001;
        await app.listen(port);
        console.log(`🚀 Application is running on: http://localhost:${port}`);
        console.log(`📚 API Documentation available at: http://localhost:${port}/api/docs`);
    }
    catch (error) {
        console.error('❌ Error starting the application:', error);
        process.exit(1);
    }
}
bootstrap();
//# sourceMappingURL=main.js.map