(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3913],{30159:(e,t,a)=>{"use strict";a.d(t,{k:()=>i});var r=a(10012),s=a(52956);let i={async getApplications(e){var t,a,i;let n=new URLSearchParams;(null==e?void 0:e.page)&&n.append("page",e.page.toString()),(null==e?void 0:e.limit)&&n.append("limit",e.limit.toString()),(null==e?void 0:e.search)&&n.append("search",e.search),(null==e?void 0:e.sortBy)&&n.append("sortBy",e.sortBy),(null==e?void 0:e.sortOrder)&&n.append("sortOrder",e.sortOrder),(null==e||null==(t=e.filters)?void 0:t.licenseTypeId)&&n.append("filter.license_category.license_type_id",e.filters.licenseTypeId),(null==e||null==(a=e.filters)?void 0:a.licenseCategoryId)&&n.append("filter.license_category_id",e.filters.licenseCategoryId),(null==e||null==(i=e.filters)?void 0:i.status)&&n.append("filter.status",e.filters.status);let l=await s.uE.get("/applications?".concat(n.toString()));return(0,r.zp)(l)},async getApplicationsByLicenseType(e,t){let a=new URLSearchParams;(null==t?void 0:t.page)&&a.append("page",t.page.toString()),(null==t?void 0:t.limit)&&a.append("limit",t.limit.toString()),(null==t?void 0:t.search)&&a.append("search",t.search),(null==t?void 0:t.status)&&a.append("filter.status",t.status),a.append("filter.license_category.license_type_id",e);let i=await s.uE.get("/applications?".concat(a.toString()));return(0,r.zp)(i)},async getApplication(e){let t=await s.uE.get("/applications/".concat(e));return(0,r.zp)(t)},async getApplicationsByApplicant(e){let t=await s.uE.get("/applications/by-applicant/".concat(e));return(0,r.zp)(t)},async getApplicationsByStatus(e){let t=await s.uE.get("/applications/by-status/".concat(e));return(0,r.zp)(t)},async updateApplicationStatus(e,t){let a=await s.uE.put("/applications/".concat(e,"/status?status=").concat(t));return(0,r.zp)(a)},async updateApplicationProgress(e,t,a){let i=await s.uE.put("/applications/".concat(e,"/progress?currentStep=").concat(t,"&progressPercentage=").concat(a));return(0,r.zp)(i)},async getApplicationStats(){let e=await s.uE.get("/applications/stats");return(0,r.zp)(e)},async createApplication(e){try{let t=await s.uE.post("/applications",e);return(0,r.zp)(t)}catch(e){throw e}},async updateApplication(e,t){try{let a=await s.uE.put("/applications/".concat(e),t,{timeout:3e4});return(0,r.zp)(a)}catch(e){var a,i,n,l;if("ECONNABORTED"===e.code)throw Error("Request timeout - please try again");if((null==(a=e.response)?void 0:a.status)===400){let t=(null==(l=e.response)||null==(n=l.data)?void 0:n.message)||"Invalid application data";throw Error("Bad Request: ".concat(t))}if((null==(i=e.response)?void 0:i.status)===429)throw Error("Too many requests - please wait a moment and try again");throw e}},async deleteApplication(e){let t=await s.uE.delete("/applications/".concat(e));return(0,r.zp)(t)},async createApplicationWithApplicant(e){try{let t=new Date,a=t.toISOString().slice(0,10).replace(/-/g,""),r=t.toTimeString().slice(0,8).replace(/:/g,""),s=Math.random().toString(36).substr(2,3).toUpperCase(),i="APP-".concat(a,"-").concat(r,"-").concat(s);if(!/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e.user_id))throw Error("Invalid user_id format: ".concat(e.user_id,". Expected UUID format."));return await this.createApplication({application_number:i,applicant_id:e.user_id,license_category_id:e.license_category_id,current_step:1,progress_percentage:0})}catch(e){throw e}},async saveApplicationSection(e,t,a){try{let a=1,r=["applicantInfo","companyProfile","businessInfo","serviceScope","businessPlan","legalHistory","reviewSubmit"].indexOf(t);a=r>=0?r+1:1;let s=Math.min(Math.round(a/6*100),100);await this.updateApplication(e,{progress_percentage:s,current_step:a})}catch(e){throw e}},async getApplicationSection(e,t){try{let a=await s.uE.get("/applications/".concat(e,"/sections/").concat(t));return(0,r.zp)(a)}catch(e){throw e}},async submitApplication(e){try{let t=await s.uE.put("/applications/".concat(e),{status:"submitted",submitted_at:new Date().toISOString(),progress_percentage:100,current_step:7});return(0,r.zp)(t)}catch(e){throw e}},async getUserApplications(){try{let e=await s.uE.get("/applications/user-applications"),t=(0,r.zp)(e),a=[];return(null==t?void 0:t.data)?a=Array.isArray(t.data)?t.data:[]:Array.isArray(t)?a=t:t&&(a=[t]),a}catch(e){throw e}},async saveAsDraft(e,t){try{let a=await s.uE.put("/applications/".concat(e),{form_data:t,status:"draft"});return(0,r.zp)(a)}catch(e){throw e}},async validateApplication(e){try{let e={},t=[];for(let a of["applicantInfo","companyProfile","businessInfo","legalHistory"])e[a]&&0!==Object.keys(e[a]).length||t.push("".concat(a," section is incomplete"));return{isValid:0===t.length,errors:t}}catch(e){throw e}}}},35695:(e,t,a)=>{"use strict";var r=a(18999);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"usePathname")&&a.d(t,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},42441:(e,t,a)=>{Promise.resolve().then(a.bind(a,85415))},85415:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>d});var r=a(95155),s=a(12115),i=a(35695),n=a(73209),l=a(40283),c=a(30159);let o=e=>{var t;let{isOpen:a,onClose:s,application:i,onContinueApplication:n,canContinueApplication:l}=e;if(!a||!i)return null;let c=e=>"draft"===i.status?1===e?"current":"upcoming":"submitted"===i.status?2===e?"error":"upcoming":"evaluation"===i.status?3===e?"error":"upcoming":"approved"===i.status?"complete":"rejected"===i.status?"error":"upcoming";return(0,r.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,r.jsxs)("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,r.jsx)("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:s}),(0,r.jsxs)("div",{className:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:[(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:["draft"==i.status&&(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Application Status"}),(0,r.jsx)("button",{type:"button",title:"Close modal",onClick:s,className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:(0,r.jsx)("i",{className:"ri-close-line text-xl"})})]}),(0,r.jsxs)("div",{className:"mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900 dark:text-gray-100",children:i.application_number}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:(null==(t=i.license_category)?void 0:t.name)||"License Category"})]}),(0,r.jsxs)("div",{className:"relative mb-6",children:[(0,r.jsx)("div",{className:"absolute top-6 left-6 right-6 h-0.5 bg-gray-200 dark:bg-gray-600",children:(0,r.jsx)("div",{className:"h-full bg-primary transition-all duration-500 ".concat("approved"===i.status||4===i.current_step?"w-full":3===i.current_step?"w-2/3":2===i.current_step?"w-1/3":(i.current_step,"w-0"))})}),(0,r.jsx)("div",{className:"relative flex justify-between",children:[{id:1,name:"Draft",description:"Application incomplete",icon:"ri-file-text-line"},{id:2,name:"Submitted",description:"Application received and logged",icon:"ri-file-text-line"},{id:3,name:"Under Review",description:"Being reviewed by MACRA team",icon:"ri-search-line"},{id:4,name:"Evaluation",description:"Technical evaluation in progress",icon:"ri-clipboard-line"},{id:5,name:"Approved",description:"License approved and issued",icon:"ri-check-line"},{id:6,name:"Rejected",description:"License rejected",icon:"ri-check-line"}].map(e=>{let t=c(e.id);return(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)("div",{className:"\n                        w-12 h-12 rounded-full flex items-center justify-center text-sm font-medium border-2 transition-all duration-300\n                        ".concat("complete"===t?"bg-primary border-primary text-white":"current"===t?"bg-primary border-primary text-white animate-pulse":"error"===t?"bg-red-500 border-red-500 text-white":"bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-400","\n                      "),children:(0,r.jsx)("i",{className:e.icon})}),(0,r.jsxs)("div",{className:"mt-3 text-center",children:[(0,r.jsx)("div",{className:"text-sm font-medium ".concat("complete"===t||"current"===t?"text-gray-900 dark:text-gray-100":"text-gray-500 dark:text-gray-400"),children:e.name}),(0,r.jsx)("div",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1 max-w-20",children:e.description})]})]},e.id)})})]}),(0,r.jsx)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"Current Status"}),(0,r.jsx)("p",{className:"font-medium text-gray-900 dark:text-gray-100",children:i.status.replace("_"," ").toUpperCase()})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"Progress"}),(0,r.jsxs)("p",{className:"font-medium text-gray-900 dark:text-gray-100",children:[i.progress_percentage||0,"% Complete"]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"Submitted"}),(0,r.jsx)("p",{className:"font-medium text-gray-900 dark:text-gray-100",children:i.submitted_at?new Date(i.submitted_at).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}):new Date(i.created_at).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"Estimated Time"}),(0,r.jsx)("p",{className:"font-medium text-gray-900 dark:text-gray-100",children:"30-45 business days"})]})]})})]}),(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,r.jsx)("button",{type:"button",onClick:s,className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm",children:"Close"}),i&&l&&n&&l(i)&&(0,r.jsxs)("button",{type:"button",onClick:()=>{n(i),s()},className:"w-full inline-flex justify-center rounded-md border border-blue-300 shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mr-3 sm:w-auto sm:text-sm",children:[(0,r.jsx)("i",{className:"ri-edit-line mr-2"}),"Continue Application"]})]})]})]})})},d=()=>{let{isAuthenticated:e,user:t}=(0,l.A)(),a=(0,i.useRouter)(),d=(0,i.useSearchParams)(),[p,u]=(0,s.useState)([]),[m,x]=(0,s.useState)(!0),[g,y]=(0,s.useState)(null),[h,b]=(0,s.useState)("all"),[f,v]=(0,s.useState)(null),[j,w]=(0,s.useState)(!1),[N,k]=(0,s.useState)(!1);(0,s.useEffect)(()=>{if("true"===d.get("submitted")){k(!0);let e=new URL(window.location.href);e.searchParams.delete("submitted"),window.history.replaceState({},"",e.toString()),setTimeout(()=>k(!1),5e3)}},[d]);let _=[{label:"Dashboard",href:"/customer"},{label:"My Licenses",href:"/customer/my-licenses"}],A=e=>{v(e),w(!0)},S=async e=>{if(y(null),!e||!e.license_category_id)return void y("Unable to continue application: License category information is missing. Please contact support.");let t="/customer/applications/apply/applicant-info?license_category_id=".concat(e.license_category_id,"&application_id=").concat(e.application_id);a.push(t)},C=e=>["draft"].includes(e.status),E=(0,s.useCallback)(async()=>{x(!0),y(null);try{let e=await c.k.getUserApplications(),t=(Array.isArray(e)?e:[]).map(e=>{var t;return{...e,status:e.status||"draft",progress_percentage:e.progress_percentage||0,current_step:e.current_step||1,application_number:e.application_number||"APP-".concat(null==(t=e.application_id)?void 0:t.slice(0,8)),license_category:e.license_category?{...e.license_category,name:e.license_category.name||"License Category",description:e.license_category.description||"Category description"}:{name:"License Category",description:"Category description"}}});t.length>0&&t.reduce((e,t)=>(e[t.status]=(e[t.status]||0)+1,e),{}),u(t)}catch(i){var e,t,r,s;if((null==(e=i.response)?void 0:e.status)===404)u([]),y(null);else if((null==(t=i.response)?void 0:t.status)===401){y("Authentication required. Please log in again."),a.push("/customer/auth/login");return}else y((null==(s=i.response)||null==(r=s.data)?void 0:r.message)||i.message||"Failed to fetch applications")}finally{x(!1)}},[a,t]);if((0,s.useEffect)(()=>{E()},[a,E]),m)return(0,r.jsx)(n.A,{breadcrumbs:_,children:(0,r.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading your licenses..."})]})})});if(g)return(0,r.jsx)(n.A,{breadcrumbs:_,children:(0,r.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-red-600 dark:text-red-400 mb-4",children:(0,r.jsx)("i",{className:"ri-error-warning-line text-4xl"})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Failed to load applications"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:g}),(0,r.jsx)("button",{type:"button",onClick:()=>{setTimeout(()=>E(),500)},className:"bg-primary text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors",children:"Try Again"})]})})});let P=p.filter(e=>"all"===h||("in_progress"===h?C(e):e.status===h));[...new Set(p.map(e=>e.status))];let D=e=>{let t={draft:{color:"bg-blue-100 text-blue-800",label:"Draft"},submitted:{color:"bg-blue-100 text-blue-800",label:"Submitted"},under_review:{color:"bg-yellow-100 text-yellow-800",label:"Under Review"},evaluation:{color:"bg-purple-100 text-purple-800",label:"Evaluation"},approved:{color:"bg-green-100 text-green-800",label:"Approved"},rejected:{color:"bg-red-100 text-red-800",label:"Rejected"}},a=t[e]||t.submitted;return(0,r.jsx)("span",{className:"px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ".concat(a.color),children:a.label})};return(0,r.jsxs)(n.A,{breadcrumbs:_,children:[(0,r.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-gray-100",children:"My Licenses"}),(0,r.jsx)("p",{className:"mt-2 text-gray-600 dark:text-gray-400",children:"Track your license applications and manage your approved licenses."})]})}),N&&(0,r.jsx)("div",{className:"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"ri-check-circle-line text-green-600 dark:text-green-400 text-xl mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-green-800 dark:text-green-200",children:"Application Submitted Successfully!"}),(0,r.jsx)("p",{className:"text-sm text-green-700 dark:text-green-300 mt-1",children:"Your license application has been submitted and is now under review. You will receive email notifications about status updates."})]}),(0,r.jsx)("button",{onClick:()=>k(!1),className:"ml-auto text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200",children:(0,r.jsx)("i",{className:"ri-close-line text-lg"})})]})}),(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4",children:"Filter Applications"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:[{key:"all",label:"All Applications"},{key:"in_progress",label:"In Progress"},{key:"submitted",label:"Submitted"},{key:"under_review",label:"Under Review"},{key:"evaluation",label:"Evaluation"},{key:"approved",label:"Approved"},{key:"rejected",label:"Rejected"},{key:"draft",label:"Draft"}].map(e=>(0,r.jsx)("button",{type:"button",onClick:()=>b(e.key),className:"px-4 py-2 rounded-lg text-sm font-medium transition-colors ".concat(h===e.key?"bg-primary text-white":"bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"),children:e.label},e.key))})]}),(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700",children:[(0,r.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-700",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:"Applications Overview"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:"View and track all your license applications"})]}),0===P.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"text-gray-400 dark:text-gray-500 mb-4",children:(0,r.jsx)("i",{className:"ri-file-list-line text-4xl"})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"No applications found"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"all"===h?"You haven't submitted any license applications yet.":'No applications with status "'.concat(h.replace("_"," "),'" found.')})]}):(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[(0,r.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Application Details"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"License Category"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Progress"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Submitted"}),(0,r.jsx)("th",{scope:"col",className:"relative px-6 py-3",children:(0,r.jsx)("span",{className:"sr-only",children:"Actions"})})]})}),(0,r.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:P.map(e=>{var t,a;return(0,r.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-3",children:(0,r.jsx)("i",{className:"ri-file-text-line text-blue-600 dark:text-blue-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:e.application_number}),(0,r.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Application ID: ",e.application_id.slice(0,8),"..."]})]})]})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:(null==(t=e.license_category)?void 0:t.name)||"License Category"}),(0,r.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:(null==(a=e.license_category)?void 0:a.description)||"Category description"})]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:D(e.status)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2 relative overflow-hidden",children:(0,r.jsx)("div",{className:"".concat(C(e)?"bg-blue-500":"bg-primary"," h-2 rounded-full transition-all duration-300 absolute top-0 left-0 ").concat((e.progress_percentage||0)>=100?"w-full":(e.progress_percentage||0)>=75?"w-3/4":(e.progress_percentage||0)>=50?"w-1/2":(e.progress_percentage||0)>=25?"w-1/4":(e.progress_percentage||0)>0?"w-1/12":"w-0")})}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsxs)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:[e.progress_percentage||0,"%"]}),C(e)&&(0,r.jsx)("span",{className:"text-xs text-blue-600 dark:text-blue-400 font-medium",children:"In Progress"})]})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:e.submitted_at?new Date(e.submitted_at).toLocaleDateString():new Date(e.created_at).toLocaleDateString()})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-2",children:[(0,r.jsxs)("button",{type:"button",title:"Continue working on this application",onClick:()=>A(e),className:"inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full hover:bg-blue-200 transition-colors",children:[(0,r.jsx)("i",{className:"ri-eye-line"}),"View Continue"]}),"approved"===e.status&&(0,r.jsx)("button",{type:"button",title:"Download license",className:"text-primary hover:text-red-700 transition-colors",children:(0,r.jsx)("i",{className:"ri-download-line"})})]})})]},e.application_id)})})]})})]})]}),(0,r.jsx)(o,{isOpen:j,onClose:()=>{v(null),w(!1)},application:f,onContinueApplication:S,canContinueApplication:C})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[8122,6766,6874,283,3209,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(42441)),_N_E=e.O()}]);