"use strict";exports.id=88,exports.ids=[88],exports.modules={70088:(e,r,a)=>{a.d(r,{A:()=>i});var t=a(60687),s=a(43210),d=a.n(s);let l=({meta:e,onPageChange:r,onPageSizeChange:a,showFirstLast:s=!0,showPageSizeSelector:l=!0,showInfo:i=!0,maxVisiblePages:n=7,pageSizeOptions:g=[10,25,50,100],className:o=""})=>{let{currentPage:x,totalPages:c,totalItems:y,itemsPerPage:m}=e;if(c<=1&&!l&&!i)return null;let h=y>0?(x-1)*m+1:0,b=Math.min(x*m,y),p=(()=>{let e=[];if(c<=n){for(let r=1;r<=c;r++)e.push(r);return e}e.push(1);let r=Math.floor((n-3)/2),a=Math.max(2,x-r),t=Math.min(c-1,x+r);x<=r+2&&(a=2,t=Math.min(c-1,n-1)),x>=c-r-1&&(a=Math.max(2,c-n+2),t=c-1),a>2&&e.push("...");for(let r=a;r<=t;r++)e.push(r);return t<c-1&&e.push("..."),c>1&&e.push(c),e})(),u=e=>{"number"==typeof e&&e!==x&&r(e)};return(0,t.jsxs)("div",{className:`flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0 px-6 py-3 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 ${o}`,children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-4",children:[i&&y>0&&(0,t.jsxs)("div",{className:"text-sm text-gray-700 dark:text-gray-300",children:["Showing ",(0,t.jsx)("span",{className:"font-medium",children:h})," to"," ",(0,t.jsx)("span",{className:"font-medium",children:b})," of"," ",(0,t.jsx)("span",{className:"font-medium",children:y})," results"]}),l&&a&&(0,t.jsx)("div",{className:"flex items-center space-x-2",children:(0,t.jsx)("select",{value:m,onChange:e=>{let r=parseInt(e.target.value);a&&a(r)},className:"px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",children:g.map(e=>(0,t.jsxs)("option",{value:e,children:[e," per page"]},e))})})]}),c>1&&(0,t.jsxs)("nav",{className:"flex items-center space-x-1","aria-label":"Pagination",children:[s&&x>1&&(0,t.jsx)("button",{onClick:()=>{1!==x&&r(1)},className:"inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-md hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300","aria-label":"Go to first page",children:(0,t.jsx)("i",{className:"ri-skip-back-line"})}),(0,t.jsx)("button",{onClick:()=>{x>1&&r(x-1)},disabled:1===x,className:`inline-flex items-center px-2 py-2 text-sm font-medium border ${1===x?"text-gray-300 bg-gray-100 border-gray-300 cursor-not-allowed dark:bg-gray-700 dark:border-gray-600 dark:text-gray-500":"text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300"} ${s&&x>1?"":"rounded-l-md"}`,"aria-label":"Go to previous page",children:(0,t.jsx)("i",{className:"ri-arrow-left-s-line"})}),p.map((e,r)=>(0,t.jsx)(d().Fragment,{children:"..."===e?(0,t.jsx)("span",{className:"inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400",children:"..."}):(0,t.jsx)("button",{onClick:()=>u(e),className:`inline-flex items-center px-4 py-2 text-sm font-medium border ${e===x?"text-white bg-red-600 border-red-600 hover:bg-red-700":"text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300"}`,"aria-label":`Go to page ${e}`,"aria-current":e===x?"page":void 0,children:e})},r)),(0,t.jsx)("button",{onClick:()=>{x<c&&r(x+1)},disabled:x===c,className:`inline-flex items-center px-2 py-2 text-sm font-medium border ${x===c?"text-gray-300 bg-gray-100 border-gray-300 cursor-not-allowed dark:bg-gray-700 dark:border-gray-600 dark:text-gray-500":"text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300"} ${s&&x<c?"":"rounded-r-md"}`,"aria-label":"Go to next page",children:(0,t.jsx)("i",{className:"ri-arrow-right-s-line"})}),s&&x<c&&(0,t.jsx)("button",{onClick:()=>{x!==c&&r(c)},className:"inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-md hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300","aria-label":"Go to last page",children:(0,t.jsx)("i",{className:"ri-skip-forward-line"})})]})]})};function i({columns:e,data:r,loading:a=!1,onQueryChange:d,searchPlaceholder:i="Search...",className:n=""}){let[g,o]=(0,s.useState)({page:1,limit:10,search:"",sortBy:[]}),[x,c]=(0,s.useState)("");(0,s.useCallback)(e=>{try{let r={...g,search:e,page:1};o(r),d(r)}catch(e){}},[g,d]);let y=e=>{let r=g.sortBy?.find(r=>r.startsWith(e)),a=[];a=r?r.endsWith(":ASC")?[`${e}:DESC`]:[]:[`${e}:ASC`];let t={...g,sortBy:a,page:1};o(t),d(t)},m=e=>{let r=g.sortBy?.find(r=>r.startsWith(e));return r?r.endsWith(":ASC")?"asc":"desc":null};return r?(0,t.jsxs)("div",{className:`bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden ${n}`,children:[(0,t.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)("i",{className:"ri-search-line text-gray-400 dark:text-gray-500"})}),(0,t.jsx)("input",{type:"text",placeholder:i,value:x,onChange:e=>c(e.target.value),className:"block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-gray-900 dark:text-gray-100"})]})}),(0,t.jsx)("div",{className:"overflow-x-auto",style:{minHeight:"500px"},children:(0,t.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[(0,t.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-900",children:(0,t.jsx)("tr",{children:e.map(e=>(0,t.jsx)("th",{className:`px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${e.sortable?"cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800":""} ${e.className||""}`,onClick:()=>e.sortable&&y(String(e.key)),children:(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{children:e.label}),e.sortable&&(0,t.jsxs)("div",{className:"flex flex-col",children:[(0,t.jsx)("i",{className:`ri-arrow-up-s-line text-xs ${"asc"===m(String(e.key))?"text-red-600":"text-gray-400"}`}),(0,t.jsx)("i",{className:`ri-arrow-down-s-line text-xs -mt-1 ${"desc"===m(String(e.key))?"text-red-600":"text-gray-400"}`})]})]})},String(e.key)))})}),(0,t.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:a?(0,t.jsx)("tr",{children:(0,t.jsx)("td",{colSpan:e.length,className:"px-6 py-4 text-center text-gray-500 dark:text-gray-400",children:(0,t.jsxs)("div",{className:"flex items-center justify-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-red-600"}),(0,t.jsx)("span",{className:"ml-2",children:"Loading..."})]})})}):r?.data&&0!==r.data.length?r.data.map((r,a)=>(0,t.jsx)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:e.map(e=>(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100",children:e.render?e.render(r[e.key],r):String(r[e.key]||"")},String(e.key)))},a)):(0,t.jsx)("tr",{children:(0,t.jsx)("td",{colSpan:e.length,className:"px-6 py-4 text-center text-gray-500 dark:text-gray-400",children:"No data found"})})})]})}),r?.meta&&(0,t.jsx)(l,{meta:r.meta,onPageChange:e=>{let r={...g,page:e};o(r),d(r)},onPageSizeChange:e=>{let r={...g,limit:e,page:1};o(r),d(r)},showFirstLast:!0,showPageSizeSelector:!0,showInfo:!0,maxVisiblePages:7,pageSizeOptions:[10,25,50,100]})]}):(0,t.jsx)("div",{className:`bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden ${n}`,children:(0,t.jsx)("div",{className:"p-6 text-center text-gray-500 dark:text-gray-400",children:(0,t.jsxs)("div",{className:"flex items-center justify-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-red-600"}),(0,t.jsx)("span",{className:"ml-2",children:"Loading..."})]})})})}}};