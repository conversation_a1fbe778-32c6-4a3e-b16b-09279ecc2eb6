import { Metadata } from 'next'
import Sidebar from '../../components/Sidebar'
import Header from '../../components/Header'

export const metadata: Metadata = {
  title: 'Task Assignment - MACRA Digital Portal',
  description: 'Assign various types of tasks to officers for processing and evaluation',
}

export default function TaskAssignmentLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="flex h-screen">
        {/* Sidebar */}
        <Sidebar />

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Header */}
          <Header />

          {/* Page Content */}
          <main className="flex-1 overflow-y-auto">
            {children}
          </main>
        </div>
      </div>
    </div>
  )
}