(()=>{var e={};e.id=9344,e.ids=[9344],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},14195:(e,r,t)=>{Promise.resolve().then(t.bind(t,58886))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44513:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>l});var s=t(65239),a=t(48088),n=t(88170),i=t.n(n),o=t(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(r,d);let l={children:["",{children:["customer",{children:["auth",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,58886)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\auth\\login\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,68736)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\auth\\login\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/customer/auth/login/page",pathname:"/customer/auth/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},51147:(e,r,t)=>{Promise.resolve().then(t.bind(t,92696))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58886:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\auth\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\auth\\login\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68736:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n,metadata:()=>a});var s=t(37413);let a={title:"Customer Dashboard - Digital Portal",description:"Customer portal for managing licenses and applications"};function n({children:e}){return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50",children:e})}},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"256x256",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},92696:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>m});var s=t(60687),a=t(43210),n=t(16189),i=t(85814),o=t.n(i),d=t(30474),l=t(63213),c=t(71773);function u(){let[e,r]=(0,a.useState)(""),[t,i]=(0,a.useState)(""),[u,m]=(0,a.useState)(!1),[x,g]=(0,a.useState)(!1),[p,h]=(0,a.useState)(""),[f,v]=(0,a.useState)(""),[b,y]=(0,a.useState)({}),[j,w]=(0,a.useState)(!1),[k,N]=(0,a.useState)(!1),{login:P,isAuthenticated:C,loading:q}=(0,l.A)(),L=(0,n.useRouter)();(0,n.useSearchParams)();let M=e=>e.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)?null:"Please enter a valid email address":"Email address is required",R=e=>e?e.length<8?"Password must be at least 8 characters long":null:"Password is required",S=()=>{let r=M(e),s=R(t);return(y({email:r||void 0,password:s||void 0}),r)?r:s||null},A=e=>{if("object"!=typeof e||null===e)return"An unexpected error occurred. Please try again.";if("ERR_NETWORK"===e.code||"Network Error"===e.message)return"Unable to connect to the server. Please check your internet connection and try again.";if("ECONNABORTED"===e.code)return"Request timed out. Please check your connection and try again.";if(e.response?.data){let{message:r,statusCode:t}=e.response.data;if(Array.isArray(r))return r.join(". ");switch(t){case 400:if("string"==typeof r)return r;return"Invalid input. Please check your email and password format.";case 401:if(r&&r.toLowerCase().includes("credential"))return"Invalid email or password. Please check your credentials and try again.";if(r&&r.toLowerCase().includes("account"))return"Account not found or inactive. Please contact support if you believe this is an error.";return"Authentication failed. Please verify your email and password.";case 403:return"Your account has been suspended or you do not have permission to access this system.";case 429:return"Too many login attempts. Please wait a few minutes before trying again.";case 500:return"Server error occurred. Please try again later or contact support.";case 503:return"Service temporarily unavailable. Please try again in a few minutes.";default:if("string"==typeof r)return r;return`Login failed (Error ${t}). Please try again.`}}return e.message?e.message.toLowerCase().includes("fetch")?"Unable to connect to the server. Please check your internet connection.":e.message:"An unexpected error occurred. Please try again."},z=async r=>{r.preventDefault(),h(""),v("");let s=S();if(s)return void h(s);g(!0);try{await P(e.trim().toLowerCase(),t),L.push("/customer")}catch(e){h(A(e)),g(!1);return}};return!k||x?(0,s.jsx)("div",{className:"min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900",children:(0,s.jsx)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:(0,s.jsx)(c.A,{message:k?"Signing in...":"Loading..."})})}):(0,s.jsxs)("div",{className:"min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900",children:[(0,s.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsx)(d.default,{src:"/images/macra-logo.png",alt:"MACRA Logo",width:64,height:64,className:"h-16 w-auto"})}),(0,s.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-gray-100",children:j?"Sign in to your account":"Staff Portal Login"}),j?(0,s.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600 dark:text-gray-400",children:["Or"," ",(0,s.jsx)(o(),{href:"/customer/auth/signup",className:"font-medium text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300",children:"create a new account"})]}):(0,s.jsx)("p",{className:"mt-2 text-center text-sm text-gray-600 dark:text-gray-400",children:"Sign in to access the staff dashboard"})]}),(0,s.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[p&&(0,s.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 relative animate-in slide-in-from-top-2 duration-300",children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("div",{className:"h-5 w-5 text-red-400 dark:text-red-500",children:(0,s.jsx)("svg",{fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})})}),(0,s.jsxs)("div",{className:"ml-3 flex-1",children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-red-800 dark:text-red-200 mb-1",children:"Login Failed"}),(0,s.jsx)("p",{className:"text-sm text-red-700 dark:text-red-300",children:p})]}),(0,s.jsx)("div",{className:"ml-4 flex-shrink-0",children:(0,s.jsx)("button",{type:"button",onClick:()=>h(""),className:"inline-flex text-red-400 hover:text-red-600 dark:text-red-500 dark:hover:text-red-300 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 rounded-md p-1 transition-colors duration-200","aria-label":"Dismiss error",children:(0,s.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})})]})}),f&&(0,s.jsx)("div",{className:"mb-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 relative animate-in slide-in-from-top-2 duration-300",children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("div",{className:"h-5 w-5 text-green-400 dark:text-green-500",children:(0,s.jsx)("svg",{fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})})}),(0,s.jsxs)("div",{className:"ml-3 flex-1",children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-green-800 dark:text-green-200 mb-1",children:"Success"}),(0,s.jsx)("p",{className:"text-sm text-green-700 dark:text-green-300",children:f})]}),(0,s.jsx)("div",{className:"ml-4 flex-shrink-0",children:(0,s.jsx)("button",{type:"button",onClick:()=>v(""),className:"inline-flex text-green-400 hover:text-green-600 dark:text-green-500 dark:hover:text-green-300 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 rounded-md p-1 transition-colors duration-200","aria-label":"Dismiss success message",children:(0,s.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})})]})}),(0,s.jsxs)("form",{className:"space-y-6",onSubmit:z,children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Email address"}),(0,s.jsx)("div",{className:"mt-1",children:(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e,onChange:e=>{r(e.target.value),b.email&&y(e=>({...e,email:void 0})),p&&h("")},className:`appearance-none block w-full px-4 py-3 border-2 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 sm:text-sm bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors ${b.email?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"}`,placeholder:"Enter your email address"})}),b.email&&(0,s.jsxs)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400 flex items-center",children:[(0,s.jsx)("svg",{className:"h-4 w-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),b.email]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Password"}),(0,s.jsx)("div",{className:"mt-1",children:(0,s.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,value:t,onChange:e=>{i(e.target.value),b.password&&y(e=>({...e,password:void 0})),p&&h("")},className:`appearance-none block w-full px-4 py-3 border-2 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 sm:text-sm bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors ${b.password?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"}`,placeholder:"Enter your password"})}),b.password&&(0,s.jsxs)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400 flex items-center",children:[(0,s.jsx)("svg",{className:"h-4 w-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),b.password]})]}),j&&(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{id:"remember-me",name:"remember-me",type:"checkbox",checked:u,onChange:e=>m(e.target.checked),className:"h-5 w-5 text-red-600 focus:ring-2 focus:ring-red-500 border-2 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700"}),(0,s.jsx)("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-900 dark:text-gray-100",children:"Remember me"})]}),(0,s.jsx)("div",{className:"text-sm",children:(0,s.jsx)(o(),{href:"/customer/auth/forgot-password",className:"font-medium text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300",children:"Forgot your password?"})})]}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",disabled:x,className:"w-full flex justify-center items-center py-3 px-4 border-2 border-transparent rounded-md shadow-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-all disabled:opacity-50 disabled:cursor-not-allowed",children:x?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Signing in..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("svg",{className:"h-4 w-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"})}),"Sign in"]})})})]})]})})]})}function m(){return(0,s.jsx)(a.Suspense,{fallback:(0,s.jsxs)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-red-600"}),(0,s.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading login page..."})]}),children:(0,s.jsx)(u,{})})}},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,7498,1658,5814,2335],()=>t(44513));module.exports=s})();