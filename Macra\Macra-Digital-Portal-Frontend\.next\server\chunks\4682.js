"use strict";exports.id=4682,exports.ids=[4682],exports.modules={78637:(e,t,a)=>{a.d(t,{k:()=>s});var i=a(51278),r=a(12234);let s={async getApplications(e){let t=new URLSearchParams;e?.page&&t.append("page",e.page.toString()),e?.limit&&t.append("limit",e.limit.toString()),e?.search&&t.append("search",e.search),e?.sortBy&&t.append("sortBy",e.sortBy),e?.sortOrder&&t.append("sortOrder",e.sortOrder),e?.filters?.licenseTypeId&&t.append("filter.license_category.license_type_id",e.filters.licenseTypeId),e?.filters?.licenseCategoryId&&t.append("filter.license_category_id",e.filters.licenseCategoryId),e?.filters?.status&&t.append("filter.status",e.filters.status);let a=await r.uE.get(`/applications?${t.toString()}`);return(0,i.zp)(a)},async getApplicationsByLicenseType(e,t){let a=new URLSearchParams;t?.page&&a.append("page",t.page.toString()),t?.limit&&a.append("limit",t.limit.toString()),t?.search&&a.append("search",t.search),t?.status&&a.append("filter.status",t.status),a.append("filter.license_category.license_type_id",e);let s=await r.uE.get(`/applications?${a.toString()}`);return(0,i.zp)(s)},async getApplication(e){let t=await r.uE.get(`/applications/${e}`);return(0,i.zp)(t)},async getApplicationsByApplicant(e){let t=await r.uE.get(`/applications/by-applicant/${e}`);return(0,i.zp)(t)},async getApplicationsByStatus(e){let t=await r.uE.get(`/applications/by-status/${e}`);return(0,i.zp)(t)},async updateApplicationStatus(e,t){let a=await r.uE.put(`/applications/${e}/status?status=${t}`);return(0,i.zp)(a)},async updateApplicationProgress(e,t,a){let s=await r.uE.put(`/applications/${e}/progress?currentStep=${t}&progressPercentage=${a}`);return(0,i.zp)(s)},async getApplicationStats(){let e=await r.uE.get("/applications/stats");return(0,i.zp)(e)},async createApplication(e){try{let t=await r.uE.post("/applications",e);return(0,i.zp)(t)}catch(e){throw e}},async updateApplication(e,t){try{let a=await r.uE.put(`/applications/${e}`,t,{timeout:3e4});return(0,i.zp)(a)}catch(e){if("ECONNABORTED"===e.code)throw Error("Request timeout - please try again");if(e.response?.status===400){let t=e.response?.data?.message||"Invalid application data";throw Error(`Bad Request: ${t}`)}if(e.response?.status===429)throw Error("Too many requests - please wait a moment and try again");throw e}},async deleteApplication(e){let t=await r.uE.delete(`/applications/${e}`);return(0,i.zp)(t)},async createApplicationWithApplicant(e){try{let t=new Date,a=t.toISOString().slice(0,10).replace(/-/g,""),i=t.toTimeString().slice(0,8).replace(/:/g,""),r=Math.random().toString(36).substr(2,3).toUpperCase(),s=`APP-${a}-${i}-${r}`;if(!/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e.user_id))throw Error(`Invalid user_id format: ${e.user_id}. Expected UUID format.`);return await this.createApplication({application_number:s,applicant_id:e.user_id,license_category_id:e.license_category_id,current_step:1,progress_percentage:0})}catch(e){throw e}},async saveApplicationSection(e,t,a){try{let a=1,i=["applicantInfo","companyProfile","businessInfo","serviceScope","businessPlan","legalHistory","reviewSubmit"].indexOf(t);a=i>=0?i+1:1;let r=Math.min(Math.round(a/6*100),100);await this.updateApplication(e,{progress_percentage:r,current_step:a})}catch(e){throw e}},async getApplicationSection(e,t){try{let a=await r.uE.get(`/applications/${e}/sections/${t}`);return(0,i.zp)(a)}catch(e){throw e}},async submitApplication(e){try{let t=await r.uE.put(`/applications/${e}`,{status:"submitted",submitted_at:new Date().toISOString(),progress_percentage:100,current_step:7});return(0,i.zp)(t)}catch(e){throw e}},async getUserApplications(){try{let e=await r.uE.get("/applications/user-applications"),t=(0,i.zp)(e),a=[];return t?.data?a=Array.isArray(t.data)?t.data:[]:Array.isArray(t)?a=t:t&&(a=[t]),a}catch(e){throw e}},async saveAsDraft(e,t){try{let a=await r.uE.put(`/applications/${e}`,{form_data:t,status:"draft"});return(0,i.zp)(a)}catch(e){throw e}},async validateApplication(e){try{let e={},t=[];for(let a of["applicantInfo","companyProfile","businessInfo","legalHistory"])e[a]&&0!==Object.keys(e[a]).length||t.push(`${a} section is incomplete`);return{isValid:0===t.length,errors:t}}catch(e){throw e}}}},90678:(e,t,a)=>{a.d(t,{oQ:()=>r});let i={email:/^[^\s@]+@[^\s@]+\.[^\s@]+$/,phone:/^(\+265|0)[0-9]{8,9}$/,percentage:/^(100|[1-9]?[0-9])$/};i.email,i.phone,i.percentage;let r=(e,t)=>{let a={};switch(t){case"applicantInfo":["name","business_registration_number","tpin","website","email","phone","date_incorporation","place_incorporation"].forEach(t=>{e[t]&&("string"!=typeof e[t]||""!==e[t].trim())||(a[t]=`${t.replace(/_/g," ")} is required`)}),e.email&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.email)&&(a.email="Please enter a valid email address"),e.phone&&!/^[+]?[\d\s\-()]+$/.test(e.phone)&&(a.phone="Please enter a valid phone number"),e.fax&&""!==e.fax.trim()&&!/^[+]?[\d\s\-()]+$/.test(e.fax)&&(a.fax="Please enter a valid fax number"),e.date_incorporation&&!/^\d{4}-\d{2}-\d{2}$/.test(e.date_incorporation)&&(a.date_incorporation="Please enter a valid date (YYYY-MM-DD)");break;case"companyProfile":["company_name","business_registration_number","tax_number","company_type","incorporation_date","incorporation_place","company_email","company_phone","company_address","company_city","company_district","number_of_employees","annual_revenue","business_description"].forEach(t=>{e[t]&&("string"!=typeof e[t]||""!==e[t].trim())||(a[t]=`${t.replace(/_/g," ")} is required`)}),e.company_email&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.company_email)&&(a.company_email="Please enter a valid email address");break;case"businessInfo":["business_model","operational_structure","target_market","competitive_advantage","facilities_description","equipment_description","operational_areas","service_delivery_model","quality_assurance","customer_support"].forEach(t=>{e[t]&&("string"!=typeof e[t]||""!==e[t].trim())||(a[t]=`${t.replace(/_/g," ")} is required`)});break;case"serviceScope":["services_offered","geographic_coverage","service_categories","target_customers","service_capacity"].forEach(t=>{e[t]&&("string"!=typeof e[t]||""!==e[t].trim())||(a[t]=`${t.replace(/_/g," ")} is required`)});break;case"businessPlan":["executive_summary","market_analysis","financial_projections","revenue_model","investment_requirements","implementation_timeline","risk_analysis","success_metrics"].forEach(t=>{e[t]&&("string"!=typeof e[t]||""!==e[t].trim())||(a[t]=`${t.replace(/_/g," ")} is required`)});break;case"legalHistory":e.compliance_record&&""!==e.compliance_record.trim()||(a.compliance_record="Compliance record is required"),e.declaration_accepted||(a.declaration_accepted="You must accept the declaration to proceed"),e.criminal_history&&(!e.criminal_details||""===e.criminal_details.trim())&&(a.criminal_details="Please provide details of your criminal history"),e.bankruptcy_history&&(!e.bankruptcy_details||""===e.bankruptcy_details.trim())&&(a.bankruptcy_details="Please provide details of your bankruptcy history"),e.regulatory_actions&&(!e.regulatory_details||""===e.regulatory_details.trim())&&(a.regulatory_details="Please provide details of regulatory actions"),e.litigation_history&&(!e.litigation_details||""===e.litigation_details.trim())&&(a.litigation_details="Please provide details of litigation history");break;case"address":["address_line_1","city","country"].forEach(t=>{e[t]&&("string"!=typeof e[t]||""!==e[t].trim())||(a[t]=`${t.replace(/_/g," ")} is required`)});break;case"contactInfo":["primary_contact_first_name","primary_contact_last_name","primary_contact_designation","primary_contact_email","primary_contact_phone"].forEach(t=>{e[t]&&("string"!=typeof e[t]||""!==e[t].trim())||(a[t]=`${t.replace(/_/g," ")} is required`)}),e.primary_contact_email&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.primary_contact_email)&&(a.primary_contact_email="Please enter a valid email address"),e.secondary_contact_email&&""!==e.secondary_contact_email.trim()&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.secondary_contact_email)&&(a.secondary_contact_email="Please enter a valid email address"),e.primary_contact_phone&&!/^[+]?[\d\s\-()]+$/.test(e.primary_contact_phone)&&(a.primary_contact_phone="Please enter a valid phone number"),e.secondary_contact_phone&&""!==e.secondary_contact_phone.trim()&&!/^[+]?[\d\s\-()]+$/.test(e.secondary_contact_phone)&&(a.secondary_contact_phone="Please enter a valid phone number")}return{isValid:0===Object.keys(a).length,errors:a}}}};