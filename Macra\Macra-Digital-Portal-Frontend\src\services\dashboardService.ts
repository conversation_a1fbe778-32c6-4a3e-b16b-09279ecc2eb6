import { apiClient } from '../lib/apiClient';
import { processApiResponse } from '@/lib/authUtils';

export interface DashboardOverview {
  applications: {
    total: number;
    pending: number;
    approved: number;
    rejected: number;
    submitted?: number;
    under_review?: number;
    evaluation?: number;
    draft?: number;
  };
  users?: {
    total: number;
    active: number;
    newThisMonth: number;
    administrators: number;
  };
  licenses: {
    total: number;
    active: number;
    expiringSoon: number;
    expired: number;
  };
  financial: {
    totalRevenue: number;
    thisMonth: number;
    pending: number;
    transactions: number;
  };
  timestamp: string;
}

export interface LicenseStats {
  total: number;
  active: number;
  expiringSoon: number;
  expired: number;
}

export interface UserStats {
  total: number;
  active: number;
  newThisMonth: number;
  administrators: number;
}

export interface FinancialStats {
  totalRevenue: number;
  thisMonth: number;
  pending: number;
  transactions: number;
}

export interface RecentApplication {
  application_id: string;
  application_number: string;
  status: string;
  created_at: string;
  applicant?: {
    company_name: string;
  };
  license_category?: {
    category_name: string;
  };
}

export interface RecentActivity {
  audit_id: string;
  action: string;
  module: string;
  resource_type: string;
  description: string;
  created_at: string;
  user?: {
    first_name: string;
    last_name: string;
  };
}

export const dashboardService = {
  // Get dashboard overview with all key metrics
  async getOverview(): Promise<DashboardOverview> {
    try {
      const response = await apiClient.get('/dashboard/overview');
      return processApiResponse(response);
    } catch (error) {
      throw error;
    }
  },

  // Get license statistics
  async getLicenseStats(): Promise<LicenseStats> {
    try {
      const response = await apiClient.get('/dashboard/licenses/stats');
      return processApiResponse(response);
    } catch (error) {
      throw error;
    }
  },

  // Get user statistics (admin only)
  async getUserStats(): Promise<UserStats> {
    try {
      const response = await apiClient.get('/dashboard/users/stats');
      return processApiResponse(response);
    } catch (error) {
      throw error;
    }
  },

  // Get financial statistics
  async getFinancialStats(): Promise<FinancialStats> {
    try {
      const response = await apiClient.get('/dashboard/financial/stats');
      return processApiResponse(response);
    } catch (error) {
      console.error('DashboardService.getFinancialStats error:', error);
      throw error;
    }
  },

  // Get recent applications
  async getRecentApplications(): Promise<RecentApplication[]> {
    try {
      const response = await apiClient.get('/dashboard/applications/recent');
      return processApiResponse(response);
    } catch (error) {
      console.error('DashboardService.getRecentApplications error:', error);
      throw error;
    }
  },

  // Get recent activities
  async getRecentActivities(): Promise<RecentActivity[]> {
    try {
      const response = await apiClient.get('/dashboard/activities/recent');
      return processApiResponse(response);
    } catch (error) {
      console.error('DashboardService.getRecentActivities error:', error);
      throw error;
    }
  },

  // Legacy method for backward compatibility
  async getDashboardStats(): Promise<any> {
    try {
      const overview = await this.getOverview();
      return overview.applications;
    } catch (error) {
      console.error('DashboardService.getDashboardStats error:', error);
      throw error;
    }
  },
};
