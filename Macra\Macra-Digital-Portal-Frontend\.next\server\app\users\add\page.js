(()=>{var e={};e.id=1165,e.ids=[1165],e.modules={1725:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var t=r(65239),a=r(48088),i=r(88170),n=r.n(i),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(s,o);let d={children:["",{children:["users",{children:["add",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,21487)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\users\\add\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,73888)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\users\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\users\\add\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/users/add/page",pathname:"/users/add",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6671:(e,s,r)=>{Promise.resolve().then(r.bind(r,21487))},10413:(e,s,r)=>{Promise.resolve().then(r.bind(r,51778))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21487:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\users\\\\add\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\users\\add\\page.tsx","default")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28973:(e,s,r)=>{Promise.resolve().then(r.bind(r,73888))},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},49048:(e,s,r)=>{"use strict";r.d(s,{D:()=>i});var t=r(12234),a=r(51278);let i={async getUsers(e={}){let s=new URLSearchParams;e.page&&s.set("page",e.page.toString()),e.limit&&s.set("limit",e.limit.toString()),e.search&&s.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>s.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>s.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(([e,r])=>{Array.isArray(r)?r.forEach(r=>s.append(`filter.${e}`,r)):s.set(`filter.${e}`,r)});let r=await t.Gf.get(`?${s.toString()}`);return(0,a.zp)(r)},async getUser(e){let s=await t.Gf.get(`/${e}`);return(0,a.zp)(s)},async getUserById(e){return this.getUser(e)},async getProfile(){let e=await t.Gf.get("/profile");return(0,a.zp)(e)},async createUser(e){let s=await t.Gf.post("",e);return(0,a.zp)(s)},async updateUser(e,s){let r=await t.Gf.put(`/${e}`,s);return(0,a.zp)(r)},async updateProfile(e){let s=await t.Gf.put("/profile",e);return(0,a.zp)(s)},async changePassword(e){let s=await t.Gf.put("/profile/password",e);return(0,a.zp)(s)},async uploadAvatar(e){let s=new FormData;s.append("avatar",e);try{let e=await t.Gf.post("/profile/avatar",s,{headers:{"Content-Type":"multipart/form-data"}});return(0,a.zp)(e)}catch(e){throw e}},async removeAvatar(){let e=await t.Gf.delete("/profile/avatar");return(0,a.zp)(e)},async deleteUser(e){await t.Gf.delete(`/${e}`)}}},51778:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>d});var t=r(60687),a=r(43210),i=r(16189),n=r(63213),l=r(21891),o=r(60417);function d({children:e}){let{isAuthenticated:s,loading:r}=(0,n.A)();(0,i.useRouter)();let[d,c]=(0,a.useState)("overview"),[m,u]=(0,a.useState)(!1);return r?(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"})}):s?(0,t.jsxs)("div",{className:"flex h-screen overflow-hidden",children:[(0,t.jsx)("div",{id:"mobileSidebarOverlay",className:`mobile-sidebar-overlay ${m?"show":""}`,onClick:()=>u(!1)}),(0,t.jsx)(o.default,{}),(0,t.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,t.jsx)(l.default,{activeTab:d,onTabChange:c,onMobileMenuToggle:()=>{u(!m)}}),e]})]}):null}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66503:(e,s,r)=>{Promise.resolve().then(r.bind(r,80789))},73888:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\users\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\users\\layout.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},80789:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>d});var t=r(60687),a=r(43210),i=r(16189),n=r(85814),l=r.n(n),o=r(49048);function d(){let e=(0,i.useRouter)(),[s,r]=(0,a.useState)({email:"",password:"",confirmPassword:"",first_name:"",last_name:"",middle_name:"",phone:"",status:"active",role_ids:[]}),[n,d]=(0,a.useState)([]),[c,m]=(0,a.useState)(!1),[u,p]=(0,a.useState)(null),[h,x]=(0,a.useState)(null),f=async r=>{if(r.preventDefault(),m(!0),p(null),x(null),s.password!==s.confirmPassword){p("Passwords do not match"),m(!1);return}if(!s.email||!s.password||!s.first_name||!s.last_name){p("Please fill in all required fields"),m(!1);return}try{let r={email:s.email,password:s.password,first_name:s.first_name,last_name:s.last_name,middle_name:s.middle_name||void 0,phone:s.phone,status:s.status,role_ids:s.role_ids.length>0?s.role_ids:void 0};await o.D.createUser(r),x("User created successfully!"),setTimeout(()=>{e.push("/users")},2e3)}catch(e){p(e.response?.data?.message||"Failed to create user")}finally{m(!1)}},g=e=>{let{name:s,value:t}=e.target;r(e=>({...e,[s]:t}))},v=(e,s)=>{r(r=>({...r,role_ids:s?[...r.role_ids,e]:r.role_ids.filter(s=>s!==e)}))};return(0,t.jsx)("main",{className:"flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,t.jsxs)("div",{className:"tab-heading",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-semibold text-gray-900",children:"Add New User"}),(0,t.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Create a new user account with appropriate roles and permissions."})]}),(0,t.jsx)("div",{className:"relative",children:(0,t.jsxs)(l(),{href:"/users",className:"main-button",role:"button",children:[(0,t.jsx)("div",{className:"w-5 h-5 flex items-center justify-center mr-2",children:(0,t.jsx)("i",{className:"ri-arrow-left-line"})}),"Back to Users"]})})]}),u&&(0,t.jsx)("div",{className:"mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md",children:u}),h&&(0,t.jsx)("div",{className:"mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md",children:h}),(0,t.jsx)("div",{className:"bg-white shadow overflow-hidden sm:rounded-lg",children:(0,t.jsx)("div",{className:"px-4 py-5 sm:p-6",children:(0,t.jsxs)("form",{onSubmit:f,className:"space-y-8",children:[(0,t.jsxs)("div",{className:"form-section",children:[(0,t.jsx)("h3",{className:"text-lg font-medium leading-6 text-gray-900 mb-6",children:"Basic Information"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"first_name",className:"custom-form-label",children:"First Name *"}),(0,t.jsx)("input",{type:"text",name:"first_name",id:"first_name",value:s.first_name,onChange:g,className:"custom-input",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"last_name",className:"custom-form-label",children:"Last Name *"}),(0,t.jsx)("input",{type:"text",name:"last_name",id:"last_name",value:s.last_name,onChange:g,className:"custom-input",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"middle_name",className:"custom-form-label",children:"Middle Name"}),(0,t.jsx)("input",{type:"text",name:"middle_name",id:"middle_name",value:s.middle_name,onChange:g,className:"custom-input"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"custom-form-label",children:"Email Address *"}),(0,t.jsx)("input",{type:"email",name:"email",id:"email",value:s.email,onChange:g,className:"custom-input",required:!0})]}),(0,t.jsxs)("div",{className:"md:col-span-2",children:[(0,t.jsx)("label",{htmlFor:"phone",className:"custom-form-label",children:"Phone Number"}),(0,t.jsx)("input",{type:"tel",name:"phone",id:"phone",value:s.phone,onChange:g,className:"custom-input",placeholder:"+265..."})]})]})]}),(0,t.jsxs)("div",{className:"form-section",children:[(0,t.jsx)("h3",{className:"text-lg font-medium leading-6 text-gray-900 mb-6",children:"Account Information"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"status",className:"custom-form-label",children:"Status *"}),(0,t.jsxs)("select",{id:"status",name:"status",value:s.status,onChange:g,className:"custom-input",required:!0,children:[(0,t.jsx)("option",{value:"active",children:"Active"}),(0,t.jsx)("option",{value:"inactive",children:"Inactive"}),(0,t.jsx)("option",{value:"suspended",children:"Suspended"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password",className:"custom-form-label",children:"Password *"}),(0,t.jsx)("input",{type:"password",name:"password",id:"password",value:s.password,onChange:g,className:"custom-input",required:!0}),(0,t.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Password must be at least 8 characters and include a number and special character."})]}),(0,t.jsxs)("div",{className:"md:col-span-2",children:[(0,t.jsx)("label",{htmlFor:"confirmPassword",className:"custom-form-label",children:"Confirm Password *"}),(0,t.jsx)("input",{type:"password",name:"confirmPassword",id:"confirmPassword",value:s.confirmPassword,onChange:g,className:"custom-input",required:!0})]})]})]}),(0,t.jsxs)("div",{className:"form-section",children:[(0,t.jsx)("h3",{className:"text-lg font-medium leading-6 text-gray-900 mb-6",children:"Role & Permissions"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"custom-form-label mb-4 block",children:"User Roles"}),(0,t.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3",children:n.map(e=>(0,t.jsxs)("label",{className:"flex items-center space-x-2 bg-gray-50 rounded-md px-3 py-2 cursor-pointer hover:bg-red-50 transition",children:[(0,t.jsx)("input",{type:"checkbox",checked:s.role_ids.includes(e.role_id),onChange:s=>v(e.role_id,s.target.checked),className:"form-checkbox h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary"}),(0,t.jsx)("span",{className:"text-sm text-gray-700 capitalize",children:e.name.replace(/_/g," ")})]},e.role_id))})]})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3 pt-6 border-t border-gray-200",children:[(0,t.jsx)(l(),{href:"/users",className:"secondary-main-button",children:"Cancel"}),(0,t.jsx)("button",{type:"submit",disabled:c,className:"main-button disabled:opacity-50 disabled:cursor-not-allowed",children:c?"Creating...":"Create User"})]})]})})})]})})}r(85177)},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85177:(e,s,r)=>{"use strict";r.d(s,{O:()=>i});var t=r(12234),a=r(51278);process.env.NEXT_PUBLIC_API_URL;let i={async getRoles(e={}){let s=new URLSearchParams;e.page&&s.set("page",e.page.toString()),e.limit&&s.set("limit",e.limit.toString()),e.search&&s.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>s.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>s.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(([e,r])=>{Array.isArray(r)?r.forEach(r=>s.append(`filter.${e}`,r)):s.set(`filter.${e}`,r)});let r=await t.rV.get(`?${s.toString()}`);return(0,a.zp)(r)},async getRole(e){let s=await t.rV.get(`/${e}`);return(0,a.zp)(s)},async getRoleWithPermissions(e){let s=await t.rV.get(`/${e}?include=permissions`);return(0,a.zp)(s)},async createRole(e){let s=await t.rV.post("",e);return(0,a.zp)(s)},async updateRole(e,s){let r=await t.rV.patch(`/${e}`,s);return(0,a.zp)(r)},async deleteRole(e){await t.rV.delete(`/${e}`)},async assignPermissions(e,s){let r=await t.rV.post(`/${e}/permissions`,{permission_ids:s});return(0,a.zp)(r)},async removePermissions(e,s){let r=await t.rV.delete(`/${e}/permissions`,{data:{permission_ids:s}});return(0,a.zp)(r)},async getPermissions(){let e=await t.uE.get("/permissions");return(0,a.zp)(e)}}},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4447,7498,1658,5814,2335,6606],()=>r(1725));module.exports=t})();