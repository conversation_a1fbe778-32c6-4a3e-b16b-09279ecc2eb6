{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|images).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|images).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "7tMnmjS3vjHwtpSqQLSfq+Oz9s5f+qG1RJUKtBKj9pI=", "__NEXT_PREVIEW_MODE_ID": "b9ed08eba6452b0efd7b63c865b02612", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "2efa1bd95ed6f94361318e022f3e15c6363e4efe6da5ed280f33a1eb863ba7aa", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "8034c0b7c0563afa4e0af1f573da3bf5dbfc109523c451960ea5186b902c7b9b"}}}, "instrumentation": null, "functions": {}}