{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|images).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|images).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "7tMnmjS3vjHwtpSqQLSfq+Oz9s5f+qG1RJUKtBKj9pI=", "__NEXT_PREVIEW_MODE_ID": "c2f3b18a545f5870554981961a35d756", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "dfd6cc83b7daa371f125177a1ea56b7d2c4d9767400959bca5e49840effb3f05", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "2addc47c06bad4c23fa5b207cfb27946b88783026322ef9beead1e21cf38287e"}}}, "instrumentation": null, "functions": {}}