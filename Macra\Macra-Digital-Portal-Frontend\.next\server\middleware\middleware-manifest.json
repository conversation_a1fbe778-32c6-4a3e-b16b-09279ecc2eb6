{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|images).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|images).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "7tMnmjS3vjHwtpSqQLSfq+Oz9s5f+qG1RJUKtBKj9pI=", "__NEXT_PREVIEW_MODE_ID": "b4dc2bb313d1e5d69f6192ace24ef7ec", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d8708dbc28c2d68301f839d96f68c5fa30e8539e7dd32ada8e32be903ff53782", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "2848821b616a15a038152b85b387bb30c46c7119a60b6cb5fe3c571f0e8075c6"}}}, "instrumentation": null, "functions": {}}