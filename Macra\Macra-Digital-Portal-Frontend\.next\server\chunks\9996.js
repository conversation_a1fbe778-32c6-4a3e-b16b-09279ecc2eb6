"use strict";exports.id=9996,exports.ids=[9996],exports.modules={74040:(e,t,a)=>{a.d(t,{nU:()=>s});var r=a(12234),i=a(51278);let s={async createReport(e){try{let t=new FormData;t.append("title",e.title),t.append("description",e.description),t.append("category",e.category),t.append("severity",e.severity),t.append("incident_date",e.incident_date),t.append("organization_involved",e.organization_involved),e.priority&&t.append("priority",e.priority),e.affected_data_types&&t.append("affected_data_types",e.affected_data_types),e.contact_attempts&&t.append("contact_attempts",e.contact_attempts),e.attachments&&e.attachments.length>0&&e.attachments.forEach(e=>{t.append("attachments",e)});let a=await r.uE.post("/data-breach-reports",t,{headers:{"Content-Type":"multipart/form-data"}});return(0,i.zp)(a)}catch(e){throw e}},async getReportById(e){try{let t=await r.uE.get(`/data-breach-reports/${e}`);return(0,i.zp)(t)}catch(e){throw e}},async updateStatus(e,t,a){try{let s=await r.uE.put(`/data-breach-reports/${e}/status`,{status:t,comment:a});return(0,i.zp)(s)}catch(e){throw e}},async assignReport(e,t){try{let a=await r.uE.put(`/data-breach-reports/${e}/assign`,{assigned_to:t});return(0,i.zp)(a)}catch(e){throw e}},async getReports(e={}){let t=new URLSearchParams;e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(([e,a])=>{Array.isArray(a)?a.forEach(a=>t.append(`filter.${e}`,a)):t.set(`filter.${e}`,a)});let a=await r.uE.get(`/data-breach-reports?${t.toString()}`);return(0,i.zp)(a)},async getReport(e){let t=await r.uE.get(`/data-breach-reports/${e}`);return(0,i.zp)(t)},async getReportById(e){return this.getReport(e)},async updateReport(e,t){let a=await r.uE.put(`/data-breach-reports/${e}`,t);return(0,i.zp)(a)},async deleteReport(e){await r.uE.delete(`/data-breach-reports/${e}`)},async updateReportStatus(e,t,a){let s=await r.uE.put(`/data-breach-reports/${e}/status`,{status:t,comment:a});return(0,i.zp)(s)},async addAttachment(e,t){let a=new FormData;a.append("files",t);let s=await r.uE.post(`/data-breach-reports/${e}/attachments`,a,{headers:{"Content-Type":"multipart/form-data"}});return(0,i.zp)(s)},async removeAttachment(e,t){await r.uE.delete(`/data-breach-reports/${e}/attachments/${t}`)},getStatusColor(e){switch(e?.toLowerCase()){case"submitted":return"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";case"under_review":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";case"investigating":return"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";case"resolved":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}},getSeverityColor(e){switch(e?.toLowerCase()){case"low":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"medium":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";case"high":return"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";case"critical":return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}},getStatusOptions:()=>[{value:"submitted",label:"Submitted"},{value:"under_review",label:"Under Review"},{value:"investigating",label:"Investigating"},{value:"resolved",label:"Resolved"},{value:"closed",label:"Closed"}],getCategoryOptions:()=>[{value:"Personal Data",label:"Personal Data"},{value:"Financial Data",label:"Financial Data"},{value:"Health Data",label:"Health Data"},{value:"Technical Data",label:"Technical Data"},{value:"Communication Data",label:"Communication Data"},{value:"Other",label:"Other"}],getSeverityOptions:()=>[{value:"low",label:"Low"},{value:"medium",label:"Medium"},{value:"high",label:"High"},{value:"critical",label:"Critical"}],getPriorityOptions:()=>[{value:"low",label:"Low"},{value:"medium",label:"Medium"},{value:"high",label:"High"},{value:"urgent",label:"Urgent"}]}},79605:(e,t,a)=>{a.d(t,{Ay:()=>i,Bf:()=>n,We:()=>s});var r=a(60687);a(43210);let i=({currentStage:e,stages:t,status:a="submitted",progressPercentage:i,showPercentage:s=!0,showStageNames:n=!0,size:l="md",variant:d="horizontal",className:c=""})=>{let o=i??Math.round((e+1)/t.length*100),g=()=>{switch(a){case"submitted":return"bg-blue-500";case"under_review":return"bg-yellow-500";case"investigating":return"bg-orange-500";case"resolved":return"bg-green-500";case"closed":return"bg-gray-500";default:return"bg-gray-400"}},u=()=>{switch(a){case"submitted":return"text-blue-600";case"under_review":return"text-yellow-600";case"investigating":return"text-orange-600";case"resolved":return"text-green-600";default:return"text-gray-600"}},p={sm:{stage:"w-6 h-6 text-xs",bar:"h-1",text:"text-xs"},md:{stage:"w-8 h-8 text-sm",bar:"h-2",text:"text-sm"},lg:{stage:"w-10 h-10 text-base",bar:"h-3",text:"text-base"}};return"vertical"===d?(0,r.jsx)("div",{className:`space-y-4 ${c}`,children:t.map((t,a)=>{let i=a<e,n=a===e;return(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:`
                ${p[l].stage} rounded-full flex items-center justify-center font-medium
                ${i?`${g()} text-white`:n?`border-2 border-current ${u()} bg-white`:"bg-gray-200 text-gray-400"}
              `,children:i?(0,r.jsx)("i",{className:"ri-check-line"}):(0,r.jsx)("span",{children:a+1})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:`font-medium ${n?u():i?"text-gray-900":"text-gray-400"}`,children:t.name}),t.description&&(0,r.jsx)("div",{className:`${p[l].text} ${n?"text-gray-600":"text-gray-400"}`,children:t.description})]}),n&&s&&(0,r.jsxs)("div",{className:`${p[l].text} font-medium ${u()}`,children:[o,"%"]})]},t.id)})}):(0,r.jsxs)("div",{className:`w-full ${c}`,children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:`w-full ${p[l].bar} bg-gray-200 rounded-full overflow-hidden`,children:(0,r.jsx)("div",{className:`${p[l].bar} ${g()} transition-all duration-500 ease-out rounded-full`,style:{width:`${o}%`}})}),(0,r.jsx)("div",{className:"absolute top-0 left-0 w-full flex justify-between items-center",style:{transform:"translateY(-50%)"},children:t.map((a,i)=>{let s=i<e,d=i===e,c=i/(t.length-1)*100;return(0,r.jsxs)("div",{className:"flex flex-col items-center",style:{position:"absolute",left:`${c}%`,transform:"translateX(-50%)"},children:[(0,r.jsx)("div",{className:`
                  ${p[l].stage} rounded-full flex items-center justify-center font-medium border-2 bg-white
                  ${s?`${g().replace("bg-","border-")} ${g()} text-white`:d?`border-current ${u()}`:"border-gray-300 text-gray-400"}
                `,children:s?(0,r.jsx)("i",{className:"ri-check-line"}):a.icon?(0,r.jsx)("i",{className:a.icon}):(0,r.jsx)("span",{children:i+1})}),n&&(0,r.jsx)("div",{className:`mt-2 ${p[l].text} font-medium text-center max-w-20 ${d?u():s?"text-gray-900":"text-gray-400"}`,children:a.name})]},a.id)})})]}),s&&(0,r.jsxs)("div",{className:"flex justify-between items-center mt-8",children:[(0,r.jsxs)("div",{className:`${p[l].text} ${u()} font-medium`,children:["Progress: ",o,"%"]}),(0,r.jsxs)("div",{className:`${p[l].text} text-gray-500 capitalize`,children:["Status: ",a.replace("_"," ")]})]}),n&&t[e]?.description&&(0,r.jsx)("div",{className:"mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg",children:(0,r.jsxs)("div",{className:`${p[l].text} text-gray-600 dark:text-gray-400`,children:[(0,r.jsx)("strong",{children:"Current Stage:"})," ",t[e].description]})})]})},s={CONSUMER_AFFAIRS:[{id:"submitted",name:"Submitted",description:"Complaint has been received and logged",icon:"ri-file-text-line"},{id:"under_review",name:"Under Review",description:"Initial review and assessment in progress",icon:"ri-search-line"},{id:"investigating",name:"Investigating",description:"Detailed investigation and fact-finding",icon:"ri-spy-line"},{id:"resolved",name:"Resolved",description:"Issue has been resolved and action taken",icon:"ri-check-double-line"}],DATA_BREACH:[{id:"submitted",name:"Reported",description:"Data breach report has been received",icon:"ri-shield-line"},{id:"under_review",name:"Assessment",description:"Assessing severity and impact",icon:"ri-search-line"},{id:"investigating",name:"Investigation",description:"Investigating the breach and gathering evidence",icon:"ri-spy-line"},{id:"resolved",name:"Resolved",description:"Breach contained and remediation completed",icon:"ri-shield-check-line"}]},n=e=>({submitted:0,under_review:1,investigating:2,resolved:3,closed:3})[e]||0}};