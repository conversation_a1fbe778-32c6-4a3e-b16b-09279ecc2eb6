"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorHandler = void 0;
const common_1 = require("@nestjs/common");
class ErrorHandler {
    static handleError(logger, error, message, context, statusCode = common_1.HttpStatus.INTERNAL_SERVER_ERROR) {
        const errorMessage = this.formatErrorMessage(message, context);
        const logContext = this.formatLogContext(context);
        logger.error(errorMessage, error.stack, logContext);
        if (error instanceof common_1.HttpException) {
            throw error;
        }
        throw new common_1.HttpException(message, statusCode);
    }
    static logError(logger, error, message, context) {
        const errorMessage = this.formatErrorMessage(message, context);
        const logContext = this.formatLogContext(context);
        logger.error(errorMessage, error.stack, logContext);
    }
    static logWarning(logger, message, context) {
        const warningMessage = this.formatErrorMessage(message, context);
        const logContext = this.formatLogContext(context);
        logger.warn(warningMessage, logContext);
    }
    static logInfo(logger, message, context) {
        const infoMessage = this.formatErrorMessage(message, context);
        const logContext = this.formatLogContext(context);
        logger.log(infoMessage, logContext);
    }
    static formatErrorMessage(message, context) {
        if (!context)
            return message;
        const contextParts = [];
        if (context.userId) {
            contextParts.push(`User: ${context.userId}`);
        }
        if (context.email) {
            contextParts.push(`Email: ${context.email}`);
        }
        if (context.action) {
            contextParts.push(`Action: ${context.action}`);
        }
        return contextParts.length > 0
            ? `${message} [${contextParts.join(', ')}]`
            : message;
    }
    static formatLogContext(context) {
        if (!context)
            return '';
        return JSON.stringify({
            userId: context.userId,
            email: context.email,
            action: context.action,
            metadata: context.metadata,
            timestamp: new Date().toISOString(),
        });
    }
    static createUserContext(user, action, metadata) {
        return {
            userId: user.user_id,
            email: user.email,
            action,
            metadata,
        };
    }
    static createActionContext(email, action, metadata) {
        return {
            email,
            action,
            metadata,
        };
    }
}
exports.ErrorHandler = ErrorHandler;
//# sourceMappingURL=error-handler.util.js.map