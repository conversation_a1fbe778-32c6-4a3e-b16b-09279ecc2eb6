(()=>{var e={};e.id=9124,e.ids=[9124],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6227:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o});var r=t(60687),a=t(43210);function i({roles:e,onEdit:s,onDelete:t,loading:a}){let i=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"});return a?(0,r.jsx)("div",{className:"bg-white shadow overflow-hidden sm:rounded-md",children:(0,r.jsx)("div",{className:"px-4 py-5 sm:p-6",children:(0,r.jsx)("div",{className:"animate-pulse",children:(0,r.jsx)("div",{className:"space-y-3",children:[void 0,void 0,void 0,void 0,void 0].map((e,s)=>(0,r.jsxs)("div",{className:"grid grid-cols-5 gap-4",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded col-span-2"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded"})]},s))})})})}):0===e.length?(0,r.jsx)("div",{className:"bg-white shadow overflow-hidden sm:rounded-md",children:(0,r.jsxs)("div",{className:"px-4 py-5 sm:p-6 text-center",children:[(0,r.jsx)("i",{className:"ri-shield-user-line text-4xl text-gray-400 mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No roles found"}),(0,r.jsx)("p",{className:"text-gray-500",children:"Get started by creating a new role."})]})}):(0,r.jsx)("div",{className:"bg-white shadow overflow-hidden sm:rounded-md",children:(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Role"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Description"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Permissions"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Users"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created"}),(0,r.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"h-10 w-10 flex-shrink-0",children:(0,r.jsx)("div",{className:"h-10 w-10 rounded-full bg-red-600 flex items-center justify-center",children:(0,r.jsx)("i",{className:"ri-shield-user-line text-white"})})}),(0,r.jsx)("div",{className:"ml-4",children:(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name.replace(/_/g," ").replace(/\b\w/g,e=>e.toUpperCase())})})]})}),(0,r.jsx)("td",{className:"px-6 py-4",children:(0,r.jsx)("div",{className:"text-sm text-gray-900 max-w-xs truncate",children:e.description||"No description"})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:[e.permissions?.length||0," permissions"]})})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[e.users?.length||0," users"]})})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:i(e.created_at)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-2",children:[(0,r.jsx)("button",{onClick:()=>s(e),className:"text-indigo-600 hover:text-indigo-900 p-1 rounded hover:bg-indigo-50",title:"Edit role",children:(0,r.jsx)("i",{className:"ri-edit-line"})}),(0,r.jsx)("button",{onClick:()=>t(e.role_id),className:"text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50",title:"Delete role",disabled:e.users&&e.users.length>0,children:(0,r.jsx)("i",{className:"ri-delete-bin-line"})})]})})]},e.role_id))})]})})})}var l=t(20539),n=t(85177);function o(){let[e,s]=(0,a.useState)([]),[t,o]=(0,a.useState)([]),[d,c]=(0,a.useState)(!0),[x,p]=(0,a.useState)(null),[u,m]=(0,a.useState)(!1),[h,f]=(0,a.useState)(null),[g,v]=(0,a.useState)(""),j=async()=>{try{c(!0);let e=await n.O.getRoles({page:1,limit:100});s(e.data||[])}catch(e){p("Failed to load roles")}finally{c(!1)}},y=async e=>{if(confirm("Are you sure you want to delete this role?"))try{await n.O.deleteRole(e),await j()}catch(e){p("Failed to delete role")}},b=()=>{m(!1),f(null)},N=e.filter(e=>e.name.toLowerCase().includes(g.toLowerCase())||e.description&&e.description.toLowerCase().includes(g.toLowerCase()));return d&&0===e.length?(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"})}):(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-semibold text-gray-900",children:"Roles"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Manage user roles and their permissions"})]}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:"text",placeholder:"Search roles...",value:g,onChange:e=>v(e.target.value),className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-red-500 focus:border-red-500 sm:text-sm"}),(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)("i",{className:"ri-search-line text-gray-400"})})]}),(0,r.jsxs)("button",{onClick:()=>{f(null),m(!0)},className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:[(0,r.jsx)("i",{className:"ri-add-line mr-2"}),"Add Role"]})]})]})}),x&&(0,r.jsx)("div",{className:"mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded",children:x}),(0,r.jsx)(i,{roles:N,onEdit:e=>{f(e),m(!0)},onDelete:y,loading:d}),(0,r.jsx)(l.A,{isOpen:u,onClose:b,onSave:()=>{j(),b()},role:h,permissions:t})]})}t(63224)},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31826:(e,s,t)=>{Promise.resolve().then(t.bind(t,43175))},33873:e=>{"use strict";e.exports=require("path")},36001:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>p,tree:()=>d});var r=t(65239),a=t(48088),i=t(88170),l=t.n(i),n=t(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(s,o);let d={children:["",{children:["roles",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,91194)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\roles\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,58597)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\roles\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\roles\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/roles/page",pathname:"/roles",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},43175:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d});var r=t(60687),a=t(43210),i=t(16189),l=t(63213),n=t(21891),o=t(60417);function d({children:e}){let{isAuthenticated:s,loading:t}=(0,l.A)();(0,i.useRouter)();let[d,c]=(0,a.useState)("overview"),[x,p]=(0,a.useState)(!1);return t?(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"})}):s?(0,r.jsxs)("div",{className:"flex h-screen overflow-hidden",children:[(0,r.jsx)("div",{id:"mobileSidebarOverlay",className:`mobile-sidebar-overlay ${x?"show":""}`,onClick:()=>p(!1)}),(0,r.jsx)(o.default,{}),(0,r.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,r.jsx)(n.default,{activeTab:d,onTabChange:c,onMobileMenuToggle:()=>{p(!x)}}),e]})]}):null}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57833:(e,s,t)=>{Promise.resolve().then(t.bind(t,6227))},58597:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\roles\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\roles\\layout.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67561:(e,s,t)=>{Promise.resolve().then(t.bind(t,91194))},68778:(e,s,t)=>{Promise.resolve().then(t.bind(t,58597))},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91194:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\roles\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\roles\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4447,7498,1658,5814,2335,6606,4182],()=>t(36001));module.exports=r})();