(()=>{var e={};e.id=9711,e.ids=[9711],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7292:(e,t,r)=>{Promise.resolve().then(r.bind(r,26455))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13921:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let d={children:["",{children:["customer",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,26455)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,68736)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/customer/page",pathname:"/customer",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26455:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},46521:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var s=r(60687),a=r(43210),i=r(85814),n=r.n(i),l=r(16189),o=r(94391);let d=({id:e,title:t,licenseNumber:r,status:a,issueDate:i,expirationDate:l})=>{let o=(e=>{switch(e){case"Active":return{badge:"bg-green-100 text-green-800",iconBg:"bg-green-100",iconColor:"text-green-600"};case"Expiring Soon":return{badge:"bg-orange-100 text-orange-800",iconBg:"bg-orange-100",iconColor:"text-orange-600"};case"Expired":return{badge:"bg-red-100 text-red-800",iconBg:"bg-red-100",iconColor:"text-red-600"};case"Pending":return{badge:"bg-yellow-100 text-yellow-800",iconBg:"bg-yellow-100",iconColor:"text-yellow-600"};default:return{badge:"bg-gray-100 text-gray-800",iconBg:"bg-gray-100",iconColor:"text-gray-600"}}})(a);return(0,s.jsxs)("div",{className:"license-card bg-white border border-gray-200 rounded-lg overflow-hidden hover:transform hover:-translate-y-1 transition-transform duration-300",children:[(0,s.jsxs)("div",{className:"p-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:`w-10 h-10 flex items-center justify-center rounded-full ${o.iconBg}`,children:(0,s.jsx)("div",{className:`w-5 h-5 flex items-center justify-center ${o.iconColor}`,children:(0,s.jsx)("i",{className:"ri-verified-badge-line"})})}),(0,s.jsxs)("div",{className:"ml-3",children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:t}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:r})]})]}),(0,s.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${o.badge}`,children:a})]}),(0,s.jsxs)("div",{className:"mt-3 grid grid-cols-2 gap-2 text-xs",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-gray-500",children:"Issue Date"}),(0,s.jsx)("p",{className:"font-medium text-gray-900 mt-1",children:i})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-gray-500",children:"Expiration"}),(0,s.jsx)("p",{className:"font-medium text-gray-900 mt-1",children:l})]})]})]}),(0,s.jsxs)("div",{className:"bg-gray-50 px-4 py-2 flex justify-between",children:[(0,s.jsx)(n(),{href:`/customer/licenses/${e}`,className:"text-xs font-medium text-primary hover:text-primary",children:"View details"}),(0,s.jsx)(n(),{href:`/customer/licenses/${e}/renew`,className:"text-xs font-medium text-gray-500 hover:text-gray-700",children:"Renew"})]})]})},c=({id:e,title:t,amount:r,dueDate:a,status:i,description:l})=>{let o=(e=>{switch(e){case"Due":return{bgColor:"bg-yellow-50",borderColor:"border-yellow-100",iconBg:"bg-yellow-100",iconColor:"text-yellow-600",badgeBg:"bg-yellow-100",badgeText:"text-yellow-800"};case"Overdue":return{bgColor:"bg-red-50",borderColor:"border-red-100",iconBg:"bg-red-100",iconColor:"text-red-600",badgeBg:"bg-red-100",badgeText:"text-red-800"};case"Paid":return{bgColor:"bg-green-50",borderColor:"border-green-100",iconBg:"bg-green-100",iconColor:"text-green-600",badgeBg:"bg-green-100",badgeText:"text-green-800"};default:return{bgColor:"bg-gray-50",borderColor:"border-gray-100",iconBg:"bg-gray-100",iconColor:"text-gray-600",badgeBg:"bg-gray-100",badgeText:"text-gray-800"}}})(i);return(0,s.jsxs)("div",{className:`${o.bgColor} border ${o.borderColor} rounded-lg p-4`,children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:`w-8 h-8 flex items-center justify-center rounded-full ${o.iconBg} ${o.iconColor}`,children:(0,s.jsx)("i",{className:"ri-calendar-line"})}),(0,s.jsxs)("div",{className:"ml-3",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-900",children:t}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:l||a})]})]}),(0,s.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${o.badgeBg} ${o.badgeText}`,children:r})]}),"Paid"!==i&&(0,s.jsx)("div",{className:"mt-2 text-right",children:(0,s.jsx)(n(),{href:`/customer/payments/${e}`,className:"text-xs font-medium text-primary hover:text-primary",children:"Pay Now"})})]})};var x=r(71773),m=r(63213),g=r(36212);class u{start(e){this.metrics.set(e,{name:e,startTime:performance.now()})}end(e){let t=this.metrics.get(e);if(!t)return null;let r=performance.now(),s=r-t.startTime;return this.metrics.set(e,{...t,endTime:r,duration:s}),s}getDuration(e){let t=this.metrics.get(e);return t?.duration||null}getAllMetrics(){return Array.from(this.metrics.values()).filter(e=>void 0!==e.duration)}clear(){this.metrics.clear()}logSummary(){let e=this.getAllMetrics();0!==e.length&&e.forEach(e=>{})}constructor(){this.metrics=new Map}}let p=new u,h=e=>(p.start(`page-load-${e}`),()=>{p.end(`page-load-${e}`)}),y=e=>(p.start(`api-${e}`),()=>{p.end(`api-${e}`)}),b=()=>{let{user:e,isAuthenticated:t}=(0,m.A)();(0,l.useRouter)(),(0,a.useEffect)(()=>h("customer-dashboard"),[]);let[r,i]=(0,a.useState)({licenses:[],applications:[],payments:[],stats:{activeLicenses:0,pendingApplications:0,expiringSoon:0,paymentsDue:0,totalPaymentAmount:0}}),[u,p]=(0,a.useState)(!0),[b,f]=(0,a.useState)("");(0,a.useEffect)(()=>{(async()=>{if(t)try{let e;p(!0),f("");let t=y("dashboard-data"),[r,s,a,n]=await Promise.all([g.dr.getLicenses({limit:10}).catch(()=>({data:[]})),g.dr.getApplications({limit:10}).catch(()=>({data:[]})),g.dr.getPayments({limit:10}).catch(()=>({data:[]})),g.dr.getDashboardStats().catch(()=>({}))]);t();let l=r.data||r||[],o=s.data||s||[],d=a.data||a||[];if(n&&Object.keys(n).length>0)e=n.data||n;else{let t=l.filter(e=>"active"===e.status).length,r=o.filter(e=>["submitted","under_review"].includes(e.status)).length,s=new Date;s.setDate(s.getDate()+30);let a=l.filter(e=>{let t=new Date(e.expirationDate);return"active"===e.status&&t<=s}).length,i=d.filter(e=>["pending","overdue"].includes(e.status)),n=i.reduce((e,t)=>e+t.amount,0);e={activeLicenses:t,pendingApplications:r,expiringSoon:a,paymentsDue:i.length,totalPaymentAmount:n}}i({licenses:l,applications:o,payments:d,stats:e})}catch(e){f("Failed to load dashboard data. Please try refreshing the page.")}finally{p(!1)}})()},[t]);let v=(0,a.useMemo)(()=>{let{licenses:e,applications:t,payments:s}=r;return{totalLicenses:e.length,activeLicenses:e.filter(e=>"active"===e.status).length,pendingApplications:t.filter(e=>["submitted","under_review"].includes(e.status)).length,overduePayments:s.filter(e=>"overdue"===e.status).length}},[r]),j=(0,a.useMemo)(()=>{let{licenses:e,applications:t,payments:s}=r;return{recentLicenses:e.slice(0,3),recentApplications:t.slice(0,3),urgentPayments:s.filter(e=>["pending","overdue"].includes(e.status)).slice(0,3)}},[r]);if(u)return(0,s.jsx)(o.A,{children:(0,s.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,s.jsx)(x.A,{message:"Loading your dashboard..."})})});if(b)return(0,s.jsx)(o.A,{children:(0,s.jsxs)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:[(0,s.jsx)("p",{children:b}),(0,s.jsx)("button",{type:"button",onClick:()=>window.location.reload(),className:"mt-2 text-sm underline hover:no-underline",children:"Try again"})]})});let w=[{title:"Active Licenses",value:r.stats.activeLicenses,icon:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})}),bgColor:"bg-white",iconBgColor:"bg-green-100",iconTextColor:"text-green-600",linkText:"View all",linkHref:"/customer/licenses"},{title:"Pending Applications",value:r.stats.pendingApplications,icon:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),bgColor:"bg-white",iconBgColor:"bg-yellow-100",iconTextColor:"text-yellow-600",linkText:"View all",linkHref:"/customer/applications"},{title:"Expiring Soon",value:r.stats.expiringSoon,icon:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),bgColor:"bg-white",iconBgColor:"bg-orange-100",iconTextColor:"text-orange-600",linkText:"View all",linkHref:"/customer/licenses?filter=expiring"},{title:"Payments Due",value:r.stats.totalPaymentAmount>0?`MK${r.stats.totalPaymentAmount.toLocaleString()}`:"MK0",icon:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),bgColor:"bg-white",iconBgColor:"bg-red-100",iconTextColor:"text-red-600",linkText:"View all",linkHref:"/customer/payments"}],N=j.recentLicenses.slice(0,2).map(e=>{let t;if("active"===e.status){let r=new Date(e.expirationDate),s=new Date;s.setDate(s.getDate()+30),t=r<=s?"Expiring Soon":"Active"}else t="expired"===e.status?"Expired":"Pending";return{id:e.id,title:e.type||"License",licenseNumber:e.licenseNumber,status:t,issueDate:new Date(e.issueDate).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),expirationDate:new Date(e.expirationDate).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}}),k=j.urgentPayments.map(e=>{let t,r=new Date(e.dueDate),s=new Date,a=Math.ceil((r.getTime()-s.getTime())/864e5);return t=a<0?`Overdue by ${Math.abs(a)} days`:0===a?"Due today":1===a?"Due tomorrow":`Due in ${a} days`,{id:e.id,title:e.description||`Payment for ${e.relatedLicense||e.relatedApplication||"Service"}`,amount:`MK${e.amount.toLocaleString()}`,dueDate:t,status:"overdue"===e.status?"Overdue":"Due",description:t}});return(0,s.jsx)(o.A,{children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h1",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:["Welcome, ",e?.first_name||"Customer","!"]}),(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Manage your licenses and applications from your personal dashboard."})]}),(0,s.jsxs)("div",{className:"flex space-x-3",children:[(0,s.jsx)("div",{className:"relative",children:(0,s.jsxs)("button",{type:"button",className:"inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-button bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary whitespace-nowrap",children:[(0,s.jsx)("div",{className:"w-4 h-4 flex items-center justify-center mr-2",children:(0,s.jsx)("i",{className:"ri-calendar-line"})}),new Date().toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"})]})}),(0,s.jsxs)(n(),{href:"/customer/applications",className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-button text-white bg-primary hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors",children:[(0,s.jsx)("div",{className:"w-4 h-4 flex items-center justify-center mr-2",children:(0,s.jsx)("i",{className:"ri-add-line"})}),"New Application"]})]})]})}),(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg mb-6 overflow-hidden",children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-medium leading-4 text-gray-900 dark:text-gray-100 mb-4",children:"Key Metrics"}),(0,s.jsx)("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4",children:w.map((e,t)=>(0,s.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 flex flex-col space-y-4",children:[(0,s.jsxs)("div",{className:"flex place-content-start items-center",children:[(0,s.jsx)("div",{className:`flex-shrink-0 ${e.iconBgColor} ${e.iconBgColor.includes("green")?"dark:bg-green-900":e.iconBgColor.includes("yellow")?"dark:bg-yellow-900":e.iconBgColor.includes("orange")?"dark:bg-orange-900":e.iconBgColor.includes("red")?"dark:bg-red-900":"dark:bg-gray-600"} rounded-md p-3`,children:(0,s.jsx)("div",{className:`w-6 h-6 flex items-center justify-center ${e.iconTextColor} ${e.iconTextColor.includes("green")?"dark:text-green-400":e.iconTextColor.includes("yellow")?"dark:text-yellow-400":e.iconTextColor.includes("orange")?"dark:text-orange-400":e.iconTextColor.includes("red")?"dark:text-red-400":"dark:text-gray-400"}`,children:e.icon})}),(0,s.jsxs)("div",{className:"ml-4 flex flex-col",children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:e.title}),(0,s.jsx)("div",{className:"mt-1 flex items-baseline",children:(0,s.jsx)("div",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:e.value})})]})]}),(0,s.jsx)("div",{children:(0,s.jsx)(n(),{href:e.linkHref,className:"inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 dark:text-gray-300 hover:text-white bg-white dark:bg-gray-600 border border-primary rounded-full hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors duration-300 transform hover:-translate-x-1 after:content-['_↗']",children:e.linkText})})]},t))})]})}),(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg mb-6 overflow-hidden",children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-medium leading-4 text-gray-900 dark:text-gray-100 mb-4",children:"Quick Overview"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4 sm:grid-cols-4",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-blue-600 dark:text-blue-400",children:v.totalLicenses}),(0,s.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Total Licenses"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-green-600 dark:text-green-400",children:v.activeLicenses}),(0,s.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Active"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-yellow-600 dark:text-yellow-400",children:v.pendingApplications}),(0,s.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Pending Apps"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-red-600 dark:text-red-400",children:v.overduePayments}),(0,s.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Overdue"})]})]})]})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-3",children:[(0,s.jsx)("div",{className:"lg:col-span-2 space-y-6",children:(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden",children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"My Licenses"}),(0,s.jsx)(n(),{href:"/customer/licenses",className:"text-sm text-primary hover:text-primary",children:"View all →"})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2",children:N.map(e=>(0,s.jsx)(d,{...e},e.id))}),0===N.length&&(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("div",{className:"w-16 h-16 mx-auto bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4",children:(0,s.jsx)("i",{className:"ri-key-line text-2xl text-gray-400 dark:text-gray-500"})}),(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",children:"No licenses yet"}),(0,s.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Start by submitting your first license application using the “New Application” button above."})]})]})})}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow",children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4",children:"License Application Process"}),(0,s.jsx)("div",{className:"space-y-4",children:[{step:1,title:"Submit Application",description:"Fill out the application form with your details and submit required documents.",icon:"ri-file-edit-line",bgColor:"bg-blue-100 dark:bg-blue-900",textColor:"text-blue-600 dark:text-blue-400"},{step:2,title:"Application Review",description:"Our team reviews your application and may request additional information if needed.",icon:"ri-search-eye-line",bgColor:"bg-yellow-100 dark:bg-yellow-900",textColor:"text-yellow-600 dark:text-yellow-400"},{step:3,title:"Payment",description:"Once approved, you'll receive an invoice for the license fee that must be paid.",icon:"ri-bank-card-line",bgColor:"bg-green-100 dark:bg-green-900",textColor:"text-green-600 dark:text-green-400"},{step:4,title:"License Issuance",description:"After payment confirmation, your license will be issued and available for download.",icon:"ri-award-line",bgColor:"bg-purple-100 dark:bg-purple-900",textColor:"text-purple-600 dark:text-purple-400"}].map(e=>(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("div",{className:`flex items-center justify-center h-10 w-10 rounded-full ${e.bgColor} ${e.textColor}`,children:(0,s.jsx)("i",{className:`${e.icon} text-lg`})})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:e.title}),(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:e.description})]})]},e.step))})]})}),(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow",children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Upcoming Payments"}),(0,s.jsx)(n(),{href:"/customer/payments",className:"text-sm text-primary hover:text-primary",children:"View all →"})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[k.map(e=>(0,s.jsx)(c,{...e},e.id)),0===k.length&&(0,s.jsxs)("div",{className:"text-center py-6",children:[(0,s.jsx)("div",{className:"w-12 h-12 mx-auto bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-3",children:(0,s.jsx)("i",{className:"ri-money-dollar-circle-line text-xl text-gray-400 dark:text-gray-500"})}),(0,s.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"No pending payments"})]})]})]})})]})]})]})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},93724:(e,t,r)=>{Promise.resolve().then(r.bind(r,46521))},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,7498,1658,5814,7563,6893,6212],()=>r(13921));module.exports=s})();