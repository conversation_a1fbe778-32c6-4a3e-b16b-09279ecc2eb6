{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/LogoutButton.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { useRouter, usePathname } from 'next/navigation';\r\nimport { useAuth } from '../contexts/AuthContext';\r\n\r\ninterface LogoutButtonProps {\r\n  variant?: 'primary' | 'secondary' | 'text' | 'icon';\r\n  size?: 'sm' | 'md' | 'lg';\r\n  className?: string;\r\n  showConfirmation?: boolean;\r\n  redirectTo?: string;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nexport default function LogoutButton({\r\n  variant = 'primary',\r\n  size = 'md',\r\n  className = '',\r\n  showConfirmation = true,\r\n  redirectTo = '/auth/login',\r\n  children,\r\n}: LogoutButtonProps) {\r\n  const [isLoggingOut, setIsLoggingOut] = useState(false);\r\n  const [showConfirm, setShowConfirm] = useState(false);\r\n  const { logout, user } = useAuth();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  const handleLogout = async () => {\r\n    if (showConfirmation && !showConfirm) {\r\n      setShowConfirm(true);\r\n      return;\r\n    }\r\n\r\n    setIsLoggingOut(true);\r\n    \r\n    try {\r\n      // Call logout from context\r\n      logout();\r\n      \r\n      // Small delay to ensure cleanup is complete\r\n      await new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n      if(pathname.includes('customer')){\r\n        // Redirect to specified page\r\n        router.push('/customer/auth/login');\r\n      } else {\r\n        router.push('/auth/login');\r\n      }\r\n      \r\n\r\n      \r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n    } finally {\r\n      setIsLoggingOut(false);\r\n      setShowConfirm(false);\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    setShowConfirm(false);\r\n  };\r\n\r\n  // Base styles for different variants\r\n  const getVariantStyles = () => {\r\n    switch (variant) {\r\n      case 'primary':\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n      case 'secondary':\r\n        return 'bg-white hover:bg-gray-50 text-red-600 border border-red-600';\r\n      case 'text':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none';\r\n      case 'icon':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none p-2';\r\n      default:\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n    }\r\n  };\r\n\r\n  // Size styles\r\n  const getSizeStyles = () => {\r\n    switch (size) {\r\n      case 'sm':\r\n        return 'px-3 py-1.5 text-sm';\r\n      case 'md':\r\n        return 'px-4 py-2 text-base';\r\n      case 'lg':\r\n        return 'px-6 py-3 text-lg';\r\n      default:\r\n        return 'px-4 py-2 text-base';\r\n    }\r\n  };\r\n\r\n  const baseStyles = 'inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\r\n  \r\n  const buttonStyles = `${baseStyles} ${getVariantStyles()} ${variant !== 'icon' ? getSizeStyles() : ''} ${className}`;\r\n\r\n  // Confirmation dialog\r\n  if (showConfirm) {\r\n    return (\r\n      <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n        <div className=\"bg-white rounded-lg p-6 max-w-sm mx-4 shadow-xl\">\r\n          <div className=\"flex items-center mb-4\">\r\n            <div className=\"flex-shrink-0\">\r\n              <svg className=\"h-6 w-6 text-red-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z\" />\r\n              </svg>\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <h3 className=\"text-lg font-medium text-gray-900\">Confirm Logout</h3>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"mb-4\">\r\n            <p className=\"text-sm text-gray-500\">\r\n              Are you sure you want to logout{user?.first_name ? `, ${user.first_name}` : ''}? You will need to login again to access your account.\r\n            </p>\r\n          </div>\r\n          \r\n          <div className=\"flex space-x-3\">\r\n            <button\r\n              onClick={handleLogout}\r\n              disabled={isLoggingOut}\r\n              className=\"flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 disabled:opacity-50\"\r\n            >\r\n              {isLoggingOut ? 'Logging out...' : 'Yes, Logout'}\r\n            </button>\r\n            <button\r\n              onClick={handleCancel}\r\n              className=\"flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200\"\r\n            >\r\n              Cancel\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <button\r\n      onClick={handleLogout}\r\n      disabled={isLoggingOut}\r\n      className={buttonStyles}\r\n      title=\"Logout\"\r\n    >\r\n      {children || (\r\n        <>\r\n          {variant === 'icon' ? (\r\n            <svg className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n            </svg>\r\n          ) : (\r\n            <>\r\n              <svg className=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n              </svg>\r\n              {isLoggingOut ? 'Logging out...' : 'Logout'}\r\n            </>\r\n          )}\r\n        </>\r\n      )}\r\n    </button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAee,SAAS,aAAa,EACnC,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,mBAAmB,IAAI,EACvB,aAAa,aAAa,EAC1B,QAAQ,EACU;;IAClB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe;QACnB,IAAI,oBAAoB,CAAC,aAAa;YACpC,eAAe;YACf;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,2BAA2B;YAC3B;YAEA,4CAA4C;YAC5C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,IAAG,SAAS,QAAQ,CAAC,aAAY;gBAC/B,6BAA6B;gBAC7B,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QAIF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,gBAAgB;YAChB,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,eAAe;IACjB;IAEA,qCAAqC;IACrC,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,cAAc;IACd,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa;IAEnB,MAAM,eAAe,GAAG,WAAW,CAAC,EAAE,mBAAmB,CAAC,EAAE,YAAY,SAAS,kBAAkB,GAAG,CAAC,EAAE,WAAW;IAEpH,sBAAsB;IACtB,IAAI,aAAa;QACf,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAAuB,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CAC3E,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;;;;;;;;;;;;kCAItD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;gCAAwB;gCACH,MAAM,aAAa,CAAC,EAAE,EAAE,KAAK,UAAU,EAAE,GAAG;gCAAG;;;;;;;;;;;;kCAInF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,eAAe,mBAAmB;;;;;;0CAErC,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,6LAAC;QACC,SAAS;QACT,UAAU;QACV,WAAW;QACX,OAAM;kBAEL,0BACC;sBACG,YAAY,uBACX,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,SAAQ;gBAAY,QAAO;0BAC9D,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;qCAGvE;;kCACE,6LAAC;wBAAI,WAAU;wBAAe,MAAK;wBAAO,SAAQ;wBAAY,QAAO;kCACnE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAEtE,eAAe,mBAAmB;;;;;;;;;AAOjD;GAvJwB;;QAUG,kIAAA,CAAA,UAAO;QACjB,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAZN", "debugId": null}}, {"offset": {"line": 277, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/customer/CustomerLayout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { usePathname, useRouter } from 'next/navigation';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useLoading } from '@/contexts/LoadingContext';\r\nimport LogoutButton from '../LogoutButton';\r\n\r\ninterface BreadcrumbItem {\r\n  label: string;\r\n  href?: string;\r\n}\r\n\r\ninterface CustomerLayoutProps {\r\n  children: React.ReactNode;\r\n  breadcrumbs?: BreadcrumbItem[];\r\n}\r\n\r\nconst CustomerLayout: React.FC<CustomerLayoutProps> = ({ children, breadcrumbs }) => {\r\n  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);\r\n  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);\r\n  const pathname = usePathname();\r\n  const router = useRouter();\r\n  const { user, logout } = useAuth();\r\n  const { showLoader } = useLoading();\r\n\r\n  // Memoize navigation items to prevent unnecessary re-renders\r\n  const navigationItems = useMemo(() => [\r\n    {\r\n      name: 'Dashboard',\r\n      href: '/customer',\r\n      icon: 'ri-dashboard-line',\r\n      current: pathname === '/customer'\r\n    },\r\n    {\r\n      name: 'My Licenses',\r\n      href: '/customer/my-licenses',\r\n      icon: 'ri-key-line',\r\n      current: pathname === '/customer/my-licenses'\r\n    },\r\n    {\r\n      name: 'New Applications',\r\n      href: '/customer/applications',\r\n      icon: 'ri-file-list-3-line',\r\n      current: pathname === '/customer/applications'\r\n    },\r\n    {\r\n      name: 'Payments',\r\n      href: '/customer/payments',\r\n      icon: 'ri-bank-card-line',\r\n      current: pathname === '/customer/payments'\r\n    },\r\n    {\r\n      name: 'Documents',\r\n      href: '/customer/documents',\r\n      icon: 'ri-file-text-line',\r\n      current: pathname === '/customer/documents'\r\n    },\r\n    {\r\n      name: 'Procurement',\r\n      href: '/customer/procurement',\r\n      icon: 'ri-auction-line',\r\n      current: pathname === '/customer/procurement'\r\n    },\r\n    {\r\n      name: 'Request Resource',\r\n      href: '/customer/resources',\r\n      icon: 'ri-hand-heart-line',\r\n      current: pathname === '/customer/resources'\r\n    }\r\n  ], [pathname]);\r\n\r\n  const supportItems = useMemo(() => [\r\n    {\r\n      name: 'Data Protection',\r\n      href: '/customer/data-protection',\r\n      icon: 'ri-shield-keyhole-line'\r\n    },\r\n    {\r\n      name: 'Help Center',\r\n      href: '/customer/help',\r\n      icon: 'ri-question-line'\r\n    }\r\n  ], []);\r\n\r\n  // Prefetch customer pages on mount for faster navigation\r\n  useEffect(() => {\r\n    const prefetchPages = () => {\r\n      const customerPages = [\r\n        '/customer',\r\n        '/customer/applications',\r\n        '/customer/applications/standards',\r\n        '/customer/payments',\r\n        '/customer/my-licenses',\r\n        '/customer/procurement',\r\n        '/customer/profile',\r\n        '/customer/data-protection',\r\n        '/customer/resources',\r\n        '/customer/help'\r\n      ];\r\n\r\n      customerPages.forEach(page => {\r\n        router.prefetch(page);\r\n      });\r\n    };\r\n\r\n    // Delay prefetching to not interfere with initial page load\r\n    const timer = setTimeout(prefetchPages, 1000);\r\n    return () => clearTimeout(timer);\r\n  }, [router]);\r\n\r\n  const toggleMobileSidebar = useCallback(() => {\r\n    setIsMobileSidebarOpen(!isMobileSidebarOpen);\r\n  }, [isMobileSidebarOpen]);\r\n\r\n  const handleNavClick = useCallback((href: string, name: string) => {\r\n    const pageMessages: { [key: string]: string } = {\r\n      '/customer': 'Loading Dashboard...',\r\n      '/customer/my-licenses': 'Loading My Licenses...',\r\n      '/customer/applications': 'Loading Applications...',\r\n      '/customer/applications/apply/': 'Loading Standards License Options...',\r\n      '/customer/payments': 'Loading Payments...',\r\n      '/customer/documents': 'Loading Documents...',\r\n      '/customer/procurement': 'Loading Procurement...',\r\n      '/customer/resources': 'Loading Resources...',\r\n      '/customer/data-protection': 'Loading Data Protection...',\r\n      '/customer/help': 'Loading Help Center...',\r\n      '/customer/profile': 'Loading Profile...',\r\n      '/customer/settings': 'Loading Settings...'\r\n    };\r\n\r\n    const message = pageMessages[href] || `Loading ${name}...`;\r\n    showLoader(message);\r\n    setIsMobileSidebarOpen(false);\r\n  }, [showLoader]);\r\n\r\n  const handleNavHover = useCallback((href: string) => {\r\n    // Prefetch on hover for instant navigation\r\n    router.prefetch(href);\r\n  }, [router]);\r\n\r\n  const toggleUserDropdown = useCallback(() => {\r\n    setIsUserDropdownOpen(!isUserDropdownOpen);\r\n  }, [isUserDropdownOpen]);\r\n\r\n  return (\r\n    <div className=\"flex h-screen overflow-hidden\">\r\n      {/* Mobile sidebar overlay */}\r\n      {isMobileSidebarOpen && (\r\n        <div \r\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden\"\r\n          onClick={() => setIsMobileSidebarOpen(false)}\r\n        />\r\n      )}\r\n\r\n      {/* Sidebar */}\r\n      <aside className={`\r\n        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out gpu-accelerated\r\n        ${isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}\r\n      `}>\r\n        <div className=\"flex flex-col h-full\">\r\n          {/* Logo/Header */}\r\n          <div className=\"h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700\">\r\n            <div className=\"flex items-center\">\r\n              <Image \r\n                src=\"/images/macra-logo.png\" \r\n                alt=\"MACRA Logo\" \r\n                className=\"max-h-12 w-auto\" \r\n                width={120}\r\n                height={48}\r\n                priority\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Navigation */}\r\n          <nav className=\"mt-6 px-4 side-nav\">\r\n            {/* Main Navigation */}\r\n            <div className=\"space-y-1\">\r\n              {navigationItems.map((item) => (\r\n                <Link\r\n                  key={item.name}\r\n                  href={item.href}\r\n                  onClick={() => handleNavClick(item.href, item.name)}\r\n                  onMouseEnter={() => handleNavHover(item.href)}\r\n                  className={`\r\n                    flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated\r\n                    ${item.current\r\n                      ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-r-2 border-red-600 dark:border-red-400'\r\n                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100'\r\n                    }\r\n                  `}\r\n                >\r\n                  <div className={`w-5 h-5 flex items-center justify-center mr-3 ${item.current ? 'text-red-600 dark:text-red-400' : ''}`}>\r\n                    <i className={item.icon}></i>\r\n                  </div>\r\n                  {item.name}\r\n                </Link>\r\n              ))}\r\n            </div>\r\n\r\n            {/* Support Section */}\r\n            <div className=\"mt-8\">\r\n              <h3 className=\"px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                Support\r\n              </h3>\r\n              <div className=\"mt-2 space-y-1\">\r\n                {supportItems.map((item) => (\r\n                  <Link\r\n                    key={item.name}\r\n                    href={item.href}\r\n                    onClick={() => handleNavClick(item.href, item.name)}\r\n                    onMouseEnter={() => handleNavHover(item.href)}\r\n                    className=\"flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100\"\r\n                  >\r\n                    <div className=\"w-5 h-5 flex items-center justify-center mr-3\">\r\n                      <i className={item.icon}></i>\r\n                    </div>\r\n                    {item.name}\r\n                  </Link>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </nav>\r\n\r\n          {/* User Info */}\r\n          <div className=\"absolute bottom-0 w-64 p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800\">\r\n            <div className=\"flex items-center space-x-3\">\r\n              <Image\r\n                className=\"h-10 w-10 rounded-full object-cover\"\r\n                src={user?.profile_image || \"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp\"}\r\n                alt=\"Profile\"\r\n                width={40}\r\n                height={40}\r\n              />\r\n              <Link \r\n                href='/customer/profile' \r\n                className=\"flex-1 min-w-0\"\r\n                onClick={() => handleNavClick('/customer/profile', 'Profile')}\r\n              >\r\n                <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100 truncate\">\r\n                  {user ? `${user.first_name} ${user.last_name}` : 'Customer'}\r\n                </p>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 truncate\">\r\n                  {/* {user?.organizationName || 'Organization'} */}\r\n                </p>\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </aside>\r\n\r\n      {/* Main content */}\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        {/* Top header */}\r\n        <header className=\"bg-white dark:bg-gray-800 shadow-sm z-10\">\r\n          <div className=\"flex items-center justify-between h-16 px-4 sm:px-6\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={toggleMobileSidebar}\r\n              className=\"md:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none\"\r\n              aria-label=\"Open mobile menu\"\r\n            >\r\n              <div className=\"w-6 h-6 flex items-center justify-center\">\r\n                <i className=\"ri-menu-line ri-lg\"></i>\r\n              </div>\r\n            </button>\r\n            \r\n            <div className=\"flex-1\">\r\n              {breadcrumbs && breadcrumbs.length > 0 && (\r\n                <nav className=\"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400\">\r\n                  {breadcrumbs.map((item, index) => (\r\n                    <React.Fragment key={index}>\r\n                      {index > 0 && <i className=\"ri-arrow-right-s-line\"></i>}\r\n                      {item.href ? (\r\n                        <Link href={item.href} className=\"hover:text-primary\">\r\n                          {item.label}\r\n                        </Link>\r\n                      ) : (\r\n                        <span className=\"text-gray-900 dark:text-gray-100\">{item.label}</span>\r\n                      )}\r\n                    </React.Fragment>\r\n                  ))}\r\n                </nav>\r\n              )}\r\n            </div>\r\n            \r\n            <div className=\"flex items-center\">\r\n              <button\r\n                type=\"button\"\r\n                className=\"flex-shrink-0 p-1 mr-4 text-gray-500 dark:text-gray-400 rounded-full hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none relative\"\r\n              >\r\n                <span className=\"sr-only\">View notifications</span>\r\n                <div className=\"w-6 h-6 flex items-center justify-center\">\r\n                  <i className=\"ri-notification-3-line ri-lg\"></i>\r\n                </div>\r\n                <span className=\"absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white dark:ring-gray-800\"></span>\r\n              </button>\r\n              \r\n              <div className=\"relative\">\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={toggleUserDropdown}\r\n                  className=\"flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n                >\r\n                  <span className=\"sr-only\">Open user menu</span>\r\n                  <Image\r\n                    className=\"h-8 w-8 rounded-full\"\r\n                    src={user?.profile_image || \"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp\"}\r\n                    alt=\"Profile\"\r\n                    width={32}\r\n                    height={32}\r\n                  />\r\n                </button>\r\n                \r\n                {isUserDropdownOpen && (\r\n                  <div className=\"absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 dark:ring-gray-700 z-50\">\r\n                    <div className=\"py-1\">\r\n                      <Link\r\n                        href=\"/customer/profile\"\r\n                        className=\"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        onClick={() => {\r\n                          handleNavClick('/customer/profile', 'Profile');\r\n                          setIsUserDropdownOpen(false);\r\n                        }}\r\n                      >\r\n                        Your Profile\r\n                      </Link>\r\n          \r\n                      <LogoutButton\r\n                        variant=\"text\"\r\n                        size=\"sm\"\r\n                        className=\"w-full justify-start text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        showConfirmation={true}\r\n                      >\r\n                        Sign out\r\n                      </LogoutButton>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </header>\r\n\r\n        {/* Main content area */}\r\n        <main className=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900\">\r\n          <div className=\"py-6\">\r\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n              {children}\r\n            </div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CustomerLayout;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAoBA,MAAM,iBAAgD,CAAC,EAAE,QAAQ,EAAE,WAAW,EAAE;;IAC9E,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,aAAU,AAAD;IAEhC,6DAA6D;IAC7D,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;mDAAE,IAAM;gBACpC;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;aACD;kDAAE;QAAC;KAAS;IAEb,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;gDAAE,IAAM;gBACjC;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;gBACR;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;gBACR;aACD;+CAAE,EAAE;IAEL,yDAAyD;IACzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;0DAAgB;oBACpB,MAAM,gBAAgB;wBACpB;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;qBACD;oBAED,cAAc,OAAO;kEAAC,CAAA;4BACpB,OAAO,QAAQ,CAAC;wBAClB;;gBACF;;YAEA,4DAA4D;YAC5D,MAAM,QAAQ,WAAW,eAAe;YACxC;4CAAO,IAAM,aAAa;;QAC5B;mCAAG;QAAC;KAAO;IAEX,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE;YACtC,uBAAuB,CAAC;QAC1B;0DAAG;QAAC;KAAoB;IAExB,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC,MAAc;YAChD,MAAM,eAA0C;gBAC9C,aAAa;gBACb,yBAAyB;gBACzB,0BAA0B;gBAC1B,iCAAiC;gBACjC,sBAAsB;gBACtB,uBAAuB;gBACvB,yBAAyB;gBACzB,uBAAuB;gBACvB,6BAA6B;gBAC7B,kBAAkB;gBAClB,qBAAqB;gBACrB,sBAAsB;YACxB;YAEA,MAAM,UAAU,YAAY,CAAC,KAAK,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,CAAC;YAC1D,WAAW;YACX,uBAAuB;QACzB;qDAAG;QAAC;KAAW;IAEf,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC;YAClC,2CAA2C;YAC3C,OAAO,QAAQ,CAAC;QAClB;qDAAG;QAAC;KAAO;IAEX,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE;YACrC,sBAAsB,CAAC;QACzB;yDAAG;QAAC;KAAmB;IAEvB,qBACE,6LAAC;QAAI,WAAU;;YAEZ,qCACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,uBAAuB;;;;;;0BAK1C,6LAAC;gBAAM,WAAW,CAAC;;QAEjB,EAAE,sBAAsB,kBAAkB,qCAAqC;MACjF,CAAC;0BACC,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,WAAU;oCACV,OAAO;oCACP,QAAQ;oCACR,QAAQ;;;;;;;;;;;;;;;;sCAMd,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC,+JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;4CAClD,cAAc,IAAM,eAAe,KAAK,IAAI;4CAC5C,WAAW,CAAC;;oBAEV,EAAE,KAAK,OAAO,GACV,8GACA,wHACH;kBACH,CAAC;;8DAED,6LAAC;oDAAI,WAAW,CAAC,8CAA8C,EAAE,KAAK,OAAO,GAAG,mCAAmC,IAAI;8DACrH,cAAA,6LAAC;wDAAE,WAAW,KAAK,IAAI;;;;;;;;;;;gDAExB,KAAK,IAAI;;2CAfL,KAAK,IAAI;;;;;;;;;;8CAqBpB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAuF;;;;;;sDAGrG,6LAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,6LAAC,+JAAA,CAAA,UAAI;oDAEH,MAAM,KAAK,IAAI;oDACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;oDAClD,cAAc,IAAM,eAAe,KAAK,IAAI;oDAC5C,WAAU;;sEAEV,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAE,WAAW,KAAK,IAAI;;;;;;;;;;;wDAExB,KAAK,IAAI;;mDATL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;sCAiBxB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,gIAAA,CAAA,UAAK;wCACJ,WAAU;wCACV,KAAK,MAAM,iBAAiB;wCAC5B,KAAI;wCACJ,OAAO;wCACP,QAAQ;;;;;;kDAEV,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,eAAe,qBAAqB;;0DAEnD,6LAAC;gDAAE,WAAU;0DACV,OAAO,GAAG,KAAK,UAAU,CAAC,CAAC,EAAE,KAAK,SAAS,EAAE,GAAG;;;;;;0DAEnD,6LAAC;gDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUvB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAO,WAAU;kCAChB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,cAAW;8CAEX,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;;;;;;;;;;;;;;;;8CAIjB,6LAAC;oCAAI,WAAU;8CACZ,eAAe,YAAY,MAAM,GAAG,mBACnC,6LAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;;oDACZ,QAAQ,mBAAK,6LAAC;wDAAE,WAAU;;;;;;oDAC1B,KAAK,IAAI,iBACR,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAM,KAAK,IAAI;wDAAE,WAAU;kEAC9B,KAAK,KAAK;;;;;6EAGb,6LAAC;wDAAK,WAAU;kEAAoC,KAAK,KAAK;;;;;;;+CAP7C;;;;;;;;;;;;;;;8CAe7B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,WAAU;;8DAEV,6LAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAE,WAAU;;;;;;;;;;;8DAEf,6LAAC;oDAAK,WAAU;;;;;;;;;;;;sDAGlB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;;sEAEV,6LAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,6LAAC,gIAAA,CAAA,UAAK;4DACJ,WAAU;4DACV,KAAK,MAAM,iBAAiB;4DAC5B,KAAI;4DACJ,OAAO;4DACP,QAAQ;;;;;;;;;;;;gDAIX,oCACC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,+JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;gEACV,SAAS;oEACP,eAAe,qBAAqB;oEACpC,sBAAsB;gEACxB;0EACD;;;;;;0EAID,6LAAC,qIAAA,CAAA,UAAY;gEACX,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,kBAAkB;0EACnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAYf,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GAlVM;;QAGa,qIAAA,CAAA,cAAW;QACb,qIAAA,CAAA,YAAS;QACC,kIAAA,CAAA,UAAO;QACT,qIAAA,CAAA,aAAU;;;KAN7B;uCAoVS", "debugId": null}}, {"offset": {"line": 924, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/forms/TextInput.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { forwardRef } from 'react';\r\n\r\ninterface TextInputProps extends React.InputHTMLAttributes<HTMLInputElement> {\r\n  label?: React.ReactNode;\r\n  error?: string;\r\n  helperText?: string;\r\n  variant?: 'default' | 'small';\r\n  fullWidth?: boolean;\r\n}\r\n\r\nconst TextInput = forwardRef<HTMLInputElement, TextInputProps>(({\r\n  label,\r\n  error,\r\n  helperText,\r\n  variant = 'default',\r\n  fullWidth = true,\r\n  className = '',\r\n  required,\r\n  disabled,\r\n  ...props\r\n}, ref) => {\r\n  // Base input styling with proper text visibility for all modes - force text color to ensure visibility\r\n  const baseInputClass = `px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 ${\r\n    fullWidth ? 'w-full' : ''\r\n  } ${variant === 'small' ? 'py-1.5 text-sm' : 'py-2'}`;\r\n\r\n  // Error and disabled states\r\n  const inputClass = `${baseInputClass} ${\r\n    error \r\n      ? \"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500\" \r\n      : \"border-gray-300 dark:border-gray-600\"\r\n  } ${\r\n    disabled \r\n      ? \"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800\" \r\n      : \"\"\r\n  } ${className}`;\r\n\r\n  const labelClass = `block font-medium text-gray-700 dark:text-gray-300 mb-2 ${\r\n    variant === 'small' ? 'text-xs text-gray-600 dark:text-gray-400 mb-1' : 'text-sm'\r\n  }`;\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      {label && (\r\n        <label className={labelClass}>\r\n          {label}\r\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\r\n        </label>\r\n      )}\r\n      \r\n      <input\r\n        ref={ref}\r\n        className={inputClass}\r\n        disabled={disabled}\r\n        required={required}\r\n        {...props}\r\n      />\r\n      \r\n      {error && (\r\n        <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\r\n          {error}\r\n        </p>\r\n      )}\r\n      \r\n      {helperText && !error && (\r\n        <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n          {helperText}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n});\r\n\r\nTextInput.displayName = 'TextInput';\r\n\r\nexport default TextInput;"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAYA,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OAAoC,CAAC,EAC9D,KAAK,EACL,KAAK,EACL,UAAU,EACV,UAAU,SAAS,EACnB,YAAY,IAAI,EAChB,YAAY,EAAE,EACd,QAAQ,EACR,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,uGAAuG;IACvG,MAAM,iBAAiB,CAAC,4OAA4O,EAClQ,YAAY,WAAW,GACxB,CAAC,EAAE,YAAY,UAAU,mBAAmB,QAAQ;IAErD,4BAA4B;IAC5B,MAAM,aAAa,GAAG,eAAe,CAAC,EACpC,QACI,+EACA,uCACL,CAAC,EACA,WACI,8DACA,GACL,CAAC,EAAE,WAAW;IAEf,MAAM,aAAa,CAAC,wDAAwD,EAC1E,YAAY,UAAU,kDAAkD,WACxE;IAEF,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBAAM,WAAW;;oBACf;oBACA,0BAAY,6LAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,6LAAC;gBACC,KAAK;gBACL,WAAW;gBACX,UAAU;gBACV,UAAU;gBACT,GAAG,KAAK;;;;;;YAGV,uBACC,6LAAC;gBAAE,WAAU;0BACV;;;;;;YAIJ,cAAc,CAAC,uBACd,6LAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;;AAEA,UAAU,WAAW,GAAG;uCAET", "debugId": null}}, {"offset": {"line": 1008, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/forms/TextArea.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { forwardRef } from 'react';\r\n\r\ninterface TextAreaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {\r\n  label?: React.ReactNode;\r\n  error?: string;\r\n  helperText?: string;\r\n  variant?: 'default' | 'small';\r\n  fullWidth?: boolean;\r\n}\r\n\r\nconst TextArea = forwardRef<HTMLTextAreaElement, TextAreaProps>(({\r\n  label,\r\n  error,\r\n  helperText,\r\n  variant = 'default',\r\n  fullWidth = true,\r\n  className = '',\r\n  required,\r\n  disabled,\r\n  rows = 3,\r\n  ...props\r\n}, ref) => {\r\n  // Base textarea styling with proper text visibility for all modes - force text color to ensure visibility\r\n  const baseTextAreaClass = `px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 resize-y ${\r\n    fullWidth ? 'w-full' : ''\r\n  } ${variant === 'small' ? 'py-1.5 text-sm' : 'py-2'}`;\r\n\r\n  // Error and disabled states\r\n  const textAreaClass = `${baseTextAreaClass} ${\r\n    error \r\n      ? \"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500\" \r\n      : \"border-gray-300 dark:border-gray-600\"\r\n  } ${\r\n    disabled \r\n      ? \"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800\" \r\n      : \"\"\r\n  } ${className}`;\r\n\r\n  const labelClass = `block font-medium text-gray-700 dark:text-gray-300 mb-2 ${\r\n    variant === 'small' ? 'text-xs text-gray-600 dark:text-gray-400 mb-1' : 'text-sm'\r\n  }`;\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      {label && (\r\n        <label className={labelClass}>\r\n          {label}\r\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\r\n        </label>\r\n      )}\r\n      \r\n      <textarea\r\n        ref={ref}\r\n        className={textAreaClass}\r\n        disabled={disabled}\r\n        required={required}\r\n        rows={rows}\r\n        {...props}\r\n      />\r\n      \r\n      {error && (\r\n        <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\r\n          {error}\r\n        </p>\r\n      )}\r\n      \r\n      {helperText && !error && (\r\n        <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n          {helperText}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n});\r\n\r\nTextArea.displayName = 'TextArea';\r\n\r\nexport default TextArea;"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAYA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OAAsC,CAAC,EAC/D,KAAK,EACL,KAAK,EACL,UAAU,EACV,UAAU,SAAS,EACnB,YAAY,IAAI,EAChB,YAAY,EAAE,EACd,QAAQ,EACR,QAAQ,EACR,OAAO,CAAC,EACR,GAAG,OACJ,EAAE;IACD,0GAA0G;IAC1G,MAAM,oBAAoB,CAAC,qPAAqP,EAC9Q,YAAY,WAAW,GACxB,CAAC,EAAE,YAAY,UAAU,mBAAmB,QAAQ;IAErD,4BAA4B;IAC5B,MAAM,gBAAgB,GAAG,kBAAkB,CAAC,EAC1C,QACI,+EACA,uCACL,CAAC,EACA,WACI,8DACA,GACL,CAAC,EAAE,WAAW;IAEf,MAAM,aAAa,CAAC,wDAAwD,EAC1E,YAAY,UAAU,kDAAkD,WACxE;IAEF,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBAAM,WAAW;;oBACf;oBACA,0BAAY,6LAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,6LAAC;gBACC,KAAK;gBACL,WAAW;gBACX,UAAU;gBACV,UAAU;gBACV,MAAM;gBACL,GAAG,KAAK;;;;;;YAGV,uBACC,6LAAC;gBAAE,WAAU;0BACV;;;;;;YAIJ,cAAc,CAAC,uBACd,6LAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;;AAEA,SAAS,WAAW,GAAG;uCAER", "debugId": null}}, {"offset": {"line": 1093, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/forms/Select.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { forwardRef } from 'react';\r\n\r\ninterface SelectOption {\r\n  value: string;\r\n  label: string;\r\n}\r\n\r\ninterface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {\r\n  label?: React.ReactNode;\r\n  error?: string;\r\n  helperText?: string;\r\n  variant?: 'default' | 'small';\r\n  fullWidth?: boolean;\r\n  options?: SelectOption[];\r\n  children?: React.ReactNode;\r\n}\r\n\r\nconst Select = forwardRef<HTMLSelectElement, SelectProps>(({\r\n  label,\r\n  error,\r\n  helperText,\r\n  variant = 'default',\r\n  fullWidth = true,\r\n  className = '',\r\n  required,\r\n  disabled,\r\n  options,\r\n  children,\r\n  ...props\r\n}, ref) => {\r\n  // Base select styling with proper text visibility for all modes\r\n  const baseSelectClass = `px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white transition-colors duration-200 ${\r\n    fullWidth ? 'w-full' : ''\r\n  } ${variant === 'small' ? 'py-1.5 text-sm' : 'py-2'}`;\r\n\r\n  // Error and disabled states\r\n  const selectClass = `${baseSelectClass} ${\r\n    error \r\n      ? \"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500\" \r\n      : \"border-gray-300 dark:border-gray-600\"\r\n  } ${\r\n    disabled \r\n      ? \"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800\" \r\n      : \"\"\r\n  } ${className}`;\r\n\r\n  const labelClass = `block font-medium text-gray-700 dark:text-gray-300 mb-2 ${\r\n    variant === 'small' ? 'text-xs text-gray-600 dark:text-gray-400 mb-1' : 'text-sm'\r\n  }`;\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      {label && (\r\n        <label className={labelClass}>\r\n          {label}\r\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\r\n        </label>\r\n      )}\r\n      \r\n      <select\r\n        ref={ref}\r\n        className={selectClass}\r\n        disabled={disabled}\r\n        required={required}\r\n        {...props}\r\n      >\r\n        {options ? (\r\n          options.map((option) => (\r\n            <option key={option.value} value={option.value}>\r\n              {option.label}\r\n            </option>\r\n          ))\r\n        ) : (\r\n          children\r\n        )}\r\n      </select>\r\n      \r\n      {error && (\r\n        <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\r\n          {error}\r\n        </p>\r\n      )}\r\n      \r\n      {helperText && !error && (\r\n        <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n          {helperText}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n});\r\n\r\nSelect.displayName = 'Select';\r\n\r\nexport default Select;"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAmBA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OAAkC,CAAC,EACzD,KAAK,EACL,KAAK,EACL,UAAU,EACV,UAAU,SAAS,EACnB,YAAY,IAAI,EAChB,YAAY,EAAE,EACd,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,gEAAgE;IAChE,MAAM,kBAAkB,CAAC,6LAA6L,EACpN,YAAY,WAAW,GACxB,CAAC,EAAE,YAAY,UAAU,mBAAmB,QAAQ;IAErD,4BAA4B;IAC5B,MAAM,cAAc,GAAG,gBAAgB,CAAC,EACtC,QACI,+EACA,uCACL,CAAC,EACA,WACI,8DACA,GACL,CAAC,EAAE,WAAW;IAEf,MAAM,aAAa,CAAC,wDAAwD,EAC1E,YAAY,UAAU,kDAAkD,WACxE;IAEF,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBAAM,WAAW;;oBACf;oBACA,0BAAY,6LAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,6LAAC;gBACC,KAAK;gBACL,WAAW;gBACX,UAAU;gBACV,UAAU;gBACT,GAAG,KAAK;0BAER,UACC,QAAQ,GAAG,CAAC,CAAC,uBACX,6LAAC;wBAA0B,OAAO,OAAO,KAAK;kCAC3C,OAAO,KAAK;uBADF,OAAO,KAAK;;;;gCAK3B;;;;;;YAIH,uBACC,6LAAC;gBAAE,WAAU;0BACV;;;;;;YAIJ,cAAc,CAAC,uBACd,6LAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;;AAEA,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 1185, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/app/customer/resources/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport Link from 'next/link';\r\nimport { useRouter } from 'next/navigation';\r\nimport CustomerLayout from '@/components/customer/CustomerLayout';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport TextInput from '@/components/forms/TextInput';\r\nimport TextArea from '@/components/forms/TextArea';\r\nimport Select from '@/components/forms/Select';\r\n\r\ninterface FileUpload {\r\n  name: string;\r\n  size: number;\r\n  file: File;\r\n}\r\n\r\nconst RequestResourcePage = () => {\r\n  const { isAuthenticated, loading } = useAuth();\r\n  const router = useRouter();\r\n  const [mounted, setMounted] = useState(false);\r\n\r\n  // Form state\r\n  const [formData, setFormData] = useState({\r\n    requestType: '',\r\n    subject: '',\r\n    description: '',\r\n    relatedLicense: '',\r\n    contactMethod: [] as string[]\r\n  });\r\n\r\n  const [uploadedFiles, setUploadedFiles] = useState<FileUpload[]>([]);\r\n  const [charCount, setCharCount] = useState(0);\r\n  const [showSuccessModal, setShowSuccessModal] = useState(false);\r\n  const [ticketNumber, setTicketNumber] = useState('');\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n\r\n  // Handle mounting to prevent hydration issues\r\n  useEffect(() => {\r\n    setMounted(true);\r\n  }, []);\r\n\r\n  // Redirect to customer login if not authenticated\r\n  useEffect(() => {\r\n    if (mounted && !loading && !isAuthenticated) {\r\n      router.push('/customer/auth/login');\r\n    }\r\n  }, [mounted, loading, isAuthenticated, router]);\r\n\r\n  // Show loading state during hydration or auth check\r\n  if (!mounted || loading) {\r\n    return (\r\n      <CustomerLayout>\r\n        <div className=\"max-w-7xl mx-auto\">\r\n          <div className=\"flex items-center justify-center min-h-[400px]\">\r\n            <div className=\"text-center\">\r\n              <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4\"></div>\r\n              <p className=\"text-gray-600 dark:text-gray-400\">Loading...</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </CustomerLayout>\r\n    );\r\n  }\r\n\r\n  // Don't render the form if not authenticated\r\n  if (!isAuthenticated) {\r\n    return null;\r\n  }\r\n\r\n  // Handle form input changes\r\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\r\n    const { name, value } = e.target;\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [name]: value\r\n    }));\r\n\r\n    // Update character count for description\r\n    if (name === 'description') {\r\n      setCharCount(value.length);\r\n    }\r\n  };\r\n\r\n  // Handle checkbox changes for contact method\r\n  const handleContactMethodChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const { value, checked } = e.target;\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      contactMethod: checked \r\n        ? [...prev.contactMethod, value]\r\n        : prev.contactMethod.filter(method => method !== value)\r\n    }));\r\n  };\r\n\r\n  // Handle file upload\r\n  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const files = Array.from(e.target.files || []);\r\n    \r\n    files.forEach(file => {\r\n      if (file.size <= 10 * 1024 * 1024) { // 10MB limit\r\n        const newFile: FileUpload = {\r\n          name: file.name,\r\n          size: file.size,\r\n          file: file\r\n        };\r\n        setUploadedFiles(prev => [...prev, newFile]);\r\n      } else {\r\n        alert(`File ${file.name} is too large. Maximum size is 10MB.`);\r\n      }\r\n    });\r\n\r\n    // Reset file input\r\n    e.target.value = '';\r\n  };\r\n\r\n  // Remove uploaded file\r\n  const removeFile = (fileName: string) => {\r\n    setUploadedFiles(prev => prev.filter(file => file.name !== fileName));\r\n  };\r\n\r\n  // Handle form submission\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    setIsSubmitting(true);\r\n\r\n    try {\r\n      // Generate random ticket number\r\n      const randomNum = Math.floor(Math.random() * 1000) + 1;\r\n      const newTicketNumber = `#REQ-2025-${String(randomNum).padStart(3, '0')}`;\r\n      setTicketNumber(newTicketNumber);\r\n\r\n      // Here you would typically send the data to your API\r\n      // await submitResourceRequest(formData, uploadedFiles);\r\n\r\n      // Show success modal\r\n      setShowSuccessModal(true);\r\n    } catch (error) {\r\n      console.error('Error submitting request:', error);\r\n      alert('Failed to submit request. Please try again.');\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Save as draft\r\n  const saveDraft = () => {\r\n    // Here you would typically save to localStorage or API\r\n    alert('Draft saved successfully! You can continue editing later.');\r\n  };\r\n\r\n  // Close success modal and reset form\r\n  const closeSuccessModal = () => {\r\n    setShowSuccessModal(false);\r\n    setFormData({\r\n      requestType: '',\r\n      subject: '',\r\n      description: '',\r\n      relatedLicense: '',\r\n      contactMethod: []\r\n    });\r\n    setUploadedFiles([]);\r\n    setCharCount(0);\r\n  };\r\n\r\n  return (\r\n    <CustomerLayout>\r\n      <div className=\"max-w-7xl mx-auto\">\r\n        {/* Header */}\r\n        <div className=\"mb-8\">\r\n          <div className=\"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mb-4\">\r\n            <Link href=\"/customer\" className=\"hover:text-primary\">Dashboard</Link>\r\n            <i className=\"ri-arrow-right-s-line\"></i>\r\n            <span className=\"text-gray-900 dark:text-gray-100\">Request Resource</span>\r\n          </div>\r\n          <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\">\r\n            <div>\r\n              <h1 className=\"text-3xl font-bold text-gray-900 dark:text-gray-100\">Request Resource</h1>\r\n              <p className=\"mt-2 text-gray-600 dark:text-gray-400\">Submit requests for additional resources, support, or services from MACRA.</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\r\n          {/* Main Form */}\r\n          <div className=\"lg:col-span-2\">\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\r\n              <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n                {/* Request Type */}\r\n                <Select\r\n                  label={\r\n                    <span>\r\n                      <i className=\"ri-folder-line mr-2 text-primary\"></i>\r\n                      Request Type\r\n                    </span>\r\n                  }\r\n                  id=\"requestType\"\r\n                  name=\"requestType\"\r\n                  required\r\n                  value={formData.requestType}\r\n                  onChange={handleInputChange}\r\n                >\r\n                  <option value=\"\">Select request type</option>\r\n                  <option value=\"technical-support\">Technical Support</option>\r\n                  <option value=\"license-modification\">License Modification</option>\r\n                  <option value=\"spectrum-allocation\">Spectrum Allocation</option>\r\n                  <option value=\"compliance-guidance\">Compliance Guidance</option>\r\n                  <option value=\"documentation\">Documentation Request</option>\r\n                  <option value=\"training\">Training & Capacity Building</option>\r\n                  <option value=\"consultation\">Regulatory Consultation</option>\r\n                  <option value=\"other\">Other</option>\r\n                </Select>\r\n\r\n                {/* Subject */}\r\n                <TextInput\r\n                  label={\r\n                    <span>\r\n                      <i className=\"ri-text mr-2 text-primary\"></i>\r\n                      Subject\r\n                    </span>\r\n                  }\r\n                  id=\"subject\"\r\n                  name=\"subject\"\r\n                  required\r\n                  value={formData.subject}\r\n                  onChange={handleInputChange}\r\n                  placeholder=\"Brief description of your request\"\r\n                />\r\n\r\n                {/* Description */}\r\n                <div>\r\n                  <TextArea\r\n                    label={\r\n                      <span>\r\n                        <i className=\"ri-file-text-line mr-2 text-primary\"></i>\r\n                        Detailed Description\r\n                      </span>\r\n                    }\r\n                    id=\"description\"\r\n                    name=\"description\"\r\n                    rows={6}\r\n                    required\r\n                    value={formData.description}\r\n                    onChange={handleInputChange}\r\n                    placeholder=\"Provide detailed information about your request, including any specific requirements or context...\"\r\n                    maxLength={1000}\r\n                  />\r\n                  <div className=\"mt-2 text-sm text-gray-500 dark:text-gray-400\">\r\n                    <span className={charCount > 1000 ? 'text-red-500' : ''}>{charCount}</span>/1000 characters\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Related License */}\r\n                <Select\r\n                  label={\r\n                    <span>\r\n                      <i className=\"ri-award-line mr-2 text-primary\"></i>\r\n                      Related License (Optional)\r\n                    </span>\r\n                  }\r\n                  id=\"relatedLicense\"\r\n                  name=\"relatedLicense\"\r\n                  value={formData.relatedLicense}\r\n                  onChange={handleInputChange}\r\n                >\r\n                  <option value=\"\">Select related license</option>\r\n                  <option value=\"NSL-2025-001\">NSL-2025-001 - Internet Service Provider License</option>\r\n                  <option value=\"RBL-2025-002\">RBL-2025-002 - Radio Broadcasting License</option>\r\n                  <option value=\"TVL-2025-003\">TVL-2025-003 - Television Broadcasting License</option>\r\n                  <option value=\"MNL-2023-001\">MNL-2023-001 - Mobile Network License</option>\r\n                  <option value=\"SCL-2025-004\">SCL-2025-004 - Satellite Communications License</option>\r\n                  <option value=\"PSL-2025-005\">PSL-2025-005 - Postal Services License</option>\r\n                </Select>\r\n\r\n                {/* File Attachments */}\r\n                <div>\r\n                  <label htmlFor=\"fileInput\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                    <i className=\"ri-attachment-line mr-2 text-primary\"></i>\r\n                    Attachments (Optional)\r\n                  </label>\r\n                  <div className=\"border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center hover:border-primary transition-colors\">\r\n                    <input \r\n                      type=\"file\" \r\n                      id=\"fileInput\" \r\n                      multiple \r\n                      accept=\".pdf,.doc,.docx,.jpg,.jpeg,.png\" \r\n                      onChange={handleFileUpload}\r\n                      aria-label=\"Upload attachment files\"\r\n                      className=\"hidden\"\r\n                    />\r\n                    <div className=\"space-y-2\">\r\n                      <i className=\"ri-upload-cloud-line text-4xl text-gray-400\"></i>\r\n                      <div>\r\n                        <button \r\n                          type=\"button\" \r\n                          onClick={() => document.getElementById('fileInput')?.click()}\r\n                          className=\"text-primary hover:text-primary font-medium\"\r\n                        >\r\n                          Click to upload files\r\n                        </button>\r\n                        <span className=\"text-gray-500 dark:text-gray-400\"> or drag and drop</span>\r\n                      </div>\r\n                      <p className=\"text-xs text-gray-500 dark:text-gray-400\">PDF, DOC, DOCX, JPG, PNG up to 10MB each</p>\r\n                    </div>\r\n                  </div>\r\n                  \r\n                  {/* File List */}\r\n                  {uploadedFiles.length > 0 && (\r\n                    <div className=\"mt-3 space-y-2\">\r\n                      {uploadedFiles.map((file, index) => (\r\n                        <div key={index} className=\"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\">\r\n                          <div className=\"flex items-center\">\r\n                            <i className=\"ri-file-line mr-3 text-primary\"></i>\r\n                            <div>\r\n                              <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">{file.name}</div>\r\n                              <div className=\"text-xs text-gray-500 dark:text-gray-400\">{(file.size / 1024 / 1024).toFixed(2)} MB</div>\r\n                            </div>\r\n                          </div>\r\n                          <button \r\n                            type=\"button\" \r\n                            onClick={() => removeFile(file.name)}\r\n                            className=\"text-red-500 hover:text-red-700\"\r\n                            aria-label={`Remove ${file.name}`}\r\n                            title={`Remove ${file.name}`}\r\n                          >\r\n                            <i className=\"ri-close-line\"></i>\r\n                          </button>\r\n                        </div>\r\n                      ))}\r\n                    </div>\r\n                  )}\r\n                </div>\r\n\r\n                {/* Contact Preference */}\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\r\n                    <i className=\"ri-phone-line mr-2 text-primary\"></i>\r\n                    Preferred Contact Method\r\n                  </label>\r\n                  <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-3\">\r\n                    <label className=\"flex items-center p-3 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700\">\r\n                      <input \r\n                        type=\"checkbox\" \r\n                        name=\"contactMethod\" \r\n                        value=\"email\" \r\n                        checked={formData.contactMethod.includes('email')}\r\n                        onChange={handleContactMethodChange}\r\n                        className=\"rounded border-gray-300 text-primary focus:ring-primary\"\r\n                      />\r\n                      <span className=\"ml-3 text-sm text-gray-900 dark:text-gray-100\">Email</span>\r\n                    </label>\r\n                    <label className=\"flex items-center p-3 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700\">\r\n                      <input \r\n                        type=\"checkbox\" \r\n                        name=\"contactMethod\" \r\n                        value=\"phone\" \r\n                        checked={formData.contactMethod.includes('phone')}\r\n                        onChange={handleContactMethodChange}\r\n                        className=\"rounded border-gray-300 text-primary focus:ring-primary\"\r\n                      />\r\n                      <span className=\"ml-3 text-sm text-gray-900 dark:text-gray-100\">Phone</span>\r\n                    </label>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Submit Button */}\r\n                <div className=\"flex flex-col sm:flex-row gap-3 pt-6\">\r\n                  <button \r\n                    type=\"submit\" \r\n                    disabled={isSubmitting}\r\n                    className=\"flex-1 bg-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                  >\r\n                    <i className=\"ri-send-plane-line mr-2\"></i>\r\n                    {isSubmitting ? 'Submitting...' : 'Submit Request'}\r\n                  </button>\r\n                  <button \r\n                    type=\"button\" \r\n                    onClick={saveDraft}\r\n                    className=\"flex-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-lg font-medium hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all\"\r\n                  >\r\n                    <i className=\"ri-save-line mr-2\"></i>\r\n                    Save as Draft\r\n                  </button>\r\n                </div>\r\n              </form>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Sidebar */}\r\n          <div className=\"space-y-6\">\r\n            {/* Quick Actions */}\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\r\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center\">\r\n                <i className=\"ri-lightning-line mr-2 text-primary\"></i>\r\n                Quick Actions\r\n              </h3>\r\n              <div className=\"space-y-3\">\r\n                <Link href=\"/customer/help\" className=\"flex items-center p-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors\">\r\n                  <i className=\"ri-question-line mr-3 text-primary\"></i>\r\n                  View FAQ\r\n                </Link>\r\n                <Link href=\"/customer/documents\" className=\"flex items-center p-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors\">\r\n                  <i className=\"ri-book-line mr-3 text-primary\"></i>\r\n                  Resource Library\r\n                </Link>\r\n                <Link href=\"/customer/help\" className=\"flex items-center p-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors\">\r\n                  <i className=\"ri-customer-service-line mr-3 text-primary\"></i>\r\n                  Help Center\r\n                </Link>\r\n                <Link href=\"/customer/resources/history\" className=\"flex items-center p-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors\">\r\n                  <i className=\"ri-time-line mr-3 text-primary\"></i>\r\n                  Request History\r\n                </Link>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Response Times */}\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\r\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center\">\r\n                <i className=\"ri-time-line mr-2 text-primary\"></i>\r\n                Expected Response Times\r\n              </h3>\r\n              <div className=\"space-y-3\">\r\n                <div className=\"flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg\">\r\n                  <div className=\"flex items-center\">\r\n                    <div className=\"w-3 h-3 bg-green-500 rounded-full mr-3\"></div>\r\n                    <span className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">Low Priority</span>\r\n                  </div>\r\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">5-7 days</span>\r\n                </div>\r\n                <div className=\"flex items-center justify-between p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg\">\r\n                  <div className=\"flex items-center\">\r\n                    <div className=\"w-3 h-3 bg-yellow-500 rounded-full mr-3\"></div>\r\n                    <span className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">Medium Priority</span>\r\n                  </div>\r\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">2-3 days</span>\r\n                </div>\r\n                <div className=\"flex items-center justify-between p-3 bg-red-50 dark:bg-red-900/20 rounded-lg\">\r\n                  <div className=\"flex items-center\">\r\n                    <div className=\"w-3 h-3 bg-red-500 rounded-full mr-3\"></div>\r\n                    <span className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">High Priority</span>\r\n                  </div>\r\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">24 hours</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Success Modal */}\r\n        {showSuccessModal && (\r\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-xl p-8 max-w-md mx-4\">\r\n              <div className=\"text-center\">\r\n                <div className=\"w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n                  <i className=\"ri-check-line text-2xl text-green-600\"></i>\r\n                </div>\r\n                <h3 className=\"text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2\">Request Submitted Successfully</h3>\r\n                <p className=\"text-gray-600 dark:text-gray-400 mb-6\">\r\n                  Your request has been submitted and assigned ticket number <strong>{ticketNumber}</strong>. You will receive updates via email.\r\n                </p>\r\n                <button \r\n                  onClick={closeSuccessModal}\r\n                  className=\"w-full bg-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-opacity-90 transition-all\"\r\n                >\r\n                  Continue\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </CustomerLayout>\r\n  );\r\n};\r\n\r\nexport default RequestResourcePage;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAiBA,MAAM,sBAAsB;;IAC1B,MAAM,EAAE,eAAe,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,aAAa;IACb,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,aAAa;QACb,SAAS;QACT,aAAa;QACb,gBAAgB;QAChB,eAAe,EAAE;IACnB;IAEA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACnE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,8CAA8C;IAC9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,WAAW;QACb;wCAAG,EAAE;IAEL,kDAAkD;IAClD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,WAAW,CAAC,WAAW,CAAC,iBAAiB;gBAC3C,OAAO,IAAI,CAAC;YACd;QACF;wCAAG;QAAC;QAAS;QAAS;QAAiB;KAAO;IAE9C,oDAAoD;IACpD,IAAI,CAAC,WAAW,SAAS;QACvB,qBACE,6LAAC,mJAAA,CAAA,UAAc;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;IAM5D;IAEA,6CAA6C;IAC7C,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,4BAA4B;IAC5B,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;QAED,yCAAyC;QACzC,IAAI,SAAS,eAAe;YAC1B,aAAa,MAAM,MAAM;QAC3B;IACF;IAEA,6CAA6C;IAC7C,MAAM,4BAA4B,CAAC;QACjC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM;QACnC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,eAAe,UACX;uBAAI,KAAK,aAAa;oBAAE;iBAAM,GAC9B,KAAK,aAAa,CAAC,MAAM,CAAC,CAAA,SAAU,WAAW;YACrD,CAAC;IACH;IAEA,qBAAqB;IACrB,MAAM,mBAAmB,CAAC;QACxB,MAAM,QAAQ,MAAM,IAAI,CAAC,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE;QAE7C,MAAM,OAAO,CAAC,CAAA;YACZ,IAAI,KAAK,IAAI,IAAI,KAAK,OAAO,MAAM;gBACjC,MAAM,UAAsB;oBAC1B,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;oBACf,MAAM;gBACR;gBACA,iBAAiB,CAAA,OAAQ;2BAAI;wBAAM;qBAAQ;YAC7C,OAAO;gBACL,MAAM,CAAC,KAAK,EAAE,KAAK,IAAI,CAAC,oCAAoC,CAAC;YAC/D;QACF;QAEA,mBAAmB;QACnB,EAAE,MAAM,CAAC,KAAK,GAAG;IACnB;IAEA,uBAAuB;IACvB,MAAM,aAAa,CAAC;QAClB,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;IAC7D;IAEA,yBAAyB;IACzB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAEhB,IAAI;YACF,gCAAgC;YAChC,MAAM,YAAY,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ;YACrD,MAAM,kBAAkB,CAAC,UAAU,EAAE,OAAO,WAAW,QAAQ,CAAC,GAAG,MAAM;YACzE,gBAAgB;YAEhB,qDAAqD;YACrD,wDAAwD;YAExD,qBAAqB;YACrB,oBAAoB;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,gBAAgB;IAChB,MAAM,YAAY;QAChB,uDAAuD;QACvD,MAAM;IACR;IAEA,qCAAqC;IACrC,MAAM,oBAAoB;QACxB,oBAAoB;QACpB,YAAY;YACV,aAAa;YACb,SAAS;YACT,aAAa;YACb,gBAAgB;YAChB,eAAe,EAAE;QACnB;QACA,iBAAiB,EAAE;QACnB,aAAa;IACf;IAEA,qBACE,6LAAC,mJAAA,CAAA,UAAc;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAU;8CAAqB;;;;;;8CACtD,6LAAC;oCAAE,WAAU;;;;;;8CACb,6LAAC;oCAAK,WAAU;8CAAmC;;;;;;;;;;;;sCAErD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;kDACpE,6LAAC;wCAAE,WAAU;kDAAwC;;;;;;;;;;;;;;;;;;;;;;;8BAK3D,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,UAAU;oCAAc,WAAU;;sDAEtC,6LAAC,wIAAA,CAAA,UAAM;4CACL,qBACE,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;;;;;;oDAAuC;;;;;;;4CAIxD,IAAG;4CACH,MAAK;4CACL,QAAQ;4CACR,OAAO,SAAS,WAAW;4CAC3B,UAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAG;;;;;;8DACjB,6LAAC;oDAAO,OAAM;8DAAoB;;;;;;8DAClC,6LAAC;oDAAO,OAAM;8DAAuB;;;;;;8DACrC,6LAAC;oDAAO,OAAM;8DAAsB;;;;;;8DACpC,6LAAC;oDAAO,OAAM;8DAAsB;;;;;;8DACpC,6LAAC;oDAAO,OAAM;8DAAgB;;;;;;8DAC9B,6LAAC;oDAAO,OAAM;8DAAW;;;;;;8DACzB,6LAAC;oDAAO,OAAM;8DAAe;;;;;;8DAC7B,6LAAC;oDAAO,OAAM;8DAAQ;;;;;;;;;;;;sDAIxB,6LAAC,2IAAA,CAAA,UAAS;4CACR,qBACE,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;;;;;;oDAAgC;;;;;;;4CAIjD,IAAG;4CACH,MAAK;4CACL,QAAQ;4CACR,OAAO,SAAS,OAAO;4CACvB,UAAU;4CACV,aAAY;;;;;;sDAId,6LAAC;;8DACC,6LAAC,0IAAA,CAAA,UAAQ;oDACP,qBACE,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;;;;;;4DAA0C;;;;;;;oDAI3D,IAAG;oDACH,MAAK;oDACL,MAAM;oDACN,QAAQ;oDACR,OAAO,SAAS,WAAW;oDAC3B,UAAU;oDACV,aAAY;oDACZ,WAAW;;;;;;8DAEb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAW,YAAY,OAAO,iBAAiB;sEAAK;;;;;;wDAAiB;;;;;;;;;;;;;sDAK/E,6LAAC,wIAAA,CAAA,UAAM;4CACL,qBACE,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;;;;;;oDAAsC;;;;;;;4CAIvD,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,cAAc;4CAC9B,UAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAG;;;;;;8DACjB,6LAAC;oDAAO,OAAM;8DAAe;;;;;;8DAC7B,6LAAC;oDAAO,OAAM;8DAAe;;;;;;8DAC7B,6LAAC;oDAAO,OAAM;8DAAe;;;;;;8DAC7B,6LAAC;oDAAO,OAAM;8DAAe;;;;;;8DAC7B,6LAAC;oDAAO,OAAM;8DAAe;;;;;;8DAC7B,6LAAC;oDAAO,OAAM;8DAAe;;;;;;;;;;;;sDAI/B,6LAAC;;8DACC,6LAAC;oDAAM,SAAQ;oDAAY,WAAU;;sEACnC,6LAAC;4DAAE,WAAU;;;;;;wDAA2C;;;;;;;8DAG1D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,MAAK;4DACL,IAAG;4DACH,QAAQ;4DACR,QAAO;4DACP,UAAU;4DACV,cAAW;4DACX,WAAU;;;;;;sEAEZ,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;;;;;;8EACb,6LAAC;;sFACC,6LAAC;4EACC,MAAK;4EACL,SAAS,IAAM,SAAS,cAAc,CAAC,cAAc;4EACrD,WAAU;sFACX;;;;;;sFAGD,6LAAC;4EAAK,WAAU;sFAAmC;;;;;;;;;;;;8EAErD,6LAAC;oEAAE,WAAU;8EAA2C;;;;;;;;;;;;;;;;;;gDAK3D,cAAc,MAAM,GAAG,mBACtB,6LAAC;oDAAI,WAAU;8DACZ,cAAc,GAAG,CAAC,CAAC,MAAM,sBACxB,6LAAC;4DAAgB,WAAU;;8EACzB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;;;;;;sFACb,6LAAC;;8FACC,6LAAC;oFAAI,WAAU;8FAAwD,KAAK,IAAI;;;;;;8FAChF,6LAAC;oFAAI,WAAU;;wFAA4C,CAAC,KAAK,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;wFAAG;;;;;;;;;;;;;;;;;;;8EAGpG,6LAAC;oEACC,MAAK;oEACL,SAAS,IAAM,WAAW,KAAK,IAAI;oEACnC,WAAU;oEACV,cAAY,CAAC,OAAO,EAAE,KAAK,IAAI,EAAE;oEACjC,OAAO,CAAC,OAAO,EAAE,KAAK,IAAI,EAAE;8EAE5B,cAAA,6LAAC;wEAAE,WAAU;;;;;;;;;;;;2DAfP;;;;;;;;;;;;;;;;sDAwBlB,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;;sEACf,6LAAC;4DAAE,WAAU;;;;;;wDAAsC;;;;;;;8DAGrD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAM,WAAU;;8EACf,6LAAC;oEACC,MAAK;oEACL,MAAK;oEACL,OAAM;oEACN,SAAS,SAAS,aAAa,CAAC,QAAQ,CAAC;oEACzC,UAAU;oEACV,WAAU;;;;;;8EAEZ,6LAAC;oEAAK,WAAU;8EAAgD;;;;;;;;;;;;sEAElE,6LAAC;4DAAM,WAAU;;8EACf,6LAAC;oEACC,MAAK;oEACL,MAAK;oEACL,OAAM;oEACN,SAAS,SAAS,aAAa,CAAC,QAAQ,CAAC;oEACzC,UAAU;oEACV,WAAU;;;;;;8EAEZ,6LAAC;oEAAK,WAAU;8EAAgD;;;;;;;;;;;;;;;;;;;;;;;;sDAMtE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,UAAU;oDACV,WAAU;;sEAEV,6LAAC;4DAAE,WAAU;;;;;;wDACZ,eAAe,kBAAkB;;;;;;;8DAEpC,6LAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;;sEAEV,6LAAC;4DAAE,WAAU;;;;;;wDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAS/C,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAE,WAAU;;;;;;gDAA0C;;;;;;;sDAGzD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAiB,WAAU;;sEACpC,6LAAC;4DAAE,WAAU;;;;;;wDAAyC;;;;;;;8DAGxD,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAsB,WAAU;;sEACzC,6LAAC;4DAAE,WAAU;;;;;;wDAAqC;;;;;;;8DAGpD,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAiB,WAAU;;sEACpC,6LAAC;4DAAE,WAAU;;;;;;wDAAiD;;;;;;;8DAGhE,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAA8B,WAAU;;sEACjD,6LAAC;4DAAE,WAAU;;;;;;wDAAqC;;;;;;;;;;;;;;;;;;;8CAOxD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAE,WAAU;;;;;;gDAAqC;;;;;;;sDAGpD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAK,WAAU;8EAAuD;;;;;;;;;;;;sEAEzE,6LAAC;4DAAK,WAAU;sEAA2C;;;;;;;;;;;;8DAE7D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAK,WAAU;8EAAuD;;;;;;;;;;;;sEAEzE,6LAAC;4DAAK,WAAU;sEAA2C;;;;;;;;;;;;8DAE7D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAK,WAAU;8EAAuD;;;;;;;;;;;;sEAEzE,6LAAC;4DAAK,WAAU;sEAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAQpE,kCACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;;;;;;;;;;;8CAEf,6LAAC;oCAAG,WAAU;8CAA8D;;;;;;8CAC5E,6LAAC;oCAAE,WAAU;;wCAAwC;sDACQ,6LAAC;sDAAQ;;;;;;wCAAsB;;;;;;;8CAE5F,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GAzcM;;QACiC,kIAAA,CAAA,UAAO;QAC7B,qIAAA,CAAA,YAAS;;;KAFpB;uCA2cS", "debugId": null}}]}