(()=>{var e={};e.id=2448,e.ids=[2448],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10598:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\applications\\\\apply\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},15885:(e,t,a)=>{"use strict";a.d(t,{U_:()=>n,_l:()=>i,qI:()=>s});class r{set(e,t,a=this.defaultTTL){let r=Date.now();this.cache.set(e,{data:t,timestamp:r,expiresAt:r+a})}get(e){let t=this.cache.get(e);return t?Date.now()>t.expiresAt?(this.cache.delete(e),null):t.data:null}has(e){return null!==this.get(e)}delete(e){return this.cache.delete(e)}clear(){this.cache.clear()}getStats(){return{size:this.cache.size,keys:Array.from(this.cache.keys())}}cleanup(){let e=Date.now(),t=0;for(let[t,a]of this.cache.entries())e>a.expiresAt&&this.cache.delete(t)}async getOrSet(e,t,a=this.defaultTTL){let r=this.get(e);if(null!==r)return r;let s=await t();return this.set(e,s,a),s}invalidatePattern(e){let t=new RegExp(e),a=0;for(let e of this.cache.keys())t.test(e)&&this.cache.delete(e)}constructor(){this.cache=new Map,this.defaultTTL=3e5}}let s=new r,i={LICENSE_TYPES:"license-types",LICENSE_CATEGORIES:"license-categories",LICENSE_CATEGORIES_BY_TYPE:e=>`license-categories-type-${e}`,USER_APPLICATIONS:"user-applications",APPLICATION:e=>`application-${e}`},n={SHORT:12e4,MEDIUM:3e5,LONG:9e5,VERY_LONG:36e5};setInterval(()=>{s.cleanup()},3e5)},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34130:(e,t,a)=>{"use strict";a.d(t,{TG:()=>c});var r=a(12234),s=a(15885);let i=e=>e.toLowerCase().replace(/[^a-z0-9\s]/g,"").replace(/\s+/g,"-").replace(/^-+|-+$/g,"").substring(0,50),n=e=>e.map(e=>({...e,code:i(e.name),children:e.children?n(e.children):void 0})),c={async getLicenseCategories(e={}){let t=new URLSearchParams;return e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(([e,a])=>{Array.isArray(a)?a.forEach(a=>t.append(`filter.${e}`,a)):t.set(`filter.${e}`,a)}),(await r.uE.get(`/license-categories?${t.toString()}`)).data},async getLicenseCategory(e){try{return(await r.uE.get(`/license-categories/${e}`,{timeout:3e4})).data}catch(e){if("ECONNABORTED"===e.code)throw Error("Request timeout - please try again");throw e}},async getLicenseCategoriesByType(e){try{return(await r.uE.get(`/license-categories/by-license-type/${e}`,{timeout:3e4})).data}catch(e){if("ECONNABORTED"===e.code)throw Error("Request timeout - please try again");if(e.response?.status===429)throw Error("Too many requests - please wait a moment and try again");throw e}},createLicenseCategory:async e=>(await r.uE.post("/license-categories",e)).data,updateLicenseCategory:async(e,t)=>(await r.uE.put(`/license-categories/${e}`,t)).data,deleteLicenseCategory:async e=>(await r.uE.delete(`/license-categories/${e}`)).data,async getAllLicenseCategories(){return s.qI.getOrSet(s._l.LICENSE_CATEGORIES,async()=>n((await this.getLicenseCategories({limit:100})).data),s.U_.LONG)},getCategoryTree:async e=>s.qI.getOrSet(`category-tree-${e}`,async()=>n((await r.uE.get(`/license-categories/license-type/${e}/tree`)).data),s.U_.MEDIUM),getRootCategories:async e=>s.qI.getOrSet(`root-categories-${e}`,async()=>(await r.uE.get(`/license-categories/license-type/${e}/root`)).data,s.U_.MEDIUM),async getCategoriesForParentSelection(e,t){try{try{let a=await r.uE.get(`/license-categories/license-type/${e}/for-parent-selection`,{params:t?{excludeId:t}:{}});if(a.data&&Array.isArray(a.data.data))return a.data.data;return[]}catch(s){let a=await r.uE.get(`/license-categories/by-license-type/${e}`);if(!(a.data&&Array.isArray(a.data)))return[];{let e=a.data;return t&&(e=e.filter(e=>e.license_category_id!==t)),e}}}catch(e){return[]}},getPotentialParents:async(e,t)=>(await r.uE.get(`/license-categories/license-type/${e}/potential-parents`,{params:t?{excludeId:t}:{}})).data}},44877:(e,t,a)=>{Promise.resolve().then(a.bind(a,47116))},47085:(e,t,a)=>{Promise.resolve().then(a.bind(a,10598))},47116:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>p});var r=a(60687),s=a(43210),i=a(16189),n=a(63213),c=a(34130),o=a(78637);let p=()=>{let e=(0,i.useRouter)(),t=(0,i.useSearchParams)(),{isAuthenticated:a,loading:p}=(0,n.A)(),l=t.get("license_category_id"),u=t.get("application_id"),d=t.get("step");return(0,s.useEffect)(()=>{(async()=>{if(!p&&!a)return e.push("/customer/auth/login");if(a&&!p){if(!l)return e.push("/customer/applications");try{if(!await c.TG.getLicenseCategory(l))return void e.push("/customer/applications");if(u)try{let t=await o.k.getApplication(u);if(!t||t.license_category_id!==l)return void e.push("/customer/applications")}catch(t){e.push("/customer/applications");return}let t=d||"applicant-info",a=new URLSearchParams;a.set("license_category_id",l),u&&a.set("application_id",u),e.push(`/customer/applications/apply/${t}?${a.toString()}`)}catch(t){e.push("/customer/applications")}}})()},[l,u,d,a,p,e]),(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Redirecting to application form..."})]})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68736:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i,metadata:()=>s});var r=a(37413);let s={title:"Customer Dashboard - Digital Portal",description:"Customer portal for managing licenses and applications"};function i({children:e}){return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50",children:e})}},69437:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>d,tree:()=>p});var r=a(65239),s=a(48088),i=a(88170),n=a.n(i),c=a(30893),o={};for(let e in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>c[e]);a.d(t,o);let p={children:["",{children:["customer",{children:["applications",{children:["apply",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,10598)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,68736)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\page.tsx"],u={require:a,loadChunk:()=>Promise.resolve()},d=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/customer/applications/apply/page",pathname:"/customer/applications/apply",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},70440:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});var r=a(31658);let s=async e=>[{type:"image/x-icon",sizes:"256x256",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},78637:(e,t,a)=>{"use strict";a.d(t,{k:()=>i});var r=a(51278),s=a(12234);let i={async getApplications(e){let t=new URLSearchParams;e?.page&&t.append("page",e.page.toString()),e?.limit&&t.append("limit",e.limit.toString()),e?.search&&t.append("search",e.search),e?.sortBy&&t.append("sortBy",e.sortBy),e?.sortOrder&&t.append("sortOrder",e.sortOrder),e?.filters?.licenseTypeId&&t.append("filter.license_category.license_type_id",e.filters.licenseTypeId),e?.filters?.licenseCategoryId&&t.append("filter.license_category_id",e.filters.licenseCategoryId),e?.filters?.status&&t.append("filter.status",e.filters.status);let a=await s.uE.get(`/applications?${t.toString()}`);return(0,r.zp)(a)},async getApplicationsByLicenseType(e,t){let a=new URLSearchParams;t?.page&&a.append("page",t.page.toString()),t?.limit&&a.append("limit",t.limit.toString()),t?.search&&a.append("search",t.search),t?.status&&a.append("filter.status",t.status),a.append("filter.license_category.license_type_id",e);let i=await s.uE.get(`/applications?${a.toString()}`);return(0,r.zp)(i)},async getApplication(e){let t=await s.uE.get(`/applications/${e}`);return(0,r.zp)(t)},async getApplicationsByApplicant(e){let t=await s.uE.get(`/applications/by-applicant/${e}`);return(0,r.zp)(t)},async getApplicationsByStatus(e){let t=await s.uE.get(`/applications/by-status/${e}`);return(0,r.zp)(t)},async updateApplicationStatus(e,t){let a=await s.uE.put(`/applications/${e}/status?status=${t}`);return(0,r.zp)(a)},async updateApplicationProgress(e,t,a){let i=await s.uE.put(`/applications/${e}/progress?currentStep=${t}&progressPercentage=${a}`);return(0,r.zp)(i)},async getApplicationStats(){let e=await s.uE.get("/applications/stats");return(0,r.zp)(e)},async createApplication(e){try{let t=await s.uE.post("/applications",e);return(0,r.zp)(t)}catch(e){throw e}},async updateApplication(e,t){try{let a=await s.uE.put(`/applications/${e}`,t,{timeout:3e4});return(0,r.zp)(a)}catch(e){if("ECONNABORTED"===e.code)throw Error("Request timeout - please try again");if(e.response?.status===400){let t=e.response?.data?.message||"Invalid application data";throw Error(`Bad Request: ${t}`)}if(e.response?.status===429)throw Error("Too many requests - please wait a moment and try again");throw e}},async deleteApplication(e){let t=await s.uE.delete(`/applications/${e}`);return(0,r.zp)(t)},async createApplicationWithApplicant(e){try{let t=new Date,a=t.toISOString().slice(0,10).replace(/-/g,""),r=t.toTimeString().slice(0,8).replace(/:/g,""),s=Math.random().toString(36).substr(2,3).toUpperCase(),i=`APP-${a}-${r}-${s}`;if(!/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e.user_id))throw Error(`Invalid user_id format: ${e.user_id}. Expected UUID format.`);return await this.createApplication({application_number:i,applicant_id:e.user_id,license_category_id:e.license_category_id,current_step:1,progress_percentage:0})}catch(e){throw e}},async saveApplicationSection(e,t,a){try{let a=1,r=["applicantInfo","companyProfile","businessInfo","serviceScope","businessPlan","legalHistory","reviewSubmit"].indexOf(t);a=r>=0?r+1:1;let s=Math.min(Math.round(a/6*100),100);await this.updateApplication(e,{progress_percentage:s,current_step:a})}catch(e){throw e}},async getApplicationSection(e,t){try{let a=await s.uE.get(`/applications/${e}/sections/${t}`);return(0,r.zp)(a)}catch(e){throw e}},async submitApplication(e){try{let t=await s.uE.put(`/applications/${e}`,{status:"submitted",submitted_at:new Date().toISOString(),progress_percentage:100,current_step:7});return(0,r.zp)(t)}catch(e){throw e}},async getUserApplications(){try{let e=await s.uE.get("/applications/user-applications"),t=(0,r.zp)(e),a=[];return t?.data?a=Array.isArray(t.data)?t.data:[]:Array.isArray(t)?a=t:t&&(a=[t]),a}catch(e){throw e}},async saveAsDraft(e,t){try{let a=await s.uE.put(`/applications/${e}`,{form_data:t,status:"draft"});return(0,r.zp)(a)}catch(e){throw e}},async validateApplication(e){try{let e={},t=[];for(let a of["applicantInfo","companyProfile","businessInfo","legalHistory"])e[a]&&0!==Object.keys(e[a]).length||t.push(`${a} section is incomplete`);return{isValid:0===t.length,errors:t}}catch(e){throw e}}}},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[4447,7498,1658,7563],()=>a(69437));module.exports=r})();