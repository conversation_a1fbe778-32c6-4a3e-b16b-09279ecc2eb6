(()=>{var e={};e.id=5668,e.ids=[5668],e.modules={821:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>d,pages:()=>c,routeModule:()=>u,tree:()=>p});var a=r(65239),s=r(48088),i=r(88170),n=r.n(i),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let p={children:["",{children:["customer",{children:["applications",{children:["apply",{children:["review-submit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,85930)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\review-submit\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,68736)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\review-submit\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/customer/applications/apply/review-submit/page",pathname:"/customer/applications/apply/review-submit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70114:(e,t,r)=>{Promise.resolve().then(r.bind(r,85930))},74075:e=>{"use strict";e.exports=require("zlib")},78637:(e,t,r)=>{"use strict";r.d(t,{k:()=>i});var a=r(51278),s=r(12234);let i={async getApplications(e){let t=new URLSearchParams;e?.page&&t.append("page",e.page.toString()),e?.limit&&t.append("limit",e.limit.toString()),e?.search&&t.append("search",e.search),e?.sortBy&&t.append("sortBy",e.sortBy),e?.sortOrder&&t.append("sortOrder",e.sortOrder),e?.filters?.licenseTypeId&&t.append("filter.license_category.license_type_id",e.filters.licenseTypeId),e?.filters?.licenseCategoryId&&t.append("filter.license_category_id",e.filters.licenseCategoryId),e?.filters?.status&&t.append("filter.status",e.filters.status);let r=await s.uE.get(`/applications?${t.toString()}`);return(0,a.zp)(r)},async getApplicationsByLicenseType(e,t){let r=new URLSearchParams;t?.page&&r.append("page",t.page.toString()),t?.limit&&r.append("limit",t.limit.toString()),t?.search&&r.append("search",t.search),t?.status&&r.append("filter.status",t.status),r.append("filter.license_category.license_type_id",e);let i=await s.uE.get(`/applications?${r.toString()}`);return(0,a.zp)(i)},async getApplication(e){let t=await s.uE.get(`/applications/${e}`);return(0,a.zp)(t)},async getApplicationsByApplicant(e){let t=await s.uE.get(`/applications/by-applicant/${e}`);return(0,a.zp)(t)},async getApplicationsByStatus(e){let t=await s.uE.get(`/applications/by-status/${e}`);return(0,a.zp)(t)},async updateApplicationStatus(e,t){let r=await s.uE.put(`/applications/${e}/status?status=${t}`);return(0,a.zp)(r)},async updateApplicationProgress(e,t,r){let i=await s.uE.put(`/applications/${e}/progress?currentStep=${t}&progressPercentage=${r}`);return(0,a.zp)(i)},async getApplicationStats(){let e=await s.uE.get("/applications/stats");return(0,a.zp)(e)},async createApplication(e){try{let t=await s.uE.post("/applications",e);return(0,a.zp)(t)}catch(e){throw e}},async updateApplication(e,t){try{let r=await s.uE.put(`/applications/${e}`,t,{timeout:3e4});return(0,a.zp)(r)}catch(e){if("ECONNABORTED"===e.code)throw Error("Request timeout - please try again");if(e.response?.status===400){let t=e.response?.data?.message||"Invalid application data";throw Error(`Bad Request: ${t}`)}if(e.response?.status===429)throw Error("Too many requests - please wait a moment and try again");throw e}},async deleteApplication(e){let t=await s.uE.delete(`/applications/${e}`);return(0,a.zp)(t)},async createApplicationWithApplicant(e){try{let t=new Date,r=t.toISOString().slice(0,10).replace(/-/g,""),a=t.toTimeString().slice(0,8).replace(/:/g,""),s=Math.random().toString(36).substr(2,3).toUpperCase(),i=`APP-${r}-${a}-${s}`;if(!/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e.user_id))throw Error(`Invalid user_id format: ${e.user_id}. Expected UUID format.`);return await this.createApplication({application_number:i,applicant_id:e.user_id,license_category_id:e.license_category_id,current_step:1,progress_percentage:0})}catch(e){throw e}},async saveApplicationSection(e,t,r){try{let r=1,a=["applicantInfo","companyProfile","businessInfo","serviceScope","businessPlan","legalHistory","reviewSubmit"].indexOf(t);r=a>=0?a+1:1;let s=Math.min(Math.round(r/6*100),100);await this.updateApplication(e,{progress_percentage:s,current_step:r})}catch(e){throw e}},async getApplicationSection(e,t){try{let r=await s.uE.get(`/applications/${e}/sections/${t}`);return(0,a.zp)(r)}catch(e){throw e}},async submitApplication(e){try{let t=await s.uE.put(`/applications/${e}`,{status:"submitted",submitted_at:new Date().toISOString(),progress_percentage:100,current_step:7});return(0,a.zp)(t)}catch(e){throw e}},async getUserApplications(){try{let e=await s.uE.get("/applications/user-applications"),t=(0,a.zp)(e),r=[];return t?.data?r=Array.isArray(t.data)?t.data:[]:Array.isArray(t)?r=t:t&&(r=[t]),r}catch(e){throw e}},async saveAsDraft(e,t){try{let r=await s.uE.put(`/applications/${e}`,{form_data:t,status:"draft"});return(0,a.zp)(r)}catch(e){throw e}},async validateApplication(e){try{let e={},t=[];for(let r of["applicantInfo","companyProfile","businessInfo","legalHistory"])e[r]&&0!==Object.keys(e[r]).length||t.push(`${r} section is incomplete`);return{isValid:0===t.length,errors:t}}catch(e){throw e}}}},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85930:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\applications\\\\apply\\\\review-submit\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\review-submit\\page.tsx","default")},88266:(e,t,r)=>{Promise.resolve().then(r.bind(r,90570))},90570:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>y});var a=r(60687),s=r(43210),i=r(16189),n=r(94391),l=r(63213),o=r(98374),p=r(78637),c=r(25011);class d{async initializeProgress(e,t){let r=(0,c.QE)(t);if(!r)throw Error(`Invalid license type: ${t}`);let a=r.steps.map(e=>({stepId:e.id,stepName:e.name,completed:!1})),s={applicationId:e,licenseTypeId:t,totalSteps:a.length,completedSteps:0,progressPercentage:0,steps:a,lastUpdated:new Date};return this.progressCache.set(e,s),await this.saveProgressToStorage(s),s}async markStepCompleted(e,t,r){let a=await this.getProgress(e);if(!a)throw Error(`No progress found for application: ${e}`);let s=a.steps.findIndex(e=>e.stepId===t);if(-1===s)throw Error(`Step not found: ${t}`);return a.steps[s].completed||(a.steps[s].completed=!0,a.steps[s].completedAt=new Date,a.steps[s].data=r,a.completedSteps=a.steps.filter(e=>e.completed).length,a.progressPercentage=Math.round(a.completedSteps/a.totalSteps*100),a.lastUpdated=new Date,this.progressCache.set(e,a),await this.saveProgressToStorage(a)),a}async markStepIncomplete(e,t){let r=await this.getProgress(e);if(!r)throw Error(`No progress found for application: ${e}`);let a=r.steps.findIndex(e=>e.stepId===t);if(-1===a)throw Error(`Step not found: ${t}`);return r.steps[a].completed&&(r.steps[a].completed=!1,r.steps[a].completedAt=void 0,r.steps[a].data=void 0,r.completedSteps=r.steps.filter(e=>e.completed).length,r.progressPercentage=Math.round(r.completedSteps/r.totalSteps*100),r.lastUpdated=new Date,this.progressCache.set(e,r),await this.saveProgressToStorage(r)),r}async getProgress(e){if(this.progressCache.has(e))return this.progressCache.get(e);let t=await this.loadProgressFromStorage(e);return t&&this.progressCache.set(e,t),t}async getCompletedStepIds(e){let t=await this.getProgress(e);return t?t.steps.filter(e=>e.completed).map(e=>e.stepId):[]}async isStepCompleted(e,t){let r=await this.getProgress(e);if(!r)return!1;let a=r.steps.find(e=>e.stepId===t);return a?.completed||!1}async getNextIncompleteStep(e){let t=await this.getProgress(e);return t&&t.steps.find(e=>!e.completed)||null}async getApplicationStatus(e){let t=await this.getProgress(e);return t&&0!==t.completedSteps?t.completedSteps===t.totalSteps?"completed":"in_progress":"not_started"}async saveProgressToStorage(e){try{let t=`application_progress_${e.applicationId}`;localStorage.setItem(t,JSON.stringify({...e,lastUpdated:e.lastUpdated.toISOString()}))}catch(e){}}async loadProgressFromStorage(e){try{let t=`application_progress_${e}`,r=localStorage.getItem(t);if(!r)return null;let a=JSON.parse(r);return{...a,lastUpdated:new Date(a.lastUpdated),steps:a.steps.map(e=>({...e,completedAt:e.completedAt?new Date(e.completedAt):void 0}))}}catch(e){return null}}clearCache(){this.progressCache.clear()}async deleteProgress(e){this.progressCache.delete(e);try{let t=`application_progress_${e}`;localStorage.removeItem(t)}catch(e){}}constructor(){this.progressCache=new Map}}let u=new d;class m{async validateStepNavigation(e,t,r){let a=(0,c.QE)(t);if(!a)return{canNavigateToStep:!1,reason:"Invalid license type",requiredSteps:[]};let s=(0,c.B5)(t,r);if(-1===s)return{canNavigateToStep:!1,reason:"Invalid step",requiredSteps:[]};let i=await u.getCompletedStepIds(e),n=a.steps.slice(0,s).filter(e=>e.required).map(e=>e.id).filter(e=>!i.includes(e));return n.length>0?{canNavigateToStep:!1,reason:"Required previous steps must be completed first",requiredSteps:n}:{canNavigateToStep:!0,requiredSteps:[]}}async validateNextStepNavigation(e,t,r){let a=(0,c.QE)(t);if(!a)return{canNavigateToStep:!1,reason:"Invalid license type",requiredSteps:[]};let s=(0,c.B5)(t,r);return -1===s?{canNavigateToStep:!1,reason:"Invalid current step",requiredSteps:[]}:a.steps[s].required&&!await u.isStepCompleted(e,r)?{canNavigateToStep:!1,reason:"Current step must be completed before proceeding",requiredSteps:[r]}:s>=a.steps.length-1?{canNavigateToStep:!1,reason:"Already at the last step",requiredSteps:[]}:{canNavigateToStep:!0,requiredSteps:[]}}async validatePreviousStepNavigation(e,t,r){return 0>=(0,c.B5)(t,r)?{canNavigateToStep:!1,reason:"Already at the first step",requiredSteps:[]}:{canNavigateToStep:!0,requiredSteps:[]}}async getNextAvailableStep(e,t){let r=(0,c.QE)(t);if(!r)return null;let a=await u.getCompletedStepIds(e);for(let e of r.steps)if(e.required&&!a.includes(e.id))return e.id;for(let e of r.steps)if(!e.required&&!a.includes(e.id))return e.id;return null}async validateApplicationCompletion(e,t){let r=(0,c.QE)(t);if(!r)return{isValid:!1,errors:["Invalid license type"],warnings:[]};let a=await u.getCompletedStepIds(e),s=r.steps.filter(e=>e.required).filter(e=>!a.includes(e.id)),i=[],n=[];s.length>0&&i.push(`Missing required steps: ${s.map(e=>e.name).join(", ")}`);let l=r.steps.filter(e=>!e.required).filter(e=>!a.includes(e.id));return l.length>0&&n.push(`Optional steps not completed: ${l.map(e=>e.name).join(", ")}`),{isValid:0===i.length,errors:i,warnings:n}}async getStepRequirements(e,t){let r=(0,c.QE)(e);if(!r)return null;let a=r.steps.find(e=>e.id===t);if(!a)return null;let s=(0,c.B5)(e,t),i=r.steps.slice(0,s).filter(e=>e.required).map(e=>e.id);return{isRequired:a.required,dependencies:i,description:a.description,estimatedTime:a.estimatedTime}}async isReadyForSubmission(e,t){return(await this.validateApplicationCompletion(e,t)).isValid}}let g=new m,h=({onSubmit:e,isSubmitting:t})=>(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Review & Submit Application"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"Please review your application and submit when ready."}),(0,a.jsx)("button",{onClick:e,disabled:t,className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50",children:t?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Submitting..."]}):"Submit Application"})]})}),y=()=>{let e=(0,i.useRouter)(),t=(0,i.useParams)(),r=(0,i.useSearchParams)(),{isAuthenticated:d,loading:m}=(0,l.A)(),{loading:y,categories:f}=(0,o.r2)(),x=t.categoryId,b="review-submit",w=r.get("app"),[v,S]=(0,s.useState)(!0),[N,j]=(0,s.useState)(null),[P,k]=(0,s.useState)([]),[_,A]=(0,s.useState)(0),[$,E]=(0,s.useState)(!1),[q,C]=(0,s.useState)(!1),[I,T]=(0,s.useState)(null),[M,z]=(0,s.useState)(""),[D,R]=(0,s.useState)({}),[F,B]=(0,s.useState)({}),[O,U]=(0,s.useState)({}),[L,G]=(0,s.useState)(!1),[Q,V]=(0,s.useState)(null),[W,H]=(0,s.useState)(null),[J,K]=(0,s.useState)(0),[X,Y]=(0,s.useState)(0),[Z,ee]=(0,s.useState)(null),[et,er]=(0,s.useState)(null);(0,s.useEffect)(()=>{if(!m&&!d)return void e.push("/customer/auth/login")},[d,m,e]),(0,s.useEffect)(()=>{let e=async()=>{try{let e=f.find(e=>e.id===x);if(e&&e.license_type_id)z(e.license_type_id);else{let e=await fetch(`/api/license-categories/${x}`);if(!e.ok)throw Error("Failed to fetch license category");let t=await e.json();if(t.license_type_id)z(t.license_type_id);else throw Error("License category does not have a license type ID")}}catch(e){j("Failed to load license category information")}};x&&!y&&e()},[x,y,f]),(0,s.useEffect)(()=>{M&&(V((0,c.QE)(M)),H((0,c.lW)(M,b)),K((0,c.B5)(M,b)),Y((0,c.Yk)(M)),ee((0,c.kR)(M,b)),er((0,c.WC)(M,b)))},[M,b]),(0,s.useEffect)(()=>{(async()=>{try{if(y||!M)return;if(!Q){j(`Invalid license type: ${M}. Please check the license type configuration.`),S(!1);return}if(!W){j(`Invalid step: ${b} for license type ${M}`),S(!1);return}if(!w){e.replace(`/customer/applications/apply/${x}/applicant-info`),S(!1);return}try{let t=await u.getProgress(w);if(t||(t=await u.initializeProgress(w,M)),t){let r=await u.getCompletedStepIds(w);k(r),A(t.progressPercentage);let a=await g.validateNextStepNavigation(w,M,W.id);E(a.canNavigateToStep);let s=await g.validatePreviousStepNavigation(w,M,W.id);C(s.canNavigateToStep);let i=await g.validateStepNavigation(w,M,W.id);if(!i.canNavigateToStep){T(i.reason||"Cannot access this step");let t=await g.getNextAvailableStep(w,M);if(t){e.replace(`/customer/applications/apply/${x}/${t}?app=${w}`),S(!1);return}}}}catch(e){A(0),k([]),E(!1)}await ea()}catch(e){j("Failed to load review page. Please try again.")}finally{S(!1)}})()},[Q,W,b,M,x,w,e,y]);let ea=async()=>{if(w)try{let e=await p.k.getApplication(w);B(e),R({})}catch(e){B({}),R({})}else B({}),R({})},es=async()=>{try{G(!0),w&&(await p.k.updateApplication(w,{status:"submitted"}),await ei(W?.id||b,{submitted:!0}),e.push("/customer/my-licenses?submitted=true"))}catch(e){j("Failed to submit application. Please try again.")}finally{G(!1)}},ei=async(e,t)=>{try{if(w){let r=await u.markStepCompleted(w,e,t);if(k(r.steps.filter(e=>e.completed).map(e=>e.stepId)),A(r.progressPercentage),W){let e=await g.validateNextStepNavigation(w,M,W.id);E(e.canNavigateToStep)}}}catch(e){}},en=t=>{if("next"===t&&Z&&$){let t="new"!==w?`?app=${w}`:"";e.push(`/customer/applications/apply/${x}/${Z.route}${t}`)}else if("previous"===t&&et&&q){let t="new"!==w?`?app=${w}`:"";e.push(`/customer/applications/apply/${x}/${et.route}${t}`)}};return m||v||!M?(0,a.jsx)(n.A,{children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading application step..."})]})})}):N?(0,a.jsx)(n.A,{children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto p-6",children:(0,a.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"ri-error-warning-line text-red-600 dark:text-red-400 text-2xl mr-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-red-800 dark:text-red-200",children:"Error Loading Step"}),(0,a.jsx)("p",{className:"text-red-700 dark:text-red-300 mt-1",children:N}),(0,a.jsxs)("button",{onClick:()=>e.back(),className:"mt-4 inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40",children:[(0,a.jsx)("i",{className:"ri-arrow-left-line mr-2"}),"Go Back"]})]})]})})})}):(0,a.jsx)(n.A,{children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto p-6",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2",children:[Q?.name," License Application"]}),(0,a.jsxs)("p",{className:"text-gray-600 dark:text-gray-400",children:["Step ",J+1," of ",X,": ",W?.name]}),(0,a.jsx)("div",{className:"mt-2 p-2 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:[(0,a.jsx)("i",{className:"ri-edit-line mr-1"}),"Editing application: ",w]})})]}),(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:["Step ",J+1," of ",X]}),(0,a.jsxs)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:[_,"% Complete"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-primary h-2 rounded-full transition-all duration-300",style:{width:`${_}%`}})})]}),(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:Q?.steps.map((e,t)=>{let r=P.includes(e.id),s=t===J;return(0,a.jsxs)("div",{className:`px-3 py-1 rounded-full text-xs font-medium ${s?"bg-primary text-white":r?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300":"bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400"}`,children:[r&&!s&&(0,a.jsx)("i",{className:"ri-check-line mr-1"}),t+1,". ",e.name]},e.id)})})}),I&&(0,a.jsx)("div",{className:"mb-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"ri-warning-line text-yellow-600 dark:text-yellow-400 text-lg mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-yellow-800 dark:text-yellow-200",children:"Navigation Restriction"}),(0,a.jsx)("p",{className:"text-yellow-700 dark:text-yellow-300 text-sm mt-1",children:I})]})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6",children:(0,a.jsx)(h,{formData:{},allFormData:F,onChange:()=>{},onSave:async()=>w||"",onSubmit:es,errors:{},applicationId:w||"",isLoading:v,isSubmitting:L})}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("button",{onClick:()=>en("previous"),disabled:!q||!et,className:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,a.jsx)("i",{className:"ri-arrow-left-line mr-2"}),"Previous"]}),(0,a.jsxs)("button",{onClick:()=>e.push("/customer/my-licenses"),className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:["View My Applications",(0,a.jsx)("i",{className:"ri-arrow-right-line ml-2"})]})]})]})})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,7498,1658,5814,7563,6893,8374],()=>r(821));module.exports=a})();