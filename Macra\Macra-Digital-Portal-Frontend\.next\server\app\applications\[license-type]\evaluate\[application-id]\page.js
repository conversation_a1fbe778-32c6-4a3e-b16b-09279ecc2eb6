(()=>{var e={};e.id=3988,e.ids=[3988],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7538:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i,metadata:()=>s});var r=a(37413);let s={title:"License Applications - MACRA Digital Portal",description:"Manage license applications for various service types"};function i({children:e}){return(0,r.jsx)(r.Fragment,{children:e})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19961:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>c});var r=a(60687),s=a(43210),i=a(16189),n=a(63213),o=a(21891),l=a(60417);function c({children:e}){let{isAuthenticated:t,loading:a}=(0,n.A)();(0,i.useRouter)();let[c,d]=(0,s.useState)("overview"),[p,u]=(0,s.useState)(!1);return a?(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"})}):t?(0,r.jsxs)("div",{className:"flex h-screen overflow-hidden",children:[(0,r.jsx)("div",{id:"mobileSidebarOverlay",className:`mobile-sidebar-overlay ${p?"show":""}`,onClick:()=>u(!1)}),(0,r.jsx)(l.default,{}),(0,r.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,r.jsx)(o.default,{activeTab:c,onTabChange:d,onMobileMenuToggle:()=>{u(!p)}}),e]})]}):null}},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},41596:(e,t,a)=>{Promise.resolve().then(a.bind(a,80886))},51324:(e,t,a)=>{Promise.resolve().then(a.bind(a,94821))},51366:(e,t,a)=>{Promise.resolve().then(a.bind(a,19961))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59614:(e,t,a)=>{Promise.resolve().then(a.bind(a,68031))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64826:(e,t,a)=>{"use strict";a.d(t,{D:()=>n});var r=a(51278),s=a(12234);let i=new Map,n={async getDocuments(e){try{let t=new URLSearchParams;e?.page&&t.append("page",e.page.toString()),e?.limit&&t.append("limit",e.limit.toString()),e?.search&&t.append("search",e.search),e?.sortBy&&t.append("sortBy",e.sortBy);let a=`/documents?${t.toString()}`;if(i.has(a))return await i.get(a);let n=s.uE.get(a).then(e=>(i.delete(a),(0,r.zp)(e))).catch(e=>{throw i.delete(a),e});return i.set(a,n),await n}catch(e){throw e}},async getDocumentsByEntity(e,t){try{let a=await s.uE.get(`/documents/entity/${e}/${t}`);return(0,r.zp)(a)}catch(e){throw e}},async getDocumentsByApplication(e){try{let t=await s.uE.get(`/documents/by-application/${e}`);return(0,r.zp)(t)}catch(e){throw e}},async getRequiredDocumentsForLicenseCategory(e){try{let t=await s.uE.get(`/license-category-documents/category/${e}`);return(0,r.zp)(t)}catch(e){throw e}},async uploadDocument(e,t){try{let a=new FormData;a.append("file",e),a.append("document_type",t.document_type),a.append("entity_type",t.entity_type),a.append("entity_id",t.entity_id),a.append("is_required",(t.is_required||!1).toString()),a.append("file_name",e.name);let i=await s.uE.post("/documents/upload",a,{headers:{"Content-Type":"multipart/form-data"}}),n=(0,r.zp)(i);return{document:n.data,message:n.message||"Document uploaded successfully"}}catch(e){throw e}},async createDocument(e){try{let t=await s.uE.post("/documents",e);return(0,r.zp)(t)}catch(e){throw e}},async updateDocument(e,t){try{let a=await s.uE.put(`/documents/${e}`,t);return(0,r.zp)(a)}catch(e){throw e}},async deleteDocument(e){try{await s.uE.delete(`/documents/${e}`)}catch(e){throw e}},async getDocument(e){try{let t=await s.uE.get(`/documents/${e}`);return(0,r.zp)(t)}catch(e){throw e}},async downloadDocument(e){try{return(await s.uE.get(`/documents/${e}/download`,{responseType:"blob"})).data}catch(e){throw e}},async previewDocument(e){try{return(await s.uE.get(`/documents/${e}/preview`,{responseType:"blob"})).data}catch(e){throw e}},isPreviewable:(e="")=>!!e&&["application/pdf","image/jpeg","image/jpg","image/png","image/gif","image/webp","text/plain","text/html","text/css","text/javascript","application/json"].includes(e.toLowerCase()),async checkRequiredDocuments(e,t){try{let a=await this.getRequiredDocumentsForLicenseCategory(t),r=(await this.getDocumentsByApplication(e)).data,s=r.map(e=>e.document_type),i=a.filter(e=>e.is_required&&!s.includes(e.name.toLowerCase().replace(/\s+/g,"_")));return{allUploaded:0===i.length,missing:i,uploaded:r}}catch(e){throw e}},getDocumentTypes:()=>["certificate_incorporation","memorandum_association","shareholding_structure","business_plan","financial_statements","technical_proposal","coverage_plan","network_diagram","equipment_specifications","insurance_certificate","tax_clearance","audited_accounts","bank_statement","cv_document","other"],formatDocumentType:e=>e.split("_").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "),mapDocumentNameToType(e){let t={"Certificate of Incorporation":"certificate_incorporation","Memorandum of Association":"memorandum_association","Shareholding Structure":"shareholding_structure","Business Plan":"business_plan","Financial Statements":"financial_statements","Technical Proposal":"technical_proposal","Coverage Plan":"coverage_plan","Network Diagram":"network_diagram","Equipment Specifications":"equipment_specifications","Insurance Certificate":"insurance_certificate","Tax Clearance Certificate":"tax_clearance","Tax Clearance":"tax_clearance","Audited Accounts":"audited_accounts","Bank Statement":"bank_statement","CV Document":"cv_document",Other:"other"};if(t[e])return t[e];let a=e.toLowerCase();for(let[e,r]of Object.entries(t))if(e.toLowerCase()===a)return r;return e.toLowerCase().replace(/\s+/g,"_")},validateFile:(e,t=10,a=[])=>e.size>1024*t*1024?{isValid:!1,error:`File size must be less than ${t}MB`}:a.length>0&&!a.includes(e.type)?{isValid:!1,error:`File type not allowed. Allowed types: ${a.join(", ")}`}:{isValid:!0}}},68031:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\applications\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\applications\\layout.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},78637:(e,t,a)=>{"use strict";a.d(t,{k:()=>i});var r=a(51278),s=a(12234);let i={async getApplications(e){let t=new URLSearchParams;e?.page&&t.append("page",e.page.toString()),e?.limit&&t.append("limit",e.limit.toString()),e?.search&&t.append("search",e.search),e?.sortBy&&t.append("sortBy",e.sortBy),e?.sortOrder&&t.append("sortOrder",e.sortOrder),e?.filters?.licenseTypeId&&t.append("filter.license_category.license_type_id",e.filters.licenseTypeId),e?.filters?.licenseCategoryId&&t.append("filter.license_category_id",e.filters.licenseCategoryId),e?.filters?.status&&t.append("filter.status",e.filters.status);let a=await s.uE.get(`/applications?${t.toString()}`);return(0,r.zp)(a)},async getApplicationsByLicenseType(e,t){let a=new URLSearchParams;t?.page&&a.append("page",t.page.toString()),t?.limit&&a.append("limit",t.limit.toString()),t?.search&&a.append("search",t.search),t?.status&&a.append("filter.status",t.status),a.append("filter.license_category.license_type_id",e);let i=await s.uE.get(`/applications?${a.toString()}`);return(0,r.zp)(i)},async getApplication(e){let t=await s.uE.get(`/applications/${e}`);return(0,r.zp)(t)},async getApplicationsByApplicant(e){let t=await s.uE.get(`/applications/by-applicant/${e}`);return(0,r.zp)(t)},async getApplicationsByStatus(e){let t=await s.uE.get(`/applications/by-status/${e}`);return(0,r.zp)(t)},async updateApplicationStatus(e,t){let a=await s.uE.put(`/applications/${e}/status?status=${t}`);return(0,r.zp)(a)},async updateApplicationProgress(e,t,a){let i=await s.uE.put(`/applications/${e}/progress?currentStep=${t}&progressPercentage=${a}`);return(0,r.zp)(i)},async getApplicationStats(){let e=await s.uE.get("/applications/stats");return(0,r.zp)(e)},async createApplication(e){try{let t=await s.uE.post("/applications",e);return(0,r.zp)(t)}catch(e){throw e}},async updateApplication(e,t){try{let a=await s.uE.put(`/applications/${e}`,t,{timeout:3e4});return(0,r.zp)(a)}catch(e){if("ECONNABORTED"===e.code)throw Error("Request timeout - please try again");if(e.response?.status===400){let t=e.response?.data?.message||"Invalid application data";throw Error(`Bad Request: ${t}`)}if(e.response?.status===429)throw Error("Too many requests - please wait a moment and try again");throw e}},async deleteApplication(e){let t=await s.uE.delete(`/applications/${e}`);return(0,r.zp)(t)},async createApplicationWithApplicant(e){try{let t=new Date,a=t.toISOString().slice(0,10).replace(/-/g,""),r=t.toTimeString().slice(0,8).replace(/:/g,""),s=Math.random().toString(36).substr(2,3).toUpperCase(),i=`APP-${a}-${r}-${s}`;if(!/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e.user_id))throw Error(`Invalid user_id format: ${e.user_id}. Expected UUID format.`);return await this.createApplication({application_number:i,applicant_id:e.user_id,license_category_id:e.license_category_id,current_step:1,progress_percentage:0})}catch(e){throw e}},async saveApplicationSection(e,t,a){try{let a=1,r=["applicantInfo","companyProfile","businessInfo","serviceScope","businessPlan","legalHistory","reviewSubmit"].indexOf(t);a=r>=0?r+1:1;let s=Math.min(Math.round(a/6*100),100);await this.updateApplication(e,{progress_percentage:s,current_step:a})}catch(e){throw e}},async getApplicationSection(e,t){try{let a=await s.uE.get(`/applications/${e}/sections/${t}`);return(0,r.zp)(a)}catch(e){throw e}},async submitApplication(e){try{let t=await s.uE.put(`/applications/${e}`,{status:"submitted",submitted_at:new Date().toISOString(),progress_percentage:100,current_step:7});return(0,r.zp)(t)}catch(e){throw e}},async getUserApplications(){try{let e=await s.uE.get("/applications/user-applications"),t=(0,r.zp)(e),a=[];return t?.data?a=Array.isArray(t.data)?t.data:[]:Array.isArray(t)?a=t:t&&(a=[t]),a}catch(e){throw e}},async saveAsDraft(e,t){try{let a=await s.uE.put(`/applications/${e}`,{form_data:t,status:"draft"});return(0,r.zp)(a)}catch(e){throw e}},async validateApplication(e){try{let e={},t=[];for(let a of["applicantInfo","companyProfile","businessInfo","legalHistory"])e[a]&&0!==Object.keys(e[a]).length||t.push(`${a} section is incomplete`);return{isValid:0===t.length,errors:t}}catch(e){throw e}}}},79551:e=>{"use strict";e.exports=require("url")},80886:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\applications\\\\[license-type]\\\\evaluate\\\\[application-id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\applications\\[license-type]\\evaluate\\[application-id]\\page.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91181:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>c});var r=a(65239),s=a(48088),i=a(88170),n=a.n(i),o=a(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);a.d(t,l);let c={children:["",{children:["applications",{children:["[license-type]",{children:["evaluate",{children:["[application-id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,80886)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\applications\\[license-type]\\evaluate\\[application-id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,7538)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\applications\\[license-type]\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,68031)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\applications\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\applications\\[license-type]\\evaluate\\[application-id]\\page.tsx"],p={require:a,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/applications/[license-type]/evaluate/[application-id]/page",pathname:"/applications/[license-type]/evaluate/[application-id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},94735:e=>{"use strict";e.exports=require("events")},94821:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>p});var r=a(60687),s=a(16189),i=a(43210),n=a(63213);a(78637);var o=a(12234),l=a(51278);let c={async getEvaluations(e){let t=new URLSearchParams;e?.page&&t.append("page",e.page.toString()),e?.limit&&t.append("limit",e.limit.toString()),e?.search&&t.append("search",e.search),e?.sortBy&&t.append("sortBy",e.sortBy),e?.sortOrder&&t.append("sortOrder",e.sortOrder);let a=await o.uE.get(`/evaluations?${t.toString()}`);return(0,l.zp)(a)},async getEvaluation(e){let t=await o.uE.get(`/evaluations/${e}`);return(0,l.zp)(t)},async getEvaluationByApplication(e){try{let t=await o.uE.get(`/evaluations/application/${e}`);return(0,l.zp)(t)}catch(e){if(e.response?.status===404)return null;throw e}},async getEvaluationCriteria(e){let t=await o.uE.get(`/evaluations/${e}/criteria`);return(0,l.zp)(t)},async createEvaluation(e){let t=await o.uE.post("/evaluations",e);return(0,l.zp)(t)},async updateEvaluation(e,t){let a=await o.uE.patch(`/evaluations/${e}`,t);return(0,l.zp)(a)},async deleteEvaluation(e){await o.uE.delete(`/evaluations/${e}`)},async getEvaluationStats(){let e=await o.uE.get("/evaluations/stats");return(0,l.zp)(e)},async submitEvaluation(e,t){return this.updateEvaluation(e,{...t,status:"completed"})},calculateTotalScore(e){if(!e||0===e.length)return 0;let t=e.reduce((e,t)=>e+t.score*t.weight,0),a=e.reduce((e,t)=>e+t.weight,0);return a>0?t/a:0},getEvaluationTemplate(e){let t={postal_service:[{category:"financial_capacity",subcategory:"financial_documents",score:0,weight:.15,max_marks:15},{category:"financial_capacity",subcategory:"capital_adequacy",score:0,weight:.1,max_marks:10},{category:"financial_capacity",subcategory:"financial_projections",score:0,weight:.1,max_marks:10},{category:"financial_capacity",subcategory:"credit_worthiness",score:0,weight:.05,max_marks:5},{category:"business_plan",subcategory:"market_analysis",score:0,weight:.1,max_marks:10},{category:"business_plan",subcategory:"business_model",score:0,weight:.1,max_marks:10},{category:"business_plan",subcategory:"revenue_projections",score:0,weight:.05,max_marks:5},{category:"business_plan",subcategory:"growth_strategy",score:0,weight:.05,max_marks:5},{category:"technical_expertise",subcategory:"technical_capacity",score:0,weight:.1,max_marks:10},{category:"technical_expertise",subcategory:"operational_plan",score:0,weight:.1,max_marks:10},{category:"technical_expertise",subcategory:"implementation_timeline",score:0,weight:.05,max_marks:5},{category:"organizational_structure",subcategory:"management_structure",score:0,weight:.05,max_marks:5}],telecommunications:[{category:"financial_capacity",subcategory:"financial_documents",score:0,weight:.2,max_marks:20},{category:"financial_capacity",subcategory:"capital_adequacy",score:0,weight:.15,max_marks:15},{category:"technical_expertise",subcategory:"network_design",score:0,weight:.2,max_marks:20},{category:"technical_expertise",subcategory:"technical_capacity",score:0,weight:.15,max_marks:15},{category:"business_plan",subcategory:"market_analysis",score:0,weight:.1,max_marks:10},{category:"business_plan",subcategory:"business_model",score:0,weight:.1,max_marks:10},{category:"organizational_structure",subcategory:"management_structure",score:0,weight:.05,max_marks:5},{category:"organizational_structure",subcategory:"compliance_framework",score:0,weight:.05,max_marks:5}]};return t[e]||t.postal_service}};function d({application:e,evaluation:t,documents:a,onSave:s,onSubmit:n,onBack:o,saving:l}){let[c,d]=(0,i.useState)("overview"),[p,u]=(0,i.useState)(t.criteria||[]),[m,x]=(0,i.useState)(t.evaluators_notes||""),[g,h]=(0,i.useState)(t.shareholding_compliance),[y,b]=(0,i.useState)(0),[f,v]=(0,i.useState)("approve"),w=(e,t,a)=>{let r=[...p];r[e]={...r[e],[t]:a},u(r)},_=e=>{if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]},j=e=>e.includes("pdf")?"ri-file-pdf-line":e.includes("word")?"ri-file-word-line":e.includes("excel")||e.includes("spreadsheet")?"ri-file-excel-line":e.includes("image")?"ri-image-line":"ri-file-line",N="completed"===t.status;return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("div",{className:"bg-white shadow",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between py-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("button",{type:"button",onClick:o,className:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:[(0,r.jsx)("i",{className:"ri-arrow-left-line mr-2"}),"Back"]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Evaluate Application"}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[e.application_number," • ",e.applicant?.name]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:`px-3 py-1 rounded-full text-xs font-medium ${N?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:N?"Completed":"Draft"}),(0,r.jsxs)("div",{className:`px-3 py-1 rounded-full text-xs font-medium ${"approve"===f?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:["Score: ",y.toFixed(1),"% • ",f.toUpperCase()]})]})]})})}),(0,r.jsx)("div",{className:"bg-white border-b border-gray-200",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsx)("nav",{className:"-mb-px flex space-x-8",children:[{id:"overview",label:"Application Overview",icon:"ri-file-list-line"},{id:"documents",label:"Documents",icon:"ri-folder-line"},{id:"evaluation",label:"Evaluation",icon:"ri-star-line"}].map(e=>(0,r.jsxs)("button",{type:"button",onClick:()=>d(e.id),className:`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${c===e.id?"border-primary text-primary":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[(0,r.jsx)("i",{className:e.icon}),(0,r.jsx)("span",{children:e.label})]},e.id))})})}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:["overview"===c&&(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Application Details"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Application Number"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:e.application_number})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Applicant Name"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:e.applicant?.name||"N/A"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"License Category"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:e.license_category?.name||"N/A"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Status"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:e.status})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Submitted Date"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:e.submitted_at?new Date(e.submitted_at).toLocaleDateString():"Not submitted"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Business Registration"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:e.applicant?.business_registration_number||"N/A"})]})]})]}),"documents"===c&&(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Application Documents"}),a.length>0?(0,r.jsx)("div",{className:"space-y-3",children:a.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("i",{className:`${j(e.mime_type)} text-2xl text-gray-400`}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.file_name}),(0,r.jsxs)("p",{className:"text-xs text-gray-500",children:[e.document_type," • ",_(e.file_size)," •",new Date(e.created_at).toLocaleDateString()]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[e.is_required&&(0,r.jsx)("span",{className:"px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded",children:"Required"}),(0,r.jsx)("button",{type:"button",className:"text-primary hover:text-primary-dark text-sm font-medium",onClick:()=>window.open(`/api/documents/${e.document_id}/download`,"_blank"),children:"View"})]})]},e.document_id))}):(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("i",{className:"ri-folder-open-line text-4xl text-gray-400 mb-4"}),(0,r.jsx)("p",{className:"text-gray-500",children:"No documents uploaded for this application."})]})]}),"evaluation"===c&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Evaluation Criteria"}),(0,r.jsx)("div",{className:"space-y-4",children:p.map((e,t)=>(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsxs)("h4",{className:"text-sm font-medium text-gray-900 capitalize",children:[e.category.replace(/_/g," ")," - ",e.subcategory.replace(/_/g," ")]}),(0,r.jsxs)("span",{className:"text-xs text-gray-500",children:["Weight: ",(100*e.weight).toFixed(0),"%"]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"Score (0-100)"}),(0,r.jsx)("input",{type:"number",min:"0",max:"100",value:e.score,onChange:e=>w(t,"score",parseFloat(e.target.value)||0),disabled:N,placeholder:"Enter score (0-100)",className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm disabled:bg-gray-100"})]}),e.max_marks&&(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:["Awarded Marks (Max: ",e.max_marks,")"]}),(0,r.jsx)("input",{type:"number",min:"0",max:e.max_marks,value:e.awarded_marks||0,onChange:e=>w(t,"awarded_marks",parseFloat(e.target.value)||0),disabled:N,placeholder:`Enter awarded marks (Max: ${e.max_marks})`,title:`Awarded Marks (Max: ${e.max_marks})`,className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm disabled:bg-gray-100"})]})]})]},t))})]}),(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Evaluation Summary"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[(0,r.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,r.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[y.toFixed(1),"%"]}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Total Score"})]}),(0,r.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"70%"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Pass Threshold"})]}),(0,r.jsxs)("div",{className:`text-center p-4 rounded-lg ${"approve"===f?"bg-green-50":"bg-red-50"}`,children:[(0,r.jsx)("p",{className:`text-2xl font-bold ${"approve"===f?"text-green-900":"text-red-900"}`,children:f.toUpperCase()}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Recommendation"})]})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Shareholding Compliance"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"radio",name:"shareholding",checked:!0===g,onChange:()=>h(!0),disabled:N,className:"focus:ring-primary h-4 w-4 text-primary border-gray-300"}),(0,r.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"Compliant"})]}),(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"radio",name:"shareholding",checked:!1===g,onChange:()=>h(!1),disabled:N,className:"focus:ring-primary h-4 w-4 text-primary border-gray-300"}),(0,r.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"Non-compliant"})]})]})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Evaluator Notes"}),(0,r.jsx)("textarea",{rows:4,value:m,onChange:e=>x(e.target.value),disabled:N,placeholder:"Enter your evaluation notes and comments...",className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm disabled:bg-gray-100"})]}),!N&&(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-3",children:[(0,r.jsx)("button",{type:"button",onClick:()=>{s({criteria:p,evaluators_notes:m,shareholding_compliance:g})},disabled:l,className:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50",children:l?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"}),"Saving..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"ri-save-line mr-2"}),"Save Draft"]})}),(0,r.jsx)("button",{type:"button",onClick:()=>{window.confirm("Are you sure you want to submit this evaluation? This action cannot be undone.")&&n({criteria:p,evaluators_notes:m,shareholding_compliance:g})},disabled:l,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50",children:l?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Submitting..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"ri-check-line mr-2"}),"Submit Evaluation"]})})]})]})]})]})]})}function p(){let e=(0,s.useParams)(),t=(0,s.useRouter)(),{user:a,isAuthenticated:o,loading:l}=(0,n.A)(),p=e["license-type"],u=e["application-id"],[m,x]=(0,i.useState)(null),[g,h]=(0,i.useState)(null),[y,b]=(0,i.useState)([]),[f,v]=(0,i.useState)(!0),[w,_]=(0,i.useState)(null),[j,N]=(0,i.useState)(!1);a?.roles?.some(e=>["admin","administrator","staff","moderator","manager"].includes(e.toLowerCase()));let k=async e=>{if(g&&a)try{let t;N(!0);let r=c.calculateTotalScore(e.criteria),s={total_score:r,recommendation:r>=70?"approve":"reject",evaluators_notes:e.evaluators_notes,shareholding_compliance:e.shareholding_compliance,criteria:e.criteria};t=g.evaluation_id?await c.updateEvaluation(g.evaluation_id,s):await c.createEvaluation({application_id:u,evaluator_id:a.user_id,evaluation_type:p,...s}),h(t),alert("Evaluation saved successfully!")}catch(e){alert("Failed to save evaluation: "+(e.message||"Unknown error"))}finally{N(!1)}},E=async e=>{if(g&&a)try{let r;N(!0);let s=c.calculateTotalScore(e.criteria),i=s>=70?"approve":"reject",n={total_score:s,recommendation:i,evaluators_notes:e.evaluators_notes,shareholding_compliance:e.shareholding_compliance,criteria:e.criteria};if(g.evaluation_id)r=await c.submitEvaluation(g.evaluation_id,n);else{let t=await c.createEvaluation({application_id:u,evaluator_id:a.user_id,evaluation_type:p,...n});r=await c.submitEvaluation(t.evaluation_id,{total_score:s,recommendation:i,evaluators_notes:e.evaluators_notes,criteria:e.criteria})}h(r),alert(`Evaluation submitted successfully! Recommendation: ${i.toUpperCase()}`),t.push(`/applications/${p}`)}catch(e){alert("Failed to submit evaluation: "+(e.message||"Unknown error"))}finally{N(!1)}},S=()=>{t.push(`/applications/${p}`)};return l||f?(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading evaluation..."})]})}):w?(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"mb-4",children:(0,r.jsx)("i",{className:"ri-error-warning-line text-4xl text-red-500"})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Error"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:w}),(0,r.jsxs)("button",{type:"button",onClick:S,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:[(0,r.jsx)("i",{className:"ri-arrow-left-line mr-2"}),"Back to Applications"]})]})}):m&&g?(0,r.jsx)(d,{application:m,evaluation:g,documents:y,onSave:k,onSubmit:E,onBack:S,saving:j,licenseType:p}):(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-gray-600",children:"Application or evaluation data not found."}),(0,r.jsxs)("button",{type:"button",onClick:S,className:"mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:[(0,r.jsx)("i",{className:"ri-arrow-left-line mr-2"}),"Back to Applications"]})]})})}a(64826)},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[4447,7498,1658,5814,2335,6606],()=>a(91181));module.exports=r})();