"use strict";exports.id=7033,exports.ids=[7033],exports.modules={22145:(e,r,t)=>{t.d(r,{A:()=>l});var a=t(60687);let s=(0,t(43210).forwardRef)(({label:e,error:r,helperText:t,variant:s="default",fullWidth:l=!0,className:d="",required:i,disabled:o,...c},n)=>{let x=`px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 ${l?"w-full":""} ${"small"===s?"py-1.5 text-sm":"py-2"}`,m=`${x} ${r?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"} ${o?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""} ${d}`,y=`block font-medium text-gray-700 dark:text-gray-300 mb-2 ${"small"===s?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"}`;return(0,a.jsxs)("div",{className:"w-full",children:[e&&(0,a.jsxs)("label",{className:y,children:[e,i&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,a.jsx)("input",{ref:n,className:m,disabled:o,required:i,...c}),r&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:r}),t&&!r&&(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:t})]})});s.displayName="TextInput";let l=s},24325:(e,r,t)=>{t.d(r,{A:()=>l});var a=t(60687),s=t(43210);let l=({id:e,label:r,accept:t=".pdf",maxSize:l=10,required:d=!1,value:i,onChange:o,description:c,className:n=""})=>{let x=(0,s.useRef)(null);return(0,a.jsxs)("div",{className:n,children:[(0,a.jsxs)("label",{htmlFor:e,className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[r," ",d&&(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsxs)("div",{onClick:()=>{x.current?.click()},className:"border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center cursor-pointer hover:border-primary hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors",children:[(0,a.jsx)("input",{ref:x,type:"file",accept:t,onChange:e=>{let r=e.target.files?.[0]||null;if(r){if(r.size>1024*l*1024)return void alert(`File size must be less than ${l}MB`);if(t&&!t.split(",").some(e=>r.name.toLowerCase().endsWith(e.trim().replace("*",""))))return void alert(`File type must be: ${t}`)}o(r)},className:"hidden",id:e,required:d}),i?(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"flex items-center justify-center",children:(0,a.jsx)("i",{className:"ri-file-text-line text-3xl text-green-500"})}),(0,a.jsx)("p",{className:"text-sm font-medium text-green-600 dark:text-green-400",children:i.name}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:[(i.size/1024/1024).toFixed(2)," MB"]}),(0,a.jsxs)("button",{type:"button",onClick:e=>{e.stopPropagation(),o(null),x.current&&(x.current.value="")},className:"inline-flex items-center px-3 py-1 bg-red-100 text-red-700 hover:bg-red-200 rounded-md text-xs font-medium transition-colors",children:[(0,a.jsx)("i",{className:"ri-delete-bin-line mr-1"}),"Remove"]})]}):(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"flex items-center justify-center",children:(0,a.jsx)("i",{className:"ri-upload-cloud-2-line text-3xl text-gray-400"})}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Click to upload ",r.toLowerCase()]}),c&&(0,a.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-500",children:c}),(0,a.jsxs)("div",{className:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors",children:[(0,a.jsx)("i",{className:"ri-folder-upload-line mr-2"}),"Choose File"]})]})]}),c&&!i&&(0,a.jsx)("p",{className:"mt-2 text-xs text-gray-500 dark:text-gray-400",children:c})]})}},55457:(e,r,t)=>{t.d(r,{W:()=>l});var a=t(51278),s=t(72901);let l={async createApplicant(e){try{let r=await s.uE.post("/applicants",e),t=(0,a.zp)(r);if(!t)throw Error("Invalid response format from applicant creation");return t}catch(e){throw e}},async getApplicant(e){try{let r=await s.uE.get(`/applicants/${e}`);return(0,a.zp)(r)}catch(e){throw e}},async updateApplicant(e,r){try{let t=await s.uE.put(`/applicants/${e}`,r);return(0,a.zp)(t)}catch(e){throw e}},async getApplicantsByUser(){try{let e=await s.uE.get("/applicants/by-user");return(0,a.zp)(e)}catch(e){throw e}},async deleteApplicant(e){try{let r=await s.uE.delete(`/applicants/${e}`);return(0,a.zp)(r)}catch(e){throw e}}}},62978:(e,r,t)=>{t.d(r,{A:()=>l});var a=t(60687);let s=(0,t(43210).forwardRef)(({label:e,error:r,helperText:t,variant:s="default",fullWidth:l=!0,className:d="",required:i,disabled:o,rows:c=3,...n},x)=>{let m=`px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 resize-y ${l?"w-full":""} ${"small"===s?"py-1.5 text-sm":"py-2"}`,y=`${m} ${r?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"} ${o?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""} ${d}`,g=`block font-medium text-gray-700 dark:text-gray-300 mb-2 ${"small"===s?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"}`;return(0,a.jsxs)("div",{className:"w-full",children:[e&&(0,a.jsxs)("label",{className:g,children:[e,i&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,a.jsx)("textarea",{ref:x,className:y,disabled:o,required:i,rows:c,...n}),r&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:r}),t&&!r&&(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:t})]})});s.displayName="TextArea";let l=s},76377:(e,r,t)=>{t.d(r,{l6:()=>l.A,fs:()=>s.A,ks:()=>a.A}),t(24325),t(60687),t(43210);var a=t(22145),s=t(62978),l=t(86732)},86732:(e,r,t)=>{t.d(r,{A:()=>l});var a=t(60687);let s=(0,t(43210).forwardRef)(({label:e,error:r,helperText:t,variant:s="default",fullWidth:l=!0,className:d="",required:i,disabled:o,options:c,children:n,...x},m)=>{let y=`px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors duration-200 ${l?"w-full":""} ${"small"===s?"py-1.5 text-sm":"py-2"}`,g=`${y} ${r?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"} ${o?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""} ${d}`,p=`block font-medium text-gray-700 dark:text-gray-300 mb-2 ${"small"===s?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"}`;return(0,a.jsxs)("div",{className:"w-full",children:[e&&(0,a.jsxs)("label",{className:p,children:[e,i&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,a.jsx)("select",{ref:m,className:g,disabled:o,required:i,...x,children:c?c.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value)):n}),r&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:r}),t&&!r&&(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:t})]})});s.displayName="Select";let l=s}};