import { useState, useEffect, useCallback, useMemo } from 'react';
import { applicationService } from '@/services/applicationService';
import { validateSection } from '@/utils/formValidation';
import { useAuth } from '@/contexts/AuthContext';

interface ApplicationFormState {
  applicationId: string | null;
  formData: Record<string, any>;
  validationErrors: Record<string, unknown>;
  isSaving: boolean;
  isLoading: boolean;
  lastSaved: string | null;
  hasUnsavedChanges: boolean;
}

interface UseApplicationFormOptions {
  licenseTypeId?: string;
  licenseCategoryId?: string;
  initialApplicationId?: string;
}

export const useApplicationForm = (options: UseApplicationFormOptions) => {
  const { licenseTypeId, licenseCategoryId, initialApplicationId } = options;
  const { user } = useAuth();

  const [state, setState] = useState<ApplicationFormState>({
    applicationId: initialApplicationId || null,
    formData: {},
    validationErrors: {},
    isSaving: false,
    isLoading: false,
    lastSaved: null,
    hasUnsavedChanges: false
  });

  // Load existing application data
  const loadApplication = useCallback(async (applicationId: string) => {
    setState(prev => ({ ...prev, isLoading: true }));

    try {
      const application = await applicationService.getApplication(applicationId);

      // Initialize empty form data since backend doesn't store form_data directly
      // Form data will be loaded separately when needed
      setState(prev => ({
        ...prev,
        applicationId: application.application_id,
        formData: {}, // Initialize empty - will be populated as sections are loaded
        isLoading: false
      }));

      console.log('Application loaded:', application);
    } catch (error) {
      console.error('Error loading application:', error);
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, []);

  // Create new application
  const createApplication = useCallback(async (applicantData: Record<string, unknown>) => {
    setState(prev => ({ ...prev, isSaving: true }));

    try {
      if (!user?.user_id) {
        throw new Error('User not authenticated');
      }

      const applicationData = {
        license_type_id: licenseTypeId,
        license_category_id: licenseCategoryId,
        user_id: user.user_id,
        ...applicantData
      };

      const application = await applicationService.createApplication(applicationData);

      setState(prev => ({
        ...prev,
        applicationId: application.application_id,
        formData: applicantData,
        isSaving: false,
        lastSaved: new Date().toISOString(),
        hasUnsavedChanges: false
      }));

      console.log('Application created:', application);
      return application;
    } catch (error) {
      console.error('Error creating application:', error);
      setState(prev => ({ ...prev, isSaving: false }));
      throw error;
    }
  }, [licenseTypeId, licenseCategoryId, user?.user_id]);

  // Update form data
  const updateFormData = useCallback((sectionData: Record<string, any>) => {
    setState(prev => ({
      ...prev,
      formData: { ...prev.formData, ...sectionData },
      hasUnsavedChanges: true
    }));
  }, []);

  // Save form data
  const saveFormData = useCallback(async () => {
    if (!state.applicationId) return;

    setState(prev => ({ ...prev, isSaving: true }));

    try {
      // Validate form data
      const errors = validateSection(state.formData);
      if (Object.keys(errors).length > 0) {
        setState(prev => ({
          ...prev,
          validationErrors: errors,
          isSaving: false
        }));
        return false;
      }

      // Save to backend
      await applicationService.updateApplication(state.applicationId, state.formData);

      setState(prev => ({
        ...prev,
        isSaving: false,
        lastSaved: new Date().toISOString(),
        hasUnsavedChanges: false,
        validationErrors: {}
      }));

      return true;
    } catch (error) {
      console.error('Error saving form data:', error);
      setState(prev => ({ ...prev, isSaving: false }));
      return false;
    }
  }, [state.applicationId, state.formData]);

  // Load application on mount if applicationId is provided
  useEffect(() => {
    if (initialApplicationId) {
      loadApplication(initialApplicationId);
    }
  }, [initialApplicationId, loadApplication]);

  return {
    applicationId: state.applicationId,
    formData: state.formData,
    validationErrors: state.validationErrors,
    isSaving: state.isSaving,
    isLoading: state.isLoading,
    lastSaved: state.lastSaved,
    hasUnsavedChanges: state.hasUnsavedChanges,
    loadApplication,
    createApplication,
    updateFormData,
    saveFormData
  };
};