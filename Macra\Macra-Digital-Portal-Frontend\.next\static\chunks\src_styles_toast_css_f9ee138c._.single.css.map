{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/toast.css"], "sourcesContent": ["/* Toast container and positioning styles */\r\n.toast-container {\r\n  position: fixed;\r\n  top: 1rem;\r\n  right: 1rem;\r\n  z-index: 9999;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.5rem;\r\n  pointer-events: none; /* Allow clicks to pass through container */\r\n}\r\n\r\n/* Make individual toasts clickable */\r\n.toast-wrapper {\r\n  pointer-events: auto;\r\n}\r\n\r\n/* Individual toast wrapper with dynamic positioning */\r\n.toast-wrapper {\r\n  position: relative;\r\n  transition: transform 0.3s ease-in-out;\r\n  pointer-events: auto;\r\n}\r\n\r\n/* Dynamic positioning classes for toast stacking */\r\n.toast-wrapper[data-index=\"0\"] {\r\n  transform: translateY(0px);\r\n}\r\n\r\n.toast-wrapper[data-index=\"1\"] {\r\n  transform: translateY(10px);\r\n}\r\n\r\n.toast-wrapper[data-index=\"2\"] {\r\n  transform: translateY(20px);\r\n}\r\n\r\n.toast-wrapper[data-index=\"3\"] {\r\n  transform: translateY(30px);\r\n}\r\n\r\n.toast-wrapper[data-index=\"4\"] {\r\n  transform: translateY(40px);\r\n}\r\n\r\n.toast-wrapper[data-index=\"5\"] {\r\n  transform: translateY(50px);\r\n}\r\n\r\n.toast-wrapper[data-index=\"6\"] {\r\n  transform: translateY(60px);\r\n}\r\n\r\n.toast-wrapper[data-index=\"7\"] {\r\n  transform: translateY(70px);\r\n}\r\n\r\n.toast-wrapper[data-index=\"8\"] {\r\n  transform: translateY(80px);\r\n}\r\n\r\n.toast-wrapper[data-index=\"9\"] {\r\n  transform: translateY(90px);\r\n}\r\n\r\n/* Additional indexes beyond 9 - extend as needed */\r\n.toast-wrapper[data-index=\"10\"] {\r\n  transform: translateY(100px);\r\n}\r\n\r\n.toast-wrapper[data-index=\"11\"] {\r\n  transform: translateY(110px);\r\n}\r\n\r\n.toast-wrapper[data-index=\"12\"] {\r\n  transform: translateY(120px);\r\n}\r\n\r\n.toast-wrapper[data-index=\"13\"] {\r\n  transform: translateY(130px);\r\n}\r\n\r\n.toast-wrapper[data-index=\"14\"] {\r\n  transform: translateY(140px);\r\n}\r\n\r\n.toast-wrapper[data-index=\"15\"] {\r\n  transform: translateY(150px);\r\n}\r\n\r\n.toast-wrapper[data-index=\"16\"] {\r\n  transform: translateY(160px);\r\n}\r\n\r\n.toast-wrapper[data-index=\"17\"] {\r\n  transform: translateY(170px);\r\n}\r\n\r\n.toast-wrapper[data-index=\"18\"] {\r\n  transform: translateY(180px);\r\n}\r\n\r\n.toast-wrapper[data-index=\"19\"] {\r\n  transform: translateY(190px);\r\n}\r\n\r\n/* For any toasts beyond 19, they'll stack naturally */\r\n\r\n/* Responsive positioning for mobile devices */\r\n@media (max-width: 640px) {\r\n  .toast-container {\r\n    top: 1rem;\r\n    left: 1rem;\r\n    right: 1rem;\r\n    width: auto;\r\n  }\r\n}\r\n\r\n/* Ensure toasts are visible above modals and other overlays */\r\n.toast-container {\r\n  z-index: 9999 !important;\r\n}\r\n\r\n/* Ensure toasts have proper backdrop and are always visible */\r\n.toast-wrapper > div {\r\n  -webkit-backdrop-filter: blur(8px);\r\n          backdrop-filter: blur(8px);\r\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);\r\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;AAYA;;;;;;;AAYA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAOA;EACE;;;;;;;;AASF;;;;AAKA"}}]}