{"/_not-found/page": "app/_not-found/page.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/auth/forgot-password/page": "app/auth/forgot-password/page.js", "/auth/login-landing/page": "app/auth/login-landing/page.js", "/auth/login/page": "app/auth/login/page.js", "/auth/test-login/page": "app/auth/test-login/page.js", "/auth/reset-password/page": "app/auth/reset-password/page.js", "/auth/signup/page": "app/auth/signup/page.js", "/auth/setup-2fa/page": "app/auth/setup-2fa/page.js", "/auth/verify-2fa/page": "app/auth/verify-2fa/page.js", "/help/page": "app/help/page.js", "/page": "app/page.js", "/test-api/page": "app/test-api/page.js", "/audit-trail/page": "app/audit-trail/page.js", "/consumer-affairs/page": "app/consumer-affairs/page.js", "/data-breach/page": "app/data-breach/page.js", "/dashboard/page": "app/dashboard/page.js", "/applications/page": "app/applications/page.js", "/postal/page": "app/postal/page.js", "/financial/page": "app/financial/page.js", "/procurement/page": "app/procurement/page.js", "/permissions/page": "app/permissions/page.js", "/roles/page": "app/roles/page.js", "/test/page": "app/test/page.js", "/settings/page": "app/settings/page.js", "/users/add/page": "app/users/add/page.js", "/profile/page": "app/profile/page.js", "/users/page": "app/users/page.js", "/users/edit/[id]/page": "app/users/edit/[id]/page.js", "/customer/applications/[licenseTypeId]/page": "app/customer/applications/[licenseTypeId]/page.js", "/customer/applications/apply/address-info/page": "app/customer/applications/apply/address-info/page.js", "/customer/applications/apply/applicant-info/page": "app/customer/applications/apply/applicant-info/page.js", "/customer/applications/apply/contact-info/page": "app/customer/applications/apply/contact-info/page.js", "/customer/applications/apply/documents/page": "app/customer/applications/apply/documents/page.js", "/customer/applications/apply/legal-history/page": "app/customer/applications/apply/legal-history/page.js", "/customer/applications/apply/management/page": "app/customer/applications/apply/management/page.js", "/customer/applications/apply/professional-services/page": "app/customer/applications/apply/professional-services/page.js", "/customer/applications/apply/review-submit/page": "app/customer/applications/apply/review-submit/page.js", "/customer/applications/apply/page": "app/customer/applications/apply/page.js", "/customer/applications/apply/submit/page": "app/customer/applications/apply/submit/page.js", "/customer/applications/apply/service-scope/page": "app/customer/applications/apply/service-scope/page.js", "/customer/auth/forgot-password/page": "app/customer/auth/forgot-password/page.js", "/customer/auth/reset-password/page": "app/customer/auth/reset-password/page.js", "/customer/applications/submitted/page": "app/customer/applications/submitted/page.js", "/customer/applications/page": "app/customer/applications/page.js", "/customer/auth/login-landing/page": "app/customer/auth/login-landing/page.js", "/customer/auth/setup-2fa/page": "app/customer/auth/setup-2fa/page.js", "/customer/auth/login/page": "app/customer/auth/login/page.js", "/customer/auth/signup/page": "app/customer/auth/signup/page.js", "/customer/documents/page": "app/customer/documents/page.js", "/customer/auth/test-login/page": "app/customer/auth/test-login/page.js", "/customer/consumer-affairs/page": "app/customer/consumer-affairs/page.js", "/customer/data-protection/page": "app/customer/data-protection/page.js", "/customer/licenses/page": "app/customer/licenses/page.js", "/customer/auth/verify-2fa/page": "app/customer/auth/verify-2fa/page.js", "/customer/help/page": "app/customer/help/page.js", "/customer/my-licenses/page": "app/customer/my-licenses/page.js", "/customer/profile/page": "app/customer/profile/page.js", "/customer/page": "app/customer/page.js", "/customer/resources/page": "app/customer/resources/page.js", "/customer/payments/page": "app/customer/payments/page.js", "/customer/procurement/page": "app/customer/procurement/page.js", "/applications/[license-type]/evaluate/[application-id]/page": "app/applications/[license-type]/evaluate/[application-id]/page.js", "/applications/[license-type]/view/[application-id]/page": "app/applications/[license-type]/view/[application-id]/page.js", "/applications/[license-type]/page": "app/applications/[license-type]/page.js"}