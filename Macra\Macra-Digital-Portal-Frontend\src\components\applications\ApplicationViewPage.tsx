'use client';

import { useState, useEffect } from 'react';
import { Application } from '../../types/license';
import { applicationService } from '../../services/applicationService';
import { applicationFormDataService } from '../../services/applicationFormDataService';

interface ApplicationViewPageProps {
  applicationId: string;
  departmentType: string;
  onBack: () => void;
}

interface ApplicationDetails extends Application {
  applicant_details?: any;
  company_profile?: any;
  business_info?: any;
  service_scope?: any;
  business_plan?: any;
  legal_history?: any;
  documents?: any[];
  form_data?: Record<string, any>;
}

export default function ApplicationViewPage({ 
  applicationId, 
  departmentType, 
  onBack 
}: ApplicationViewPageProps) {
  const [application, setApplication] = useState<ApplicationDetails | null>(null);
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('applicant');

  useEffect(() => {
    if (applicationId) {
      fetchApplicationDetails();
    }
  }, [applicationId]);

  const fetchApplicationDetails = async () => {
    setLoading(true);
    setError(null);

    try {
      // Fetch application details
      const response = await applicationService.getApplication(applicationId);
      setApplication(response);

      // Fetch form data from database
      const applicationFormData = await applicationFormDataService.getApplicationFormData(applicationId);
      setFormData(applicationFormData);

      console.log('Application data loaded:', response);
      console.log('Form data loaded:', applicationFormData);
    } catch (err: any) {
      console.error('Error fetching application details:', err);
      setError('Failed to load application details');
    } finally {
      setLoading(false);
    }
  };

  const getDepartmentName = (type: string) => {
    const names: Record<string, string> = {
      'postal': 'Postal Services',
      'telecommunications': 'Telecommunications',
      'standards': 'Standards Compliance',
      'clf': 'CLF (Converged Licensing Framework)'
    };
    return names[type] || type;
  };

  const getStatusBadge = (status: string) => {
    // Handle undefined or null status
    if (!status) {
      return (
        <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
          Unknown
        </span>
      );
    }

    const statusClasses: Record<string, string> = {
      'draft': 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
      'submitted': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
      'under_review': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
      'evaluation': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
      'approved': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      'rejected': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
      'withdrawn': 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
    };

    const displayText = status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());

    return (
      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClasses[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'}`}>
        {displayText}
      </span>
    );
  };

  const tabs = [
    { id: 'applicant', label: 'Applicant Details', icon: 'ri-user-line' },
    { id: 'company', label: 'Company Profile', icon: 'ri-building-line' },
    { id: 'management', label: 'Management', icon: 'ri-team-line' },
    { id: 'professional', label: 'Professional Services', icon: 'ri-service-line' },
    { id: 'business', label: 'Business Info', icon: 'ri-briefcase-line' },
    { id: 'service', label: 'Service Scope', icon: 'ri-global-line' },
    { id: 'plan', label: 'Business Plan', icon: 'ri-file-chart-line' },
    { id: 'legal', label: 'Legal History', icon: 'ri-scales-line' },
    { id: 'documents', label: 'Documents', icon: 'ri-folder-line' },
    { id: 'comments', label: 'Comments', icon: 'ri-chat-3-line' },
    { id: 'assignment', label: 'Assignment', icon: 'ri-user-settings-line' }
  ];



  const renderSectionData = (sectionName: string, sectionKey: string) => {
    // Get data from formData using the section key
    const sectionData = formData[sectionKey] || {};

    if (!sectionData || Object.keys(sectionData).length === 0) {
      return (
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
            {sectionName}
          </h4>
          <p className="text-gray-500 dark:text-gray-400">No data available for this section.</p>
        </div>
      );
    }

    return (
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
        <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
          {sectionName}
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Object.entries(sectionData).map(([key, value]) => (
            <div key={key} className="space-y-1">
              <label className="text-sm font-medium text-gray-600 dark:text-gray-400 capitalize">
                {key.replace(/([A-Z])/g, ' $1').trim()}:
              </label>
              <div className="text-gray-900 dark:text-gray-100">
                {Array.isArray(value) ? (
                  <ul className="list-disc list-inside space-y-1">
                    {value.map((item, index) => (
                      <li key={index} className="text-sm">
                        {typeof item === 'object' ? JSON.stringify(item, null, 2) : String(item)}
                      </li>
                    ))}
                  </ul>
                ) : typeof value === 'object' && value !== null ? (
                  <pre className="text-sm bg-gray-100 dark:bg-gray-700 p-2 rounded overflow-auto">
                    {JSON.stringify(value, null, 2)}
                  </pre>
                ) : (
                  <span className="text-sm">{String(value) || 'N/A'}</span>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'applicant':
        return renderSectionData('Applicant Details', 'applicantInfo');
      case 'company':
        return renderSectionData('Company Profile', 'companyProfile');
      case 'management':
        return renderSectionData('Management', 'management');
      case 'professional':
        return renderSectionData('Professional Services', 'professionalServices');
      case 'business':
        return renderSectionData('Business Information', 'businessInfo');
      case 'service':
        return renderSectionData('Service Scope', 'serviceScope');
      case 'plan':
        return renderSectionData('Business Plan', 'businessPlan');
      case 'legal':
        return renderSectionData('Legal History', 'legalHistory');
      case 'documents':
        return (
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Documents</h4>
            {application?.documents && application.documents.length > 0 ? (
              <div className="space-y-3">
                {application.documents.map((doc: any, index: number) => (
                  <div key={index} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="flex items-center">
                      <i className="ri-file-line text-2xl text-gray-400 mr-3"></i>
                      <div>
                        <p className="font-medium text-gray-900 dark:text-gray-100">{doc.name || `Document ${index + 1}`}</p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">{doc.type || 'Unknown type'}</p>
                      </div>
                    </div>
                    <button
                      type="button"
                      className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium"
                    >
                      <i className="ri-download-line mr-1"></i>
                      Download
                    </button>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 dark:text-gray-400">No documents uploaded</p>
            )}
          </div>
        );
      case 'comments':
        return (
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Comments & Reviews</h4>
            <p className="text-gray-500 dark:text-gray-400 mb-4">Add comments for each section before assigning to an officer.</p>
            <div className="space-y-4">
              <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                <h5 className="font-medium text-gray-900 dark:text-gray-100 mb-2">General Comments</h5>
                <textarea
                  className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  rows={4}
                  placeholder="Add your comments about this application..."
                ></textarea>
                <button
                  type="button"
                  className="mt-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Save Comment
                </button>
              </div>
            </div>
          </div>
        );
      case 'assignment':
        return (
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Officer Assignment</h4>
            <p className="text-gray-500 dark:text-gray-400 mb-6">Assign this application to an officer for review and processing.</p>

            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Select Officer
                </label>
                <select className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                  <option value="">Choose an officer...</option>
                  <option value="officer1">John Doe - Senior Officer</option>
                  <option value="officer2">Jane Smith - Review Officer</option>
                  <option value="officer3">Mike Johnson - Technical Officer</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Assignment Notes
                </label>
                <textarea
                  className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  rows={4}
                  placeholder="Add any specific instructions or notes for the assigned officer..."
                ></textarea>
              </div>

              <div className="flex space-x-4">
                <button
                  type="button"
                  className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Assign Officer
                </button>
                <button
                  type="button"
                  className="px-6 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
                >
                  Save as Draft
                </button>
              </div>
            </div>
          </div>
        );
      default:
        return renderSectionData('Applicant Details', 'applicantInfo');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading application details...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <i className="ri-error-warning-line text-4xl text-red-500 mb-4"></i>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Error Loading Application</h3>
            <p className="text-gray-500 dark:text-gray-400 mb-4">{error}</p>
            <div className="space-x-4">
              <button
                type="button"
                onClick={fetchApplicationDetails}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Try Again
              </button>
              <button
                type="button"
                onClick={onBack}
                className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
              >
                Go Back
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!application) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <i className="ri-file-line text-4xl text-gray-400 mb-4"></i>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No Application Data</h3>
            <p className="text-gray-500 dark:text-gray-400 mb-4">Application details could not be found.</p>
            <button
              type="button"
              onClick={onBack}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
            >
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center space-x-4">
              <button
                type="button"
                onClick={onBack}
                className="flex items-center text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100"
              >
                <i className="ri-arrow-left-line mr-2"></i>
                Back to {getDepartmentName(departmentType)}
              </button>
              <div className="h-6 border-l border-gray-300 dark:border-gray-600"></div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  Application Details
                </h1>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  {application.application_number} • {getStatusBadge(application?.status)}
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <p className="text-sm text-gray-500 dark:text-gray-400">Progress</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  {application?.progress_percentage || 0}%
                </p>
              </div>
              <div className="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${application?.progress_percentage || 0}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8" aria-label="Tabs">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                type="button"
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:border-gray-300'
                }`}
              >
                <i className={`${tab.icon} mr-2`}></i>
                {tab.label}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {renderTabContent()}
      </div>
    </div>
  );
}
