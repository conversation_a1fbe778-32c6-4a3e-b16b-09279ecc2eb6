import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  Request,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { LicenseTypesService } from './license-types.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CreateLicenseTypeDto } from '../dto/license-types/create-license-type.dto';
import { UpdateLicenseTypeDto } from '../dto/license-types/update-license-type.dto';
import { LicenseTypes } from '../entities/license-types.entity';
import { Paginate, PaginateQuery } from 'nestjs-paginate';
import { PaginatedResult } from '../common/interfaces/pagination.interface';
import { Audit } from '../common/interceptors/audit.interceptor';
import { AuditAction, AuditModule } from '../entities/audit-trail.entity';

@ApiTags('License Types')
@Controller('license-types')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class LicenseTypesController {
  constructor(private readonly licenseTypesService: LicenseTypesService) {}

  @Get()
  @ApiOperation({ summary: 'Get all license types' })
  @ApiResponse({
    status: 200,
    description: 'List of license types retrieved successfully',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'LicenseType',
    description: 'Viewed license types list',
  })
  async findAll(@Paginate() query: PaginateQuery): Promise<PaginatedResult<LicenseTypes>> {
    return this.licenseTypesService.findAll(query);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get license type by ID' })
  @ApiParam({ name: 'id', description: 'License type UUID' })
  @ApiResponse({
    status: 200,
    description: 'License type retrieved successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'License type not found',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'LicenseType',
    description: 'Viewed license type details',
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<LicenseTypes> {
    return this.licenseTypesService.findOne(id);
  }

  @Post()
  @ApiOperation({ summary: 'Create a new license type' })
  @ApiResponse({
    status: 201,
    description: 'License type created successfully',
  })
  @ApiResponse({
    status: 409,
    description: 'License type with this name already exists',
  })
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'LicenseType',
    description: 'Created new license type',
  })
  async create(
    @Body() createLicenseTypeDto: CreateLicenseTypeDto,
    @Request() req: any,
  ): Promise<LicenseTypes> {
    return this.licenseTypesService.create(createLicenseTypeDto, req.user.userId);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update license type' })
  @ApiParam({ name: 'id', description: 'License type UUID' })
  @ApiResponse({
    status: 200,
    description: 'License type updated successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'License type not found',
  })
  @ApiResponse({
    status: 409,
    description: 'License type with this name already exists',
  })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'LicenseType',
    description: 'Updated license type',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateLicenseTypeDto: UpdateLicenseTypeDto,
    @Request() req: any,
  ): Promise<LicenseTypes> {
    return this.licenseTypesService.update(id, updateLicenseTypeDto, req.user.userId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete license type' })
  @ApiParam({ name: 'id', description: 'License type UUID' })
  @ApiResponse({
    status: 200,
    description: 'License type deleted successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'License type not found',
  })
  @Audit({
    action: AuditAction.DELETE,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'LicenseType',
    description: 'Deleted license type',
  })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<{ message: string }> {
    await this.licenseTypesService.remove(id);
    return { message: 'License type deleted successfully' };
  }
}
