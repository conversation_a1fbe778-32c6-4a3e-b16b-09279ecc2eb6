{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/services/resource/resourceService.ts"], "sourcesContent": ["import { apiClient } from '@/lib/apiClient';\nimport { processApiResponse } from '@/lib/authUtils';\n\n// Types following backend entity structure\nexport interface ResourceApplication {\n  application_id: string;\n  application_number: string;\n  applicant_id: string;\n  title: string;\n  description: string;\n  resource_type: ResourceType;\n  status: ResourceStatus;\n  priority: ResourcePriority;\n  assigned_to?: string;\n  resolution?: string;\n  internal_notes?: string;\n  resolved_at?: string;\n  created_at: string;\n  updated_at: string;\n  deleted_at?: string;\n  created_by?: string;\n  updated_by?: string;\n\n  // Related data\n  applicant?: {\n    user_id: string;\n    first_name: string;\n    last_name: string;\n    email: string;\n    company_name?: string;\n  };\n\n  assignee?: {\n    user_id: string;\n    first_name: string;\n    last_name: string;\n    email: string;\n  };\n}\n\n// Enums\nexport enum ResourceType {\n  EQUIPMENT = 'Equipment',\n  FACILITY = 'Facility',\n  SPECTRUM = 'Spectrum',\n  TECHNICAL_SUPPORT = 'Technical Support',\n  CONSULTATION = 'Consultation',\n  OTHER = 'Other',\n}\n\nexport enum ResourceStatus {\n  SUBMITTED = 'submitted',\n  UNDER_REVIEW = 'under_review',\n  APPROVED = 'approved',\n  REJECTED = 'rejected',\n  IN_PROGRESS = 'in_progress',\n  COMPLETED = 'completed',\n  CANCELLED = 'cancelled',\n}\n\nexport enum ResourcePriority {\n  LOW = 'low',\n  MEDIUM = 'medium',\n  HIGH = 'high',\n  URGENT = 'urgent',\n}\n\nexport interface CreateResourceApplicationData {\n  title: string;\n  description: string;\n  resource_type: ResourceType;\n  priority?: ResourcePriority;\n  attachments?: File[];\n}\n\nexport interface UpdateResourceApplicationData {\n  title?: string;\n  description?: string;\n  resource_type?: ResourceType;\n  status?: ResourceStatus;\n  priority?: ResourcePriority;\n  assigned_to?: string;\n  resolution?: string;\n  internal_notes?: string;\n  resolved_at?: string;\n}\n\nexport interface PaginateQuery {\n  page?: number;\n  limit?: number;\n  search?: string;\n  sortBy?: string;\n  sortOrder?: 'ASC' | 'DESC';\n  filter?: Record<string, string | string[]>;\n}\n\nexport interface PaginatedResponse<T> {\n  data: T[];\n  meta: {\n    itemsPerPage: number;\n    totalItems: number;\n    currentPage: number;\n    totalPages: number;\n    sortBy?: string;\n    sortOrder?: 'ASC' | 'DESC';\n    search?: string;\n    filter?: Record<string, string | string[]>;\n  };\n}\n\nexport type ResourceApplicationsResponse = PaginatedResponse<ResourceApplication>;\n\nexport const resourceService = {\n\n  // Create new resource application\n  async createApplication(data: CreateResourceApplicationData): Promise<ResourceApplication> {\n    try {\n      console.log('🔄 Creating resource application:', {\n        title: data.title,\n        resource_type: data.resource_type,\n        priority: data.priority\n      });\n\n      const formData = new FormData();\n      formData.append('title', data.title);\n      formData.append('description', data.description);\n      formData.append('resource_type', data.resource_type);\n      \n      if (data.priority) {\n        formData.append('priority', data.priority);\n      }\n\n      // Handle file attachments\n      if (data.attachments && data.attachments.length > 0) {\n        data.attachments.forEach((file, index) => {\n          formData.append(`attachments`, file);\n        });\n      }\n\n      const response = await apiClient.post('/resource-applications', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n        },\n      });\n\n      console.log('✅ Resource application created successfully:', response.data);\n      return processApiResponse(response);\n    } catch (error) {\n      console.error('❌ Error creating resource application:', error);\n      throw error;\n    }\n  },\n\n  // Get all resource applications with pagination\n  async getApplications(query: PaginateQuery = {}): Promise<ResourceApplicationsResponse> {\n    const params = new URLSearchParams();\n\n    if (query.page) params.set('page', query.page.toString());\n    if (query.limit) params.set('limit', query.limit.toString());\n    if (query.search) params.set('search', query.search);\n    if (query.sortBy) params.set('sortBy', query.sortBy);\n    if (query.sortOrder) params.set('sortOrder', query.sortOrder);\n\n    // Handle filters\n    if (query.filter) {\n      Object.entries(query.filter).forEach(([key, value]) => {\n        if (Array.isArray(value)) {\n          value.forEach(v => params.append(`filter.${key}`, v));\n        } else {\n          params.set(`filter.${key}`, value);\n        }\n      });\n    }\n\n    const response = await apiClient.get(`/resource-applications?${params.toString()}`);\n    return processApiResponse(response);\n  },\n\n  // Get resource application by ID\n  async getApplication(id: string): Promise<ResourceApplication> {\n    const response = await apiClient.get(`/resource-applications/${id}`);\n    return processApiResponse(response);\n  },\n\n  // Get resource application by ID (alias for consistency)\n  async getApplicationById(id: string): Promise<ResourceApplication> {\n    return this.getApplication(id);\n  },\n\n  // Update resource application\n  async updateApplication(id: string, data: UpdateResourceApplicationData): Promise<ResourceApplication> {\n    const response = await apiClient.put(`/resource-applications/${id}`, data);\n    return processApiResponse(response);\n  },\n\n  // Update resource application status\n  async updateStatus(id: string, status: ResourceStatus): Promise<ResourceApplication> {\n    const response = await apiClient.patch(`/resource-applications/${id}/status`, { status });\n    return processApiResponse(response);\n  },\n\n  // Assign resource application to officer\n  async assignApplication(id: string, assignedTo: string): Promise<ResourceApplication> {\n    const response = await apiClient.patch(`/resource-applications/${id}/assign`, { \n      assigned_to: assignedTo \n    });\n    return processApiResponse(response);\n  },\n\n  // Delete resource application\n  async deleteApplication(id: string): Promise<void> {\n    const response = await apiClient.delete(`/resource-applications/${id}`);\n    return processApiResponse(response);\n  },\n\n  // Get resource application statistics\n  async getStats(): Promise<Record<string, number>> {\n    const response = await apiClient.get('/resource-applications/stats');\n    return processApiResponse(response);\n  },\n\n  // Get resource type options\n  getResourceTypeOptions(): Array<{ value: string; label: string }> {\n    return [\n      { value: 'Equipment', label: 'Equipment' },\n      { value: 'Facility', label: 'Facility' },\n      { value: 'Spectrum', label: 'Spectrum' },\n      { value: 'Technical Support', label: 'Technical Support' },\n      { value: 'Consultation', label: 'Consultation' },\n      { value: 'Other', label: 'Other' }\n    ];\n  },\n\n  // Get status options\n  getStatusOptions(): Array<{ value: string; label: string }> {\n    return [\n      { value: 'submitted', label: 'Submitted' },\n      { value: 'under_review', label: 'Under Review' },\n      { value: 'approved', label: 'Approved' },\n      { value: 'rejected', label: 'Rejected' },\n      { value: 'in_progress', label: 'In Progress' },\n      { value: 'completed', label: 'Completed' },\n      { value: 'cancelled', label: 'Cancelled' }\n    ];\n  },\n\n  // Get priority options\n  getPriorityOptions(): Array<{ value: string; label: string }> {\n    return [\n      { value: 'low', label: 'Low' },\n      { value: 'medium', label: 'Medium' },\n      { value: 'high', label: 'High' },\n      { value: 'urgent', label: 'Urgent' }\n    ];\n  },\n};\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAwCO,IAAA,AAAK,sCAAA;;;;;;;WAAA;;AASL,IAAA,AAAK,wCAAA;;;;;;;;WAAA;;AAUL,IAAA,AAAK,0CAAA;;;;;WAAA;;AAoDL,MAAM,kBAAkB;IAE7B,kCAAkC;IAClC,MAAM,mBAAkB,IAAmC;QACzD,IAAI;YACF,QAAQ,GAAG,CAAC,qCAAqC;gBAC/C,OAAO,KAAK,KAAK;gBACjB,eAAe,KAAK,aAAa;gBACjC,UAAU,KAAK,QAAQ;YACzB;YAEA,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,SAAS,KAAK,KAAK;YACnC,SAAS,MAAM,CAAC,eAAe,KAAK,WAAW;YAC/C,SAAS,MAAM,CAAC,iBAAiB,KAAK,aAAa;YAEnD,IAAI,KAAK,QAAQ,EAAE;gBACjB,SAAS,MAAM,CAAC,YAAY,KAAK,QAAQ;YAC3C;YAEA,0BAA0B;YAC1B,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG,GAAG;gBACnD,KAAK,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM;oBAC9B,SAAS,MAAM,CAAC,CAAC,WAAW,CAAC,EAAE;gBACjC;YACF;YAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,0BAA0B,UAAU;gBACxE,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,QAAQ,GAAG,CAAC,gDAAgD,SAAS,IAAI;YACzE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,MAAM;QACR;IACF;IAEA,gDAAgD;IAChD,MAAM,iBAAgB,QAAuB,CAAC,CAAC;QAC7C,MAAM,SAAS,IAAI;QAEnB,IAAI,MAAM,IAAI,EAAE,OAAO,GAAG,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ;QACtD,IAAI,MAAM,KAAK,EAAE,OAAO,GAAG,CAAC,SAAS,MAAM,KAAK,CAAC,QAAQ;QACzD,IAAI,MAAM,MAAM,EAAE,OAAO,GAAG,CAAC,UAAU,MAAM,MAAM;QACnD,IAAI,MAAM,MAAM,EAAE,OAAO,GAAG,CAAC,UAAU,MAAM,MAAM;QACnD,IAAI,MAAM,SAAS,EAAE,OAAO,GAAG,CAAC,aAAa,MAAM,SAAS;QAE5D,iBAAiB;QACjB,IAAI,MAAM,MAAM,EAAE;YAChB,OAAO,OAAO,CAAC,MAAM,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAChD,IAAI,MAAM,OAAO,CAAC,QAAQ;oBACxB,MAAM,OAAO,CAAC,CAAA,IAAK,OAAO,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBACpD,OAAO;oBACL,OAAO,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBAC9B;YACF;QACF;QAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,OAAO,QAAQ,IAAI;QAClF,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,iCAAiC;IACjC,MAAM,gBAAe,EAAU;QAC7B,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,IAAI;QACnE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,yDAAyD;IACzD,MAAM,oBAAmB,EAAU;QACjC,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B;IAEA,8BAA8B;IAC9B,MAAM,mBAAkB,EAAU,EAAE,IAAmC;QACrE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,IAAI,EAAE;QACrE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,qCAAqC;IACrC,MAAM,cAAa,EAAU,EAAE,MAAsB;QACnD,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,CAAC,uBAAuB,EAAE,GAAG,OAAO,CAAC,EAAE;YAAE;QAAO;QACvF,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,yCAAyC;IACzC,MAAM,mBAAkB,EAAU,EAAE,UAAkB;QACpD,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,CAAC,uBAAuB,EAAE,GAAG,OAAO,CAAC,EAAE;YAC5E,aAAa;QACf;QACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8BAA8B;IAC9B,MAAM,mBAAkB,EAAU;QAChC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,uBAAuB,EAAE,IAAI;QACtE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,sCAAsC;IACtC,MAAM;QACJ,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,4BAA4B;IAC5B;QACE,OAAO;YACL;gBAAE,OAAO;gBAAa,OAAO;YAAY;YACzC;gBAAE,OAAO;gBAAY,OAAO;YAAW;YACvC;gBAAE,OAAO;gBAAY,OAAO;YAAW;YACvC;gBAAE,OAAO;gBAAqB,OAAO;YAAoB;YACzD;gBAAE,OAAO;gBAAgB,OAAO;YAAe;YAC/C;gBAAE,OAAO;gBAAS,OAAO;YAAQ;SAClC;IACH;IAEA,qBAAqB;IACrB;QACE,OAAO;YACL;gBAAE,OAAO;gBAAa,OAAO;YAAY;YACzC;gBAAE,OAAO;gBAAgB,OAAO;YAAe;YAC/C;gBAAE,OAAO;gBAAY,OAAO;YAAW;YACvC;gBAAE,OAAO;gBAAY,OAAO;YAAW;YACvC;gBAAE,OAAO;gBAAe,OAAO;YAAc;YAC7C;gBAAE,OAAO;gBAAa,OAAO;YAAY;YACzC;gBAAE,OAAO;gBAAa,OAAO;YAAY;SAC1C;IACH;IAEA,uBAAuB;IACvB;QACE,OAAO;YACL;gBAAE,OAAO;gBAAO,OAAO;YAAM;YAC7B;gBAAE,OAAO;gBAAU,OAAO;YAAS;YACnC;gBAAE,OAAO;gBAAQ,OAAO;YAAO;YAC/B;gBAAE,OAAO;gBAAU,OAAO;YAAS;SACpC;IACH;AACF", "debugId": null}}, {"offset": {"line": 229, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/services/resource/index.ts"], "sourcesContent": ["export * from './resourceService';\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 250, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/services/task-assignment.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\r\n\r\n// Generic Task interface for different types of tasks\r\nexport interface GenericTask {\r\n  task_id: string;\r\n  task_type: 'application' | 'complaint' | 'data_breach' | 'evaluation' | 'inspection';\r\n  task_number: string;\r\n  title: string;\r\n  description: string;\r\n  status: string;\r\n  priority?: 'low' | 'medium' | 'high' | 'urgent';\r\n  created_at: string;\r\n  updated_at: string;\r\n  assigned_to?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n  assigned_by?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n  assigned_at?: string;\r\n  due_date?: string;\r\n  metadata?: {\r\n    [key: string]: unknown;\r\n  };\r\n}\r\n\r\n// Legacy interface for backward compatibility\r\nexport interface TaskAssignmentApplication {\r\n  application_id: string;\r\n  application_number: string;\r\n  status: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  applicant: {\r\n    applicant_id: string;\r\n    company_name: string;\r\n    first_name: string;\r\n    last_name: string;\r\n  };\r\n  license_category: {\r\n    license_category_id: string;\r\n    name: string;\r\n    license_type: {\r\n      license_type_id: string;\r\n      name: string;\r\n    };\r\n  };\r\n  assignee?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n  assigned_at?: string;\r\n}\r\n\r\nexport interface TaskAssignmentOfficer {\r\n  user_id: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  email: string;\r\n  department?: string;\r\n}\r\n\r\nexport interface AssignApplicationRequest {\r\n  assignedTo: string;\r\n}\r\n\r\nexport interface PaginatedResponse<T> {\r\n  data: T[];\r\n  meta: {\r\n    itemCount: number;\r\n    totalItems: number;\r\n    itemsPerPage: number;\r\n    totalPages: number;\r\n    currentPage: number;\r\n  };\r\n  links: {\r\n    first: string;\r\n    previous: string;\r\n    next: string;\r\n    last: string;\r\n  };\r\n}\r\n\r\nexport const taskAssignmentService = {\r\n  // Generic task management methods\r\n  getUnassignedTasks: async (params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    task_type?: string;\r\n  }) => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n    if (params?.task_type) searchParams.append('task_type', params.task_type);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/tasks/unassigned${queryString ? `?${queryString}` : ''}`;\r\n\r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  getAssignedTasks: async (params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    task_type?: string;\r\n  }) => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n    if (params?.task_type) searchParams.append('task_type', params.task_type);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/tasks/assigned${queryString ? `?${queryString}` : ''}`;\r\n\r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  assignTask: async (taskId: string, assignData: { assignedTo: string; comment?: string }) => {\r\n    const response = await apiClient.put(`/tasks/${taskId}/assign`, assignData);\r\n    return response.data;\r\n  },\r\n\r\n  getTaskById: async (taskId: string) => {\r\n    const response = await apiClient.get(`/tasks/${taskId}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Legacy application-specific methods (for backward compatibility)\r\n  getUnassignedApplications: async (params?: { page?: number; limit?: number; search?: string }) => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/applications/unassigned${queryString ? `?${queryString}` : ''}`;\r\n    \r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  // Get all applications (including assigned)\r\n  getAllApplications: async (params?: { page?: number; limit?: number; search?: string }) => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/applications${queryString ? `?${queryString}` : ''}`;\r\n    \r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  // Get applications assigned to current user\r\n  getMyAssignedApplications: async (params?: { page?: number; limit?: number; search?: string }) => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/applications/assigned/me${queryString ? `?${queryString}` : ''}`;\r\n    \r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  // Get officers for assignment\r\n  getOfficers: async () => {\r\n    try {\r\n      const response = await apiClient.get('/users');\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error fetching officers:', error);\r\n      return { data: [] };\r\n    }\r\n  },\r\n\r\n  // Assign application to officer\r\n  assignApplication: async (applicationId: string, assignData: AssignApplicationRequest) => {\r\n    const response = await apiClient.put(`/applications/${applicationId}/assign`, assignData);\r\n    return response.data;\r\n  },\r\n\r\n  // Get application details\r\n  getApplication: async (applicationId: string) => {\r\n    const response = await apiClient.get(`/applications/${applicationId}`);\r\n    return response.data;\r\n  },\r\n};"], "names": [], "mappings": ";;;AAAA;;AA2FO,MAAM,wBAAwB;IACnC,kCAAkC;IAClC,oBAAoB,OAAO;QAMzB,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAC/D,IAAI,QAAQ,WAAW,aAAa,MAAM,CAAC,aAAa,OAAO,SAAS;QAExE,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAEtE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB,OAAO;QAMvB,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAC/D,IAAI,QAAQ,WAAW,aAAa,MAAM,CAAC,aAAa,OAAO,SAAS;QAExE,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAEpE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,OAAO,OAAO,CAAC,EAAE;QAChE,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ;QACvD,OAAO,SAAS,IAAI;IACtB;IAEA,mEAAmE;IACnE,2BAA2B,OAAO;QAChC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAE/D,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,wBAAwB,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAE7E,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,4CAA4C;IAC5C,oBAAoB,OAAO;QACzB,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAE/D,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAElE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,4CAA4C;IAC5C,2BAA2B,OAAO;QAChC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAE/D,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,yBAAyB,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAE9E,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,8BAA8B;IAC9B,aAAa;QACX,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;gBAAE,MAAM,EAAE;YAAC;QACpB;IACF;IAEA,gCAAgC;IAChC,mBAAmB,OAAO,eAAuB;QAC/C,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,cAAc,OAAO,CAAC,EAAE;QAC9E,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe;QACrE,OAAO,SAAS,IAAI;IACtB;AACF", "debugId": null}}, {"offset": {"line": 352, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/common/AssignModal.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useToast } from '@/contexts/ToastContext';\nimport { taskAssignmentService } from '@/services/task-assignment';\n\ninterface Officer {\n  user_id: string;\n  first_name: string;\n  last_name: string;\n  email: string;\n  department?: string;\n}\n\ninterface AssignModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  itemId: string | null;\n  itemType: 'data_breach' | 'application' | 'complaint';\n  itemTitle?: string;\n  onAssignSuccess?: () => void;\n}\n\nconst AssignModal: React.FC<AssignModalProps> = ({\n  isOpen,\n  onClose,\n  itemId,\n  itemType,\n  itemTitle,\n  onAssignSuccess\n}) => {\n  const { showSuccess, showError } = useToast();\n  const [officers, setOfficers] = useState<Officer[]>([]);\n  const [filteredOfficers, setFilteredOfficers] = useState<Officer[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [assigning, setAssigning] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedOfficer, setSelectedOfficer] = useState<string>('');\n  const [comment, setComment] = useState('');\n\n  useEffect(() => {\n    if (isOpen) {\n      fetchOfficers();\n      setSearchQuery('');\n      setSelectedOfficer('');\n      setComment('');\n    }\n  }, [isOpen]);\n\n  useEffect(() => {\n    // Filter officers based on search query\n    if (searchQuery.trim() === '') {\n      setFilteredOfficers(officers);\n    } else {\n      const filtered = officers.filter(officer =>\n        `${officer.first_name} ${officer.last_name}`.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        officer.email.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        officer.department?.toLowerCase().includes(searchQuery.toLowerCase())\n      );\n      setFilteredOfficers(filtered);\n    }\n  }, [officers, searchQuery]);\n\n  const fetchOfficers = async () => {\n    setLoading(true);\n    try {\n      const response = await taskAssignmentService.getOfficers();\n      setOfficers(response.data || []);\n    } catch (error) {\n      console.error('Error fetching officers:', error);\n      showError('Failed to load officers');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleAssign = async () => {\n    if (!selectedOfficer || !itemId) {\n      showError('Please select an officer');\n      return;\n    }\n\n    setAssigning(true);\n    try {\n      // Create a task for the assignment\n      await taskAssignmentService.assignTask(itemId, {\n        assignedTo: selectedOfficer,\n        comment: comment.trim() || undefined\n      });\n\n      showSuccess('Successfully assigned to officer');\n      onAssignSuccess?.();\n      onClose();\n    } catch (error) {\n      console.error('Error assigning task:', error);\n      showError('Failed to assign task');\n    } finally {\n      setAssigning(false);\n    }\n  };\n\n  const getSelectedOfficerDetails = () => {\n    return officers.find(officer => officer.user_id === selectedOfficer);\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n      <div className=\"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800\">\n        <div className=\"mt-3\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between mb-6\">\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n              Assign {itemType.replace('_', ' ').toUpperCase()}\n            </h3>\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            >\n              <i className=\"ri-close-line text-xl\"></i>\n            </button>\n          </div>\n\n          {/* Item Details */}\n          {itemTitle && (\n            <div className=\"mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n              <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\n                Item to Assign:\n              </h4>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">{itemTitle}</p>\n            </div>\n          )}\n\n          {/* Search Officers */}\n          <div className=\"mb-6\">\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Search Officers\n            </label>\n            <input\n              type=\"text\"\n              placeholder=\"Search by name, email, or department...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n            />\n          </div>\n\n          {/* Officers List */}\n          <div className=\"mb-6\">\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Select Officer ({filteredOfficers.length} found)\n            </label>\n            <div className=\"max-h-64 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md\">\n              {loading ? (\n                <div className=\"p-4 text-center text-gray-500 dark:text-gray-400\">\n                  Loading officers...\n                </div>\n              ) : filteredOfficers.length === 0 ? (\n                <div className=\"p-4 text-center text-gray-500 dark:text-gray-400\">\n                  No officers found\n                </div>\n              ) : (\n                filteredOfficers.map((officer) => (\n                  <div\n                    key={officer.user_id}\n                    className={`p-3 border-b border-gray-200 dark:border-gray-600 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 ${\n                      selectedOfficer === officer.user_id ? 'bg-blue-50 dark:bg-blue-900' : ''\n                    }`}\n                    onClick={() => setSelectedOfficer(officer.user_id)}\n                  >\n                    <div className=\"flex items-center\">\n                      <input\n                        type=\"radio\"\n                        name=\"officer\"\n                        value={officer.user_id}\n                        checked={selectedOfficer === officer.user_id}\n                        onChange={() => setSelectedOfficer(officer.user_id)}\n                        className=\"mr-3\"\n                      />\n                      <div className=\"flex-1\">\n                        <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\n                          {officer.first_name} {officer.last_name}\n                        </div>\n                        <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                          {officer.email}\n                        </div>\n                        {officer.department && (\n                          <div className=\"text-xs text-gray-400 dark:text-gray-500\">\n                            {officer.department}\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                ))\n              )}\n            </div>\n          </div>\n\n          {/* Comment */}\n          <div className=\"mb-6\">\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Assignment Comment (Optional)\n            </label>\n            <textarea\n              value={comment}\n              onChange={(e) => setComment(e.target.value)}\n              placeholder=\"Add any notes or instructions for the assigned officer...\"\n              rows={3}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n            />\n          </div>\n\n          {/* Selected Officer Summary */}\n          {selectedOfficer && (\n            <div className=\"mb-6 p-4 bg-green-50 dark:bg-green-900 rounded-lg\">\n              <h4 className=\"text-sm font-medium text-green-900 dark:text-green-100 mb-2\">\n                Selected Officer:\n              </h4>\n              <div className=\"text-sm text-green-700 dark:text-green-200\">\n                {getSelectedOfficerDetails()?.first_name} {getSelectedOfficerDetails()?.last_name}\n                <br />\n                {getSelectedOfficerDetails()?.email}\n                {getSelectedOfficerDetails()?.department && (\n                  <>\n                    <br />\n                    Department: {getSelectedOfficerDetails()?.department}\n                  </>\n                )}\n              </div>\n            </div>\n          )}\n\n          {/* Actions */}\n          <div className=\"flex justify-end space-x-3\">\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"button\"\n              onClick={handleAssign}\n              disabled={!selectedOfficer || assigning}\n              className=\"px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {assigning ? 'Assigning...' : 'Assign'}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AssignModal;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAuBA,MAAM,cAA0C,CAAC,EAC/C,MAAM,EACN,OAAO,EACP,MAAM,EACN,QAAQ,EACR,SAAS,EACT,eAAe,EAChB;;IACC,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAC1C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,QAAQ;gBACV;gBACA,eAAe;gBACf,mBAAmB;gBACnB,WAAW;YACb;QACF;gCAAG;QAAC;KAAO;IAEX,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,wCAAwC;YACxC,IAAI,YAAY,IAAI,OAAO,IAAI;gBAC7B,oBAAoB;YACtB,OAAO;gBACL,MAAM,WAAW,SAAS,MAAM;sDAAC,CAAA,UAC/B,GAAG,QAAQ,UAAU,CAAC,CAAC,EAAE,QAAQ,SAAS,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC3F,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC5D,QAAQ,UAAU,EAAE,cAAc,SAAS,YAAY,WAAW;;gBAEpE,oBAAoB;YACtB;QACF;gCAAG;QAAC;QAAU;KAAY;IAE1B,MAAM,gBAAgB;QACpB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,wIAAA,CAAA,wBAAqB,CAAC,WAAW;YACxD,YAAY,SAAS,IAAI,IAAI,EAAE;QACjC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,UAAU;QACZ,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,mBAAmB,CAAC,QAAQ;YAC/B,UAAU;YACV;QACF;QAEA,aAAa;QACb,IAAI;YACF,mCAAmC;YACnC,MAAM,wIAAA,CAAA,wBAAqB,CAAC,UAAU,CAAC,QAAQ;gBAC7C,YAAY;gBACZ,SAAS,QAAQ,IAAI,MAAM;YAC7B;YAEA,YAAY;YACZ;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,UAAU;QACZ,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,4BAA4B;QAChC,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,OAAO,KAAK;IACtD;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAuD;oCAC3D,SAAS,OAAO,CAAC,KAAK,KAAK,WAAW;;;;;;;0CAEhD,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC;oCAAE,WAAU;;;;;;;;;;;;;;;;;oBAKhB,2BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA4D;;;;;;0CAG1E,6LAAC;gCAAE,WAAU;0CAA4C;;;;;;;;;;;;kCAK7D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,6LAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,WAAU;;;;;;;;;;;;kCAKd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,WAAU;;oCAAkE;oCAChE,iBAAiB,MAAM;oCAAC;;;;;;;0CAE3C,6LAAC;gCAAI,WAAU;0CACZ,wBACC,6LAAC;oCAAI,WAAU;8CAAmD;;;;;2CAGhE,iBAAiB,MAAM,KAAK,kBAC9B,6LAAC;oCAAI,WAAU;8CAAmD;;;;;2CAIlE,iBAAiB,GAAG,CAAC,CAAC,wBACpB,6LAAC;wCAEC,WAAW,CAAC,yGAAyG,EACnH,oBAAoB,QAAQ,OAAO,GAAG,gCAAgC,IACtE;wCACF,SAAS,IAAM,mBAAmB,QAAQ,OAAO;kDAEjD,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAO,QAAQ,OAAO;oDACtB,SAAS,oBAAoB,QAAQ,OAAO;oDAC5C,UAAU,IAAM,mBAAmB,QAAQ,OAAO;oDAClD,WAAU;;;;;;8DAEZ,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;gEACZ,QAAQ,UAAU;gEAAC;gEAAE,QAAQ,SAAS;;;;;;;sEAEzC,6LAAC;4DAAI,WAAU;sEACZ,QAAQ,KAAK;;;;;;wDAEf,QAAQ,UAAU,kBACjB,6LAAC;4DAAI,WAAU;sEACZ,QAAQ,UAAU;;;;;;;;;;;;;;;;;;uCAxBtB,QAAQ,OAAO;;;;;;;;;;;;;;;;kCAoC9B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;gCAC1C,aAAY;gCACZ,MAAM;gCACN,WAAU;;;;;;;;;;;;oBAKb,iCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA8D;;;;;;0CAG5E,6LAAC;gCAAI,WAAU;;oCACZ,6BAA6B;oCAAW;oCAAE,6BAA6B;kDACxE,6LAAC;;;;;oCACA,6BAA6B;oCAC7B,6BAA6B,4BAC5B;;0DACE,6LAAC;;;;;4CAAK;4CACO,6BAA6B;;;;;;;;;;;;;;;kCAQpD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,UAAU,CAAC,mBAAmB;gCAC9B,WAAU;0CAET,YAAY,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5C;GA1OM;;QAQ+B,mIAAA,CAAA,WAAQ;;;KARvC;uCA4OS", "debugId": null}}, {"offset": {"line": 784, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/common/AssignButton.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport AssignModal from './AssignModal';\n\ninterface AssignButtonProps {\n  itemId: string;\n  itemType: 'data_breach' | 'application' | 'complaint';\n  itemTitle?: string;\n  onAssignSuccess?: () => void;\n  className?: string;\n  size?: 'sm' | 'md' | 'lg';\n  variant?: 'primary' | 'secondary' | 'success';\n  disabled?: boolean;\n  children?: React.ReactNode;\n}\n\nconst AssignButton: React.FC<AssignButtonProps> = ({\n  itemId,\n  itemType,\n  itemTitle,\n  onAssignSuccess,\n  className = '',\n  size = 'sm',\n  variant = 'success',\n  disabled = false,\n  children\n}) => {\n  const [showAssignModal, setShowAssignModal] = useState(false);\n\n  const handleAssignClick = () => {\n    setShowAssignModal(true);\n  };\n\n  const handleCloseModal = () => {\n    setShowAssignModal(false);\n  };\n\n  const handleAssignSuccess = () => {\n    setShowAssignModal(false);\n    onAssignSuccess?.();\n  };\n\n  // Size classes\n  const sizeClasses = {\n    sm: 'px-3 py-1 text-xs',\n    md: 'px-4 py-2 text-sm',\n    lg: 'px-6 py-3 text-base'\n  };\n\n  // Variant classes\n  const variantClasses = {\n    primary: 'text-white bg-primary hover:bg-primary-dark focus:ring-primary',\n    secondary: 'text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 focus:ring-gray-500',\n    success: 'text-white bg-green-600 hover:bg-green-700 focus:ring-green-500'\n  };\n\n  // Base classes\n  const baseClasses = 'inline-flex items-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed';\n\n  // Combine all classes\n  const buttonClasses = `${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]} ${className}`;\n\n  return (\n    <>\n      <button\n        type=\"button\"\n        onClick={handleAssignClick}\n        disabled={disabled}\n        className={buttonClasses}\n        title={`Assign ${itemType.replace('_', ' ')} to officer`}\n      >\n        <i className=\"ri-user-add-line mr-1\"></i>\n        {children || 'Assign'}\n      </button>\n\n      <AssignModal\n        isOpen={showAssignModal}\n        onClose={handleCloseModal}\n        itemId={itemId}\n        itemType={itemType}\n        itemTitle={itemTitle}\n        onAssignSuccess={handleAssignSuccess}\n      />\n    </>\n  );\n};\n\nexport default AssignButton;\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAiBA,MAAM,eAA4C,CAAC,EACjD,MAAM,EACN,QAAQ,EACR,SAAS,EACT,eAAe,EACf,YAAY,EAAE,EACd,OAAO,IAAI,EACX,UAAU,SAAS,EACnB,WAAW,KAAK,EAChB,QAAQ,EACT;;IACC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,oBAAoB;QACxB,mBAAmB;IACrB;IAEA,MAAM,mBAAmB;QACvB,mBAAmB;IACrB;IAEA,MAAM,sBAAsB;QAC1B,mBAAmB;QACnB;IACF;IAEA,eAAe;IACf,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,kBAAkB;IAClB,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,SAAS;IACX;IAEA,eAAe;IACf,MAAM,cAAc;IAEpB,sBAAsB;IACtB,MAAM,gBAAgB,GAAG,YAAY,CAAC,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC,EAAE,WAAW;IAEnG,qBACE;;0BACE,6LAAC;gBACC,MAAK;gBACL,SAAS;gBACT,UAAU;gBACV,WAAW;gBACX,OAAO,CAAC,OAAO,EAAE,SAAS,OAAO,CAAC,KAAK,KAAK,WAAW,CAAC;;kCAExD,6LAAC;wBAAE,WAAU;;;;;;oBACZ,YAAY;;;;;;;0BAGf,6LAAC,8IAAA,CAAA,UAAW;gBACV,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,UAAU;gBACV,WAAW;gBACX,iBAAiB;;;;;;;;AAIzB;GArEM;KAAA;uCAuES", "debugId": null}}, {"offset": {"line": 876, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/resource/ResourceViewModal.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { resourceService, ResourceApplication } from '@/services/resource';\nimport { useToast } from '@/contexts/ToastContext';\nimport Loader from '@/components/Loader';\n\ninterface ResourceViewModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  applicationId: string | null;\n  onUpdate?: () => void;\n}\n\nconst ResourceViewModal: React.FC<ResourceViewModalProps> = ({\n  isOpen,\n  onClose,\n  applicationId,\n  onUpdate\n}) => {\n  const { showSuccess, showError } = useToast();\n  const [application, setApplication] = useState<ResourceApplication | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    if (isOpen && applicationId) {\n      fetchApplicationDetails();\n    }\n  }, [isOpen, applicationId]);\n\n  const fetchApplicationDetails = async () => {\n    if (!applicationId) return;\n    \n    setLoading(true);\n    setError(null);\n    \n    try {\n      const response = await resourceService.getApplication(applicationId);\n      setApplication(response);\n    } catch (err: unknown) {\n      console.error('Error fetching resource application details:', err);\n      const errorMessage = err instanceof Error ? err.message : 'Unknown error';\n      setError(`Failed to load resource application details: ${errorMessage}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status?.toLowerCase()) {\n      case 'submitted': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';\n      case 'under_review': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\n      case 'approved': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\n      case 'rejected': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';\n      case 'in_progress': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';\n      case 'completed': return 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200';\n      case 'cancelled': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n    }\n  };\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority?.toLowerCase()) {\n      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\n      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\n      case 'high': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\n      case 'urgent': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleString();\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n      <div className=\"relative top-20 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white dark:bg-gray-800\">\n        <div className=\"mt-3\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between mb-6\">\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n              Resource Application Details\n            </h3>\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            >\n              <i className=\"ri-close-line text-xl\"></i>\n            </button>\n          </div>\n\n          {/* Content */}\n          {loading ? (\n            <div className=\"py-8\">\n              <Loader message=\"Loading resource application details...\" />\n            </div>\n          ) : error ? (\n            <div className=\"bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4\">\n              <div className=\"flex\">\n                <i className=\"ri-error-warning-line text-red-400 mr-2\"></i>\n                <p className=\"text-red-700 dark:text-red-200\">{error}</p>\n              </div>\n            </div>\n          ) : application ? (\n            <div className=\"space-y-6\">\n              {/* Basic Information */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\n                    Application Number\n                  </h4>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    {application.application_number}\n                  </p>\n                </div>\n                <div>\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\n                    Status\n                  </h4>\n                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(application.status)}`}>\n                    {application.status?.replace('_', ' ').toUpperCase()}\n                  </span>\n                </div>\n                <div>\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\n                    Resource Type\n                  </h4>\n                  <span className=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\">\n                    {application.resource_type}\n                  </span>\n                </div>\n                <div>\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\n                    Priority\n                  </h4>\n                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(application.priority)}`}>\n                    {application.priority?.toUpperCase() || 'MEDIUM'}\n                  </span>\n                </div>\n              </div>\n\n              {/* Title and Description */}\n              <div>\n                <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\n                  Title\n                </h4>\n                <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                  {application.title}\n                </p>\n              </div>\n\n              <div>\n                <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\n                  Description\n                </h4>\n                <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400 whitespace-pre-wrap\">\n                    {application.description}\n                  </p>\n                </div>\n              </div>\n\n              {/* Applicant Information */}\n              {application.applicant && (\n                <div>\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\n                    Applicant\n                  </h4>\n                  <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                      <strong>Name:</strong> {application.applicant.first_name} {application.applicant.last_name}\n                    </p>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                      <strong>Email:</strong> {application.applicant.email}\n                    </p>\n                    {application.applicant.company_name && (\n                      <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                        <strong>Company:</strong> {application.applicant.company_name}\n                      </p>\n                    )}\n                  </div>\n                </div>\n              )}\n\n              {/* Assignment Information */}\n              {application.assignee && (\n                <div>\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\n                    Assigned To\n                  </h4>\n                  <div className=\"bg-green-50 dark:bg-green-900 rounded-lg p-4\">\n                    <p className=\"text-sm text-green-700 dark:text-green-200\">\n                      <strong>Officer:</strong> {application.assignee.first_name} {application.assignee.last_name}\n                    </p>\n                    <p className=\"text-sm text-green-700 dark:text-green-200\">\n                      <strong>Email:</strong> {application.assignee.email}\n                    </p>\n                  </div>\n                </div>\n              )}\n\n              {/* Resolution */}\n              {application.resolution && (\n                <div>\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\n                    Resolution\n                  </h4>\n                  <div className=\"bg-green-50 dark:bg-green-900 rounded-lg p-4\">\n                    <p className=\"text-sm text-green-700 dark:text-green-200 whitespace-pre-wrap\">\n                      {application.resolution}\n                    </p>\n                  </div>\n                </div>\n              )}\n\n              {/* Internal Notes */}\n              {application.internal_notes && (\n                <div>\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\n                    Internal Notes\n                  </h4>\n                  <div className=\"bg-yellow-50 dark:bg-yellow-900 rounded-lg p-4\">\n                    <p className=\"text-sm text-yellow-700 dark:text-yellow-200 whitespace-pre-wrap\">\n                      {application.internal_notes}\n                    </p>\n                  </div>\n                </div>\n              )}\n\n              {/* Timestamps */}\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 pt-4 border-t border-gray-200 dark:border-gray-600\">\n                <div>\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\n                    Submitted\n                  </h4>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    {formatDate(application.created_at)}\n                  </p>\n                </div>\n                <div>\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\n                    Last Updated\n                  </h4>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    {formatDate(application.updated_at)}\n                  </p>\n                </div>\n                {application.resolved_at && (\n                  <div>\n                    <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\n                      Resolved\n                    </h4>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                      {formatDate(application.resolved_at)}\n                    </p>\n                  </div>\n                )}\n              </div>\n            </div>\n          ) : (\n            <div className=\"text-center py-8\">\n              <p className=\"text-gray-500 dark:text-gray-400\">No resource application data available</p>\n            </div>\n          )}\n\n          {/* Actions */}\n          <div className=\"flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-600\">\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\n            >\n              Close\n            </button>\n            {application && (\n              <button\n                type=\"button\"\n                onClick={() => {\n                  // For now, just show a message. In the future, this could navigate to an evaluation page\n                  showSuccess('Resource application evaluation feature coming soon');\n                }}\n                className=\"px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\n              >\n                <i className=\"ri-clipboard-line mr-2\"></i>\n                Evaluate\n              </button>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ResourceViewModal;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;;;AALA;;;;;AAcA,MAAM,oBAAsD,CAAC,EAC3D,MAAM,EACN,OAAO,EACP,aAAa,EACb,QAAQ,EACT;;IACC,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAC1C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B;IAC3E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,UAAU,eAAe;gBAC3B;YACF;QACF;sCAAG;QAAC;QAAQ;KAAc;IAE1B,MAAM,0BAA0B;QAC9B,IAAI,CAAC,eAAe;QAEpB,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,iJAAA,CAAA,kBAAe,CAAC,cAAc,CAAC;YACtD,eAAe;QACjB,EAAE,OAAO,KAAc;YACrB,QAAQ,KAAK,CAAC,gDAAgD;YAC9D,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS,CAAC,6CAA6C,EAAE,cAAc;QACzE,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ,QAAQ;YACd,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ,UAAU;YAChB,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,cAAc;IAC5C;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAuD;;;;;;0CAGrE,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC;oCAAE,WAAU;;;;;;;;;;;;;;;;;oBAKhB,wBACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+HAAA,CAAA,UAAM;4BAAC,SAAQ;;;;;;;;;;+BAEhB,sBACF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;;;;;;8CACb,6LAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;;;;;+BAGjD,4BACF,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,6LAAC;gDAAE,WAAU;0DACV,YAAY,kBAAkB;;;;;;;;;;;;kDAGnC,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,6LAAC;gDAAK,WAAW,CAAC,yDAAyD,EAAE,eAAe,YAAY,MAAM,GAAG;0DAC9G,YAAY,MAAM,EAAE,QAAQ,KAAK,KAAK;;;;;;;;;;;;kDAG3C,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,6LAAC;gDAAK,WAAU;0DACb,YAAY,aAAa;;;;;;;;;;;;kDAG9B,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,6LAAC;gDAAK,WAAW,CAAC,yDAAyD,EAAE,iBAAiB,YAAY,QAAQ,GAAG;0DAClH,YAAY,QAAQ,EAAE,iBAAiB;;;;;;;;;;;;;;;;;;0CAM9C,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,6LAAC;wCAAE,WAAU;kDACV,YAAY,KAAK;;;;;;;;;;;;0CAItB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDACV,YAAY,WAAW;;;;;;;;;;;;;;;;;4BAM7B,YAAY,SAAS,kBACpB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;;kEACX,6LAAC;kEAAO;;;;;;oDAAc;oDAAE,YAAY,SAAS,CAAC,UAAU;oDAAC;oDAAE,YAAY,SAAS,CAAC,SAAS;;;;;;;0DAE5F,6LAAC;gDAAE,WAAU;;kEACX,6LAAC;kEAAO;;;;;;oDAAe;oDAAE,YAAY,SAAS,CAAC,KAAK;;;;;;;4CAErD,YAAY,SAAS,CAAC,YAAY,kBACjC,6LAAC;gDAAE,WAAU;;kEACX,6LAAC;kEAAO;;;;;;oDAAiB;oDAAE,YAAY,SAAS,CAAC,YAAY;;;;;;;;;;;;;;;;;;;4BAQtE,YAAY,QAAQ,kBACnB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;;kEACX,6LAAC;kEAAO;;;;;;oDAAiB;oDAAE,YAAY,QAAQ,CAAC,UAAU;oDAAC;oDAAE,YAAY,QAAQ,CAAC,SAAS;;;;;;;0DAE7F,6LAAC;gDAAE,WAAU;;kEACX,6LAAC;kEAAO;;;;;;oDAAe;oDAAE,YAAY,QAAQ,CAAC,KAAK;;;;;;;;;;;;;;;;;;;4BAO1D,YAAY,UAAU,kBACrB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDACV,YAAY,UAAU;;;;;;;;;;;;;;;;;4BAO9B,YAAY,cAAc,kBACzB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDACV,YAAY,cAAc;;;;;;;;;;;;;;;;;0CAOnC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,6LAAC;gDAAE,WAAU;0DACV,WAAW,YAAY,UAAU;;;;;;;;;;;;kDAGtC,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,6LAAC;gDAAE,WAAU;0DACV,WAAW,YAAY,UAAU;;;;;;;;;;;;oCAGrC,YAAY,WAAW,kBACtB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,6LAAC;gDAAE,WAAU;0DACV,WAAW,YAAY,WAAW;;;;;;;;;;;;;;;;;;;;;;;6CAO7C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;kCAKpD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CACX;;;;;;4BAGA,6BACC,6LAAC;gCACC,MAAK;gCACL,SAAS;oCACP,yFAAyF;oCACzF,YAAY;gCACd;gCACA,WAAU;;kDAEV,6LAAC;wCAAE,WAAU;;;;;;oCAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS1D;GA3RM;;QAM+B,mIAAA,CAAA,WAAQ;;;KANvC;uCA6RS", "debugId": null}}, {"offset": {"line": 1590, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/app/resources/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { resourceService, ResourceApplication } from '@/services/resource';\nimport Loader from '@/components/Loader';\nimport AssignButton from '@/components/common/AssignButton';\nimport ResourceViewModal from '@/components/resource/ResourceViewModal';\n\nconst ResourcesPage: React.FC = () => {\n  const { isAuthenticated } = useAuth();\n  const [applications, setApplications] = useState<ResourceApplication[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string>('');\n  const [selectedApplicationId, setSelectedApplicationId] = useState<string | null>(null);\n  const [showViewModal, setShowViewModal] = useState(false);\n  const [filter, setFilter] = useState({\n    status: '',\n    resource_type: '',\n    priority: '',\n    search: ''\n  });\n\n  const fetchApplications = async () => {\n    if (!isAuthenticated) return;\n\n    try {\n      setLoading(true);\n      console.log('🔄 Fetching all resource applications for staff...');\n      \n      // For staff, fetch all applications (not filtered by user)\n      const response = await resourceService.getApplications({\n        limit: 100,\n        ...filter\n      });\n\n      console.log('✅ Resource applications fetched:', response);\n      \n      if (Array.isArray(response.data)) {\n        setApplications(response.data);\n      } else {\n        setApplications([]);\n      }\n    } catch (err: unknown) {\n      console.error('❌ Error fetching resource applications:', err);\n      if (err instanceof Error) {\n        setError(`Failed to load resource applications: ${err.message}`);\n      } else {\n        setError('Failed to load resource applications: Unknown error');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch applications\n  useEffect(() => {\n    fetchApplications();\n  }, [isAuthenticated, filter]);\n\n  const handleViewApplication = (applicationId: string) => {\n    setSelectedApplicationId(applicationId);\n    setShowViewModal(true);\n  };\n\n  const handleCloseViewModal = () => {\n    setShowViewModal(false);\n    setSelectedApplicationId(null);\n  };\n\n  const handleAssignSuccess = () => {\n    // Refresh the applications list when assignment is successful\n    fetchApplications();\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status?.toLowerCase()) {\n      case 'submitted': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';\n      case 'under_review': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\n      case 'approved': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\n      case 'rejected': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';\n      case 'in_progress': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';\n      case 'completed': return 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200';\n      case 'cancelled': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n    }\n  };\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority?.toLowerCase()) {\n      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\n      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\n      case 'high': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\n      case 'urgent': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n    }\n  };\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <Loader message=\"Checking authentication...\" />\n      </div>\n    );\n  }\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <Loader message=\"Loading resource applications...\" />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"p-6 min-h-full bg-gray-50 dark:bg-gray-900\">\n      {/* Header */}\n      <div className=\"mb-6\">\n        <h1 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">\n          Resource Management\n        </h1>\n        <p className=\"text-gray-600 dark:text-gray-400 mt-2\">\n          Manage and process resource applications and requests\n        </p>\n      </div>\n\n      {/* Statistics Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <i className=\"ri-file-list-line text-2xl text-blue-600\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Total Applications</p>\n              <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">\n                {applications.length}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <i className=\"ri-time-line text-2xl text-yellow-600\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Under Review</p>\n              <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">\n                {applications.filter(a => a.status === 'under_review').length}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <i className=\"ri-check-line text-2xl text-green-600\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Approved</p>\n              <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">\n                {applications.filter(a => a.status === 'approved').length}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <i className=\"ri-alert-line text-2xl text-red-600\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">High Priority</p>\n              <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">\n                {applications.filter(a => a.priority === 'high' || a.priority === 'urgent').length}\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Filters */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4\">Filters</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Search\n            </label>\n            <input\n              type=\"text\"\n              placeholder=\"Search applications...\"\n              value={filter.search}\n              onChange={(e) => setFilter({ ...filter, search: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n            />\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Status\n            </label>\n            <select\n              value={filter.status}\n              onChange={(e) => setFilter({ ...filter, status: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n            >\n              <option value=\"\">All Statuses</option>\n              <option value=\"submitted\">Submitted</option>\n              <option value=\"under_review\">Under Review</option>\n              <option value=\"approved\">Approved</option>\n              <option value=\"rejected\">Rejected</option>\n              <option value=\"in_progress\">In Progress</option>\n              <option value=\"completed\">Completed</option>\n              <option value=\"cancelled\">Cancelled</option>\n            </select>\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Resource Type\n            </label>\n            <select\n              value={filter.resource_type}\n              onChange={(e) => setFilter({ ...filter, resource_type: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n            >\n              <option value=\"\">All Types</option>\n              <option value=\"Equipment\">Equipment</option>\n              <option value=\"Facility\">Facility</option>\n              <option value=\"Spectrum\">Spectrum</option>\n              <option value=\"Technical Support\">Technical Support</option>\n              <option value=\"Consultation\">Consultation</option>\n              <option value=\"Other\">Other</option>\n            </select>\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Priority\n            </label>\n            <select\n              value={filter.priority}\n              onChange={(e) => setFilter({ ...filter, priority: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n            >\n              <option value=\"\">All Priorities</option>\n              <option value=\"low\">Low</option>\n              <option value=\"medium\">Medium</option>\n              <option value=\"high\">High</option>\n              <option value=\"urgent\">Urgent</option>\n            </select>\n          </div>\n        </div>\n      </div>\n\n      {/* Error Message */}\n      {error && (\n        <div className=\"bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4 mb-6\">\n          <div className=\"flex\">\n            <i className=\"ri-error-warning-line text-red-400 mr-2\"></i>\n            <p className=\"text-red-700 dark:text-red-200\">{error}</p>\n          </div>\n        </div>\n      )}\n\n      {/* Applications Table */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden\">\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n            Resource Applications ({applications.length})\n          </h3>\n        </div>\n\n        {applications.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <i className=\"ri-folder-line text-4xl text-gray-400 mb-4\"></i>\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\">No applications found</h3>\n            <p className=\"text-gray-500 dark:text-gray-400\">\n              No resource applications have been submitted yet.\n            </p>\n          </div>\n        ) : (\n          <div className=\"overflow-x-auto\">\n            <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n              <thead className=\"bg-gray-50 dark:bg-gray-900\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    Application\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    Resource Type\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    Status\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    Priority\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    Submitted\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    Actions\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n                {applications.map((application) => (\n                  <tr key={application.application_id} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div>\n                        <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\n                          {application.title}\n                        </div>\n                        <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                          {application.application_number}\n                        </div>\n                        <div className=\"text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs\">\n                          {application.description}\n                        </div>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\">\n                        {application.resource_type}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(application.status)}`}>\n                        {application.status?.replace('_', ' ').toUpperCase()}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(application.priority)}`}>\n                        {application.priority?.toUpperCase() || 'MEDIUM'}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\n                      {new Date(application.created_at).toLocaleDateString()}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                      <div className=\"flex items-center space-x-2\">\n                        <button\n                          type=\"button\"\n                          onClick={() => handleViewApplication(application.application_id)}\n                          className=\"inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\"\n                          title=\"View application details\"\n                        >\n                          <i className=\"ri-eye-line mr-1\"></i>\n                          View\n                        </button>\n                        <AssignButton\n                          itemId={application.application_id}\n                          itemType=\"application\"\n                          itemTitle={application.title}\n                          onAssignSuccess={handleAssignSuccess}\n                        />\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        )}\n      </div>\n\n      {/* View Modal */}\n      <ResourceViewModal\n        isOpen={showViewModal}\n        onClose={handleCloseViewModal}\n        applicationId={selectedApplicationId}\n      />\n    </div>\n  );\n};\n\nexport default ResourcesPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;;;AAPA;;;;;;;AASA,MAAM,gBAA0B;;IAC9B,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAClC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB,EAAE;IAC1E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACnC,QAAQ;QACR,eAAe;QACf,UAAU;QACV,QAAQ;IACV;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,iBAAiB;QAEtB,IAAI;YACF,WAAW;YACX,QAAQ,GAAG,CAAC;YAEZ,2DAA2D;YAC3D,MAAM,WAAW,MAAM,iJAAA,CAAA,kBAAe,CAAC,eAAe,CAAC;gBACrD,OAAO;gBACP,GAAG,MAAM;YACX;YAEA,QAAQ,GAAG,CAAC,oCAAoC;YAEhD,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBAChC,gBAAgB,SAAS,IAAI;YAC/B,OAAO;gBACL,gBAAgB,EAAE;YACpB;QACF,EAAE,OAAO,KAAc;YACrB,QAAQ,KAAK,CAAC,2CAA2C;YACzD,IAAI,eAAe,OAAO;gBACxB,SAAS,CAAC,sCAAsC,EAAE,IAAI,OAAO,EAAE;YACjE,OAAO;gBACL,SAAS;YACX;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBAAqB;IACrB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG;QAAC;QAAiB;KAAO;IAE5B,MAAM,wBAAwB,CAAC;QAC7B,yBAAyB;QACzB,iBAAiB;IACnB;IAEA,MAAM,uBAAuB;QAC3B,iBAAiB;QACjB,yBAAyB;IAC3B;IAEA,MAAM,sBAAsB;QAC1B,8DAA8D;QAC9D;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ,QAAQ;YACd,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ,UAAU;YAChB,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,CAAC,iBAAiB;QACpB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,+HAAA,CAAA,UAAM;gBAAC,SAAQ;;;;;;;;;;;IAGtB;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,+HAAA,CAAA,UAAM;gBAAC,SAAQ;;;;;;;;;;;IAGtB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsD;;;;;;kCAGpE,6LAAC;wBAAE,WAAU;kCAAwC;;;;;;;;;;;;0BAMvD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;;;;;;;;;;;8CAEf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAuD;;;;;;sDACpE,6LAAC;4CAAE,WAAU;sDACV,aAAa,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAM5B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;;;;;;;;;;;8CAEf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAuD;;;;;;sDACpE,6LAAC;4CAAE,WAAU;sDACV,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,gBAAgB,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAMrE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;;;;;;;;;;;8CAEf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAuD;;;;;;sDACpE,6LAAC;4CAAE,WAAU;sDACV,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,YAAY,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAMjE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;;;;;;;;;;;8CAEf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAuD;;;;;;sDACpE,6LAAC;4CAAE,WAAU;sDACV,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,UAAU,EAAE,QAAQ,KAAK,UAAU,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ5F,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA4D;;;;;;kCAC1E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO,OAAO,MAAM;wCACpB,UAAU,CAAC,IAAM,UAAU;gDAAE,GAAG,MAAM;gDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;4CAAC;wCAC/D,WAAU;;;;;;;;;;;;0CAGd,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,OAAO,OAAO,MAAM;wCACpB,UAAU,CAAC,IAAM,UAAU;gDAAE,GAAG,MAAM;gDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;4CAAC;wCAC/D,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAG;;;;;;0DACjB,6LAAC;gDAAO,OAAM;0DAAY;;;;;;0DAC1B,6LAAC;gDAAO,OAAM;0DAAe;;;;;;0DAC7B,6LAAC;gDAAO,OAAM;0DAAW;;;;;;0DACzB,6LAAC;gDAAO,OAAM;0DAAW;;;;;;0DACzB,6LAAC;gDAAO,OAAM;0DAAc;;;;;;0DAC5B,6LAAC;gDAAO,OAAM;0DAAY;;;;;;0DAC1B,6LAAC;gDAAO,OAAM;0DAAY;;;;;;;;;;;;;;;;;;0CAG9B,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,OAAO,OAAO,aAAa;wCAC3B,UAAU,CAAC,IAAM,UAAU;gDAAE,GAAG,MAAM;gDAAE,eAAe,EAAE,MAAM,CAAC,KAAK;4CAAC;wCACtE,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAG;;;;;;0DACjB,6LAAC;gDAAO,OAAM;0DAAY;;;;;;0DAC1B,6LAAC;gDAAO,OAAM;0DAAW;;;;;;0DACzB,6LAAC;gDAAO,OAAM;0DAAW;;;;;;0DACzB,6LAAC;gDAAO,OAAM;0DAAoB;;;;;;0DAClC,6LAAC;gDAAO,OAAM;0DAAe;;;;;;0DAC7B,6LAAC;gDAAO,OAAM;0DAAQ;;;;;;;;;;;;;;;;;;0CAG1B,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,OAAO,OAAO,QAAQ;wCACtB,UAAU,CAAC,IAAM,UAAU;gDAAE,GAAG,MAAM;gDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4CAAC;wCACjE,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAG;;;;;;0DACjB,6LAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,6LAAC;gDAAO,OAAM;0DAAS;;;;;;0DACvB,6LAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,6LAAC;gDAAO,OAAM;0DAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAO9B,uBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;;;;;;sCACb,6LAAC;4BAAE,WAAU;sCAAkC;;;;;;;;;;;;;;;;;0BAMrD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;;gCAAuD;gCAC3C,aAAa,MAAM;gCAAC;;;;;;;;;;;;oBAI/C,aAAa,MAAM,KAAK,kBACvB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;;;;;0CACb,6LAAC;gCAAG,WAAU;0CAA4D;;;;;;0CAC1E,6LAAC;gCAAE,WAAU;0CAAmC;;;;;;;;;;;6CAKlD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAM,WAAU;;8CACf,6LAAC;oCAAM,WAAU;8CACf,cAAA,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;;;;;;;;;;;;8CAKtH,6LAAC;oCAAM,WAAU;8CACd,aAAa,GAAG,CAAC,CAAC,4BACjB,6LAAC;4CAAoC,WAAU;;8DAC7C,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;0EACZ,YAAY,KAAK;;;;;;0EAEpB,6LAAC;gEAAI,WAAU;0EACZ,YAAY,kBAAkB;;;;;;0EAEjC,6LAAC;gEAAI,WAAU;0EACZ,YAAY,WAAW;;;;;;;;;;;;;;;;;8DAI9B,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAK,WAAU;kEACb,YAAY,aAAa;;;;;;;;;;;8DAG9B,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAK,WAAW,CAAC,yDAAyD,EAAE,eAAe,YAAY,MAAM,GAAG;kEAC9G,YAAY,MAAM,EAAE,QAAQ,KAAK,KAAK;;;;;;;;;;;8DAG3C,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAK,WAAW,CAAC,yDAAyD,EAAE,iBAAiB,YAAY,QAAQ,GAAG;kEAClH,YAAY,QAAQ,EAAE,iBAAiB;;;;;;;;;;;8DAG5C,6LAAC;oDAAG,WAAU;8DACX,IAAI,KAAK,YAAY,UAAU,EAAE,kBAAkB;;;;;;8DAEtD,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,MAAK;gEACL,SAAS,IAAM,sBAAsB,YAAY,cAAc;gEAC/D,WAAU;gEACV,OAAM;;kFAEN,6LAAC;wEAAE,WAAU;;;;;;oEAAuB;;;;;;;0EAGtC,6LAAC,+IAAA,CAAA,UAAY;gEACX,QAAQ,YAAY,cAAc;gEAClC,UAAS;gEACT,WAAW,YAAY,KAAK;gEAC5B,iBAAiB;;;;;;;;;;;;;;;;;;2CA/ChB,YAAY,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;0BA4D/C,6LAAC,sJAAA,CAAA,UAAiB;gBAChB,QAAQ;gBACR,SAAS;gBACT,eAAe;;;;;;;;;;;;AAIvB;GAhXM;;QACwB,kIAAA,CAAA,UAAO;;;KAD/B;uCAkXS", "debugId": null}}]}