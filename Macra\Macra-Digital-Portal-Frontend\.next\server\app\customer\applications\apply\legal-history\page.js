(()=>{var e={};e.id=443,e.ids=[443],e.modules={2813:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\applications\\\\apply\\\\legal-history\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\legal-history\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3970:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x});var a=r(60687),i=r(43210),s=r(16189),l=r(94391),o=r(13128),n=r(63213),c=r(76377),d=r(99798),p=r(25890),u=r(78637),y=r(12234);let h={async createLegalHistory(e){try{try{return(await y.uE.post("/legal-history",e,{timeout:3e4})).data}catch(t){return t instanceof Error&&t.message,{legal_history_id:`mock-${Date.now()}`,...e,created_at:new Date().toISOString(),updated_at:new Date().toISOString()}}}catch(e){throw e}},async getLegalHistory(e){try{throw Error("Legal history not found")}catch(e){throw e}},async getLegalHistoryByApplicant(e){try{return null}catch(e){return null}},async updateLegalHistory(e){try{try{return(await y.uE.put(`/legal-history/${e.legal_history_id}`,e,{timeout:3e4})).data}catch(t){return t instanceof Error&&t.message,{legal_history_id:e.legal_history_id,applicant_id:"",criminal_history:e.criminal_history||!1,criminal_details:e.criminal_details||"",bankruptcy_history:e.bankruptcy_history||!1,bankruptcy_details:e.bankruptcy_details||"",regulatory_actions:e.regulatory_actions||!1,regulatory_details:e.regulatory_details||"",litigation_history:e.litigation_history||!1,litigation_details:e.litigation_details||"",compliance_record:e.compliance_record||"",previous_licenses:e.previous_licenses||"",declaration_accepted:e.declaration_accepted||!1,updated_at:new Date().toISOString()}}}catch(e){throw e}},async createOrUpdateLegalHistory(e,t){try{let r=await this.getLegalHistoryByApplicant(e);if(r)return await this.updateLegalHistory({legal_history_id:r.legal_history_id,...t});return await this.createLegalHistory({applicant_id:e,...t})}catch(e){throw e}}};var g=r(85787),m=r(34130);let x=()=>{let e=(0,s.useSearchParams)(),{isAuthenticated:t,loading:r}=(0,n.A)(),y=e.get("license_category_id"),x=e.get("application_id"),[_,b]=(0,i.useState)(!0),[v,f]=(0,i.useState)(!1),[k,j]=(0,i.useState)(null),[w,N]=(0,i.useState)(!1),[P,C]=(0,i.useState)({}),[q,L]=(0,i.useState)(null),[S,D]=(0,i.useState)(null),{handleNext:A,handlePrevious:E,nextStep:H}=(0,p.f)({currentStepRoute:"legal-history",licenseCategoryId:y,applicationId:x}),[M,B]=(0,i.useState)(null),{saveFormData:F}=(0,g.u)({applicationId:x,stepName:"legal-history",autoLoad:!0}),[I,O]=(0,i.useState)({criminal_history:!1,criminal_details:"",bankruptcy_history:!1,bankruptcy_details:"",regulatory_actions:!1,regulatory_details:"",litigation_history:!1,litigation_details:"",compliance_record:"",previous_licenses:"",declaration_accepted:!1}),G=(e,t)=>{O(r=>({...r,[e]:t})),N(!0),S&&D(null),P[e]&&C(t=>{let r={...t};return delete r[e],r})};(0,i.useEffect)(()=>{(async()=>{if(x&&t&&!r)try{if(b(!0),j(null),L(null),y)try{let e=await m.TG.getLicenseCategory(y);e&&e.license_type&&B(e.license_type)}catch(e){}let e=await u.k.getApplication(x);if(e.applicant_id)try{let t=await h.getLegalHistoryByApplicant(e.applicant_id);t&&O({criminal_history:t.criminal_history||!1,criminal_details:t.criminal_details||"",bankruptcy_history:t.bankruptcy_history||!1,bankruptcy_details:t.bankruptcy_details||"",regulatory_actions:t.regulatory_actions||!1,regulatory_details:t.regulatory_details||"",litigation_history:t.litigation_history||!1,litigation_details:t.litigation_details||"",compliance_record:t.compliance_record||"",previous_licenses:t.previous_licenses||"",declaration_accepted:t.declaration_accepted||!1})}catch(e){L("Could not load existing legal history data. You can still fill out the form.")}}catch(e){j("Failed to load application data")}finally{b(!1)}})()},[x,t,r]);let R=async()=>{if(!x)return C({save:"Application ID is required"}),!1;f(!0);try{let e={};if(I.criminal_history&&!I.criminal_details.trim()&&(e.criminal_details="Criminal history details are required when criminal history is indicated"),I.bankruptcy_history&&!I.bankruptcy_details.trim()&&(e.bankruptcy_details="Bankruptcy details are required when bankruptcy history is indicated"),I.regulatory_actions&&!I.regulatory_details.trim()&&(e.regulatory_details="Regulatory action details are required when regulatory actions are indicated"),I.litigation_history&&!I.litigation_details.trim()&&(e.litigation_details="Litigation details are required when litigation history is indicated"),I.declaration_accepted||(e.declaration_accepted="You must accept the declaration to proceed"),Object.keys(e).length>0)return C(e),f(!1),!1;let t=await u.k.getApplication(x);if(t&&t.applicant_id){let e={applicant_id:t.applicant_id,criminal_history:I.criminal_history,criminal_details:I.criminal_details,bankruptcy_history:I.bankruptcy_history,bankruptcy_details:I.bankruptcy_details,regulatory_actions:I.regulatory_actions,regulatory_details:I.regulatory_details,litigation_history:I.litigation_history,litigation_details:I.litigation_details,compliance_record:I.compliance_record,previous_licenses:I.previous_licenses,declaration_accepted:I.declaration_accepted},r=[h.createOrUpdateLegalHistory(t.applicant_id,e),u.k.updateApplication(x,{current_step:7,progress_percentage:100})];try{await Promise.all(r)}catch(e){throw Error("Failed to save legal history information")}}else throw Error("No applicant found for this application");return N(!1),C({}),D("Legal history information saved successfully!"),setTimeout(()=>{D(null)},5e3),!0}catch(e){return C({save:"Failed to save legal history information. Please try again."}),!1}finally{f(!1)}},T=async()=>{await A(R)};return r||_?(0,a.jsx)(l.A,{children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading legal history form..."})]})})}):k?(0,a.jsx)(l.A,{children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto p-6",children:(0,a.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"ri-error-warning-line text-red-600 dark:text-red-400 text-2xl mr-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-red-800 dark:text-red-200",children:"Error Loading Step"}),(0,a.jsx)("p",{className:"text-red-700 dark:text-red-300 mt-1",children:k}),(0,a.jsxs)("button",{onClick:()=>E(),className:"mt-4 inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40",children:[(0,a.jsx)("i",{className:"ri-arrow-left-line mr-2"}),"Go Back"]})]})]})})})}):(0,a.jsx)(l.A,{children:(0,a.jsxs)(o.A,{onNext:T,onPrevious:()=>{E()},onSave:R,showNextButton:!0,showPreviousButton:!0,showSaveButton:!0,nextButtonText:H?`Continue to ${H.name}`:"Continue",previousButtonText:"Back to Previous Step",saveButtonText:"Save Legal History Information",nextButtonDisabled:!1,isSaving:v,children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:x?"Edit Legal History Information":"Legal History Information"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:x?"Update your legal history and compliance information below.":"Provide information about your legal and compliance history."}),x&&!q&&!_&&(0,a.jsx)("div",{className:"mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg",children:(0,a.jsx)("p",{className:"text-sm text-green-700 dark:text-green-300",children:"✅ Editing existing application. Your saved legal history information has been loaded."})}),q&&(0,a.jsx)("div",{className:"mt-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-yellow-700 dark:text-yellow-300",children:["⚠️ ",q]})})]}),(0,a.jsx)(d.bc,{successMessage:S,errorMessage:P.save,validationErrors:Object.fromEntries(Object.entries(P).filter(([e])=>"save"!==e))}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6",children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"border-b border-gray-200 dark:border-gray-700 pb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Legal History & Compliance"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:"Please provide information about your legal and compliance history."})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"criminal_history",checked:I.criminal_history,onChange:e=>G("criminal_history",e.target.checked),className:"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"criminal_history",className:"ml-2 block text-sm text-gray-900 dark:text-gray-100",children:"Do you have any criminal history?"})]}),I.criminal_history&&(0,a.jsx)(c.fs,{label:"Criminal History Details",value:I.criminal_details,onChange:e=>G("criminal_details",e.target.value),error:P.criminal_details,rows:3,placeholder:"Please provide details of your criminal history...",required:!0})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"bankruptcy_history",checked:I.bankruptcy_history,onChange:e=>G("bankruptcy_history",e.target.checked),className:"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"bankruptcy_history",className:"ml-2 block text-sm text-gray-900 dark:text-gray-100",children:"Do you have any bankruptcy history?"})]}),I.bankruptcy_history&&(0,a.jsx)(c.fs,{label:"Bankruptcy History Details",value:I.bankruptcy_details,onChange:e=>G("bankruptcy_details",e.target.value),error:P.bankruptcy_details,rows:3,placeholder:"Please provide details of your bankruptcy history...",required:!0})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"regulatory_actions",checked:I.regulatory_actions,onChange:e=>G("regulatory_actions",e.target.checked),className:"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"regulatory_actions",className:"ml-2 block text-sm text-gray-900 dark:text-gray-100",children:"Have you been subject to any regulatory actions?"})]}),I.regulatory_actions&&(0,a.jsx)(c.fs,{label:"Regulatory Actions Details",value:I.regulatory_details,onChange:e=>G("regulatory_details",e.target.value),error:P.regulatory_details,rows:3,placeholder:"Please provide details of regulatory actions...",required:!0})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"litigation_history",checked:I.litigation_history,onChange:e=>G("litigation_history",e.target.checked),className:"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"litigation_history",className:"ml-2 block text-sm text-gray-900 dark:text-gray-100",children:"Do you have any litigation history?"})]}),I.litigation_history&&(0,a.jsx)(c.fs,{label:"Litigation History Details",value:I.litigation_details,onChange:e=>G("litigation_details",e.target.value),error:P.litigation_details,rows:3,placeholder:"Please provide details of litigation history...",required:!0})]}),(0,a.jsx)(c.fs,{label:"Compliance Record",value:I.compliance_record,onChange:e=>G("compliance_record",e.target.value),rows:3,placeholder:"Describe your compliance record and any relevant certifications..."}),(0,a.jsx)(c.fs,{label:"Previous Licenses",value:I.previous_licenses,onChange:e=>G("previous_licenses",e.target.value),rows:3,placeholder:"List any previous licenses held and their current status..."}),(0,a.jsxs)("div",{className:"border-t border-gray-200 dark:border-gray-700 pt-6",children:[(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("input",{type:"checkbox",id:"declaration_accepted",checked:I.declaration_accepted,onChange:e=>G("declaration_accepted",e.target.checked),className:"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded mt-1"}),(0,a.jsxs)("label",{htmlFor:"declaration_accepted",className:"ml-2 block text-sm text-gray-900 dark:text-gray-100",children:["I declare that the information provided above is true and complete to the best of my knowledge. I understand that providing false information may result in the rejection of my application or revocation of any license granted. ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]})]}),P.declaration_accepted&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:P.declaration_accepted})]})]})]})})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28805:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>c});var a=r(65239),i=r(48088),s=r(88170),l=r.n(s),o=r(30893),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);r.d(t,n);let c={children:["",{children:["customer",{children:["applications",{children:["apply",{children:["legal-history",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,2813)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\legal-history\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,68736)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\legal-history\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/customer/applications/apply/legal-history/page",pathname:"/customer/applications/apply/legal-history",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},29021:e=>{"use strict";e.exports=require("fs")},29123:(e,t,r)=>{Promise.resolve().then(r.bind(r,2813))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},43195:(e,t,r)=>{Promise.resolve().then(r.bind(r,3970))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,7498,1658,5814,7563,6893,140,1887,2324],()=>r(28805));module.exports=a})();