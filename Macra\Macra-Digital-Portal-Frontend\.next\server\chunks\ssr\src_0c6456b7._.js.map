{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/LogoutButton.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { useRouter, usePathname } from 'next/navigation';\r\nimport { useAuth } from '../contexts/AuthContext';\r\n\r\ninterface LogoutButtonProps {\r\n  variant?: 'primary' | 'secondary' | 'text' | 'icon';\r\n  size?: 'sm' | 'md' | 'lg';\r\n  className?: string;\r\n  showConfirmation?: boolean;\r\n  redirectTo?: string;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nexport default function LogoutButton({\r\n  variant = 'primary',\r\n  size = 'md',\r\n  className = '',\r\n  showConfirmation = true,\r\n  redirectTo = '/auth/login',\r\n  children,\r\n}: LogoutButtonProps) {\r\n  const [isLoggingOut, setIsLoggingOut] = useState(false);\r\n  const [showConfirm, setShowConfirm] = useState(false);\r\n  const { logout, user } = useAuth();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  const handleLogout = async () => {\r\n    if (showConfirmation && !showConfirm) {\r\n      setShowConfirm(true);\r\n      return;\r\n    }\r\n\r\n    setIsLoggingOut(true);\r\n    \r\n    try {\r\n      // Call logout from context\r\n      logout();\r\n      \r\n      // Small delay to ensure cleanup is complete\r\n      await new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n      if(pathname.includes('customer')){\r\n        // Redirect to specified page\r\n        router.push('/customer/auth/login');\r\n      } else {\r\n        router.push('/auth/login');\r\n      }\r\n      \r\n\r\n      \r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n    } finally {\r\n      setIsLoggingOut(false);\r\n      setShowConfirm(false);\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    setShowConfirm(false);\r\n  };\r\n\r\n  // Base styles for different variants\r\n  const getVariantStyles = () => {\r\n    switch (variant) {\r\n      case 'primary':\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n      case 'secondary':\r\n        return 'bg-white hover:bg-gray-50 text-red-600 border border-red-600';\r\n      case 'text':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none';\r\n      case 'icon':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none p-2';\r\n      default:\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n    }\r\n  };\r\n\r\n  // Size styles\r\n  const getSizeStyles = () => {\r\n    switch (size) {\r\n      case 'sm':\r\n        return 'px-3 py-1.5 text-sm';\r\n      case 'md':\r\n        return 'px-4 py-2 text-base';\r\n      case 'lg':\r\n        return 'px-6 py-3 text-lg';\r\n      default:\r\n        return 'px-4 py-2 text-base';\r\n    }\r\n  };\r\n\r\n  const baseStyles = 'inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\r\n  \r\n  const buttonStyles = `${baseStyles} ${getVariantStyles()} ${variant !== 'icon' ? getSizeStyles() : ''} ${className}`;\r\n\r\n  // Confirmation dialog\r\n  if (showConfirm) {\r\n    return (\r\n      <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n        <div className=\"bg-white rounded-lg p-6 max-w-sm mx-4 shadow-xl\">\r\n          <div className=\"flex items-center mb-4\">\r\n            <div className=\"flex-shrink-0\">\r\n              <svg className=\"h-6 w-6 text-red-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z\" />\r\n              </svg>\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <h3 className=\"text-lg font-medium text-gray-900\">Confirm Logout</h3>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"mb-4\">\r\n            <p className=\"text-sm text-gray-500\">\r\n              Are you sure you want to logout{user?.first_name ? `, ${user.first_name}` : ''}? You will need to login again to access your account.\r\n            </p>\r\n          </div>\r\n          \r\n          <div className=\"flex space-x-3\">\r\n            <button\r\n              onClick={handleLogout}\r\n              disabled={isLoggingOut}\r\n              className=\"flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 disabled:opacity-50\"\r\n            >\r\n              {isLoggingOut ? 'Logging out...' : 'Yes, Logout'}\r\n            </button>\r\n            <button\r\n              onClick={handleCancel}\r\n              className=\"flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200\"\r\n            >\r\n              Cancel\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <button\r\n      onClick={handleLogout}\r\n      disabled={isLoggingOut}\r\n      className={buttonStyles}\r\n      title=\"Logout\"\r\n    >\r\n      {children || (\r\n        <>\r\n          {variant === 'icon' ? (\r\n            <svg className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n            </svg>\r\n          ) : (\r\n            <>\r\n              <svg className=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n              </svg>\r\n              {isLoggingOut ? 'Logging out...' : 'Logout'}\r\n            </>\r\n          )}\r\n        </>\r\n      )}\r\n    </button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAee,SAAS,aAAa,EACnC,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,mBAAmB,IAAI,EACvB,aAAa,aAAa,EAC1B,QAAQ,EACU;IAClB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe;QACnB,IAAI,oBAAoB,CAAC,aAAa;YACpC,eAAe;YACf;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,2BAA2B;YAC3B;YAEA,4CAA4C;YAC5C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,IAAG,SAAS,QAAQ,CAAC,aAAY;gBAC/B,6BAA6B;gBAC7B,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QAIF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,gBAAgB;YAChB,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,eAAe;IACjB;IAEA,qCAAqC;IACrC,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,cAAc;IACd,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa;IAEnB,MAAM,eAAe,GAAG,WAAW,CAAC,EAAE,mBAAmB,CAAC,EAAE,YAAY,SAAS,kBAAkB,GAAG,CAAC,EAAE,WAAW;IAEpH,sBAAsB;IACtB,IAAI,aAAa;QACf,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAAuB,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CAC3E,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;;;;;;;;;;;;kCAItD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;gCAAwB;gCACH,MAAM,aAAa,CAAC,EAAE,EAAE,KAAK,UAAU,EAAE,GAAG;gCAAG;;;;;;;;;;;;kCAInF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,eAAe,mBAAmB;;;;;;0CAErC,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,UAAU;QACV,WAAW;QACX,OAAM;kBAEL,0BACC;sBACG,YAAY,uBACX,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,SAAQ;gBAAY,QAAO;0BAC9D,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;qCAGvE;;kCACE,8OAAC;wBAAI,WAAU;wBAAe,MAAK;wBAAO,SAAQ;wBAAY,QAAO;kCACnE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAEtE,eAAe,mBAAmB;;;;;;;;;AAOjD", "debugId": null}}, {"offset": {"line": 262, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/customer/CustomerLayout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { usePathname, useRouter } from 'next/navigation';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useLoading } from '@/contexts/LoadingContext';\r\nimport LogoutButton from '../LogoutButton';\r\n\r\ninterface BreadcrumbItem {\r\n  label: string;\r\n  href?: string;\r\n}\r\n\r\ninterface CustomerLayoutProps {\r\n  children: React.ReactNode;\r\n  breadcrumbs?: BreadcrumbItem[];\r\n}\r\n\r\nconst CustomerLayout: React.FC<CustomerLayoutProps> = ({ children, breadcrumbs }) => {\r\n  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);\r\n  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);\r\n  const pathname = usePathname();\r\n  const router = useRouter();\r\n  const { user, logout } = useAuth();\r\n  const { showLoader } = useLoading();\r\n\r\n  // Memoize navigation items to prevent unnecessary re-renders\r\n  const navigationItems = useMemo(() => [\r\n    {\r\n      name: 'Dashboard',\r\n      href: '/customer',\r\n      icon: 'ri-dashboard-line',\r\n      current: pathname === '/customer'\r\n    },\r\n    {\r\n      name: 'My Licenses',\r\n      href: '/customer/my-licenses',\r\n      icon: 'ri-key-line',\r\n      current: pathname === '/customer/my-licenses'\r\n    },\r\n    {\r\n      name: 'New Applications',\r\n      href: '/customer/applications',\r\n      icon: 'ri-file-list-3-line',\r\n      current: pathname === '/customer/applications'\r\n    },\r\n    {\r\n      name: 'Payments',\r\n      href: '/customer/payments',\r\n      icon: 'ri-bank-card-line',\r\n      current: pathname === '/customer/payments'\r\n    },\r\n    {\r\n      name: 'Documents',\r\n      href: '/customer/documents',\r\n      icon: 'ri-file-text-line',\r\n      current: pathname === '/customer/documents'\r\n    },\r\n    {\r\n      name: 'Procurement',\r\n      href: '/customer/procurement',\r\n      icon: 'ri-auction-line',\r\n      current: pathname === '/customer/procurement'\r\n    },\r\n    {\r\n      name: 'Request Resource',\r\n      href: '/customer/resources',\r\n      icon: 'ri-hand-heart-line',\r\n      current: pathname === '/customer/resources'\r\n    }\r\n  ], [pathname]);\r\n\r\n  const supportItems = useMemo(() => [\r\n    {\r\n      name: 'Data Protection',\r\n      href: '/customer/data-protection',\r\n      icon: 'ri-shield-keyhole-line'\r\n    },\r\n    {\r\n      name: 'Help Center',\r\n      href: '/customer/help',\r\n      icon: 'ri-question-line'\r\n    }\r\n  ], []);\r\n\r\n  // Prefetch customer pages on mount for faster navigation\r\n  useEffect(() => {\r\n    const prefetchPages = () => {\r\n      const customerPages = [\r\n        '/customer',\r\n        '/customer/applications',\r\n        '/customer/applications/standards',\r\n        '/customer/payments',\r\n        '/customer/my-licenses',\r\n        '/customer/procurement',\r\n        '/customer/profile',\r\n        '/customer/data-protection',\r\n        '/customer/resources',\r\n        '/customer/help'\r\n      ];\r\n\r\n      customerPages.forEach(page => {\r\n        router.prefetch(page);\r\n      });\r\n    };\r\n\r\n    // Delay prefetching to not interfere with initial page load\r\n    const timer = setTimeout(prefetchPages, 1000);\r\n    return () => clearTimeout(timer);\r\n  }, [router]);\r\n\r\n  const toggleMobileSidebar = useCallback(() => {\r\n    setIsMobileSidebarOpen(!isMobileSidebarOpen);\r\n  }, [isMobileSidebarOpen]);\r\n\r\n  const handleNavClick = useCallback((href: string, name: string) => {\r\n    const pageMessages: { [key: string]: string } = {\r\n      '/customer': 'Loading Dashboard...',\r\n      '/customer/my-licenses': 'Loading My Licenses...',\r\n      '/customer/applications': 'Loading Applications...',\r\n      '/customer/applications/apply/': 'Loading Standards License Options...',\r\n      '/customer/payments': 'Loading Payments...',\r\n      '/customer/documents': 'Loading Documents...',\r\n      '/customer/procurement': 'Loading Procurement...',\r\n      '/customer/resources': 'Loading Resources...',\r\n      '/customer/data-protection': 'Loading Data Protection...',\r\n      '/customer/help': 'Loading Help Center...',\r\n      '/customer/profile': 'Loading Profile...',\r\n      '/customer/settings': 'Loading Settings...'\r\n    };\r\n\r\n    const message = pageMessages[href] || `Loading ${name}...`;\r\n    showLoader(message);\r\n    setIsMobileSidebarOpen(false);\r\n  }, [showLoader]);\r\n\r\n  const handleNavHover = useCallback((href: string) => {\r\n    // Prefetch on hover for instant navigation\r\n    router.prefetch(href);\r\n  }, [router]);\r\n\r\n  const toggleUserDropdown = useCallback(() => {\r\n    setIsUserDropdownOpen(!isUserDropdownOpen);\r\n  }, [isUserDropdownOpen]);\r\n\r\n  return (\r\n    <div className=\"flex h-screen overflow-hidden\">\r\n      {/* Mobile sidebar overlay */}\r\n      {isMobileSidebarOpen && (\r\n        <div \r\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden\"\r\n          onClick={() => setIsMobileSidebarOpen(false)}\r\n        />\r\n      )}\r\n\r\n      {/* Sidebar */}\r\n      <aside className={`\r\n        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out gpu-accelerated\r\n        ${isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}\r\n      `}>\r\n        <div className=\"flex flex-col h-full\">\r\n          {/* Logo/Header */}\r\n          <div className=\"h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700\">\r\n            <div className=\"flex items-center\">\r\n              <Image \r\n                src=\"/images/macra-logo.png\" \r\n                alt=\"MACRA Logo\" \r\n                className=\"max-h-12 w-auto\" \r\n                width={120}\r\n                height={48}\r\n                priority\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Navigation */}\r\n          <nav className=\"mt-6 px-4 side-nav\">\r\n            {/* Main Navigation */}\r\n            <div className=\"space-y-1\">\r\n              {navigationItems.map((item) => (\r\n                <Link\r\n                  key={item.name}\r\n                  href={item.href}\r\n                  onClick={() => handleNavClick(item.href, item.name)}\r\n                  onMouseEnter={() => handleNavHover(item.href)}\r\n                  className={`\r\n                    flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated\r\n                    ${item.current\r\n                      ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-r-2 border-red-600 dark:border-red-400'\r\n                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100'\r\n                    }\r\n                  `}\r\n                >\r\n                  <div className={`w-5 h-5 flex items-center justify-center mr-3 ${item.current ? 'text-red-600 dark:text-red-400' : ''}`}>\r\n                    <i className={item.icon}></i>\r\n                  </div>\r\n                  {item.name}\r\n                </Link>\r\n              ))}\r\n            </div>\r\n\r\n            {/* Support Section */}\r\n            <div className=\"mt-8\">\r\n              <h3 className=\"px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                Support\r\n              </h3>\r\n              <div className=\"mt-2 space-y-1\">\r\n                {supportItems.map((item) => (\r\n                  <Link\r\n                    key={item.name}\r\n                    href={item.href}\r\n                    onClick={() => handleNavClick(item.href, item.name)}\r\n                    onMouseEnter={() => handleNavHover(item.href)}\r\n                    className=\"flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100\"\r\n                  >\r\n                    <div className=\"w-5 h-5 flex items-center justify-center mr-3\">\r\n                      <i className={item.icon}></i>\r\n                    </div>\r\n                    {item.name}\r\n                  </Link>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </nav>\r\n\r\n          {/* User Info */}\r\n          <div className=\"absolute bottom-0 w-64 p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800\">\r\n            <div className=\"flex items-center space-x-3\">\r\n              <Image\r\n                className=\"h-10 w-10 rounded-full object-cover\"\r\n                src={user?.profile_image || \"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp\"}\r\n                alt=\"Profile\"\r\n                width={40}\r\n                height={40}\r\n              />\r\n              <Link \r\n                href='/customer/profile' \r\n                className=\"flex-1 min-w-0\"\r\n                onClick={() => handleNavClick('/customer/profile', 'Profile')}\r\n              >\r\n                <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100 truncate\">\r\n                  {user ? `${user.first_name} ${user.last_name}` : 'Customer'}\r\n                </p>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 truncate\">\r\n                  {/* {user?.organizationName || 'Organization'} */}\r\n                </p>\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </aside>\r\n\r\n      {/* Main content */}\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        {/* Top header */}\r\n        <header className=\"bg-white dark:bg-gray-800 shadow-sm z-10\">\r\n          <div className=\"flex items-center justify-between h-16 px-4 sm:px-6\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={toggleMobileSidebar}\r\n              className=\"md:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none\"\r\n              aria-label=\"Open mobile menu\"\r\n            >\r\n              <div className=\"w-6 h-6 flex items-center justify-center\">\r\n                <i className=\"ri-menu-line ri-lg\"></i>\r\n              </div>\r\n            </button>\r\n            \r\n            <div className=\"flex-1\">\r\n              {breadcrumbs && breadcrumbs.length > 0 && (\r\n                <nav className=\"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400\">\r\n                  {breadcrumbs.map((item, index) => (\r\n                    <React.Fragment key={index}>\r\n                      {index > 0 && <i className=\"ri-arrow-right-s-line\"></i>}\r\n                      {item.href ? (\r\n                        <Link href={item.href} className=\"hover:text-primary\">\r\n                          {item.label}\r\n                        </Link>\r\n                      ) : (\r\n                        <span className=\"text-gray-900 dark:text-gray-100\">{item.label}</span>\r\n                      )}\r\n                    </React.Fragment>\r\n                  ))}\r\n                </nav>\r\n              )}\r\n            </div>\r\n            \r\n            <div className=\"flex items-center\">\r\n              <button\r\n                type=\"button\"\r\n                className=\"flex-shrink-0 p-1 mr-4 text-gray-500 dark:text-gray-400 rounded-full hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none relative\"\r\n              >\r\n                <span className=\"sr-only\">View notifications</span>\r\n                <div className=\"w-6 h-6 flex items-center justify-center\">\r\n                  <i className=\"ri-notification-3-line ri-lg\"></i>\r\n                </div>\r\n                <span className=\"absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white dark:ring-gray-800\"></span>\r\n              </button>\r\n              \r\n              <div className=\"relative\">\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={toggleUserDropdown}\r\n                  className=\"flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n                >\r\n                  <span className=\"sr-only\">Open user menu</span>\r\n                  <Image\r\n                    className=\"h-8 w-8 rounded-full\"\r\n                    src={user?.profile_image || \"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp\"}\r\n                    alt=\"Profile\"\r\n                    width={32}\r\n                    height={32}\r\n                  />\r\n                </button>\r\n                \r\n                {isUserDropdownOpen && (\r\n                  <div className=\"absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 dark:ring-gray-700 z-50\">\r\n                    <div className=\"py-1\">\r\n                      <Link\r\n                        href=\"/customer/profile\"\r\n                        className=\"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        onClick={() => {\r\n                          handleNavClick('/customer/profile', 'Profile');\r\n                          setIsUserDropdownOpen(false);\r\n                        }}\r\n                      >\r\n                        Your Profile\r\n                      </Link>\r\n          \r\n                      <LogoutButton\r\n                        variant=\"text\"\r\n                        size=\"sm\"\r\n                        className=\"w-full justify-start text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        showConfirmation={true}\r\n                      >\r\n                        Sign out\r\n                      </LogoutButton>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </header>\r\n\r\n        {/* Main content area */}\r\n        <main className=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900\">\r\n          <div className=\"py-6\">\r\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n              {children}\r\n            </div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CustomerLayout;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAoBA,MAAM,iBAAgD,CAAC,EAAE,QAAQ,EAAE,WAAW,EAAE;IAC9E,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,aAAU,AAAD;IAEhC,6DAA6D;IAC7D,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM;YACpC;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;SACD,EAAE;QAAC;KAAS;IAEb,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM;YACjC;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;YACR;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;YACR;SACD,EAAE,EAAE;IAEL,yDAAyD;IACzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,MAAM,gBAAgB;gBACpB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YAED,cAAc,OAAO,CAAC,CAAA;gBACpB,OAAO,QAAQ,CAAC;YAClB;QACF;QAEA,4DAA4D;QAC5D,MAAM,QAAQ,WAAW,eAAe;QACxC,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAO;IAEX,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,uBAAuB,CAAC;IAC1B,GAAG;QAAC;KAAoB;IAExB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,MAAc;QAChD,MAAM,eAA0C;YAC9C,aAAa;YACb,yBAAyB;YACzB,0BAA0B;YAC1B,iCAAiC;YACjC,sBAAsB;YACtB,uBAAuB;YACvB,yBAAyB;YACzB,uBAAuB;YACvB,6BAA6B;YAC7B,kBAAkB;YAClB,qBAAqB;YACrB,sBAAsB;QACxB;QAEA,MAAM,UAAU,YAAY,CAAC,KAAK,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,CAAC;QAC1D,WAAW;QACX,uBAAuB;IACzB,GAAG;QAAC;KAAW;IAEf,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,2CAA2C;QAC3C,OAAO,QAAQ,CAAC;IAClB,GAAG;QAAC;KAAO;IAEX,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,sBAAsB,CAAC;IACzB,GAAG;QAAC;KAAmB;IAEvB,qBACE,8OAAC;QAAI,WAAU;;YAEZ,qCACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,uBAAuB;;;;;;0BAK1C,8OAAC;gBAAM,WAAW,CAAC;;QAEjB,EAAE,sBAAsB,kBAAkB,qCAAqC;MACjF,CAAC;0BACC,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,WAAU;oCACV,OAAO;oCACP,QAAQ;oCACR,QAAQ;;;;;;;;;;;;;;;;sCAMd,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;4CAClD,cAAc,IAAM,eAAe,KAAK,IAAI;4CAC5C,WAAW,CAAC;;oBAEV,EAAE,KAAK,OAAO,GACV,8GACA,wHACH;kBACH,CAAC;;8DAED,8OAAC;oDAAI,WAAW,CAAC,8CAA8C,EAAE,KAAK,OAAO,GAAG,mCAAmC,IAAI;8DACrH,cAAA,8OAAC;wDAAE,WAAW,KAAK,IAAI;;;;;;;;;;;gDAExB,KAAK,IAAI;;2CAfL,KAAK,IAAI;;;;;;;;;;8CAqBpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuF;;;;;;sDAGrG,8OAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,4JAAA,CAAA,UAAI;oDAEH,MAAM,KAAK,IAAI;oDACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;oDAClD,cAAc,IAAM,eAAe,KAAK,IAAI;oDAC5C,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAE,WAAW,KAAK,IAAI;;;;;;;;;;;wDAExB,KAAK,IAAI;;mDATL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;sCAiBxB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,6HAAA,CAAA,UAAK;wCACJ,WAAU;wCACV,KAAK,MAAM,iBAAiB;wCAC5B,KAAI;wCACJ,OAAO;wCACP,QAAQ;;;;;;kDAEV,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,eAAe,qBAAqB;;0DAEnD,8OAAC;gDAAE,WAAU;0DACV,OAAO,GAAG,KAAK,UAAU,CAAC,CAAC,EAAE,KAAK,SAAS,EAAE,GAAG;;;;;;0DAEnD,8OAAC;gDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUvB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAO,WAAU;kCAChB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,cAAW;8CAEX,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;;;;;;;;;;;;;;;;8CAIjB,8OAAC;oCAAI,WAAU;8CACZ,eAAe,YAAY,MAAM,GAAG,mBACnC,8OAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;;oDACZ,QAAQ,mBAAK,8OAAC;wDAAE,WAAU;;;;;;oDAC1B,KAAK,IAAI,iBACR,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAM,KAAK,IAAI;wDAAE,WAAU;kEAC9B,KAAK,KAAK;;;;;6EAGb,8OAAC;wDAAK,WAAU;kEAAoC,KAAK,KAAK;;;;;;;+CAP7C;;;;;;;;;;;;;;;8CAe7B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,WAAU;;8DAEV,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;;;;;;;;;;;8DAEf,8OAAC;oDAAK,WAAU;;;;;;;;;;;;sDAGlB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;;sEAEV,8OAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,8OAAC,6HAAA,CAAA,UAAK;4DACJ,WAAU;4DACV,KAAK,MAAM,iBAAiB;4DAC5B,KAAI;4DACJ,OAAO;4DACP,QAAQ;;;;;;;;;;;;gDAIX,oCACC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,4JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;gEACV,SAAS;oEACP,eAAe,qBAAqB;oEACpC,sBAAsB;gEACxB;0EACD;;;;;;0EAID,8OAAC,kIAAA,CAAA,UAAY;gEACX,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,kBAAkB;0EACnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAYf,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;uCAEe", "debugId": null}}, {"offset": {"line": 873, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/config/licenseTypeStepConfig.ts"], "sourcesContent": ["/**\r\n * License Type Step Configuration System\r\n *\r\n * SINGLE SOURCE OF TRUTH for all license type step configurations\r\n *\r\n * This is the consolidated configuration system that defines:\r\n * - Which form steps are required for each license type\r\n * - Step order and navigation flow\r\n * - Validation requirements and estimated times\r\n * - Fallback configurations for unknown license types\r\n *\r\n * Supports the 5 specified license type codes:\r\n * - telecommunications\r\n * - postal_services\r\n * - standards_compliance\r\n * - broadcasting\r\n * - spectrum_management\r\n *\r\n * Features:\r\n * - Optimized step loading based on license type codes\r\n * - Automatic fallback for unsupported types\r\n * - Smart license type resolution (UUID, code, name mapping)\r\n * - Comprehensive helper functions for navigation and progress tracking\r\n */\r\n\r\nexport interface StepConfig {\r\n  id: string;\r\n  name: string;\r\n  component: string;\r\n  route: string;\r\n  required: boolean;\r\n  description: string;\r\n  estimatedTime: string; // in minutes\r\n}\r\n\r\nexport interface LicenseTypeStepConfig {\r\n  licenseTypeId: string;\r\n  name: string;\r\n  description: string;\r\n  steps: StepConfig[];\r\n  estimatedTotalTime: string;\r\n  requirements: string[];\r\n}\r\n\r\n// Base steps that can be used across license types\r\nconst BASE_STEPS: Record<string, StepConfig> = {\r\n  applicantInfo: {\r\n    id: 'applicant-info',\r\n    name: 'Applicant Information',\r\n    component: 'ApplicantInfo',\r\n    route: 'applicant-info',\r\n    required: true,\r\n    description: 'Personal or company information of the applicant',\r\n    estimatedTime: '5'\r\n  },\r\n  addressInfo: {\r\n    id: 'address-info',\r\n    name: 'Address Information',\r\n    component: 'AddressInfo',\r\n    route: 'address-info',\r\n    required: true,\r\n    description: 'Physical and postal address details',\r\n    estimatedTime: '3'\r\n  },\r\n  contactInfo: {\r\n    id: 'contact-info',\r\n    name: 'Contact Information',\r\n    component: 'ContactInfo',\r\n    route: 'contact-info',\r\n    required: true,\r\n    description: 'Contact details and communication preferences',\r\n    estimatedTime: '5'\r\n  },\r\n\r\n  management: {\r\n    id: 'management',\r\n    name: 'Management Structure',\r\n    component: 'Management',\r\n    route: 'management',\r\n    required: false,\r\n    description: 'Management team and organizational structure',\r\n    estimatedTime: '8'\r\n  },\r\n  professionalServices: {\r\n    id: 'professional-services',\r\n    name: 'Professional Services',\r\n    component: 'ProfessionalServices',\r\n    route: 'professional-services',\r\n    required: false,\r\n    description: 'External consultants and service providers',\r\n    estimatedTime: '6'\r\n  },\r\n  serviceScope: {\r\n    id: 'service-scope',\r\n    name: 'Service Scope',\r\n    component: 'ServiceScope',\r\n    route: 'service-scope',\r\n    required: true,\r\n    description: 'Services offered and geographic coverage',\r\n    estimatedTime: '8'\r\n  },\r\n\r\n  legalHistory: {\r\n    id: 'legal-history',\r\n    name: 'Legal History',\r\n    component: 'LegalHistory',\r\n    route: 'legal-history',\r\n    required: true,\r\n    description: 'Legal compliance and regulatory history',\r\n    estimatedTime: '5'\r\n  },\r\n  documents: {\r\n    id: 'documents',\r\n    name: 'Required Documents',\r\n    component: 'Documents',\r\n    route: 'documents',\r\n    required: true,\r\n    description: 'Upload required documents for license application',\r\n    estimatedTime: '10'\r\n  },\r\n  submit: {\r\n    id: 'submit',\r\n    name: 'Submit Application',\r\n    component: 'Submit',\r\n    route: 'submit',\r\n    required: true,\r\n    description: 'Review and submit your application',\r\n    estimatedTime: '5'\r\n  }\r\n};\r\n\r\n// License type specific configurations\r\nexport const LICENSE_TYPE_STEP_CONFIGS: Record<string, LicenseTypeStepConfig> = {\r\n  telecommunications: {\r\n    licenseTypeId: 'telecommunications',\r\n    name: 'Telecommunications License',\r\n    description: 'License for telecommunications service providers including ISPs, mobile operators, and fixed-line services',\r\n    steps: [\r\n      BASE_STEPS.applicantInfo,\r\n      BASE_STEPS.addressInfo,\r\n      BASE_STEPS.contactInfo,\r\n      BASE_STEPS.management,\r\n      BASE_STEPS.serviceScope,\r\n      BASE_STEPS.legalHistory,\r\n      BASE_STEPS.documents,\r\n      BASE_STEPS.submit\r\n    ],\r\n    estimatedTotalTime: '97 minutes',\r\n    requirements: [\r\n      'Business registration certificate',\r\n      'Tax compliance certificate',\r\n      'Technical specifications',\r\n      'Financial statements',\r\n      'Management CVs',\r\n      'Network coverage plans'\r\n    ]\r\n  },\r\n\r\n  postal_services: {\r\n    licenseTypeId: 'postal_services',\r\n    name: 'Postal Services License',\r\n    description: 'License for postal and courier service providers',\r\n    steps: [\r\n      BASE_STEPS.applicantInfo,\r\n      BASE_STEPS.addressInfo,\r\n      BASE_STEPS.contactInfo,\r\n      BASE_STEPS.legalHistory,\r\n      BASE_STEPS.documents,\r\n      BASE_STEPS.submit\r\n    ],\r\n    estimatedTotalTime: '65 minutes',\r\n    requirements: [\r\n      'Business registration certificate',\r\n      'Fleet inventory',\r\n      'Service coverage map',\r\n      'Insurance certificates',\r\n      'Premises documentation'\r\n    ]\r\n  },\r\n\r\n  standards_compliance: {\r\n    licenseTypeId: 'standards_compliance',\r\n    name: 'Standards Compliance License',\r\n    description: 'License for standards compliance and certification services',\r\n    steps: [\r\n      BASE_STEPS.applicantInfo,\r\n      BASE_STEPS.addressInfo,\r\n      BASE_STEPS.contactInfo,\r\n      BASE_STEPS.management,\r\n      BASE_STEPS.professionalServices,\r\n      BASE_STEPS.serviceScope,\r\n      BASE_STEPS.legalHistory,\r\n      BASE_STEPS.documents,\r\n      BASE_STEPS.submit\r\n    ],\r\n    estimatedTotalTime: '82 minutes',\r\n    requirements: [\r\n      'Accreditation certificates',\r\n      'Technical competency proof',\r\n      'Quality management system',\r\n      'Laboratory facilities documentation',\r\n      'Staff qualifications'\r\n    ]\r\n  },\r\n\r\n  broadcasting: {\r\n    licenseTypeId: 'broadcasting',\r\n    name: 'Broadcasting License',\r\n    description: 'License for radio and television broadcasting services',\r\n    steps: [\r\n      BASE_STEPS.applicantInfo,\r\n      BASE_STEPS.addressInfo,\r\n      BASE_STEPS.contactInfo,\r\n      BASE_STEPS.management,\r\n      BASE_STEPS.serviceScope,\r\n      BASE_STEPS.professionalServices,\r\n      BASE_STEPS.legalHistory,\r\n      BASE_STEPS.documents,\r\n      BASE_STEPS.submit\r\n    ],\r\n    estimatedTotalTime: '86 minutes',\r\n    requirements: [\r\n      'Broadcasting equipment specifications',\r\n      'Content programming plan',\r\n      'Studio facility documentation',\r\n      'Transmission coverage maps',\r\n      'Local content compliance plan'\r\n    ]\r\n  },\r\n\r\n  spectrum_management: {\r\n    licenseTypeId: 'spectrum_management',\r\n    name: 'Spectrum Management License',\r\n    description: 'License for radio frequency spectrum management and allocation',\r\n    steps: [\r\n      BASE_STEPS.applicantInfo,\r\n      BASE_STEPS.management,\r\n      BASE_STEPS.serviceScope,\r\n      BASE_STEPS.professionalServices,\r\n      BASE_STEPS.legalHistory,\r\n      BASE_STEPS.documents,\r\n      BASE_STEPS.submit\r\n    ],\r\n    estimatedTotalTime: '89 minutes',\r\n    requirements: [\r\n      'Spectrum usage plan',\r\n      'Technical interference analysis',\r\n      'Equipment type approval',\r\n      'Frequency coordination agreements',\r\n      'Monitoring capabilities documentation'\r\n    ]\r\n  },\r\n\r\n  clf: {\r\n    licenseTypeId: 'clf',\r\n    name: 'CLF License',\r\n    description: 'Consumer Lending and Finance license',\r\n    steps: [\r\n      BASE_STEPS.applicantInfo,\r\n      BASE_STEPS.addressInfo,\r\n      BASE_STEPS.contactInfo,\r\n      BASE_STEPS.management,\r\n      BASE_STEPS.professionalServices,\r\n      BASE_STEPS.legalHistory,\r\n      BASE_STEPS.documents,\r\n      BASE_STEPS.submit\r\n    ],\r\n    estimatedTotalTime: '51 minutes',\r\n    requirements: [\r\n      'Financial institution license',\r\n      'Capital adequacy documentation',\r\n      'Risk management framework',\r\n      'Consumer protection policies',\r\n      'Anti-money laundering procedures'\r\n    ]\r\n  }\r\n};\r\n\r\n// License type name to config key mapping\r\nconst LICENSE_TYPE_NAME_MAPPING: Record<string, string> = {\r\n  'telecommunications': 'telecommunications',\r\n  'postal services': 'postal_services',\r\n  'postal_services': 'postal_services',\r\n  'standards compliance': 'standards_compliance',\r\n  'standards_compliance': 'standards_compliance',\r\n  'broadcasting': 'broadcasting',\r\n  'spectrum management': 'spectrum_management',\r\n  'spectrum_management': 'spectrum_management',\r\n  'clf': 'clf',\r\n  'consumer lending and finance': 'clf'\r\n};\r\n\r\n// Default fallback configuration for unknown license types\r\nconst DEFAULT_FALLBACK_CONFIG: LicenseTypeStepConfig = {\r\n  licenseTypeId: 'default',\r\n  name: 'Standard License Application',\r\n  description: 'Standard license application process with all required steps',\r\n  steps: [\r\n    BASE_STEPS.applicantInfo,\r\n    BASE_STEPS.addressInfo,\r\n    BASE_STEPS.contactInfo,\r\n    BASE_STEPS.management,\r\n    BASE_STEPS.professionalServices,\r\n    BASE_STEPS.serviceScope,\r\n    BASE_STEPS.legalHistory,\r\n    BASE_STEPS.documents,\r\n    BASE_STEPS.submit\r\n  ],\r\n  estimatedTotalTime: '120 minutes',\r\n  requirements: [\r\n    'Business registration certificate',\r\n    'Tax compliance certificate',\r\n    'Financial statements',\r\n    'Management CVs',\r\n    'Professional qualifications',\r\n    'Service documentation'\r\n  ]\r\n};\r\n\r\n// Helper functions\r\nexport const getLicenseTypeStepConfig = (licenseTypeId: string): LicenseTypeStepConfig => {\r\n  // Check if licenseTypeId is valid\r\n  if (!licenseTypeId || typeof licenseTypeId !== 'string') {\r\n    return DEFAULT_FALLBACK_CONFIG;\r\n  }\r\n\r\n  // First try direct lookup with exact match\r\n  let config = LICENSE_TYPE_STEP_CONFIGS[licenseTypeId];\r\n  if (config) {\r\n    return config;\r\n  }\r\n\r\n  // Try normalized lookup (lowercase with underscores)\r\n  const normalizedId = licenseTypeId.toLowerCase().replace(/[^a-z0-9]/g, '_');\r\n  config = LICENSE_TYPE_STEP_CONFIGS[normalizedId];\r\n  if (config) {\r\n    return config;\r\n  }\r\n\r\n  // Try name mapping for common variations\r\n  const mappedKey = LICENSE_TYPE_NAME_MAPPING[normalizedId];\r\n  if (mappedKey) {\r\n    return LICENSE_TYPE_STEP_CONFIGS[mappedKey];\r\n  }\r\n\r\n  // If licenseTypeId looks like a UUID, try to get the code from license types\r\n  if (isUUID(licenseTypeId)) {\r\n    const code = getLicenseTypeCodeFromUUID(licenseTypeId);\r\n    if (code) {\r\n      const foundConfig = LICENSE_TYPE_STEP_CONFIGS[code];\r\n      if (foundConfig) {\r\n        return foundConfig;\r\n      }\r\n    }\r\n  }\r\n\r\n  // Try partial matching for known license type codes\r\n  const knownCodes = Object.keys(LICENSE_TYPE_STEP_CONFIGS);\r\n  const partialMatch = knownCodes.find(code =>\r\n    licenseTypeId.toLowerCase().includes(code) ||\r\n    code.includes(licenseTypeId.toLowerCase())\r\n  );\r\n\r\n  if (partialMatch) {\r\n    return LICENSE_TYPE_STEP_CONFIGS[partialMatch];\r\n  }\r\n  return DEFAULT_FALLBACK_CONFIG;\r\n};\r\n\r\n// Helper function to check if a string is a UUID\r\nconst isUUID = (str: string): boolean => {\r\n  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\r\n  return uuidRegex.test(str);\r\n};\r\n\r\n// Helper function to get license type code from UUID\r\n// This will be populated by the license type service\r\nlet licenseTypeUUIDToCodeMap: Record<string, string> = {};\r\n\r\nexport const setLicenseTypeUUIDToCodeMap = (map: Record<string, string>) => {\r\n  licenseTypeUUIDToCodeMap = map;\r\n};\r\n\r\nconst getLicenseTypeCodeFromUUID = (uuid: string): string | null => {\r\n  return licenseTypeUUIDToCodeMap[uuid] || null;\r\n};\r\n\r\n// Optimized function to get steps by license type code\r\nexport const getStepsByLicenseTypeCode = (licenseTypeCode: string): StepConfig[] => {\r\n  // Validate known license type codes\r\n  const validCodes = ['telecommunications', 'postal_services', 'standards_compliance', 'broadcasting', 'spectrum_management'];\r\n\r\n  if (validCodes.includes(licenseTypeCode)) {\r\n    const config = LICENSE_TYPE_STEP_CONFIGS[licenseTypeCode];\r\n    if (config) {\r\n      return config.steps;\r\n    }\r\n  }\r\n  return DEFAULT_FALLBACK_CONFIG.steps;\r\n};\r\n\r\n// Enhanced function to check if a license type code is supported\r\nexport const isLicenseTypeCodeSupported = (licenseTypeCode: string): boolean => {\r\n  const validCodes = ['telecommunications', 'postal_services', 'standards_compliance', 'broadcasting', 'spectrum_management'];\r\n  return validCodes.includes(licenseTypeCode);\r\n};\r\n\r\n// Function to get all supported license type codes\r\nexport const getSupportedLicenseTypeCodes = (): string[] => {\r\n  return ['telecommunications', 'postal_services', 'standards_compliance', 'broadcasting', 'spectrum_management'];\r\n};\r\n\r\nexport const getStepByRoute = (licenseTypeId: string, stepRoute: string): StepConfig | null => {\r\n  const config = getLicenseTypeStepConfig(licenseTypeId);\r\n  if (!config) return null;\r\n\r\n  return config.steps.find(step => step.route === stepRoute) || null;\r\n};\r\n\r\nexport const getStepByIndex = (licenseTypeId: string, stepIndex: number): StepConfig | null => {\r\n  const config = getLicenseTypeStepConfig(licenseTypeId);\r\n  if (stepIndex < 0 || stepIndex >= config.steps.length) return null;\r\n\r\n  return config.steps[stepIndex];\r\n};\r\n\r\nexport const getStepIndex = (licenseTypeId: string, stepRoute: string): number => {\r\n  const config = getLicenseTypeStepConfig(licenseTypeId);\r\n  return config.steps.findIndex(step => step.route === stepRoute);\r\n};\r\n\r\nexport const getTotalSteps = (licenseTypeId: string): number => {\r\n  const config = getLicenseTypeStepConfig(licenseTypeId);\r\n  return config.steps.length;\r\n};\r\n\r\nexport const getRequiredSteps = (licenseTypeId: string): StepConfig[] => {\r\n  const config = getLicenseTypeStepConfig(licenseTypeId);\r\n  return config.steps.filter(step => step.required);\r\n};\r\n\r\nexport const getOptionalSteps = (licenseTypeId: string): StepConfig[] => {\r\n  const config = getLicenseTypeStepConfig(licenseTypeId);\r\n  return config.steps.filter(step => !step.required);\r\n};\r\n\r\nexport const calculateProgress = (licenseTypeId: string, completedSteps: string[]): number => {\r\n  const config = getLicenseTypeStepConfig(licenseTypeId);\r\n  const totalSteps = config.steps.length;\r\n  const completed = completedSteps.length;\r\n\r\n  return Math.round((completed / totalSteps) * 100);\r\n};\r\n\r\nexport const getNextStep = (licenseTypeId: string, currentStepRoute: string): StepConfig | null => {\r\n  const config = getLicenseTypeStepConfig(licenseTypeId);\r\n  const currentIndex = getStepIndex(licenseTypeId, currentStepRoute);\r\n\r\n  if (currentIndex === -1 || currentIndex >= config.steps.length - 1) return null;\r\n\r\n  return config.steps[currentIndex + 1];\r\n};\r\n\r\nexport const getPreviousStep = (licenseTypeId: string, currentStepRoute: string): StepConfig | null => {\r\n  const config = getLicenseTypeStepConfig(licenseTypeId);\r\n  const currentIndex = getStepIndex(licenseTypeId, currentStepRoute);\r\n\r\n  if (currentIndex <= 0) return null;\r\n\r\n  return config.steps[currentIndex - 1];\r\n};\r\n\r\n// Enhanced function to get step configuration with license type code validation\r\nexport const getOptimizedStepConfig = (licenseTypeCode: string): LicenseTypeStepConfig => {\r\n  // Check if it's a supported license type code\r\n  if (isLicenseTypeCodeSupported(licenseTypeCode)) {\r\n    const config = LICENSE_TYPE_STEP_CONFIGS[licenseTypeCode];\r\n    return config;\r\n  }\r\n  return DEFAULT_FALLBACK_CONFIG;\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;CAuBC;;;;;;;;;;;;;;;;;;AAqBD,mDAAmD;AACnD,MAAM,aAAyC;IAC7C,eAAe;QACb,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,aAAa;QACX,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,aAAa;QACX,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IAEA,YAAY;QACV,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,sBAAsB;QACpB,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,cAAc;QACZ,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IAEA,cAAc;QACZ,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,WAAW;QACT,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,QAAQ;QACN,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;AACF;AAGO,MAAM,4BAAmE;IAC9E,oBAAoB;QAClB,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,WAAW;YACtB,WAAW,WAAW;YACtB,WAAW,UAAU;YACrB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,SAAS;YACpB,WAAW,MAAM;SAClB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,iBAAiB;QACf,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,WAAW;YACtB,WAAW,WAAW;YACtB,WAAW,YAAY;YACvB,WAAW,SAAS;YACpB,WAAW,MAAM;SAClB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,sBAAsB;QACpB,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,WAAW;YACtB,WAAW,WAAW;YACtB,WAAW,UAAU;YACrB,WAAW,oBAAoB;YAC/B,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,SAAS;YACpB,WAAW,MAAM;SAClB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,cAAc;QACZ,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,WAAW;YACtB,WAAW,WAAW;YACtB,WAAW,UAAU;YACrB,WAAW,YAAY;YACvB,WAAW,oBAAoB;YAC/B,WAAW,YAAY;YACvB,WAAW,SAAS;YACpB,WAAW,MAAM;SAClB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,qBAAqB;QACnB,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,UAAU;YACrB,WAAW,YAAY;YACvB,WAAW,oBAAoB;YAC/B,WAAW,YAAY;YACvB,WAAW,SAAS;YACpB,WAAW,MAAM;SAClB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,KAAK;QACH,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,WAAW;YACtB,WAAW,WAAW;YACtB,WAAW,UAAU;YACrB,WAAW,oBAAoB;YAC/B,WAAW,YAAY;YACvB,WAAW,SAAS;YACpB,WAAW,MAAM;SAClB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;AACF;AAEA,0CAA0C;AAC1C,MAAM,4BAAoD;IACxD,sBAAsB;IACtB,mBAAmB;IACnB,mBAAmB;IACnB,wBAAwB;IACxB,wBAAwB;IACxB,gBAAgB;IAChB,uBAAuB;IACvB,uBAAuB;IACvB,OAAO;IACP,gCAAgC;AAClC;AAEA,2DAA2D;AAC3D,MAAM,0BAAiD;IACrD,eAAe;IACf,MAAM;IACN,aAAa;IACb,OAAO;QACL,WAAW,aAAa;QACxB,WAAW,WAAW;QACtB,WAAW,WAAW;QACtB,WAAW,UAAU;QACrB,WAAW,oBAAoB;QAC/B,WAAW,YAAY;QACvB,WAAW,YAAY;QACvB,WAAW,SAAS;QACpB,WAAW,MAAM;KAClB;IACD,oBAAoB;IACpB,cAAc;QACZ;QACA;QACA;QACA;QACA;QACA;KACD;AACH;AAGO,MAAM,2BAA2B,CAAC;IACvC,kCAAkC;IAClC,IAAI,CAAC,iBAAiB,OAAO,kBAAkB,UAAU;QACvD,OAAO;IACT;IAEA,2CAA2C;IAC3C,IAAI,SAAS,yBAAyB,CAAC,cAAc;IACrD,IAAI,QAAQ;QACV,OAAO;IACT;IAEA,qDAAqD;IACrD,MAAM,eAAe,cAAc,WAAW,GAAG,OAAO,CAAC,cAAc;IACvE,SAAS,yBAAyB,CAAC,aAAa;IAChD,IAAI,QAAQ;QACV,OAAO;IACT;IAEA,yCAAyC;IACzC,MAAM,YAAY,yBAAyB,CAAC,aAAa;IACzD,IAAI,WAAW;QACb,OAAO,yBAAyB,CAAC,UAAU;IAC7C;IAEA,6EAA6E;IAC7E,IAAI,OAAO,gBAAgB;QACzB,MAAM,OAAO,2BAA2B;QACxC,IAAI,MAAM;YACR,MAAM,cAAc,yBAAyB,CAAC,KAAK;YACnD,IAAI,aAAa;gBACf,OAAO;YACT;QACF;IACF;IAEA,oDAAoD;IACpD,MAAM,aAAa,OAAO,IAAI,CAAC;IAC/B,MAAM,eAAe,WAAW,IAAI,CAAC,CAAA,OACnC,cAAc,WAAW,GAAG,QAAQ,CAAC,SACrC,KAAK,QAAQ,CAAC,cAAc,WAAW;IAGzC,IAAI,cAAc;QAChB,OAAO,yBAAyB,CAAC,aAAa;IAChD;IACA,OAAO;AACT;AAEA,iDAAiD;AACjD,MAAM,SAAS,CAAC;IACd,MAAM,YAAY;IAClB,OAAO,UAAU,IAAI,CAAC;AACxB;AAEA,qDAAqD;AACrD,qDAAqD;AACrD,IAAI,2BAAmD,CAAC;AAEjD,MAAM,8BAA8B,CAAC;IAC1C,2BAA2B;AAC7B;AAEA,MAAM,6BAA6B,CAAC;IAClC,OAAO,wBAAwB,CAAC,KAAK,IAAI;AAC3C;AAGO,MAAM,4BAA4B,CAAC;IACxC,oCAAoC;IACpC,MAAM,aAAa;QAAC;QAAsB;QAAmB;QAAwB;QAAgB;KAAsB;IAE3H,IAAI,WAAW,QAAQ,CAAC,kBAAkB;QACxC,MAAM,SAAS,yBAAyB,CAAC,gBAAgB;QACzD,IAAI,QAAQ;YACV,OAAO,OAAO,KAAK;QACrB;IACF;IACA,OAAO,wBAAwB,KAAK;AACtC;AAGO,MAAM,6BAA6B,CAAC;IACzC,MAAM,aAAa;QAAC;QAAsB;QAAmB;QAAwB;QAAgB;KAAsB;IAC3H,OAAO,WAAW,QAAQ,CAAC;AAC7B;AAGO,MAAM,+BAA+B;IAC1C,OAAO;QAAC;QAAsB;QAAmB;QAAwB;QAAgB;KAAsB;AACjH;AAEO,MAAM,iBAAiB,CAAC,eAAuB;IACpD,MAAM,SAAS,yBAAyB;IACxC,IAAI,CAAC,QAAQ,OAAO;IAEpB,OAAO,OAAO,KAAK,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK,cAAc;AAChE;AAEO,MAAM,iBAAiB,CAAC,eAAuB;IACpD,MAAM,SAAS,yBAAyB;IACxC,IAAI,YAAY,KAAK,aAAa,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO;IAE9D,OAAO,OAAO,KAAK,CAAC,UAAU;AAChC;AAEO,MAAM,eAAe,CAAC,eAAuB;IAClD,MAAM,SAAS,yBAAyB;IACxC,OAAO,OAAO,KAAK,CAAC,SAAS,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK;AACvD;AAEO,MAAM,gBAAgB,CAAC;IAC5B,MAAM,SAAS,yBAAyB;IACxC,OAAO,OAAO,KAAK,CAAC,MAAM;AAC5B;AAEO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,SAAS,yBAAyB;IACxC,OAAO,OAAO,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ;AAClD;AAEO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,SAAS,yBAAyB;IACxC,OAAO,OAAO,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,QAAQ;AACnD;AAEO,MAAM,oBAAoB,CAAC,eAAuB;IACvD,MAAM,SAAS,yBAAyB;IACxC,MAAM,aAAa,OAAO,KAAK,CAAC,MAAM;IACtC,MAAM,YAAY,eAAe,MAAM;IAEvC,OAAO,KAAK,KAAK,CAAC,AAAC,YAAY,aAAc;AAC/C;AAEO,MAAM,cAAc,CAAC,eAAuB;IACjD,MAAM,SAAS,yBAAyB;IACxC,MAAM,eAAe,aAAa,eAAe;IAEjD,IAAI,iBAAiB,CAAC,KAAK,gBAAgB,OAAO,KAAK,CAAC,MAAM,GAAG,GAAG,OAAO;IAE3E,OAAO,OAAO,KAAK,CAAC,eAAe,EAAE;AACvC;AAEO,MAAM,kBAAkB,CAAC,eAAuB;IACrD,MAAM,SAAS,yBAAyB;IACxC,MAAM,eAAe,aAAa,eAAe;IAEjD,IAAI,gBAAgB,GAAG,OAAO;IAE9B,OAAO,OAAO,KAAK,CAAC,eAAe,EAAE;AACvC;AAGO,MAAM,yBAAyB,CAAC;IACrC,8CAA8C;IAC9C,IAAI,2BAA2B,kBAAkB;QAC/C,MAAM,SAAS,yBAAyB,CAAC,gBAAgB;QACzD,OAAO;IACT;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1324, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/lib/customer-api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';\r\nimport Cookies from 'js-cookie';\r\nimport { processApiResponse } from './authUtils';\r\n\r\n// API Configuration\r\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';\r\n\r\n// Create axios instance for customer portal (same as staff portal)\r\nconst customerApiClient: AxiosInstance = axios.create({\r\n  baseURL: API_BASE_URL,\r\n  timeout: 120000, // Increased timeout to match main API client (120 seconds)\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n    'Accept': 'application/json',\r\n  },\r\n});\r\n\r\n// Create auth-specific client (same as staff portal)\r\nconst customerAuthApiClient: AxiosInstance = axios.create({\r\n  baseURL: `${API_BASE_URL}/auth`,\r\n  timeout: 120000, // Increased timeout to match main API client\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n    'Accept': 'application/json',\r\n  },\r\n});\r\n\r\n// Add debug logging to auth client (only in development)\r\ncustomerAuthApiClient.interceptors.request.use(\r\n  (config) => {\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log('Customer Auth API Request:', {\r\n        url: `${config.baseURL}${config.url}`,\r\n        method: config.method,\r\n        headers: config.headers,\r\n        data: config.data\r\n      });\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    console.error('Customer Auth API Request Error:', error);\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\ncustomerAuthApiClient.interceptors.response.use(\r\n  (response) => {\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log('Customer Auth API Response Success:', {\r\n        status: response.status,\r\n        statusText: response.statusText,\r\n        url: response.config.url\r\n      });\r\n    }\r\n    return response;\r\n  },\r\n  (error) => {\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.error('Customer Auth API Interceptor Error:', {\r\n        message: error?.message || 'Unknown error',\r\n        code: error?.code || 'NO_CODE',\r\n        status: error?.response?.status || 'NO_STATUS',\r\n        statusText: error?.response?.statusText || 'NO_STATUS_TEXT',\r\n        url: error?.config?.url || 'NO_URL',\r\n        method: error?.config?.method || 'NO_METHOD',\r\n        baseURL: error?.config?.baseURL || 'NO_BASE_URL',\r\n        isAxiosError: error?.isAxiosError || false,\r\n        responseData: error?.response?.data || 'NO_RESPONSE_DATA',\r\n        requestData: error?.config?.data || 'NO_REQUEST_DATA',\r\n        headers: error?.config?.headers || 'NO_HEADERS'\r\n      });\r\n    }\r\n\r\n    // Don't handle 401 here, let the login method handle it\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Request interceptor to add auth token\r\ncustomerApiClient.interceptors.request.use(\r\n  (config) => {\r\n    const token = Cookies.get('auth_token');\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Response interceptor for error handling with retry logic\r\ncustomerApiClient.interceptors.response.use(\r\n  (response: AxiosResponse) => {\r\n    return response;\r\n  },\r\n  async (error: AxiosError) => {\r\n    const originalRequest = error.config as AxiosError['config'] & {\r\n      _retry?: boolean;\r\n      _retryCount?: number;\r\n    };\r\n\r\n    // Handle 429 Rate Limiting\r\n    if (error.response?.status === 429) {\r\n      if (!originalRequest._retry) {\r\n        originalRequest._retry = true;\r\n        \r\n        // Get retry delay from headers or use exponential backoff\r\n        const retryAfter = error.response.headers['retry-after'];\r\n        const delay = retryAfter ? parseInt(retryAfter) * 1000 : Math.min(1000 * Math.pow(2, originalRequest._retryCount || 0), 10000);\r\n        \r\n        originalRequest._retryCount = (originalRequest._retryCount || 0) + 1;\r\n        \r\n        // Don't retry more than 3 times\r\n        if (originalRequest._retryCount <= 3) {\r\n          console.warn(`Rate limited. Retrying after ${delay}ms (attempt ${originalRequest._retryCount})`);\r\n          \r\n          await new Promise(resolve => setTimeout(resolve, delay));\r\n          return customerApiClient(originalRequest);\r\n        }\r\n      }\r\n    }\r\n\r\n    // Handle 401 Unauthorized\r\n    if (error.response?.status === 401) {\r\n      // Clear auth token and redirect to login\r\n      Cookies.remove('auth_token');\r\n      Cookies.remove('auth_user');\r\n      window.location.href = '/auth/login';\r\n    }\r\n    \r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// API Service Class\r\nexport class CustomerApiService {\r\n  public api: AxiosInstance;\r\n  private pendingRequests: Map<string, Promise<unknown>> = new Map();\r\n\r\n  constructor() {\r\n    this.api = customerApiClient;\r\n  }\r\n\r\n  // Request deduplication helper\r\n  private async deduplicateRequest<T>(key: string, requestFn: () => Promise<T>): Promise<T> {\r\n    if (this.pendingRequests.has(key)) {\r\n      return this.pendingRequests.get(key) as Promise<T>;\r\n    }\r\n\r\n    const promise = requestFn().finally(() => {\r\n      this.pendingRequests.delete(key);\r\n    });\r\n\r\n    this.pendingRequests.set(key, promise);\r\n    return promise;\r\n  }\r\n\r\n  // Set auth token\r\n  setAuthToken(token: string) {\r\n    this.api.defaults.headers.common['Authorization'] = `Bearer ${token}`;\r\n  }\r\n\r\n  // Remove auth token\r\n  removeAuthToken() {\r\n    delete this.api.defaults.headers.common['Authorization'];\r\n  }\r\n\r\n  async logout() {\r\n    const response = await customerAuthApiClient.post('/logout');\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async refreshToken() {\r\n    const response = await customerAuthApiClient.post('/refresh');\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // 2FA endpoints\r\n  async generateTwoFactorCode(userId: string, action: string) {\r\n    const response = await customerAuthApiClient.post('/generate-2fa', { user_id: userId, action });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async verify2FA(data: { user_id: string; code: string; unique: string }) {\r\n    const response = await customerAuthApiClient.post('/verify-2fa', data);\r\n\r\n    // Handle response structure consistently with login\r\n    if (processApiResponse(response)?.data) {\r\n      const authData = processApiResponse(response).data;\r\n      \r\n      // Map backend field names to frontend expected format\r\n      const mappedAuthData = {\r\n        access_token: authData.access_token,\r\n        user: {\r\n          id: authData.user.user_id,\r\n          firstName: authData.user.first_name,\r\n          lastName: authData.user.last_name,\r\n          email: authData.user.email,\r\n          roles: authData.user.roles || [],\r\n          isAdmin: (authData.user.roles || []).includes('administrator'),\r\n          profileImage: authData.user.profile_image,\r\n          createdAt: authData.user.created_at || new Date().toISOString(),\r\n          lastLogin: authData.user.last_login,\r\n          organizationName: authData.user.organization_name,\r\n          two_factor_enabled: authData.user.two_factor_enabled\r\n        }\r\n      };\r\n      \r\n      return mappedAuthData;\r\n    }\r\n\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async setupTwoFactorAuth(data: { access_token: string; user_id: string }) {\r\n    const response = await customerAuthApiClient.post('/setup-2fa', data);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // User profile endpoints\r\n  async getProfile() {\r\n    return this.deduplicateRequest('getProfile', async () => {\r\n      const response = await this.api.get('/users/profile');\r\n      return processApiResponse(response);\r\n    });\r\n  }\r\n\r\n  async updateProfile(profileData: ProfileUpdateData) {\r\n    const response = await this.api.put('/users/profile', profileData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Addressing endpoints\r\n  async getAddresses() {\r\n    const response = await this.api.get('/address/all');\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async createAddress(addressData: CreateAddressData) {\r\n    const response = await this.api.post('/address/create', addressData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getAddress(id: string) {\r\n    const response = await this.api.get(`/address/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async editAddress(addressData: EditAddressData) {\r\n    const { address_id, ...updateData } = addressData;\r\n    if (!address_id) {\r\n      throw new Error('Address ID is required for updating');\r\n    }\r\n    const response = await this.api.put(`/address/${address_id}`, updateData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getAddressesByEntity(entityType: string, entityId: string) {\r\n    const response = await this.api.get(`/address/all?entity_type=${encodeURIComponent(entityType)}&entity_id=${encodeURIComponent(entityId)}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async deleteAddress(id: string) {\r\n    const response = await this.api.delete(`/address/soft/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async searchPostcodes(searchParams: SearchPostcodes) {\r\n    const response = await this.api.post('/postal-codes/search', searchParams);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // License endpoints\r\n  async getLicenses(params?: { status?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/licenses', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getLicense(id: string) {\r\n    const response = await this.api.get(`/licenses/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async createLicenseApplication(applicationData: LicenseApplicationData) {\r\n    const response = await this.api.post('/license-applications', applicationData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // License Types endpoints\r\n  async getLicenseTypes(params?: { page?: number; limit?: number }) {\r\n    const response = await this.api.get('/license-types', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getLicenseType(id: string) {\r\n    const response = await this.api.get(`/license-types/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // License Categories endpoints\r\n  async getLicenseCategories(params?: { page?: number; limit?: number }) {\r\n    const response = await this.api.get('/license-categories', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getLicenseCategoriesByType(licenseTypeId: string) {\r\n    const response = await this.api.get(`/license-categories/by-license-type/${licenseTypeId}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getLicenseCategoryTree(licenseTypeId: string) {\r\n    const response = await this.api.get(`/license-categories/license-type/${licenseTypeId}/tree`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getLicenseCategory(id: string) {\r\n    const response = await this.api.get(`/license-categories/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Application endpoints\r\n  async getApplications(params?: { status?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/applications', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getApplication(id: string) {\r\n    const response = await this.api.get(`/applications/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async createApplication(applicationData: any) {\r\n    const response = await this.api.post('/applications', applicationData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async updateApplication(id: string, applicationData: Partial<LicenseApplicationData>) {\r\n    const response = await this.api.put(`/applications/${id}`, applicationData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Payment endpoints\r\n  async getPayments(params?: { status?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/payments', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getPayment(id: string) {\r\n    const response = await this.api.get(`/payments/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async createPayment(paymentData: PaymentCreateData) {\r\n    const response = await this.api.post('/payments', paymentData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Document endpoints\r\n  async getDocuments(params?: { type?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/documents', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async uploadDocument(formData: FormData) {\r\n    const response = await this.api.post('/documents/upload', formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async downloadDocument(id: string) {\r\n    const response = await this.api.get(`/documents/${id}/download`, {\r\n      responseType: 'blob',\r\n    });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Dashboard statistics\r\n  async getDashboardStats() {\r\n    const response = await this.api.get('/dashboard/stats');\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Notifications\r\n  async getNotifications(params?: { read?: boolean; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/notifications', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async markNotificationAsRead(id: string) {\r\n    const response = await this.api.patch(`/notifications/${id}/read`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Procurement endpoints\r\n  async getTenders(params?: { status?: string; category?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/procurement/tenders', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getTender(id: string) {\r\n    const response = await this.api.get(`/procurement/tenders/${id}`);\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async payForTenderAccess(tenderId: string, paymentData: TenderPaymentData) {\r\n    const response = await this.api.post(`/procurement/tenders/${tenderId}/pay-access`, paymentData);\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async downloadTenderDocument(documentId: string) {\r\n    const response = await this.api.get(`/procurement/documents/${documentId}/download`, {\r\n      responseType: 'blob',\r\n    });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async getMyBids(params?: { status?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/procurement/my-bids', { params });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async getBid(id: string) {\r\n    const response = await this.api.get(`/procurement/bids/${id}`);\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async submitBid(formData: FormData) {\r\n    const response = await this.api.post('/procurement/bids', formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async updateBid(id: string, formData: FormData) {\r\n    const response = await this.api.put(`/procurement/bids/${id}`, formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async getProcurementPayments(params?: { status?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/procurement/payments', { params });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async getProcurementPayment(id: string) {\r\n    const response = await this.api.get(`/procurement/payments/${id}`);\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  // Consumer Affairs endpoints\r\n  async getComplaints(params?: { status?: string; category?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/consumer-affairs/complaints', { params });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async getComplaint(id: string) {\r\n    const response = await this.api.get(`/consumer-affairs/complaints/${id}`);\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async submitComplaint(complaintData: ComplaintData) {\r\n    const formData = new FormData();\r\n    formData.append('title', complaintData.title);\r\n    formData.append('description', complaintData.description);\r\n    formData.append('category', complaintData.category);\r\n\r\n    if (complaintData.attachments) {\r\n      complaintData.attachments.forEach((file, index) => {\r\n        formData.append(`attachments[${index}]`, file);\r\n      });\r\n    }\r\n\r\n    const response = await this.api.post('/consumer-affairs/complaints', formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async updateComplaint(id: string, updates: Partial<ComplaintData>) {\r\n    const response = await this.api.put(`/consumer-affairs/complaints/${id}`, updates);\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async downloadComplaintAttachment(complaintId: string, attachmentId: string) {\r\n    const response = await this.api.get(`/consumer-affairs/complaints/${complaintId}/attachments/${attachmentId}/download`, {\r\n      responseType: 'blob',\r\n    });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const customerApi = new CustomerApiService();\r\n\r\n// Export axios instance for direct use if needed\r\nexport { customerApiClient };\r\n\r\n// Export types\r\nexport interface ApiResponse<T = unknown> {\r\n  success: boolean;\r\n  data: T;\r\n  message?: string;\r\n  errors?: string[] | { [key: string]: string[] } | string;\r\n}\r\n\r\nexport interface PaginatedResponse<T = unknown> {\r\n  data: T[];\r\n  total: number;\r\n  page: number;\r\n  limit: number;\r\n  totalPages: number;\r\n}\r\n\r\nexport interface License {\r\n  id: string;\r\n  licenseNumber: string;\r\n  type: string;\r\n  status: 'active' | 'expired' | 'suspended' | 'pending';\r\n  issueDate: string;\r\n  expirationDate: string;\r\n  organizationName: string;\r\n  description?: string;\r\n}\r\n\r\nexport interface Application {\r\n  id: string;\r\n  applicationNumber: string;\r\n  type: string;\r\n  status: 'draft' | 'submitted' | 'under_review' | 'approved' | 'rejected';\r\n  submittedDate: string;\r\n  lastUpdated: string;\r\n  organizationName: string;\r\n  description?: string;\r\n}\r\n\r\nexport interface Payment {\r\n  id: string;\r\n  invoiceNumber: string;\r\n  amount: number;\r\n  currency: string;\r\n  status: 'pending' | 'paid' | 'overdue' | 'cancelled';\r\n  dueDate: string;\r\n  paidDate?: string;\r\n  issueDate: string;\r\n  description: string;\r\n  paymentType: string;\r\n  clientName: string;\r\n  clientEmail: string;\r\n  paymentMethod?: string;\r\n  notes?: string;\r\n  relatedLicense?: string;\r\n  relatedApplication?: string;\r\n}\r\n\r\nexport interface User {\r\n  id: string;\r\n  firstName: string;\r\n  lastName: string;\r\n  email: string;\r\n  organizationName?: string;\r\n  roles: string[];\r\n  isAdmin: boolean;\r\n  profileImage?: string;\r\n  createdAt: string;\r\n  lastLogin?: string;\r\n  phone?: string;\r\n  address?: string;\r\n  city?: string;\r\n  country?: string;\r\n  two_factor_enabled?: boolean;\r\n}\r\n\r\nexport interface ProfileUpdateData {\r\n  firstName?: string;\r\n  lastName?: string;\r\n  email?: string;\r\n  organizationName?: string;\r\n  profileImage?: string;\r\n}\r\n\r\nexport interface CreateAddressData {\r\n  address_type: string;\r\n  entity_type?: string;\r\n  entity_id?:string;\r\n  address_line_1: string;\r\n  address_line_2?: string;\r\n  postal_code: string;\r\n  country: string;\r\n  city: string;\r\n}\r\n\r\nexport interface EditAddressData {\r\n  address_id: string;\r\n  address_type?: string;\r\n  address_line_1?: string;\r\n  address_line_2?: string;\r\n  postal_code?: string;\r\n  country?: string;\r\n  city?: string;\r\n}\r\n\r\nexport interface SearchPostcodes {\r\n  region?: string;\r\n  district?: string;\r\n  location?: string;\r\n  postal_code?: string;\r\n}\r\n\r\nexport interface PostalCodeLookupResult {\r\n  postal_code_id: string;\r\n  region: string;\r\n  district: string;\r\n  location: string;\r\n  postal_code: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  deleted_at?: string | null;\r\n}\r\n\r\n\r\nexport interface LicenseApplicationData {\r\n  type: string;\r\n  organizationName: string;\r\n  description?: string;\r\n  contactEmail?: string;\r\n  contactPhone?: string;\r\n  businessAddress?: string;\r\n  businessType?: string;\r\n  requestedStartDate?: string;\r\n  additionalDocuments?: string[];\r\n  notes?: string;\r\n}\r\n\r\nexport interface PaymentCreateData {\r\n  amount: number;\r\n  currency: string;\r\n  dueDate: string;\r\n  issueDate: string;\r\n  description: string;\r\n  paymentType: string;\r\n  clientName: string;\r\n  clientEmail: string;\r\n  paymentMethod?: string;\r\n  notes?: string;\r\n  relatedLicense?: string;\r\n  relatedApplication?: string;\r\n}\r\n\r\nexport interface TenderPaymentData {\r\n  amount: number;\r\n  currency: string;\r\n  paymentMethod: string;\r\n  description?: string;\r\n}\r\n\r\nexport interface ComplaintData {\r\n  title: string;\r\n  description: string;\r\n  category: string;\r\n  attachments?: File[];\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAEA,oBAAoB;AACpB,MAAM,eAAe,QAAQ,GAAG,CAAC,mBAAmB,IAAI;AAExD,mEAAmE;AACnE,MAAM,oBAAmC,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACpD,SAAS;IACT,SAAS;IACT,SAAS;QACP,gBAAgB;QAChB,UAAU;IACZ;AACF;AAEA,qDAAqD;AACrD,MAAM,wBAAuC,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACxD,SAAS,GAAG,aAAa,KAAK,CAAC;IAC/B,SAAS;IACT,SAAS;QACP,gBAAgB;QAChB,UAAU;IACZ;AACF;AAEA,yDAAyD;AACzD,sBAAsB,YAAY,CAAC,OAAO,CAAC,GAAG,CAC5C,CAAC;IACC,wCAA4C;QAC1C,QAAQ,GAAG,CAAC,8BAA8B;YACxC,KAAK,GAAG,OAAO,OAAO,GAAG,OAAO,GAAG,EAAE;YACrC,QAAQ,OAAO,MAAM;YACrB,SAAS,OAAO,OAAO;YACvB,MAAM,OAAO,IAAI;QACnB;IACF;IACA,OAAO;AACT,GACA,CAAC;IACC,QAAQ,KAAK,CAAC,oCAAoC;IAClD,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,sBAAsB,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC7C,CAAC;IACC,wCAA4C;QAC1C,QAAQ,GAAG,CAAC,uCAAuC;YACjD,QAAQ,SAAS,MAAM;YACvB,YAAY,SAAS,UAAU;YAC/B,KAAK,SAAS,MAAM,CAAC,GAAG;QAC1B;IACF;IACA,OAAO;AACT,GACA,CAAC;IACC,wCAA4C;QAC1C,QAAQ,KAAK,CAAC,wCAAwC;YACpD,SAAS,OAAO,WAAW;YAC3B,MAAM,OAAO,QAAQ;YACrB,QAAQ,OAAO,UAAU,UAAU;YACnC,YAAY,OAAO,UAAU,cAAc;YAC3C,KAAK,OAAO,QAAQ,OAAO;YAC3B,QAAQ,OAAO,QAAQ,UAAU;YACjC,SAAS,OAAO,QAAQ,WAAW;YACnC,cAAc,OAAO,gBAAgB;YACrC,cAAc,OAAO,UAAU,QAAQ;YACvC,aAAa,OAAO,QAAQ,QAAQ;YACpC,SAAS,OAAO,QAAQ,WAAW;QACrC;IACF;IAEA,wDAAwD;IACxD,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,wCAAwC;AACxC,kBAAkB,YAAY,CAAC,OAAO,CAAC,GAAG,CACxC,CAAC;IACC,MAAM,QAAQ,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;IAC1B,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,2DAA2D;AAC3D,kBAAkB,YAAY,CAAC,QAAQ,CAAC,GAAG,CACzC,CAAC;IACC,OAAO;AACT,GACA,OAAO;IACL,MAAM,kBAAkB,MAAM,MAAM;IAKpC,2BAA2B;IAC3B,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;QAClC,IAAI,CAAC,gBAAgB,MAAM,EAAE;YAC3B,gBAAgB,MAAM,GAAG;YAEzB,0DAA0D;YAC1D,MAAM,aAAa,MAAM,QAAQ,CAAC,OAAO,CAAC,cAAc;YACxD,MAAM,QAAQ,aAAa,SAAS,cAAc,OAAO,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,CAAC,GAAG,gBAAgB,WAAW,IAAI,IAAI;YAExH,gBAAgB,WAAW,GAAG,CAAC,gBAAgB,WAAW,IAAI,CAAC,IAAI;YAEnE,gCAAgC;YAChC,IAAI,gBAAgB,WAAW,IAAI,GAAG;gBACpC,QAAQ,IAAI,CAAC,CAAC,6BAA6B,EAAE,MAAM,YAAY,EAAE,gBAAgB,WAAW,CAAC,CAAC,CAAC;gBAE/F,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACjD,OAAO,kBAAkB;YAC3B;QACF;IACF;IAEA,0BAA0B;IAC1B,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;QAClC,yCAAyC;QACzC,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACf,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACf,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAIK,MAAM;IACJ,IAAmB;IAClB,kBAAiD,IAAI,MAAM;IAEnE,aAAc;QACZ,IAAI,CAAC,GAAG,GAAG;IACb;IAEA,+BAA+B;IAC/B,MAAc,mBAAsB,GAAW,EAAE,SAA2B,EAAc;QACxF,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM;YACjC,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;QAClC;QAEA,MAAM,UAAU,YAAY,OAAO,CAAC;YAClC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;QAC9B;QAEA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK;QAC9B,OAAO;IACT;IAEA,iBAAiB;IACjB,aAAa,KAAa,EAAE;QAC1B,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;IACvE;IAEA,oBAAoB;IACpB,kBAAkB;QAChB,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB;IAC1D;IAEA,MAAM,SAAS;QACb,MAAM,WAAW,MAAM,sBAAsB,IAAI,CAAC;QAClD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,eAAe;QACnB,MAAM,WAAW,MAAM,sBAAsB,IAAI,CAAC;QAClD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gBAAgB;IAChB,MAAM,sBAAsB,MAAc,EAAE,MAAc,EAAE;QAC1D,MAAM,WAAW,MAAM,sBAAsB,IAAI,CAAC,iBAAiB;YAAE,SAAS;YAAQ;QAAO;QAC7F,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,UAAU,IAAuD,EAAE;QACvE,MAAM,WAAW,MAAM,sBAAsB,IAAI,CAAC,eAAe;QAEjE,oDAAoD;QACpD,IAAI,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW,MAAM;YACtC,MAAM,WAAW,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU,IAAI;YAElD,sDAAsD;YACtD,MAAM,iBAAiB;gBACrB,cAAc,SAAS,YAAY;gBACnC,MAAM;oBACJ,IAAI,SAAS,IAAI,CAAC,OAAO;oBACzB,WAAW,SAAS,IAAI,CAAC,UAAU;oBACnC,UAAU,SAAS,IAAI,CAAC,SAAS;oBACjC,OAAO,SAAS,IAAI,CAAC,KAAK;oBAC1B,OAAO,SAAS,IAAI,CAAC,KAAK,IAAI,EAAE;oBAChC,SAAS,CAAC,SAAS,IAAI,CAAC,KAAK,IAAI,EAAE,EAAE,QAAQ,CAAC;oBAC9C,cAAc,SAAS,IAAI,CAAC,aAAa;oBACzC,WAAW,SAAS,IAAI,CAAC,UAAU,IAAI,IAAI,OAAO,WAAW;oBAC7D,WAAW,SAAS,IAAI,CAAC,UAAU;oBACnC,kBAAkB,SAAS,IAAI,CAAC,iBAAiB;oBACjD,oBAAoB,SAAS,IAAI,CAAC,kBAAkB;gBACtD;YACF;YAEA,OAAO;QACT;QAEA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,mBAAmB,IAA+C,EAAE;QACxE,MAAM,WAAW,MAAM,sBAAsB,IAAI,CAAC,cAAc;QAChE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,yBAAyB;IACzB,MAAM,aAAa;QACjB,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc;YAC3C,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;YACpC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B;IACF;IAEA,MAAM,cAAc,WAA8B,EAAE;QAClD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,kBAAkB;QACtD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,uBAAuB;IACvB,MAAM,eAAe;QACnB,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;QACpC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,cAAc,WAA8B,EAAE;QAClD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,mBAAmB;QACxD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,WAAW,EAAU,EAAE;QAC3B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI;QACpD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,YAAY,WAA4B,EAAE;QAC9C,MAAM,EAAE,UAAU,EAAE,GAAG,YAAY,GAAG;QACtC,IAAI,CAAC,YAAY;YACf,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,YAAY,EAAE;QAC9D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,qBAAqB,UAAkB,EAAE,QAAgB,EAAE;QAC/D,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,yBAAyB,EAAE,mBAAmB,YAAY,WAAW,EAAE,mBAAmB,WAAW;QAC1I,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,cAAc,EAAU,EAAE;QAC9B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,cAAc,EAAE,IAAI;QAC5D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,gBAAgB,YAA6B,EAAE;QACnD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,wBAAwB;QAC7D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,oBAAoB;IACpB,MAAM,YAAY,MAA2D,EAAE;QAC7E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,aAAa;YAAE;QAAO;QAC1D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,WAAW,EAAU,EAAE;QAC3B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI;QACrD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,yBAAyB,eAAuC,EAAE;QACtE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,yBAAyB;QAC9D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,0BAA0B;IAC1B,MAAM,gBAAgB,MAA0C,EAAE;QAChE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,kBAAkB;YAAE;QAAO;QAC/D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,eAAe,EAAU,EAAE;QAC/B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,IAAI;QAC1D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,+BAA+B;IAC/B,MAAM,qBAAqB,MAA0C,EAAE;QACrE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,uBAAuB;YAAE;QAAO;QACpE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,2BAA2B,aAAqB,EAAE;QACtD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,oCAAoC,EAAE,eAAe;QAC1F,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,uBAAuB,aAAqB,EAAE;QAClD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,KAAK,CAAC;QAC5F,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,mBAAmB,EAAU,EAAE;QACnC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,IAAI;QAC/D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,wBAAwB;IACxB,MAAM,gBAAgB,MAA2D,EAAE;QACjF,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,iBAAiB;YAAE;QAAO;QAC9D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,eAAe,EAAU,EAAE;QAC/B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI;QACzD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,kBAAkB,eAAoB,EAAE;QAC5C,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB;QACtD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,kBAAkB,EAAU,EAAE,eAAgD,EAAE;QACpF,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI,EAAE;QAC3D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,oBAAoB;IACpB,MAAM,YAAY,MAA2D,EAAE;QAC7E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,aAAa;YAAE;QAAO;QAC1D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,WAAW,EAAU,EAAE;QAC3B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI;QACrD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,cAAc,WAA8B,EAAE;QAClD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa;QAClD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,qBAAqB;IACrB,MAAM,aAAa,MAAyD,EAAE;QAC5E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,cAAc;YAAE;QAAO;QAC3D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,eAAe,QAAkB,EAAE;QACvC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,qBAAqB,UAAU;YAClE,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,iBAAiB,EAAU,EAAE;QACjC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,GAAG,SAAS,CAAC,EAAE;YAC/D,cAAc;QAChB;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,uBAAuB;IACvB,MAAM,oBAAoB;QACxB,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;QACpC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gBAAgB;IAChB,MAAM,iBAAiB,MAA0D,EAAE;QACjF,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,kBAAkB;YAAE;QAAO;QAC/D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,uBAAuB,EAAU,EAAE;QACvC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,eAAe,EAAE,GAAG,KAAK,CAAC;QACjE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,wBAAwB;IACxB,MAAM,WAAW,MAA8E,EAAE;QAC/F,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,wBAAwB;YAAE;QAAO;QACrE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,UAAU,EAAU,EAAE;QAC1B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,IAAI;QAChE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,mBAAmB,QAAgB,EAAE,WAA8B,EAAE;QACzE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,qBAAqB,EAAE,SAAS,WAAW,CAAC,EAAE;QACpF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,uBAAuB,UAAkB,EAAE;QAC/C,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,WAAW,SAAS,CAAC,EAAE;YACnF,cAAc;QAChB;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,UAAU,MAA2D,EAAE;QAC3E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,wBAAwB;YAAE;QAAO;QACrE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,OAAO,EAAU,EAAE;QACvB,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,IAAI;QAC7D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,UAAU,QAAkB,EAAE;QAClC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,qBAAqB,UAAU;YAClE,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,UAAU,EAAU,EAAE,QAAkB,EAAE;QAC9C,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,IAAI,EAAE,UAAU;YACvE,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,uBAAuB,MAA2D,EAAE;QACxF,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,yBAAyB;YAAE;QAAO;QACtE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,sBAAsB,EAAU,EAAE;QACtC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,sBAAsB,EAAE,IAAI;QACjE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,6BAA6B;IAC7B,MAAM,cAAc,MAA8E,EAAE;QAClG,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,gCAAgC;YAAE;QAAO;QAC7E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,aAAa,EAAU,EAAE;QAC7B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,IAAI;QACxE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,gBAAgB,aAA4B,EAAE;QAClD,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS,cAAc,KAAK;QAC5C,SAAS,MAAM,CAAC,eAAe,cAAc,WAAW;QACxD,SAAS,MAAM,CAAC,YAAY,cAAc,QAAQ;QAElD,IAAI,cAAc,WAAW,EAAE;YAC7B,cAAc,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM;gBACvC,SAAS,MAAM,CAAC,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC,EAAE;YAC3C;QACF;QAEA,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,gCAAgC,UAAU;YAC7E,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,gBAAgB,EAAU,EAAE,OAA+B,EAAE;QACjE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,IAAI,EAAE;QAC1E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,4BAA4B,WAAmB,EAAE,YAAoB,EAAE;QAC3E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,YAAY,aAAa,EAAE,aAAa,SAAS,CAAC,EAAE;YACtH,cAAc;QAChB;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;AACF;AAGO,MAAM,cAAc,IAAI", "debugId": null}}, {"offset": {"line": 1773, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/applications/ApplicationProgress.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\r\nimport { useRouter, useSearchParams, usePathname } from 'next/navigation';\r\nimport {\r\n  getLicenseTypeStepConfig,\r\n  getOptimizedStepConfig,\r\n  getStepsByLicenseTypeCode,\r\n  isLicenseTypeCodeSupported,\r\n  StepConfig\r\n} from '@/config/licenseTypeStepConfig';\r\nimport { CustomerApiService } from '@/lib/customer-api';\r\n\r\n// Cache for license data to avoid repeated API calls\r\nconst licenseDataCache = new Map<string, {\r\n  category: any;\r\n  licenseType: any;\r\n  steps: StepConfig[];\r\n  timestamp: number;\r\n}>();\r\n\r\nconst CACHE_DURATION = 5 * 60 * 1000; // 5 minutes\r\n\r\ninterface ApplicationProgressProps {\r\n  className?: string;\r\n}\r\n\r\nconst ApplicationProgress: React.FC<ApplicationProgressProps> = ({ className = '' }) => {\r\n  const router = useRouter();\r\n  const searchParams = useSearchParams();\r\n  const pathname = usePathname();\r\n\r\n  // Create customer API service instance\r\n  const customerApi = useMemo(() => new CustomerApiService(), []);\r\n\r\n  // Get query parameters\r\n  const licenseCategoryId = searchParams.get('license_category_id');\r\n  const applicationId = searchParams.get('application_id');\r\n\r\n  // State\r\n  const [applicationSteps, setApplicationSteps] = useState<StepConfig[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  // Get current step from pathname (memoized)\r\n  const currentStepIndex = useMemo(() => {\r\n    if (!applicationSteps.length) return -1;\r\n    const pathSegments = pathname.split('/');\r\n    const currentStepId = pathSegments[pathSegments.length - 1];\r\n    return applicationSteps.findIndex(step => step.id === currentStepId);\r\n  }, [pathname, applicationSteps]);\r\n\r\n  // Check cache for license data\r\n  const getCachedLicenseData = useCallback((licenseCategoryId: string) => {\r\n    const cached = licenseDataCache.get(licenseCategoryId);\r\n    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {\r\n      return cached;\r\n    }\r\n    return null;\r\n  }, []);\r\n\r\n  // Cache license data\r\n  const cacheLicenseData = useCallback((licenseCategoryId: string, data: any) => {\r\n    licenseDataCache.set(licenseCategoryId, {\r\n      ...data,\r\n      timestamp: Date.now()\r\n    });\r\n  }, []);\r\n\r\n  // Load data with caching and optimization\r\n  useEffect(() => {\r\n    const loadData = async () => {\r\n      try {\r\n        if (!licenseCategoryId) {\r\n          setLoading(false);\r\n          return;\r\n        }\r\n\r\n        setError(null);\r\n\r\n        // Check cache first\r\n        const cachedData = getCachedLicenseData(licenseCategoryId);\r\n        if (cachedData) {\r\n          console.log('Using cached license data for:', licenseCategoryId);\r\n          setApplicationSteps(cachedData.steps);\r\n          setLoading(false);\r\n          return;\r\n        }\r\n\r\n        console.log('Fetching license data for:', licenseCategoryId);\r\n\r\n        // Load license category\r\n        const category = await customerApi.getLicenseCategory(licenseCategoryId);\r\n        if (!category?.license_type_id) {\r\n          throw new Error('License category does not have a license type ID');\r\n        }\r\n\r\n        const licenseType = await customerApi.getLicenseType(category.license_type_id);\r\n        if (!licenseType) {\r\n          throw new Error('License type not found');\r\n        }\r\n\r\n        console.log('🔍 License type loaded:', {\r\n          id: licenseType.license_type_id,\r\n          name: licenseType.name,\r\n          code: licenseType.code\r\n        });\r\n\r\n        // Use optimized step configuration\r\n        let steps: StepConfig[] = [];\r\n\r\n        if (licenseType.code && isLicenseTypeCodeSupported(licenseType.code)) {\r\n          console.log('✅ Using optimized config for supported license type:', licenseType.code);\r\n          steps = getStepsByLicenseTypeCode(licenseType.code);\r\n        } else {\r\n          console.log('⚠️ Using fallback config for license type:', licenseType.code || 'unknown');\r\n          const config = getLicenseTypeStepConfig(licenseType.code || licenseType.license_type_id);\r\n          steps = config.steps;\r\n        }\r\n\r\n        // Cache the data\r\n        cacheLicenseData(licenseCategoryId, {\r\n          category,\r\n          licenseType,\r\n          steps\r\n        });\r\n\r\n        setApplicationSteps(steps);\r\n        setLoading(false);\r\n\r\n      } catch (err: any) {\r\n        console.error('Error loading application steps:', err);\r\n        setError(err.message || 'Failed to load license information');\r\n        setApplicationSteps([]);\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    loadData();\r\n  }, [licenseCategoryId, applicationId, customerApi, getCachedLicenseData, cacheLicenseData]);\r\n\r\n  // Navigation handlers\r\n  const handleStepClick = (stepIndex: number) => {\r\n    // Prevent navigation to future steps if not editing an existing application\r\n    if (!applicationId && stepIndex > currentStepIndex) {\r\n      return;\r\n    }\r\n\r\n    const step = applicationSteps[stepIndex];\r\n    const params = new URLSearchParams();\r\n    params.set('license_category_id', licenseCategoryId!);\r\n    if (applicationId) {\r\n      params.set('application_id', applicationId);\r\n    }\r\n    router.push(`/customer/applications/apply/${step.id}?${params.toString()}`);\r\n  };\r\n\r\n  // Loading state\r\n  if (loading) {\r\n    return (\r\n      <div className={`mb-8 ${className}`}>\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4\">\r\n          <div className=\"animate-pulse\">\r\n            <div className=\"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4\"></div>\r\n            <div className=\"space-y-2\">\r\n              {[...Array(4)].map((_, i) => (\r\n                <div key={i} className=\"flex items-center p-2\">\r\n                  <div className=\"w-6 h-6 bg-gray-200 dark:bg-gray-700 rounded-full mr-3\"></div>\r\n                  <div className=\"flex-1\">\r\n                    <div className=\"h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3 mb-1\"></div>\r\n                    <div className=\"h-2 bg-gray-200 dark:bg-gray-700 rounded w-1/2\"></div>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Error state\r\n  if (error) {\r\n    return (\r\n      <div className={`mb-8 ${className}`}>\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-red-200 dark:border-red-800 p-4\">\r\n          <div className=\"flex items-center text-red-600 dark:text-red-400\">\r\n            <i className=\"ri-error-warning-line mr-2\"></i>\r\n            <span className=\"text-sm font-medium\">Failed to load progress</span>\r\n          </div>\r\n          <p className=\"text-xs text-red-500 dark:text-red-400 mt-1\">{error}</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // No steps available\r\n  if (!applicationSteps.length) {\r\n    return (\r\n      <div className={`mb-8 ${className}`}>\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4\">\r\n          <div className=\"text-center text-gray-500 dark:text-gray-400\">\r\n            <i className=\"ri-file-list-line text-2xl mb-2\"></i>\r\n            <p className=\"text-sm\">No application steps available</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className={`mb-8 ${className}`}>\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4\">\r\n        <h3 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-4\">\r\n          Application Progress ({currentStepIndex + 1} of {applicationSteps.length})\r\n        </h3>\r\n        <div className=\"space-y-2\">\r\n          {applicationSteps.map((step, index) => {\r\n            const isAccessible = applicationId || index <= currentStepIndex;\r\n            return (\r\n              <div\r\n                key={step.id}\r\n                className={`flex items-center p-2 rounded-md transition-colors ${\r\n                  isAccessible ? 'cursor-pointer' : 'cursor-not-allowed opacity-60'\r\n                } ${\r\n                  index === currentStepIndex\r\n                    ? 'bg-primary/10 border border-primary/20'\r\n                    : index < currentStepIndex\r\n                    ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800'\r\n                    : 'bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600'\r\n                }`}\r\n                onClick={() => isAccessible && handleStepClick(index)}\r\n              >\r\n                <div\r\n                  className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium mr-3 ${\r\n                    index === currentStepIndex\r\n                      ? 'bg-primary text-white'\r\n                      : index < currentStepIndex\r\n                      ? 'bg-green-500 text-white'\r\n                      : 'bg-gray-300 text-gray-600 dark:bg-gray-600 dark:text-gray-300'\r\n                  }`}\r\n                >\r\n                  {index < currentStepIndex ? (\r\n                    <i className=\"ri-check-line\"></i>\r\n                  ) : (\r\n                    index + 1\r\n                  )}\r\n                </div>\r\n                <div className=\"flex-1\">\r\n                  <div className={`text-sm font-medium ${\r\n                    index === currentStepIndex\r\n                      ? 'text-primary'\r\n                      : index < currentStepIndex\r\n                      ? 'text-green-700 dark:text-green-300'\r\n                      : 'text-gray-600 dark:text-gray-400'\r\n                  }`}>\r\n                    {step.name}\r\n                  </div>\r\n                  {step.description && (\r\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                      {step.description}\r\n                    </div>\r\n                  )}\r\n                </div>\r\n                {step.required && (\r\n                  <span className=\"text-xs text-red-500 ml-2\">*</span>\r\n                )}\r\n              </div>\r\n            );\r\n          })}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ApplicationProgress;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAOA;AAXA;;;;;;AAaA,qDAAqD;AACrD,MAAM,mBAAmB,IAAI;AAO7B,MAAM,iBAAiB,IAAI,KAAK,MAAM,YAAY;AAMlD,MAAM,sBAA0D,CAAC,EAAE,YAAY,EAAE,EAAE;IACjF,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,uCAAuC;IACvC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,IAAI,6HAAA,CAAA,qBAAkB,IAAI,EAAE;IAE9D,uBAAuB;IACvB,MAAM,oBAAoB,aAAa,GAAG,CAAC;IAC3C,MAAM,gBAAgB,aAAa,GAAG,CAAC;IAEvC,QAAQ;IACR,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACzE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,4CAA4C;IAC5C,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC/B,IAAI,CAAC,iBAAiB,MAAM,EAAE,OAAO,CAAC;QACtC,MAAM,eAAe,SAAS,KAAK,CAAC;QACpC,MAAM,gBAAgB,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE;QAC3D,OAAO,iBAAiB,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IACxD,GAAG;QAAC;QAAU;KAAiB;IAE/B,+BAA+B;IAC/B,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACxC,MAAM,SAAS,iBAAiB,GAAG,CAAC;QACpC,IAAI,UAAU,KAAK,GAAG,KAAK,OAAO,SAAS,GAAG,gBAAgB;YAC5D,OAAO;QACT;QACA,OAAO;IACT,GAAG,EAAE;IAEL,qBAAqB;IACrB,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,mBAA2B;QAC/D,iBAAiB,GAAG,CAAC,mBAAmB;YACtC,GAAG,IAAI;YACP,WAAW,KAAK,GAAG;QACrB;IACF,GAAG,EAAE;IAEL,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW;YACf,IAAI;gBACF,IAAI,CAAC,mBAAmB;oBACtB,WAAW;oBACX;gBACF;gBAEA,SAAS;gBAET,oBAAoB;gBACpB,MAAM,aAAa,qBAAqB;gBACxC,IAAI,YAAY;oBACd,QAAQ,GAAG,CAAC,kCAAkC;oBAC9C,oBAAoB,WAAW,KAAK;oBACpC,WAAW;oBACX;gBACF;gBAEA,QAAQ,GAAG,CAAC,8BAA8B;gBAE1C,wBAAwB;gBACxB,MAAM,WAAW,MAAM,YAAY,kBAAkB,CAAC;gBACtD,IAAI,CAAC,UAAU,iBAAiB;oBAC9B,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,cAAc,MAAM,YAAY,cAAc,CAAC,SAAS,eAAe;gBAC7E,IAAI,CAAC,aAAa;oBAChB,MAAM,IAAI,MAAM;gBAClB;gBAEA,QAAQ,GAAG,CAAC,2BAA2B;oBACrC,IAAI,YAAY,eAAe;oBAC/B,MAAM,YAAY,IAAI;oBACtB,MAAM,YAAY,IAAI;gBACxB;gBAEA,mCAAmC;gBACnC,IAAI,QAAsB,EAAE;gBAE5B,IAAI,YAAY,IAAI,IAAI,CAAA,GAAA,sIAAA,CAAA,6BAA0B,AAAD,EAAE,YAAY,IAAI,GAAG;oBACpE,QAAQ,GAAG,CAAC,wDAAwD,YAAY,IAAI;oBACpF,QAAQ,CAAA,GAAA,sIAAA,CAAA,4BAAyB,AAAD,EAAE,YAAY,IAAI;gBACpD,OAAO;oBACL,QAAQ,GAAG,CAAC,8CAA8C,YAAY,IAAI,IAAI;oBAC9E,MAAM,SAAS,CAAA,GAAA,sIAAA,CAAA,2BAAwB,AAAD,EAAE,YAAY,IAAI,IAAI,YAAY,eAAe;oBACvF,QAAQ,OAAO,KAAK;gBACtB;gBAEA,iBAAiB;gBACjB,iBAAiB,mBAAmB;oBAClC;oBACA;oBACA;gBACF;gBAEA,oBAAoB;gBACpB,WAAW;YAEb,EAAE,OAAO,KAAU;gBACjB,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,SAAS,IAAI,OAAO,IAAI;gBACxB,oBAAoB,EAAE;gBACtB,WAAW;YACb;QACF;QAEA;IACF,GAAG;QAAC;QAAmB;QAAe;QAAa;QAAsB;KAAiB;IAE1F,sBAAsB;IACtB,MAAM,kBAAkB,CAAC;QACvB,4EAA4E;QAC5E,IAAI,CAAC,iBAAiB,YAAY,kBAAkB;YAClD;QACF;QAEA,MAAM,OAAO,gBAAgB,CAAC,UAAU;QACxC,MAAM,SAAS,IAAI;QACnB,OAAO,GAAG,CAAC,uBAAuB;QAClC,IAAI,eAAe;YACjB,OAAO,GAAG,CAAC,kBAAkB;QAC/B;QACA,OAAO,IAAI,CAAC,CAAC,6BAA6B,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI;IAC5E;IAEA,gBAAgB;IAChB,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAW,CAAC,KAAK,EAAE,WAAW;sBACjC,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;sCACZ;mCAAI,MAAM;6BAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;oCAAY,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;mCAJT;;;;;;;;;;;;;;;;;;;;;;;;;;IAaxB;IAEA,cAAc;IACd,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAW,CAAC,KAAK,EAAE,WAAW;sBACjC,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAsB;;;;;;;;;;;;kCAExC,8OAAC;wBAAE,WAAU;kCAA+C;;;;;;;;;;;;;;;;;IAIpE;IAEA,qBAAqB;IACrB,IAAI,CAAC,iBAAiB,MAAM,EAAE;QAC5B,qBACE,8OAAC;YAAI,WAAW,CAAC,KAAK,EAAE,WAAW;sBACjC,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;;;;;sCACb,8OAAC;4BAAE,WAAU;sCAAU;;;;;;;;;;;;;;;;;;;;;;IAKjC;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,KAAK,EAAE,WAAW;kBACjC,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;;wBAA4D;wBACjD,mBAAmB;wBAAE;wBAAK,iBAAiB,MAAM;wBAAC;;;;;;;8BAE3E,8OAAC;oBAAI,WAAU;8BACZ,iBAAiB,GAAG,CAAC,CAAC,MAAM;wBAC3B,MAAM,eAAe,iBAAiB,SAAS;wBAC/C,qBACE,8OAAC;4BAEC,WAAW,CAAC,mDAAmD,EAC7D,eAAe,mBAAmB,gCACnC,CAAC,EACA,UAAU,mBACN,2CACA,QAAQ,mBACR,mFACA,8EACJ;4BACF,SAAS,IAAM,gBAAgB,gBAAgB;;8CAE/C,8OAAC;oCACC,WAAW,CAAC,+EAA+E,EACzF,UAAU,mBACN,0BACA,QAAQ,mBACR,4BACA,iEACJ;8CAED,QAAQ,iCACP,8OAAC;wCAAE,WAAU;;;;;+CAEb,QAAQ;;;;;;8CAGZ,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAW,CAAC,oBAAoB,EACnC,UAAU,mBACN,iBACA,QAAQ,mBACR,uCACA,oCACJ;sDACC,KAAK,IAAI;;;;;;wCAEX,KAAK,WAAW,kBACf,8OAAC;4CAAI,WAAU;sDACZ,KAAK,WAAW;;;;;;;;;;;;gCAItB,KAAK,QAAQ,kBACZ,8OAAC;oCAAK,WAAU;8CAA4B;;;;;;;2BA5CzC,KAAK,EAAE;;;;;oBAgDlB;;;;;;;;;;;;;;;;;AAKV;uCAEe", "debugId": null}}, {"offset": {"line": 2185, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/applications/ApplicationLayout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { Suspense } from 'react';\r\nimport ApplicationProgress from './ApplicationProgress';\r\n\r\ninterface ApplicationLayoutProps {\r\n  children: React.ReactNode;\r\n  onSubmit?: () => void;\r\n  onSave?: () => void;\r\n  onNext?: () => void;\r\n  onPrevious?: () => void;\r\n  isSubmitting?: boolean;\r\n  isSaving?: boolean;\r\n  showNextButton?: boolean;\r\n  showPreviousButton?: boolean;\r\n  showSaveButton?: boolean;\r\n  showSubmitButton?: boolean;\r\n  nextButtonText?: string;\r\n  previousButtonText?: string;\r\n  saveButtonText?: string;\r\n  submitButtonText?: string;\r\n  nextButtonDisabled?: boolean;\r\n  previousButtonDisabled?: boolean;\r\n  saveButtonDisabled?: boolean;\r\n  submitButtonDisabled?: boolean;\r\n  className?: string;\r\n  showProgress?: boolean; // Allow disabling progress for better performance\r\n  progressFallback?: React.ReactNode; // Custom loading fallback\r\n\r\n  // Enhanced props for optimized step configuration\r\n  licenseTypeCode?: string; // For step validation and navigation\r\n  currentStepRoute?: string; // Current step identifier\r\n  stepValidationErrors?: string[]; // Step-specific validation errors\r\n  showStepInfo?: boolean; // Show step information and requirements\r\n}\r\n\r\n// Progress Loading Fallback Component\r\nconst ProgressLoadingFallback: React.FC = () => (\r\n  <div className=\"mb-8\">\r\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4\">\r\n      <div className=\"animate-pulse\">\r\n        <div className=\"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4\"></div>\r\n        <div className=\"space-y-2\">\r\n          {[...Array(4)].map((_, i) => (\r\n            <div key={i} className=\"flex items-center p-2\">\r\n              <div className=\"w-6 h-6 bg-gray-200 dark:bg-gray-700 rounded-full mr-3\"></div>\r\n              <div className=\"flex-1\">\r\n                <div className=\"h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3 mb-1\"></div>\r\n                <div className=\"h-2 bg-gray-200 dark:bg-gray-700 rounded w-1/2\"></div>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n);\r\n\r\nconst ApplicationLayout: React.FC<ApplicationLayoutProps> = ({\r\n  children,\r\n  onSubmit,\r\n  onSave,\r\n  onNext,\r\n  onPrevious,\r\n  isSubmitting = false,\r\n  isSaving = false,\r\n  showNextButton = true,\r\n  showPreviousButton = true,\r\n  showSaveButton = false,\r\n  showSubmitButton = false,\r\n  nextButtonText = 'Next',\r\n  previousButtonText = 'Previous',\r\n  saveButtonText = 'Save',\r\n  submitButtonText = 'Submit',\r\n  nextButtonDisabled = false,\r\n  previousButtonDisabled = false,\r\n  saveButtonDisabled = false,\r\n  submitButtonDisabled = false,\r\n  className = '',\r\n  showProgress = true,\r\n  progressFallback,\r\n  licenseTypeCode,\r\n  currentStepRoute,\r\n  stepValidationErrors = [],\r\n  showStepInfo = false\r\n}) => {\r\n  return (\r\n    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 ${className}`}>\r\n      <div className=\"max-w-7xl mx-auto\">\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8\">\r\n          {/* Progress Steps - Left Sidebar */}\r\n          {showProgress && (\r\n            <div className=\"lg:col-span-1\">\r\n              <div className=\"sticky top-8\">\r\n                <Suspense fallback={progressFallback || <ProgressLoadingFallback />}>\r\n                  <ApplicationProgress />\r\n                </Suspense>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Main Content Area */}\r\n          <div className={showProgress ? \"lg:col-span-3\" : \"lg:col-span-4\"}>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\">\r\n              {/* Step Information Banner */}\r\n              {showStepInfo && licenseTypeCode && currentStepRoute && (\r\n                <div className=\"border-b border-gray-200 dark:border-gray-700 p-4 bg-blue-50 dark:bg-blue-900/20\">\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <div>\r\n                      <h3 className=\"text-sm font-medium text-blue-900 dark:text-blue-100\">\r\n                        License Type: {licenseTypeCode.replace(/_/g, ' ').toUpperCase()}\r\n                      </h3>\r\n                      <p className=\"text-xs text-blue-700 dark:text-blue-300 mt-1\">\r\n                        Current Step: {currentStepRoute.replace(/-/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase())}\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"text-xs text-blue-600 dark:text-blue-400\">\r\n                      <i className=\"ri-information-line mr-1\"></i>\r\n                      Optimized Configuration Active\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {/* Validation Errors */}\r\n              {stepValidationErrors.length > 0 && (\r\n                <div className=\"border-b border-gray-200 dark:border-gray-700 p-4 bg-red-50 dark:bg-red-900/20\">\r\n                  <div className=\"flex items-start\">\r\n                    <i className=\"ri-error-warning-line text-red-500 mr-2 mt-0.5\"></i>\r\n                    <div>\r\n                      <h4 className=\"text-sm font-medium text-red-900 dark:text-red-100 mb-2\">\r\n                        Please fix the following issues:\r\n                      </h4>\r\n                      <ul className=\"text-xs text-red-700 dark:text-red-300 space-y-1\">\r\n                        {stepValidationErrors.map((error, index) => (\r\n                          <li key={index}>• {error}</li>\r\n                        ))}\r\n                      </ul>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {/* Form Content */}\r\n              <div className=\"p-6\">\r\n                {children}\r\n              </div>\r\n\r\n              {/* Footer with Action Buttons */}\r\n              <div className=\"px-6 py-4 bg-gray-50 dark:bg-gray-700/50 border-t border-gray-200 dark:border-gray-600 rounded-b-lg\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  {/* Left Side - Previous Button */}\r\n                  <div>\r\n                    {showPreviousButton && onPrevious && (\r\n                      <button\r\n                        type=\"button\"\r\n                        onClick={onPrevious}\r\n                        disabled={previousButtonDisabled}\r\n                        className=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                      >\r\n                        <i className=\"ri-arrow-left-line mr-2\"></i>\r\n                        {previousButtonText}\r\n                      </button>\r\n                    )}\r\n                  </div>\r\n\r\n                  {/* Right Side - Action Buttons */}\r\n                  <div className=\"flex items-center space-x-3\">\r\n                    {/* Save Button */}\r\n                    {showSaveButton && onSave && (\r\n                      <button\r\n                        type=\"button\"\r\n                        onClick={() => {\r\n                          console.log('🔘 Save button clicked in ApplicationLayout');\r\n                          onSave();\r\n                        }}\r\n                        disabled={saveButtonDisabled || isSaving}\r\n                        className=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                      >\r\n                        {isSaving ? (\r\n                          <>\r\n                            <i className=\"ri-loader-4-line animate-spin mr-2\"></i>\r\n                            Saving...\r\n                          </>\r\n                        ) : (\r\n                          <>\r\n                            <i className=\"ri-save-line mr-2\"></i>\r\n                            {saveButtonText}\r\n                          </>\r\n                        )}\r\n                      </button>\r\n                    )}\r\n\r\n                    {/* Submit Button */}\r\n                    {showSubmitButton && onSubmit && (\r\n                      <button\r\n                        type=\"button\"\r\n                        onClick={onSubmit}\r\n                        disabled={submitButtonDisabled || isSubmitting}\r\n                        className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                      >\r\n                        {isSubmitting ? (\r\n                          <>\r\n                            <i className=\"ri-loader-4-line animate-spin mr-2\"></i>\r\n                            Submitting...\r\n                          </>\r\n                        ) : (\r\n                          <>\r\n                            <i className=\"ri-send-plane-line mr-2\"></i>\r\n                            {submitButtonText}\r\n                          </>\r\n                        )}\r\n                      </button>\r\n                    )}\r\n\r\n                    {/* Next Button */}\r\n                    {showNextButton && onNext && (\r\n                      <button\r\n                        type=\"button\"\r\n                        onClick={() => {\r\n                          console.log('🔘 Next button clicked in ApplicationLayout');\r\n                          onNext();\r\n                        }}\r\n                        disabled={nextButtonDisabled}\r\n                        className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                      >\r\n                        {nextButtonText}\r\n                        <i className=\"ri-arrow-right-line ml-2\"></i>\r\n                      </button>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ApplicationLayout;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAoCA,sCAAsC;AACtC,MAAM,0BAAoC,kBACxC,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;gCAAY,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;+BAJT;;;;;;;;;;;;;;;;;;;;;;;;;;AActB,MAAM,oBAAsD,CAAC,EAC3D,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,MAAM,EACN,UAAU,EACV,eAAe,KAAK,EACpB,WAAW,KAAK,EAChB,iBAAiB,IAAI,EACrB,qBAAqB,IAAI,EACzB,iBAAiB,KAAK,EACtB,mBAAmB,KAAK,EACxB,iBAAiB,MAAM,EACvB,qBAAqB,UAAU,EAC/B,iBAAiB,MAAM,EACvB,mBAAmB,QAAQ,EAC3B,qBAAqB,KAAK,EAC1B,yBAAyB,KAAK,EAC9B,qBAAqB,KAAK,EAC1B,uBAAuB,KAAK,EAC5B,YAAY,EAAE,EACd,eAAe,IAAI,EACnB,gBAAgB,EAChB,eAAe,EACf,gBAAgB,EAChB,uBAAuB,EAAE,EACzB,eAAe,KAAK,EACrB;IACC,qBACE,8OAAC;QAAI,WAAW,CAAC,yCAAyC,EAAE,WAAW;kBACrE,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;oBAEZ,8BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,qMAAA,CAAA,WAAQ;gCAAC,UAAU,kCAAoB,8OAAC;;;;;0CACvC,cAAA,8OAAC,yJAAA,CAAA,UAAmB;;;;;;;;;;;;;;;;;;;;kCAO5B,8OAAC;wBAAI,WAAW,eAAe,kBAAkB;kCAC/C,cAAA,8OAAC;4BAAI,WAAU;;gCAEZ,gBAAgB,mBAAmB,kCAClC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;;4DAAuD;4DACpD,gBAAgB,OAAO,CAAC,MAAM,KAAK,WAAW;;;;;;;kEAE/D,8OAAC;wDAAE,WAAU;;4DAAgD;4DAC5C,iBAAiB,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;;;;;;;;;;;;;0DAG1F,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;;;;;;oDAA+B;;;;;;;;;;;;;;;;;;gCAQnD,qBAAqB,MAAM,GAAG,mBAC7B,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;;;;;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA0D;;;;;;kEAGxE,8OAAC;wDAAG,WAAU;kEACX,qBAAqB,GAAG,CAAC,CAAC,OAAO,sBAChC,8OAAC;;oEAAe;oEAAG;;+DAAV;;;;;;;;;;;;;;;;;;;;;;;;;;;8CASrB,8OAAC;oCAAI,WAAU;8CACZ;;;;;;8CAIH,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;0DACE,sBAAsB,4BACrB,8OAAC;oDACC,MAAK;oDACL,SAAS;oDACT,UAAU;oDACV,WAAU;;sEAEV,8OAAC;4DAAE,WAAU;;;;;;wDACZ;;;;;;;;;;;;0DAMP,8OAAC;gDAAI,WAAU;;oDAEZ,kBAAkB,wBACjB,8OAAC;wDACC,MAAK;wDACL,SAAS;4DACP,QAAQ,GAAG,CAAC;4DACZ;wDACF;wDACA,UAAU,sBAAsB;wDAChC,WAAU;kEAET,yBACC;;8EACE,8OAAC;oEAAE,WAAU;;;;;;gEAAyC;;yFAIxD;;8EACE,8OAAC;oEAAE,WAAU;;;;;;gEACZ;;;;;;;;oDAOR,oBAAoB,0BACnB,8OAAC;wDACC,MAAK;wDACL,SAAS;wDACT,UAAU,wBAAwB;wDAClC,WAAU;kEAET,6BACC;;8EACE,8OAAC;oEAAE,WAAU;;;;;;gEAAyC;;yFAIxD;;8EACE,8OAAC;oEAAE,WAAU;;;;;;gEACZ;;;;;;;;oDAOR,kBAAkB,wBACjB,8OAAC;wDACC,MAAK;wDACL,SAAS;4DACP,QAAQ,GAAG,CAAC;4DACZ;wDACF;wDACA,UAAU;wDACV,WAAU;;4DAET;0EACD,8OAAC;gEAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrC;uCAEe", "debugId": null}}, {"offset": {"line": 2622, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/forms/FileUpload.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useRef } from 'react';\r\n\r\ninterface FileUploadProps {\r\n  id: string;\r\n  label: string;\r\n  accept?: string;\r\n  maxSize?: number; // in MB\r\n  required?: boolean;\r\n  value?: File | null;\r\n  onChange: (file: File | null) => void;\r\n  description?: string;\r\n  className?: string;\r\n}\r\n\r\nconst FileUpload: React.FC<FileUploadProps> = ({\r\n  id,\r\n  label,\r\n  accept = '.pdf',\r\n  maxSize = 10,\r\n  required = false,\r\n  value,\r\n  onChange,\r\n  description,\r\n  className = ''\r\n}) => {\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n    const file = event.target.files?.[0] || null;\r\n    \r\n    if (file) {\r\n      // Check file size\r\n      if (file.size > maxSize * 1024 * 1024) {\r\n        alert(`File size must be less than ${maxSize}MB`);\r\n        return;\r\n      }\r\n      \r\n      // Check file type\r\n      if (accept && !accept.split(',').some(type => file.name.toLowerCase().endsWith(type.trim().replace('*', '')))) {\r\n        alert(`File type must be: ${accept}`);\r\n        return;\r\n      }\r\n    }\r\n    \r\n    onChange(file);\r\n  };\r\n\r\n  const handleClick = () => {\r\n    fileInputRef.current?.click();\r\n  };\r\n\r\n  const handleRemove = (e: React.MouseEvent) => {\r\n    e.stopPropagation();\r\n    onChange(null);\r\n    if (fileInputRef.current) {\r\n      fileInputRef.current.value = '';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={className}>\r\n      <label htmlFor={id} className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n        {label} {required && <span className=\"text-red-500\">*</span>}\r\n      </label>\r\n      \r\n      <div\r\n        onClick={handleClick}\r\n        className=\"border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-3 text-center cursor-pointer hover:border-primary hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors\"\r\n      >\r\n        <input\r\n          ref={fileInputRef}\r\n          type=\"file\"\r\n          accept={accept}\r\n          onChange={handleFileChange}\r\n          className=\"hidden\"\r\n          id={id}\r\n          required={required}\r\n        />\r\n        \r\n        {value ? (\r\n          <div className=\"space-y-1\">\r\n            <div className=\"flex items-center justify-center\">\r\n              <i className=\"ri-file-text-line text-2xl text-green-500\"></i>\r\n            </div>\r\n            <p className=\"text-sm font-medium text-green-600 dark:text-green-400\">\r\n              {value.name}\r\n            </p>\r\n            <p className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n              {(value.size / 1024 / 1024).toFixed(2)} MB\r\n            </p>\r\n            <button\r\n              type=\"button\"\r\n              onClick={handleRemove}\r\n              className=\"inline-flex items-center px-3 py-1 bg-red-100 text-red-700 hover:bg-red-200 rounded-md text-xs font-medium transition-colors\"\r\n            >\r\n              <i className=\"ri-delete-bin-line mr-1\"></i>\r\n              Remove\r\n            </button>\r\n          </div>\r\n        ) : (\r\n          <div className=\"space-y-1\">\r\n            <div className=\"flex items-center justify-center\">\r\n              <i className=\"ri-upload-cloud-2-line text-2xl text-gray-400\"></i>\r\n            </div>\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              Click to upload {label.toLowerCase()}\r\n            </p>\r\n            {description && (\r\n              <p className=\"text-xs text-gray-500 dark:text-gray-500\">\r\n                {description}\r\n              </p>\r\n            )}\r\n            <div className=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors\">\r\n              <i className=\"ri-folder-upload-line mr-2\"></i>\r\n              Choose File\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n      \r\n      {description && !value && (\r\n        <p className=\"mt-2 text-xs text-gray-500 dark:text-gray-400\">\r\n          {description}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FileUpload;\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAgBA,MAAM,aAAwC,CAAC,EAC7C,EAAE,EACF,KAAK,EACL,SAAS,MAAM,EACf,UAAU,EAAE,EACZ,WAAW,KAAK,EAChB,KAAK,EACL,QAAQ,EACR,WAAW,EACX,YAAY,EAAE,EACf;IACC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI;QAExC,IAAI,MAAM;YACR,kBAAkB;YAClB,IAAI,KAAK,IAAI,GAAG,UAAU,OAAO,MAAM;gBACrC,MAAM,CAAC,4BAA4B,EAAE,QAAQ,EAAE,CAAC;gBAChD;YACF;YAEA,kBAAkB;YAClB,IAAI,UAAU,CAAC,OAAO,KAAK,CAAC,KAAK,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,KAAK,IAAI,GAAG,OAAO,CAAC,KAAK,OAAO;gBAC7G,MAAM,CAAC,mBAAmB,EAAE,QAAQ;gBACpC;YACF;QACF;QAEA,SAAS;IACX;IAEA,MAAM,cAAc;QAClB,aAAa,OAAO,EAAE;IACxB;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,eAAe;QACjB,SAAS;QACT,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK,GAAG;QAC/B;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW;;0BACd,8OAAC;gBAAM,SAAS;gBAAI,WAAU;;oBAC3B;oBAAM;oBAAE,0BAAY,8OAAC;wBAAK,WAAU;kCAAe;;;;;;;;;;;;0BAGtD,8OAAC;gBACC,SAAS;gBACT,WAAU;;kCAEV,8OAAC;wBACC,KAAK;wBACL,MAAK;wBACL,QAAQ;wBACR,UAAU;wBACV,WAAU;wBACV,IAAI;wBACJ,UAAU;;;;;;oBAGX,sBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;;;;;;;;;;0CAEf,8OAAC;gCAAE,WAAU;0CACV,MAAM,IAAI;;;;;;0CAEb,8OAAC;gCAAE,WAAU;;oCACV,CAAC,MAAM,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;oCAAG;;;;;;;0CAEzC,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;;kDAEV,8OAAC;wCAAE,WAAU;;;;;;oCAA8B;;;;;;;;;;;;6CAK/C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;;;;;;;;;;0CAEf,8OAAC;gCAAE,WAAU;;oCAA2C;oCACrC,MAAM,WAAW;;;;;;;4BAEnC,6BACC,8OAAC;gCAAE,WAAU;0CACV;;;;;;0CAGL,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;;;;;;oCAAiC;;;;;;;;;;;;;;;;;;;YAOrD,eAAe,CAAC,uBACf,8OAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;uCAEe", "debugId": null}}, {"offset": {"line": 2845, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/forms/FormField.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\n\r\ninterface FormFieldProps {\r\n  label: string;\r\n  required?: boolean;\r\n  error?: string;\r\n  children: React.ReactNode;\r\n  className?: string;\r\n  description?: string;\r\n}\r\n\r\nconst FormField: React.FC<FormFieldProps> = ({\r\n  label,\r\n  required = false,\r\n  error,\r\n  children,\r\n  className = '',\r\n  description\r\n}) => {\r\n  return (\r\n    <div className={className}>\r\n      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n        {label} {required && <span className=\"text-red-500\">*</span>}\r\n      </label>\r\n      \r\n      {description && (\r\n        <p className=\"text-xs text-gray-500 dark:text-gray-400 mb-2\">\r\n          {description}\r\n        </p>\r\n      )}\r\n      \r\n      {children}\r\n      \r\n      {error && (\r\n        <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\r\n          <i className=\"ri-error-warning-line mr-1\"></i>\r\n          {error}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FormField;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAaA,MAAM,YAAsC,CAAC,EAC3C,KAAK,EACL,WAAW,KAAK,EAChB,KAAK,EACL,QAAQ,EACR,YAAY,EAAE,EACd,WAAW,EACZ;IACC,qBACE,8OAAC;QAAI,WAAW;;0BACd,8OAAC;gBAAM,WAAU;;oBACd;oBAAM;oBAAE,0BAAY,8OAAC;wBAAK,WAAU;kCAAe;;;;;;;;;;;;YAGrD,6BACC,8OAAC;gBAAE,WAAU;0BACV;;;;;;YAIJ;YAEA,uBACC,8OAAC;gBAAE,WAAU;;kCACX,8OAAC;wBAAE,WAAU;;;;;;oBACZ;;;;;;;;;;;;;AAKX;uCAEe", "debugId": null}}, {"offset": {"line": 2914, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/forms/ProgressIndicator.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\n\r\ninterface Step {\r\n  id: number;\r\n  label: string;\r\n  completed?: boolean;\r\n}\r\n\r\ninterface ProgressIndicatorProps {\r\n  steps: Step[];\r\n  currentStep: number;\r\n  className?: string;\r\n}\r\n\r\nconst ProgressIndicator: React.FC<ProgressIndicatorProps> = ({\r\n  steps,\r\n  currentStep,\r\n  className = ''\r\n}) => {\r\n  return (\r\n    <div className={className}>\r\n      <div className=\"flex items-center justify-between\">\r\n        {steps.map((step, index) => (\r\n          <div key={step.id} className=\"flex items-center\">\r\n            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-colors ${\r\n              step.id === currentStep\r\n                ? 'bg-primary text-white'\r\n                : step.id < currentStep || step.completed\r\n                ? 'bg-green-500 text-white'\r\n                : 'bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400'\r\n            }`}>\r\n              {step.id < currentStep || step.completed ? (\r\n                <i className=\"ri-check-line\"></i>\r\n              ) : (\r\n                step.id\r\n              )}\r\n            </div>\r\n            {index < steps.length - 1 && (\r\n              <div className={`w-12 h-0.5 mx-2 transition-colors ${\r\n                step.id < currentStep || step.completed ? 'bg-green-500' : 'bg-gray-200 dark:bg-gray-700'\r\n              }`}></div>\r\n            )}\r\n          </div>\r\n        ))}\r\n      </div>\r\n      \r\n      <div className=\"flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-2\">\r\n        {steps.map((step) => (\r\n          <span key={step.id} className=\"text-center max-w-20 truncate\">\r\n            {step.label}\r\n          </span>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProgressIndicator;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAgBA,MAAM,oBAAsD,CAAC,EAC3D,KAAK,EACL,WAAW,EACX,YAAY,EAAE,EACf;IACC,qBACE,8OAAC;QAAI,WAAW;;0BACd,8OAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;wBAAkB,WAAU;;0CAC3B,8OAAC;gCAAI,WAAW,CAAC,4FAA4F,EAC3G,KAAK,EAAE,KAAK,cACR,0BACA,KAAK,EAAE,GAAG,eAAe,KAAK,SAAS,GACvC,4BACA,iEACJ;0CACC,KAAK,EAAE,GAAG,eAAe,KAAK,SAAS,iBACtC,8OAAC;oCAAE,WAAU;;;;;2CAEb,KAAK,EAAE;;;;;;4BAGV,QAAQ,MAAM,MAAM,GAAG,mBACtB,8OAAC;gCAAI,WAAW,CAAC,kCAAkC,EACjD,KAAK,EAAE,GAAG,eAAe,KAAK,SAAS,GAAG,iBAAiB,gCAC3D;;;;;;;uBAjBI,KAAK,EAAE;;;;;;;;;;0BAuBrB,8OAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;wBAAmB,WAAU;kCAC3B,KAAK,KAAK;uBADF,KAAK,EAAE;;;;;;;;;;;;;;;;AAO5B;uCAEe", "debugId": null}}, {"offset": {"line": 2990, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/forms/CountryDropdown.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useRef } from 'react';\r\n\r\n// Country list for nationality dropdown\r\nconst COUNTRIES = [\r\n  'Afghanistan', 'Albania', 'Algeria', 'Andorra', 'Angola', 'Antigua and Barbuda', 'Argentina', 'Armenia', 'Australia', 'Austria',\r\n  'Azerbaijan', 'Bahamas', 'Bahrain', 'Bangladesh', 'Barbados', 'Belarus', 'Belgium', 'Belize', 'Benin', 'Bhutan',\r\n  'Bolivia', 'Bosnia and Herzegovina', 'Botswana', 'Brazil', 'Brunei', 'Bulgaria', 'Burkina Faso', 'Burundi', 'Cabo Verde', 'Cambodia',\r\n  'Cameroon', 'Canada', 'Central African Republic', 'Chad', 'Chile', 'China', 'Colombia', 'Comoros', 'Congo (Congo-Brazzaville)', 'Congo (Democratic Republic)',\r\n  'Costa Rica', 'Croatia', 'Cuba', 'Cyprus', 'Czechia (Czech Republic)', 'Denmark', 'Djibouti', 'Dominica', 'Dominican Republic', 'Ecuador',\r\n  'Egypt', 'El Salvador', 'Equatorial Guinea', 'Eritrea', 'Estonia', '<PERSON><PERSON><PERSON><PERSON>', 'Ethiopia', 'Fiji', 'Finland', 'France',\r\n  'Gabon', 'Gambia', 'Georgia', 'Germany', 'Ghana', 'Greece', 'Grenada', 'Guatemala', 'Guinea', 'Guinea-Bissau',\r\n  'Guyana', 'Haiti', 'Holy See', 'Honduras', 'Hungary', 'Iceland', 'India', 'Indonesia', 'Iran', 'Iraq',\r\n  'Ireland', 'Israel', 'Italy', 'Jamaica', 'Japan', 'Jordan', 'Kazakhstan', 'Kenya', 'Kiribati', 'Kuwait',\r\n  'Kyrgyzstan', 'Laos', 'Latvia', 'Lebanon', 'Lesotho', 'Liberia', 'Libya', 'Liechtenstein', 'Lithuania', 'Luxembourg',\r\n  'Madagascar', 'Malawi', 'Malaysia', 'Maldives', 'Mali', 'Malta', 'Marshall Islands', 'Mauritania', 'Mauritius', 'Mexico',\r\n  'Micronesia', 'Moldova', 'Monaco', 'Mongolia', 'Montenegro', 'Morocco', 'Mozambique', 'Myanmar (formerly Burma)', 'Namibia', 'Nauru',\r\n  'Nepal', 'Netherlands', 'New Zealand', 'Nicaragua', 'Niger', 'Nigeria', 'North Korea', 'North Macedonia', 'Norway', 'Oman',\r\n  'Pakistan', 'Palau', 'Palestine State', 'Panama', 'Papua New Guinea', 'Paraguay', 'Peru', 'Philippines', 'Poland', 'Portugal',\r\n  'Qatar', 'Romania', 'Russia', 'Rwanda', 'Saint Kitts and Nevis', 'Saint Lucia', 'Saint Vincent and the Grenadines', 'Samoa', 'San Marino', 'Sao Tome and Principe',\r\n  'Saudi Arabia', 'Senegal', 'Serbia', 'Seychelles', 'Sierra Leone', 'Singapore', 'Slovakia', 'Slovenia', 'Solomon Islands', 'Somalia',\r\n  'South Africa', 'South Korea', 'South Sudan', 'Spain', 'Sri Lanka', 'Sudan', 'Suriname', 'Sweden', 'Switzerland', 'Syria',\r\n  'Tajikistan', 'Tanzania', 'Thailand', 'Timor-Leste', 'Togo', 'Tonga', 'Trinidad and Tobago', 'Tunisia', 'Turkey', 'Turkmenistan',\r\n  'Tuvalu', 'Uganda', 'Ukraine', 'United Arab Emirates', 'United Kingdom', 'United States of America', 'Uruguay', 'Uzbekistan', 'Vanuatu', 'Venezuela',\r\n  'Vietnam', 'Yemen', 'Zambia', 'Zimbabwe'\r\n];\r\n\r\ninterface CountryDropdownProps {\r\n  label?: string;\r\n  value: string;\r\n  onChange: (value: string) => void;\r\n  placeholder?: string;\r\n  required?: boolean;\r\n  className?: string;\r\n  disabled?: boolean;\r\n  error?: string;\r\n  id?: string;\r\n  name?: string;\r\n}\r\n\r\nconst CountryDropdown: React.FC<CountryDropdownProps> = ({ \r\n  label,\r\n  value, \r\n  onChange, \r\n  placeholder = \"Select or type country name\", \r\n  required = false,\r\n  className = \"\",\r\n  disabled = false,\r\n  error = \"\",\r\n  id,\r\n  name\r\n}) => {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [filteredCountries, setFilteredCountries] = useState(COUNTRIES);\r\n  const [activeIndex, setActiveIndex] = useState(-1);\r\n  const dropdownRef = useRef<HTMLDivElement>(null);\r\n  const inputRef = useRef<HTMLInputElement>(null);\r\n  const listboxId = `${id || 'country-dropdown'}-listbox`;\r\n\r\n  // Filter countries based on search term\r\n  useEffect(() => {\r\n    const filtered = COUNTRIES.filter(country =>\r\n      country.toLowerCase().includes(searchTerm.toLowerCase())\r\n    );\r\n    setFilteredCountries(filtered);\r\n    setActiveIndex(-1); // Reset active index when filtering\r\n  }, [searchTerm]);\r\n\r\n  // Close dropdown when clicking outside\r\n  useEffect(() => {\r\n    const handleClickOutside = (event: MouseEvent) => {\r\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\r\n        setIsOpen(false);\r\n        setActiveIndex(-1);\r\n      }\r\n    };\r\n\r\n    document.addEventListener('mousedown', handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener('mousedown', handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const inputValue = e.target.value;\r\n    setSearchTerm(inputValue);\r\n    onChange(inputValue);\r\n    if (!disabled) {\r\n      setIsOpen(true);\r\n    }\r\n  };\r\n\r\n  const handleCountrySelect = (country: string) => {\r\n    onChange(country);\r\n    setSearchTerm(country);\r\n    setIsOpen(false);\r\n    setActiveIndex(-1);\r\n  };\r\n\r\n  const handleInputFocus = () => {\r\n    if (!disabled) {\r\n      setIsOpen(true);\r\n      setSearchTerm(value);\r\n    }\r\n  };\r\n\r\n  const handleInputBlur = () => {\r\n    // Delay hiding dropdown to allow for country selection\r\n    setTimeout(() => {\r\n      setIsOpen(false);\r\n      setActiveIndex(-1);\r\n    }, 150);\r\n  };\r\n\r\n  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {\r\n    if (e.key === 'Escape') {\r\n      setIsOpen(false);\r\n      setActiveIndex(-1);\r\n      inputRef.current?.blur();\r\n    } else if (e.key === 'ArrowDown') {\r\n      e.preventDefault();\r\n      if (!isOpen) {\r\n        setIsOpen(true);\r\n      }\r\n      setActiveIndex(prev =>\r\n        prev < filteredCountries.length - 1 ? prev + 1 : 0\r\n      );\r\n    } else if (e.key === 'ArrowUp') {\r\n      e.preventDefault();\r\n      if (!isOpen) {\r\n        setIsOpen(true);\r\n      }\r\n      setActiveIndex(prev =>\r\n        prev > 0 ? prev - 1 : filteredCountries.length - 1\r\n      );\r\n    } else if (e.key === 'Enter') {\r\n      e.preventDefault();\r\n      if (isOpen && activeIndex >= 0 && filteredCountries[activeIndex]) {\r\n        handleCountrySelect(filteredCountries[activeIndex]);\r\n      }\r\n    } else if (e.key === 'Tab') {\r\n      // Allow tab to close dropdown and move to next element\r\n      setIsOpen(false);\r\n      setActiveIndex(-1);\r\n    }\r\n  };\r\n\r\n  // Base input styling with proper text visibility\r\n  const baseInputClass = `w-full px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 ${\r\n    className.includes('text-sm') ? 'py-1.5 text-sm' : 'py-2'\r\n  }`;\r\n  \r\n  // Error and disabled states\r\n  const inputClass = `${baseInputClass} ${\r\n    error \r\n      ? \"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500\" \r\n      : \"border-gray-300 dark:border-gray-600\"\r\n  } ${\r\n    disabled \r\n      ? \"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800\" \r\n      : \"\"\r\n  }`;\r\n\r\n  const labelClass = \"block mb-1 font-medium text-gray-700 dark:text-gray-200\";\r\n\r\n  return (\r\n    <div className={`relative ${className}`} ref={dropdownRef}>\r\n      {label && (\r\n        <label className={labelClass}>\r\n          {label}\r\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\r\n        </label>\r\n      )}\r\n      <input\r\n        ref={inputRef}\r\n        type=\"text\"\r\n        id={id}\r\n        name={name}\r\n        value={isOpen ? searchTerm : value}\r\n        onChange={handleInputChange}\r\n        onFocus={handleInputFocus}\r\n        onBlur={handleInputBlur}\r\n        onKeyDown={handleKeyDown}\r\n        className={inputClass}\r\n        placeholder={placeholder}\r\n        required={required}\r\n        disabled={disabled}\r\n        autoComplete=\"country\"\r\n        aria-expanded={isOpen}\r\n        aria-haspopup=\"listbox\"\r\n        aria-controls={isOpen ? listboxId : undefined}\r\n        aria-activedescendant={activeIndex >= 0 && isOpen ? `${listboxId}-option-${activeIndex}` : undefined}\r\n        role=\"combobox\"\r\n        aria-autocomplete=\"list\"\r\n        aria-describedby={error ? `${id}-error` : undefined}\r\n      />\r\n      \r\n      {/* Dropdown icon */}\r\n      <div className={`absolute inset-y-0 right-0 top-7 flex items-center pr-3 pointer-events-none ${disabled ? 'opacity-50' : ''}`}>\r\n        <i className={`ri-arrow-down-s-line text-gray-400 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}></i>\r\n      </div>\r\n\r\n      {/* Dropdown list */}\r\n      {isOpen && !disabled && (\r\n        <div \r\n          id={listboxId}\r\n          className=\"absolute z-50 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-y-auto\"\r\n          role=\"listbox\"\r\n          aria-label=\"Country options\"\r\n        >\r\n          {filteredCountries.length > 0 ? (\r\n            filteredCountries.map((country, index) => (\r\n              <div\r\n                key={country}\r\n                id={`${listboxId}-option-${index}`}\r\n                className={`px-3 py-2 cursor-pointer text-sm transition-colors duration-150 ${\r\n                  index === activeIndex\r\n                    ? 'font-semibold text-red-600 dark:text-red-300 bg-gray-100 dark:bg-gray-800'\r\n                    : 'font-normal text-gray-900 dark:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-600'\r\n                }`}\r\n                onClick={() => handleCountrySelect(country)}\r\n                onMouseDown={(e) => e.preventDefault()} // Prevent input blur\r\n                onMouseEnter={() => setActiveIndex(index)}\r\n                role=\"option\"\r\n                aria-selected={value === country}\r\n              >\r\n                  {country}\r\n              </div>\r\n            ))\r\n          ) : (\r\n            <div className=\"px-3 py-2 text-sm text-gray-500 dark:text-gray-400\" role=\"option\" aria-disabled=\"true\" aria-selected=\"false\">\r\n              No countries found\r\n            </div>\r\n          )}\r\n        </div>\r\n      )}\r\n\r\n      {/* Error message */}\r\n      {error && (\r\n        <p id={`${id}-error`} className=\"mt-1 text-sm text-red-600 dark:text-red-400\" role=\"alert\">\r\n          {error}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CountryDropdown;"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,wCAAwC;AACxC,MAAM,YAAY;IAChB;IAAe;IAAW;IAAW;IAAW;IAAU;IAAuB;IAAa;IAAW;IAAa;IACtH;IAAc;IAAW;IAAW;IAAc;IAAY;IAAW;IAAW;IAAU;IAAS;IACvG;IAAW;IAA0B;IAAY;IAAU;IAAU;IAAY;IAAgB;IAAW;IAAc;IAC1H;IAAY;IAAU;IAA4B;IAAQ;IAAS;IAAS;IAAY;IAAW;IAA6B;IAChI;IAAc;IAAW;IAAQ;IAAU;IAA4B;IAAW;IAAY;IAAY;IAAsB;IAChI;IAAS;IAAe;IAAqB;IAAW;IAAW;IAAY;IAAY;IAAQ;IAAW;IAC9G;IAAS;IAAU;IAAW;IAAW;IAAS;IAAU;IAAW;IAAa;IAAU;IAC9F;IAAU;IAAS;IAAY;IAAY;IAAW;IAAW;IAAS;IAAa;IAAQ;IAC/F;IAAW;IAAU;IAAS;IAAW;IAAS;IAAU;IAAc;IAAS;IAAY;IAC/F;IAAc;IAAQ;IAAU;IAAW;IAAW;IAAW;IAAS;IAAiB;IAAa;IACxG;IAAc;IAAU;IAAY;IAAY;IAAQ;IAAS;IAAoB;IAAc;IAAa;IAChH;IAAc;IAAW;IAAU;IAAY;IAAc;IAAW;IAAc;IAA4B;IAAW;IAC7H;IAAS;IAAe;IAAe;IAAa;IAAS;IAAW;IAAe;IAAmB;IAAU;IACpH;IAAY;IAAS;IAAmB;IAAU;IAAoB;IAAY;IAAQ;IAAe;IAAU;IACnH;IAAS;IAAW;IAAU;IAAU;IAAyB;IAAe;IAAoC;IAAS;IAAc;IAC3I;IAAgB;IAAW;IAAU;IAAc;IAAgB;IAAa;IAAY;IAAY;IAAmB;IAC3H;IAAgB;IAAe;IAAe;IAAS;IAAa;IAAS;IAAY;IAAU;IAAe;IAClH;IAAc;IAAY;IAAY;IAAe;IAAQ;IAAS;IAAuB;IAAW;IAAU;IAClH;IAAU;IAAU;IAAW;IAAwB;IAAkB;IAA4B;IAAW;IAAc;IAAW;IACzI;IAAW;IAAS;IAAU;CAC/B;AAeD,MAAM,kBAAkD,CAAC,EACvD,KAAK,EACL,KAAK,EACL,QAAQ,EACR,cAAc,6BAA6B,EAC3C,WAAW,KAAK,EAChB,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,QAAQ,EAAE,EACV,EAAE,EACF,IAAI,EACL;IACC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAChD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,YAAY,GAAG,MAAM,mBAAmB,QAAQ,CAAC;IAEvD,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,UAAU,MAAM,CAAC,CAAA,UAChC,QAAQ,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAEvD,qBAAqB;QACrB,eAAe,CAAC,IAAI,oCAAoC;IAC1D,GAAG;QAAC;KAAW;IAEf,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E,UAAU;gBACV,eAAe,CAAC;YAClB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG,EAAE;IAEL,MAAM,oBAAoB,CAAC;QACzB,MAAM,aAAa,EAAE,MAAM,CAAC,KAAK;QACjC,cAAc;QACd,SAAS;QACT,IAAI,CAAC,UAAU;YACb,UAAU;QACZ;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,SAAS;QACT,cAAc;QACd,UAAU;QACV,eAAe,CAAC;IAClB;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,UAAU;YACb,UAAU;YACV,cAAc;QAChB;IACF;IAEA,MAAM,kBAAkB;QACtB,uDAAuD;QACvD,WAAW;YACT,UAAU;YACV,eAAe,CAAC;QAClB,GAAG;IACL;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,UAAU;YACtB,UAAU;YACV,eAAe,CAAC;YAChB,SAAS,OAAO,EAAE;QACpB,OAAO,IAAI,EAAE,GAAG,KAAK,aAAa;YAChC,EAAE,cAAc;YAChB,IAAI,CAAC,QAAQ;gBACX,UAAU;YACZ;YACA,eAAe,CAAA,OACb,OAAO,kBAAkB,MAAM,GAAG,IAAI,OAAO,IAAI;QAErD,OAAO,IAAI,EAAE,GAAG,KAAK,WAAW;YAC9B,EAAE,cAAc;YAChB,IAAI,CAAC,QAAQ;gBACX,UAAU;YACZ;YACA,eAAe,CAAA,OACb,OAAO,IAAI,OAAO,IAAI,kBAAkB,MAAM,GAAG;QAErD,OAAO,IAAI,EAAE,GAAG,KAAK,SAAS;YAC5B,EAAE,cAAc;YAChB,IAAI,UAAU,eAAe,KAAK,iBAAiB,CAAC,YAAY,EAAE;gBAChE,oBAAoB,iBAAiB,CAAC,YAAY;YACpD;QACF,OAAO,IAAI,EAAE,GAAG,KAAK,OAAO;YAC1B,uDAAuD;YACvD,UAAU;YACV,eAAe,CAAC;QAClB;IACF;IAEA,iDAAiD;IACjD,MAAM,iBAAiB,CAAC,yPAAyP,EAC/Q,UAAU,QAAQ,CAAC,aAAa,mBAAmB,QACnD;IAEF,4BAA4B;IAC5B,MAAM,aAAa,GAAG,eAAe,CAAC,EACpC,QACI,+EACA,uCACL,CAAC,EACA,WACI,8DACA,IACJ;IAEF,MAAM,aAAa;IAEnB,qBACE,8OAAC;QAAI,WAAW,CAAC,SAAS,EAAE,WAAW;QAAE,KAAK;;YAC3C,uBACC,8OAAC;gBAAM,WAAW;;oBACf;oBACA,0BAAY,8OAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAGrD,8OAAC;gBACC,KAAK;gBACL,MAAK;gBACL,IAAI;gBACJ,MAAM;gBACN,OAAO,SAAS,aAAa;gBAC7B,UAAU;gBACV,SAAS;gBACT,QAAQ;gBACR,WAAW;gBACX,WAAW;gBACX,aAAa;gBACb,UAAU;gBACV,UAAU;gBACV,cAAa;gBACb,iBAAe;gBACf,iBAAc;gBACd,iBAAe,SAAS,YAAY;gBACpC,yBAAuB,eAAe,KAAK,SAAS,GAAG,UAAU,QAAQ,EAAE,aAAa,GAAG;gBAC3F,MAAK;gBACL,qBAAkB;gBAClB,oBAAkB,QAAQ,GAAG,GAAG,MAAM,CAAC,GAAG;;;;;;0BAI5C,8OAAC;gBAAI,WAAW,CAAC,4EAA4E,EAAE,WAAW,eAAe,IAAI;0BAC3H,cAAA,8OAAC;oBAAE,WAAW,CAAC,qEAAqE,EAAE,SAAS,eAAe,IAAI;;;;;;;;;;;YAInH,UAAU,CAAC,0BACV,8OAAC;gBACC,IAAI;gBACJ,WAAU;gBACV,MAAK;gBACL,cAAW;0BAEV,kBAAkB,MAAM,GAAG,IAC1B,kBAAkB,GAAG,CAAC,CAAC,SAAS,sBAC9B,8OAAC;wBAEC,IAAI,GAAG,UAAU,QAAQ,EAAE,OAAO;wBAClC,WAAW,CAAC,gEAAgE,EAC1E,UAAU,cACN,8EACA,yFACJ;wBACF,SAAS,IAAM,oBAAoB;wBACnC,aAAa,CAAC,IAAM,EAAE,cAAc;wBACpC,cAAc,IAAM,eAAe;wBACnC,MAAK;wBACL,iBAAe,UAAU;kCAEtB;uBAbE;;;;8CAiBT,8OAAC;oBAAI,WAAU;oBAAqD,MAAK;oBAAS,iBAAc;oBAAO,iBAAc;8BAAQ;;;;;;;;;;;YAQlI,uBACC,8OAAC;gBAAE,IAAI,GAAG,GAAG,MAAM,CAAC;gBAAE,WAAU;gBAA8C,MAAK;0BAChF;;;;;;;;;;;;AAKX;uCAEe", "debugId": null}}, {"offset": {"line": 3405, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/forms/TextInput.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { forwardRef } from 'react';\r\n\r\ninterface TextInputProps extends React.InputHTMLAttributes<HTMLInputElement> {\r\n  label?: React.ReactNode;\r\n  error?: string;\r\n  helperText?: string;\r\n  variant?: 'default' | 'small';\r\n  fullWidth?: boolean;\r\n}\r\n\r\nconst TextInput = forwardRef<HTMLInputElement, TextInputProps>(({\r\n  label,\r\n  error,\r\n  helperText,\r\n  variant = 'default',\r\n  fullWidth = true,\r\n  className = '',\r\n  required,\r\n  disabled,\r\n  ...props\r\n}, ref) => {\r\n  // Base input styling with proper text visibility for all modes - force text color to ensure visibility\r\n  const baseInputClass = `px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 ${\r\n    fullWidth ? 'w-full' : ''\r\n  } ${variant === 'small' ? 'py-1.5 text-sm' : 'py-2'}`;\r\n\r\n  // Error and disabled states\r\n  const inputClass = `${baseInputClass} ${\r\n    error \r\n      ? \"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500\" \r\n      : \"border-gray-300 dark:border-gray-600\"\r\n  } ${\r\n    disabled \r\n      ? \"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800\" \r\n      : \"\"\r\n  } ${className}`;\r\n\r\n  const labelClass = `block font-medium text-gray-700 dark:text-gray-300 mb-2 ${\r\n    variant === 'small' ? 'text-xs text-gray-600 dark:text-gray-400 mb-1' : 'text-sm'\r\n  }`;\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      {label && (\r\n        <label className={labelClass}>\r\n          {label}\r\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\r\n        </label>\r\n      )}\r\n      \r\n      <input\r\n        ref={ref}\r\n        className={inputClass}\r\n        disabled={disabled}\r\n        required={required}\r\n        {...props}\r\n      />\r\n      \r\n      {error && (\r\n        <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\r\n          {error}\r\n        </p>\r\n      )}\r\n      \r\n      {helperText && !error && (\r\n        <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n          {helperText}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n});\r\n\r\nTextInput.displayName = 'TextInput';\r\n\r\nexport default TextInput;"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAYA,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAoC,CAAC,EAC9D,KAAK,EACL,KAAK,EACL,UAAU,EACV,UAAU,SAAS,EACnB,YAAY,IAAI,EAChB,YAAY,EAAE,EACd,QAAQ,EACR,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,uGAAuG;IACvG,MAAM,iBAAiB,CAAC,4OAA4O,EAClQ,YAAY,WAAW,GACxB,CAAC,EAAE,YAAY,UAAU,mBAAmB,QAAQ;IAErD,4BAA4B;IAC5B,MAAM,aAAa,GAAG,eAAe,CAAC,EACpC,QACI,+EACA,uCACL,CAAC,EACA,WACI,8DACA,GACL,CAAC,EAAE,WAAW;IAEf,MAAM,aAAa,CAAC,wDAAwD,EAC1E,YAAY,UAAU,kDAAkD,WACxE;IAEF,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBAAM,WAAW;;oBACf;oBACA,0BAAY,8OAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,8OAAC;gBACC,KAAK;gBACL,WAAW;gBACX,UAAU;gBACV,UAAU;gBACT,GAAG,KAAK;;;;;;YAGV,uBACC,8OAAC;gBAAE,WAAU;0BACV;;;;;;YAIJ,cAAc,CAAC,uBACd,8OAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;AAEA,UAAU,WAAW,GAAG;uCAET", "debugId": null}}, {"offset": {"line": 3482, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/forms/TextArea.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { forwardRef } from 'react';\r\n\r\ninterface TextAreaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {\r\n  label?: React.ReactNode;\r\n  error?: string;\r\n  helperText?: string;\r\n  variant?: 'default' | 'small';\r\n  fullWidth?: boolean;\r\n}\r\n\r\nconst TextArea = forwardRef<HTMLTextAreaElement, TextAreaProps>(({\r\n  label,\r\n  error,\r\n  helperText,\r\n  variant = 'default',\r\n  fullWidth = true,\r\n  className = '',\r\n  required,\r\n  disabled,\r\n  rows = 3,\r\n  ...props\r\n}, ref) => {\r\n  // Base textarea styling with proper text visibility for all modes - force text color to ensure visibility\r\n  const baseTextAreaClass = `px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 resize-y ${\r\n    fullWidth ? 'w-full' : ''\r\n  } ${variant === 'small' ? 'py-1.5 text-sm' : 'py-2'}`;\r\n\r\n  // Error and disabled states\r\n  const textAreaClass = `${baseTextAreaClass} ${\r\n    error \r\n      ? \"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500\" \r\n      : \"border-gray-300 dark:border-gray-600\"\r\n  } ${\r\n    disabled \r\n      ? \"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800\" \r\n      : \"\"\r\n  } ${className}`;\r\n\r\n  const labelClass = `block font-medium text-gray-700 dark:text-gray-300 mb-2 ${\r\n    variant === 'small' ? 'text-xs text-gray-600 dark:text-gray-400 mb-1' : 'text-sm'\r\n  }`;\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      {label && (\r\n        <label className={labelClass}>\r\n          {label}\r\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\r\n        </label>\r\n      )}\r\n      \r\n      <textarea\r\n        ref={ref}\r\n        className={textAreaClass}\r\n        disabled={disabled}\r\n        required={required}\r\n        rows={rows}\r\n        {...props}\r\n      />\r\n      \r\n      {error && (\r\n        <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\r\n          {error}\r\n        </p>\r\n      )}\r\n      \r\n      {helperText && !error && (\r\n        <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n          {helperText}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n});\r\n\r\nTextArea.displayName = 'TextArea';\r\n\r\nexport default TextArea;"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAYA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAsC,CAAC,EAC/D,KAAK,EACL,KAAK,EACL,UAAU,EACV,UAAU,SAAS,EACnB,YAAY,IAAI,EAChB,YAAY,EAAE,EACd,QAAQ,EACR,QAAQ,EACR,OAAO,CAAC,EACR,GAAG,OACJ,EAAE;IACD,0GAA0G;IAC1G,MAAM,oBAAoB,CAAC,qPAAqP,EAC9Q,YAAY,WAAW,GACxB,CAAC,EAAE,YAAY,UAAU,mBAAmB,QAAQ;IAErD,4BAA4B;IAC5B,MAAM,gBAAgB,GAAG,kBAAkB,CAAC,EAC1C,QACI,+EACA,uCACL,CAAC,EACA,WACI,8DACA,GACL,CAAC,EAAE,WAAW;IAEf,MAAM,aAAa,CAAC,wDAAwD,EAC1E,YAAY,UAAU,kDAAkD,WACxE;IAEF,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBAAM,WAAW;;oBACf;oBACA,0BAAY,8OAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,8OAAC;gBACC,KAAK;gBACL,WAAW;gBACX,UAAU;gBACV,UAAU;gBACV,MAAM;gBACL,GAAG,KAAK;;;;;;YAGV,uBACC,8OAAC;gBAAE,WAAU;0BACV;;;;;;YAIJ,cAAc,CAAC,uBACd,8OAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;AAEA,SAAS,WAAW,GAAG;uCAER", "debugId": null}}, {"offset": {"line": 3560, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/forms/Select.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { forwardRef } from 'react';\r\n\r\ninterface SelectOption {\r\n  value: string;\r\n  label: string;\r\n}\r\n\r\ninterface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {\r\n  label?: React.ReactNode;\r\n  error?: string;\r\n  helperText?: string;\r\n  variant?: 'default' | 'small';\r\n  fullWidth?: boolean;\r\n  options?: SelectOption[];\r\n  children?: React.ReactNode;\r\n}\r\n\r\nconst Select = forwardRef<HTMLSelectElement, SelectProps>(({\r\n  label,\r\n  error,\r\n  helperText,\r\n  variant = 'default',\r\n  fullWidth = true,\r\n  className = '',\r\n  required,\r\n  disabled,\r\n  options,\r\n  children,\r\n  ...props\r\n}, ref) => {\r\n  // Base select styling with proper text visibility for all modes\r\n  const baseSelectClass = `px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white transition-colors duration-200 ${\r\n    fullWidth ? 'w-full' : ''\r\n  } ${variant === 'small' ? 'py-1.5 text-sm' : 'py-2'}`;\r\n\r\n  // Error and disabled states\r\n  const selectClass = `${baseSelectClass} ${\r\n    error \r\n      ? \"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500\" \r\n      : \"border-gray-300 dark:border-gray-600\"\r\n  } ${\r\n    disabled \r\n      ? \"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800\" \r\n      : \"\"\r\n  } ${className}`;\r\n\r\n  const labelClass = `block font-medium text-gray-700 dark:text-gray-300 mb-2 ${\r\n    variant === 'small' ? 'text-xs text-gray-600 dark:text-gray-400 mb-1' : 'text-sm'\r\n  }`;\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      {label && (\r\n        <label className={labelClass}>\r\n          {label}\r\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\r\n        </label>\r\n      )}\r\n      \r\n      <select\r\n        ref={ref}\r\n        className={selectClass}\r\n        disabled={disabled}\r\n        required={required}\r\n        {...props}\r\n      >\r\n        {options ? (\r\n          options.map((option) => (\r\n            <option key={option.value} value={option.value}>\r\n              {option.label}\r\n            </option>\r\n          ))\r\n        ) : (\r\n          children\r\n        )}\r\n      </select>\r\n      \r\n      {error && (\r\n        <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\r\n          {error}\r\n        </p>\r\n      )}\r\n      \r\n      {helperText && !error && (\r\n        <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n          {helperText}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n});\r\n\r\nSelect.displayName = 'Select';\r\n\r\nexport default Select;"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAmBA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAkC,CAAC,EACzD,KAAK,EACL,KAAK,EACL,UAAU,EACV,UAAU,SAAS,EACnB,YAAY,IAAI,EAChB,YAAY,EAAE,EACd,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,gEAAgE;IAChE,MAAM,kBAAkB,CAAC,6LAA6L,EACpN,YAAY,WAAW,GACxB,CAAC,EAAE,YAAY,UAAU,mBAAmB,QAAQ;IAErD,4BAA4B;IAC5B,MAAM,cAAc,GAAG,gBAAgB,CAAC,EACtC,QACI,+EACA,uCACL,CAAC,EACA,WACI,8DACA,GACL,CAAC,EAAE,WAAW;IAEf,MAAM,aAAa,CAAC,wDAAwD,EAC1E,YAAY,UAAU,kDAAkD,WACxE;IAEF,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBAAM,WAAW;;oBACf;oBACA,0BAAY,8OAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,8OAAC;gBACC,KAAK;gBACL,WAAW;gBACX,UAAU;gBACV,UAAU;gBACT,GAAG,KAAK;0BAER,UACC,QAAQ,GAAG,CAAC,CAAC,uBACX,8OAAC;wBAA0B,OAAO,OAAO,KAAK;kCAC3C,OAAO,KAAK;uBADF,OAAO,KAAK;;;;gCAK3B;;;;;;YAIH,uBACC,8OAAC;gBAAE,WAAU;0BACV;;;;;;YAIJ,cAAc,CAAC,uBACd,8OAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;AAEA,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 3645, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/forms/FormInput.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport TextInput from './TextInput';\r\nimport TextArea from './TextArea';\r\nimport Select from './Select';\r\n\r\ninterface Option {\r\n  value: string;\r\n  label: string;\r\n}\r\n\r\ninterface FormInputProps {\r\n  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'textarea' | 'select';\r\n  label?: string | React.ReactNode;\r\n  id?: string;\r\n  name?: string;\r\n  value?: string;\r\n  onChange?: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;\r\n  placeholder?: string;\r\n  required?: boolean;\r\n  disabled?: boolean;\r\n  error?: string;\r\n  helperText?: string;\r\n  options?: Option[];\r\n  rows?: number;\r\n  maxLength?: number;\r\n  variant?: 'default' | 'small';\r\n  className?: string;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nconst FormInput: React.FC<FormInputProps> = ({\r\n  type = 'text',\r\n  children,\r\n  options,\r\n  rows,\r\n  ...props\r\n}) => {\r\n  switch (type) {\r\n    case 'textarea':\r\n      return <TextArea rows={rows} {...props} />;\r\n    \r\n    case 'select':\r\n      return (\r\n        <Select {...props}>\r\n          {options ? (\r\n            options.map((option) => (\r\n              <option key={option.value} value={option.value}>\r\n                {option.label}\r\n              </option>\r\n            ))\r\n          ) : (\r\n            children\r\n          )}\r\n        </Select>\r\n      );\r\n    \r\n    default:\r\n      return <TextInput type={type} {...props} />;\r\n  }\r\n};\r\n\r\nexport default FormInput;"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAgCA,MAAM,YAAsC,CAAC,EAC3C,OAAO,MAAM,EACb,QAAQ,EACR,OAAO,EACP,IAAI,EACJ,GAAG,OACJ;IACC,OAAQ;QACN,KAAK;YACH,qBAAO,8OAAC,uIAAA,CAAA,UAAQ;gBAAC,MAAM;gBAAO,GAAG,KAAK;;;;;;QAExC,KAAK;YACH,qBACE,8OAAC,qIAAA,CAAA,UAAM;gBAAE,GAAG,KAAK;0BACd,UACC,QAAQ,GAAG,CAAC,CAAC,uBACX,8OAAC;wBAA0B,OAAO,OAAO,KAAK;kCAC3C,OAAO,KAAK;uBADF,OAAO,KAAK;;;;gCAK3B;;;;;;QAKR;YACE,qBAAO,8OAAC,wIAAA,CAAA,UAAS;gBAAC,MAAM;gBAAO,GAAG,KAAK;;;;;;IAC3C;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 3702, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/forms/index.ts"], "sourcesContent": ["export { default as FileUpload } from './FileUpload';\r\nexport { default as FormField } from './FormField';\r\nexport { default as ProgressIndicator } from './ProgressIndicator';\r\nexport { default as CountryDropdown } from './CountryDropdown';\r\nexport { default as TextInput } from './TextInput';\r\nexport { default as TextArea } from './TextArea';\r\nexport { default as Select } from './Select';\r\nexport { default as FormInput } from './FormInput';"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 3751, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/forms/FormMessages.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\n\r\ninterface FormMessagesProps {\r\n  successMessage?: string | null;\r\n  errorMessage?: string | null;\r\n  validationErrors?: Record<string, string>;\r\n  className?: string;\r\n}\r\n\r\nexport const FormMessages: React.FC<FormMessagesProps> = ({\r\n  successMessage,\r\n  errorMessage,\r\n  validationErrors = {},\r\n  className = ''\r\n}) => {\r\n  const hasErrors = errorMessage || Object.keys(validationErrors).length > 0;\r\n\r\n  return (\r\n    <div className={className}>\r\n      {/* Success Message */}\r\n      {successMessage && (\r\n        <div className=\"mb-6 bg-green-50 border border-green-200 rounded-lg p-4\">\r\n          <div className=\"flex items-center\">\r\n            <i className=\"ri-check-circle-line text-green-500 mr-2\"></i>\r\n            <p className=\"text-green-700\">{successMessage}</p>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Error Message */}\r\n      {errorMessage && (\r\n        <div className=\"mb-6 bg-red-50 border border-red-200 rounded-lg p-4\">\r\n          <div className=\"flex items-center\">\r\n            <i className=\"ri-error-warning-line text-red-500 mr-2\"></i>\r\n            <p className=\"text-red-700\">{errorMessage}</p>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Validation Errors */}\r\n      {Object.keys(validationErrors).length > 0 && !errorMessage && (\r\n        <div className=\"mb-6 bg-red-50 border border-red-200 rounded-lg p-4\">\r\n          <div className=\"flex items-start\">\r\n            <i className=\"ri-error-warning-line text-red-500 mr-2 mt-0.5\"></i>\r\n            <div>\r\n              <h4 className=\"text-sm font-medium text-red-900 mb-2\">\r\n                Please fix the following issues:\r\n              </h4>\r\n              <ul className=\"text-sm text-red-700 space-y-1\">\r\n                {Object.entries(validationErrors).map(([field, error]) => (\r\n                  <li key={field}>• {error}</li>\r\n                ))}\r\n              </ul>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\ninterface SuccessMessageProps {\r\n  message: string;\r\n  onDismiss?: () => void;\r\n  autoHide?: boolean;\r\n  autoHideDelay?: number;\r\n  className?: string;\r\n}\r\n\r\nexport const SuccessMessage: React.FC<SuccessMessageProps> = ({\r\n  message,\r\n  onDismiss,\r\n  autoHide = true,\r\n  autoHideDelay = 5000,\r\n  className = ''\r\n}) => {\r\n  React.useEffect(() => {\r\n    if (autoHide && onDismiss) {\r\n      const timer = setTimeout(() => {\r\n        onDismiss();\r\n      }, autoHideDelay);\r\n\r\n      return () => clearTimeout(timer);\r\n    }\r\n  }, [autoHide, autoHideDelay, onDismiss]);\r\n\r\n  return (\r\n    <div className={`bg-green-50 border border-green-200 rounded-lg p-4 ${className}`}>\r\n      <div className=\"flex items-center justify-between\">\r\n        <div className=\"flex items-center\">\r\n          <i className=\"ri-check-circle-line text-green-500 mr-2\"></i>\r\n          <p className=\"text-green-700\">{message}</p>\r\n        </div>\r\n        {onDismiss && (\r\n          <button\r\n            onClick={onDismiss}\r\n            className=\"text-green-500 hover:text-green-700 ml-4\"\r\n            aria-label=\"Dismiss\"\r\n          >\r\n            <i className=\"ri-close-line\"></i>\r\n          </button>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\ninterface ErrorMessageProps {\r\n  message: string;\r\n  onDismiss?: () => void;\r\n  className?: string;\r\n}\r\n\r\nexport const ErrorMessage: React.FC<ErrorMessageProps> = ({\r\n  message,\r\n  onDismiss,\r\n  className = ''\r\n}) => {\r\n  return (\r\n    <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>\r\n      <div className=\"flex items-center justify-between\">\r\n        <div className=\"flex items-center\">\r\n          <i className=\"ri-error-warning-line text-red-500 mr-2\"></i>\r\n          <p className=\"text-red-700\">{message}</p>\r\n        </div>\r\n        {onDismiss && (\r\n          <button\r\n            onClick={onDismiss}\r\n            className=\"text-red-500 hover:text-red-700 ml-4\"\r\n            aria-label=\"Dismiss\"\r\n          >\r\n            <i className=\"ri-close-line\"></i>\r\n          </button>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\ninterface ValidationErrorsProps {\r\n  errors: Record<string, string>;\r\n  className?: string;\r\n}\r\n\r\nexport const ValidationErrors: React.FC<ValidationErrorsProps> = ({\r\n  errors,\r\n  className = ''\r\n}) => {\r\n  if (Object.keys(errors).length === 0) return null;\r\n\r\n  return (\r\n    <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>\r\n      <div className=\"flex items-start\">\r\n        <i className=\"ri-error-warning-line text-red-500 mr-2 mt-0.5\"></i>\r\n        <div>\r\n          <h4 className=\"text-sm font-medium text-red-900 mb-2\">\r\n            Please fix the following issues:\r\n          </h4>\r\n          <ul className=\"text-sm text-red-700 space-y-1\">\r\n            {Object.entries(errors).map(([field, error]) => (\r\n              <li key={field}>• {error}</li>\r\n            ))}\r\n          </ul>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FormMessages;\r\n"], "names": [], "mappings": ";;;;;;;;AAEA;AAFA;;;AAWO,MAAM,eAA4C,CAAC,EACxD,cAAc,EACd,YAAY,EACZ,mBAAmB,CAAC,CAAC,EACrB,YAAY,EAAE,EACf;IACC,MAAM,YAAY,gBAAgB,OAAO,IAAI,CAAC,kBAAkB,MAAM,GAAG;IAEzE,qBACE,8OAAC;QAAI,WAAW;;YAEb,gCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;;;;;sCACb,8OAAC;4BAAE,WAAU;sCAAkB;;;;;;;;;;;;;;;;;YAMpC,8BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;;;;;sCACb,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;YAMlC,OAAO,IAAI,CAAC,kBAAkB,MAAM,GAAG,KAAK,CAAC,8BAC5C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;;;;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,8OAAC;oCAAG,WAAU;8CACX,OAAO,OAAO,CAAC,kBAAkB,GAAG,CAAC,CAAC,CAAC,OAAO,MAAM,iBACnD,8OAAC;;gDAAe;gDAAG;;2CAAV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3B;AAUO,MAAM,iBAAgD,CAAC,EAC5D,OAAO,EACP,SAAS,EACT,WAAW,IAAI,EACf,gBAAgB,IAAI,EACpB,YAAY,EAAE,EACf;IACC,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,YAAY,WAAW;YACzB,MAAM,QAAQ,WAAW;gBACvB;YACF,GAAG;YAEH,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;QAAU;QAAe;KAAU;IAEvC,qBACE,8OAAC;QAAI,WAAW,CAAC,mDAAmD,EAAE,WAAW;kBAC/E,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;;;;;sCACb,8OAAC;4BAAE,WAAU;sCAAkB;;;;;;;;;;;;gBAEhC,2BACC,8OAAC;oBACC,SAAS;oBACT,WAAU;oBACV,cAAW;8BAEX,cAAA,8OAAC;wBAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMzB;AAQO,MAAM,eAA4C,CAAC,EACxD,OAAO,EACP,SAAS,EACT,YAAY,EAAE,EACf;IACC,qBACE,8OAAC;QAAI,WAAW,CAAC,+CAA+C,EAAE,WAAW;kBAC3E,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;;;;;sCACb,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;gBAE9B,2BACC,8OAAC;oBACC,SAAS;oBACT,WAAU;oBACV,cAAW;8BAEX,cAAA,8OAAC;wBAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMzB;AAOO,MAAM,mBAAoD,CAAC,EAChE,MAAM,EACN,YAAY,EAAE,EACf;IACC,IAAI,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK,GAAG,OAAO;IAE7C,qBACE,8OAAC;QAAI,WAAW,CAAC,+CAA+C,EAAE,WAAW;kBAC3E,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAE,WAAU;;;;;;8BACb,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAG,WAAU;sCACX,OAAO,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,OAAO,MAAM,iBACzC,8OAAC;;wCAAe;wCAAG;;mCAAV;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvB;uCAEe", "debugId": null}}, {"offset": {"line": 4087, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/hooks/useDynamicNavigation.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useCallback, useMemo } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { \r\n  getStepsByLicenseTypeCode,\r\n  isLicenseTypeCodeSupported,\r\n  getLicenseTypeStepConfig,\r\n  StepConfig \r\n} from '@/config/licenseTypeStepConfig';\r\nimport { CustomerApiService } from '@/lib/customer-api';\r\n\r\ninterface NavigationParams {\r\n  licenseCategoryId: string;\r\n  applicationId?: string;\r\n}\r\n\r\ninterface UseDynamicNavigationProps {\r\n  currentStepRoute: string;\r\n  licenseCategoryId: string | null;\r\n  applicationId: string | null;\r\n}\r\n\r\ninterface UseDynamicNavigationReturn {\r\n  // Navigation functions\r\n  handleNext: (saveFunction?: () => Promise<boolean>) => Promise<void>;\r\n  handlePrevious: () => void;\r\n  navigateToStep: (stepRoute: string) => void;\r\n  \r\n  // Step information\r\n  currentStep: StepConfig | null;\r\n  nextStep: StepConfig | null;\r\n  previousStep: StepConfig | null;\r\n  currentStepIndex: number;\r\n  totalSteps: number;\r\n  \r\n  // State\r\n  loading: boolean;\r\n  error: string | null;\r\n  licenseTypeCode: string | null;\r\n  \r\n  // Utility functions\r\n  isFirstStep: boolean;\r\n  isLastStep: boolean;\r\n  canNavigateNext: boolean;\r\n  canNavigatePrevious: boolean;\r\n}\r\n\r\nexport const useDynamicNavigation = ({\r\n  currentStepRoute,\r\n  licenseCategoryId,\r\n  applicationId\r\n}: UseDynamicNavigationProps): UseDynamicNavigationReturn => {\r\n  const router = useRouter();\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [licenseTypeCode, setLicenseTypeCode] = useState<string | null>(null);\r\n  const [steps, setSteps] = useState<StepConfig[]>([]);\r\n\r\n  // Create customer API service instance\r\n  const customerApi = useMemo(() => new CustomerApiService(), []);\r\n\r\n  // Load license type and steps\r\n  const loadLicenseTypeSteps = useCallback(async () => {\r\n    if (!licenseCategoryId) {\r\n      setError('License category ID is required');\r\n      setLoading(false);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n\r\n      console.log('🧭 Loading license type for navigation:', licenseCategoryId);\r\n\r\n      // Get license category and type\r\n      const category = await customerApi.getLicenseCategory(licenseCategoryId);\r\n      if (!category?.license_type_id) {\r\n        throw new Error('License category does not have a license type ID');\r\n      }\r\n\r\n      // Add small delay to prevent rate limiting\r\n      await new Promise(resolve => setTimeout(resolve, 500));\r\n\r\n      const licenseType = await customerApi.getLicenseType(category.license_type_id);\r\n      if (!licenseType) {\r\n        throw new Error('License type not found');\r\n      }\r\n\r\n      const typeCode = licenseType.code || licenseType.license_type_id;\r\n      setLicenseTypeCode(typeCode);\r\n\r\n      // Get steps based on license type code\r\n      let licenseSteps: StepConfig[] = [];\r\n      \r\n      if (isLicenseTypeCodeSupported(typeCode)) {\r\n        console.log('✅ Using optimized steps for supported license type:', typeCode);\r\n        licenseSteps = getStepsByLicenseTypeCode(typeCode);\r\n      } else {\r\n        console.log('⚠️ Using fallback steps for license type:', typeCode);\r\n        const config = getLicenseTypeStepConfig(typeCode);\r\n        licenseSteps = config.steps;\r\n      }\r\n\r\n      setSteps(licenseSteps);\r\n      console.log('🧭 Loaded steps for navigation:', licenseSteps.map(s => s.route));\r\n\r\n    } catch (err: any) {\r\n      console.error('Error loading license type steps:', err);\r\n      setError(err.message || 'Failed to load navigation configuration');\r\n      \r\n      // Use default fallback steps\r\n      const fallbackConfig = getLicenseTypeStepConfig('default');\r\n      setSteps(fallbackConfig.steps);\r\n      setLicenseTypeCode('default');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [licenseCategoryId, customerApi]);\r\n\r\n  // Load steps when dependencies change\r\n  useEffect(() => {\r\n    loadLicenseTypeSteps();\r\n  }, [loadLicenseTypeSteps]);\r\n\r\n  // Computed values\r\n  const currentStepIndex = useMemo(() => {\r\n    return steps.findIndex(step => step.route === currentStepRoute);\r\n  }, [steps, currentStepRoute]);\r\n\r\n  const currentStep = useMemo(() => {\r\n    return steps[currentStepIndex] || null;\r\n  }, [steps, currentStepIndex]);\r\n\r\n  const nextStep = useMemo(() => {\r\n    return currentStepIndex >= 0 && currentStepIndex < steps.length - 1 \r\n      ? steps[currentStepIndex + 1] \r\n      : null;\r\n  }, [steps, currentStepIndex]);\r\n\r\n  const previousStep = useMemo(() => {\r\n    return currentStepIndex > 0 \r\n      ? steps[currentStepIndex - 1] \r\n      : null;\r\n  }, [steps, currentStepIndex]);\r\n\r\n  const totalSteps = steps.length;\r\n  const isFirstStep = currentStepIndex === 0;\r\n  const isLastStep = currentStepIndex === steps.length - 1;\r\n  const canNavigateNext = !isLastStep && nextStep !== null;\r\n  const canNavigatePrevious = !isFirstStep && previousStep !== null;\r\n\r\n  // Navigation functions\r\n  const createNavigationUrl = useCallback((stepRoute: string) => {\r\n    const params = new URLSearchParams();\r\n    params.set('license_category_id', licenseCategoryId || '');\r\n    \r\n    if (applicationId) {\r\n      params.set('application_id', applicationId);\r\n    }\r\n    \r\n    return `/customer/applications/apply/${stepRoute}?${params.toString()}`;\r\n  }, [licenseCategoryId, applicationId]);\r\n\r\n  const navigateToStep = useCallback((stepRoute: string) => {\r\n    const url = createNavigationUrl(stepRoute);\r\n    console.log('🧭 Navigating to step:', stepRoute, 'URL:', url);\r\n    router.push(url);\r\n  }, [createNavigationUrl, router]);\r\n\r\n  const handleNext = useCallback(async (saveFunction?: () => Promise<boolean>) => {\r\n    if (!canNavigateNext || !nextStep) {\r\n      console.warn('⚠️ Cannot navigate to next step');\r\n      return;\r\n    }\r\n\r\n    // If save function is provided, save first\r\n    if (saveFunction) {\r\n      console.log('💾 Saving current step before navigation...');\r\n      try {\r\n        const saved = await saveFunction();\r\n        if (!saved) {\r\n          console.warn('⚠️ Save failed, not navigating');\r\n          return;\r\n        }\r\n      } catch (error: any) {\r\n        console.error('❌ Error during save operation:', error);\r\n\r\n        // Handle specific error types\r\n        if (error.message?.includes('timeout')) {\r\n          console.error('Save operation timed out');\r\n        } else if (error.message?.includes('Bad Request')) {\r\n          console.error('Invalid data provided for save operation');\r\n        } else if (error.message?.includes('Too many requests')) {\r\n          console.error('Rate limit exceeded, please wait and try again');\r\n        }\r\n\r\n        // Don't navigate if save failed\r\n        return;\r\n      }\r\n    }\r\n\r\n    navigateToStep(nextStep.route);\r\n  }, [canNavigateNext, nextStep, navigateToStep]);\r\n\r\n  const handlePrevious = useCallback(() => {\r\n    if (!canNavigatePrevious || !previousStep) {\r\n      console.warn('⚠️ Cannot navigate to previous step');\r\n      return;\r\n    }\r\n\r\n    navigateToStep(previousStep.route);\r\n  }, [canNavigatePrevious, previousStep, navigateToStep]);\r\n\r\n  return {\r\n    // Navigation functions\r\n    handleNext,\r\n    handlePrevious,\r\n    navigateToStep,\r\n    \r\n    // Step information\r\n    currentStep,\r\n    nextStep,\r\n    previousStep,\r\n    currentStepIndex,\r\n    totalSteps,\r\n    \r\n    // State\r\n    loading,\r\n    error,\r\n    licenseTypeCode,\r\n    \r\n    // Utility functions\r\n    isFirstStep,\r\n    isLastStep,\r\n    canNavigateNext,\r\n    canNavigatePrevious,\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AAMA;AAVA;;;;;AAgDO,MAAM,uBAAuB,CAAC,EACnC,gBAAgB,EAChB,iBAAiB,EACjB,aAAa,EACa;IAC1B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAEnD,uCAAuC;IACvC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,IAAI,6HAAA,CAAA,qBAAkB,IAAI,EAAE;IAE9D,8BAA8B;IAC9B,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACvC,IAAI,CAAC,mBAAmB;YACtB,SAAS;YACT,WAAW;YACX;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YAET,QAAQ,GAAG,CAAC,2CAA2C;YAEvD,gCAAgC;YAChC,MAAM,WAAW,MAAM,YAAY,kBAAkB,CAAC;YACtD,IAAI,CAAC,UAAU,iBAAiB;gBAC9B,MAAM,IAAI,MAAM;YAClB;YAEA,2CAA2C;YAC3C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,cAAc,MAAM,YAAY,cAAc,CAAC,SAAS,eAAe;YAC7E,IAAI,CAAC,aAAa;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,YAAY,IAAI,IAAI,YAAY,eAAe;YAChE,mBAAmB;YAEnB,uCAAuC;YACvC,IAAI,eAA6B,EAAE;YAEnC,IAAI,CAAA,GAAA,sIAAA,CAAA,6BAA0B,AAAD,EAAE,WAAW;gBACxC,QAAQ,GAAG,CAAC,uDAAuD;gBACnE,eAAe,CAAA,GAAA,sIAAA,CAAA,4BAAyB,AAAD,EAAE;YAC3C,OAAO;gBACL,QAAQ,GAAG,CAAC,6CAA6C;gBACzD,MAAM,SAAS,CAAA,GAAA,sIAAA,CAAA,2BAAwB,AAAD,EAAE;gBACxC,eAAe,OAAO,KAAK;YAC7B;YAEA,SAAS;YACT,QAAQ,GAAG,CAAC,mCAAmC,aAAa,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;QAE9E,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,qCAAqC;YACnD,SAAS,IAAI,OAAO,IAAI;YAExB,6BAA6B;YAC7B,MAAM,iBAAiB,CAAA,GAAA,sIAAA,CAAA,2BAAwB,AAAD,EAAE;YAChD,SAAS,eAAe,KAAK;YAC7B,mBAAmB;QACrB,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;QAAmB;KAAY;IAEnC,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAqB;IAEzB,kBAAkB;IAClB,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC/B,OAAO,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK;IAChD,GAAG;QAAC;QAAO;KAAiB;IAE5B,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC1B,OAAO,KAAK,CAAC,iBAAiB,IAAI;IACpC,GAAG;QAAC;QAAO;KAAiB;IAE5B,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACvB,OAAO,oBAAoB,KAAK,mBAAmB,MAAM,MAAM,GAAG,IAC9D,KAAK,CAAC,mBAAmB,EAAE,GAC3B;IACN,GAAG;QAAC;QAAO;KAAiB;IAE5B,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC3B,OAAO,mBAAmB,IACtB,KAAK,CAAC,mBAAmB,EAAE,GAC3B;IACN,GAAG;QAAC;QAAO;KAAiB;IAE5B,MAAM,aAAa,MAAM,MAAM;IAC/B,MAAM,cAAc,qBAAqB;IACzC,MAAM,aAAa,qBAAqB,MAAM,MAAM,GAAG;IACvD,MAAM,kBAAkB,CAAC,cAAc,aAAa;IACpD,MAAM,sBAAsB,CAAC,eAAe,iBAAiB;IAE7D,uBAAuB;IACvB,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACvC,MAAM,SAAS,IAAI;QACnB,OAAO,GAAG,CAAC,uBAAuB,qBAAqB;QAEvD,IAAI,eAAe;YACjB,OAAO,GAAG,CAAC,kBAAkB;QAC/B;QAEA,OAAO,CAAC,6BAA6B,EAAE,UAAU,CAAC,EAAE,OAAO,QAAQ,IAAI;IACzE,GAAG;QAAC;QAAmB;KAAc;IAErC,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,MAAM,MAAM,oBAAoB;QAChC,QAAQ,GAAG,CAAC,0BAA0B,WAAW,QAAQ;QACzD,OAAO,IAAI,CAAC;IACd,GAAG;QAAC;QAAqB;KAAO;IAEhC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACpC,IAAI,CAAC,mBAAmB,CAAC,UAAU;YACjC,QAAQ,IAAI,CAAC;YACb;QACF;QAEA,2CAA2C;QAC3C,IAAI,cAAc;YAChB,QAAQ,GAAG,CAAC;YACZ,IAAI;gBACF,MAAM,QAAQ,MAAM;gBACpB,IAAI,CAAC,OAAO;oBACV,QAAQ,IAAI,CAAC;oBACb;gBACF;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,kCAAkC;gBAEhD,8BAA8B;gBAC9B,IAAI,MAAM,OAAO,EAAE,SAAS,YAAY;oBACtC,QAAQ,KAAK,CAAC;gBAChB,OAAO,IAAI,MAAM,OAAO,EAAE,SAAS,gBAAgB;oBACjD,QAAQ,KAAK,CAAC;gBAChB,OAAO,IAAI,MAAM,OAAO,EAAE,SAAS,sBAAsB;oBACvD,QAAQ,KAAK,CAAC;gBAChB;gBAEA,gCAAgC;gBAChC;YACF;QACF;QAEA,eAAe,SAAS,KAAK;IAC/B,GAAG;QAAC;QAAiB;QAAU;KAAe;IAE9C,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,IAAI,CAAC,uBAAuB,CAAC,cAAc;YACzC,QAAQ,IAAI,CAAC;YACb;QACF;QAEA,eAAe,aAAa,KAAK;IACnC,GAAG;QAAC;QAAqB;QAAc;KAAe;IAEtD,OAAO;QACL,uBAAuB;QACvB;QACA;QACA;QAEA,mBAAmB;QACnB;QACA;QACA;QACA;QACA;QAEA,QAAQ;QACR;QACA;QACA;QAEA,oBAAoB;QACpB;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 4286, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/services/professionalServicesService.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\r\nimport { processApiResponse } from '../lib/authUtils';\r\n\r\n// Types following backend entity structure\r\nexport interface ProfessionalServicesData {\r\n  professional_services_id?: string;\r\n  application_id: string;\r\n  consultants: string;\r\n  service_providers: string;\r\n  technical_support: string;\r\n  maintenance_arrangements: string;\r\n  professional_partnerships?: string;\r\n  outsourced_services?: string;\r\n  quality_assurance?: string;\r\n  training_programs?: string;\r\n  created_at?: string;\r\n  updated_at?: string;\r\n  created_by?: string;\r\n  updated_by?: string;\r\n}\r\n\r\nexport interface CreateProfessionalServicesData {\r\n  application_id: string;\r\n  consultants: string;\r\n  service_providers: string;\r\n  technical_support: string;\r\n  maintenance_arrangements: string;\r\n  professional_partnerships?: string;\r\n  outsourced_services?: string;\r\n  quality_assurance?: string;\r\n  training_programs?: string;\r\n}\r\n\r\nexport interface UpdateProfessionalServicesData {\r\n  professional_services_id: string;\r\n  consultants: string;\r\n  service_providers: string;\r\n  technical_support: string;\r\n  maintenance_arrangements: string;\r\n  professional_partnerships?: string;\r\n  outsourced_services?: string;\r\n  quality_assurance?: string;\r\n  training_programs?: string;\r\n}\r\n\r\nclass ProfessionalServicesService {\r\n  private baseUrl = '/professional-services';\r\n\r\n  // Create professional services record\r\n  async createProfessionalServices(data: CreateProfessionalServicesData): Promise<ProfessionalServicesData> {\r\n    try {\r\n      console.log('🔧 Creating professional services record:', data);\r\n      const response = await apiClient.post(this.baseUrl, data);\r\n      return processApiResponse(response);\r\n    } catch (error: any) {\r\n      console.error('❌ Error creating professional services:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Update professional services record\r\n  async updateProfessionalServices(data: UpdateProfessionalServicesData): Promise<ProfessionalServicesData> {\r\n    try {\r\n      console.log('🔧 Updating professional services record:', data.professional_services_id);\r\n      const response = await apiClient.put(`${this.baseUrl}/${data.professional_services_id}`, data);\r\n      return processApiResponse(response);\r\n    } catch (error: any) {\r\n      console.error('❌ Error updating professional services:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Get professional services by application ID\r\n  async getProfessionalServicesByApplication(applicationId: string): Promise<ProfessionalServicesData | null> {\r\n    try {\r\n      console.log('🔧 Getting professional services for application:', applicationId);\r\n      const response = await apiClient.get(`${this.baseUrl}/application/${applicationId}`);\r\n      return processApiResponse(response);\r\n    } catch (error: any) {\r\n      if (error.response?.status === 404) {\r\n        console.log('📝 No professional services found for application:', applicationId);\r\n        return null;\r\n      }\r\n      console.error('❌ Error getting professional services:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Get professional services by ID\r\n  async getProfessionalServices(professionalServicesId: string): Promise<ProfessionalServicesData> {\r\n    try {\r\n      console.log('🔧 Getting professional services:', professionalServicesId);\r\n      const response = await apiClient.get(`${this.baseUrl}/${professionalServicesId}`);\r\n      return processApiResponse(response);\r\n    } catch (error: any) {\r\n      console.error('❌ Error getting professional services:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Delete professional services record\r\n  async deleteProfessionalServices(professionalServicesId: string): Promise<void> {\r\n    try {\r\n      console.log('🔧 Deleting professional services:', professionalServicesId);\r\n      const response = await apiClient.delete(`${this.baseUrl}/${professionalServicesId}`);\r\n      return processApiResponse(response);\r\n    } catch (error: any) {\r\n      console.error('❌ Error deleting professional services:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Get all professional services (admin only)\r\n  async getAllProfessionalServices(): Promise<ProfessionalServicesData[]> {\r\n    try {\r\n      console.log('🔧 Getting all professional services');\r\n      const response = await apiClient.get(this.baseUrl);\r\n      return processApiResponse(response);\r\n    } catch (error: any) {\r\n      console.error('❌ Error getting all professional services:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Create or update professional services for an application\r\n  async createOrUpdateProfessionalServices(applicationId: string, data: Omit<CreateProfessionalServicesData, 'application_id'>): Promise<ProfessionalServicesData> {\r\n    try {\r\n      // Use the backend's combined create/update endpoint\r\n      const response = await apiClient.post(`${this.baseUrl}/application/${applicationId}`, data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('ProfessionalServicesService.createOrUpdateProfessionalServices error:', error);\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n\r\nexport const professionalServicesService = new ProfessionalServicesService();\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AA4CA,MAAM;IACI,UAAU,yBAAyB;IAE3C,sCAAsC;IACtC,MAAM,2BAA2B,IAAoC,EAAqC;QACxG,IAAI;YACF,QAAQ,GAAG,CAAC,6CAA6C;YACzD,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACpD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,2CAA2C;YACzD,MAAM;QACR;IACF;IAEA,sCAAsC;IACtC,MAAM,2BAA2B,IAAoC,EAAqC;QACxG,IAAI;YACF,QAAQ,GAAG,CAAC,6CAA6C,KAAK,wBAAwB;YACtF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,wBAAwB,EAAE,EAAE;YACzF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,2CAA2C;YACzD,MAAM;QACR;IACF;IAEA,8CAA8C;IAC9C,MAAM,qCAAqC,aAAqB,EAA4C;QAC1G,IAAI;YACF,QAAQ,GAAG,CAAC,qDAAqD;YACjE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,eAAe;YACnF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAY;YACnB,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,QAAQ,GAAG,CAAC,sDAAsD;gBAClE,OAAO;YACT;YACA,QAAQ,KAAK,CAAC,0CAA0C;YACxD,MAAM;QACR;IACF;IAEA,kCAAkC;IAClC,MAAM,wBAAwB,sBAA8B,EAAqC;QAC/F,IAAI;YACF,QAAQ,GAAG,CAAC,qCAAqC;YACjD,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,wBAAwB;YAChF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,0CAA0C;YACxD,MAAM;QACR;IACF;IAEA,sCAAsC;IACtC,MAAM,2BAA2B,sBAA8B,EAAiB;QAC9E,IAAI;YACF,QAAQ,GAAG,CAAC,sCAAsC;YAClD,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,wBAAwB;YACnF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,2CAA2C;YACzD,MAAM;QACR;IACF;IAEA,6CAA6C;IAC7C,MAAM,6BAAkE;QACtE,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO;YACjD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,MAAM;QACR;IACF;IAEA,4DAA4D;IAC5D,MAAM,mCAAmC,aAAqB,EAAE,IAA4D,EAAqC;QAC/J,IAAI;YACF,oDAAoD;YACpD,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,eAAe,EAAE;YACtF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yEAAyE;YACvF,MAAM;QACR;IACF;AACF;AAEO,MAAM,8BAA8B,IAAI", "debugId": null}}, {"offset": {"line": 4384, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/app/customer/applications/apply/professional-services/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { useSearchParams } from 'next/navigation';\r\nimport CustomerLayout from '@/components/customer/CustomerLayout';\r\nimport ApplicationLayout from '@/components/applications/ApplicationLayout';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { TextArea } from '@/components/forms';\r\nimport { FormMessages } from '@/components/forms/FormMessages';\r\nimport { useDynamicNavigation } from '@/hooks/useDynamicNavigation';\r\nimport { professionalServicesService } from '@/services/professionalServicesService';\r\n\r\nconst ProfessionalServicesPage: React.FC = () => {\r\n  const searchParams = useSearchParams();\r\n  const { isAuthenticated, loading: authLoading } = useAuth();\r\n\r\n  // Get query parameters\r\n  const licenseCategoryId = searchParams.get('license_category_id');\r\n  const applicationId = searchParams.get('application_id');\r\n\r\n  // State management\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [isSaving, setIsSaving] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});\r\n  const [successMessage, setSuccessMessage] = useState<string | null>(null);\r\n\r\n  // Dynamic navigation hook\r\n  const {\r\n    handleNext: dynamicHandleNext,\r\n    handlePrevious: dynamicHandlePrevious\r\n  } = useDynamicNavigation({\r\n    currentStepRoute: 'professional-services',\r\n    licenseCategoryId,\r\n    applicationId\r\n  });\r\n\r\n\r\n\r\n  // Form data state\r\n  const [formData, setFormData] = useState({\r\n    consultants: '',\r\n    service_providers: '',\r\n    technical_support: '',\r\n    maintenance_arrangements: '',\r\n    professional_partnerships: '',\r\n    outsourced_services: '',\r\n    quality_assurance: '',\r\n    training_programs: ''\r\n  });\r\n\r\n  // Form handling functions\r\n  const handleFormChange = (field: string, value: any) => {\r\n    setFormData(prev => ({ ...prev, [field]: value }));\r\n\r\n    // Clear success message when user starts making changes\r\n    if (successMessage) {\r\n      setSuccessMessage(null);\r\n    }\r\n\r\n    // Clear validation error for this field\r\n    if (validationErrors[field]) {\r\n      setValidationErrors(prev => {\r\n        const newErrors = { ...prev };\r\n        delete newErrors[field];\r\n        return newErrors;\r\n      });\r\n    }\r\n  };\r\n\r\n  // Load existing data\r\n  useEffect(() => {\r\n    const loadData = async () => {\r\n      if (!applicationId || !isAuthenticated || authLoading) return;\r\n\r\n      try {\r\n        setIsLoading(true);\r\n        setError(null);\r\n\r\n        // Load existing professional services data\r\n        try {\r\n          const existingData = await professionalServicesService.getProfessionalServicesByApplication(applicationId);\r\n          if (existingData) {\r\n            setFormData({\r\n              consultants: existingData.consultants || '',\r\n              service_providers: existingData.service_providers || '',\r\n              technical_support: existingData.technical_support || '',\r\n              maintenance_arrangements: existingData.maintenance_arrangements || '',\r\n              professional_partnerships: existingData.professional_partnerships || '',\r\n              outsourced_services: existingData.outsourced_services || '',\r\n              quality_assurance: existingData.quality_assurance || '',\r\n              training_programs: existingData.training_programs || ''\r\n            });\r\n            console.log('Professional services data auto-populated');\r\n          } else {\r\n            console.log('No existing professional services data found');\r\n          }\r\n        } catch (professionalServicesError: any) {\r\n          console.error('Error loading professional services data:', professionalServicesError);\r\n          // Silently handle error - form will start empty\r\n        }\r\n\r\n      } catch (error: any) {\r\n        console.error('Error loading professional services form:', error);\r\n        setError('Failed to load professional services form. Please try again.');\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    loadData();\r\n  }, [applicationId, isAuthenticated, authLoading]);\r\n\r\n  // Save function - following other apply pages pattern\r\n  const handleSave = async (): Promise<boolean> => {\r\n    if (!applicationId) {\r\n      setValidationErrors({ save: 'Application ID is required' });\r\n      return false;\r\n    }\r\n\r\n    setIsSaving(true);\r\n    try {\r\n      // Basic validation - check required fields\r\n      const errors: Record<string, string> = {};\r\n\r\n      if (!formData.consultants.trim()) errors.consultants = 'Consultants information is required';\r\n      if (!formData.service_providers.trim()) errors.service_providers = 'Service providers information is required';\r\n      if (!formData.technical_support.trim()) errors.technical_support = 'Technical support information is required';\r\n      if (!formData.maintenance_arrangements.trim()) errors.maintenance_arrangements = 'Maintenance arrangements information is required';\r\n\r\n      if (Object.keys(errors).length > 0) {\r\n        setValidationErrors(errors);\r\n        setIsSaving(false);\r\n        return false;\r\n      }\r\n\r\n      // Create or update professional services record using the proper API\r\n      const professionalServicesData = {\r\n        consultants: formData.consultants,\r\n        service_providers: formData.service_providers,\r\n        technical_support: formData.technical_support,\r\n        maintenance_arrangements: formData.maintenance_arrangements,\r\n        professional_partnerships: formData.professional_partnerships,\r\n        outsourced_services: formData.outsourced_services,\r\n        quality_assurance: formData.quality_assurance,\r\n        training_programs: formData.training_programs\r\n      };\r\n\r\n      // Save professional services data\r\n      try {\r\n        await professionalServicesService.createOrUpdateProfessionalServices(applicationId, professionalServicesData);\r\n      } catch (saveError: any) {\r\n        console.error('Error saving professional services data:', saveError);\r\n        throw new Error('Failed to save professional services information');\r\n      }\r\n\r\n      setValidationErrors({});\r\n      setSuccessMessage('Professional services information saved successfully!');\r\n\r\n      // Auto-hide success message after 5 seconds\r\n      setTimeout(() => {\r\n        setSuccessMessage(null);\r\n      }, 5000);\r\n\r\n      console.log('Professional services information saved successfully');\r\n      return true;\r\n\r\n    } catch (error: any) {\r\n      console.error('Error saving professional services information:', error);\r\n      \r\n      // Extract specific error message from API response\r\n      let errorMessage = 'Failed to save professional services information. Please try again.';\r\n      \r\n      if (error.response?.data?.message) {\r\n        // Use the specific error message from the backend\r\n        errorMessage = error.response.data.message;\r\n      } else if (error.message) {\r\n        // Fallback to error message\r\n        errorMessage = error.message;\r\n      }\r\n      \r\n      setValidationErrors({ save: errorMessage });\r\n      return false;\r\n    } finally {\r\n      setIsSaving(false);\r\n    }\r\n  };\r\n\r\n  // Navigation functions using dynamic navigation\r\n  const handleNext = async () => {\r\n    await dynamicHandleNext(handleSave);\r\n  };\r\n\r\n  const handlePrevious = () => {\r\n    dynamicHandlePrevious();\r\n  };\r\n\r\n  if (authLoading || isLoading) {\r\n    return (\r\n      <CustomerLayout>\r\n        <div className=\"flex items-center justify-center min-h-96\">\r\n          <div className=\"text-center\">\r\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4\"></div>\r\n            <p className=\"text-gray-600 dark:text-gray-400\">Loading professional services form...</p>\r\n          </div>\r\n        </div>\r\n      </CustomerLayout>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <CustomerLayout>\r\n        <div className=\"max-w-4xl mx-auto p-6\">\r\n          <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6\">\r\n            <div className=\"flex items-center\">\r\n              <i className=\"ri-error-warning-line text-red-600 dark:text-red-400 text-2xl mr-4\"></i>\r\n              <div>\r\n                <h3 className=\"text-lg font-medium text-red-800 dark:text-red-200\">Error Loading Form</h3>\r\n                <p className=\"text-red-700 dark:text-red-300 mt-1\">{error}</p>\r\n                <button\r\n                  onClick={() => window.location.reload()}\r\n                  className=\"mt-4 inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40\"\r\n                >\r\n                  <i className=\"ri-refresh-line mr-2\"></i>\r\n                  Retry\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </CustomerLayout>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <CustomerLayout>\r\n      <ApplicationLayout\r\n        onNext={handleNext}\r\n        onPrevious={handlePrevious}\r\n        onSave={handleSave}\r\n        showNextButton={true}\r\n        showPreviousButton={true}\r\n        showSaveButton={true}\r\n        nextButtonText=\"Continue to Next Step\"\r\n        previousButtonText=\"Back to Previous Step\"\r\n        saveButtonText=\"Save Professional Services Information\"\r\n        nextButtonDisabled={false}\r\n        previousButtonDisabled={false}\r\n        saveButtonDisabled={isSaving}\r\n      >\r\n        <div className=\"max-w-4xl mx-auto\">\r\n          <FormMessages\r\n            successMessage={successMessage}\r\n            errorMessage={validationErrors.save}\r\n          />\r\n\r\n          <div className=\"bg-white shadow-sm rounded-lg p-6\">\r\n            <div className=\"mb-6\">\r\n              <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">\r\n                Professional Services Information\r\n              </h2>\r\n              <p className=\"text-gray-600\">\r\n                Provide details about professional services, consultants, and technical support arrangements.\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"space-y-6\">\r\n              {/* Consultants */}\r\n              <div>\r\n                <TextArea\r\n                  label=\"Consultants *\"\r\n                  name=\"consultants\"\r\n                  value={formData.consultants}\r\n                  onChange={(e) => handleFormChange('consultants', e.target.value)}\r\n                  placeholder=\"Describe the consultants and advisory services you use...\"\r\n                  rows={3}\r\n                  error={validationErrors.consultants}\r\n                />\r\n              </div>\r\n\r\n              {/* Service Providers */}\r\n              <div>\r\n                <TextArea\r\n                  label=\"Service Providers *\"\r\n                  name=\"service_providers\"\r\n                  value={formData.service_providers}\r\n                  onChange={(e) => handleFormChange('service_providers', e.target.value)}\r\n                  placeholder=\"List your key service providers and their roles...\"\r\n                  rows={3}\r\n                  error={validationErrors.service_providers}\r\n                />\r\n              </div>\r\n\r\n              {/* Technical Support */}\r\n              <div>\r\n                <TextArea\r\n                  label=\"Technical Support *\"\r\n                  name=\"technical_support\"\r\n                  value={formData.technical_support}\r\n                  onChange={(e) => handleFormChange('technical_support', e.target.value)}\r\n                  placeholder=\"Describe your technical support arrangements and capabilities...\"\r\n                  rows={3}\r\n                  error={validationErrors.technical_support}\r\n                />\r\n              </div>\r\n\r\n              {/* Maintenance Arrangements */}\r\n              <div>\r\n                <TextArea\r\n                  label=\"Maintenance Arrangements *\"\r\n                  name=\"maintenance_arrangements\"\r\n                  value={formData.maintenance_arrangements}\r\n                  onChange={(e) => handleFormChange('maintenance_arrangements', e.target.value)}\r\n                  placeholder=\"Detail your maintenance and support arrangements...\"\r\n                  rows={3}\r\n                  error={validationErrors.maintenance_arrangements}\r\n                />\r\n              </div>\r\n\r\n              {/* Professional Partnerships */}\r\n              <div>\r\n                <TextArea\r\n                  label=\"Professional Partnerships\"\r\n                  name=\"professional_partnerships\"\r\n                  value={formData.professional_partnerships}\r\n                  onChange={(e) => handleFormChange('professional_partnerships', e.target.value)}\r\n                  placeholder=\"Describe any professional partnerships and collaborations...\"\r\n                  rows={3}\r\n                  error={validationErrors.professional_partnerships}\r\n                />\r\n              </div>\r\n\r\n              {/* Outsourced Services */}\r\n              <div>\r\n                <TextArea\r\n                  label=\"Outsourced Services\"\r\n                  name=\"outsourced_services\"\r\n                  value={formData.outsourced_services}\r\n                  onChange={(e) => handleFormChange('outsourced_services', e.target.value)}\r\n                  placeholder=\"List any services that are outsourced and the arrangements in place...\"\r\n                  rows={3}\r\n                  error={validationErrors.outsourced_services}\r\n                />\r\n              </div>\r\n\r\n              {/* Quality Assurance */}\r\n              <div>\r\n                <TextArea\r\n                  label=\"Quality Assurance\"\r\n                  name=\"quality_assurance\"\r\n                  value={formData.quality_assurance}\r\n                  onChange={(e) => handleFormChange('quality_assurance', e.target.value)}\r\n                  placeholder=\"Describe your quality assurance processes and standards...\"\r\n                  rows={3}\r\n                  error={validationErrors.quality_assurance}\r\n                />\r\n              </div>\r\n\r\n              {/* Training Programs */}\r\n              <div>\r\n                <TextArea\r\n                  label=\"Training Programs\"\r\n                  name=\"training_programs\"\r\n                  value={formData.training_programs}\r\n                  onChange={(e) => handleFormChange('training_programs', e.target.value)}\r\n                  placeholder=\"Describe any training programs for staff and professional development initiatives...\"\r\n                  rows={3}\r\n                  error={validationErrors.training_programs}\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </ApplicationLayout>\r\n    </CustomerLayout>\r\n  );\r\n};\r\n\r\nexport default ProfessionalServicesPage;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAVA;;;;;;;;;;;AAYA,MAAM,2BAAqC;IACzC,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,EAAE,eAAe,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAExD,uBAAuB;IACvB,MAAM,oBAAoB,aAAa,GAAG,CAAC;IAC3C,MAAM,gBAAgB,aAAa,GAAG,CAAC;IAEvC,mBAAmB;IACnB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAClF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,0BAA0B;IAC1B,MAAM,EACJ,YAAY,iBAAiB,EAC7B,gBAAgB,qBAAqB,EACtC,GAAG,CAAA,GAAA,oIAAA,CAAA,uBAAoB,AAAD,EAAE;QACvB,kBAAkB;QAClB;QACA;IACF;IAIA,kBAAkB;IAClB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,aAAa;QACb,mBAAmB;QACnB,mBAAmB;QACnB,0BAA0B;QAC1B,2BAA2B;QAC3B,qBAAqB;QACrB,mBAAmB;QACnB,mBAAmB;IACrB;IAEA,0BAA0B;IAC1B,MAAM,mBAAmB,CAAC,OAAe;QACvC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAEhD,wDAAwD;QACxD,IAAI,gBAAgB;YAClB,kBAAkB;QACpB;QAEA,wCAAwC;QACxC,IAAI,gBAAgB,CAAC,MAAM,EAAE;YAC3B,oBAAoB,CAAA;gBAClB,MAAM,YAAY;oBAAE,GAAG,IAAI;gBAAC;gBAC5B,OAAO,SAAS,CAAC,MAAM;gBACvB,OAAO;YACT;QACF;IACF;IAEA,qBAAqB;IACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW;YACf,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,aAAa;YAEvD,IAAI;gBACF,aAAa;gBACb,SAAS;gBAET,2CAA2C;gBAC3C,IAAI;oBACF,MAAM,eAAe,MAAM,8IAAA,CAAA,8BAA2B,CAAC,oCAAoC,CAAC;oBAC5F,IAAI,cAAc;wBAChB,YAAY;4BACV,aAAa,aAAa,WAAW,IAAI;4BACzC,mBAAmB,aAAa,iBAAiB,IAAI;4BACrD,mBAAmB,aAAa,iBAAiB,IAAI;4BACrD,0BAA0B,aAAa,wBAAwB,IAAI;4BACnE,2BAA2B,aAAa,yBAAyB,IAAI;4BACrE,qBAAqB,aAAa,mBAAmB,IAAI;4BACzD,mBAAmB,aAAa,iBAAiB,IAAI;4BACrD,mBAAmB,aAAa,iBAAiB,IAAI;wBACvD;wBACA,QAAQ,GAAG,CAAC;oBACd,OAAO;wBACL,QAAQ,GAAG,CAAC;oBACd;gBACF,EAAE,OAAO,2BAAgC;oBACvC,QAAQ,KAAK,CAAC,6CAA6C;gBAC3D,gDAAgD;gBAClD;YAEF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,6CAA6C;gBAC3D,SAAS;YACX,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG;QAAC;QAAe;QAAiB;KAAY;IAEhD,sDAAsD;IACtD,MAAM,aAAa;QACjB,IAAI,CAAC,eAAe;YAClB,oBAAoB;gBAAE,MAAM;YAA6B;YACzD,OAAO;QACT;QAEA,YAAY;QACZ,IAAI;YACF,2CAA2C;YAC3C,MAAM,SAAiC,CAAC;YAExC,IAAI,CAAC,SAAS,WAAW,CAAC,IAAI,IAAI,OAAO,WAAW,GAAG;YACvD,IAAI,CAAC,SAAS,iBAAiB,CAAC,IAAI,IAAI,OAAO,iBAAiB,GAAG;YACnE,IAAI,CAAC,SAAS,iBAAiB,CAAC,IAAI,IAAI,OAAO,iBAAiB,GAAG;YACnE,IAAI,CAAC,SAAS,wBAAwB,CAAC,IAAI,IAAI,OAAO,wBAAwB,GAAG;YAEjF,IAAI,OAAO,IAAI,CAAC,QAAQ,MAAM,GAAG,GAAG;gBAClC,oBAAoB;gBACpB,YAAY;gBACZ,OAAO;YACT;YAEA,qEAAqE;YACrE,MAAM,2BAA2B;gBAC/B,aAAa,SAAS,WAAW;gBACjC,mBAAmB,SAAS,iBAAiB;gBAC7C,mBAAmB,SAAS,iBAAiB;gBAC7C,0BAA0B,SAAS,wBAAwB;gBAC3D,2BAA2B,SAAS,yBAAyB;gBAC7D,qBAAqB,SAAS,mBAAmB;gBACjD,mBAAmB,SAAS,iBAAiB;gBAC7C,mBAAmB,SAAS,iBAAiB;YAC/C;YAEA,kCAAkC;YAClC,IAAI;gBACF,MAAM,8IAAA,CAAA,8BAA2B,CAAC,kCAAkC,CAAC,eAAe;YACtF,EAAE,OAAO,WAAgB;gBACvB,QAAQ,KAAK,CAAC,4CAA4C;gBAC1D,MAAM,IAAI,MAAM;YAClB;YAEA,oBAAoB,CAAC;YACrB,kBAAkB;YAElB,4CAA4C;YAC5C,WAAW;gBACT,kBAAkB;YACpB,GAAG;YAEH,QAAQ,GAAG,CAAC;YACZ,OAAO;QAET,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,mDAAmD;YAEjE,mDAAmD;YACnD,IAAI,eAAe;YAEnB,IAAI,MAAM,QAAQ,EAAE,MAAM,SAAS;gBACjC,kDAAkD;gBAClD,eAAe,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;YAC5C,OAAO,IAAI,MAAM,OAAO,EAAE;gBACxB,4BAA4B;gBAC5B,eAAe,MAAM,OAAO;YAC9B;YAEA,oBAAoB;gBAAE,MAAM;YAAa;YACzC,OAAO;QACT,SAAU;YACR,YAAY;QACd;IACF;IAEA,gDAAgD;IAChD,MAAM,aAAa;QACjB,MAAM,kBAAkB;IAC1B;IAEA,MAAM,iBAAiB;QACrB;IACF;IAEA,IAAI,eAAe,WAAW;QAC5B,qBACE,8OAAC,gJAAA,CAAA,UAAc;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;;;;;;;;;;;IAK1D;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC,gJAAA,CAAA,UAAc;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;;;;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAqD;;;;;;kDACnE,8OAAC;wCAAE,WAAU;kDAAuC;;;;;;kDACpD,8OAAC;wCACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;wCACrC,WAAU;;0DAEV,8OAAC;gDAAE,WAAU;;;;;;4CAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASxD;IAEA,qBACE,8OAAC,gJAAA,CAAA,UAAc;kBACb,cAAA,8OAAC,uJAAA,CAAA,UAAiB;YAChB,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,gBAAgB;YAChB,oBAAoB;YACpB,gBAAgB;YAChB,gBAAe;YACf,oBAAmB;YACnB,gBAAe;YACf,oBAAoB;YACpB,wBAAwB;YACxB,oBAAoB;sBAEpB,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,2IAAA,CAAA,eAAY;wBACX,gBAAgB;wBAChB,cAAc,iBAAiB,IAAI;;;;;;kCAGrC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDAGzD,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAK/B,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;kDACC,cAAA,8OAAC,8KAAA,CAAA,WAAQ;4CACP,OAAM;4CACN,MAAK;4CACL,OAAO,SAAS,WAAW;4CAC3B,UAAU,CAAC,IAAM,iBAAiB,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC/D,aAAY;4CACZ,MAAM;4CACN,OAAO,iBAAiB,WAAW;;;;;;;;;;;kDAKvC,8OAAC;kDACC,cAAA,8OAAC,8KAAA,CAAA,WAAQ;4CACP,OAAM;4CACN,MAAK;4CACL,OAAO,SAAS,iBAAiB;4CACjC,UAAU,CAAC,IAAM,iBAAiB,qBAAqB,EAAE,MAAM,CAAC,KAAK;4CACrE,aAAY;4CACZ,MAAM;4CACN,OAAO,iBAAiB,iBAAiB;;;;;;;;;;;kDAK7C,8OAAC;kDACC,cAAA,8OAAC,8KAAA,CAAA,WAAQ;4CACP,OAAM;4CACN,MAAK;4CACL,OAAO,SAAS,iBAAiB;4CACjC,UAAU,CAAC,IAAM,iBAAiB,qBAAqB,EAAE,MAAM,CAAC,KAAK;4CACrE,aAAY;4CACZ,MAAM;4CACN,OAAO,iBAAiB,iBAAiB;;;;;;;;;;;kDAK7C,8OAAC;kDACC,cAAA,8OAAC,8KAAA,CAAA,WAAQ;4CACP,OAAM;4CACN,MAAK;4CACL,OAAO,SAAS,wBAAwB;4CACxC,UAAU,CAAC,IAAM,iBAAiB,4BAA4B,EAAE,MAAM,CAAC,KAAK;4CAC5E,aAAY;4CACZ,MAAM;4CACN,OAAO,iBAAiB,wBAAwB;;;;;;;;;;;kDAKpD,8OAAC;kDACC,cAAA,8OAAC,8KAAA,CAAA,WAAQ;4CACP,OAAM;4CACN,MAAK;4CACL,OAAO,SAAS,yBAAyB;4CACzC,UAAU,CAAC,IAAM,iBAAiB,6BAA6B,EAAE,MAAM,CAAC,KAAK;4CAC7E,aAAY;4CACZ,MAAM;4CACN,OAAO,iBAAiB,yBAAyB;;;;;;;;;;;kDAKrD,8OAAC;kDACC,cAAA,8OAAC,8KAAA,CAAA,WAAQ;4CACP,OAAM;4CACN,MAAK;4CACL,OAAO,SAAS,mBAAmB;4CACnC,UAAU,CAAC,IAAM,iBAAiB,uBAAuB,EAAE,MAAM,CAAC,KAAK;4CACvE,aAAY;4CACZ,MAAM;4CACN,OAAO,iBAAiB,mBAAmB;;;;;;;;;;;kDAK/C,8OAAC;kDACC,cAAA,8OAAC,8KAAA,CAAA,WAAQ;4CACP,OAAM;4CACN,MAAK;4CACL,OAAO,SAAS,iBAAiB;4CACjC,UAAU,CAAC,IAAM,iBAAiB,qBAAqB,EAAE,MAAM,CAAC,KAAK;4CACrE,aAAY;4CACZ,MAAM;4CACN,OAAO,iBAAiB,iBAAiB;;;;;;;;;;;kDAK7C,8OAAC;kDACC,cAAA,8OAAC,8KAAA,CAAA,WAAQ;4CACP,OAAM;4CACN,MAAK;4CACL,OAAO,SAAS,iBAAiB;4CACjC,UAAU,CAAC,IAAM,iBAAiB,qBAAqB,EAAE,MAAM,CAAC,KAAK;4CACrE,aAAY;4CACZ,MAAM;4CACN,OAAO,iBAAiB,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3D;uCAEe", "debugId": null}}]}