import { AuthService } from './auth.service';
import { LoginDto } from '../dto/auth/login.dto';
import { RegisterDto } from '../dto/auth/register.dto';
import { ForgotPasswordDto, ResetPasswordDto } from '../dto/auth/forgot-password.dto';
import { TwoFactorDto, RequestTwoFactorDto } from '../dto/auth/two-factor.dto';
import { Request } from "express";
export declare class AuthController {
    private authService;
    constructor(authService: AuthService);
    login(loginDto: LoginDto, req: Request): Promise<import("../common/types/auth.types").AuthResponse>;
    register(registerDto: RegisterDto): Promise<import("../common/types/auth.types").AuthResponse>;
    forgotPassword(forgotPasswordDto: ForgotPasswordDto): Promise<{
        message: string;
    }>;
    resetPassword(resetPasswordDto: ResetPasswordDto): Promise<{
        message: string;
    }>;
    verifyEmail(twoFactorDto: TwoFactorDto, req: Request): Promise<import("../common/types/auth.types").AuthResponse | {
        message: string;
    }>;
    setupTwoFactorAuth(setup2FA: RequestTwoFactorDto): Promise<{
        otpAuthUrl: string;
        qrCodeDataUrl: string;
        secret: string;
        message: string;
    }>;
    verifyTwoFactorCode(twoFactorDto: TwoFactorDto, req: Request): Promise<import("../common/types/auth.types").AuthResponse | {
        message: string;
    }>;
    refresh(req: any): Promise<{
        access_token: string;
        user: {
            user_id: string | undefined;
            email: string | undefined;
            first_name: string | undefined;
            last_name: string | undefined;
            roles: import("../entities").Role[] | undefined;
        };
    }>;
}
