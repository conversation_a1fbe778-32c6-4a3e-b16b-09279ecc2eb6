import { Injectable } from '@nestjs/common';
import { NotificationsService } from './notifications.service';
import { NotificationType, RecipientType } from '../entities/notifications.entity';

@Injectable()
export class NotificationHelperService {
  constructor(private readonly notificationsService: NotificationsService) {}

  /**
   * Send application status notification to customer
   */
  async notifyApplicationStatus(
    applicationId: string,
    applicantId: string,
    applicantEmail: string,
    applicantPhone: string,
    applicationNumber: string,
    status: string,
    createdBy: string
  ): Promise<void> {
    const subject = `Application ${applicationNumber} Status Update`;
    const message = `Your application ${applicationNumber} status has been updated to: ${status.toUpperCase()}`;
    
    // Create email notification
    if (applicantEmail) {
      await this.notificationsService.create({
        type: NotificationType.EMAIL,
        recipient_type: RecipientType.CUSTOMER,
        recipient_id: applicantId,
        recipient_email: applicantEmail,
        subject,
        message,
        html_content: this.generateApplicationStatusEmailHtml(applicationNumber, status),
        entity_type: 'application',
        entity_id: applicationId,
      }, createdBy);
    }

    // Create SMS notification
    if (applicantPhone) {
      await this.notificationsService.create({
        type: NotificationType.SMS,
        recipient_type: RecipientType.CUSTOMER,
        recipient_id: applicantId,
        recipient_phone: applicantPhone,
        subject,
        message: `MACRA: ${message}`,
        entity_type: 'application',
        entity_id: applicationId,
      }, createdBy);
    }

    // Create in-app notification
    await this.notificationsService.create({
      type: NotificationType.IN_APP,
      recipient_type: RecipientType.CUSTOMER,
      recipient_id: applicantId,
      subject,
      message,
      entity_type: 'application',
      entity_id: applicationId,
      action_url: `/customer/my-licenses?application_id=${applicationId}`,
    }, createdBy);
  }

  /**
   * Send task assignment notification to staff
   */
  async notifyTaskAssignment(
    taskId: string,
    assigneeId: string,
    assigneeEmail: string,
    assigneePhone: string,
    taskTitle: string,
    taskDescription: string,
    createdBy: string
  ): Promise<void> {
    const subject = `New Task Assigned: ${taskTitle}`;
    const message = `You have been assigned a new task: ${taskTitle}. ${taskDescription}`;
    
    // Create email notification
    if (assigneeEmail) {
      await this.notificationsService.create({
        type: NotificationType.EMAIL,
        recipient_type: RecipientType.STAFF,
        recipient_id: assigneeId,
        recipient_email: assigneeEmail,
        subject,
        message,
        html_content: this.generateTaskAssignmentEmailHtml(taskTitle, taskDescription),
        entity_type: 'task',
        entity_id: taskId,
      }, createdBy);
    }

    // Create SMS notification
    if (assigneePhone) {
      await this.notificationsService.create({
        type: NotificationType.SMS,
        recipient_type: RecipientType.STAFF,
        recipient_id: assigneeId,
        recipient_phone: assigneePhone,
        subject,
        message: `MACRA: ${message}`,
        entity_type: 'task',
        entity_id: taskId,
      }, createdBy);
    }

    // Create in-app notification
    await this.notificationsService.create({
      type: NotificationType.IN_APP,
      recipient_type: RecipientType.STAFF,
      recipient_id: assigneeId,
      subject,
      message,
      entity_type: 'task',
      entity_id: taskId,
      action_url: `/tasks?task_id=${taskId}`,
    }, createdBy);
  }

  /**
   * Send license expiry notification to customer
   */
  async notifyLicenseExpiry(
    licenseId: string,
    customerId: string,
    customerEmail: string,
    customerPhone: string,
    licenseNumber: string,
    expiryDate: Date,
    daysUntilExpiry: number,
    createdBy: string
  ): Promise<void> {
    const subject = `License ${licenseNumber} Expiry Notice`;
    const message = `Your license ${licenseNumber} will expire in ${daysUntilExpiry} days on ${expiryDate.toDateString()}. Please renew to avoid service interruption.`;
    
    // Create email notification
    if (customerEmail) {
      await this.notificationsService.create({
        type: NotificationType.EMAIL,
        recipient_type: RecipientType.CUSTOMER,
        recipient_id: customerId,
        recipient_email: customerEmail,
        subject,
        message,
        html_content: this.generateLicenseExpiryEmailHtml(licenseNumber, expiryDate, daysUntilExpiry),
        entity_type: 'license',
        entity_id: licenseId,
      }, createdBy);
    }

    // Create SMS notification
    if (customerPhone) {
      await this.notificationsService.create({
        type: NotificationType.SMS,
        recipient_type: RecipientType.CUSTOMER,
        recipient_id: customerId,
        recipient_phone: customerPhone,
        subject,
        message: `MACRA: ${message}`,
        entity_type: 'license',
        entity_id: licenseId,
      }, createdBy);
    }

    // Create in-app notification
    await this.notificationsService.create({
      type: NotificationType.IN_APP,
      recipient_type: RecipientType.CUSTOMER,
      recipient_id: customerId,
      subject,
      message,
      entity_type: 'license',
      entity_id: licenseId,
      action_url: `/customer/my-licenses?license_id=${licenseId}`,
    }, createdBy);
  }

  /**
   * Generate HTML content for application status email
   */
  private generateApplicationStatusEmailHtml(applicationNumber: string, status: string): string {
    return `
      <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2 style="color: #d32f2f;">MACRA - Application Status Update</h2>
            <p>Dear Applicant,</p>
            <p>We are writing to inform you that your application <strong>${applicationNumber}</strong> status has been updated.</p>
            <div style="background-color: #f5f5f5; padding: 15px; border-left: 4px solid #d32f2f; margin: 20px 0;">
              <p style="margin: 0;"><strong>New Status:</strong> ${status.toUpperCase()}</p>
            </div>
            <p>You can view the full details of your application by logging into your MACRA portal account.</p>
            <p>If you have any questions, please contact our support team.</p>
            <p>Best regards,<br>MACRA Team</p>
          </div>
        </body>
      </html>
    `;
  }

  /**
   * Generate HTML content for task assignment email
   */
  private generateTaskAssignmentEmailHtml(taskTitle: string, taskDescription: string): string {
    return `
      <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2 style="color: #d32f2f;">MACRA - New Task Assignment</h2>
            <p>Dear Team Member,</p>
            <p>You have been assigned a new task that requires your attention.</p>
            <div style="background-color: #f5f5f5; padding: 15px; border-left: 4px solid #d32f2f; margin: 20px 0;">
              <p style="margin: 0 0 10px 0;"><strong>Task:</strong> ${taskTitle}</p>
              <p style="margin: 0;"><strong>Description:</strong> ${taskDescription}</p>
            </div>
            <p>Please log into the MACRA portal to view and manage this task.</p>
            <p>Best regards,<br>MACRA System</p>
          </div>
        </body>
      </html>
    `;
  }

  /**
   * Generate HTML content for license expiry email
   */
  private generateLicenseExpiryEmailHtml(licenseNumber: string, expiryDate: Date, daysUntilExpiry: number): string {
    return `
      <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2 style="color: #d32f2f;">MACRA - License Expiry Notice</h2>
            <p>Dear License Holder,</p>
            <p>This is an important reminder regarding your MACRA license.</p>
            <div style="background-color: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 20px 0;">
              <p style="margin: 0 0 10px 0;"><strong>License Number:</strong> ${licenseNumber}</p>
              <p style="margin: 0 0 10px 0;"><strong>Expiry Date:</strong> ${expiryDate.toDateString()}</p>
              <p style="margin: 0;"><strong>Days Until Expiry:</strong> ${daysUntilExpiry} days</p>
            </div>
            <p>Please ensure you renew your license before the expiry date to avoid any service interruptions.</p>
            <p>You can renew your license by logging into your MACRA portal account.</p>
            <p>Best regards,<br>MACRA Team</p>
          </div>
        </body>
      </html>
    `;
  }
}
