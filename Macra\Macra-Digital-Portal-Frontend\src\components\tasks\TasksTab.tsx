'use client';

import { useState, useEffect, useCallback } from 'react';
import { taskService, Task, TaskFilters, TaskType, TaskStatus, TaskPriority } from '../../services/task-assignment';
import { User } from '../../services/userService';
import { PaginateQuery } from '../../services/userService';
import DataTable from '../common/DataTable';
import ConfirmationModal from '../common/ConfirmationModal';
import ReassignTaskModal from './ReassignTaskModal';
import Select from '../common/Select';
import { useTaskNavigation } from '../../hooks/useTaskNavigation';

interface TasksTabProps {
  onEditTask: (task: Task) => void;
  onCreateTask: () => void;
}

const TasksTab = ({ onEditTask, onCreateTask }: TasksTabProps) => {
  const [tasksData, setTasksData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [taskToDelete, setTaskToDelete] = useState<Task | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showReassignModal, setShowReassignModal] = useState(false);
  const [taskToReassign, setTaskToReassign] = useState<Task | null>(null);
  const [filters, setFilters] = useState<TaskFilters>({});
  const [users, setUsers] = useState<User[]>([]);
  const [currentQuery, setCurrentQuery] = useState<PaginateQuery>({ page: 1, limit: 10 });

  // Add task navigation hook
  const { navigateToTaskView, openTaskViewInNewTab, isLoading: isNavigating } = useTaskNavigation();

  const handleFilterChange = (key: keyof TaskFilters, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value === '' ? undefined : value
    }));
  };

  const handleDeleteTask = (task: Task) => {
    setTaskToDelete(task);
    setShowDeleteModal(true);
  };

  const handleCancelDelete = () => {
    setShowDeleteModal(false);
    setTaskToDelete(null);
  };

  const handleReassignTask = (task: Task) => {
    setTaskToReassign(task);
    setShowReassignModal(true);
  };

  const handleCancelReassign = () => {
    setShowReassignModal(false);
    setTaskToReassign(null);
  };

  const handleReassignSuccess = () => {
    setShowReassignModal(false);
    setTaskToReassign(null);
    // Reload tasks to show updated assignment
    loadTasks(currentQuery);
  };

  const handleConfirmDelete = async () => {
    if (!taskToDelete) return;

    setIsDeleting(true);
    try {
      await taskService.deleteTask(taskToDelete.task_id);
      setShowDeleteModal(false);
      setTaskToDelete(null);
      // Reload tasks
      loadTasks(currentQuery);
    } catch (err) {
      console.error('Error deleting task:', err);
      setError('Failed to delete task');
    } finally {
      setIsDeleting(false);
    }
  };

  const loadTasks = useCallback(async (query: PaginateQuery) => {
    try {
      setLoading(true);
      setError(null);
      setCurrentQuery(query);

      // Combine query with filters
      const params = {
        ...query,
        ...filters
      };

      const response = await taskService.getTasks(params);
      setTasksData(response);
    } catch (err: any) {
      let errorMessage = 'Failed to load tasks. Please try again.';
      
      if (err && typeof err === 'object') {
        if ('response' in err && err.response && typeof err.response === 'object') {
          if ('status' in err.response) {
            const status = err.response.status;
            if (status === 401) {
              errorMessage = 'Authentication required. Please log in again.';
            } else if (status === 403) {
              errorMessage = 'You do not have permission to view tasks.';
            } else if (status === 500) {
              errorMessage = 'Server error. Please try again later.';
            } else if ('data' in err.response &&
                      err.response.data &&
                      typeof err.response.data === 'object' &&
                      'message' in err.response.data &&
                      typeof err.response.data.message === 'string') {
              errorMessage = err.response.data.message;
            }
          }
        } else if ('message' in err && typeof err.message === 'string') {
          errorMessage = err.message;
        }
      }

      setError(errorMessage);
      setTasksData({
        data: [],
        meta: {
          itemsPerPage: query.limit || 10,
          totalItems: 0,
          currentPage: query.page || 1,
          totalPages: 0,
          sortBy: [],
          searchBy: [],
          search: '',
          select: [],
        },
        links: {
          current: '',
        },
      });
    } finally {
      setLoading(false);
    }
  }, [filters]);

  useEffect(() => {
    loadTasks({ page: 1, limit: 10 });
    loadUsers();
  }, [loadTasks]);

  // Reload data when filters change
  useEffect(() => {
    const cleanFilters: Record<string, string> = {};
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value.trim() !== '') {
        cleanFilters[key] = value;
      }
    });

    loadTasks({
      page: 1,
      limit: currentQuery.limit || 10,
      filter: Object.keys(cleanFilters).length > 0 ? cleanFilters : undefined
    });
  }, [filters, currentQuery.limit]);

  const loadUsers = async () => {
    try {
      const usersResponse = await taskService.getOfficers();
      setUsers(usersResponse.data);
    } catch (err) {
      console.error('Error loading users:', err);
      setUsers([]);
    }
  };

  const getStatusBadgeClass = (status: TaskStatus) => {
    switch (status) {
      case TaskStatus.PENDING:
        return 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200';
      case TaskStatus.IN_PROGRESS:
        return 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200';
      case TaskStatus.COMPLETED:
        return 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200';
      case TaskStatus.CANCELLED:
        return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200';
      case TaskStatus.ON_HOLD:
        return 'bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200';
      default:
        return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200';
    }
  };

  const getPriorityBadgeClass = (priority: TaskPriority) => {
    switch (priority) {
      case TaskPriority.LOW:
        return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200';
      case TaskPriority.MEDIUM:
        return 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200';
      case TaskPriority.HIGH:
        return 'bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200';
      case TaskPriority.URGENT:
        return 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200';
      default:
        return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200';
    }
  };

  const taskColumns = [
    {
      key: 'task_number',
      label: 'Task Number',
      sortable: true,
      render: (value: string, task: Task) => (
        <div
          className="text-sm font-medium text-gray-900 dark:text-gray-100 cursor-pointer hover:text-blue-600 dark:hover:text-blue-400 hover:underline"
          onClick={() => navigateToTaskView(task.task_id)}
          title="Click to view task"
        >
          {value}
        </div>
      ),
    },
    {
      key: 'title',
      label: 'Title',
      sortable: true,
      render: (value: string, task: Task) => (
        <div>
          <div
            className="text-sm font-medium text-gray-900 dark:text-gray-100 cursor-pointer hover:text-blue-600 dark:hover:text-blue-400 hover:underline"
            onClick={() => navigateToTaskView(task.task_id)}
            title="Click to view task"
          >
            {value}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs">
            {task.description}
          </div>
        </div>
      ),
    },
    {
      key: 'task_type',
      label: 'Type',
      render: (value: TaskType) => (
        <span className="text-sm text-gray-900 dark:text-gray-100 capitalize">
          {value.replace('_', ' ')}
        </span>
      ),
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (value: TaskStatus) => (
        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(value)}`}>
          {value.replace('_', ' ').toUpperCase()}
        </span>
      ),
    },
    {
      key: 'priority',
      label: 'Priority',
      sortable: true,
      render: (value: TaskPriority) => (
        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getPriorityBadgeClass(value)}`}>
          {value.toUpperCase()}
        </span>
      ),
    },
    {
      key: 'assignee',
      label: 'Assigned To',
      render: (value: any, task: Task) => (
        <span className="text-sm text-gray-900 dark:text-gray-100">
          {task.assignee ? `${task.assignee.first_name} ${task.assignee.last_name}` : 'Unassigned'}
        </span>
      ),
    },
    {
      key: 'due_date',
      label: 'Due Date',
      sortable: true,
      render: (value: string) => (
        <span className="text-sm text-gray-500 dark:text-gray-400">
          {value ? new Date(value).toLocaleDateString() : 'No due date'}
        </span>
      ),
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (value: unknown, task: Task) => (
        <div className="flex items-center space-x-2">
          <button
            onClick={() => navigateToTaskView(task.task_id)}
            disabled={isNavigating}
            className="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 p-1 rounded hover:bg-blue-50 dark:hover:bg-blue-900 disabled:opacity-50"
            title="View task"
          >
            <i className="ri-eye-line"></i>
          </button>
          <button
            onClick={() => openTaskViewInNewTab(task.task_id)}
            disabled={isNavigating}
            className="text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300 p-1 rounded hover:bg-green-50 dark:hover:bg-green-900 disabled:opacity-50"
            title="Open in new tab"
          >
            <i className="ri-external-link-line"></i>
          </button>
          <button
            onClick={() => handleReassignTask(task)}
            className="text-purple-600 dark:text-purple-400 hover:text-purple-900 dark:hover:text-purple-300 p-1 rounded hover:bg-purple-50 dark:hover:bg-purple-900"
            title="Reassign task"
          >
            <i className="ri-user-shared-line"></i>
          </button>
          <button
            onClick={() => handleDeleteTask(task)}
            className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 p-1 rounded hover:bg-red-50 dark:hover:bg-red-900"
            title="Delete task"
          >
            <i className="ri-delete-bin-line"></i>
          </button>
        </div>
      ),
    },
  ];

  return (
    <div>
      {/* Action header */}
      <div className="mb-6 flex justify-end">
        <button
          type="button"
          onClick={onCreateTask}
          className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-900"
        >
          <i className="ri-add-line w-5 h-5 mr-2"></i>
          Add Task
        </button>
      </div>

      {error && (
        <div className="mb-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {/* Filters Section */}
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">Filters</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          {/* Task Type Filter */}
          <Select
            label="Task Type"
            value={filters.task_type || ''}
            onChange={(value) => handleFilterChange('task_type', value)}
            options={[
              { value: '', label: 'All Types' },
              { value: TaskType.APPLICATION, label: 'Application' },
              { value: TaskType.COMPLAINT, label: 'Complaint' },
              { value: TaskType.DATA_BREACH, label: 'Data Breach' },
              { value: TaskType.EVALUATION, label: 'Evaluation' },
              { value: TaskType.INSPECTION, label: 'Inspection' },
              { value: TaskType.DOCUMENT_REVIEW, label: 'Document Review' },
              { value: TaskType.COMPLIANCE_CHECK, label: 'Compliance Check' },
              { value: TaskType.FOLLOW_UP, label: 'Follow Up' },
            ]}
          />

          {/* Status Filter */}
          <Select
            label="Status"
            value={filters.status || ''}
            onChange={(value) => handleFilterChange('status', value)}
            options={[
              { value: '', label: 'All Statuses' },
              { value: TaskStatus.PENDING, label: 'Pending' },
              { value: TaskStatus.IN_PROGRESS, label: 'In Progress' },
              { value: TaskStatus.COMPLETED, label: 'Completed' },
              { value: TaskStatus.CANCELLED, label: 'Cancelled' },
              { value: TaskStatus.ON_HOLD, label: 'On Hold' },
            ]}
          />

          {/* Priority Filter */}
          <Select
            label="Priority"
            value={filters.priority || ''}
            onChange={(value) => handleFilterChange('priority', value)}
            options={[
              { value: '', label: 'All Priorities' },
              { value: TaskPriority.LOW, label: 'Low' },
              { value: TaskPriority.MEDIUM, label: 'Medium' },
              { value: TaskPriority.HIGH, label: 'High' },
              { value: TaskPriority.URGENT, label: 'Urgent' },
            ]}
          />

          {/* Assignment Status Filter */}
          <Select
            label="Assignment Status"
            value={filters.assignment_status || ''}
            onChange={(value) => handleFilterChange('assignment_status', value)}
            options={[
              { value: '', label: 'All Tasks' },
              { value: 'assigned', label: 'Assigned' },
              { value: 'unassigned', label: 'Unassigned' },
            ]}
          />

          {/* Assigned To Filter */}
          {users.length > 0 && (
            <Select
              label="Assigned To"
              value={filters.assigned_to || ''}
              onChange={(value) => handleFilterChange('assigned_to', value)}
              options={[
                { value: '', label: 'All Users' },
                { value: 'null', label: 'Unassigned' },
                ...users.map((user) => ({
                  value: user.user_id,
                  label: `${user.first_name} ${user.last_name}`
                }))
              ]}
            />
          )}

          {/* Created By Filter */}
          {users.length > 0 && (
            <Select
              label="Created By"
              value={filters.created_by || ''}
              onChange={(value) => handleFilterChange('created_by', value)}
              options={[
                { value: '', label: 'All Users' },
                ...users.map((user) => ({
                  value: user.user_id,
                  label: `${user.first_name} ${user.last_name}`
                }))
              ]}
            />
          )}
        </div>
      </div>

      <DataTable
        columns={taskColumns}
        data={tasksData}
        loading={loading}
        onQueryChange={loadTasks}
        searchPlaceholder="Search tasks by title, description, or task number..."
      />

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={showDeleteModal}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title="Delete Task"
        message={
          taskToDelete ? (
            <div>
              <p className="mb-2">
                Are you sure you want to delete task <strong>{taskToDelete.task_number}</strong>?
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                This action cannot be undone. All data associated with this task will be permanently removed.
              </p>
            </div>
          ) : (
            'Are you sure you want to delete this task?'
          )
        }
        confirmText="Yes, Delete Task"
        cancelText="Cancel"
        confirmVariant="danger"
        loading={isDeleting}
      />

      {/* Reassign Task Modal */}
      <ReassignTaskModal
        isOpen={showReassignModal}
        onClose={handleCancelReassign}
        task={taskToReassign}
        onReassignSuccess={handleReassignSuccess}
      />
    </div>
  );
};

export default TasksTab;
