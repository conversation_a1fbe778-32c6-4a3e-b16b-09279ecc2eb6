import { processApiResponse } from '@/lib/authUtils';
import { apiClient } from '../lib/apiClient';
import { Application, ApplicationStatus, ApplicationFilters, PaginatedResponse } from '../types/license';

export const applicationService = {
  // Get all applications with pagination and filters
  async getApplications(params?: {
    page?: number;
    limit?: number;
    search?: string;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
    filters?: ApplicationFilters;
  }): Promise<PaginatedResponse<Application>> {
    const queryParams = new URLSearchParams();
    
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.search) queryParams.append('search', params.search);
    if (params?.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);
    
    // Add filters
    if (params?.filters?.licenseTypeId) {
      queryParams.append('filter.license_category.license_type_id', params.filters.licenseTypeId);
    }
    if (params?.filters?.licenseCategoryId) {
      queryParams.append('filter.license_category_id', params.filters.licenseCategoryId);
    }
    if (params?.filters?.status) {
      queryParams.append('filter.status', params.filters.status);
    }

    const response = await apiClient.get(`/applications?${queryParams.toString()}`);
    return processApiResponse(response);
  },

  // Get applications by license type (through license category)
  async getApplicationsByLicenseType(licenseTypeId: string, params?: {
    page?: number;
    limit?: number;
    search?: string;
    status?: ApplicationStatus;
  }): Promise<PaginatedResponse<Application>> {
    const queryParams = new URLSearchParams();
    
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.search) queryParams.append('search', params.search);
    if (params?.status) queryParams.append('filter.status', params.status);
    
    // Filter by license type through license category
    queryParams.append('filter.license_category.license_type_id', licenseTypeId);

    const response = await apiClient.get(`/applications?${queryParams.toString()}`);
    return processApiResponse(response);
  },

  // Get single application by ID
  async getApplication(id: string): Promise<Application> {
    const response = await apiClient.get(`/applications/${id}`);
    return processApiResponse(response);
  },

  // Get applications by applicant
  async getApplicationsByApplicant(applicantId: string): Promise<Application[]> {
    const response = await apiClient.get(`/applications/by-applicant/${applicantId}`);
    return processApiResponse(response);
  },

  // Get applications by status
  async getApplicationsByStatus(status: ApplicationStatus): Promise<Application[]> {
    const response = await apiClient.get(`/applications/by-status/${status}`);
    return processApiResponse(response);
  },

  // Update application status
  async updateApplicationStatus(id: string, status: ApplicationStatus): Promise<Application> {
    const response = await apiClient.put(`/applications/${id}/status?status=${status}`);
    return processApiResponse(response);
  },

  // Update application progress
  async updateApplicationProgress(id: string, currentStep: number, progressPercentage: number): Promise<Application> {
    const response = await apiClient.put(
      `/applications/${id}/progress?currentStep=${currentStep}&progressPercentage=${progressPercentage}`
    );
    return processApiResponse(response);
  },

  // Get application statistics
  async getApplicationStats(): Promise<Record<string, number>> {
    const response = await apiClient.get('/applications/stats');
    return processApiResponse(response);
  },

  // Create new application
  async createApplication(data: {
    application_number: string;
    applicant_id: string;
    license_category_id: string;
    status?: ApplicationStatus;
    current_step?: number;
    progress_percentage?: number;
    submitted_at?: Date;
  }): Promise<Application> {
    try {
      const response = await apiClient.post('/applications', data);
      return processApiResponse(response);
    } catch (error) {
      throw error;
    }
  },

  // Update application with improved error handling
  async updateApplication(id: string, data: Partial<Application>): Promise<Application> {
    try {
      console.log('Updating application:', id, 'with data:', data);
      const response = await apiClient.put(`/applications/${id}`, data, {
        timeout: 30000, // 30 second timeout
      });
      return processApiResponse(response);
    } catch (error: any) {
      console.error('Error updating application:', error);

      // Handle specific error cases
      if (error.code === 'ECONNABORTED') {
        throw new Error('Request timeout - please try again');
      }

      if (error.response?.status === 400) {
        const message = error.response?.data?.message || 'Invalid application data';
        console.error('400 Bad Request details:', error.response?.data);
        throw new Error(`Bad Request: ${message}`);
      }

      if (error.response?.status === 429) {
        throw new Error('Too many requests - please wait a moment and try again');
      }

      throw error;
    }
  },

  // Delete application
  async deleteApplication(id: string): Promise<{ message: string }> {
    const response = await apiClient.delete(`/applications/${id}`);
    return processApiResponse(response);
  },

  // Create new application with applicant data
  async createApplicationWithApplicant(data: {
    license_type_id: string;
    license_category_id: string;
    applicant_data: Record<string, any>;
    user_id: string;
  }): Promise<Application> {
    try {
      // Generate proper application number (format: APP-YYYYMMDD-HHMMSS-XXX)
      const now = new Date();
      const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');
      const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');
      const randomStr = Math.random().toString(36).substr(2, 3).toUpperCase();
      const applicationNumber = `APP-${dateStr}-${timeStr}-${randomStr}`;

      // Validate user_id is a proper UUID
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(data.user_id)) {
        throw new Error(`Invalid user_id format: ${data.user_id}. Expected UUID format.`);
      }

      // Create application using user_id as applicant_id
      // In most systems, the authenticated user is the applicant
      const application = await this.createApplication({
        application_number: applicationNumber,
        applicant_id: data.user_id, // Use user_id as applicant_id
        license_category_id: data.license_category_id,
        current_step: 1,
        progress_percentage: 0 // Start with 0% progress
      });

      return application;
    } catch (error) {
      console.error('Error creating application with applicant:', error);
      throw error;
    }
  },

  // Save application section data
  async saveApplicationSection(
    applicationId: string,
    sectionName: string,
    sectionData: Record<string, any>
  ): Promise<void> {
    try {
      console.log(`Saving section ${sectionName} for application ${applicationId}:`, sectionData);

      // Try to save using form data service, but continue if it fails
      let completedSections = 1; // At least one section is being saved

      // Use entity-specific APIs instead of form data service
      console.warn('saveApplicationSection is deprecated. Use entity-specific APIs instead.');

      // Estimate progress based on section name
      const sectionOrder = ['applicantInfo', 'companyProfile', 'businessInfo', 'serviceScope', 'businessPlan', 'legalHistory', 'reviewSubmit'];
      const sectionIndex = sectionOrder.indexOf(sectionName);
      completedSections = sectionIndex >= 0 ? sectionIndex + 1 : 1;

      // Calculate progress based on completed sections (excluding reviewSubmit from total)
      const totalSections = 6; // Total number of form sections (excluding reviewSubmit)
      const progressPercentage = Math.min(Math.round((completedSections / totalSections) * 100), 100);

      // Update the application progress
      await this.updateApplication(applicationId, {
        progress_percentage: progressPercentage,
        current_step: completedSections
      });

      console.log(`Section ${sectionName} saved successfully with ${progressPercentage}% progress`);
    } catch (error) {
      console.error(`Error saving section ${sectionName}:`, error);
      throw error;
    }
  },

  // Get application section data
  async getApplicationSection(applicationId: string, sectionName: string): Promise<any> {
    try {
      const response = await apiClient.get(`/applications/${applicationId}/sections/${sectionName}`);
      return processApiResponse(response);
    } catch (error) {
      console.error(`Error fetching section ${sectionName}:`, error);
      throw error;
    }
  },

  // Submit application for review
  async submitApplication(applicationId: string): Promise<Application> {
    try {
      console.log('Submitting application:', applicationId);

      // Update application status to submitted and set submission date
      const response = await apiClient.put(`/applications/${applicationId}`, {
        status: 'submitted',
        submitted_at: new Date().toISOString(),
        progress_percentage: 100,
        current_step: 7
      });

      console.log('Application submitted successfully:', processApiResponse(response));
      return processApiResponse(response);
    } catch (error) {
      console.error('Error submitting application:', error);
      throw error;
    }
  },

  // Get user's applications (filtered by authenticated user)
  async getUserApplications(): Promise<Application[]> {
    try {
      console.log('Fetching user applications...');

      // Use dedicated endpoint that explicitly filters by current user
      const response = await apiClient.get('/applications/user-applications');
      const processedResponse = processApiResponse(response);

      console.log('User applications API response:', processedResponse);

      // Handle paginated response structure
      let applications = [];
      if (processedResponse?.data) {
        applications = Array.isArray(processedResponse.data) ? processedResponse.data : [];
      } else if (Array.isArray(processedResponse)) {
        applications = processedResponse;
      } else if (processedResponse) {
        // Single application or other structure
        applications = [processedResponse];
      }

      console.log('Processed user applications:', applications);
      return applications;
    } catch (error) {
      console.error('Error fetching user applications:', error);
      console.error('Error details:', {
        message: (error as any)?.message,
        response: (error as any)?.response?.data,
        status: (error as any)?.response?.status
      });
      throw error;
    }
  },

  // Save application as draft
  async saveAsDraft(applicationId: string, formData: Record<string, any>): Promise<Application> {
    try {
      const response = await apiClient.put(`/applications/${applicationId}`, {
        form_data: formData,
        status: 'draft'
      });
      return processApiResponse(response);
    } catch (error) {
      console.error('Error saving application as draft:', error);
      throw error;
    }
  },

  // Validate application before submission
  async validateApplication(applicationId: string): Promise<{ isValid: boolean; errors: any[] }> {
    try {
      // Get data from entity-specific APIs for validation
      let formData: Record<string, any> = {};
      console.warn('Application validation should use entity-specific APIs instead of form data service');
      // Continue with empty form data for now

      const errors: any[] = [];
      const requiredSections = ['applicantInfo', 'companyProfile', 'businessInfo', 'legalHistory'];

      // Check if all required sections are completed
      for (const section of requiredSections) {
        if (!formData[section] || Object.keys(formData[section]).length === 0) {
          errors.push(`${section} section is incomplete`);
        }
      }

      return {
        isValid: errors.length === 0,
        errors
      };
    } catch (error) {
      console.error('Error validating application:', error);
      throw error;
    }
  },
};
