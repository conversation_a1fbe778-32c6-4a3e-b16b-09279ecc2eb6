import { AxiosInstance } from 'axios';
import { authApiClient } from '../lib/apiClient';
import { processApiResponse } from '@/lib/authUtils';

export interface LoginData {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  middle_name?: string;
  phone: string;
  organization?: string;
}

export interface User {
  user_id: string;
  email: string;
  first_name: string;
  last_name: string;
  middle_name?: string;
  phone?: string;
  status?: string;
  profile_image?: string;
  roles: string[]; // Array of role names from the API
  isAdmin?: boolean; // Computed property for backward compatibility
  two_factor_enabled?: boolean;
}

export interface AuthResponse {
  access_token: string;
  user: User;
}

export interface ForgotPasswordData {
  email: string;
}

export interface ResetPasswordData {
  user_id: string;
  code: string;
  new_password: string;
  unique: string;
}

export interface TwoFactorData {
  user_id: string;
  code: string;
  unique: string;
}

export interface SetUpTwoFactorData {
  access_token: string;
  user_id: string;
}

type TwoFactorResponse = {
  access_token: string;
  user: {
    user_id: string;
    email: string;
    first_name: string;
    last_name: string;
    two_factor_enabled: boolean;
    roles: string[];
  };
  message: string;
};

type SetupTwoFactorAuthResponse = {
  otp_auth_url: string;
  qr_code_data_url: string;
  secret: string;
  message: string;
}


class AuthService {
  private api: AxiosInstance;

  constructor() {
    // Use the centralized auth API client with proper error handling
    this.api = authApiClient;
  }

  async login(data: LoginData): Promise<AuthResponse> {
    const response = await this.api.post('/login', data);

    // The backend returns data directly, not nested in a data property
    const authData = processApiResponse(response);

    // Check if the auth data is empty (which indicates an error)
    if (!authData || Object.keys(authData).length === 0) {
      throw new Error('Authentication failed - invalid credentials');
    }

    // Validate that we have the required fields
    if (!authData.access_token || !authData.user) {
      throw new Error('Authentication failed - incomplete response');
    }

    return authData;
  }

  async register(data: RegisterData): Promise<AuthResponse> {
    const response = await this.api.post('/register', data);

    // The backend returns data directly, not nested in a data property
    return response.data;
  }

  async forgotPassword(data: ForgotPasswordData): Promise<{ message: string }> {
    const response = await this.api.post('/forgot-password', data);
    return response.data;
  }

  async resetPassword(data: ResetPasswordData): Promise<{ message: string }> {
    try {
      console.log('🔄 Calling reset password API:', { ...data, new_password: '***' });
      const response = await this.api.post('/reset-password', data);
      console.log('✅ Reset password API response:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Reset password API error:', error);
      throw error;
    }
  }

  async verify2FA(data: TwoFactorData): Promise<TwoFactorResponse> {
    try {
      console.log('🔄 Calling verify 2FA API:', data);
      const response = await this.api.post('/verify-2fa', data);
      console.log('✅ Verify 2FA API response:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Verify 2FA API error:', error);
      throw error;
    }
  }

  async verifyEmail(data: TwoFactorData): Promise<{ message: string }> {
    const response = await this.api.post('/verify-email', data);
    return response.data;
  }

  async setupTwoFactorAuth(data: SetUpTwoFactorData): Promise<SetupTwoFactorAuthResponse> {
    const response = await this.api.post('/setup-2fa', data);
    return response.data;
  }


  async verifyTwoFactorCode(data: TwoFactorData): Promise<{ message: string }> {
    const response = await this.api.post('/verify-2fa', data);
    return response.data;
  }

  async generateTwoFactorCode(userId: string, action: string): Promise<{ message: string }> {
    const response = await this.api.post('/generate-2fa', { user_id: userId, action });
    return response.data;
  }

  async refreshToken(): Promise<AuthResponse> {
    const response = await this.api.post('/refresh');
    return response.data;
  }

  setAuthToken(token: string): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('auth_token', token);
    }
  }

  getAuthToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('auth_token');
    }
    return null;
  }

  clearAuthToken(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_token');
    }
  }
}

export const authService = new AuthService();
