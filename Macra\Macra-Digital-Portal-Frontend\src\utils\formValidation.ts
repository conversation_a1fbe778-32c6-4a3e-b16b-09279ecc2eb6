// Form validation utilities

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => string | null;
}

export interface ValidationErrors {
  [key: string]: string;
}

export const validateField = (value: any, rules: ValidationRule): string | null => {
  // Required validation
  if (rules.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
    return 'This field is required';
  }

  // Skip other validations if field is empty and not required
  if (!value || (typeof value === 'string' && value.trim() === '')) {
    return null;
  }

  // String validations
  if (typeof value === 'string') {
    // Min length validation
    if (rules.minLength && value.length < rules.minLength) {
      return `Must be at least ${rules.minLength} characters`;
    }

    // Max length validation
    if (rules.maxLength && value.length > rules.maxLength) {
      return `Must be no more than ${rules.maxLength} characters`;
    }

    // Pattern validation
    if (rules.pattern && !rules.pattern.test(value)) {
      return 'Invalid format';
    }
  }

  // Custom validation
  if (rules.custom) {
    return rules.custom(value);
  }

  return null;
};

export const validateForm = (data: any, rules: { [key: string]: ValidationRule }): ValidationErrors => {
  const errors: ValidationErrors = {};

  Object.keys(rules).forEach(field => {
    const error = validateField(data[field], rules[field]);
    if (error) {
      errors[field] = error;
    }
  });

  return errors;
};

// Common validation patterns
export const patterns = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^(\+265|0)[0-9]{8,9}$/,
  url: /^https?:\/\/.+/,
  alphanumeric: /^[a-zA-Z0-9]+$/,
  alphabetic: /^[a-zA-Z\s]+$/,
  numeric: /^[0-9]+$/,
  percentage: /^(100|[1-9]?[0-9])$/
};

// Common validation rules
export const commonRules = {
  required: { required: true },
  email: { 
    required: true, 
    pattern: patterns.email,
    custom: (value: string) => {
      if (value && !patterns.email.test(value)) {
        return 'Please enter a valid email address';
      }
      return null;
    }
  },
  phone: {
    required: true,
    pattern: patterns.phone,
    custom: (value: string) => {
      if (value && !patterns.phone.test(value)) {
        return 'Please enter a valid Malawi phone number (e.g., +265123456789 or 0123456789)';
      }
      return null;
    }
  },
  businessRegistration: {
    required: true,
    minLength: 5,
    custom: (value: string) => {
      if (value && value.length < 5) {
        return 'Business registration number must be at least 5 characters';
      }
      return null;
    }
  },
  percentage: {
    pattern: patterns.percentage,
    custom: (value: string) => {
      if (value && !patterns.percentage.test(value)) {
        return 'Please enter a valid percentage (0-100)';
      }
      return null;
    }
  }
};

// Utility function to check if form has errors
export const hasErrors = (errors: ValidationErrors): boolean => {
  return Object.keys(errors).length > 0;
};

// Utility function to get first error message
export const getFirstError = (errors: ValidationErrors): string | null => {
  const firstKey = Object.keys(errors)[0];
  return firstKey ? errors[firstKey] : null;
};

// Utility function to validate array fields
export const validateArrayField = (
  array: any[], 
  rules: { [key: string]: ValidationRule },
  minItems?: number,
  maxItems?: number
): { [key: string]: ValidationErrors } => {
  const arrayErrors: { [key: string]: ValidationErrors } = {};

  // Check array length
  if (minItems && array.length < minItems) {
    arrayErrors._array = { length: `Must have at least ${minItems} items` };
  }
  if (maxItems && array.length > maxItems) {
    arrayErrors._array = { length: `Must have no more than ${maxItems} items` };
  }

  // Validate each item in array
  array.forEach((item, index) => {
    const itemErrors = validateForm(item, rules);
    if (hasErrors(itemErrors)) {
      arrayErrors[index] = itemErrors;
    }
  });

  return arrayErrors;
};

// File validation
export const validateFile = (
  file: File | null, 
  required: boolean = false,
  maxSize: number = 10, // MB
  allowedTypes: string[] = ['.pdf']
): string | null => {
  if (required && !file) {
    return 'File is required';
  }

  if (!file) {
    return null;
  }

  // Check file size
  if (file.size > maxSize * 1024 * 1024) {
    return `File size must be less than ${maxSize}MB`;
  }

  // Check file type
  const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
  if (!allowedTypes.includes(fileExtension)) {
    return `File type must be: ${allowedTypes.join(', ')}`;
  }

  return null;
};

// Section validation interface
export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
}

// Validate section data for application forms
export const validateSection = (data: Record<string, any>, sectionName: string): ValidationResult => {
  const errors: Record<string, string> = {};

  switch (sectionName) {
    case 'applicantInfo':
      // Required fields for applicant info - matching the actual form fields
      const applicantRequiredFields = [
        'name', 'business_registration_number', 'tpin', 'email', 'phone',
        'date_incorporation', 'place_incorporation'
      ];

      applicantRequiredFields.forEach(field => {
        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {
          errors[field] = `${field.replace(/_/g, ' ')} is required`;
        }
      });

      // Email validation
      if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
        errors.email = 'Please enter a valid email address';
      }

      // Website validation (optional field)
      if (data.website && data.website.trim() !== '') {
        if (!/^https?:\/\/.+/.test(data.website)) {
          errors.website = 'Please enter a valid website URL (starting with http:// or https://)';
        }
      }

      // Phone validation - using backend validation pattern
      if (data.phone) {
        if (!/^[+]?[\d\s\-()]+$/.test(data.phone)) {
            errors.phone = 'Phone number can only contain numbers, spaces, dashes, and parentheses';
        } else if (data.phone.trim().length < 10) {
          errors.phone = 'Phone number must be at least 10 characters long';
        } else if (data.phone.trim().length > 20) {
          errors.phone = 'Phone number must be no more than 20 characters long';
        }
      }

      // Fax validation (optional field)
      if (data.fax && data.fax.trim() !== '') {
        if (!/^[+]?[\d\s\-()]+$/.test(data.fax)) {
            errors.fax = 'Fax number can only contain numbers, spaces, dashes, and parentheses';
        } else if (data.fax.trim().length < 10) {
          errors.fax = 'Fax number must be at least 10 characters long';
        } else if (data.fax.trim().length > 20) {
          errors.fax = 'Fax number must be no more than 20 characters long';
        }
      }

      // Level of insurance cover validation (optional field)
      if (data.level_of_insurance_cover && data.level_of_insurance_cover.trim() !== '' && 
          data.level_of_insurance_cover.trim().length < 3) {
        errors.level_of_insurance_cover = 'Please provide a valid insurance cover amount';
      }

      // Date validation
      if (data.date_incorporation && !/^\d{4}-\d{2}-\d{2}$/.test(data.date_incorporation)) {
        errors.date_incorporation = 'Please enter a valid date (YYYY-MM-DD)';
      }

      break;

    case 'companyProfile':
      const companyRequiredFields = [
        'company_name', 'business_registration_number', 'tax_number', 'company_type',
        'incorporation_date', 'incorporation_place', 'company_email', 'company_phone',
        'company_address', 'company_city', 'company_district', 'number_of_employees',
        'annual_revenue', 'business_description'
      ];

      companyRequiredFields.forEach(field => {
        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {
          errors[field] = `${field.replace(/_/g, ' ')} is required`;
        }
      });

      // Email validation
      if (data.company_email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.company_email)) {
        errors.company_email = 'Please enter a valid email address';
      }

      break;

    case 'businessInfo':
      const businessRequiredFields = [
        'business_model', 'operational_structure', 'target_market', 'competitive_advantage',
        'facilities_description', 'equipment_description', 'operational_areas',
        'service_delivery_model', 'quality_assurance', 'customer_support'
      ];

      businessRequiredFields.forEach(field => {
        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {
          errors[field] = `${field.replace(/_/g, ' ')} is required`;
        }
      });

      break;

    case 'serviceScope':
      const serviceScopeRequiredFields = [
        'services_offered', 'geographic_coverage', 'service_categories',
        'target_customers', 'service_capacity'
      ];

      serviceScopeRequiredFields.forEach(field => {
        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {
          errors[field] = `${field.replace(/_/g, ' ')} is required`;
        }
      });

      break;

    case 'businessPlan':
      const businessPlanRequiredFields = [
        'executive_summary', 'market_analysis', 'financial_projections',
        'revenue_model', 'investment_requirements', 'implementation_timeline',
        'risk_analysis', 'success_metrics'
      ];

      businessPlanRequiredFields.forEach(field => {
        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {
          errors[field] = `${field.replace(/_/g, ' ')} is required`;
        }
      });

      break;

    case 'legalHistory':
      // Required fields
      if (!data.compliance_record || data.compliance_record.trim() === '') {
        errors.compliance_record = 'Compliance record is required';
      }

      // Declaration must be accepted
      if (!data.declaration_accepted) {
        errors.declaration_accepted = 'You must accept the declaration to proceed';
      }

      // Conditional validations
      if (data.criminal_history && (!data.criminal_details || data.criminal_details.trim() === '')) {
        errors.criminal_details = 'Please provide details of your criminal history';
      }

      if (data.bankruptcy_history && (!data.bankruptcy_details || data.bankruptcy_details.trim() === '')) {
        errors.bankruptcy_details = 'Please provide details of your bankruptcy history';
      }

      if (data.regulatory_actions && (!data.regulatory_details || data.regulatory_details.trim() === '')) {
        errors.regulatory_details = 'Please provide details of regulatory actions';
      }

      if (data.litigation_history && (!data.litigation_details || data.litigation_details.trim() === '')) {
        errors.litigation_details = 'Please provide details of litigation history';
      }

      break;

    case 'address':
      // Required fields for address information
      const addressRequiredFields = [
        'address_line_1', 'city', 'country'
      ];

      addressRequiredFields.forEach(field => {
        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {
          errors[field] = `${field.replace(/_/g, ' ')} is required`;
        }
      });

      break;

    case 'contactInfo':
      // Required fields for contact information
      const contactRequiredFields = [
        'primary_contact_first_name', 'primary_contact_last_name', 'primary_contact_designation',
        'primary_contact_email', 'primary_contact_phone'
      ];

      contactRequiredFields.forEach(field => {
        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {
          errors[field] = `${field.replace(/_/g, ' ')} is required`;
        }
      });

      // Email validation for primary contact
      if (data.primary_contact_email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.primary_contact_email)) {
        errors.primary_contact_email = 'Please enter a valid email address';
      }

      // Email validation for secondary contact (if provided)
      if (data.secondary_contact_email && data.secondary_contact_email.trim() !== '' &&
          !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.secondary_contact_email)) {
        errors.secondary_contact_email = 'Please enter a valid email address';
      }



      // Phone validation
      if (data.primary_contact_phone && !/^[+]?[\d\s\-()]+$/.test(data.primary_contact_phone)) {
        errors.primary_contact_phone = 'Please enter a valid phone number';
      }

      if (data.secondary_contact_phone && data.secondary_contact_phone.trim() !== '' &&
          !/^[+]?[\d\s\-()]+$/.test(data.secondary_contact_phone)) {
        errors.secondary_contact_phone = 'Please enter a valid phone number';
      }



      break;

    default:
      // No validation for unknown sections
      break;
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};
