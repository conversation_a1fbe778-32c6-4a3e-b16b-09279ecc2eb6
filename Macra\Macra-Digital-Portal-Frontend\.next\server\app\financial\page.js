(()=>{var e={};e.id=2482,e.ids=[2482],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7827:(e,t,r)=>{Promise.resolve().then(r.bind(r,39720))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},15905:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var a=r(60687),s=r(43210),i=r(16189),n=r(63213),d=r(21891),l=r(60417);function o({children:e}){let{isAuthenticated:t,loading:r}=(0,n.A)();(0,i.useRouter)();let[o,c]=(0,s.useState)("overview"),[x,m]=(0,s.useState)(!1);return r?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"})}):t?(0,a.jsxs)("div",{className:"flex h-screen overflow-hidden",children:[(0,a.jsx)("div",{id:"mobileSidebarOverlay",className:`mobile-sidebar-overlay ${x?"show":""}`,onClick:()=>m(!1)}),(0,a.jsx)(l.A,{}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,a.jsx)(d.A,{activeTab:o,onTabChange:c,onMobileMenuToggle:()=>{m(!x)}}),e]})]}):null}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33715:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\financial\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\financial\\layout.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},34932:(e,t,r)=>{Promise.resolve().then(r.bind(r,15905))},37390:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var a=r(60687),s=r(43210),i=r(63213);function n(){let{user:e}=(0,i.A)(),[t,r]=(0,s.useState)([]),[n,d]=(0,s.useState)(!0),[l,o]=(0,s.useState)({totalRevenue:0,monthlyRevenue:0,pendingPayments:0,overduePayments:0}),c=e?.role?.name==="ADMINISTRATOR",x=e?.role?.name==="ACCOUNTANT",m=e=>new Intl.NumberFormat("en-MW",{style:"currency",currency:"MWK"}).format(e),g=e=>{let t={Completed:"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200",Pending:"bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200",Failed:"bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200",Refunded:"bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200"};return(0,a.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${t[e]||t.Pending}`,children:e})};return c||x?n?(0,a.jsx)("div",{className:"flex items-center justify-center h-64 bg-gray-50 dark:bg-gray-900",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 dark:border-red-500"})}):(0,a.jsxs)("div",{className:"p-6 bg-gray-50 dark:bg-gray-900 min-h-screen",children:[(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:"Financial Management"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Monitor revenue, transactions, and financial performance"})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsxs)("button",{type:"button",className:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,a.jsx)("i",{className:"ri-download-line mr-2"}),"Export Report"]}),c&&(0,a.jsxs)("button",{type:"button",className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800",children:[(0,a.jsx)("i",{className:"ri-add-line mr-2"}),"Manual Entry"]})]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-green-100 dark:bg-green-900 rounded-lg",children:(0,a.jsx)("i",{className:"ri-money-dollar-circle-line text-2xl text-green-600 dark:text-green-400"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Total Revenue"}),(0,a.jsx)("p",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:m(l.totalRevenue)})]})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg",children:(0,a.jsx)("i",{className:"ri-calendar-line text-2xl text-blue-600 dark:text-blue-400"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Monthly Revenue"}),(0,a.jsx)("p",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:m(l.monthlyRevenue)})]})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg",children:(0,a.jsx)("i",{className:"ri-time-line text-2xl text-yellow-600 dark:text-yellow-400"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Pending Payments"}),(0,a.jsx)("p",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:m(l.pendingPayments)})]})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-red-100 dark:bg-red-900 rounded-lg",children:(0,a.jsx)("i",{className:"ri-error-warning-line text-2xl text-red-600 dark:text-red-400"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Overdue Payments"}),(0,a.jsx)("p",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:m(l.overduePayments)})]})]})})]}),(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-700",children:(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Recent Transactions"})}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[(0,a.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Transaction"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Company"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Amount"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Type"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Method"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Date"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:t.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:e.id})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:e.company})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:m(e.amount)})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:e.type})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:e.method})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:g(e.status)}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:new Date(e.date).toLocaleDateString()}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex items-center justify-end space-x-2",children:[(0,a.jsx)("button",{type:"button",title:"View transaction",className:"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 p-1 rounded hover:bg-indigo-50 dark:hover:bg-indigo-900",children:(0,a.jsx)("i",{className:"ri-eye-line"})}),c&&(0,a.jsx)("button",{type:"button",title:"Edit transaction",className:"text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300 p-1 rounded hover:bg-green-50 dark:hover:bg-green-900",children:(0,a.jsx)("i",{className:"ri-edit-line"})})]})})]},e.id))})]})})]})]}):(0,a.jsx)("div",{className:"flex items-center justify-center h-64 bg-gray-50 dark:bg-gray-900",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("i",{className:"ri-error-warning-line text-4xl text-red-600 dark:text-red-500 mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Access Denied"}),(0,a.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"You don't have permission to view financial data."})]})})}},39720:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\financial\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\financial\\page.tsx","default")},43129:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>o});var a=r(65239),s=r(48088),i=r(88170),n=r.n(i),d=r(30893),l={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>d[e]);r.d(t,l);let o={children:["",{children:["financial",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,39720)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\financial\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,33715)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\financial\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\financial\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/financial/page",pathname:"/financial",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},44779:(e,t,r)=>{Promise.resolve().then(r.bind(r,37390))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},98548:(e,t,r)=>{Promise.resolve().then(r.bind(r,33715))}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,7498,1658,5814,7563,6606],()=>r(43129));module.exports=a})();