/* [project]/src/styles/dashboard.css [app-client] (css) */
.progress-bar-72 {
  width: 72%;
}

.progress-bar-45 {
  width: 45%;
}

.progress-bar-10 {
  width: 10%;
}

.progress-bar-20 {
  width: 20%;
}

.progress-bar-25 {
  width: 25%;
}

.progress-bar-30 {
  width: 30%;
}

.progress-bar-40 {
  width: 40%;
}

.progress-bar-50 {
  width: 50%;
}

.progress-bar-60 {
  width: 60%;
}

.progress-bar-70 {
  width: 70%;
}

.progress-bar-75 {
  width: 75%;
}

.progress-bar-80 {
  width: 80%;
}

.progress-bar-90 {
  width: 90%;
}

.progress-bar-100 {
  width: 100%;
}

.progress-container {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
  border-radius: 9999px;
  width: 100%;
  height: .5rem;
  margin-top: .25rem;
}

:is(:where(.dark) .progress-container) {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity));
}

.progress-fill {
  border-radius: 9999px;
  height: .5rem;
}

.progress-green {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity));
}

:is(:where(.dark) .progress-green) {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity));
}

.progress-yellow {
  --tw-bg-opacity: 1;
  background-color: rgb(202 138 4 / var(--tw-bg-opacity));
}

:is(:where(.dark) .progress-yellow) {
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity));
}

.progress-red {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity));
}

:is(:where(.dark) .progress-red) {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity));
}

.progress-blue {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity));
}

:is(:where(.dark) .progress-blue) {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
}

.spectrum-utilization-card {
  --tw-border-opacity: 1;
  border-width: 1px;
  border-color: rgb(229 231 235 / var(--tw-border-opacity));
  border-radius: 16px;
  padding: 1rem;
}

:is(:where(.dark) .spectrum-utilization-card) {
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity));
}

.spectrum-band-title {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
  font-weight: 500;
}

:is(:where(.dark) .spectrum-band-title) {
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity));
}

.spectrum-band-range {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
  font-size: .875rem;
  line-height: 1.25rem;
}

:is(:where(.dark) .spectrum-band-range) {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

.spectrum-utilization-stats {
  justify-content: space-between;
  font-size: .875rem;
  line-height: 1.25rem;
  display: flex;
}

.spectrum-utilization-label {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

:is(:where(.dark) .spectrum-utilization-label) {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

.spectrum-utilization-value {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}

:is(:where(.dark) .spectrum-utilization-value) {
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity));
}


/*# sourceMappingURL=src_styles_dashboard_2f938b4d.css.map*/