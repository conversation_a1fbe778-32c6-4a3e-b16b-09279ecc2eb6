1:"$Sreact.fragment"
2:I[70933,["8122","static/chunks/8122-f2e6a12781bb7001.js","6766","static/chunks/6766-fe45bd29b2b561d6.js","8006","static/chunks/8006-5b7ea5677a5eb858.js","283","static/chunks/283-0ac9568efbe0d41c.js","7177","static/chunks/app/layout-e950a206ead44412.js"],"default"]
3:I[58818,["8122","static/chunks/8122-f2e6a12781bb7001.js","6766","static/chunks/6766-fe45bd29b2b561d6.js","8006","static/chunks/8006-5b7ea5677a5eb858.js","283","static/chunks/283-0ac9568efbe0d41c.js","7177","static/chunks/app/layout-e950a206ead44412.js"],"default"]
4:I[87555,[],""]
5:I[31295,[],""]
6:I[13568,["8122","static/chunks/8122-f2e6a12781bb7001.js","6766","static/chunks/6766-fe45bd29b2b561d6.js","8006","static/chunks/8006-5b7ea5677a5eb858.js","283","static/chunks/283-0ac9568efbe0d41c.js","7177","static/chunks/app/layout-e950a206ead44412.js"],"Toaster"]
7:I[90894,[],"ClientPageRoot"]
8:I[48374,["8122","static/chunks/8122-f2e6a12781bb7001.js","6766","static/chunks/6766-fe45bd29b2b561d6.js","6874","static/chunks/6874-fa436bac5997856d.js","283","static/chunks/283-0ac9568efbe0d41c.js","3209","static/chunks/3209-804aaeb82cd8cdac.js","490","static/chunks/490-41cfb828fbb0867f.js","6584","static/chunks/app/customer/data-protection/page-5ba549adf8c5a031.js"],"default"]
b:I[59665,[],"MetadataBoundary"]
d:I[59665,[],"OutletBoundary"]
10:I[74911,[],"AsyncMetadataOutlet"]
12:I[59665,[],"ViewportBoundary"]
14:I[26614,[],""]
:HL["/_next/static/css/90015dfbfdf6a8a6.css","style"]
:HL["/_next/static/css/9ed0b7741a7b29db.css","style"]
:HL["/_next/static/css/b353e70df8138853.css","style"]
:HL["/_next/static/css/030565f5b7d12790.css","style"]
0:{"P":null,"b":"Z4Qo1hQDb_ctiQJcvT6qH","p":"","c":["","customer","data-protection"],"i":false,"f":[[["",{"children":["customer",{"children":["data-protection",{"children":["__PAGE__",{}]}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/90015dfbfdf6a8a6.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/9ed0b7741a7b29db.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}],["$","link","2",{"rel":"stylesheet","href":"/_next/static/css/b353e70df8138853.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}],["$","link","3",{"rel":"stylesheet","href":"/_next/static/css/030565f5b7d12790.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","className":"__variable_549949","suppressHydrationWarning":true,"children":[["$","head",null,{"children":[["$","script",null,{"dangerouslySetInnerHTML":{"__html":"\n              (function() {\n                try {\n                  const savedTheme = localStorage.getItem('theme');\n                  const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n\n                  if (\n                    savedTheme === 'dark' || \n                    (savedTheme === 'system' && systemPrefersDark) || \n                    (!savedTheme && systemPrefersDark)\n                  ) {\n                    document.documentElement.classList.add('dark');\n                  } else {\n                    document.documentElement.classList.remove('dark');\n                  }\n                } catch (e) {\n                  document.documentElement.classList.remove('dark');\n                }\n              })();\n            "}}],["$","link",null,{"rel":"preconnect","href":"https://cdnjs.cloudflare.com"}],["$","link",null,{"rel":"stylesheet","href":"https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"}],["$","link",null,{"rel":"stylesheet","href":"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css","integrity":"sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==","crossOrigin":"anonymous","referrerPolicy":"no-referrer"}],["$","meta",null,{"name":"viewport","content":"width=device-width, initial-scale=1"}],["$","meta",null,{"httpEquiv":"X-UA-Compatible","content":"IE=edge"}]]}],["$","body",null,{"className":"__className_549949 antialiased","suppressHydrationWarning":true,"children":[["$","$L2",null,{"children":["$","$L3",null,{"children":["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}],["$","$L6",null,{"position":"top-right","toastOptions":{"duration":4000,"style":{"background":"#363636","color":"#fff"},"success":{"duration":3000,"style":{"background":"#10b981","color":"#fff"}},"error":{"duration":5000,"style":{"background":"#ef4444","color":"#fff"}}}}]]}]]}]]}],{"children":["customer",["$","$1","c",{"children":[null,["$","div",null,{"className":"min-h-screen bg-gray-50","children":["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]}]]}],{"children":["data-protection",["$","$1","c",{"children":[null,["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","$L7",null,{"Component":"$8","searchParams":{},"params":{},"promises":["$@9","$@a"]}],["$","$Lb",null,{"children":"$Lc"}],null,["$","$Ld",null,{"children":["$Le","$Lf",["$","$L10",null,{"promise":"$@11"}]]}]]}],{},null,false]},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","8QRmLozZJ2Cm5fwDQKYjo",{"children":[["$","$L12",null,{"children":"$L13"}],null]}],null]}],false]],"m":"$undefined","G":["$14","$undefined"],"s":false,"S":true}
15:"$Sreact.suspense"
16:I[74911,[],"AsyncMetadata"]
9:{}
a:{}
c:["$","$15",null,{"fallback":null,"children":["$","$L16",null,{"promise":"$@17"}]}]
f:null
13:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
e:null
17:{"metadata":[["$","title","0",{"children":"Customer Dashboard - Digital Portal"}],["$","meta","1",{"name":"description","content":"Customer portal for managing licenses and applications"}],["$","link","2",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"256x256"}]],"error":null,"digest":"$undefined"}
11:{"metadata":"$17:metadata","error":null,"digest":"$undefined"}
