(()=>{var e={};e.id=2990,e.ids=[2990],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3639:(e,t,r)=>{Promise.resolve().then(r.bind(r,32022))},7156:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(60687),a=r(43210),i=r(12234);function n(){let[e,t]=(0,a.useState)({}),[r,n]=(0,a.useState)(!1),o=async(e,r="GET")=>{n(!0);try{let s;s="GET"===r?await i.uE.get(e):await i.uE.post(e,{title:"Test Complaint",description:"This is a test complaint for API testing",category:"Other"}),t(t=>({...t,[e]:{success:!0,data:s.data,status:s.status,dataType:typeof s.data.data,isArray:Array.isArray(s.data.data)}}))}catch(r){t(t=>({...t,[e]:{success:!1,error:r.message,status:r.response?.status,data:r.response?.data}}))}finally{n(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 p-8",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-8",children:"API Endpoint Testing"}),(0,s.jsx)("div",{className:"space-y-4 mb-8",children:[{path:"/consumer-affairs-complaints",method:"GET"},{path:"/data-breach-reports",method:"GET"},{path:"/consumer-affairs-complaints",method:"POST"}].map(e=>(0,s.jsx)("button",{onClick:()=>o(e.path,e.method),disabled:r,className:"w-full text-left p-4 bg-white dark:bg-gray-800 rounded-lg shadow border hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50",children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("span",{className:"font-medium",children:[e.method," ",e.path]}),(0,s.jsx)("span",{className:"text-sm text-gray-500",children:r?"Testing...":"Click to test"})]})},`${e.method}-${e.path}`))}),(0,s.jsx)("div",{className:"space-y-6",children:Object.entries(e).map(([e,t])=>(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold mb-4 flex items-center",children:[e,(0,s.jsx)("span",{className:`ml-2 px-2 py-1 text-xs rounded ${t.success?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"}`,children:t.success?"SUCCESS":"ERROR"}),t.status&&(0,s.jsx)("span",{className:"ml-2 px-2 py-1 text-xs bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 rounded",children:t.status})]}),(0,s.jsx)("div",{className:"bg-gray-50 dark:bg-gray-900 rounded p-4 overflow-auto",children:(0,s.jsx)("pre",{className:"text-sm text-gray-700 dark:text-gray-300",children:JSON.stringify(t,null,2)})})]},e))}),(0,s.jsxs)("div",{className:"mt-8 p-4 bg-blue-50 dark:bg-blue-900 rounded-lg",children:[(0,s.jsx)("h3",{className:"font-semibold text-blue-900 dark:text-blue-100 mb-2",children:"Instructions:"}),(0,s.jsxs)("ul",{className:"text-sm text-blue-800 dark:text-blue-200 space-y-1",children:[(0,s.jsx)("li",{children:"• Make sure you're logged in before testing"}),(0,s.jsx)("li",{children:"• Check the browser console for detailed logs"}),(0,s.jsx)("li",{children:"• Backend should be running on port 3001"}),(0,s.jsx)("li",{children:"• Test credentials: <EMAIL> / Password123!"})]})]})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32022:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\test-api\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\test-api\\page.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56783:(e,t,r)=>{Promise.resolve().then(r.bind(r,7156))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"256x256",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},80665:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>l});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),o=r(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(t,d);let l={children:["",{children:["test-api",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,32022)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\test-api\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\test-api\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/test-api/page",pathname:"/test-api",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,7498,1658,2335],()=>r(80665));module.exports=s})();