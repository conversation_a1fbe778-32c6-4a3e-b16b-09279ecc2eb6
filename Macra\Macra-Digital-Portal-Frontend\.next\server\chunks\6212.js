"use strict";exports.id=6212,exports.ids=[6212],exports.modules={36212:(e,t,a)=>{a.d(t,{dr:()=>u,ef:()=>c});var s=a(51060),r=a(15659),i=a(51278);let n=process.env.NEXT_PUBLIC_API_URL||"http://localhost:3001",p=s.A.create({baseURL:n,timeout:15e3,headers:{"Content-Type":"application/json",Accept:"application/json"}}),o=s.A.create({baseURL:`${n}/auth`,timeout:15e3,headers:{"Content-Type":"application/json",Accept:"application/json"}});o.interceptors.request.use(e=>e,e=>Promise.reject(e)),o.interceptors.response.use(e=>e,e=>Promise.reject(e)),p.interceptors.request.use(e=>{let t=r.A.get("auth_token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e)),p.interceptors.response.use(e=>e,async e=>{let t=e.config;if(e.response?.status===429&&!t._retry){t._retry=!0;let a=e.response.headers["retry-after"],s=a?1e3*parseInt(a):Math.min(1e3*Math.pow(2,t._retryCount||0),1e4);if(t._retryCount=(t._retryCount||0)+1,t._retryCount<=3)return await new Promise(e=>setTimeout(e,s)),p(t)}return e.response?.status===401&&(r.A.remove("auth_token"),r.A.remove("auth_user"),window.location.href="/auth/login"),Promise.reject(e)});class c{constructor(){this.pendingRequests=new Map,this.api=p}async deduplicateRequest(e,t){if(this.pendingRequests.has(e))return this.pendingRequests.get(e);let a=t().finally(()=>{this.pendingRequests.delete(e)});return this.pendingRequests.set(e,a),a}setAuthToken(e){this.api.defaults.headers.common.Authorization=`Bearer ${e}`}removeAuthToken(){delete this.api.defaults.headers.common.Authorization}async logout(){let e=await o.post("/logout");return(0,i.zp)(e)}async refreshToken(){let e=await o.post("/refresh");return(0,i.zp)(e)}async generateTwoFactorCode(e,t){let a=await o.post("/generate-2fa",{user_id:e,action:t});return(0,i.zp)(a)}async verify2FA(e){let t=await o.post("/verify-2fa",e);if((0,i.zp)(t)?.data){let e=(0,i.zp)(t).data;return{access_token:e.access_token,user:{id:e.user.user_id,firstName:e.user.first_name,lastName:e.user.last_name,email:e.user.email,roles:e.user.roles||[],isAdmin:(e.user.roles||[]).includes("administrator"),profileImage:e.user.profile_image,createdAt:e.user.created_at||new Date().toISOString(),lastLogin:e.user.last_login,organizationName:e.user.organization_name,two_factor_enabled:e.user.two_factor_enabled}}}return(0,i.zp)(t)}async setupTwoFactorAuth(e){let t=await o.post("/setup-2fa",e);return(0,i.zp)(t)}async getProfile(){return this.deduplicateRequest("getProfile",async()=>{let e=await this.api.get("/users/profile");return(0,i.zp)(e)})}async updateProfile(e){let t=await this.api.put("/users/profile",e);return(0,i.zp)(t)}async getAddresses(){let e=await this.api.get("/address/all");return(0,i.zp)(e)}async createAddress(e){let t=await this.api.post("/address/create",e);return(0,i.zp)(t)}async getAddress(e){let t=await this.api.get(`/address/${e}`);return(0,i.zp)(t)}async editAddress(e){let{address_id:t,...a}=e;if(!t)throw Error("Address ID is required for updating");let s=await this.api.put(`/address/${t}`,a);return(0,i.zp)(s)}async getAddressesByEntity(e,t){let a=await this.api.get(`/address/all?entity_type=${encodeURIComponent(e)}&entity_id=${encodeURIComponent(t)}`);return(0,i.zp)(a)}async deleteAddress(e){let t=await this.api.delete(`/address/soft/${e}`);return(0,i.zp)(t)}async searchPostcodes(e){let t=await this.api.post("/postal-codes/search",e);return(0,i.zp)(t)}async getLicenses(e){let t=await this.api.get("/licenses",{params:e});return(0,i.zp)(t)}async getLicense(e){let t=await this.api.get(`/licenses/${e}`);return(0,i.zp)(t)}async createLicenseApplication(e){let t=await this.api.post("/license-applications",e);return(0,i.zp)(t)}async getLicenseTypes(e){let t=await this.api.get("/license-types",{params:e});return(0,i.zp)(t)}async getLicenseType(e){let t=await this.api.get(`/license-types/${e}`);return(0,i.zp)(t)}async getLicenseCategories(e){let t=await this.api.get("/license-categories",{params:e});return(0,i.zp)(t)}async getLicenseCategoriesByType(e){let t=await this.api.get(`/license-categories/by-license-type/${e}`);return(0,i.zp)(t)}async getLicenseCategoryTree(e){let t=await this.api.get(`/license-categories/license-type/${e}/tree`);return(0,i.zp)(t)}async getLicenseCategory(e){let t=await this.api.get(`/license-categories/${e}`);return(0,i.zp)(t)}async getApplications(e){let t=await this.api.get("/applications",{params:e});return(0,i.zp)(t)}async getApplication(e){let t=await this.api.get(`/applications/${e}`);return(0,i.zp)(t)}async createApplication(e){let t=await this.api.post("/applications",e);return(0,i.zp)(t)}async updateApplication(e,t){let a=await this.api.put(`/applications/${e}`,t);return(0,i.zp)(a)}async getPayments(e){let t=await this.api.get("/payments",{params:e});return(0,i.zp)(t)}async getPayment(e){let t=await this.api.get(`/payments/${e}`);return(0,i.zp)(t)}async createPayment(e){let t=await this.api.post("/payments",e);return(0,i.zp)(t)}async getDocuments(e){let t=await this.api.get("/documents",{params:e});return(0,i.zp)(t)}async uploadDocument(e){let t=await this.api.post("/documents/upload",e,{headers:{"Content-Type":"multipart/form-data"}});return(0,i.zp)(t)}async downloadDocument(e){let t=await this.api.get(`/documents/${e}/download`,{responseType:"blob"});return(0,i.zp)(t)}async getDashboardStats(){let e=await this.api.get("/dashboard/stats");return(0,i.zp)(e)}async getNotifications(e){let t=await this.api.get("/notifications",{params:e});return(0,i.zp)(t)}async markNotificationAsRead(e){let t=await this.api.patch(`/notifications/${e}/read`);return(0,i.zp)(t)}async getTenders(e){let t=await this.api.get("/procurement/tenders",{params:e});return(0,i.zp)(t)}async getTender(e){let t=await this.api.get(`/procurement/tenders/${e}`);return(0,i.zp)((0,i.zp)(t))}async payForTenderAccess(e,t){let a=await this.api.post(`/procurement/tenders/${e}/pay-access`,t);return(0,i.zp)((0,i.zp)(a))}async downloadTenderDocument(e){let t=await this.api.get(`/procurement/documents/${e}/download`,{responseType:"blob"});return(0,i.zp)((0,i.zp)(t))}async getMyBids(e){let t=await this.api.get("/procurement/my-bids",{params:e});return(0,i.zp)((0,i.zp)(t))}async getBid(e){let t=await this.api.get(`/procurement/bids/${e}`);return(0,i.zp)((0,i.zp)(t))}async submitBid(e){let t=await this.api.post("/procurement/bids",e,{headers:{"Content-Type":"multipart/form-data"}});return(0,i.zp)((0,i.zp)(t))}async updateBid(e,t){let a=await this.api.put(`/procurement/bids/${e}`,t,{headers:{"Content-Type":"multipart/form-data"}});return(0,i.zp)((0,i.zp)(a))}async getProcurementPayments(e){let t=await this.api.get("/procurement/payments",{params:e});return(0,i.zp)((0,i.zp)(t))}async getProcurementPayment(e){let t=await this.api.get(`/procurement/payments/${e}`);return(0,i.zp)((0,i.zp)(t))}async getComplaints(e){let t=await this.api.get("/consumer-affairs/complaints",{params:e});return(0,i.zp)((0,i.zp)(t))}async getComplaint(e){let t=await this.api.get(`/consumer-affairs/complaints/${e}`);return(0,i.zp)((0,i.zp)(t))}async submitComplaint(e){let t=new FormData;t.append("title",e.title),t.append("description",e.description),t.append("category",e.category),e.attachments&&e.attachments.forEach((e,a)=>{t.append(`attachments[${a}]`,e)});let a=await this.api.post("/consumer-affairs/complaints",t,{headers:{"Content-Type":"multipart/form-data"}});return(0,i.zp)((0,i.zp)(a))}async updateComplaint(e,t){let a=await this.api.put(`/consumer-affairs/complaints/${e}`,t);return(0,i.zp)((0,i.zp)(a))}async downloadComplaintAttachment(e,t){let a=await this.api.get(`/consumer-affairs/complaints/${e}/attachments/${t}/download`,{responseType:"blob"});return(0,i.zp)((0,i.zp)(a))}}let u=new c}};