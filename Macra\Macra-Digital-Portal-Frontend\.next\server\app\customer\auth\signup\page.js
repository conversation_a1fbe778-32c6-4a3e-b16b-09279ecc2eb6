(()=>{var e={};e.id=3063,e.ids=[3063],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},16849:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>f,tree:()=>c});var s=t(65239),a=t(48088),o=t(88170),n=t.n(o),i=t(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(r,d);let c={children:["",{children:["customer",{children:["auth",{children:["signup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,56799)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\auth\\signup\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,68736)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\auth\\signup\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},f=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/customer/auth/signup/page",pathname:"/customer/auth/signup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30877:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>f});var s=t(60687),a=t(76180),o=t.n(a),n=t(43210),i=t(16189),d=t(85814),c=t.n(d),l=t(30474),u=t(63213);function f(){let[e,r]=(0,n.useState)({first_name:"",last_name:"",email:"",phone:"",organization:"",password:"",confirmPassword:""}),[t,a]=(0,n.useState)(!1),[d,f]=(0,n.useState)(!1),[b,m]=(0,n.useState)(""),[p,h]=(0,n.useState)(""),{register:x}=(0,u.A)(),g=(0,i.useRouter)(),y=t=>{r({...e,[t.target.name]:t.target.value}),b&&m(""),p&&h("")},j=e=>{let r=[];return e.length<8&&r.push("At least 8 characters long"),r},v=async r=>{r.preventDefault(),f(!0),m(""),h("");let s=j(e.password);if(s.length>0){m(`Password must have: ${s.join(", ")}`),f(!1);return}if(e.password!==e.confirmPassword){m("Passwords do not match"),f(!1);return}if(!t){m("Please accept the Terms of Service and Privacy Policy"),f(!1);return}if(!/^\+?\d{10,15}$/.test(e.phone)){m("Please enter a valid phone number (10-15 digits, optionally starting with +)"),f(!1);return}try{let{confirmPassword:r,...t}=e;await x(t),h("Account created successfully! Please log in to continue."),setTimeout(()=>{g.push("/customer/auth/login")},2e3)}catch(r){let e="Registration failed. Please try again.";if("object"==typeof r&&null!==r&&"response"in r){if(r.response?.status===409)e="An account with this email address already exists. Please use a different email or try logging in.";else if(r.response?.status===422)e="An account with this email address already exists. Please use a different email or try logging in.";else if(r.response?.status===400){let t=r.response?.data?.message||"";e=t.toLowerCase().includes("email")&&(t.toLowerCase().includes("exists")||t.toLowerCase().includes("already"))?"An account with this email address already exists. Please use a different email or try logging in.":t||"Please check your information and try again."}else if(r.response?.data?.message){let t=r.response.data.message;e=t.toLowerCase().includes("email")&&(t.toLowerCase().includes("exists")||t.toLowerCase().includes("already")||t.toLowerCase().includes("duplicate")||t.toLowerCase().includes("taken"))?"An account with this email address already exists. Please use a different email or try logging in.":t}}else"object"==typeof r&&null!==r&&"message"in r&&"string"==typeof r.message&&(e=r.message);m(e)}finally{f(!1)}};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o(),{id:"5fb9e4356c15d0fb",children:".custom-scrollbar.jsx-5fb9e4356c15d0fb{scroll-behavior:smooth}.custom-scrollbar.jsx-5fb9e4356c15d0fb::-webkit-scrollbar{width:8px}.custom-scrollbar.jsx-5fb9e4356c15d0fb::-webkit-scrollbar-track{background:#f1f1f1;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px}.custom-scrollbar.jsx-5fb9e4356c15d0fb::-webkit-scrollbar-thumb{background:#dc2626;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px;-webkit-transition:background-color.2s ease;-moz-transition:background-color.2s ease;-o-transition:background-color.2s ease;transition:background-color.2s ease}.custom-scrollbar.jsx-5fb9e4356c15d0fb::-webkit-scrollbar-thumb:hover{background:#b91c1c}.dark.jsx-5fb9e4356c15d0fb .custom-scrollbar.jsx-5fb9e4356c15d0fb::-webkit-scrollbar-track{background:#374151}.dark.jsx-5fb9e4356c15d0fb .custom-scrollbar.jsx-5fb9e4356c15d0fb::-webkit-scrollbar-thumb{background:#ef4444}.dark.jsx-5fb9e4356c15d0fb .custom-scrollbar.jsx-5fb9e4356c15d0fb::-webkit-scrollbar-thumb:hover{background:#dc2626}input.jsx-5fb9e4356c15d0fb:focus,textarea.jsx-5fb9e4356c15d0fb:focus,select.jsx-5fb9e4356c15d0fb:focus{scroll-margin-top:2rem}.signup-input.jsx-5fb9e4356c15d0fb{-webkit-appearance:none;-moz-appearance:none;-ms-appearance:none;appearance:none;display:block;width:100%;padding:12px 16px;border:2px solid#d1d5db;-webkit-border-radius:.375rem;-moz-border-radius:.375rem;border-radius:.375rem;-webkit-box-shadow:0 1px 2px 0 rgba(0,0,0,.05);-moz-box-shadow:0 1px 2px 0 rgba(0,0,0,.05);box-shadow:0 1px 2px 0 rgba(0,0,0,.05);background-color:#f9fafb;color:#111827;-webkit-transition:all.2s ease;-moz-transition:all.2s ease;-o-transition:all.2s ease;transition:all.2s ease}.signup-input.jsx-5fb9e4356c15d0fb:hover{background-color:#fff}.signup-input.jsx-5fb9e4356c15d0fb:focus{outline:none;border-color:#dc2626;-webkit-box-shadow:0 0 0 2px rgba(220,38,38,.2);-moz-box-shadow:0 0 0 2px rgba(220,38,38,.2);box-shadow:0 0 0 2px rgba(220,38,38,.2)}.dark.jsx-5fb9e4356c15d0fb .signup-input.jsx-5fb9e4356c15d0fb{border-color:#4b5563;background-color:#374151;color:#f9fafb}.dark.jsx-5fb9e4356c15d0fb .signup-input.jsx-5fb9e4356c15d0fb:hover{background-color:#4b5563}.dark.jsx-5fb9e4356c15d0fb .signup-input.jsx-5fb9e4356c15d0fb::-webkit-input-placeholder{color:#9ca3af}.dark.jsx-5fb9e4356c15d0fb .signup-input.jsx-5fb9e4356c15d0fb:-moz-placeholder{color:#9ca3af}.dark.jsx-5fb9e4356c15d0fb .signup-input.jsx-5fb9e4356c15d0fb::-moz-placeholder{color:#9ca3af}.dark.jsx-5fb9e4356c15d0fb .signup-input.jsx-5fb9e4356c15d0fb:-ms-input-placeholder{color:#9ca3af}.dark.jsx-5fb9e4356c15d0fb .signup-input.jsx-5fb9e4356c15d0fb::-ms-input-placeholder{color:#9ca3af}.dark.jsx-5fb9e4356c15d0fb .signup-input.jsx-5fb9e4356c15d0fb::placeholder{color:#9ca3af}@media(max-width:640px){.signup-container.jsx-5fb9e4356c15d0fb{padding:1rem}}"}),(0,s.jsx)("div",{className:"jsx-5fb9e4356c15d0fb min-h-screen max-h-screen overflow-y-auto bg-gray-50 dark:bg-gray-900 custom-scrollbar",children:(0,s.jsxs)("div",{className:"jsx-5fb9e4356c15d0fb flex flex-col justify-center py-6 sm:py-12 px-4 sm:px-6 lg:px-8 min-h-full signup-container",children:[(0,s.jsxs)("div",{className:"jsx-5fb9e4356c15d0fb sm:mx-auto sm:w-full sm:max-w-2xl",children:[(0,s.jsx)("div",{className:"jsx-5fb9e4356c15d0fb flex justify-center",children:(0,s.jsx)(l.default,{src:"/images/macra-logo.png",alt:"MACRA Logo",width:64,height:64,className:"h-16 w-auto"})}),(0,s.jsx)("h2",{className:"jsx-5fb9e4356c15d0fb mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-gray-100",children:"Create your account"}),(0,s.jsxs)("p",{className:"jsx-5fb9e4356c15d0fb mt-2 text-center text-sm text-gray-600 dark:text-gray-400",children:["Or"," ",(0,s.jsx)(c(),{href:"/customer/auth/login",className:"font-medium text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300",children:"sign in to your existing account"})]})]}),(0,s.jsx)("div",{className:"jsx-5fb9e4356c15d0fb mt-6 sm:mt-8 sm:mx-auto sm:w-full sm:max-w-2xl",children:(0,s.jsxs)("div",{className:"jsx-5fb9e4356c15d0fb bg-white dark:bg-gray-800 py-6 sm:py-8 px-4 sm:px-10 shadow sm:rounded-lg border border-gray-200 dark:border-gray-700",children:[b&&(0,s.jsx)("div",{className:"jsx-5fb9e4356c15d0fb mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-md relative",children:(0,s.jsxs)("div",{className:"jsx-5fb9e4356c15d0fb flex justify-between items-start",children:[(0,s.jsx)("span",{className:"jsx-5fb9e4356c15d0fb flex-1",children:b}),(0,s.jsx)("button",{type:"button",onClick:()=>m(""),"aria-label":"Close error message",className:"jsx-5fb9e4356c15d0fb ml-2 text-red-400 hover:text-red-600 dark:text-red-500 dark:hover:text-red-300 focus:outline-none",children:(0,s.jsx)("svg",{fill:"currentColor",viewBox:"0 0 20 20",className:"jsx-5fb9e4356c15d0fb w-4 h-4",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd",className:"jsx-5fb9e4356c15d0fb"})})})]})}),p&&(0,s.jsx)("div",{className:"jsx-5fb9e4356c15d0fb mb-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-600 dark:text-green-400 px-4 py-3 rounded-md relative",children:(0,s.jsxs)("div",{className:"jsx-5fb9e4356c15d0fb flex justify-between items-start",children:[(0,s.jsx)("span",{className:"jsx-5fb9e4356c15d0fb flex-1",children:p}),(0,s.jsx)("button",{type:"button",onClick:()=>h(""),"aria-label":"Close success message",className:"jsx-5fb9e4356c15d0fb ml-2 text-green-400 hover:text-green-600 dark:text-green-500 dark:hover:text-green-300 focus:outline-none",children:(0,s.jsx)("svg",{fill:"currentColor",viewBox:"0 0 20 20",className:"jsx-5fb9e4356c15d0fb w-4 h-4",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd",className:"jsx-5fb9e4356c15d0fb"})})})]})}),(0,s.jsxs)("form",{onSubmit:v,className:"jsx-5fb9e4356c15d0fb space-y-4 sm:space-y-6",children:[(0,s.jsxs)("div",{className:"jsx-5fb9e4356c15d0fb grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-2 sm:gap-y-6",children:[(0,s.jsxs)("div",{className:"jsx-5fb9e4356c15d0fb",children:[(0,s.jsx)("label",{htmlFor:"first_name",className:"jsx-5fb9e4356c15d0fb block text-sm font-medium text-gray-700 dark:text-gray-300",children:"First name"}),(0,s.jsx)("div",{className:"jsx-5fb9e4356c15d0fb mt-1",children:(0,s.jsx)("input",{type:"text",name:"first_name",id:"first_name",autoComplete:"given-name",required:!0,value:e.first_name,onChange:y,className:"jsx-5fb9e4356c15d0fb appearance-none block w-full px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors"})})]}),(0,s.jsxs)("div",{className:"jsx-5fb9e4356c15d0fb",children:[(0,s.jsx)("label",{htmlFor:"last_name",className:"jsx-5fb9e4356c15d0fb block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Last name"}),(0,s.jsx)("div",{className:"jsx-5fb9e4356c15d0fb mt-1",children:(0,s.jsx)("input",{type:"text",name:"last_name",id:"last_name",autoComplete:"family-name",required:!0,value:e.last_name,onChange:y,className:"jsx-5fb9e4356c15d0fb appearance-none block w-full px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors"})})]})]}),(0,s.jsxs)("div",{className:"jsx-5fb9e4356c15d0fb grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-2 sm:gap-y-6",children:[(0,s.jsxs)("div",{className:"jsx-5fb9e4356c15d0fb",children:[(0,s.jsx)("label",{htmlFor:"email",className:"jsx-5fb9e4356c15d0fb block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Email address"}),(0,s.jsx)("div",{className:"jsx-5fb9e4356c15d0fb mt-1",children:(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e.email,onChange:y,className:"jsx-5fb9e4356c15d0fb appearance-none block w-full px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors"})})]}),(0,s.jsxs)("div",{className:"jsx-5fb9e4356c15d0fb",children:[(0,s.jsx)("label",{htmlFor:"phone",className:"jsx-5fb9e4356c15d0fb block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Phone Number"}),(0,s.jsx)("div",{className:"jsx-5fb9e4356c15d0fb mt-1",children:(0,s.jsx)("input",{id:"phone",name:"phone",type:"tel",required:!0,value:e.phone,onChange:y,placeholder:"+265123456789",className:"jsx-5fb9e4356c15d0fb appearance-none block w-full px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors"})})]})]}),(0,s.jsxs)("div",{className:"jsx-5fb9e4356c15d0fb",children:[(0,s.jsx)("label",{htmlFor:"organization",className:"jsx-5fb9e4356c15d0fb block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Organization (Optional)"}),(0,s.jsx)("div",{className:"jsx-5fb9e4356c15d0fb mt-1",children:(0,s.jsx)("input",{id:"organization",name:"organization",type:"text",value:e.organization,onChange:y,className:"jsx-5fb9e4356c15d0fb appearance-none block w-full px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors"})})]}),(0,s.jsxs)("div",{className:"jsx-5fb9e4356c15d0fb grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-2 sm:gap-y-6",children:[(0,s.jsxs)("div",{className:"jsx-5fb9e4356c15d0fb",children:[(0,s.jsx)("label",{htmlFor:"password",className:"jsx-5fb9e4356c15d0fb block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Password"}),(0,s.jsx)("div",{className:"jsx-5fb9e4356c15d0fb mt-1",children:(0,s.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"new-password",required:!0,value:e.password,onChange:y,className:"jsx-5fb9e4356c15d0fb appearance-none block w-full px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors"})})]}),(0,s.jsxs)("div",{className:"jsx-5fb9e4356c15d0fb",children:[(0,s.jsx)("label",{htmlFor:"confirmPassword",className:"jsx-5fb9e4356c15d0fb block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Confirm password"}),(0,s.jsx)("div",{className:"jsx-5fb9e4356c15d0fb mt-1",children:(0,s.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:"password",autoComplete:"new-password",required:!0,value:e.confirmPassword,onChange:y,className:"jsx-5fb9e4356c15d0fb appearance-none block w-full px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors"})})]})]}),(0,s.jsx)("div",{className:"jsx-5fb9e4356c15d0fb text-xs text-gray-500 dark:text-gray-400 -mt-2",children:"Password must contain at least 8 characters with uppercase, lowercase, and number/special character."}),(0,s.jsxs)("div",{className:"jsx-5fb9e4356c15d0fb flex items-center",children:[(0,s.jsx)("input",{id:"terms",name:"terms",type:"checkbox",required:!0,checked:t,onChange:e=>a(e.target.checked),className:"jsx-5fb9e4356c15d0fb h-5 w-5 text-red-600 focus:ring-2 focus:ring-red-500 border-2 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700"}),(0,s.jsxs)("label",{htmlFor:"terms",className:"jsx-5fb9e4356c15d0fb ml-2 block text-sm text-gray-900 dark:text-gray-100",children:["I agree to the"," ",(0,s.jsx)("a",{href:"#",className:"jsx-5fb9e4356c15d0fb font-medium text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300",children:"Terms of Service"})," ","and"," ",(0,s.jsx)("a",{href:"#",className:"jsx-5fb9e4356c15d0fb font-medium text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300",children:"Privacy Policy"})]})]}),(0,s.jsx)("div",{className:"jsx-5fb9e4356c15d0fb",children:(0,s.jsx)("button",{type:"submit",disabled:d,className:"jsx-5fb9e4356c15d0fb w-full flex justify-center py-3 px-4 border-2 border-transparent rounded-md shadow-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-all disabled:opacity-50 disabled:cursor-not-allowed",children:d?"Creating account...":"Create account"})})]})]})})]})})]})}},33873:e=>{"use strict";e.exports=require("path")},36534:(e,r,t)=>{Promise.resolve().then(t.bind(t,56799))},48158:(e,r,t)=>{Promise.resolve().then(t.bind(t,30877))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56397:()=>{},56799:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\auth\\\\signup\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\auth\\signup\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68736:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o,metadata:()=>a});var s=t(37413);let a={title:"Customer Dashboard - Digital Portal",description:"Customer portal for managing licenses and applications"};function o({children:e}){return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50",children:e})}},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"256x256",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},75913:(e,r,t)=>{"use strict";t(56397);var s=t(43210),a=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(s),o="undefined"!=typeof process&&process.env&&!0,n=function(e){return"[object String]"===Object.prototype.toString.call(e)},i=function(){function e(e){var r=void 0===e?{}:e,t=r.name,s=void 0===t?"stylesheet":t,a=r.optimizeForSpeed,i=void 0===a?o:a;d(n(s),"`name` must be a string"),this._name=s,this._deletedRulePlaceholder="#"+s+"-deleted-rule____{}",d("boolean"==typeof i,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=i,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0,this._nonce=null}var r,t=e.prototype;return t.setOptimizeForSpeed=function(e){d("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),d(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},t.isOptimizeForSpeed=function(){return this._optimizeForSpeed},t.inject=function(){var e=this;d(!this._injected,"sheet already injected"),this._injected=!0,this._serverSheet={cssRules:[],insertRule:function(r,t){return"number"==typeof t?e._serverSheet.cssRules[t]={cssText:r}:e._serverSheet.cssRules.push({cssText:r}),t},deleteRule:function(r){e._serverSheet.cssRules[r]=null}}},t.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var r=0;r<document.styleSheets.length;r++)if(document.styleSheets[r].ownerNode===e)return document.styleSheets[r]},t.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},t.insertRule=function(e,r){return d(n(e),"`insertRule` accepts only strings"),"number"!=typeof r&&(r=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,r),this._rulesCount++},t.replaceRule=function(e,r){this._optimizeForSpeed;var t=this._serverSheet;if(r.trim()||(r=this._deletedRulePlaceholder),!t.cssRules[e])return e;t.deleteRule(e);try{t.insertRule(r,e)}catch(r){t.insertRule(this._deletedRulePlaceholder,e)}return e},t.deleteRule=function(e){this._serverSheet.deleteRule(e)},t.flush=function(){this._injected=!1,this._rulesCount=0,this._serverSheet.cssRules=[]},t.cssRules=function(){return this._serverSheet.cssRules},t.makeStyleTag=function(e,r,t){r&&d(n(r),"makeStyleTag accepts only strings as second parameter");var s=document.createElement("style");this._nonce&&s.setAttribute("nonce",this._nonce),s.type="text/css",s.setAttribute("data-"+e,""),r&&s.appendChild(document.createTextNode(r));var a=document.head||document.getElementsByTagName("head")[0];return t?a.insertBefore(s,t):a.appendChild(s),s},r=[{key:"length",get:function(){return this._rulesCount}}],function(e,r){for(var t=0;t<r.length;t++){var s=r[t];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}(e.prototype,r),e}();function d(e,r){if(!e)throw Error("StyleSheet: "+r+".")}var c=function(e){for(var r=5381,t=e.length;t;)r=33*r^e.charCodeAt(--t);return r>>>0},l={};function u(e,r){if(!r)return"jsx-"+e;var t=String(r),s=e+t;return l[s]||(l[s]="jsx-"+c(e+"-"+t)),l[s]}function f(e,r){var t=e+(r=r.replace(/\/style/gi,"\\/style"));return l[t]||(l[t]=r.replace(/__jsx-style-dynamic-selector/g,e)),l[t]}var b=function(){function e(e){var r=void 0===e?{}:e,t=r.styleSheet,s=void 0===t?null:t,a=r.optimizeForSpeed,o=void 0!==a&&a;this._sheet=s||new i({name:"styled-jsx",optimizeForSpeed:o}),this._sheet.inject(),s&&"boolean"==typeof o&&(this._sheet.setOptimizeForSpeed(o),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var r=e.prototype;return r.add=function(e){var r=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed());var t=this.getIdAndRules(e),s=t.styleId,a=t.rules;if(s in this._instancesCounts){this._instancesCounts[s]+=1;return}var o=a.map(function(e){return r._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[s]=o,this._instancesCounts[s]=1},r.remove=function(e){var r=this,t=this.getIdAndRules(e).styleId;if(function(e,r){if(!e)throw Error("StyleSheetRegistry: "+r+".")}(t in this._instancesCounts,"styleId: `"+t+"` not found"),this._instancesCounts[t]-=1,this._instancesCounts[t]<1){var s=this._fromServer&&this._fromServer[t];s?(s.parentNode.removeChild(s),delete this._fromServer[t]):(this._indices[t].forEach(function(e){return r._sheet.deleteRule(e)}),delete this._indices[t]),delete this._instancesCounts[t]}},r.update=function(e,r){this.add(r),this.remove(e)},r.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},r.cssRules=function(){var e=this,r=this._fromServer?Object.keys(this._fromServer).map(function(r){return[r,e._fromServer[r]]}):[],t=this._sheet.cssRules();return r.concat(Object.keys(this._indices).map(function(r){return[r,e._indices[r].map(function(e){return t[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},r.styles=function(e){var r,t;return r=this.cssRules(),void 0===(t=e)&&(t={}),r.map(function(e){var r=e[0],s=e[1];return a.default.createElement("style",{id:"__"+r,key:"__"+r,nonce:t.nonce?t.nonce:void 0,dangerouslySetInnerHTML:{__html:s}})})},r.getIdAndRules=function(e){var r=e.children,t=e.dynamic,s=e.id;if(t){var a=u(s,t);return{styleId:a,rules:Array.isArray(r)?r.map(function(e){return f(a,e)}):[f(a,r)]}}return{styleId:u(s),rules:Array.isArray(r)?r:[r]}},r.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,r){return e[r.id.slice(2)]=r,e},{})},e}(),m=s.createContext(null);m.displayName="StyleSheetContext";a.default.useInsertionEffect||a.default.useLayoutEffect;var p=void 0;function h(e){var r=p||s.useContext(m);return r&&r.add(e),null}h.dynamic=function(e){return e.map(function(e){return u(e[0],e[1])}).join(" ")},r.style=h},76180:(e,r,t)=>{"use strict";e.exports=t(75913).style},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,7498,1658,5814,2335],()=>t(16849));module.exports=s})();