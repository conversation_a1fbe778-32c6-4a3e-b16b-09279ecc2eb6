"use strict";exports.id=4182,exports.ids=[4182],exports.modules={20539:(e,r,s)=>{s.d(r,{A:()=>l});var a=s(60687),t=s(43210),i=s(85177),d=s(22145),n=s(62978);function l({isOpen:e,onClose:r,onSave:s,role:l,permissions:o=[]}){let[c,m]=(0,t.useState)({name:"",description:"",permission_ids:[]}),[g,p]=(0,t.useState)(!1),[x,y]=(0,t.useState)(null),u=(o||[]).reduce((e,r)=>(e[r.category]||(e[r.category]=[]),e[r.category].push(r),e),{}),h=async e=>{if(e.preventDefault(),p(!0),y(null),!c.name.trim()){y("Role name is required"),p(!1);return}if(c.name.trim().length<2){y("Role name must be at least 2 characters long"),p(!1);return}try{if(l){let e={name:c.name.trim(),description:c.description.trim()||void 0,permission_ids:c.permission_ids};await i.O.updateRole(l.role_id,e)}else{let e={name:c.name.trim(),description:c.description.trim()||void 0,permission_ids:c.permission_ids};await i.O.createRole(e)}s(c.name.trim(),!!l)}catch(e){y(e.response?.data?.message||"Failed to save role")}finally{p(!1)}},b=e=>{let{name:r,value:s}=e.target;m(e=>({...e,[r]:s}))},f=e=>{m(r=>({...r,permission_ids:r.permission_ids.includes(e)?r.permission_ids.filter(r=>r!==e):[...r.permission_ids,e]}))},k=e=>{let r=u[e].map(e=>e.permission_id);r.every(e=>c.permission_ids.includes(e))?m(e=>({...e,permission_ids:e.permission_ids.filter(e=>!r.includes(e))})):m(e=>({...e,permission_ids:[...new Set([...e.permission_ids,...r])]}))};return e?(0,a.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,a.jsxs)("div",{className:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 transition-opacity",onClick:r}),(0,a.jsx)("div",{className:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full",children:(0,a.jsxs)("form",{onSubmit:h,children:[(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:(0,a.jsx)("div",{className:"sm:flex sm:items-start",children:(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4",children:l?"Edit Role":"Create New Role"}),x&&(0,a.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded",children:x}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(d.A,{type:"text",name:"name",label:"Role Name",required:!0,value:c.name,onChange:b,placeholder:"Enter role name (e.g., Administrator, Manager, etc.)"}),(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:"Choose a descriptive name for this role"})]}),(0,a.jsx)(n.A,{name:"description",label:"Description",rows:3,value:c.description,onChange:b,placeholder:"Describe the role and its responsibilities..."}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:["Selected Permissions: ",c.permission_ids.length]}),(0,a.jsx)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Select permissions to assign to this role"})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Permissions"}),(0,a.jsx)("div",{className:"max-h-96 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700",children:o&&o.length>0?Object.keys(u).length>0?Object.entries(u).map(([e,r])=>{let s=r.map(e=>e.permission_id),t=s.every(e=>c.permission_ids.includes(e)),i=s.some(e=>c.permission_ids.includes(e));return(0,a.jsxs)("div",{className:"border-b border-gray-200 dark:border-gray-600 last:border-b-0",children:[(0,a.jsx)("div",{className:"px-4 py-3 bg-gray-50 dark:bg-gray-600",children:(0,a.jsxs)("label",{className:"flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:t,ref:e=>{e&&(e.indeterminate=i&&!t)},onChange:()=>k(e),className:"h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 dark:border-gray-500 rounded bg-white dark:bg-gray-700"}),(0,a.jsx)("span",{className:"ml-2 text-sm font-medium text-gray-900 dark:text-gray-100",children:e}),(0,a.jsxs)("span",{className:"ml-2 text-xs text-gray-500 dark:text-gray-400",children:["(",r.length,")"]})]})}),(0,a.jsx)("div",{className:"px-4 py-2 space-y-2 bg-white dark:bg-gray-700",children:r.map(e=>(0,a.jsxs)("label",{className:"flex items-start cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-600 p-2 rounded",children:[(0,a.jsx)("input",{type:"checkbox",checked:c.permission_ids.includes(e.permission_id),onChange:()=>f(e.permission_id),className:"h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 dark:border-gray-500 rounded bg-white dark:bg-gray-700 mt-0.5"}),(0,a.jsxs)("div",{className:"ml-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-900 dark:text-gray-100",children:e.name.replace(/[_:]/g," ").replace(/\b\w/g,e=>e.toUpperCase())}),(0,a.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:e.description})]})]},e.permission_id))})]},e)}):(0,a.jsx)("div",{className:"p-4 text-center text-gray-500 dark:text-gray-400",children:"No permissions available"}):(0,a.jsx)("div",{className:"p-4 text-center text-gray-500 dark:text-gray-400",children:"Loading permissions..."})})]})]})]})})}),(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,a.jsx)("button",{type:"submit",disabled:g,className:"inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed w-full sm:w-auto sm:ml-3 transition-colors duration-200",children:g?(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Saving..."]}):l?"Update Role":"Create Role"}),(0,a.jsx)("button",{type:"button",onClick:r,className:"inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 mt-3 w-full sm:mt-0 sm:ml-3 sm:w-auto transition-colors duration-200",children:"Cancel"})]})]})})]})}):null}},22145:(e,r,s)=>{s.d(r,{A:()=>i});var a=s(60687);let t=(0,s(43210).forwardRef)(({label:e,error:r,helperText:s,variant:t="default",fullWidth:i=!0,className:d="",required:n,disabled:l,...o},c)=>{let m=`px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 ${i?"w-full":""} ${"small"===t?"py-1.5 text-sm":"py-2"}`,g=`${m} ${r?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"} ${l?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""} ${d}`,p=`block font-medium text-gray-700 dark:text-gray-300 mb-2 ${"small"===t?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"}`;return(0,a.jsxs)("div",{className:"w-full",children:[e&&(0,a.jsxs)("label",{className:p,children:[e,n&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,a.jsx)("input",{ref:c,className:g,disabled:l,required:n,...o}),r&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:r}),s&&!r&&(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:s})]})});t.displayName="TextInput";let i=t},62978:(e,r,s)=>{s.d(r,{A:()=>i});var a=s(60687);let t=(0,s(43210).forwardRef)(({label:e,error:r,helperText:s,variant:t="default",fullWidth:i=!0,className:d="",required:n,disabled:l,rows:o=3,...c},m)=>{let g=`px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 resize-y ${i?"w-full":""} ${"small"===t?"py-1.5 text-sm":"py-2"}`,p=`${g} ${r?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"} ${l?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""} ${d}`,x=`block font-medium text-gray-700 dark:text-gray-300 mb-2 ${"small"===t?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"}`;return(0,a.jsxs)("div",{className:"w-full",children:[e&&(0,a.jsxs)("label",{className:x,children:[e,n&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,a.jsx)("textarea",{ref:m,className:p,disabled:l,required:n,rows:o,...c}),r&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:r}),s&&!r&&(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:s})]})});t.displayName="TextArea";let i=t},63224:(e,r,s)=>{s.d(r,{p:()=>t});var a=s(12234);process.env.NEXT_PUBLIC_API_URL;let t={async getPermissions(e={page:1,limit:10}){let r=new URLSearchParams;return r.set("page",e.page?.toString()||"1"),r.set("limit",e.limit?.toString()||"10"),e.search&&r.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>r.append("sortBy",e)),e.filter&&Object.entries(e.filter).forEach(([e,s])=>{Array.isArray(s)?s.forEach(s=>r.append(`filter.${e}`,s)):r.set(`filter.${e}`,s)}),(await a.uE.get(`/permissions?${r.toString()}`)).data},async getAllPermissions(){try{let e=await a.uE.get("/permissions/by-category"),r=e.data?.data||e.data;if(!r)return[];if(Array.isArray(r))return r;let s=[];return Object.values(r).forEach(e=>{Array.isArray(e)&&s.push(...e)}),s}catch(e){return[]}},async getPermissionsByCategory(){try{let e=await a.uE.get("/permissions/by-category"),r=e.data?.data||e.data;if(!r)return{};return r}catch(e){return{}}},async getPermission(e){let r=await a.uE.get(`/permissions/${e}`);return r.data?.data||r.data},async createPermission(e){let r=await a.uE.post("/permissions",e);return r.data?.data||r.data},async updatePermission(e,r){let s=await a.uE.patch(`/permissions/${e}`,r);return s.data?.data||s.data},async deletePermission(e){await a.uE.delete(`/permissions/${e}`)}}},85177:(e,r,s)=>{s.d(r,{O:()=>i});var a=s(12234),t=s(51278);process.env.NEXT_PUBLIC_API_URL;let i={async getRoles(e={}){let r=new URLSearchParams;e.page&&r.set("page",e.page.toString()),e.limit&&r.set("limit",e.limit.toString()),e.search&&r.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>r.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>r.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(([e,s])=>{Array.isArray(s)?s.forEach(s=>r.append(`filter.${e}`,s)):r.set(`filter.${e}`,s)});let s=await a.rV.get(`?${r.toString()}`);return(0,t.zp)(s)},async getRole(e){let r=await a.rV.get(`/${e}`);return(0,t.zp)(r)},async getRoleWithPermissions(e){let r=await a.rV.get(`/${e}?include=permissions`);return(0,t.zp)(r)},async createRole(e){let r=await a.rV.post("",e);return(0,t.zp)(r)},async updateRole(e,r){let s=await a.rV.patch(`/${e}`,r);return(0,t.zp)(s)},async deleteRole(e){await a.rV.delete(`/${e}`)},async assignPermissions(e,r){let s=await a.rV.post(`/${e}/permissions`,{permission_ids:r});return(0,t.zp)(s)},async removePermissions(e,r){let s=await a.rV.delete(`/${e}/permissions`,{data:{permission_ids:r}});return(0,t.zp)(s)},async getPermissions(){let e=await a.uE.get("/permissions");return(0,t.zp)(e)}}}};