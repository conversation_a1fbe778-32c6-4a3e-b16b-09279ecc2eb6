{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/types/license.ts"], "sourcesContent": ["export interface LicenseType {\r\n  license_type_id: string;\r\n  name: string;\r\n  code?: string;\r\n  description?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface LicenseCategory {\r\n  license_category_id: string;\r\n  name: string;\r\n  description?: string;\r\n  license_type_id: string;\r\n  license_type?: LicenseType;\r\n  parent_id?: string;\r\n  parent?: LicenseCategory;\r\n  children?: LicenseCategory[];\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface Applicant {\r\n  applicant_id: string;\r\n  name: string;\r\n  business_registration_number: string;\r\n  tpin: string;\r\n  website: string;\r\n  email: string;\r\n  phone: string;\r\n  fax?: string;\r\n  level_of_insurance_cover?: string;\r\n  address_id?: string;\r\n  contact_id?: string;\r\n  date_incorporation: string;\r\n  place_incorporation: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface Application {\r\n  application_id: string;\r\n  application_number: string;\r\n  applicant_id: string;\r\n  license_category_id: string;\r\n  status: ApplicationStatus;\r\n  current_step: number;\r\n  progress_percentage: number;\r\n  submitted_at?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  applicant?: Applicant;\r\n  license_category?: LicenseCategory;\r\n}\r\n\r\nexport enum ApplicationStatus {\r\n  DRAFT = 'draft',\r\n  SUBMITTED = 'submitted',\r\n  UNDER_REVIEW = 'under_review',\r\n  EVALUATION = 'evaluation',\r\n  APPROVED = 'approved',\r\n  REJECTED = 'rejected',\r\n  WITHDRAWN = 'withdrawn',\r\n}\r\n\r\nexport interface ApplicationFilters {\r\n  licenseTypeId?: string;\r\n  licenseCategoryId?: string;\r\n  status?: ApplicationStatus | '';\r\n  dateRange?: string;\r\n  search?: string;\r\n}\r\n\r\nexport interface PaginatedResponse<T> {\r\n  data: T[];\r\n  meta: {\r\n    itemsPerPage: number;\r\n    totalItems: number;\r\n    currentPage: number;\r\n    totalPages: number;\r\n    sortBy: [string, string][];\r\n    searchBy: string[];\r\n    search: string;\r\n    filter: Record<string, string | string[]>;\r\n  };\r\n  links: {\r\n    first: string;\r\n    previous: string;\r\n    current: string;\r\n    next: string;\r\n    last: string;\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAuDO,IAAA,AAAK,2CAAA;;;;;;;;WAAA", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/services/applicationService.ts"], "sourcesContent": ["import { processApiResponse } from '@/lib/authUtils';\r\nimport { apiClient } from '../lib/apiClient';\r\nimport { Application, ApplicationStatus, ApplicationFilters, PaginatedResponse } from '../types/license';\r\n\r\nexport const applicationService = {\r\n  // Get all applications with pagination and filters\r\n  async getApplications(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    sortBy?: string;\r\n    sortOrder?: 'ASC' | 'DESC';\r\n    filters?: ApplicationFilters;\r\n  }): Promise<PaginatedResponse<Application>> {\r\n    const queryParams = new URLSearchParams();\r\n    \r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.sortBy) queryParams.append('sortBy', params.sortBy);\r\n    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);\r\n    \r\n    // Add filters\r\n    if (params?.filters?.licenseTypeId) {\r\n      queryParams.append('filter.license_category.license_type_id', params.filters.licenseTypeId);\r\n    }\r\n    if (params?.filters?.licenseCategoryId) {\r\n      queryParams.append('filter.license_category_id', params.filters.licenseCategoryId);\r\n    }\r\n    if (params?.filters?.status) {\r\n      queryParams.append('filter.status', params.filters.status);\r\n    }\r\n\r\n    const response = await apiClient.get(`/applications?${queryParams.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get applications by license type (through license category)\r\n  async getApplicationsByLicenseType(licenseTypeId: string, params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    status?: ApplicationStatus;\r\n  }): Promise<PaginatedResponse<Application>> {\r\n    const queryParams = new URLSearchParams();\r\n    \r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.status) queryParams.append('filter.status', params.status);\r\n    \r\n    // Filter by license type through license category\r\n    queryParams.append('filter.license_category.license_type_id', licenseTypeId);\r\n\r\n    const response = await apiClient.get(`/applications?${queryParams.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get single application by ID\r\n  async getApplication(id: string): Promise<Application> {\r\n    const response = await apiClient.get(`/applications/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get applications by applicant\r\n  async getApplicationsByApplicant(applicantId: string): Promise<Application[]> {\r\n    const response = await apiClient.get(`/applications/by-applicant/${applicantId}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get applications by status\r\n  async getApplicationsByStatus(status: ApplicationStatus): Promise<Application[]> {\r\n    const response = await apiClient.get(`/applications/by-status/${status}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update application status\r\n  async updateApplicationStatus(id: string, status: ApplicationStatus): Promise<Application> {\r\n    const response = await apiClient.put(`/applications/${id}/status?status=${status}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update application progress\r\n  async updateApplicationProgress(id: string, currentStep: number, progressPercentage: number): Promise<Application> {\r\n    const response = await apiClient.put(\r\n      `/applications/${id}/progress?currentStep=${currentStep}&progressPercentage=${progressPercentage}`\r\n    );\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get application statistics\r\n  async getApplicationStats(): Promise<Record<string, number>> {\r\n    const response = await apiClient.get('/applications/stats');\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Create new application\r\n  async createApplication(data: {\r\n    application_number: string;\r\n    applicant_id: string;\r\n    license_category_id: string;\r\n    status?: ApplicationStatus;\r\n    current_step?: number;\r\n    progress_percentage?: number;\r\n    submitted_at?: Date;\r\n  }): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.post('/applications', data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Update application with improved error handling\r\n  async updateApplication(id: string, data: Partial<Application>): Promise<Application> {\r\n    try {\r\n      console.log('Updating application:', id, 'with data:', data);\r\n      const response = await apiClient.put(`/applications/${id}`, data, {\r\n        timeout: 30000, // 30 second timeout\r\n      });\r\n      return processApiResponse(response);\r\n    } catch (error: any) {\r\n      console.error('Error updating application:', error);\r\n\r\n      // Handle specific error cases\r\n      if (error.code === 'ECONNABORTED') {\r\n        throw new Error('Request timeout - please try again');\r\n      }\r\n\r\n      if (error.response?.status === 400) {\r\n        const message = error.response?.data?.message || 'Invalid application data';\r\n        console.error('400 Bad Request details:', error.response?.data);\r\n        throw new Error(`Bad Request: ${message}`);\r\n      }\r\n\r\n      if (error.response?.status === 429) {\r\n        throw new Error('Too many requests - please wait a moment and try again');\r\n      }\r\n\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Delete application\r\n  async deleteApplication(id: string): Promise<{ message: string }> {\r\n    const response = await apiClient.delete(`/applications/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Create new application with applicant data\r\n  async createApplicationWithApplicant(data: {\r\n    license_type_id: string;\r\n    license_category_id: string;\r\n    applicant_data: Record<string, any>;\r\n    user_id: string;\r\n  }): Promise<Application> {\r\n    try {\r\n      // Generate proper application number (format: APP-YYYYMMDD-HHMMSS-XXX)\r\n      const now = new Date();\r\n      const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');\r\n      const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');\r\n      const randomStr = Math.random().toString(36).substr(2, 3).toUpperCase();\r\n      const applicationNumber = `APP-${dateStr}-${timeStr}-${randomStr}`;\r\n\r\n      // Validate user_id is a proper UUID\r\n      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\r\n      if (!uuidRegex.test(data.user_id)) {\r\n        throw new Error(`Invalid user_id format: ${data.user_id}. Expected UUID format.`);\r\n      }\r\n\r\n      // Create application using user_id as applicant_id\r\n      // In most systems, the authenticated user is the applicant\r\n      const application = await this.createApplication({\r\n        application_number: applicationNumber,\r\n        applicant_id: data.user_id, // Use user_id as applicant_id\r\n        license_category_id: data.license_category_id,\r\n        current_step: 1,\r\n        progress_percentage: 0 // Start with 0% progress\r\n      });\r\n\r\n      return application;\r\n    } catch (error) {\r\n      console.error('Error creating application with applicant:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Save application section data\r\n  async saveApplicationSection(\r\n    applicationId: string,\r\n    sectionName: string,\r\n    sectionData: Record<string, any>\r\n  ): Promise<void> {\r\n    try {\r\n      // Try to save using form data service, but continue if it fails\r\n      let completedSections = 1; // At least one section is being saved\r\n\r\n      // Estimate progress based on section name\r\n      const sectionOrder = ['applicantInfo', 'companyProfile', 'businessInfo', 'serviceScope', 'businessPlan', 'legalHistory', 'reviewSubmit'];\r\n      const sectionIndex = sectionOrder.indexOf(sectionName);\r\n      completedSections = sectionIndex >= 0 ? sectionIndex + 1 : 1;\r\n\r\n      // Calculate progress based on completed sections (excluding reviewSubmit from total)\r\n      const totalSections = 6; // Total number of form sections (excluding reviewSubmit)\r\n      const progressPercentage = Math.min(Math.round((completedSections / totalSections) * 100), 100);\r\n\r\n      // Update the application progress\r\n      await this.updateApplication(applicationId, {\r\n        progress_percentage: progressPercentage,\r\n        current_step: completedSections\r\n      });\r\n\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get application section data\r\n  async getApplicationSection(applicationId: string, sectionName: string): Promise<any> {\r\n    try {\r\n      const response = await apiClient.get(`/applications/${applicationId}/sections/${sectionName}`);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Submit application for review\r\n  async submitApplication(applicationId: string): Promise<Application> {\r\n    try {\r\n      // Update application status to submitted and set submission date\r\n      const response = await apiClient.put(`/applications/${applicationId}`, {\r\n        status: 'submitted',\r\n        submitted_at: new Date().toISOString(),\r\n        progress_percentage: 100,\r\n        current_step: 7\r\n      });\r\n\r\n      console.log('Application submitted successfully:', processApiResponse(response));\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error submitting application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get user's applications (filtered by authenticated user)\r\n  async getUserApplications(): Promise<Application[]> {\r\n    try {\r\n      // Use dedicated endpoint that explicitly filters by current user\r\n      const response = await apiClient.get('/applications/user-applications');\r\n      const processedResponse = processApiResponse(response);\r\n\r\n      // Handle paginated response structure\r\n      let applications = [];\r\n      if (processedResponse?.data) {\r\n        applications = Array.isArray(processedResponse.data) ? processedResponse.data : [];\r\n      } else if (Array.isArray(processedResponse)) {\r\n        applications = processedResponse;\r\n      } else if (processedResponse) {\r\n        // Single application or other structure\r\n        applications = [processedResponse];\r\n      }\r\n\r\n      return applications;\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Save application as draft\r\n  async saveAsDraft(applicationId: string, formData: Record<string, any>): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.put(`/applications/${applicationId}`, {\r\n        form_data: formData,\r\n        status: 'draft'\r\n      });\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error saving application as draft:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Validate application before submission\r\n  async validateApplication(applicationId: string): Promise<{ isValid: boolean; errors: any[] }> {\r\n    try {\r\n      // Get data from entity-specific APIs for validation\r\n      let formData: Record<string, any> = {};\r\n\r\n      const errors: any[] = [];\r\n      const requiredSections = ['applicantInfo', 'companyProfile', 'businessInfo', 'legalHistory'];\r\n\r\n      // Check if all required sections are completed\r\n      for (const section of requiredSections) {\r\n        if (!formData[section] || Object.keys(formData[section]).length === 0) {\r\n          errors.push(`${section} section is incomplete`);\r\n        }\r\n      }\r\n\r\n      return {\r\n        isValid: errors.length === 0,\r\n        errors\r\n      };\r\n    } catch (error) {\r\n      console.error('Error validating application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,MAAM,qBAAqB;IAChC,mDAAmD;IACnD,MAAM,iBAAgB,MAOrB;QACC,MAAM,cAAc,IAAI;QAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,WAAW,YAAY,MAAM,CAAC,aAAa,OAAO,SAAS;QAEvE,cAAc;QACd,IAAI,QAAQ,SAAS,eAAe;YAClC,YAAY,MAAM,CAAC,2CAA2C,OAAO,OAAO,CAAC,aAAa;QAC5F;QACA,IAAI,QAAQ,SAAS,mBAAmB;YACtC,YAAY,MAAM,CAAC,8BAA8B,OAAO,OAAO,CAAC,iBAAiB;QACnF;QACA,IAAI,QAAQ,SAAS,QAAQ;YAC3B,YAAY,MAAM,CAAC,iBAAiB,OAAO,OAAO,CAAC,MAAM;QAC3D;QAEA,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,YAAY,QAAQ,IAAI;QAC9E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8DAA8D;IAC9D,MAAM,8BAA6B,aAAqB,EAAE,MAKzD;QACC,MAAM,cAAc,IAAI;QAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,iBAAiB,OAAO,MAAM;QAErE,kDAAkD;QAClD,YAAY,MAAM,CAAC,2CAA2C;QAE9D,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,YAAY,QAAQ,IAAI;QAC9E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,+BAA+B;IAC/B,MAAM,gBAAe,EAAU;QAC7B,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI;QAC1D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gCAAgC;IAChC,MAAM,4BAA2B,WAAmB;QAClD,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,2BAA2B,EAAE,aAAa;QAChF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6BAA6B;IAC7B,MAAM,yBAAwB,MAAyB;QACrD,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,wBAAwB,EAAE,QAAQ;QACxE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,4BAA4B;IAC5B,MAAM,yBAAwB,EAAU,EAAE,MAAyB;QACjE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,GAAG,eAAe,EAAE,QAAQ;QAClF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8BAA8B;IAC9B,MAAM,2BAA0B,EAAU,EAAE,WAAmB,EAAE,kBAA0B;QACzF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAC,cAAc,EAAE,GAAG,sBAAsB,EAAE,YAAY,oBAAoB,EAAE,oBAAoB;QAEpG,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6BAA6B;IAC7B,MAAM;QACJ,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,yBAAyB;IACzB,MAAM,mBAAkB,IAQvB;QACC,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,iBAAiB;YACvD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,kDAAkD;IAClD,MAAM,mBAAkB,EAAU,EAAE,IAA0B;QAC5D,IAAI;YACF,QAAQ,GAAG,CAAC,yBAAyB,IAAI,cAAc;YACvD,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI,EAAE,MAAM;gBAChE,SAAS;YACX;YACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,+BAA+B;YAE7C,8BAA8B;YAC9B,IAAI,MAAM,IAAI,KAAK,gBAAgB;gBACjC,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,WAAW;gBACjD,QAAQ,KAAK,CAAC,4BAA4B,MAAM,QAAQ,EAAE;gBAC1D,MAAM,IAAI,MAAM,CAAC,aAAa,EAAE,SAAS;YAC3C;YAEA,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM;QACR;IACF;IAEA,qBAAqB;IACrB,MAAM,mBAAkB,EAAU;QAChC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,cAAc,EAAE,IAAI;QAC7D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6CAA6C;IAC7C,MAAM,gCAA+B,IAKpC;QACC,IAAI;YACF,uEAAuE;YACvE,MAAM,MAAM,IAAI;YAChB,MAAM,UAAU,IAAI,WAAW,GAAG,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,MAAM;YAC7D,MAAM,UAAU,IAAI,YAAY,GAAG,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM;YAC7D,MAAM,YAAY,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG,WAAW;YACrE,MAAM,oBAAoB,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,WAAW;YAElE,oCAAoC;YACpC,MAAM,YAAY;YAClB,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,OAAO,GAAG;gBACjC,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,KAAK,OAAO,CAAC,uBAAuB,CAAC;YAClF;YAEA,mDAAmD;YACnD,2DAA2D;YAC3D,MAAM,cAAc,MAAM,IAAI,CAAC,iBAAiB,CAAC;gBAC/C,oBAAoB;gBACpB,cAAc,KAAK,OAAO;gBAC1B,qBAAqB,KAAK,mBAAmB;gBAC7C,cAAc;gBACd,qBAAqB,EAAE,yBAAyB;YAClD;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,MAAM,wBACJ,aAAqB,EACrB,WAAmB,EACnB,WAAgC;QAEhC,IAAI;YACF,gEAAgE;YAChE,IAAI,oBAAoB,GAAG,sCAAsC;YAEjE,0CAA0C;YAC1C,MAAM,eAAe;gBAAC;gBAAiB;gBAAkB;gBAAgB;gBAAgB;gBAAgB;gBAAgB;aAAe;YACxI,MAAM,eAAe,aAAa,OAAO,CAAC;YAC1C,oBAAoB,gBAAgB,IAAI,eAAe,IAAI;YAE3D,qFAAqF;YACrF,MAAM,gBAAgB,GAAG,yDAAyD;YAClF,MAAM,qBAAqB,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,AAAC,oBAAoB,gBAAiB,MAAM;YAE3F,kCAAkC;YAClC,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe;gBAC1C,qBAAqB;gBACrB,cAAc;YAChB;QAEF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,+BAA+B;IAC/B,MAAM,uBAAsB,aAAqB,EAAE,WAAmB;QACpE,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,cAAc,UAAU,EAAE,aAAa;YAC7F,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,MAAM,mBAAkB,aAAqB;QAC3C,IAAI;YACF,iEAAiE;YACjE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe,EAAE;gBACrE,QAAQ;gBACR,cAAc,IAAI,OAAO,WAAW;gBACpC,qBAAqB;gBACrB,cAAc;YAChB;YAEA,QAAQ,GAAG,CAAC,uCAAuC,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;YACtE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA,2DAA2D;IAC3D,MAAM;QACJ,IAAI;YACF,iEAAiE;YACjE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,MAAM,oBAAoB,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;YAE7C,sCAAsC;YACtC,IAAI,eAAe,EAAE;YACrB,IAAI,mBAAmB,MAAM;gBAC3B,eAAe,MAAM,OAAO,CAAC,kBAAkB,IAAI,IAAI,kBAAkB,IAAI,GAAG,EAAE;YACpF,OAAO,IAAI,MAAM,OAAO,CAAC,oBAAoB;gBAC3C,eAAe;YACjB,OAAO,IAAI,mBAAmB;gBAC5B,wCAAwC;gBACxC,eAAe;oBAAC;iBAAkB;YACpC;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,4BAA4B;IAC5B,MAAM,aAAY,aAAqB,EAAE,QAA6B;QACpE,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe,EAAE;gBACrE,WAAW;gBACX,QAAQ;YACV;YACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACR;IACF;IAEA,yCAAyC;IACzC,MAAM,qBAAoB,aAAqB;QAC7C,IAAI;YACF,oDAAoD;YACpD,IAAI,WAAgC,CAAC;YAErC,MAAM,SAAgB,EAAE;YACxB,MAAM,mBAAmB;gBAAC;gBAAiB;gBAAkB;gBAAgB;aAAe;YAE5F,+CAA+C;YAC/C,KAAK,MAAM,WAAW,iBAAkB;gBACtC,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG;oBACrE,OAAO,IAAI,CAAC,GAAG,QAAQ,sBAAsB,CAAC;gBAChD;YACF;YAEA,OAAO;gBACL,SAAS,OAAO,MAAM,KAAK;gBAC3B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/services/licenseCategoryService.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\r\nimport { LicenseType } from './licenseTypeService';\r\nimport { cacheService, CACHE_KEYS, CACHE_TTL } from './cacheService';\r\n\r\n// Utility functions for category codes\r\nexport const generateCategoryCode = (name: string): string => {\r\n  return name\r\n    .toLowerCase()\r\n    .replace(/[^a-z0-9\\s]/g, '') // Remove special characters\r\n    .replace(/\\s+/g, '-') // Replace spaces with hyphens\r\n    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens\r\n    .substring(0, 50); // Limit length\r\n};\r\n\r\nexport const addCodesToCategories = (categories: LicenseCategory[]): LicenseCategory[] => {\r\n  return categories.map(category => ({\r\n    ...category,\r\n    code: generateCategoryCode(category.name),\r\n    children: category.children ? addCodesToCategories(category.children) : undefined\r\n  }));\r\n};\r\n\r\nexport const findCategoryByCode = (categories: LicenseCategory[], code: string): LicenseCategory | null => {\r\n  for (const category of categories) {\r\n    if (category.code === code) {\r\n      return category;\r\n    }\r\n    if (category.children) {\r\n      const found = findCategoryByCode(category.children, code);\r\n      if (found) return found;\r\n    }\r\n  }\r\n  return null;\r\n};\r\n\r\nexport const findCategoryById = (categories: LicenseCategory[], id: string): LicenseCategory | null => {\r\n  for (const category of categories) {\r\n    if (category.license_category_id === id) {\r\n      return category;\r\n    }\r\n    if (category.children) {\r\n      const found = findCategoryById(category.children, id);\r\n      if (found) return found;\r\n    }\r\n  }\r\n  return null;\r\n};\r\n\r\n// Types\r\nexport interface LicenseCategory {\r\n  license_category_id: string;\r\n  license_type_id: string;\r\n  parent_id?: string;\r\n  name: string;\r\n  fee: string;\r\n  description: string;\r\n  authorizes: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  created_by?: string;\r\n  updated_by?: string;\r\n  license_type?: LicenseType;\r\n  parent?: LicenseCategory;\r\n  children?: LicenseCategory[];\r\n  creator?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n  updater?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n  // Generated code for URL-friendly routing\r\n  code?: string;\r\n}\r\n\r\nexport interface CreateLicenseCategoryDto {\r\n  license_type_id: string;\r\n  parent_id?: string;\r\n  name: string;\r\n  fee: string;\r\n  description: string;\r\n  authorizes: string;\r\n}\r\n\r\nexport interface UpdateLicenseCategoryDto {\r\n  license_type_id?: string;\r\n  parent_id?: string;\r\n  name?: string;\r\n  fee?: string;\r\n  description?: string;\r\n  authorizes?: string;\r\n}\r\n\r\nexport interface PaginatedResponse<T> {\r\n  data: T[];\r\n  meta: {\r\n    itemsPerPage: number;\r\n    totalItems?: number;\r\n    currentPage?: number;\r\n    totalPages?: number;\r\n    sortBy: [string, string][];\r\n    searchBy: string[];\r\n    search: string;\r\n    select: string[];\r\n    filter?: Record<string, string | string[]>;\r\n  };\r\n  links: {\r\n    first?: string;\r\n    previous?: string;\r\n    current: string;\r\n    next?: string;\r\n    last?: string;\r\n  };\r\n}\r\n\r\nexport type LicenseCategoriesResponse = PaginatedResponse<LicenseCategory>;\r\n\r\nexport interface PaginateQuery {\r\n  page?: number;\r\n  limit?: number;\r\n  sortBy?: string[];\r\n  searchBy?: string[];\r\n  search?: string;\r\n  filter?: Record<string, string | string[]>;\r\n}\r\n\r\nexport const licenseCategoryService = {\r\n  // Get all license categories with pagination\r\n  async getLicenseCategories(query: PaginateQuery = {}): Promise<LicenseCategoriesResponse> {\r\n    const params = new URLSearchParams();\r\n\r\n    if (query.page) params.set('page', query.page.toString());\r\n    if (query.limit) params.set('limit', query.limit.toString());\r\n    if (query.search) params.set('search', query.search);\r\n    if (query.sortBy) {\r\n      query.sortBy.forEach(sort => params.append('sortBy', sort));\r\n    }\r\n    if (query.searchBy) {\r\n      query.searchBy.forEach(search => params.append('searchBy', search));\r\n    }\r\n    if (query.filter) {\r\n      Object.entries(query.filter).forEach(([key, value]) => {\r\n        if (Array.isArray(value)) {\r\n          value.forEach(v => params.append(`filter.${key}`, v));\r\n        } else {\r\n          params.set(`filter.${key}`, value);\r\n        }\r\n      });\r\n    }\r\n\r\n    const response = await apiClient.get(`/license-categories?${params.toString()}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Get license category by ID with timeout and retry handling\r\n  async getLicenseCategory(id: string): Promise<LicenseCategory> {\r\n    try {\r\n      const response = await apiClient.get(`/license-categories/${id}`, {\r\n        timeout: 30000, // 30 second timeout for individual requests\r\n      });\r\n      return response.data;\r\n    } catch (error: any) {\r\n      console.error('Error fetching license category:', error);\r\n      if (error.code === 'ECONNABORTED') {\r\n        throw new Error('Request timeout - please try again');\r\n      }\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get license categories by license type with improved error handling\r\n  async getLicenseCategoriesByType(licenseTypeId: string): Promise<any> {\r\n    try {\r\n      const response = await apiClient.get(`/license-categories/by-license-type/${licenseTypeId}`, {\r\n        timeout: 30000, // 30 second timeout\r\n      });\r\n      return response.data;\r\n    } catch (error: any) {\r\n      console.error('Error fetching license categories by type:', error);\r\n      if (error.code === 'ECONNABORTED') {\r\n        throw new Error('Request timeout - please try again');\r\n      }\r\n      if (error.response?.status === 429) {\r\n        throw new Error('Too many requests - please wait a moment and try again');\r\n      }\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Create new license category\r\n  async createLicenseCategory(licenseCategoryData: CreateLicenseCategoryDto): Promise<LicenseCategory> {\r\n    const response = await apiClient.post('/license-categories', licenseCategoryData);\r\n    return response.data;\r\n  },\r\n\r\n  // Update license category\r\n  async updateLicenseCategory(id: string, licenseCategoryData: UpdateLicenseCategoryDto): Promise<LicenseCategory> {\r\n    const response = await apiClient.put(`/license-categories/${id}`, licenseCategoryData);\r\n    return response.data;\r\n  },\r\n\r\n  // Delete license category\r\n  async deleteLicenseCategory(id: string): Promise<{ message: string }> {\r\n    const response = await apiClient.delete(`/license-categories/${id}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Get all license categories (simple list for dropdowns) with caching\r\n  async getAllLicenseCategories(): Promise<any> {\r\n    return cacheService.getOrSet(\r\n      CACHE_KEYS.LICENSE_CATEGORIES,\r\n      async () => {\r\n        console.log('Fetching license categories from API...');\r\n        // Reduce limit to avoid rate limiting\r\n        const response = await this.getLicenseCategories({ limit: 100 });\r\n        return addCodesToCategories(response.data);\r\n      },\r\n      CACHE_TTL.LONG // Cache for 15 minutes\r\n    );\r\n  },\r\n\r\n  // Get hierarchical tree of categories for a license type with caching\r\n  async getCategoryTree(licenseTypeId: string): Promise<LicenseCategory[]> {\r\n    return cacheService.getOrSet(\r\n      `category-tree-${licenseTypeId}`,\r\n      async () => {\r\n        console.log(`Fetching category tree for license type: ${licenseTypeId}`);\r\n        const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/tree`);\r\n        return addCodesToCategories(response.data);\r\n      },\r\n      CACHE_TTL.MEDIUM // Cache for 5 minutes\r\n    );\r\n  },\r\n\r\n  // Get root categories (no parent) for a license type with caching\r\n  async getRootCategories(licenseTypeId: string): Promise<LicenseCategory[]> {\r\n    return cacheService.getOrSet(\r\n      `root-categories-${licenseTypeId}`,\r\n      async () => {\r\n        console.log(`Fetching root categories for license type: ${licenseTypeId}`);\r\n        const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/root`);\r\n        return response.data;\r\n      },\r\n      CACHE_TTL.MEDIUM // Cache for 5 minutes\r\n    );\r\n  },\r\n\r\n  // Get license categories for parent selection dropdown\r\n  async getCategoriesForParentSelection(licenseTypeId: string, excludeId?: string): Promise<LicenseCategory[]> {\r\n    try {\r\n      const params = excludeId ? { excludeId } : {};\r\n      console.log('🚀 Fetching parent categories for license type:', licenseTypeId, 'with params:', params);\r\n\r\n      // Try the new endpoint first\r\n      try {\r\n        const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/for-parent-selection`, { params });\r\n\r\n\r\n        if (response.data && Array.isArray(response.data.data)) {\r\n          console.log('✅ Valid array response with', response.data.data.length, 'items')\r\n          return response.data.data;\r\n        } else {\r\n          console.warn('⚠️ API returned non-array data:', response.data);\r\n          return [];\r\n        }\r\n      } catch (newEndpointError) {\r\n        console.warn('🔄 New endpoint failed, trying fallback:', newEndpointError);\r\n\r\n        // Fallback to existing endpoint\r\n        const response = await apiClient.get(`/license-categories/by-license-type/${licenseTypeId}`);\r\n        console.log('🔄 Fallback response:', response.data);\r\n\r\n        if (response.data && Array.isArray(response.data)) {\r\n          // Filter out the excluded category if specified\r\n          let categories = response.data;\r\n          if (excludeId) {\r\n            categories = categories.filter(cat => cat.license_category_id !== excludeId);\r\n          }\r\n          console.log('✅ Fallback successful with', categories.length, 'items');\r\n          return categories;\r\n        } else {\r\n          console.warn('⚠️ Fallback also returned non-array data:', response.data);\r\n          return [];\r\n        }\r\n      }\r\n    } catch (error) {\r\n\r\n      return [];\r\n    }\r\n  },\r\n\r\n  // Get potential parent categories for a license type\r\n  async getPotentialParents(licenseTypeId: string, excludeId?: string): Promise<LicenseCategory[]> {\r\n    const params = excludeId ? { excludeId } : {};\r\n    const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/potential-parents`, { params });\r\n    return response.data;\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AAEA;;;AAGO,MAAM,uBAAuB,CAAC;IACnC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,gBAAgB,IAAI,4BAA4B;KACxD,OAAO,CAAC,QAAQ,KAAK,8BAA8B;KACnD,OAAO,CAAC,YAAY,IAAI,kCAAkC;KAC1D,SAAS,CAAC,GAAG,KAAK,eAAe;AACtC;AAEO,MAAM,uBAAuB,CAAC;IACnC,OAAO,WAAW,GAAG,CAAC,CAAA,WAAY,CAAC;YACjC,GAAG,QAAQ;YACX,MAAM,qBAAqB,SAAS,IAAI;YACxC,UAAU,SAAS,QAAQ,GAAG,qBAAqB,SAAS,QAAQ,IAAI;QAC1E,CAAC;AACH;AAEO,MAAM,qBAAqB,CAAC,YAA+B;IAChE,KAAK,MAAM,YAAY,WAAY;QACjC,IAAI,SAAS,IAAI,KAAK,MAAM;YAC1B,OAAO;QACT;QACA,IAAI,SAAS,QAAQ,EAAE;YACrB,MAAM,QAAQ,mBAAmB,SAAS,QAAQ,EAAE;YACpD,IAAI,OAAO,OAAO;QACpB;IACF;IACA,OAAO;AACT;AAEO,MAAM,mBAAmB,CAAC,YAA+B;IAC9D,KAAK,MAAM,YAAY,WAAY;QACjC,IAAI,SAAS,mBAAmB,KAAK,IAAI;YACvC,OAAO;QACT;QACA,IAAI,SAAS,QAAQ,EAAE;YACrB,MAAM,QAAQ,iBAAiB,SAAS,QAAQ,EAAE;YAClD,IAAI,OAAO,OAAO;QACpB;IACF;IACA,OAAO;AACT;AAqFO,MAAM,yBAAyB;IACpC,6CAA6C;IAC7C,MAAM,sBAAqB,QAAuB,CAAC,CAAC;QAClD,MAAM,SAAS,IAAI;QAEnB,IAAI,MAAM,IAAI,EAAE,OAAO,GAAG,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ;QACtD,IAAI,MAAM,KAAK,EAAE,OAAO,GAAG,CAAC,SAAS,MAAM,KAAK,CAAC,QAAQ;QACzD,IAAI,MAAM,MAAM,EAAE,OAAO,GAAG,CAAC,UAAU,MAAM,MAAM;QACnD,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,MAAM,CAAC,OAAO,CAAC,CAAA,OAAQ,OAAO,MAAM,CAAC,UAAU;QACvD;QACA,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAA,SAAU,OAAO,MAAM,CAAC,YAAY;QAC7D;QACA,IAAI,MAAM,MAAM,EAAE;YAChB,OAAO,OAAO,CAAC,MAAM,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAChD,IAAI,MAAM,OAAO,CAAC,QAAQ;oBACxB,MAAM,OAAO,CAAC,CAAA,IAAK,OAAO,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBACpD,OAAO;oBACL,OAAO,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBAC9B;YACF;QACF;QAEA,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,OAAO,QAAQ,IAAI;QAC/E,OAAO,SAAS,IAAI;IACtB;IAEA,6DAA6D;IAC7D,MAAM,oBAAmB,EAAU;QACjC,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,IAAI,EAAE;gBAChE,SAAS;YACX;YACA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,oCAAoC;YAClD,IAAI,MAAM,IAAI,KAAK,gBAAgB;gBACjC,MAAM,IAAI,MAAM;YAClB;YACA,MAAM;QACR;IACF;IAEA,sEAAsE;IACtE,MAAM,4BAA2B,aAAqB;QACpD,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oCAAoC,EAAE,eAAe,EAAE;gBAC3F,SAAS;YACX;YACA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,IAAI,MAAM,IAAI,KAAK,gBAAgB;gBACjC,MAAM,IAAI,MAAM;YAClB;YACA,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,MAAM,IAAI,MAAM;YAClB;YACA,MAAM;QACR;IACF;IAEA,8BAA8B;IAC9B,MAAM,uBAAsB,mBAA6C;QACvE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,uBAAuB;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,MAAM,uBAAsB,EAAU,EAAE,mBAA6C;QACnF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,IAAI,EAAE;QAClE,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,MAAM,uBAAsB,EAAU;QACpC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,oBAAoB,EAAE,IAAI;QACnE,OAAO,SAAS,IAAI;IACtB;IAEA,sEAAsE;IACtE,MAAM;QACJ,OAAO,+HAAA,CAAA,eAAY,CAAC,QAAQ,CAC1B,+HAAA,CAAA,aAAU,CAAC,kBAAkB,EAC7B;YACE,QAAQ,GAAG,CAAC;YACZ,sCAAsC;YACtC,MAAM,WAAW,MAAM,IAAI,CAAC,oBAAoB,CAAC;gBAAE,OAAO;YAAI;YAC9D,OAAO,qBAAqB,SAAS,IAAI;QAC3C,GACA,+HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,uBAAuB;;IAE1C;IAEA,sEAAsE;IACtE,MAAM,iBAAgB,aAAqB;QACzC,OAAO,+HAAA,CAAA,eAAY,CAAC,QAAQ,CAC1B,CAAC,cAAc,EAAE,eAAe,EAChC;YACE,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,eAAe;YACvE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,KAAK,CAAC;YAC7F,OAAO,qBAAqB,SAAS,IAAI;QAC3C,GACA,+HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,sBAAsB;;IAE3C;IAEA,kEAAkE;IAClE,MAAM,mBAAkB,aAAqB;QAC3C,OAAO,+HAAA,CAAA,eAAY,CAAC,QAAQ,CAC1B,CAAC,gBAAgB,EAAE,eAAe,EAClC;YACE,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,eAAe;YACzE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,KAAK,CAAC;YAC7F,OAAO,SAAS,IAAI;QACtB,GACA,+HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,sBAAsB;;IAE3C;IAEA,uDAAuD;IACvD,MAAM,iCAAgC,aAAqB,EAAE,SAAkB;QAC7E,IAAI;YACF,MAAM,SAAS,YAAY;gBAAE;YAAU,IAAI,CAAC;YAC5C,QAAQ,GAAG,CAAC,mDAAmD,eAAe,gBAAgB;YAE9F,6BAA6B;YAC7B,IAAI;gBACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,qBAAqB,CAAC,EAAE;oBAAE;gBAAO;gBAGxH,IAAI,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,GAAG;oBACtD,QAAQ,GAAG,CAAC,+BAA+B,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;oBACtE,OAAO,SAAS,IAAI,CAAC,IAAI;gBAC3B,OAAO;oBACL,QAAQ,IAAI,CAAC,mCAAmC,SAAS,IAAI;oBAC7D,OAAO,EAAE;gBACX;YACF,EAAE,OAAO,kBAAkB;gBACzB,QAAQ,IAAI,CAAC,4CAA4C;gBAEzD,gCAAgC;gBAChC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oCAAoC,EAAE,eAAe;gBAC3F,QAAQ,GAAG,CAAC,yBAAyB,SAAS,IAAI;gBAElD,IAAI,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;oBACjD,gDAAgD;oBAChD,IAAI,aAAa,SAAS,IAAI;oBAC9B,IAAI,WAAW;wBACb,aAAa,WAAW,MAAM,CAAC,CAAA,MAAO,IAAI,mBAAmB,KAAK;oBACpE;oBACA,QAAQ,GAAG,CAAC,8BAA8B,WAAW,MAAM,EAAE;oBAC7D,OAAO;gBACT,OAAO;oBACL,QAAQ,IAAI,CAAC,6CAA6C,SAAS,IAAI;oBACvE,OAAO,EAAE;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YAEd,OAAO,EAAE;QACX;IACF;IAEA,qDAAqD;IACrD,MAAM,qBAAoB,aAAqB,EAAE,SAAkB;QACjE,MAAM,SAAS,YAAY;YAAE;QAAU,IAAI,CAAC;QAC5C,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,kBAAkB,CAAC,EAAE;YAAE;QAAO;QACrH,OAAO,SAAS,IAAI;IACtB;AACF", "debugId": null}}, {"offset": {"line": 499, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/services/task-assignment.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\r\n\r\n// Generic Task interface for different types of tasks\r\nexport interface GenericTask {\r\n  task_id: string;\r\n  task_type: 'application' | 'complaint' | 'data_breach' | 'evaluation' | 'inspection';\r\n  task_number: string;\r\n  title: string;\r\n  description: string;\r\n  status: string;\r\n  priority?: 'low' | 'medium' | 'high' | 'urgent';\r\n  created_at: string;\r\n  updated_at: string;\r\n  assigned_to?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n  assigned_by?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n  assigned_at?: string;\r\n  due_date?: string;\r\n  metadata?: {\r\n    [key: string]: unknown;\r\n  };\r\n}\r\n\r\n// Legacy interface for backward compatibility\r\nexport interface TaskAssignmentApplication {\r\n  application_id: string;\r\n  application_number: string;\r\n  status: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  applicant: {\r\n    applicant_id: string;\r\n    company_name: string;\r\n    first_name: string;\r\n    last_name: string;\r\n  };\r\n  license_category: {\r\n    license_category_id: string;\r\n    name: string;\r\n    license_type: {\r\n      license_type_id: string;\r\n      name: string;\r\n    };\r\n  };\r\n  assignee?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n  assigned_at?: string;\r\n}\r\n\r\nexport interface TaskAssignmentOfficer {\r\n  user_id: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  email: string;\r\n  department?: string;\r\n}\r\n\r\nexport interface AssignApplicationRequest {\r\n  assignedTo: string;\r\n}\r\n\r\nexport interface PaginatedResponse<T> {\r\n  data: T[];\r\n  meta: {\r\n    itemCount: number;\r\n    totalItems: number;\r\n    itemsPerPage: number;\r\n    totalPages: number;\r\n    currentPage: number;\r\n  };\r\n  links: {\r\n    first: string;\r\n    previous: string;\r\n    next: string;\r\n    last: string;\r\n  };\r\n}\r\n\r\nexport const taskAssignmentService = {\r\n  // Generic task management methods\r\n  getUnassignedTasks: async (params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    task_type?: string;\r\n  }) => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n    if (params?.task_type) searchParams.append('task_type', params.task_type);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/tasks/unassigned${queryString ? `?${queryString}` : ''}`;\r\n\r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  getAssignedTasks: async (params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    task_type?: string;\r\n  }) => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n    if (params?.task_type) searchParams.append('task_type', params.task_type);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/tasks/assigned${queryString ? `?${queryString}` : ''}`;\r\n\r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  assignTask: async (taskId: string, assignData: { assignedTo: string; comment?: string }) => {\r\n    const response = await apiClient.put(`/tasks/${taskId}/assign`, assignData);\r\n    return response.data;\r\n  },\r\n\r\n  getTaskById: async (taskId: string) => {\r\n    const response = await apiClient.get(`/tasks/${taskId}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Legacy application-specific methods (for backward compatibility)\r\n  getUnassignedApplications: async (params?: { page?: number; limit?: number; search?: string }) => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/applications/unassigned${queryString ? `?${queryString}` : ''}`;\r\n    \r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  // Get all applications (including assigned)\r\n  getAllApplications: async (params?: { page?: number; limit?: number; search?: string }) => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/applications${queryString ? `?${queryString}` : ''}`;\r\n    \r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  // Get applications assigned to current user\r\n  getMyAssignedApplications: async (params?: { page?: number; limit?: number; search?: string }) => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/applications/assigned/me${queryString ? `?${queryString}` : ''}`;\r\n    \r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  // Get officers for assignment\r\n  getOfficers: async () => {\r\n    try {\r\n      const response = await apiClient.get('/users');\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error fetching officers:', error);\r\n      return { data: [] };\r\n    }\r\n  },\r\n\r\n  // Assign application to officer\r\n  assignApplication: async (applicationId: string, assignData: AssignApplicationRequest) => {\r\n    const response = await apiClient.put(`/applications/${applicationId}/assign`, assignData);\r\n    return response.data;\r\n  },\r\n\r\n  // Get application details\r\n  getApplication: async (applicationId: string) => {\r\n    const response = await apiClient.get(`/applications/${applicationId}`);\r\n    return response.data;\r\n  },\r\n};"], "names": [], "mappings": ";;;AAAA;;AA2FO,MAAM,wBAAwB;IACnC,kCAAkC;IAClC,oBAAoB,OAAO;QAMzB,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAC/D,IAAI,QAAQ,WAAW,aAAa,MAAM,CAAC,aAAa,OAAO,SAAS;QAExE,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAEtE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB,OAAO;QAMvB,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAC/D,IAAI,QAAQ,WAAW,aAAa,MAAM,CAAC,aAAa,OAAO,SAAS;QAExE,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAEpE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,OAAO,OAAO,CAAC,EAAE;QAChE,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ;QACvD,OAAO,SAAS,IAAI;IACtB;IAEA,mEAAmE;IACnE,2BAA2B,OAAO;QAChC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAE/D,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,wBAAwB,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAE7E,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,4CAA4C;IAC5C,oBAAoB,OAAO;QACzB,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAE/D,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAElE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,4CAA4C;IAC5C,2BAA2B,OAAO;QAChC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAE/D,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,yBAAyB,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAE9E,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,8BAA8B;IAC9B,aAAa;QACX,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;gBAAE,MAAM,EAAE;YAAC;QACpB;IACF;IAEA,gCAAgC;IAChC,mBAAmB,OAAO,eAAuB;QAC/C,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,cAAc,OAAO,CAAC,EAAE;QAC9E,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe;QACrE,OAAO,SAAS,IAAI;IACtB;AACF", "debugId": null}}, {"offset": {"line": 598, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/common/AssignModal.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useToast } from '@/contexts/ToastContext';\nimport { taskAssignmentService } from '@/services/task-assignment';\n\ninterface Officer {\n  user_id: string;\n  first_name: string;\n  last_name: string;\n  email: string;\n  department?: string;\n}\n\ninterface AssignModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  itemId: string | null;\n  itemType: 'data_breach' | 'application' | 'complaint';\n  itemTitle?: string;\n  onAssignSuccess?: () => void;\n}\n\nconst AssignModal: React.FC<AssignModalProps> = ({\n  isOpen,\n  onClose,\n  itemId,\n  itemType,\n  itemTitle,\n  onAssignSuccess\n}) => {\n  const { showSuccess, showError } = useToast();\n  const [officers, setOfficers] = useState<Officer[]>([]);\n  const [filteredOfficers, setFilteredOfficers] = useState<Officer[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [assigning, setAssigning] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedOfficer, setSelectedOfficer] = useState<string>('');\n  const [comment, setComment] = useState('');\n\n  useEffect(() => {\n    if (isOpen) {\n      fetchOfficers();\n      setSearchQuery('');\n      setSelectedOfficer('');\n      setComment('');\n    }\n  }, [isOpen]);\n\n  useEffect(() => {\n    // Filter officers based on search query\n    if (searchQuery.trim() === '') {\n      setFilteredOfficers(officers);\n    } else {\n      const filtered = officers.filter(officer =>\n        `${officer.first_name} ${officer.last_name}`.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        officer.email.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        officer.department?.toLowerCase().includes(searchQuery.toLowerCase())\n      );\n      setFilteredOfficers(filtered);\n    }\n  }, [officers, searchQuery]);\n\n  const fetchOfficers = async () => {\n    setLoading(true);\n    try {\n      const response = await taskAssignmentService.getOfficers();\n      setOfficers(response.data || []);\n    } catch (error) {\n      console.error('Error fetching officers:', error);\n      showError('Failed to load officers');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleAssign = async () => {\n    if (!selectedOfficer || !itemId) {\n      showError('Please select an officer');\n      return;\n    }\n\n    setAssigning(true);\n    try {\n      // Create a task for the assignment\n      await taskAssignmentService.assignTask(itemId, {\n        assignedTo: selectedOfficer,\n        comment: comment.trim() || undefined\n      });\n\n      showSuccess('Successfully assigned to officer');\n      onAssignSuccess?.();\n      onClose();\n    } catch (error) {\n      console.error('Error assigning task:', error);\n      showError('Failed to assign task');\n    } finally {\n      setAssigning(false);\n    }\n  };\n\n  const getSelectedOfficerDetails = () => {\n    return officers.find(officer => officer.user_id === selectedOfficer);\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n      <div className=\"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800\">\n        <div className=\"mt-3\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between mb-6\">\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n              Assign {itemType.replace('_', ' ').toUpperCase()}\n            </h3>\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            >\n              <i className=\"ri-close-line text-xl\"></i>\n            </button>\n          </div>\n\n          {/* Item Details */}\n          {itemTitle && (\n            <div className=\"mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n              <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\n                Item to Assign:\n              </h4>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">{itemTitle}</p>\n            </div>\n          )}\n\n          {/* Search Officers */}\n          <div className=\"mb-6\">\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Search Officers\n            </label>\n            <input\n              type=\"text\"\n              placeholder=\"Search by name, email, or department...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n            />\n          </div>\n\n          {/* Officers List */}\n          <div className=\"mb-6\">\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Select Officer ({filteredOfficers.length} found)\n            </label>\n            <div className=\"max-h-64 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md\">\n              {loading ? (\n                <div className=\"p-4 text-center text-gray-500 dark:text-gray-400\">\n                  Loading officers...\n                </div>\n              ) : filteredOfficers.length === 0 ? (\n                <div className=\"p-4 text-center text-gray-500 dark:text-gray-400\">\n                  No officers found\n                </div>\n              ) : (\n                filteredOfficers.map((officer) => (\n                  <div\n                    key={officer.user_id}\n                    className={`p-3 border-b border-gray-200 dark:border-gray-600 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 ${\n                      selectedOfficer === officer.user_id ? 'bg-blue-50 dark:bg-blue-900' : ''\n                    }`}\n                    onClick={() => setSelectedOfficer(officer.user_id)}\n                  >\n                    <div className=\"flex items-center\">\n                      <input\n                        type=\"radio\"\n                        name=\"officer\"\n                        value={officer.user_id}\n                        checked={selectedOfficer === officer.user_id}\n                        onChange={() => setSelectedOfficer(officer.user_id)}\n                        className=\"mr-3\"\n                      />\n                      <div className=\"flex-1\">\n                        <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\n                          {officer.first_name} {officer.last_name}\n                        </div>\n                        <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                          {officer.email}\n                        </div>\n                        {officer.department && (\n                          <div className=\"text-xs text-gray-400 dark:text-gray-500\">\n                            {officer.department}\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                ))\n              )}\n            </div>\n          </div>\n\n          {/* Comment */}\n          <div className=\"mb-6\">\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Assignment Comment (Optional)\n            </label>\n            <textarea\n              value={comment}\n              onChange={(e) => setComment(e.target.value)}\n              placeholder=\"Add any notes or instructions for the assigned officer...\"\n              rows={3}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n            />\n          </div>\n\n          {/* Selected Officer Summary */}\n          {selectedOfficer && (\n            <div className=\"mb-6 p-4 bg-green-50 dark:bg-green-900 rounded-lg\">\n              <h4 className=\"text-sm font-medium text-green-900 dark:text-green-100 mb-2\">\n                Selected Officer:\n              </h4>\n              <div className=\"text-sm text-green-700 dark:text-green-200\">\n                {getSelectedOfficerDetails()?.first_name} {getSelectedOfficerDetails()?.last_name}\n                <br />\n                {getSelectedOfficerDetails()?.email}\n                {getSelectedOfficerDetails()?.department && (\n                  <>\n                    <br />\n                    Department: {getSelectedOfficerDetails()?.department}\n                  </>\n                )}\n              </div>\n            </div>\n          )}\n\n          {/* Actions */}\n          <div className=\"flex justify-end space-x-3\">\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"button\"\n              onClick={handleAssign}\n              disabled={!selectedOfficer || assigning}\n              className=\"px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {assigning ? 'Assigning...' : 'Assign'}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AssignModal;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAuBA,MAAM,cAA0C,CAAC,EAC/C,MAAM,EACN,OAAO,EACP,MAAM,EACN,QAAQ,EACR,SAAS,EACT,eAAe,EAChB;IACC,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAC1C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV;YACA,eAAe;YACf,mBAAmB;YACnB,WAAW;QACb;IACF,GAAG;QAAC;KAAO;IAEX,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAwC;QACxC,IAAI,YAAY,IAAI,OAAO,IAAI;YAC7B,oBAAoB;QACtB,OAAO;YACL,MAAM,WAAW,SAAS,MAAM,CAAC,CAAA,UAC/B,GAAG,QAAQ,UAAU,CAAC,CAAC,EAAE,QAAQ,SAAS,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC3F,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC5D,QAAQ,UAAU,EAAE,cAAc,SAAS,YAAY,WAAW;YAEpE,oBAAoB;QACtB;IACF,GAAG;QAAC;QAAU;KAAY;IAE1B,MAAM,gBAAgB;QACpB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,qIAAA,CAAA,wBAAqB,CAAC,WAAW;YACxD,YAAY,SAAS,IAAI,IAAI,EAAE;QACjC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,UAAU;QACZ,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,mBAAmB,CAAC,QAAQ;YAC/B,UAAU;YACV;QACF;QAEA,aAAa;QACb,IAAI;YACF,mCAAmC;YACnC,MAAM,qIAAA,CAAA,wBAAqB,CAAC,UAAU,CAAC,QAAQ;gBAC7C,YAAY;gBACZ,SAAS,QAAQ,IAAI,MAAM;YAC7B;YAEA,YAAY;YACZ;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,UAAU;QACZ,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,4BAA4B;QAChC,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,OAAO,KAAK;IACtD;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAuD;oCAC3D,SAAS,OAAO,CAAC,KAAK,KAAK,WAAW;;;;;;;0CAEhD,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC;oCAAE,WAAU;;;;;;;;;;;;;;;;;oBAKhB,2BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA4D;;;;;;0CAG1E,8OAAC;gCAAE,WAAU;0CAA4C;;;;;;;;;;;;kCAK7D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,WAAU;;;;;;;;;;;;kCAKd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,WAAU;;oCAAkE;oCAChE,iBAAiB,MAAM;oCAAC;;;;;;;0CAE3C,8OAAC;gCAAI,WAAU;0CACZ,wBACC,8OAAC;oCAAI,WAAU;8CAAmD;;;;;2CAGhE,iBAAiB,MAAM,KAAK,kBAC9B,8OAAC;oCAAI,WAAU;8CAAmD;;;;;2CAIlE,iBAAiB,GAAG,CAAC,CAAC,wBACpB,8OAAC;wCAEC,WAAW,CAAC,yGAAyG,EACnH,oBAAoB,QAAQ,OAAO,GAAG,gCAAgC,IACtE;wCACF,SAAS,IAAM,mBAAmB,QAAQ,OAAO;kDAEjD,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAO,QAAQ,OAAO;oDACtB,SAAS,oBAAoB,QAAQ,OAAO;oDAC5C,UAAU,IAAM,mBAAmB,QAAQ,OAAO;oDAClD,WAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;gEACZ,QAAQ,UAAU;gEAAC;gEAAE,QAAQ,SAAS;;;;;;;sEAEzC,8OAAC;4DAAI,WAAU;sEACZ,QAAQ,KAAK;;;;;;wDAEf,QAAQ,UAAU,kBACjB,8OAAC;4DAAI,WAAU;sEACZ,QAAQ,UAAU;;;;;;;;;;;;;;;;;;uCAxBtB,QAAQ,OAAO;;;;;;;;;;;;;;;;kCAoC9B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,8OAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;gCAC1C,aAAY;gCACZ,MAAM;gCACN,WAAU;;;;;;;;;;;;oBAKb,iCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA8D;;;;;;0CAG5E,8OAAC;gCAAI,WAAU;;oCACZ,6BAA6B;oCAAW;oCAAE,6BAA6B;kDACxE,8OAAC;;;;;oCACA,6BAA6B;oCAC7B,6BAA6B,4BAC5B;;0DACE,8OAAC;;;;;4CAAK;4CACO,6BAA6B;;;;;;;;;;;;;;;kCAQpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,UAAU,CAAC,mBAAmB;gCAC9B,WAAU;0CAET,YAAY,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5C;uCAEe", "debugId": null}}, {"offset": {"line": 1011, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/common/AssignButton.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport AssignModal from './AssignModal';\n\ninterface AssignButtonProps {\n  itemId: string;\n  itemType: 'data_breach' | 'application' | 'complaint';\n  itemTitle?: string;\n  onAssignSuccess?: () => void;\n  className?: string;\n  size?: 'sm' | 'md' | 'lg';\n  variant?: 'primary' | 'secondary' | 'success';\n  disabled?: boolean;\n  children?: React.ReactNode;\n}\n\nconst AssignButton: React.FC<AssignButtonProps> = ({\n  itemId,\n  itemType,\n  itemTitle,\n  onAssignSuccess,\n  className = '',\n  size = 'sm',\n  variant = 'success',\n  disabled = false,\n  children\n}) => {\n  const [showAssignModal, setShowAssignModal] = useState(false);\n\n  const handleAssignClick = () => {\n    setShowAssignModal(true);\n  };\n\n  const handleCloseModal = () => {\n    setShowAssignModal(false);\n  };\n\n  const handleAssignSuccess = () => {\n    setShowAssignModal(false);\n    onAssignSuccess?.();\n  };\n\n  // Size classes\n  const sizeClasses = {\n    sm: 'px-3 py-1 text-xs',\n    md: 'px-4 py-2 text-sm',\n    lg: 'px-6 py-3 text-base'\n  };\n\n  // Variant classes\n  const variantClasses = {\n    primary: 'text-white bg-primary hover:bg-primary-dark focus:ring-primary',\n    secondary: 'text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 focus:ring-gray-500',\n    success: 'text-white bg-green-600 hover:bg-green-700 focus:ring-green-500'\n  };\n\n  // Base classes\n  const baseClasses = 'inline-flex items-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed';\n\n  // Combine all classes\n  const buttonClasses = `${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]} ${className}`;\n\n  return (\n    <>\n      <button\n        type=\"button\"\n        onClick={handleAssignClick}\n        disabled={disabled}\n        className={buttonClasses}\n        title={`Assign ${itemType.replace('_', ' ')} to officer`}\n      >\n        <i className=\"ri-user-add-line mr-1\"></i>\n        {children || 'Assign'}\n      </button>\n\n      <AssignModal\n        isOpen={showAssignModal}\n        onClose={handleCloseModal}\n        itemId={itemId}\n        itemType={itemType}\n        itemTitle={itemTitle}\n        onAssignSuccess={handleAssignSuccess}\n      />\n    </>\n  );\n};\n\nexport default AssignButton;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAiBA,MAAM,eAA4C,CAAC,EACjD,MAAM,EACN,QAAQ,EACR,SAAS,EACT,eAAe,EACf,YAAY,EAAE,EACd,OAAO,IAAI,EACX,UAAU,SAAS,EACnB,WAAW,KAAK,EAChB,QAAQ,EACT;IACC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,oBAAoB;QACxB,mBAAmB;IACrB;IAEA,MAAM,mBAAmB;QACvB,mBAAmB;IACrB;IAEA,MAAM,sBAAsB;QAC1B,mBAAmB;QACnB;IACF;IAEA,eAAe;IACf,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,kBAAkB;IAClB,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,SAAS;IACX;IAEA,eAAe;IACf,MAAM,cAAc;IAEpB,sBAAsB;IACtB,MAAM,gBAAgB,GAAG,YAAY,CAAC,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC,EAAE,WAAW;IAEnG,qBACE;;0BACE,8OAAC;gBACC,MAAK;gBACL,SAAS;gBACT,UAAU;gBACV,WAAW;gBACX,OAAO,CAAC,OAAO,EAAE,SAAS,OAAO,CAAC,KAAK,KAAK,WAAW,CAAC;;kCAExD,8OAAC;wBAAE,WAAU;;;;;;oBACZ,YAAY;;;;;;;0BAGf,8OAAC,2IAAA,CAAA,UAAW;gBACV,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,UAAU;gBACV,WAAW;gBACX,iBAAiB;;;;;;;;AAIzB;uCAEe", "debugId": null}}, {"offset": {"line": 1094, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/common/Pagination.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\n\r\n// Import the pagination response type from the existing structure\r\ninterface PaginationMeta {\r\n  itemsPerPage: number;\r\n  totalItems: number;\r\n  currentPage: number;\r\n  totalPages: number;\r\n  sortBy: [string, string][];\r\n  searchBy: string[];\r\n  search: string;\r\n  filter?: Record<string, string | string[]>;\r\n}\r\n\r\ninterface PaginationProps {\r\n  meta: PaginationMeta;\r\n  onPageChange: (page: number) => void;\r\n  onPageSizeChange?: (pageSize: number) => void;\r\n  showFirstLast?: boolean;\r\n  showPageSizeSelector?: boolean;\r\n  showInfo?: boolean;\r\n  maxVisiblePages?: number;\r\n  pageSizeOptions?: number[];\r\n  className?: string;\r\n}\r\n\r\nconst Pagination: React.FC<PaginationProps> = ({\r\n  meta,\r\n  onPageChange,\r\n  onPageSizeChange,\r\n  showFirstLast = true,\r\n  showPageSizeSelector = true,\r\n  showInfo = true,\r\n  maxVisiblePages = 7,\r\n  pageSizeOptions = [10, 25, 50, 100],\r\n  className = ''\r\n}) => {\r\n  const { currentPage, totalPages, totalItems, itemsPerPage } = meta;\r\n\r\n  // Don't render if there's only one page or no pages and no additional features\r\n  if (totalPages <= 1 && !showPageSizeSelector && !showInfo) {\r\n    return null;\r\n  }\r\n\r\n  // Calculate current items range\r\n  const startItem = totalItems > 0 ? (currentPage - 1) * itemsPerPage + 1 : 0;\r\n  const endItem = Math.min(currentPage * itemsPerPage, totalItems);\r\n\r\n  // Calculate which pages to show\r\n  const getVisiblePages = (): (number | string)[] => {\r\n    const pages: (number | string)[] = [];\r\n    \r\n    // If total pages is less than or equal to maxVisiblePages, show all\r\n    if (totalPages <= maxVisiblePages) {\r\n      for (let i = 1; i <= totalPages; i++) {\r\n        pages.push(i);\r\n      }\r\n      return pages;\r\n    }\r\n\r\n    // Always show first page\r\n    pages.push(1);\r\n\r\n    // Calculate start and end of the visible range around current page\r\n    const sidePages = Math.floor((maxVisiblePages - 3) / 2); // -3 for first, last, and current\r\n    let startPage = Math.max(2, currentPage - sidePages);\r\n    let endPage = Math.min(totalPages - 1, currentPage + sidePages);\r\n\r\n    // Adjust if we're near the beginning\r\n    if (currentPage <= sidePages + 2) {\r\n      startPage = 2;\r\n      endPage = Math.min(totalPages - 1, maxVisiblePages - 1);\r\n    }\r\n\r\n    // Adjust if we're near the end\r\n    if (currentPage >= totalPages - sidePages - 1) {\r\n      startPage = Math.max(2, totalPages - maxVisiblePages + 2);\r\n      endPage = totalPages - 1;\r\n    }\r\n\r\n    // Add ellipsis after first page if needed\r\n    if (startPage > 2) {\r\n      pages.push('...');\r\n    }\r\n\r\n    // Add pages in the visible range\r\n    for (let i = startPage; i <= endPage; i++) {\r\n      pages.push(i);\r\n    }\r\n\r\n    // Add ellipsis before last page if needed\r\n    if (endPage < totalPages - 1) {\r\n      pages.push('...');\r\n    }\r\n\r\n    // Always show last page (if it's not already included)\r\n    if (totalPages > 1) {\r\n      pages.push(totalPages);\r\n    }\r\n\r\n    return pages;\r\n  };\r\n\r\n  const visiblePages = getVisiblePages();\r\n\r\n  const handlePageClick = (page: number | string) => {\r\n    if (typeof page === 'number' && page !== currentPage) {\r\n      onPageChange(page);\r\n    }\r\n  };\r\n\r\n  const handlePageSizeChange = (event: React.ChangeEvent<HTMLSelectElement>) => {\r\n    const newPageSize = parseInt(event.target.value);\r\n    if (onPageSizeChange) {\r\n      onPageSizeChange(newPageSize);\r\n    }\r\n  };\r\n\r\n  const handlePrevious = () => {\r\n    if (currentPage > 1) {\r\n      onPageChange(currentPage - 1);\r\n    }\r\n  };\r\n\r\n  const handleNext = () => {\r\n    if (currentPage < totalPages) {\r\n      onPageChange(currentPage + 1);\r\n    }\r\n  };\r\n\r\n  const handleFirst = () => {\r\n    if (currentPage !== 1) {\r\n      onPageChange(1);\r\n    }\r\n  };\r\n\r\n  const handleLast = () => {\r\n    if (currentPage !== totalPages) {\r\n      onPageChange(totalPages);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={`flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0 px-6 py-3 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 ${className}`}>\r\n      {/* Left side - Info and page size selector */}\r\n      <div className=\"flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-4\">\r\n        {/* Items info */}\r\n        {showInfo && totalItems > 0 && (\r\n          <div className=\"text-sm text-gray-700 dark:text-gray-300\">\r\n            Showing <span className=\"font-medium\">{startItem}</span> to{' '}\r\n            <span className=\"font-medium\">{endItem}</span> of{' '}\r\n            <span className=\"font-medium\">{totalItems}</span> results\r\n          </div>\r\n        )}\r\n\r\n        {/* Page size selector */}\r\n        {showPageSizeSelector && onPageSizeChange && (\r\n          <div className=\"flex items-center space-x-2\">\r\n            <select\r\n              value={itemsPerPage}\r\n              onChange={handlePageSizeChange}\r\n              className=\"px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent\"\r\n            >\r\n              {pageSizeOptions.map((size) => (\r\n                <option key={size} value={size}>\r\n                  {size} per page\r\n                </option>\r\n              ))}\r\n            </select>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Right side - Pagination controls */}\r\n      {totalPages > 1 && (\r\n        <nav className=\"flex items-center space-x-1\" aria-label=\"Pagination\">\r\n          {/* First page button */}\r\n          {showFirstLast && currentPage > 1 && (\r\n            <button\r\n              onClick={handleFirst}\r\n              className=\"inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-md hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300\"\r\n              aria-label=\"Go to first page\"\r\n            >\r\n              <i className=\"ri-skip-back-line\"></i>\r\n            </button>\r\n          )}\r\n\r\n          {/* Previous button */}\r\n          <button\r\n            onClick={handlePrevious}\r\n            disabled={currentPage === 1}\r\n            className={`inline-flex items-center px-2 py-2 text-sm font-medium border ${\r\n              currentPage === 1\r\n                ? 'text-gray-300 bg-gray-100 border-gray-300 cursor-not-allowed dark:bg-gray-700 dark:border-gray-600 dark:text-gray-500'\r\n                : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300'\r\n            } ${showFirstLast && currentPage > 1 ? '' : 'rounded-l-md'}`}\r\n            aria-label=\"Go to previous page\"\r\n          >\r\n            <i className=\"ri-arrow-left-s-line\"></i>\r\n          </button>\r\n\r\n          {/* Page numbers */}\r\n          {visiblePages.map((page, index) => (\r\n            <React.Fragment key={index}>\r\n              {page === '...' ? (\r\n                <span className=\"inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400\">\r\n                  ...\r\n                </span>\r\n              ) : (\r\n                <button\r\n                  onClick={() => handlePageClick(page)}\r\n                  className={`inline-flex items-center px-4 py-2 text-sm font-medium border ${\r\n                    page === currentPage\r\n                      ? 'text-white bg-red-600 border-red-600 hover:bg-red-700'\r\n                      : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300'\r\n                  }`}\r\n                  aria-label={`Go to page ${page}`}\r\n                  aria-current={page === currentPage ? 'page' : undefined}\r\n                >\r\n                  {page}\r\n                </button>\r\n              )}\r\n            </React.Fragment>\r\n          ))}\r\n\r\n          {/* Next button */}\r\n          <button\r\n            onClick={handleNext}\r\n            disabled={currentPage === totalPages}\r\n            className={`inline-flex items-center px-2 py-2 text-sm font-medium border ${\r\n              currentPage === totalPages\r\n                ? 'text-gray-300 bg-gray-100 border-gray-300 cursor-not-allowed dark:bg-gray-700 dark:border-gray-600 dark:text-gray-500'\r\n                : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300'\r\n            } ${showFirstLast && currentPage < totalPages ? '' : 'rounded-r-md'}`}\r\n            aria-label=\"Go to next page\"\r\n          >\r\n            <i className=\"ri-arrow-right-s-line\"></i>\r\n          </button>\r\n\r\n          {/* Last page button */}\r\n          {showFirstLast && currentPage < totalPages && (\r\n            <button\r\n              onClick={handleLast}\r\n              className=\"inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-md hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300\"\r\n              aria-label=\"Go to last page\"\r\n            >\r\n              <i className=\"ri-skip-forward-line\"></i>\r\n            </button>\r\n          )}\r\n        </nav>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Pagination;\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AA4BA,MAAM,aAAwC,CAAC,EAC7C,IAAI,EACJ,YAAY,EACZ,gBAAgB,EAChB,gBAAgB,IAAI,EACpB,uBAAuB,IAAI,EAC3B,WAAW,IAAI,EACf,kBAAkB,CAAC,EACnB,kBAAkB;IAAC;IAAI;IAAI;IAAI;CAAI,EACnC,YAAY,EAAE,EACf;IACC,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG;IAE9D,+EAA+E;IAC/E,IAAI,cAAc,KAAK,CAAC,wBAAwB,CAAC,UAAU;QACzD,OAAO;IACT;IAEA,gCAAgC;IAChC,MAAM,YAAY,aAAa,IAAI,CAAC,cAAc,CAAC,IAAI,eAAe,IAAI;IAC1E,MAAM,UAAU,KAAK,GAAG,CAAC,cAAc,cAAc;IAErD,gCAAgC;IAChC,MAAM,kBAAkB;QACtB,MAAM,QAA6B,EAAE;QAErC,oEAAoE;QACpE,IAAI,cAAc,iBAAiB;YACjC,IAAK,IAAI,IAAI,GAAG,KAAK,YAAY,IAAK;gBACpC,MAAM,IAAI,CAAC;YACb;YACA,OAAO;QACT;QAEA,yBAAyB;QACzB,MAAM,IAAI,CAAC;QAEX,mEAAmE;QACnE,MAAM,YAAY,KAAK,KAAK,CAAC,CAAC,kBAAkB,CAAC,IAAI,IAAI,kCAAkC;QAC3F,IAAI,YAAY,KAAK,GAAG,CAAC,GAAG,cAAc;QAC1C,IAAI,UAAU,KAAK,GAAG,CAAC,aAAa,GAAG,cAAc;QAErD,qCAAqC;QACrC,IAAI,eAAe,YAAY,GAAG;YAChC,YAAY;YACZ,UAAU,KAAK,GAAG,CAAC,aAAa,GAAG,kBAAkB;QACvD;QAEA,+BAA+B;QAC/B,IAAI,eAAe,aAAa,YAAY,GAAG;YAC7C,YAAY,KAAK,GAAG,CAAC,GAAG,aAAa,kBAAkB;YACvD,UAAU,aAAa;QACzB;QAEA,0CAA0C;QAC1C,IAAI,YAAY,GAAG;YACjB,MAAM,IAAI,CAAC;QACb;QAEA,iCAAiC;QACjC,IAAK,IAAI,IAAI,WAAW,KAAK,SAAS,IAAK;YACzC,MAAM,IAAI,CAAC;QACb;QAEA,0CAA0C;QAC1C,IAAI,UAAU,aAAa,GAAG;YAC5B,MAAM,IAAI,CAAC;QACb;QAEA,uDAAuD;QACvD,IAAI,aAAa,GAAG;YAClB,MAAM,IAAI,CAAC;QACb;QAEA,OAAO;IACT;IAEA,MAAM,eAAe;IAErB,MAAM,kBAAkB,CAAC;QACvB,IAAI,OAAO,SAAS,YAAY,SAAS,aAAa;YACpD,aAAa;QACf;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,cAAc,SAAS,MAAM,MAAM,CAAC,KAAK;QAC/C,IAAI,kBAAkB;YACpB,iBAAiB;QACnB;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,cAAc,GAAG;YACnB,aAAa,cAAc;QAC7B;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,cAAc,YAAY;YAC5B,aAAa,cAAc;QAC7B;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,gBAAgB,GAAG;YACrB,aAAa;QACf;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,gBAAgB,YAAY;YAC9B,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,gKAAgK,EAAE,WAAW;;0BAE5L,8OAAC;gBAAI,WAAU;;oBAEZ,YAAY,aAAa,mBACxB,8OAAC;wBAAI,WAAU;;4BAA2C;0CAChD,8OAAC;gCAAK,WAAU;0CAAe;;;;;;4BAAiB;4BAAI;0CAC5D,8OAAC;gCAAK,WAAU;0CAAe;;;;;;4BAAe;4BAAI;0CAClD,8OAAC;gCAAK,WAAU;0CAAe;;;;;;4BAAkB;;;;;;;oBAKpD,wBAAwB,kCACvB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,OAAO;4BACP,UAAU;4BACV,WAAU;sCAET,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC;oCAAkB,OAAO;;wCACvB;wCAAK;;mCADK;;;;;;;;;;;;;;;;;;;;;YAUtB,aAAa,mBACZ,8OAAC;gBAAI,WAAU;gBAA8B,cAAW;;oBAErD,iBAAiB,cAAc,mBAC9B,8OAAC;wBACC,SAAS;wBACT,WAAU;wBACV,cAAW;kCAEX,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;kCAKjB,8OAAC;wBACC,SAAS;wBACT,UAAU,gBAAgB;wBAC1B,WAAW,CAAC,8DAA8D,EACxE,gBAAgB,IACZ,0HACA,uLACL,CAAC,EAAE,iBAAiB,cAAc,IAAI,KAAK,gBAAgB;wBAC5D,cAAW;kCAEX,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;oBAId,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;sCACZ,SAAS,sBACR,8OAAC;gCAAK,WAAU;0CAAgK;;;;;qDAIhL,8OAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAW,CAAC,8DAA8D,EACxE,SAAS,cACL,0DACA,wLACJ;gCACF,cAAY,CAAC,WAAW,EAAE,MAAM;gCAChC,gBAAc,SAAS,cAAc,SAAS;0CAE7C;;;;;;2BAhBc;;;;;kCAuBvB,8OAAC;wBACC,SAAS;wBACT,UAAU,gBAAgB;wBAC1B,WAAW,CAAC,8DAA8D,EACxE,gBAAgB,aACZ,0HACA,uLACL,CAAC,EAAE,iBAAiB,cAAc,aAAa,KAAK,gBAAgB;wBACrE,cAAW;kCAEX,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;oBAId,iBAAiB,cAAc,4BAC9B,8OAAC;wBACC,SAAS;wBACT,WAAU;wBACV,cAAW;kCAEX,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAO3B;uCAEe", "debugId": null}}, {"offset": {"line": 1384, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/common/DataTable.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { PaginatedResponse, PaginateQuery } from '../../services/userService';\r\nimport Pagination from './Pagination';\r\n\r\ninterface Column<T> {\r\n  key: keyof T | string;\r\n  label: string;\r\n  sortable?: boolean;\r\n  searchable?: boolean;\r\n  render?: (value: any, item: T) => React.ReactNode;\r\n  className?: string;\r\n}\r\n\r\ninterface DataTableProps<T> {\r\n  columns: Column<T>[];\r\n  data: PaginatedResponse<T> | null;\r\n  loading?: boolean;\r\n  onQueryChange: (query: PaginateQuery) => void;\r\n  searchPlaceholder?: string;\r\n  className?: string;\r\n}\r\n\r\nexport default function DataTable<T extends Record<string, any>>({\r\n  columns,\r\n  data,\r\n  loading = false,\r\n  onQueryChange,\r\n  searchPlaceholder = \"Search...\",\r\n  className = \"\",\r\n}: DataTableProps<T>) {\r\n  const [query, setQuery] = useState<PaginateQuery>({\r\n    page: 1,\r\n    limit: 10,\r\n    search: '',\r\n    sortBy: [],\r\n  });\r\n  const [searchInput, setSearchInput] = useState('');\r\n\r\n  // Debounced search effect\r\n  useEffect(() => {\r\n    const timeoutId = setTimeout(() => {\r\n      if (searchInput !== query.search) {\r\n        handleSearch(searchInput);\r\n      }\r\n    }, 300);\r\n\r\n    return () => clearTimeout(timeoutId);\r\n  }, [searchInput]);\r\n\r\n  const handleSearch = useCallback((search: string) => {\r\n    try {\r\n      const newQuery = { ...query, search, page: 1 };\r\n      setQuery(newQuery);\r\n      onQueryChange(newQuery);\r\n    } catch (error) {\r\n      console.error('Error handling search:', error);\r\n    }\r\n  }, [query, onQueryChange]);\r\n\r\n  const handleSort = (columnKey: string) => {\r\n    const currentSort = query.sortBy?.find(sort => sort.startsWith(columnKey));\r\n    let newSortBy: string[] = [];\r\n\r\n    if (!currentSort) {\r\n      newSortBy = [`${columnKey}:ASC`];\r\n    } else if (currentSort.endsWith(':ASC')) {\r\n      newSortBy = [`${columnKey}:DESC`];\r\n    } else {\r\n      newSortBy = [];\r\n    }\r\n\r\n    const newQuery = { ...query, sortBy: newSortBy, page: 1 };\r\n    setQuery(newQuery);\r\n    onQueryChange(newQuery);\r\n  };\r\n\r\n  const handlePageChange = (page: number) => {\r\n    const newQuery = { ...query, page };\r\n    setQuery(newQuery);\r\n    onQueryChange(newQuery);\r\n  };\r\n\r\n  const handleLimitChange = (limit: number) => {\r\n    const newQuery = { ...query, limit, page: 1 };\r\n    setQuery(newQuery);\r\n    onQueryChange(newQuery);\r\n  };\r\n\r\n  const getSortDirection = (columnKey: string): 'asc' | 'desc' | null => {\r\n    const currentSort = query.sortBy?.find(sort => sort.startsWith(columnKey));\r\n    if (!currentSort) return null;\r\n    return currentSort.endsWith(':ASC') ? 'asc' : 'desc';\r\n  };\r\n\r\n  // Handle null data case early\r\n  if (!data) {\r\n    return (\r\n      <div className={`bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden ${className}`}>\r\n        <div className=\"p-6 text-center text-gray-500 dark:text-gray-400\">\r\n          <div className=\"flex items-center justify-center\">\r\n            <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-red-600\"></div>\r\n            <span className=\"ml-2\">Loading...</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const handlePageSizeChange = (newPageSize: number) => {\r\n    const newQuery = { ...query, limit: newPageSize, page: 1 };\r\n    setQuery(newQuery);\r\n    onQueryChange(newQuery);\r\n  };\r\n\r\n  return (\r\n    <div className={`bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden ${className}`}>\r\n      {/* Search Bar */}\r\n      <div className=\"p-6 border-b border-gray-200 dark:border-gray-700\">\r\n        <div className=\"relative\">\r\n          <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n            <i className=\"ri-search-line text-gray-400 dark:text-gray-500\"></i>\r\n          </div>\r\n          <input\r\n            type=\"text\"\r\n            placeholder={searchPlaceholder}\r\n            value={searchInput}\r\n            onChange={(e) => setSearchInput(e.target.value)}\r\n            className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-gray-900 dark:text-gray-100\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Table */}\r\n      <div className=\"overflow-x-auto\" style={{ minHeight: '500px' }}>\r\n        <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\r\n          <thead className=\"bg-gray-50 dark:bg-gray-900\">\r\n            <tr>\r\n              {columns.map((column) => (\r\n                <th\r\n                  key={String(column.key)}\r\n                  className={`px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${\r\n                    column.sortable ? 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800' : ''\r\n                  } ${column.className || ''}`}\r\n                  onClick={() => column.sortable && handleSort(String(column.key))}\r\n                >\r\n                  <div className=\"flex items-center space-x-1\">\r\n                    <span>{column.label}</span>\r\n                    {column.sortable && (\r\n                      <div className=\"flex flex-col\">\r\n                        <i className={`ri-arrow-up-s-line text-xs ${\r\n                          getSortDirection(String(column.key)) === 'asc' ? 'text-red-600' : 'text-gray-400'\r\n                        }`}></i>\r\n                        <i className={`ri-arrow-down-s-line text-xs -mt-1 ${\r\n                          getSortDirection(String(column.key)) === 'desc' ? 'text-red-600' : 'text-gray-400'\r\n                        }`}></i>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </th>\r\n              ))}\r\n            </tr>\r\n          </thead>\r\n          <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\r\n            {loading ? (\r\n              <tr>\r\n                <td colSpan={columns.length} className=\"px-6 py-4 text-center text-gray-500 dark:text-gray-400\">\r\n                  <div className=\"flex items-center justify-center\">\r\n                    <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-red-600\"></div>\r\n                    <span className=\"ml-2\">Loading...</span>\r\n                  </div>\r\n                </td>\r\n              </tr>\r\n            ) : !data?.data || data.data.length === 0 ? (\r\n              <tr>\r\n                <td colSpan={columns.length} className=\"px-6 py-4 text-center text-gray-500 dark:text-gray-400\">\r\n                  No data found\r\n                </td>\r\n              </tr>\r\n            ) : (\r\n              data.data.map((item, index) => (\r\n                <tr key={index} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\r\n                  {columns.map((column) => (\r\n                    <td key={String(column.key)} className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100\">\r\n                      {column.render\r\n                        ? column.render(item[column.key as keyof T], item)\r\n                        : String(item[column.key as keyof T] || '')\r\n                      }\r\n                    </td>\r\n                  ))}\r\n                </tr>\r\n              ))\r\n            )}\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n\r\n      {/* Pagination */}\r\n      {data?.meta && (\r\n        <Pagination\r\n          meta={data.meta}\r\n          onPageChange={handlePageChange}\r\n          onPageSizeChange={handlePageSizeChange}\r\n          showFirstLast={true}\r\n          showPageSizeSelector={true}\r\n          showInfo={true}\r\n          maxVisiblePages={7}\r\n          pageSizeOptions={[10, 25, 50, 100]}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAJA;;;;AAwBe,SAAS,UAAyC,EAC/D,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,aAAa,EACb,oBAAoB,WAAW,EAC/B,YAAY,EAAE,EACI;IAClB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QAChD,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ,EAAE;IACZ;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,WAAW;YAC3B,IAAI,gBAAgB,MAAM,MAAM,EAAE;gBAChC,aAAa;YACf;QACF,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAY;IAEhB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,IAAI;YACF,MAAM,WAAW;gBAAE,GAAG,KAAK;gBAAE;gBAAQ,MAAM;YAAE;YAC7C,SAAS;YACT,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF,GAAG;QAAC;QAAO;KAAc;IAEzB,MAAM,aAAa,CAAC;QAClB,MAAM,cAAc,MAAM,MAAM,EAAE,KAAK,CAAA,OAAQ,KAAK,UAAU,CAAC;QAC/D,IAAI,YAAsB,EAAE;QAE5B,IAAI,CAAC,aAAa;YAChB,YAAY;gBAAC,GAAG,UAAU,IAAI,CAAC;aAAC;QAClC,OAAO,IAAI,YAAY,QAAQ,CAAC,SAAS;YACvC,YAAY;gBAAC,GAAG,UAAU,KAAK,CAAC;aAAC;QACnC,OAAO;YACL,YAAY,EAAE;QAChB;QAEA,MAAM,WAAW;YAAE,GAAG,KAAK;YAAE,QAAQ;YAAW,MAAM;QAAE;QACxD,SAAS;QACT,cAAc;IAChB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,WAAW;YAAE,GAAG,KAAK;YAAE;QAAK;QAClC,SAAS;QACT,cAAc;IAChB;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,WAAW;YAAE,GAAG,KAAK;YAAE;YAAO,MAAM;QAAE;QAC5C,SAAS;QACT,cAAc;IAChB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,cAAc,MAAM,MAAM,EAAE,KAAK,CAAA,OAAQ,KAAK,UAAU,CAAC;QAC/D,IAAI,CAAC,aAAa,OAAO;QACzB,OAAO,YAAY,QAAQ,CAAC,UAAU,QAAQ;IAChD;IAEA,8BAA8B;IAC9B,IAAI,CAAC,MAAM;QACT,qBACE,8OAAC;YAAI,WAAW,CAAC,4DAA4D,EAAE,WAAW;sBACxF,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAK,WAAU;sCAAO;;;;;;;;;;;;;;;;;;;;;;IAKjC;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,WAAW;YAAE,GAAG,KAAK;YAAE,OAAO;YAAa,MAAM;QAAE;QACzD,SAAS;QACT,cAAc;IAChB;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,4DAA4D,EAAE,WAAW;;0BAExF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;;;;;;;;;;sCAEf,8OAAC;4BACC,MAAK;4BACL,aAAa;4BACb,OAAO;4BACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4BAC9C,WAAU;;;;;;;;;;;;;;;;;0BAMhB,8OAAC;gBAAI,WAAU;gBAAkB,OAAO;oBAAE,WAAW;gBAAQ;0BAC3D,cAAA,8OAAC;oBAAM,WAAU;;sCACf,8OAAC;4BAAM,WAAU;sCACf,cAAA,8OAAC;0CACE,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;wCAEC,WAAW,CAAC,kGAAkG,EAC5G,OAAO,QAAQ,GAAG,4DAA4D,GAC/E,CAAC,EAAE,OAAO,SAAS,IAAI,IAAI;wCAC5B,SAAS,IAAM,OAAO,QAAQ,IAAI,WAAW,OAAO,OAAO,GAAG;kDAE9D,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAM,OAAO,KAAK;;;;;;gDAClB,OAAO,QAAQ,kBACd,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAW,CAAC,2BAA2B,EACxC,iBAAiB,OAAO,OAAO,GAAG,OAAO,QAAQ,iBAAiB,iBAClE;;;;;;sEACF,8OAAC;4DAAE,WAAW,CAAC,mCAAmC,EAChD,iBAAiB,OAAO,OAAO,GAAG,OAAO,SAAS,iBAAiB,iBACnE;;;;;;;;;;;;;;;;;;uCAfH,OAAO,OAAO,GAAG;;;;;;;;;;;;;;;sCAuB9B,8OAAC;4BAAM,WAAU;sCACd,wBACC,8OAAC;0CACC,cAAA,8OAAC;oCAAG,SAAS,QAAQ,MAAM;oCAAE,WAAU;8CACrC,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAAO;;;;;;;;;;;;;;;;;;;;;uCAI3B,CAAC,MAAM,QAAQ,KAAK,IAAI,CAAC,MAAM,KAAK,kBACtC,8OAAC;0CACC,cAAA,8OAAC;oCAAG,SAAS,QAAQ,MAAM;oCAAE,WAAU;8CAAyD;;;;;;;;;;uCAKlG,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,sBACnB,8OAAC;oCAAe,WAAU;8CACvB,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;4CAA4B,WAAU;sDACpC,OAAO,MAAM,GACV,OAAO,MAAM,CAAC,IAAI,CAAC,OAAO,GAAG,CAAY,EAAE,QAC3C,OAAO,IAAI,CAAC,OAAO,GAAG,CAAY,IAAI;2CAHnC,OAAO,OAAO,GAAG;;;;;mCAFrB;;;;;;;;;;;;;;;;;;;;;YAiBlB,MAAM,sBACL,8OAAC,0IAAA,CAAA,UAAU;gBACT,MAAM,KAAK,IAAI;gBACf,cAAc;gBACd,kBAAkB;gBAClB,eAAe;gBACf,sBAAsB;gBACtB,UAAU;gBACV,iBAAiB;gBACjB,iBAAiB;oBAAC;oBAAI;oBAAI;oBAAI;iBAAI;;;;;;;;;;;;AAK5C", "debugId": null}}, {"offset": {"line": 1751, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/common/Select.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { forwardRef } from 'react';\r\n\r\ninterface SelectOption {\r\n  value: string;\r\n  label: string;\r\n  disabled?: boolean;\r\n}\r\n\r\ninterface SelectProps extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'onChange'> {\r\n  label?: string;\r\n  error?: string;\r\n  helperText?: string;\r\n  required?: boolean;\r\n  options: SelectOption[];\r\n  placeholder?: string;\r\n  className?: string;\r\n  containerClassName?: string;\r\n  onChange?: (value: string) => void;\r\n}\r\n\r\nconst Select = forwardRef<HTMLSelectElement, SelectProps>(({\r\n  label,\r\n  error,\r\n  helperText,\r\n  required = false,\r\n  options = [],\r\n  placeholder = 'Select an option...',\r\n  className = '',\r\n  containerClassName = '',\r\n  onChange,\r\n  id,\r\n  value,\r\n  ...props\r\n}, ref) => {\r\n  const selectId = id || `select-${Math.random().toString(36).substr(2, 9)}`;\r\n  \r\n  const baseSelectClasses = `\r\n    w-full px-3 py-2 border rounded-md shadow-sm \r\n    focus:outline-none focus:ring-2 focus:ring-offset-2 \r\n    disabled:opacity-50 disabled:cursor-not-allowed\r\n    dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600\r\n    transition-colors duration-200\r\n    appearance-none bg-white\r\n    bg-no-repeat bg-right bg-[length:16px_16px]\r\n    pr-10\r\n  `;\r\n  \r\n  const selectClasses = error\r\n    ? `${baseSelectClasses} border-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-600`\r\n    : `${baseSelectClasses} border-gray-300 focus:border-primary focus:ring-primary dark:focus:border-primary dark:focus:ring-primary`;\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\r\n    if (onChange) {\r\n      onChange(e.target.value);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={`space-y-1 ${containerClassName}`}>\r\n      {label && (\r\n        <label \r\n          htmlFor={selectId}\r\n          className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\"\r\n        >\r\n          {label}\r\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\r\n        </label>\r\n      )}\r\n      \r\n      <div className=\"relative\">\r\n        <select\r\n          ref={ref}\r\n          id={selectId}\r\n          value={value || ''}\r\n          onChange={handleChange}\r\n          className={`${selectClasses} ${className}`}\r\n          {...props}\r\n        >\r\n          {placeholder && (\r\n            <option value=\"\" disabled>\r\n              {placeholder}\r\n            </option>\r\n          )}\r\n          \r\n          {options.map((option) => (\r\n            <option \r\n              key={option.value} \r\n              value={option.value}\r\n              disabled={option.disabled}\r\n            >\r\n              {option.label}\r\n            </option>\r\n          ))}\r\n        </select>\r\n        \r\n        {/* Custom dropdown arrow */}\r\n        <div className=\"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none\">\r\n          <i className=\"ri-arrow-down-s-line text-gray-400 dark:text-gray-500\"></i>\r\n        </div>\r\n      </div>\r\n      \r\n      {error && (\r\n        <p className=\"text-sm text-red-600 dark:text-red-400 flex items-center\">\r\n          <i className=\"ri-error-warning-line mr-1\"></i>\r\n          {error}\r\n        </p>\r\n      )}\r\n      \r\n      {helperText && !error && (\r\n        <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n          {helperText}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n});\r\n\r\nSelect.displayName = 'Select';\r\n\r\nexport default Select;\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAsBA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAkC,CAAC,EACzD,KAAK,EACL,KAAK,EACL,UAAU,EACV,WAAW,KAAK,EAChB,UAAU,EAAE,EACZ,cAAc,qBAAqB,EACnC,YAAY,EAAE,EACd,qBAAqB,EAAE,EACvB,QAAQ,EACR,EAAE,EACF,KAAK,EACL,GAAG,OACJ,EAAE;IACD,MAAM,WAAW,MAAM,CAAC,OAAO,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAE1E,MAAM,oBAAoB,CAAC;;;;;;;;;EAS3B,CAAC;IAED,MAAM,gBAAgB,QAClB,GAAG,kBAAkB,2EAA2E,CAAC,GACjG,GAAG,kBAAkB,0GAA0G,CAAC;IAEpI,MAAM,eAAe,CAAC;QACpB,IAAI,UAAU;YACZ,SAAS,EAAE,MAAM,CAAC,KAAK;QACzB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,oBAAoB;;YAC9C,uBACC,8OAAC;gBACC,SAAS;gBACT,WAAU;;oBAET;oBACA,0BAAY,8OAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,KAAK;wBACL,IAAI;wBACJ,OAAO,SAAS;wBAChB,UAAU;wBACV,WAAW,GAAG,cAAc,CAAC,EAAE,WAAW;wBACzC,GAAG,KAAK;;4BAER,6BACC,8OAAC;gCAAO,OAAM;gCAAG,QAAQ;0CACtB;;;;;;4BAIJ,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;oCAEC,OAAO,OAAO,KAAK;oCACnB,UAAU,OAAO,QAAQ;8CAExB,OAAO,KAAK;mCAJR,OAAO,KAAK;;;;;;;;;;;kCAUvB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;;;;;;;YAIhB,uBACC,8OAAC;gBAAE,WAAU;;kCACX,8OAAC;wBAAE,WAAU;;;;;;oBACZ;;;;;;;YAIJ,cAAc,CAAC,uBACd,8OAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;AAEA,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 1894, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/license/LicenseManagementTable.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { useAuth } from '../../contexts/AuthContext';\r\nimport { Application, ApplicationStatus } from '../../types/license';\r\nimport { applicationService } from '../../services/applicationService';\r\nimport { licenseCategoryService, LicenseCategory } from '../../services/licenseCategoryService';\r\nimport { licenseTypeService, LicenseType } from '../../services/licenseTypeService';\r\nimport AssignButton from '../common/AssignButton';\r\nimport ApplicationViewModal from './ApplicationViewModal';\r\nimport { PaginateQuery } from '../../services/userService';\r\nimport DataTable from '../common/DataTable';\r\nimport Select from '../common/Select';\r\n\r\ninterface LicenseManagementTableProps {\r\n  licenseTypeId?: string;\r\n  licenseTypeCode?: string; // Primary filter by license type code\r\n  licenseTypeFilter?: string; // Filter by license type name (fallback)\r\n  title: string;\r\n  description: string;\r\n  searchPlaceholder: string;\r\n  emptyStateIcon: string;\r\n  emptyStateMessage: string;\r\n  departmentType?: string; // Department type for navigation\r\n}\r\n\r\nexport default function LicenseManagementTable({\r\n  licenseTypeId,\r\n  licenseTypeCode,\r\n  licenseTypeFilter,\r\n  title,\r\n  description,\r\n  searchPlaceholder,\r\n  emptyStateIcon,\r\n  emptyStateMessage,\r\n  departmentType,\r\n}: LicenseManagementTableProps) {\r\n  const { user } = useAuth();\r\n  const router = useRouter();\r\n  const [applicationsData, setApplicationsData] = useState<any>(null);\r\n  const [licenseCategories, setLicenseCategories] = useState<LicenseCategory[]>([]);\r\n  const [licenseTypes, setLicenseTypes] = useState<LicenseType[]>([]);\r\n  const [resolvedLicenseTypeId, setResolvedLicenseTypeId] = useState<string | undefined>(undefined);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  const [selectedLicenseCategory, setSelectedLicenseCategory] = useState('');\r\n  const [statusFilter, setStatusFilter] = useState<ApplicationStatus | ''>('');\r\n  const [dateRangeFilter, setDateRangeFilter] = useState('');\r\n\r\n  // View modal state\r\n  const [showViewModal, setShowViewModal] = useState(false);\r\n  const [selectedApplicationId, setSelectedApplicationId] = useState<string | null>(null);\r\n\r\n  const isAdmin = user?.isAdmin;\r\n\r\n  // Function for viewing applications - opens modal\r\n  const handleViewApplication = (applicationId: string) => {\r\n    setSelectedApplicationId(applicationId);\r\n    setShowViewModal(true);\r\n  };\r\n\r\n  const handleCloseViewModal = () => {\r\n    setShowViewModal(false);\r\n    setSelectedApplicationId(null);\r\n  };\r\n\r\n  const handleViewModalUpdate = () => {\r\n    // Refresh the applications list when modal updates data\r\n    loadApplications({ page: 1, limit: 10 });\r\n  };\r\n\r\n  // Load applications function for DataTable\r\n  const loadApplications = useCallback(async (query: PaginateQuery) => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n\r\n      const effectiveLicenseTypeId = licenseTypeId || resolvedLicenseTypeId;\r\n\r\n      // Build filters object\r\n      const filters: any = {};\r\n\r\n      // Add license category filter if selected\r\n      if (selectedLicenseCategory) {\r\n        filters.licenseCategoryId = selectedLicenseCategory;\r\n      }\r\n\r\n      // Add license type filter - prioritize licenseTypeCode search\r\n      if (effectiveLicenseTypeId) {\r\n        filters.licenseTypeId = effectiveLicenseTypeId;\r\n      } else if (licenseTypeCode) {\r\n        // If we have a license type code but no resolved ID yet, add it as a filter\r\n        filters.licenseTypeCode = licenseTypeCode;\r\n      }\r\n\r\n      // Add status filter if selected\r\n      if (statusFilter) {\r\n        filters.status = statusFilter;\r\n      }\r\n\r\n      const params = {\r\n        page: query.page,\r\n        limit: query.limit,\r\n        search: query.search || undefined,\r\n        filters: Object.keys(filters).length > 0 ? filters : undefined,\r\n      };\r\n\r\n      console.log('Loading applications with params:', params);\r\n      const response = await applicationService.getApplications(params);\r\n      setApplicationsData(response);\r\n    } catch (err: any) {\r\n      console.error('Error loading applications:', err);\r\n      setError('Failed to load applications');\r\n      // Set empty data structure to prevent undefined errors\r\n      setApplicationsData({\r\n        data: [],\r\n        meta: {\r\n          itemsPerPage: query.limit || 10,\r\n          totalItems: 0,\r\n          currentPage: query.page || 1,\r\n          totalPages: 0,\r\n          sortBy: [],\r\n          searchBy: [],\r\n          search: '',\r\n          select: [],\r\n        },\r\n        links: {\r\n          current: '',\r\n        },\r\n      });\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [licenseTypeId, resolvedLicenseTypeId, selectedLicenseCategory, statusFilter, licenseTypeCode]);\r\n\r\n\r\n\r\n  // Load license types and resolve license type ID from code or filter name\r\n  useEffect(() => {\r\n    const fetchLicenseTypes = async () => {\r\n      try {\r\n        const response = await licenseTypeService.getAllLicenseTypes();\r\n        const types = Array.isArray(response) ? response : (response?.data || []);\r\n\r\n        if (!Array.isArray(types)) {\r\n          console.warn('License types response is not an array:', types);\r\n          setLicenseTypes([]);\r\n          return;\r\n        }\r\n\r\n        setLicenseTypes(types);\r\n\r\n        // Priority 1: If we have a licenseTypeCode, find the matching license type ID\r\n        if (licenseTypeCode && types.length > 0) {\r\n          const matchingType = types.find(type =>\r\n            type.code === licenseTypeCode\r\n          );\r\n          if (matchingType) {\r\n            setResolvedLicenseTypeId(matchingType.license_type_id);\r\n          }\r\n        }\r\n        // Priority 2: If we have a licenseTypeFilter (fallback), find the matching license type ID\r\n        else if (licenseTypeFilter && types.length > 0) {\r\n          const matchingType = types.find(type =>\r\n            type.name.toLowerCase().includes(licenseTypeFilter.toLowerCase())\r\n          );\r\n          if (matchingType) {\r\n            setResolvedLicenseTypeId(matchingType.license_type_id);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Error fetching license types:', error);\r\n        setLicenseTypes([]);\r\n      }\r\n    };\r\n\r\n    fetchLicenseTypes();\r\n  }, [licenseTypeCode, licenseTypeFilter]);\r\n\r\n  // Fetch license categories for the dropdown\r\n  useEffect(() => {\r\n    const fetchLicenseCategories = async () => {\r\n      try {\r\n        let categories: LicenseCategory[] = [];\r\n        const effectiveLicenseTypeId = licenseTypeId || resolvedLicenseTypeId;\r\n\r\n        if (effectiveLicenseTypeId) {\r\n          // Fetch categories for specific license type\r\n          const response = await licenseCategoryService.getLicenseCategoriesByType(effectiveLicenseTypeId);\r\n          categories = Array.isArray(response) ? response : (response?.data || []);\r\n        } else {\r\n          // Fetch all categories\r\n          const response = await licenseCategoryService.getAllLicenseCategories();\r\n          categories = Array.isArray(response) ? response : (response?.data || []);\r\n        }\r\n\r\n        if (!Array.isArray(categories)) {\r\n          console.warn('License categories response is not an array:', categories);\r\n          categories = [];\r\n        }\r\n\r\n        setLicenseCategories(categories);\r\n      } catch (error) {\r\n        console.error('Error fetching license categories:', error);\r\n        setLicenseCategories([]);\r\n      }\r\n    };\r\n\r\n    if (resolvedLicenseTypeId || licenseTypeId) {\r\n      fetchLicenseCategories();\r\n    }\r\n  }, [licenseTypeId, resolvedLicenseTypeId]);\r\n\r\n  // Load applications on component mount and when filters change\r\n  useEffect(() => {\r\n    if (resolvedLicenseTypeId || licenseTypeId) {\r\n      loadApplications({ page: 1, limit: 10 });\r\n    }\r\n  }, [loadApplications, resolvedLicenseTypeId, licenseTypeId]);\r\n\r\n  const getStatusBadge = (status: ApplicationStatus) => {\r\n    const statusClasses: Record<ApplicationStatus, string> = {\r\n      [ApplicationStatus.DRAFT]: 'bg-gray-100 text-gray-800',\r\n      [ApplicationStatus.SUBMITTED]: 'bg-blue-100 text-blue-800',\r\n      [ApplicationStatus.UNDER_REVIEW]: 'bg-yellow-100 text-yellow-800',\r\n      [ApplicationStatus.EVALUATION]: 'bg-purple-100 text-purple-800',\r\n      [ApplicationStatus.APPROVED]: 'bg-green-100 text-green-800',\r\n      [ApplicationStatus.REJECTED]: 'bg-red-100 text-red-800',\r\n      [ApplicationStatus.WITHDRAWN]: 'bg-gray-100 text-gray-800',\r\n    };\r\n\r\n    const statusLabels: Record<ApplicationStatus, string> = {\r\n      [ApplicationStatus.DRAFT]: 'Draft',\r\n      [ApplicationStatus.SUBMITTED]: 'Submitted',\r\n      [ApplicationStatus.UNDER_REVIEW]: 'Under Review',\r\n      [ApplicationStatus.EVALUATION]: 'Evaluation',\r\n      [ApplicationStatus.APPROVED]: 'Approved',\r\n      [ApplicationStatus.REJECTED]: 'Rejected',\r\n      [ApplicationStatus.WITHDRAWN]: 'Withdrawn',\r\n    };\r\n\r\n    return (\r\n      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClasses[status]}`}>\r\n        {statusLabels[status]}\r\n      </span>\r\n    );\r\n  };\r\n\r\n  // Define columns for applications table\r\n  const applicationColumns = [\r\n    {\r\n      key: 'application_number',\r\n      label: 'Application Number',\r\n      sortable: true,\r\n      render: (value: string) => (\r\n        <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n          {value}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: 'applicant',\r\n      label: 'Applicant',\r\n      render: (value: any, application: Application) => (\r\n        <div className=\"flex items-center\">\r\n          <div className=\"flex-shrink-0 h-10 w-10\">\r\n            <div className=\"h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center\">\r\n              <i className=\"ri-building-line text-blue-600 dark:text-blue-400\"></i>\r\n            </div>\r\n          </div>\r\n          <div className=\"ml-4\">\r\n            <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n              {application.applicant?.name || 'N/A'}\r\n            </div>\r\n            <div className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n              BRN: {application.applicant?.business_registration_number || 'N/A'} |\r\n              TPIN: {application.applicant?.tpin || 'N/A'}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: 'license_category',\r\n      label: 'License Category',\r\n      render: (value: any, application: Application) => (\r\n        <div>\r\n          <div className=\"text-sm text-gray-900 dark:text-gray-100\">\r\n            {application.license_category?.name || 'N/A'}\r\n          </div>\r\n          <div className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n            {application.license_category?.license_type?.name || 'N/A'}\r\n          </div>\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: 'status',\r\n      label: 'Status',\r\n      render: (value: ApplicationStatus) => getStatusBadge(value),\r\n    },\r\n    {\r\n      key: 'progress_percentage',\r\n      label: 'Progress',\r\n      render: (value: number) => getProgressBar(value),\r\n    },\r\n    {\r\n      key: 'submitted_at',\r\n      label: 'Submitted Date',\r\n      sortable: true,\r\n      render: (value: string, application: Application) => (\r\n        <span className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n          {application.submitted_at\r\n            ? new Date(application.submitted_at).toLocaleDateString()\r\n            : new Date(application.created_at).toLocaleDateString()\r\n          }\r\n        </span>\r\n      ),\r\n    },\r\n    {\r\n      key: 'actions',\r\n      label: 'Actions',\r\n      render: (value: any, application: Application) => (\r\n        <div className=\"flex items-center justify-end space-x-2\">\r\n          <button\r\n            type=\"button\"\r\n            onClick={() => handleViewApplication(application.application_id)}\r\n            className=\"inline-flex items-center px-3 py-1 text-xs font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/40 transition-colors\"\r\n          >\r\n            <i className=\"ri-eye-line mr-1\"></i>\r\n            View\r\n          </button>\r\n          <AssignButton\r\n            itemId={application.application_id}\r\n            itemType=\"application\"\r\n            itemTitle={application.application_number}\r\n            onAssignSuccess={handleAssignSuccess}\r\n            className=\"text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 hover:bg-green-100 dark:hover:bg-green-900/40\"\r\n          />\r\n        </div>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  const getProgressBar = (percentage: number) => {\r\n    const getProgressColor = (percent: number) => {\r\n      if (percent >= 100) return 'bg-green-600';\r\n      if (percent >= 75) return 'bg-blue-600';\r\n      if (percent >= 50) return 'bg-yellow-600';\r\n      if (percent >= 25) return 'bg-orange-600';\r\n      return 'bg-red-600';\r\n    };\r\n\r\n    return (\r\n      <div className=\"flex items-center\">\r\n        <div className=\"w-16 bg-gray-200 rounded-full h-2 mr-2\">\r\n          <div \r\n            className={`h-2 rounded-full ${getProgressColor(percentage)}`} \r\n            style={{ width: `${percentage}%` }}\r\n          ></div>\r\n        </div>\r\n        <span className=\"text-sm text-gray-500\">{percentage}%</span>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  const handleAssignSuccess = () => {\r\n    // Refresh the applications list when assignment is successful\r\n    loadApplications({ page: 1, limit: 10 });\r\n  };\r\n\r\n  const handleFilterChange = (filterType: string, value: string) => {\r\n    switch (filterType) {\r\n      case 'licenseCategory':\r\n        setSelectedLicenseCategory(value);\r\n        break;\r\n      case 'status':\r\n        setStatusFilter(value as ApplicationStatus | '');\r\n        break;\r\n      case 'dateRange':\r\n        setDateRangeFilter(value);\r\n        break;\r\n    }\r\n    // Reload applications with new filters\r\n    loadApplications({ page: 1, limit: 10 });\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-6\">\r\n      <div className=\"max-w-7xl mx-auto\">\r\n        {/* Page header */}\r\n        <div className=\"mb-8\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <h1 className=\"text-3xl font-bold text-gray-900 dark:text-gray-100\">{title}</h1>\r\n              <p className=\"mt-2 text-sm text-gray-600 dark:text-gray-400\">\r\n                {description}\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Filters Section */}\r\n        <div className=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6\">\r\n          <h2 className=\"text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4\">Filters</h2>\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n            {/* License Category Filter */}\r\n            <Select\r\n              label=\"License Category\"\r\n              value={selectedLicenseCategory}\r\n              onChange={(value) => handleFilterChange('licenseCategory', value)}\r\n              options={[\r\n                { value: '', label: 'All Categories' },\r\n                ...licenseCategories.map(category => ({\r\n                  value: category.license_category_id,\r\n                  label: category.name\r\n                }))\r\n              ]}\r\n            />\r\n\r\n            {/* Status Filter */}\r\n            <Select\r\n              label=\"Application Status\"\r\n              value={statusFilter}\r\n              onChange={(value) => handleFilterChange('status', value)}\r\n              options={[\r\n                { value: '', label: 'All Statuses' },\r\n                { value: ApplicationStatus.DRAFT, label: 'Draft' },\r\n                { value: ApplicationStatus.SUBMITTED, label: 'Submitted' },\r\n                { value: ApplicationStatus.UNDER_REVIEW, label: 'Under Review' },\r\n                { value: ApplicationStatus.EVALUATION, label: 'Evaluation' },\r\n                { value: ApplicationStatus.APPROVED, label: 'Approved' },\r\n                { value: ApplicationStatus.REJECTED, label: 'Rejected' },\r\n                { value: ApplicationStatus.WITHDRAWN, label: 'Withdrawn' }\r\n              ]}\r\n            />\r\n\r\n            {/* Date Range Filter */}\r\n            <Select\r\n              label=\"Date Range\"\r\n              value={dateRangeFilter}\r\n              onChange={(value) => handleFilterChange('dateRange', value)}\r\n              options={[\r\n                { value: '', label: 'All Time' },\r\n                { value: 'last-30', label: 'Last 30 Days' },\r\n                { value: 'last-90', label: 'Last 90 Days' },\r\n                { value: 'last-year', label: 'Last Year' }\r\n              ]}\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n\r\n\r\n        {/* Applications Table */}\r\n        <DataTable\r\n          columns={applicationColumns}\r\n          data={applicationsData}\r\n          loading={loading}\r\n          onQueryChange={loadApplications}\r\n          searchPlaceholder={searchPlaceholder}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AAbA;;;;;;;;;;;;AA2Be,SAAS,uBAAuB,EAC7C,aAAa,EACb,eAAe,EACf,iBAAiB,EACjB,KAAK,EACL,WAAW,EACX,iBAAiB,EACjB,cAAc,EACd,iBAAiB,EACjB,cAAc,EACc;IAC5B,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC9D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IAChF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IACvF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IACzE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,mBAAmB;IACnB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElF,MAAM,UAAU,MAAM;IAEtB,kDAAkD;IAClD,MAAM,wBAAwB,CAAC;QAC7B,yBAAyB;QACzB,iBAAiB;IACnB;IAEA,MAAM,uBAAuB;QAC3B,iBAAiB;QACjB,yBAAyB;IAC3B;IAEA,MAAM,wBAAwB;QAC5B,wDAAwD;QACxD,iBAAiB;YAAE,MAAM;YAAG,OAAO;QAAG;IACxC;IAEA,2CAA2C;IAC3C,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC1C,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,yBAAyB,iBAAiB;YAEhD,uBAAuB;YACvB,MAAM,UAAe,CAAC;YAEtB,0CAA0C;YAC1C,IAAI,yBAAyB;gBAC3B,QAAQ,iBAAiB,GAAG;YAC9B;YAEA,8DAA8D;YAC9D,IAAI,wBAAwB;gBAC1B,QAAQ,aAAa,GAAG;YAC1B,OAAO,IAAI,iBAAiB;gBAC1B,4EAA4E;gBAC5E,QAAQ,eAAe,GAAG;YAC5B;YAEA,gCAAgC;YAChC,IAAI,cAAc;gBAChB,QAAQ,MAAM,GAAG;YACnB;YAEA,MAAM,SAAS;gBACb,MAAM,MAAM,IAAI;gBAChB,OAAO,MAAM,KAAK;gBAClB,QAAQ,MAAM,MAAM,IAAI;gBACxB,SAAS,OAAO,IAAI,CAAC,SAAS,MAAM,GAAG,IAAI,UAAU;YACvD;YAEA,QAAQ,GAAG,CAAC,qCAAqC;YACjD,MAAM,WAAW,MAAM,qIAAA,CAAA,qBAAkB,CAAC,eAAe,CAAC;YAC1D,oBAAoB;QACtB,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,SAAS;YACT,uDAAuD;YACvD,oBAAoB;gBAClB,MAAM,EAAE;gBACR,MAAM;oBACJ,cAAc,MAAM,KAAK,IAAI;oBAC7B,YAAY;oBACZ,aAAa,MAAM,IAAI,IAAI;oBAC3B,YAAY;oBACZ,QAAQ,EAAE;oBACV,UAAU,EAAE;oBACZ,QAAQ;oBACR,QAAQ,EAAE;gBACZ;gBACA,OAAO;oBACL,SAAS;gBACX;YACF;QACF,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;QAAe;QAAuB;QAAyB;QAAc;KAAgB;IAIjG,0EAA0E;IAC1E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,oBAAoB;YACxB,IAAI;gBACF,MAAM,WAAW,MAAM,qIAAA,CAAA,qBAAkB,CAAC,kBAAkB;gBAC5D,MAAM,QAAQ,MAAM,OAAO,CAAC,YAAY,WAAY,UAAU,QAAQ,EAAE;gBAExE,IAAI,CAAC,MAAM,OAAO,CAAC,QAAQ;oBACzB,QAAQ,IAAI,CAAC,2CAA2C;oBACxD,gBAAgB,EAAE;oBAClB;gBACF;gBAEA,gBAAgB;gBAEhB,8EAA8E;gBAC9E,IAAI,mBAAmB,MAAM,MAAM,GAAG,GAAG;oBACvC,MAAM,eAAe,MAAM,IAAI,CAAC,CAAA,OAC9B,KAAK,IAAI,KAAK;oBAEhB,IAAI,cAAc;wBAChB,yBAAyB,aAAa,eAAe;oBACvD;gBACF,OAEK,IAAI,qBAAqB,MAAM,MAAM,GAAG,GAAG;oBAC9C,MAAM,eAAe,MAAM,IAAI,CAAC,CAAA,OAC9B,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,kBAAkB,WAAW;oBAEhE,IAAI,cAAc;wBAChB,yBAAyB,aAAa,eAAe;oBACvD;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,gBAAgB,EAAE;YACpB;QACF;QAEA;IACF,GAAG;QAAC;QAAiB;KAAkB;IAEvC,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,yBAAyB;YAC7B,IAAI;gBACF,IAAI,aAAgC,EAAE;gBACtC,MAAM,yBAAyB,iBAAiB;gBAEhD,IAAI,wBAAwB;oBAC1B,6CAA6C;oBAC7C,MAAM,WAAW,MAAM,yIAAA,CAAA,yBAAsB,CAAC,0BAA0B,CAAC;oBACzE,aAAa,MAAM,OAAO,CAAC,YAAY,WAAY,UAAU,QAAQ,EAAE;gBACzE,OAAO;oBACL,uBAAuB;oBACvB,MAAM,WAAW,MAAM,yIAAA,CAAA,yBAAsB,CAAC,uBAAuB;oBACrE,aAAa,MAAM,OAAO,CAAC,YAAY,WAAY,UAAU,QAAQ,EAAE;gBACzE;gBAEA,IAAI,CAAC,MAAM,OAAO,CAAC,aAAa;oBAC9B,QAAQ,IAAI,CAAC,gDAAgD;oBAC7D,aAAa,EAAE;gBACjB;gBAEA,qBAAqB;YACvB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,sCAAsC;gBACpD,qBAAqB,EAAE;YACzB;QACF;QAEA,IAAI,yBAAyB,eAAe;YAC1C;QACF;IACF,GAAG;QAAC;QAAe;KAAsB;IAEzC,+DAA+D;IAC/D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,yBAAyB,eAAe;YAC1C,iBAAiB;gBAAE,MAAM;gBAAG,OAAO;YAAG;QACxC;IACF,GAAG;QAAC;QAAkB;QAAuB;KAAc;IAE3D,MAAM,iBAAiB,CAAC;QACtB,MAAM,gBAAmD;YACvD,CAAC,uHAAA,CAAA,oBAAiB,CAAC,KAAK,CAAC,EAAE;YAC3B,CAAC,uHAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC,EAAE;YAC/B,CAAC,uHAAA,CAAA,oBAAiB,CAAC,YAAY,CAAC,EAAE;YAClC,CAAC,uHAAA,CAAA,oBAAiB,CAAC,UAAU,CAAC,EAAE;YAChC,CAAC,uHAAA,CAAA,oBAAiB,CAAC,QAAQ,CAAC,EAAE;YAC9B,CAAC,uHAAA,CAAA,oBAAiB,CAAC,QAAQ,CAAC,EAAE;YAC9B,CAAC,uHAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC,EAAE;QACjC;QAEA,MAAM,eAAkD;YACtD,CAAC,uHAAA,CAAA,oBAAiB,CAAC,KAAK,CAAC,EAAE;YAC3B,CAAC,uHAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC,EAAE;YAC/B,CAAC,uHAAA,CAAA,oBAAiB,CAAC,YAAY,CAAC,EAAE;YAClC,CAAC,uHAAA,CAAA,oBAAiB,CAAC,UAAU,CAAC,EAAE;YAChC,CAAC,uHAAA,CAAA,oBAAiB,CAAC,QAAQ,CAAC,EAAE;YAC9B,CAAC,uHAAA,CAAA,oBAAiB,CAAC,QAAQ,CAAC,EAAE;YAC9B,CAAC,uHAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC,EAAE;QACjC;QAEA,qBACE,8OAAC;YAAK,WAAW,CAAC,8DAA8D,EAAE,aAAa,CAAC,OAAO,EAAE;sBACtG,YAAY,CAAC,OAAO;;;;;;IAG3B;IAEA,wCAAwC;IACxC,MAAM,qBAAqB;QACzB;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,sBACP,8OAAC;oBAAI,WAAU;8BACZ;;;;;;QAGP;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,OAAY,4BACnB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;;;;;;;;;;;;;;;sCAGjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACZ,YAAY,SAAS,EAAE,QAAQ;;;;;;8CAElC,8OAAC;oCAAI,WAAU;;wCAA2C;wCAClD,YAAY,SAAS,EAAE,gCAAgC;wCAAM;wCAC5D,YAAY,SAAS,EAAE,QAAQ;;;;;;;;;;;;;;;;;;;QAKhD;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,OAAY,4BACnB,8OAAC;;sCACC,8OAAC;4BAAI,WAAU;sCACZ,YAAY,gBAAgB,EAAE,QAAQ;;;;;;sCAEzC,8OAAC;4BAAI,WAAU;sCACZ,YAAY,gBAAgB,EAAE,cAAc,QAAQ;;;;;;;;;;;;QAI7D;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,QAA6B,eAAe;QACvD;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,QAAkB,eAAe;QAC5C;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,OAAe,4BACtB,8OAAC;oBAAK,WAAU;8BACb,YAAY,YAAY,GACrB,IAAI,KAAK,YAAY,YAAY,EAAE,kBAAkB,KACrD,IAAI,KAAK,YAAY,UAAU,EAAE,kBAAkB;;;;;;QAI7D;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,OAAY,4BACnB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,MAAK;4BACL,SAAS,IAAM,sBAAsB,YAAY,cAAc;4BAC/D,WAAU;;8CAEV,8OAAC;oCAAE,WAAU;;;;;;gCAAuB;;;;;;;sCAGtC,8OAAC,4IAAA,CAAA,UAAY;4BACX,QAAQ,YAAY,cAAc;4BAClC,UAAS;4BACT,WAAW,YAAY,kBAAkB;4BACzC,iBAAiB;4BACjB,WAAU;;;;;;;;;;;;QAIlB;KACD;IAED,MAAM,iBAAiB,CAAC;QACtB,MAAM,mBAAmB,CAAC;YACxB,IAAI,WAAW,KAAK,OAAO;YAC3B,IAAI,WAAW,IAAI,OAAO;YAC1B,IAAI,WAAW,IAAI,OAAO;YAC1B,IAAI,WAAW,IAAI,OAAO;YAC1B,OAAO;QACT;QAEA,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,WAAW,CAAC,iBAAiB,EAAE,iBAAiB,aAAa;wBAC7D,OAAO;4BAAE,OAAO,GAAG,WAAW,CAAC,CAAC;wBAAC;;;;;;;;;;;8BAGrC,8OAAC;oBAAK,WAAU;;wBAAyB;wBAAW;;;;;;;;;;;;;IAG1D;IAEA,MAAM,sBAAsB;QAC1B,8DAA8D;QAC9D,iBAAiB;YAAE,MAAM;YAAG,OAAO;QAAG;IACxC;IAEA,MAAM,qBAAqB,CAAC,YAAoB;QAC9C,OAAQ;YACN,KAAK;gBACH,2BAA2B;gBAC3B;YACF,KAAK;gBACH,gBAAgB;gBAChB;YACF,KAAK;gBACH,mBAAmB;gBACnB;QACJ;QACA,uCAAuC;QACvC,iBAAiB;YAAE,MAAM;YAAG,OAAO;QAAG;IACxC;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAuD;;;;;;8CACrE,8OAAC;oCAAE,WAAU;8CACV;;;;;;;;;;;;;;;;;;;;;;8BAOT,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA8D;;;;;;sCAC5E,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,sIAAA,CAAA,UAAM;oCACL,OAAM;oCACN,OAAO;oCACP,UAAU,CAAC,QAAU,mBAAmB,mBAAmB;oCAC3D,SAAS;wCACP;4CAAE,OAAO;4CAAI,OAAO;wCAAiB;2CAClC,kBAAkB,GAAG,CAAC,CAAA,WAAY,CAAC;gDACpC,OAAO,SAAS,mBAAmB;gDACnC,OAAO,SAAS,IAAI;4CACtB,CAAC;qCACF;;;;;;8CAIH,8OAAC,sIAAA,CAAA,UAAM;oCACL,OAAM;oCACN,OAAO;oCACP,UAAU,CAAC,QAAU,mBAAmB,UAAU;oCAClD,SAAS;wCACP;4CAAE,OAAO;4CAAI,OAAO;wCAAe;wCACnC;4CAAE,OAAO,uHAAA,CAAA,oBAAiB,CAAC,KAAK;4CAAE,OAAO;wCAAQ;wCACjD;4CAAE,OAAO,uHAAA,CAAA,oBAAiB,CAAC,SAAS;4CAAE,OAAO;wCAAY;wCACzD;4CAAE,OAAO,uHAAA,CAAA,oBAAiB,CAAC,YAAY;4CAAE,OAAO;wCAAe;wCAC/D;4CAAE,OAAO,uHAAA,CAAA,oBAAiB,CAAC,UAAU;4CAAE,OAAO;wCAAa;wCAC3D;4CAAE,OAAO,uHAAA,CAAA,oBAAiB,CAAC,QAAQ;4CAAE,OAAO;wCAAW;wCACvD;4CAAE,OAAO,uHAAA,CAAA,oBAAiB,CAAC,QAAQ;4CAAE,OAAO;wCAAW;wCACvD;4CAAE,OAAO,uHAAA,CAAA,oBAAiB,CAAC,SAAS;4CAAE,OAAO;wCAAY;qCAC1D;;;;;;8CAIH,8OAAC,sIAAA,CAAA,UAAM;oCACL,OAAM;oCACN,OAAO;oCACP,UAAU,CAAC,QAAU,mBAAmB,aAAa;oCACrD,SAAS;wCACP;4CAAE,OAAO;4CAAI,OAAO;wCAAW;wCAC/B;4CAAE,OAAO;4CAAW,OAAO;wCAAe;wCAC1C;4CAAE,OAAO;4CAAW,OAAO;wCAAe;wCAC1C;4CAAE,OAAO;4CAAa,OAAO;wCAAY;qCAC1C;;;;;;;;;;;;;;;;;;8BAQP,8OAAC,yIAAA,CAAA,UAAS;oBACR,SAAS;oBACT,MAAM;oBACN,SAAS;oBACT,eAAe;oBACf,mBAAmB;;;;;;;;;;;;;;;;;AAK7B", "debugId": null}}, {"offset": {"line": 2553, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/app/applications/%5Blicense-type%5D/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useState } from 'react';\r\nimport { useParams } from 'next/navigation';\r\nimport LicenseManagementTable from '../../../components/license/LicenseManagementTable';\r\nimport { LicenseType } from '@/types/license';\r\nimport { licenseTypeService } from '@/services/licenseTypeService';\r\n\r\nexport default function LicenseTypeApplicationsPage() {\r\n  const params = useParams();\r\n  const licenseTypeCode = params['license-type'] as string;\r\n  const [licenseType, setLicenseType] = useState<LicenseType | null>(null);\r\n\r\n  // Load required documents and existing uploads\r\n  useEffect(() => {\r\n    const fetchLicenseType = async () => {\r\n      try {\r\n        const licenseTypeResponse = await licenseTypeService.getLicenseTypeByCode(licenseTypeCode);\r\n        setLicenseType(licenseTypeResponse);\r\n      } catch (error) {\r\n        console.error('Error fetching license type:', error);\r\n      }\r\n    };\r\n\r\n    if (licenseTypeCode) {\r\n      fetchLicenseType();\r\n    }\r\n  }, [licenseTypeCode]);\r\n\r\n  return (\r\n    <LicenseManagementTable\r\n      licenseTypeCode={licenseTypeCode}\r\n      title={`${licenseType?.name || 'License'} Management`}\r\n      description={licenseType?.description || licenseType?.name || 'License management'}\r\n      searchPlaceholder={`Search ${licenseType?.name || 'license'} applications...`}\r\n      emptyStateIcon={'ri-mail-line'}\r\n      emptyStateMessage={'No applications are available.'}\r\n      departmentType={licenseType?.name}\r\n    />\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,kBAAkB,MAAM,CAAC,eAAe;IAC9C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAEnE,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI;gBACF,MAAM,sBAAsB,MAAM,qIAAA,CAAA,qBAAkB,CAAC,oBAAoB,CAAC;gBAC1E,eAAe;YACjB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gCAAgC;YAChD;QACF;QAEA,IAAI,iBAAiB;YACnB;QACF;IACF,GAAG;QAAC;KAAgB;IAEpB,qBACE,8OAAC,uJAAA,CAAA,UAAsB;QACrB,iBAAiB;QACjB,OAAO,GAAG,aAAa,QAAQ,UAAU,WAAW,CAAC;QACrD,aAAa,aAAa,eAAe,aAAa,QAAQ;QAC9D,mBAAmB,CAAC,OAAO,EAAE,aAAa,QAAQ,UAAU,gBAAgB,CAAC;QAC7E,gBAAgB;QAChB,mBAAmB;QACnB,gBAAgB,aAAa;;;;;;AAGnC", "debugId": null}}]}