module.exports = {

"[project]/src/types/license.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ApplicationStatus": (()=>ApplicationStatus)
});
var ApplicationStatus = /*#__PURE__*/ function(ApplicationStatus) {
    ApplicationStatus["DRAFT"] = "draft";
    ApplicationStatus["SUBMITTED"] = "submitted";
    ApplicationStatus["UNDER_REVIEW"] = "under_review";
    ApplicationStatus["EVALUATION"] = "evaluation";
    ApplicationStatus["APPROVED"] = "approved";
    ApplicationStatus["REJECTED"] = "rejected";
    ApplicationStatus["WITHDRAWN"] = "withdrawn";
    return ApplicationStatus;
}({});
}}),
"[project]/src/services/applicationService.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "applicationService": (()=>applicationService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/authUtils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/apiClient.ts [app-ssr] (ecmascript)");
;
;
const applicationService = {
    // Get all applications with pagination and filters
    async getApplications (params) {
        const queryParams = new URLSearchParams();
        if (params?.page) queryParams.append('page', params.page.toString());
        if (params?.limit) queryParams.append('limit', params.limit.toString());
        if (params?.search) queryParams.append('search', params.search);
        if (params?.sortBy) queryParams.append('sortBy', params.sortBy);
        if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);
        // Add filters
        if (params?.filters?.licenseTypeId) {
            queryParams.append('filter.license_category.license_type_id', params.filters.licenseTypeId);
        }
        if (params?.filters?.licenseCategoryId) {
            queryParams.append('filter.license_category_id', params.filters.licenseCategoryId);
        }
        if (params?.filters?.status) {
            queryParams.append('filter.status', params.filters.status);
        }
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/applications?${queryParams.toString()}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Get applications by license type (through license category)
    async getApplicationsByLicenseType (licenseTypeId, params) {
        const queryParams = new URLSearchParams();
        if (params?.page) queryParams.append('page', params.page.toString());
        if (params?.limit) queryParams.append('limit', params.limit.toString());
        if (params?.search) queryParams.append('search', params.search);
        if (params?.status) queryParams.append('filter.status', params.status);
        // Filter by license type through license category
        queryParams.append('filter.license_category.license_type_id', licenseTypeId);
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/applications?${queryParams.toString()}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Get single application by ID
    async getApplication (id) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/applications/${id}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Get applications by applicant
    async getApplicationsByApplicant (applicantId) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/applications/by-applicant/${applicantId}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Get applications by status
    async getApplicationsByStatus (status) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/applications/by-status/${status}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Update application status
    async updateApplicationStatus (id, status) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].put(`/applications/${id}/status?status=${status}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Update application progress
    async updateApplicationProgress (id, currentStep, progressPercentage) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].put(`/applications/${id}/progress?currentStep=${currentStep}&progressPercentage=${progressPercentage}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Get application statistics
    async getApplicationStats () {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get('/applications/stats');
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Create new application
    async createApplication (data) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post('/applications', data);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            throw error;
        }
    },
    // Update application with improved error handling
    async updateApplication (id, data) {
        try {
            console.log('Updating application:', id, 'with data:', data);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].put(`/applications/${id}`, data, {
                timeout: 30000
            });
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            console.error('Error updating application:', error);
            // Handle specific error cases
            if (error.code === 'ECONNABORTED') {
                throw new Error('Request timeout - please try again');
            }
            if (error.response?.status === 400) {
                const message = error.response?.data?.message || 'Invalid application data';
                console.error('400 Bad Request details:', error.response?.data);
                throw new Error(`Bad Request: ${message}`);
            }
            if (error.response?.status === 429) {
                throw new Error('Too many requests - please wait a moment and try again');
            }
            throw error;
        }
    },
    // Delete application
    async deleteApplication (id) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].delete(`/applications/${id}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Create new application with applicant data
    async createApplicationWithApplicant (data) {
        try {
            // Generate proper application number (format: APP-YYYYMMDD-HHMMSS-XXX)
            const now = new Date();
            const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');
            const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');
            const randomStr = Math.random().toString(36).substr(2, 3).toUpperCase();
            const applicationNumber = `APP-${dateStr}-${timeStr}-${randomStr}`;
            // Validate user_id is a proper UUID
            const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
            if (!uuidRegex.test(data.user_id)) {
                throw new Error(`Invalid user_id format: ${data.user_id}. Expected UUID format.`);
            }
            // Create application using user_id as applicant_id
            // In most systems, the authenticated user is the applicant
            const application = await this.createApplication({
                application_number: applicationNumber,
                applicant_id: data.user_id,
                license_category_id: data.license_category_id,
                current_step: 1,
                progress_percentage: 0 // Start with 0% progress
            });
            return application;
        } catch (error) {
            console.error('Error creating application with applicant:', error);
            throw error;
        }
    },
    // Save application section data
    async saveApplicationSection (applicationId, sectionName, sectionData) {
        try {
            // Try to save using form data service, but continue if it fails
            let completedSections = 1; // At least one section is being saved
            // Estimate progress based on section name
            const sectionOrder = [
                'applicantInfo',
                'companyProfile',
                'businessInfo',
                'serviceScope',
                'businessPlan',
                'legalHistory',
                'reviewSubmit'
            ];
            const sectionIndex = sectionOrder.indexOf(sectionName);
            completedSections = sectionIndex >= 0 ? sectionIndex + 1 : 1;
            // Calculate progress based on completed sections (excluding reviewSubmit from total)
            const totalSections = 6; // Total number of form sections (excluding reviewSubmit)
            const progressPercentage = Math.min(Math.round(completedSections / totalSections * 100), 100);
            // Update the application progress
            await this.updateApplication(applicationId, {
                progress_percentage: progressPercentage,
                current_step: completedSections
            });
        } catch (error) {
            throw error;
        }
    },
    // Get application section data
    async getApplicationSection (applicationId, sectionName) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/applications/${applicationId}/sections/${sectionName}`);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            throw error;
        }
    },
    // Submit application for review
    async submitApplication (applicationId) {
        try {
            // Update application status to submitted and set submission date
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].put(`/applications/${applicationId}`, {
                status: 'submitted',
                submitted_at: new Date().toISOString(),
                progress_percentage: 100,
                current_step: 7
            });
            console.log('Application submitted successfully:', (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response));
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            console.error('Error submitting application:', error);
            throw error;
        }
    },
    // Get user's applications (filtered by authenticated user)
    async getUserApplications () {
        try {
            // Use dedicated endpoint that explicitly filters by current user
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get('/applications/user-applications');
            const processedResponse = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
            // Handle paginated response structure
            let applications = [];
            if (processedResponse?.data) {
                applications = Array.isArray(processedResponse.data) ? processedResponse.data : [];
            } else if (Array.isArray(processedResponse)) {
                applications = processedResponse;
            } else if (processedResponse) {
                // Single application or other structure
                applications = [
                    processedResponse
                ];
            }
            return applications;
        } catch (error) {
            throw error;
        }
    },
    // Save application as draft
    async saveAsDraft (applicationId, formData) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].put(`/applications/${applicationId}`, {
                form_data: formData,
                status: 'draft'
            });
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            console.error('Error saving application as draft:', error);
            throw error;
        }
    },
    // Validate application before submission
    async validateApplication (applicationId) {
        try {
            // Get data from entity-specific APIs for validation
            let formData = {};
            const errors = [];
            const requiredSections = [
                'applicantInfo',
                'companyProfile',
                'businessInfo',
                'legalHistory'
            ];
            // Check if all required sections are completed
            for (const section of requiredSections){
                if (!formData[section] || Object.keys(formData[section]).length === 0) {
                    errors.push(`${section} section is incomplete`);
                }
            }
            return {
                isValid: errors.length === 0,
                errors
            };
        } catch (error) {
            console.error('Error validating application:', error);
            throw error;
        }
    }
};
}}),
"[project]/src/services/licenseCategoryService.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "addCodesToCategories": (()=>addCodesToCategories),
    "findCategoryByCode": (()=>findCategoryByCode),
    "findCategoryById": (()=>findCategoryById),
    "generateCategoryCode": (()=>generateCategoryCode),
    "licenseCategoryService": (()=>licenseCategoryService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/apiClient.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/cacheService.ts [app-ssr] (ecmascript)");
;
;
const generateCategoryCode = (name)=>{
    return name.toLowerCase().replace(/[^a-z0-9\s]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
    .substring(0, 50); // Limit length
};
const addCodesToCategories = (categories)=>{
    return categories.map((category)=>({
            ...category,
            code: generateCategoryCode(category.name),
            children: category.children ? addCodesToCategories(category.children) : undefined
        }));
};
const findCategoryByCode = (categories, code)=>{
    for (const category of categories){
        if (category.code === code) {
            return category;
        }
        if (category.children) {
            const found = findCategoryByCode(category.children, code);
            if (found) return found;
        }
    }
    return null;
};
const findCategoryById = (categories, id)=>{
    for (const category of categories){
        if (category.license_category_id === id) {
            return category;
        }
        if (category.children) {
            const found = findCategoryById(category.children, id);
            if (found) return found;
        }
    }
    return null;
};
const licenseCategoryService = {
    // Get all license categories with pagination
    async getLicenseCategories (query = {}) {
        const params = new URLSearchParams();
        if (query.page) params.set('page', query.page.toString());
        if (query.limit) params.set('limit', query.limit.toString());
        if (query.search) params.set('search', query.search);
        if (query.sortBy) {
            query.sortBy.forEach((sort)=>params.append('sortBy', sort));
        }
        if (query.searchBy) {
            query.searchBy.forEach((search)=>params.append('searchBy', search));
        }
        if (query.filter) {
            Object.entries(query.filter).forEach(([key, value])=>{
                if (Array.isArray(value)) {
                    value.forEach((v)=>params.append(`filter.${key}`, v));
                } else {
                    params.set(`filter.${key}`, value);
                }
            });
        }
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories?${params.toString()}`);
        return response.data;
    },
    // Get license category by ID with timeout and retry handling
    async getLicenseCategory (id) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories/${id}`, {
                timeout: 30000
            });
            return response.data;
        } catch (error) {
            console.error('Error fetching license category:', error);
            if (error.code === 'ECONNABORTED') {
                throw new Error('Request timeout - please try again');
            }
            throw error;
        }
    },
    // Get license categories by license type with improved error handling
    async getLicenseCategoriesByType (licenseTypeId) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories/by-license-type/${licenseTypeId}`, {
                timeout: 30000
            });
            return response.data;
        } catch (error) {
            console.error('Error fetching license categories by type:', error);
            if (error.code === 'ECONNABORTED') {
                throw new Error('Request timeout - please try again');
            }
            if (error.response?.status === 429) {
                throw new Error('Too many requests - please wait a moment and try again');
            }
            throw error;
        }
    },
    // Create new license category
    async createLicenseCategory (licenseCategoryData) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post('/license-categories', licenseCategoryData);
        return response.data;
    },
    // Update license category
    async updateLicenseCategory (id, licenseCategoryData) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].put(`/license-categories/${id}`, licenseCategoryData);
        return response.data;
    },
    // Delete license category
    async deleteLicenseCategory (id) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].delete(`/license-categories/${id}`);
        return response.data;
    },
    // Get all license categories (simple list for dropdowns) with caching
    async getAllLicenseCategories () {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cacheService"].getOrSet(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CACHE_KEYS"].LICENSE_CATEGORIES, async ()=>{
            console.log('Fetching license categories from API...');
            // Reduce limit to avoid rate limiting
            const response = await this.getLicenseCategories({
                limit: 100
            });
            return addCodesToCategories(response.data);
        }, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CACHE_TTL"].LONG // Cache for 15 minutes
        );
    },
    // Get hierarchical tree of categories for a license type with caching
    async getCategoryTree (licenseTypeId) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cacheService"].getOrSet(`category-tree-${licenseTypeId}`, async ()=>{
            console.log(`Fetching category tree for license type: ${licenseTypeId}`);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories/license-type/${licenseTypeId}/tree`);
            return addCodesToCategories(response.data);
        }, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CACHE_TTL"].MEDIUM // Cache for 5 minutes
        );
    },
    // Get root categories (no parent) for a license type with caching
    async getRootCategories (licenseTypeId) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cacheService"].getOrSet(`root-categories-${licenseTypeId}`, async ()=>{
            console.log(`Fetching root categories for license type: ${licenseTypeId}`);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories/license-type/${licenseTypeId}/root`);
            return response.data;
        }, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CACHE_TTL"].MEDIUM // Cache for 5 minutes
        );
    },
    // Get license categories for parent selection dropdown
    async getCategoriesForParentSelection (licenseTypeId, excludeId) {
        try {
            const params = excludeId ? {
                excludeId
            } : {};
            console.log('🚀 Fetching parent categories for license type:', licenseTypeId, 'with params:', params);
            // Try the new endpoint first
            try {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories/license-type/${licenseTypeId}/for-parent-selection`, {
                    params
                });
                if (response.data && Array.isArray(response.data.data)) {
                    console.log('✅ Valid array response with', response.data.data.length, 'items');
                    return response.data.data;
                } else {
                    console.warn('⚠️ API returned non-array data:', response.data);
                    return [];
                }
            } catch (newEndpointError) {
                console.warn('🔄 New endpoint failed, trying fallback:', newEndpointError);
                // Fallback to existing endpoint
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories/by-license-type/${licenseTypeId}`);
                console.log('🔄 Fallback response:', response.data);
                if (response.data && Array.isArray(response.data)) {
                    // Filter out the excluded category if specified
                    let categories = response.data;
                    if (excludeId) {
                        categories = categories.filter((cat)=>cat.license_category_id !== excludeId);
                    }
                    console.log('✅ Fallback successful with', categories.length, 'items');
                    return categories;
                } else {
                    console.warn('⚠️ Fallback also returned non-array data:', response.data);
                    return [];
                }
            }
        } catch (error) {
            return [];
        }
    },
    // Get potential parent categories for a license type
    async getPotentialParents (licenseTypeId, excludeId) {
        const params = excludeId ? {
            excludeId
        } : {};
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories/license-type/${licenseTypeId}/potential-parents`, {
            params
        });
        return response.data;
    }
};
}}),
"[project]/src/services/task-assignment.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "taskAssignmentService": (()=>taskAssignmentService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/apiClient.ts [app-ssr] (ecmascript)");
;
const taskAssignmentService = {
    // Generic task management methods
    getUnassignedTasks: async (params)=>{
        const searchParams = new URLSearchParams();
        if (params?.page) searchParams.append('page', params.page.toString());
        if (params?.limit) searchParams.append('limit', params.limit.toString());
        if (params?.search) searchParams.append('search', params.search);
        if (params?.task_type) searchParams.append('task_type', params.task_type);
        const queryString = searchParams.toString();
        const url = `/tasks/unassigned${queryString ? `?${queryString}` : ''}`;
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(url);
        return response.data;
    },
    getAssignedTasks: async (params)=>{
        const searchParams = new URLSearchParams();
        if (params?.page) searchParams.append('page', params.page.toString());
        if (params?.limit) searchParams.append('limit', params.limit.toString());
        if (params?.search) searchParams.append('search', params.search);
        if (params?.task_type) searchParams.append('task_type', params.task_type);
        const queryString = searchParams.toString();
        const url = `/tasks/assigned${queryString ? `?${queryString}` : ''}`;
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(url);
        return response.data;
    },
    assignTask: async (taskId, assignData)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].put(`/tasks/${taskId}/assign`, assignData);
        return response.data;
    },
    getTaskById: async (taskId)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/tasks/${taskId}`);
        return response.data;
    },
    // Legacy application-specific methods (for backward compatibility)
    getUnassignedApplications: async (params)=>{
        const searchParams = new URLSearchParams();
        if (params?.page) searchParams.append('page', params.page.toString());
        if (params?.limit) searchParams.append('limit', params.limit.toString());
        if (params?.search) searchParams.append('search', params.search);
        const queryString = searchParams.toString();
        const url = `/applications/unassigned${queryString ? `?${queryString}` : ''}`;
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(url);
        return response.data;
    },
    // Get all applications (including assigned)
    getAllApplications: async (params)=>{
        const searchParams = new URLSearchParams();
        if (params?.page) searchParams.append('page', params.page.toString());
        if (params?.limit) searchParams.append('limit', params.limit.toString());
        if (params?.search) searchParams.append('search', params.search);
        const queryString = searchParams.toString();
        const url = `/applications${queryString ? `?${queryString}` : ''}`;
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(url);
        return response.data;
    },
    // Get applications assigned to current user
    getMyAssignedApplications: async (params)=>{
        const searchParams = new URLSearchParams();
        if (params?.page) searchParams.append('page', params.page.toString());
        if (params?.limit) searchParams.append('limit', params.limit.toString());
        if (params?.search) searchParams.append('search', params.search);
        const queryString = searchParams.toString();
        const url = `/applications/assigned/me${queryString ? `?${queryString}` : ''}`;
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(url);
        return response.data;
    },
    // Get officers for assignment
    getOfficers: async ()=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get('/users');
            return response.data;
        } catch (error) {
            console.error('Error fetching officers:', error);
            return {
                data: []
            };
        }
    },
    // Assign application to officer
    assignApplication: async (applicationId, assignData)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].put(`/applications/${applicationId}/assign`, assignData);
        return response.data;
    },
    // Get application details
    getApplication: async (applicationId)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/applications/${applicationId}`);
        return response.data;
    }
};
}}),
"[project]/src/components/common/AssignModal.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ToastContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/ToastContext.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$task$2d$assignment$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/task-assignment.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
const AssignModal = ({ isOpen, onClose, itemId, itemType, itemTitle, onAssignSuccess })=>{
    const { showSuccess, showError } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ToastContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useToast"])();
    const [officers, setOfficers] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [filteredOfficers, setFilteredOfficers] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [assigning, setAssigning] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [searchQuery, setSearchQuery] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    const [selectedOfficer, setSelectedOfficer] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    const [comment, setComment] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (isOpen) {
            fetchOfficers();
            setSearchQuery('');
            setSelectedOfficer('');
            setComment('');
        }
    }, [
        isOpen
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Filter officers based on search query
        if (searchQuery.trim() === '') {
            setFilteredOfficers(officers);
        } else {
            const filtered = officers.filter((officer)=>`${officer.first_name} ${officer.last_name}`.toLowerCase().includes(searchQuery.toLowerCase()) || officer.email.toLowerCase().includes(searchQuery.toLowerCase()) || officer.department?.toLowerCase().includes(searchQuery.toLowerCase()));
            setFilteredOfficers(filtered);
        }
    }, [
        officers,
        searchQuery
    ]);
    const fetchOfficers = async ()=>{
        setLoading(true);
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$task$2d$assignment$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskAssignmentService"].getOfficers();
            setOfficers(response.data || []);
        } catch (error) {
            console.error('Error fetching officers:', error);
            showError('Failed to load officers');
        } finally{
            setLoading(false);
        }
    };
    const handleAssign = async ()=>{
        if (!selectedOfficer || !itemId) {
            showError('Please select an officer');
            return;
        }
        setAssigning(true);
        try {
            // Create a task for the assignment
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$task$2d$assignment$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskAssignmentService"].assignTask(itemId, {
                assignedTo: selectedOfficer,
                comment: comment.trim() || undefined
            });
            showSuccess('Successfully assigned to officer');
            onAssignSuccess?.();
            onClose();
        } catch (error) {
            console.error('Error assigning task:', error);
            showError('Failed to assign task');
        } finally{
            setAssigning(false);
        }
    };
    const getSelectedOfficerDetails = ()=>{
        return officers.find((officer)=>officer.user_id === selectedOfficer);
    };
    if (!isOpen) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mt-3",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-between mb-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-lg font-medium text-gray-900 dark:text-gray-100",
                                children: [
                                    "Assign ",
                                    itemType.replace('_', ' ').toUpperCase()
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/AssignModal.tsx",
                                lineNumber: 114,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                type: "button",
                                onClick: onClose,
                                className: "text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                    className: "ri-close-line text-xl"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/AssignModal.tsx",
                                    lineNumber: 122,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/AssignModal.tsx",
                                lineNumber: 117,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/AssignModal.tsx",
                        lineNumber: 113,
                        columnNumber: 11
                    }, this),
                    itemTitle && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                className: "text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",
                                children: "Item to Assign:"
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/AssignModal.tsx",
                                lineNumber: 129,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-sm text-gray-600 dark:text-gray-400",
                                children: itemTitle
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/AssignModal.tsx",
                                lineNumber: 132,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/AssignModal.tsx",
                        lineNumber: 128,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mb-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                children: "Search Officers"
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/AssignModal.tsx",
                                lineNumber: 138,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                type: "text",
                                placeholder: "Search by name, email, or department...",
                                value: searchQuery,
                                onChange: (e)=>setSearchQuery(e.target.value),
                                className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/AssignModal.tsx",
                                lineNumber: 141,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/AssignModal.tsx",
                        lineNumber: 137,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mb-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                children: [
                                    "Select Officer (",
                                    filteredOfficers.length,
                                    " found)"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/AssignModal.tsx",
                                lineNumber: 152,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "max-h-64 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md",
                                children: loading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "p-4 text-center text-gray-500 dark:text-gray-400",
                                    children: "Loading officers..."
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/AssignModal.tsx",
                                    lineNumber: 157,
                                    columnNumber: 17
                                }, this) : filteredOfficers.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "p-4 text-center text-gray-500 dark:text-gray-400",
                                    children: "No officers found"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/AssignModal.tsx",
                                    lineNumber: 161,
                                    columnNumber: 17
                                }, this) : filteredOfficers.map((officer)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: `p-3 border-b border-gray-200 dark:border-gray-600 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 ${selectedOfficer === officer.user_id ? 'bg-blue-50 dark:bg-blue-900' : ''}`,
                                        onClick: ()=>setSelectedOfficer(officer.user_id),
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                    type: "radio",
                                                    name: "officer",
                                                    value: officer.user_id,
                                                    checked: selectedOfficer === officer.user_id,
                                                    onChange: ()=>setSelectedOfficer(officer.user_id),
                                                    className: "mr-3"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/common/AssignModal.tsx",
                                                    lineNumber: 174,
                                                    columnNumber: 23
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex-1",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "text-sm font-medium text-gray-900 dark:text-gray-100",
                                                            children: [
                                                                officer.first_name,
                                                                " ",
                                                                officer.last_name
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/common/AssignModal.tsx",
                                                            lineNumber: 183,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "text-sm text-gray-500 dark:text-gray-400",
                                                            children: officer.email
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/common/AssignModal.tsx",
                                                            lineNumber: 186,
                                                            columnNumber: 25
                                                        }, this),
                                                        officer.department && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "text-xs text-gray-400 dark:text-gray-500",
                                                            children: officer.department
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/common/AssignModal.tsx",
                                                            lineNumber: 190,
                                                            columnNumber: 27
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/common/AssignModal.tsx",
                                                    lineNumber: 182,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/common/AssignModal.tsx",
                                            lineNumber: 173,
                                            columnNumber: 21
                                        }, this)
                                    }, officer.user_id, false, {
                                        fileName: "[project]/src/components/common/AssignModal.tsx",
                                        lineNumber: 166,
                                        columnNumber: 19
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/AssignModal.tsx",
                                lineNumber: 155,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/AssignModal.tsx",
                        lineNumber: 151,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mb-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                children: "Assignment Comment (Optional)"
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/AssignModal.tsx",
                                lineNumber: 204,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                value: comment,
                                onChange: (e)=>setComment(e.target.value),
                                placeholder: "Add any notes or instructions for the assigned officer...",
                                rows: 3,
                                className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/AssignModal.tsx",
                                lineNumber: 207,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/AssignModal.tsx",
                        lineNumber: 203,
                        columnNumber: 11
                    }, this),
                    selectedOfficer && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mb-6 p-4 bg-green-50 dark:bg-green-900 rounded-lg",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                className: "text-sm font-medium text-green-900 dark:text-green-100 mb-2",
                                children: "Selected Officer:"
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/AssignModal.tsx",
                                lineNumber: 219,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-sm text-green-700 dark:text-green-200",
                                children: [
                                    getSelectedOfficerDetails()?.first_name,
                                    " ",
                                    getSelectedOfficerDetails()?.last_name,
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                        fileName: "[project]/src/components/common/AssignModal.tsx",
                                        lineNumber: 224,
                                        columnNumber: 17
                                    }, this),
                                    getSelectedOfficerDetails()?.email,
                                    getSelectedOfficerDetails()?.department && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                                fileName: "[project]/src/components/common/AssignModal.tsx",
                                                lineNumber: 228,
                                                columnNumber: 21
                                            }, this),
                                            "Department: ",
                                            getSelectedOfficerDetails()?.department
                                        ]
                                    }, void 0, true)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/AssignModal.tsx",
                                lineNumber: 222,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/AssignModal.tsx",
                        lineNumber: 218,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex justify-end space-x-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                type: "button",
                                onClick: onClose,
                                className: "px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",
                                children: "Cancel"
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/AssignModal.tsx",
                                lineNumber: 238,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                type: "button",
                                onClick: handleAssign,
                                disabled: !selectedOfficer || assigning,
                                className: "px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed",
                                children: assigning ? 'Assigning...' : 'Assign'
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/AssignModal.tsx",
                                lineNumber: 245,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/AssignModal.tsx",
                        lineNumber: 237,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/AssignModal.tsx",
                lineNumber: 111,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/common/AssignModal.tsx",
            lineNumber: 110,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/common/AssignModal.tsx",
        lineNumber: 109,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = AssignModal;
}}),
"[project]/src/components/common/AssignButton.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$AssignModal$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/AssignModal.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
const AssignButton = ({ itemId, itemType, itemTitle, onAssignSuccess, className = '', size = 'sm', variant = 'success', disabled = false, children })=>{
    const [showAssignModal, setShowAssignModal] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const handleAssignClick = ()=>{
        setShowAssignModal(true);
    };
    const handleCloseModal = ()=>{
        setShowAssignModal(false);
    };
    const handleAssignSuccess = ()=>{
        setShowAssignModal(false);
        onAssignSuccess?.();
    };
    // Size classes
    const sizeClasses = {
        sm: 'px-3 py-1 text-xs',
        md: 'px-4 py-2 text-sm',
        lg: 'px-6 py-3 text-base'
    };
    // Variant classes
    const variantClasses = {
        primary: 'text-white bg-primary hover:bg-primary-dark focus:ring-primary',
        secondary: 'text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 focus:ring-gray-500',
        success: 'text-white bg-green-600 hover:bg-green-700 focus:ring-green-500'
    };
    // Base classes
    const baseClasses = 'inline-flex items-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed';
    // Combine all classes
    const buttonClasses = `${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]} ${className}`;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                type: "button",
                onClick: handleAssignClick,
                disabled: disabled,
                className: buttonClasses,
                title: `Assign ${itemType.replace('_', ' ')} to officer`,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                        className: "ri-user-add-line mr-1"
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/AssignButton.tsx",
                        lineNumber: 73,
                        columnNumber: 9
                    }, this),
                    children || 'Assign'
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/AssignButton.tsx",
                lineNumber: 66,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$AssignModal$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                isOpen: showAssignModal,
                onClose: handleCloseModal,
                itemId: itemId,
                itemType: itemType,
                itemTitle: itemTitle,
                onAssignSuccess: handleAssignSuccess
            }, void 0, false, {
                fileName: "[project]/src/components/common/AssignButton.tsx",
                lineNumber: 77,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
};
const __TURBOPACK__default__export__ = AssignButton;
}}),
"[project]/src/components/common/Pagination.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
const Pagination = ({ meta, onPageChange, onPageSizeChange, showFirstLast = true, showPageSizeSelector = true, showInfo = true, maxVisiblePages = 7, pageSizeOptions = [
    10,
    25,
    50,
    100
], className = '' })=>{
    const { currentPage, totalPages, totalItems, itemsPerPage } = meta;
    // Don't render if there's only one page or no pages and no additional features
    if (totalPages <= 1 && !showPageSizeSelector && !showInfo) {
        return null;
    }
    // Calculate current items range
    const startItem = totalItems > 0 ? (currentPage - 1) * itemsPerPage + 1 : 0;
    const endItem = Math.min(currentPage * itemsPerPage, totalItems);
    // Calculate which pages to show
    const getVisiblePages = ()=>{
        const pages = [];
        // If total pages is less than or equal to maxVisiblePages, show all
        if (totalPages <= maxVisiblePages) {
            for(let i = 1; i <= totalPages; i++){
                pages.push(i);
            }
            return pages;
        }
        // Always show first page
        pages.push(1);
        // Calculate start and end of the visible range around current page
        const sidePages = Math.floor((maxVisiblePages - 3) / 2); // -3 for first, last, and current
        let startPage = Math.max(2, currentPage - sidePages);
        let endPage = Math.min(totalPages - 1, currentPage + sidePages);
        // Adjust if we're near the beginning
        if (currentPage <= sidePages + 2) {
            startPage = 2;
            endPage = Math.min(totalPages - 1, maxVisiblePages - 1);
        }
        // Adjust if we're near the end
        if (currentPage >= totalPages - sidePages - 1) {
            startPage = Math.max(2, totalPages - maxVisiblePages + 2);
            endPage = totalPages - 1;
        }
        // Add ellipsis after first page if needed
        if (startPage > 2) {
            pages.push('...');
        }
        // Add pages in the visible range
        for(let i = startPage; i <= endPage; i++){
            pages.push(i);
        }
        // Add ellipsis before last page if needed
        if (endPage < totalPages - 1) {
            pages.push('...');
        }
        // Always show last page (if it's not already included)
        if (totalPages > 1) {
            pages.push(totalPages);
        }
        return pages;
    };
    const visiblePages = getVisiblePages();
    const handlePageClick = (page)=>{
        if (typeof page === 'number' && page !== currentPage) {
            onPageChange(page);
        }
    };
    const handlePageSizeChange = (event)=>{
        const newPageSize = parseInt(event.target.value);
        if (onPageSizeChange) {
            onPageSizeChange(newPageSize);
        }
    };
    const handlePrevious = ()=>{
        if (currentPage > 1) {
            onPageChange(currentPage - 1);
        }
    };
    const handleNext = ()=>{
        if (currentPage < totalPages) {
            onPageChange(currentPage + 1);
        }
    };
    const handleFirst = ()=>{
        if (currentPage !== 1) {
            onPageChange(1);
        }
    };
    const handleLast = ()=>{
        if (currentPage !== totalPages) {
            onPageChange(totalPages);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0 px-6 py-3 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 ${className}`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-4",
                children: [
                    showInfo && totalItems > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-sm text-gray-700 dark:text-gray-300",
                        children: [
                            "Showing ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: startItem
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/Pagination.tsx",
                                lineNumber: 152,
                                columnNumber: 21
                            }, this),
                            " to",
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: endItem
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/Pagination.tsx",
                                lineNumber: 153,
                                columnNumber: 13
                            }, this),
                            " of",
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: totalItems
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/Pagination.tsx",
                                lineNumber: 154,
                                columnNumber: 13
                            }, this),
                            " results"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/Pagination.tsx",
                        lineNumber: 151,
                        columnNumber: 11
                    }, this),
                    showPageSizeSelector && onPageSizeChange && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center space-x-2",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                            value: itemsPerPage,
                            onChange: handlePageSizeChange,
                            className: "px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",
                            children: pageSizeOptions.map((size)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                    value: size,
                                    children: [
                                        size,
                                        " per page"
                                    ]
                                }, size, true, {
                                    fileName: "[project]/src/components/common/Pagination.tsx",
                                    lineNumber: 167,
                                    columnNumber: 17
                                }, this))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/Pagination.tsx",
                            lineNumber: 161,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/Pagination.tsx",
                        lineNumber: 160,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/Pagination.tsx",
                lineNumber: 148,
                columnNumber: 7
            }, this),
            totalPages > 1 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                className: "flex items-center space-x-1",
                "aria-label": "Pagination",
                children: [
                    showFirstLast && currentPage > 1 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: handleFirst,
                        className: "inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-md hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300",
                        "aria-label": "Go to first page",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                            className: "ri-skip-back-line"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/Pagination.tsx",
                            lineNumber: 186,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/Pagination.tsx",
                        lineNumber: 181,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: handlePrevious,
                        disabled: currentPage === 1,
                        className: `inline-flex items-center px-2 py-2 text-sm font-medium border ${currentPage === 1 ? 'text-gray-300 bg-gray-100 border-gray-300 cursor-not-allowed dark:bg-gray-700 dark:border-gray-600 dark:text-gray-500' : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300'} ${showFirstLast && currentPage > 1 ? '' : 'rounded-l-md'}`,
                        "aria-label": "Go to previous page",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                            className: "ri-arrow-left-s-line"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/Pagination.tsx",
                            lineNumber: 201,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/Pagination.tsx",
                        lineNumber: 191,
                        columnNumber: 11
                    }, this),
                    visiblePages.map((page, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].Fragment, {
                            children: page === '...' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400",
                                children: "..."
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/Pagination.tsx",
                                lineNumber: 208,
                                columnNumber: 17
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>handlePageClick(page),
                                className: `inline-flex items-center px-4 py-2 text-sm font-medium border ${page === currentPage ? 'text-white bg-red-600 border-red-600 hover:bg-red-700' : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300'}`,
                                "aria-label": `Go to page ${page}`,
                                "aria-current": page === currentPage ? 'page' : undefined,
                                children: page
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/Pagination.tsx",
                                lineNumber: 212,
                                columnNumber: 17
                            }, this)
                        }, index, false, {
                            fileName: "[project]/src/components/common/Pagination.tsx",
                            lineNumber: 206,
                            columnNumber: 13
                        }, this)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: handleNext,
                        disabled: currentPage === totalPages,
                        className: `inline-flex items-center px-2 py-2 text-sm font-medium border ${currentPage === totalPages ? 'text-gray-300 bg-gray-100 border-gray-300 cursor-not-allowed dark:bg-gray-700 dark:border-gray-600 dark:text-gray-500' : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300'} ${showFirstLast && currentPage < totalPages ? '' : 'rounded-r-md'}`,
                        "aria-label": "Go to next page",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                            className: "ri-arrow-right-s-line"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/Pagination.tsx",
                            lineNumber: 239,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/Pagination.tsx",
                        lineNumber: 229,
                        columnNumber: 11
                    }, this),
                    showFirstLast && currentPage < totalPages && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: handleLast,
                        className: "inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-md hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300",
                        "aria-label": "Go to last page",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                            className: "ri-skip-forward-line"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/Pagination.tsx",
                            lineNumber: 249,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/Pagination.tsx",
                        lineNumber: 244,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/Pagination.tsx",
                lineNumber: 178,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/Pagination.tsx",
        lineNumber: 146,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = Pagination;
}}),
"[project]/src/components/common/DataTable.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>DataTable)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Pagination$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/Pagination.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
function DataTable({ columns, data, loading = false, onQueryChange, searchPlaceholder = "Search...", className = "" }) {
    const [query, setQuery] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        page: 1,
        limit: 10,
        search: '',
        sortBy: []
    });
    const [searchInput, setSearchInput] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    // Debounced search effect
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const timeoutId = setTimeout(()=>{
            if (searchInput !== query.search) {
                handleSearch(searchInput);
            }
        }, 300);
        return ()=>clearTimeout(timeoutId);
    }, [
        searchInput
    ]);
    const handleSearch = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((search)=>{
        try {
            const newQuery = {
                ...query,
                search,
                page: 1
            };
            setQuery(newQuery);
            onQueryChange(newQuery);
        } catch (error) {
            console.error('Error handling search:', error);
        }
    }, [
        query,
        onQueryChange
    ]);
    const handleSort = (columnKey)=>{
        const currentSort = query.sortBy?.find((sort)=>sort.startsWith(columnKey));
        let newSortBy = [];
        if (!currentSort) {
            newSortBy = [
                `${columnKey}:ASC`
            ];
        } else if (currentSort.endsWith(':ASC')) {
            newSortBy = [
                `${columnKey}:DESC`
            ];
        } else {
            newSortBy = [];
        }
        const newQuery = {
            ...query,
            sortBy: newSortBy,
            page: 1
        };
        setQuery(newQuery);
        onQueryChange(newQuery);
    };
    const handlePageChange = (page)=>{
        const newQuery = {
            ...query,
            page
        };
        setQuery(newQuery);
        onQueryChange(newQuery);
    };
    const handleLimitChange = (limit)=>{
        const newQuery = {
            ...query,
            limit,
            page: 1
        };
        setQuery(newQuery);
        onQueryChange(newQuery);
    };
    const getSortDirection = (columnKey)=>{
        const currentSort = query.sortBy?.find((sort)=>sort.startsWith(columnKey));
        if (!currentSort) return null;
        return currentSort.endsWith(':ASC') ? 'asc' : 'desc';
    };
    // Handle null data case early
    if (!data) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: `bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden ${className}`,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "p-6 text-center text-gray-500 dark:text-gray-400",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center justify-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "animate-spin rounded-full h-6 w-6 border-b-2 border-red-600"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/DataTable.tsx",
                            lineNumber: 103,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "ml-2",
                            children: "Loading..."
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/DataTable.tsx",
                            lineNumber: 104,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/DataTable.tsx",
                    lineNumber: 102,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/common/DataTable.tsx",
                lineNumber: 101,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/common/DataTable.tsx",
            lineNumber: 100,
            columnNumber: 7
        }, this);
    }
    const handlePageSizeChange = (newPageSize)=>{
        const newQuery = {
            ...query,
            limit: newPageSize,
            page: 1
        };
        setQuery(newQuery);
        onQueryChange(newQuery);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden ${className}`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "p-6 border-b border-gray-200 dark:border-gray-700",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "relative",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                className: "ri-search-line text-gray-400 dark:text-gray-500"
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/DataTable.tsx",
                                lineNumber: 123,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/DataTable.tsx",
                            lineNumber: 122,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                            type: "text",
                            placeholder: searchPlaceholder,
                            value: searchInput,
                            onChange: (e)=>setSearchInput(e.target.value),
                            className: "block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-gray-900 dark:text-gray-100"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/DataTable.tsx",
                            lineNumber: 125,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/DataTable.tsx",
                    lineNumber: 121,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/common/DataTable.tsx",
                lineNumber: 120,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "overflow-x-auto",
                style: {
                    minHeight: '500px'
                },
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("table", {
                    className: "min-w-full divide-y divide-gray-200 dark:divide-gray-700",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("thead", {
                            className: "bg-gray-50 dark:bg-gray-900",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                children: columns.map((column)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                        className: `px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${column.sortable ? 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800' : ''} ${column.className || ''}`,
                                        onClick: ()=>column.sortable && handleSort(String(column.key)),
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center space-x-1",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    children: column.label
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/common/DataTable.tsx",
                                                    lineNumber: 149,
                                                    columnNumber: 21
                                                }, this),
                                                column.sortable && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex flex-col",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                            className: `ri-arrow-up-s-line text-xs ${getSortDirection(String(column.key)) === 'asc' ? 'text-red-600' : 'text-gray-400'}`
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/common/DataTable.tsx",
                                                            lineNumber: 152,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                            className: `ri-arrow-down-s-line text-xs -mt-1 ${getSortDirection(String(column.key)) === 'desc' ? 'text-red-600' : 'text-gray-400'}`
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/common/DataTable.tsx",
                                                            lineNumber: 155,
                                                            columnNumber: 25
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/common/DataTable.tsx",
                                                    lineNumber: 151,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/common/DataTable.tsx",
                                            lineNumber: 148,
                                            columnNumber: 19
                                        }, this)
                                    }, String(column.key), false, {
                                        fileName: "[project]/src/components/common/DataTable.tsx",
                                        lineNumber: 141,
                                        columnNumber: 17
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/DataTable.tsx",
                                lineNumber: 139,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/DataTable.tsx",
                            lineNumber: 138,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("tbody", {
                            className: "bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",
                            children: loading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    colSpan: columns.length,
                                    className: "px-6 py-4 text-center text-gray-500 dark:text-gray-400",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center justify-center",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "animate-spin rounded-full h-6 w-6 border-b-2 border-red-600"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/common/DataTable.tsx",
                                                lineNumber: 170,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "ml-2",
                                                children: "Loading..."
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/common/DataTable.tsx",
                                                lineNumber: 171,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/DataTable.tsx",
                                        lineNumber: 169,
                                        columnNumber: 19
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/DataTable.tsx",
                                    lineNumber: 168,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/DataTable.tsx",
                                lineNumber: 167,
                                columnNumber: 15
                            }, this) : !data?.data || data.data.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    colSpan: columns.length,
                                    className: "px-6 py-4 text-center text-gray-500 dark:text-gray-400",
                                    children: "No data found"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/DataTable.tsx",
                                    lineNumber: 177,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/DataTable.tsx",
                                lineNumber: 176,
                                columnNumber: 15
                            }, this) : data.data.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                    className: "hover:bg-gray-50 dark:hover:bg-gray-700",
                                    children: columns.map((column)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                            className: "px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100",
                                            children: column.render ? column.render(item[column.key], item) : String(item[column.key] || '')
                                        }, String(column.key), false, {
                                            fileName: "[project]/src/components/common/DataTable.tsx",
                                            lineNumber: 185,
                                            columnNumber: 21
                                        }, this))
                                }, index, false, {
                                    fileName: "[project]/src/components/common/DataTable.tsx",
                                    lineNumber: 183,
                                    columnNumber: 17
                                }, this))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/DataTable.tsx",
                            lineNumber: 165,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/DataTable.tsx",
                    lineNumber: 137,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/common/DataTable.tsx",
                lineNumber: 136,
                columnNumber: 7
            }, this),
            data?.meta && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Pagination$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                meta: data.meta,
                onPageChange: handlePageChange,
                onPageSizeChange: handlePageSizeChange,
                showFirstLast: true,
                showPageSizeSelector: true,
                showInfo: true,
                maxVisiblePages: 7,
                pageSizeOptions: [
                    10,
                    25,
                    50,
                    100
                ]
            }, void 0, false, {
                fileName: "[project]/src/components/common/DataTable.tsx",
                lineNumber: 201,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/DataTable.tsx",
        lineNumber: 118,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/components/common/Select.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
const Select = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ label, error, helperText, required = false, options = [], placeholder = 'Select an option...', className = '', containerClassName = '', onChange, id, value, ...props }, ref)=>{
    const selectId = id || `select-${Math.random().toString(36).substr(2, 9)}`;
    const baseSelectClasses = `
    w-full px-3 py-2 border rounded-md shadow-sm 
    focus:outline-none focus:ring-2 focus:ring-offset-2 
    disabled:opacity-50 disabled:cursor-not-allowed
    dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600
    transition-colors duration-200
    appearance-none bg-white
    bg-no-repeat bg-right bg-[length:16px_16px]
    pr-10
  `;
    const selectClasses = error ? `${baseSelectClasses} border-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-600` : `${baseSelectClasses} border-gray-300 focus:border-primary focus:ring-primary dark:focus:border-primary dark:focus:ring-primary`;
    const handleChange = (e)=>{
        if (onChange) {
            onChange(e.target.value);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `space-y-1 ${containerClassName}`,
        children: [
            label && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                htmlFor: selectId,
                className: "block text-sm font-medium text-gray-700 dark:text-gray-300",
                children: [
                    label,
                    required && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "text-red-500 ml-1",
                        children: "*"
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/Select.tsx",
                        lineNumber: 68,
                        columnNumber: 24
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/Select.tsx",
                lineNumber: 63,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "relative",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                        ref: ref,
                        id: selectId,
                        value: value || '',
                        onChange: handleChange,
                        className: `${selectClasses} ${className}`,
                        ...props,
                        children: [
                            placeholder && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                value: "",
                                disabled: true,
                                children: placeholder
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/Select.tsx",
                                lineNumber: 82,
                                columnNumber: 13
                            }, this),
                            options.map((option)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                    value: option.value,
                                    disabled: option.disabled,
                                    children: option.label
                                }, option.value, false, {
                                    fileName: "[project]/src/components/common/Select.tsx",
                                    lineNumber: 88,
                                    columnNumber: 13
                                }, this))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/Select.tsx",
                        lineNumber: 73,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                            className: "ri-arrow-down-s-line text-gray-400 dark:text-gray-500"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/Select.tsx",
                            lineNumber: 100,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/Select.tsx",
                        lineNumber: 99,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/Select.tsx",
                lineNumber: 72,
                columnNumber: 7
            }, this),
            error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "text-sm text-red-600 dark:text-red-400 flex items-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                        className: "ri-error-warning-line mr-1"
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/Select.tsx",
                        lineNumber: 106,
                        columnNumber: 11
                    }, this),
                    error
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/Select.tsx",
                lineNumber: 105,
                columnNumber: 9
            }, this),
            helperText && !error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "text-sm text-gray-500 dark:text-gray-400",
                children: helperText
            }, void 0, false, {
                fileName: "[project]/src/components/common/Select.tsx",
                lineNumber: 112,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/Select.tsx",
        lineNumber: 61,
        columnNumber: 5
    }, this);
});
Select.displayName = 'Select';
const __TURBOPACK__default__export__ = Select;
}}),
"[project]/src/components/license/LicenseManagementTable.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>LicenseManagementTable)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/license.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicationService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/applicationService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$licenseCategoryService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/licenseCategoryService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$licenseTypeService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/licenseTypeService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$AssignButton$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/AssignButton.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$DataTable$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/DataTable.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Select$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/Select.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
;
;
;
function LicenseManagementTable({ licenseTypeId, licenseTypeCode, licenseTypeFilter, title, description, searchPlaceholder, emptyStateIcon, emptyStateMessage, departmentType }) {
    const { user } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuth"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    const [applicationsData, setApplicationsData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [licenseCategories, setLicenseCategories] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [licenseTypes, setLicenseTypes] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [resolvedLicenseTypeId, setResolvedLicenseTypeId] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(undefined);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [selectedLicenseCategory, setSelectedLicenseCategory] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    const [statusFilter, setStatusFilter] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    const [dateRangeFilter, setDateRangeFilter] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    const isAdmin = user?.isAdmin;
    // Navigation function for viewing applications - opens evaluation page
    const handleViewApplication = (applicationId)=>{
        // Get the license type code from the application data or use departmentType/licenseTypeCode
        const application = applicationsData?.data?.find((app)=>app.application_id === applicationId);
        // Debug application data structure
        console.log('🔍 Application Data Debug:', {
            applicationId,
            application,
            license_category_id: application?.license_category_id,
            license_category: application?.license_category
        });
        const resolvedLicenseTypeCode = application?.license_category?.license_type?.code || licenseTypeCode || departmentType || 'telecommunications'; // fallback
        // Create URL with both application_id and license_category_id
        const params = new URLSearchParams();
        params.set('application_id', applicationId);
        // Get license category ID from application
        const licenseCategoryId = application?.license_category_id;
        if (licenseCategoryId) {
            params.set('license_category_id', licenseCategoryId);
        } else {
            console.warn('⚠️ No license_category_id found in application data');
        }
        // Navigate to evaluation page with proper query parameters
        router.push(`/applications/${resolvedLicenseTypeCode}/evaluate?${params.toString()}`);
    };
    // Load applications function for DataTable
    const loadApplications = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (query)=>{
        try {
            setLoading(true);
            setError(null);
            const effectiveLicenseTypeId = licenseTypeId || resolvedLicenseTypeId;
            // Build filters object
            const filters = {};
            // Add license category filter if selected
            if (selectedLicenseCategory) {
                filters.licenseCategoryId = selectedLicenseCategory;
            }
            // Add license type filter - prioritize licenseTypeCode search
            if (effectiveLicenseTypeId) {
                filters.licenseTypeId = effectiveLicenseTypeId;
            } else if (licenseTypeCode) {
                // If we have a license type code but no resolved ID yet, add it as a filter
                filters.licenseTypeCode = licenseTypeCode;
            }
            // Add status filter if selected
            if (statusFilter) {
                filters.status = statusFilter;
            }
            const params = {
                page: query.page,
                limit: query.limit,
                search: query.search || undefined,
                filters: Object.keys(filters).length > 0 ? filters : undefined
            };
            console.log('Loading applications with params:', params);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicationService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["applicationService"].getApplications(params);
            setApplicationsData(response);
        } catch (err) {
            console.error('Error loading applications:', err);
            setError('Failed to load applications');
            // Set empty data structure to prevent undefined errors
            setApplicationsData({
                data: [],
                meta: {
                    itemsPerPage: query.limit || 10,
                    totalItems: 0,
                    currentPage: query.page || 1,
                    totalPages: 0,
                    sortBy: [],
                    searchBy: [],
                    search: '',
                    select: []
                },
                links: {
                    current: ''
                }
            });
        } finally{
            setLoading(false);
        }
    }, [
        licenseTypeId,
        resolvedLicenseTypeId,
        selectedLicenseCategory,
        statusFilter,
        licenseTypeCode
    ]);
    // Load license types and resolve license type ID from code or filter name
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const fetchLicenseTypes = async ()=>{
            try {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$licenseTypeService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["licenseTypeService"].getAllLicenseTypes();
                const types = Array.isArray(response) ? response : response?.data || [];
                if (!Array.isArray(types)) {
                    console.warn('License types response is not an array:', types);
                    setLicenseTypes([]);
                    return;
                }
                setLicenseTypes(types);
                // Priority 1: If we have a licenseTypeCode, find the matching license type ID
                if (licenseTypeCode && types.length > 0) {
                    const matchingType = types.find((type)=>type.code === licenseTypeCode);
                    if (matchingType) {
                        setResolvedLicenseTypeId(matchingType.license_type_id);
                    }
                } else if (licenseTypeFilter && types.length > 0) {
                    const matchingType = types.find((type)=>type.name.toLowerCase().includes(licenseTypeFilter.toLowerCase()));
                    if (matchingType) {
                        setResolvedLicenseTypeId(matchingType.license_type_id);
                    }
                }
            } catch (error) {
                console.error('Error fetching license types:', error);
                setLicenseTypes([]);
            }
        };
        fetchLicenseTypes();
    }, [
        licenseTypeCode,
        licenseTypeFilter
    ]);
    // Fetch license categories for the dropdown
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const fetchLicenseCategories = async ()=>{
            try {
                let categories = [];
                const effectiveLicenseTypeId = licenseTypeId || resolvedLicenseTypeId;
                if (effectiveLicenseTypeId) {
                    // Fetch categories for specific license type
                    const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$licenseCategoryService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["licenseCategoryService"].getLicenseCategoriesByType(effectiveLicenseTypeId);
                    categories = Array.isArray(response) ? response : response?.data || [];
                } else {
                    // Fetch all categories
                    const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$licenseCategoryService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["licenseCategoryService"].getAllLicenseCategories();
                    categories = Array.isArray(response) ? response : response?.data || [];
                }
                if (!Array.isArray(categories)) {
                    console.warn('License categories response is not an array:', categories);
                    categories = [];
                }
                setLicenseCategories(categories);
            } catch (error) {
                console.error('Error fetching license categories:', error);
                setLicenseCategories([]);
            }
        };
        if (resolvedLicenseTypeId || licenseTypeId) {
            fetchLicenseCategories();
        }
    }, [
        licenseTypeId,
        resolvedLicenseTypeId
    ]);
    // Load applications on component mount and when filters change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (resolvedLicenseTypeId || licenseTypeId) {
            loadApplications({
                page: 1,
                limit: 10
            });
        }
    }, [
        loadApplications,
        resolvedLicenseTypeId,
        licenseTypeId
    ]);
    const getStatusBadge = (status)=>{
        const statusClasses = {
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApplicationStatus"].DRAFT]: 'bg-gray-100 text-gray-800',
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApplicationStatus"].SUBMITTED]: 'bg-blue-100 text-blue-800',
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApplicationStatus"].UNDER_REVIEW]: 'bg-yellow-100 text-yellow-800',
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApplicationStatus"].EVALUATION]: 'bg-purple-100 text-purple-800',
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApplicationStatus"].APPROVED]: 'bg-green-100 text-green-800',
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApplicationStatus"].REJECTED]: 'bg-red-100 text-red-800',
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApplicationStatus"].WITHDRAWN]: 'bg-gray-100 text-gray-800'
        };
        const statusLabels = {
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApplicationStatus"].DRAFT]: 'Draft',
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApplicationStatus"].SUBMITTED]: 'Submitted',
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApplicationStatus"].UNDER_REVIEW]: 'Under Review',
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApplicationStatus"].EVALUATION]: 'Evaluation',
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApplicationStatus"].APPROVED]: 'Approved',
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApplicationStatus"].REJECTED]: 'Rejected',
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApplicationStatus"].WITHDRAWN]: 'Withdrawn'
        };
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
            className: `px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClasses[status]}`,
            children: statusLabels[status]
        }, void 0, false, {
            fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
            lineNumber: 259,
            columnNumber: 7
        }, this);
    };
    // Define columns for applications table
    const applicationColumns = [
        {
            key: 'application_number',
            label: 'Application Number',
            sortable: true,
            render: (value)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-sm font-medium text-gray-900 dark:text-gray-100",
                    children: value
                }, void 0, false, {
                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                    lineNumber: 272,
                    columnNumber: 9
                }, this)
        },
        {
            key: 'applicant',
            label: 'Applicant',
            render: (value, application)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex-shrink-0 h-10 w-10",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                    className: "ri-building-line text-blue-600 dark:text-blue-400"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                                    lineNumber: 284,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                                lineNumber: 283,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                            lineNumber: 282,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "ml-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-sm font-medium text-gray-900 dark:text-gray-100",
                                    children: application.applicant?.name || 'N/A'
                                }, void 0, false, {
                                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                                    lineNumber: 288,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-sm text-gray-500 dark:text-gray-400",
                                    children: [
                                        "BRN: ",
                                        application.applicant?.business_registration_number || 'N/A',
                                        " | TPIN: ",
                                        application.applicant?.tpin || 'N/A'
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                                    lineNumber: 291,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                            lineNumber: 287,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                    lineNumber: 281,
                    columnNumber: 9
                }, this)
        },
        {
            key: 'license_category',
            label: 'License Category',
            render: (value, application)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "text-sm text-gray-900 dark:text-gray-100",
                            children: application.license_category?.name || 'N/A'
                        }, void 0, false, {
                            fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                            lineNumber: 304,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "text-sm text-gray-500 dark:text-gray-400",
                            children: application.license_category?.license_type?.name || 'N/A'
                        }, void 0, false, {
                            fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                            lineNumber: 307,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                    lineNumber: 303,
                    columnNumber: 9
                }, this)
        },
        {
            key: 'status',
            label: 'Status',
            render: (value)=>getStatusBadge(value)
        },
        {
            key: 'progress_percentage',
            label: 'Progress',
            render: (value)=>getProgressBar(value)
        },
        {
            key: 'submitted_at',
            label: 'Submitted Date',
            sortable: true,
            render: (value, application)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                    className: "text-sm text-gray-500 dark:text-gray-400",
                    children: application.submitted_at ? new Date(application.submitted_at).toLocaleDateString() : new Date(application.created_at).toLocaleDateString()
                }, void 0, false, {
                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                    lineNumber: 328,
                    columnNumber: 9
                }, this)
        },
        {
            key: 'actions',
            label: 'Actions',
            render: (value, application)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center justify-end space-x-2",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            type: "button",
                            onClick: ()=>handleViewApplication(application.application_id),
                            className: "inline-flex items-center px-3 py-1 text-xs font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/40 transition-colors",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                    className: "ri-eye-line mr-1"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                                    lineNumber: 346,
                                    columnNumber: 13
                                }, this),
                                "View"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                            lineNumber: 341,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$AssignButton$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            itemId: application.application_id,
                            itemType: "application",
                            itemTitle: application.application_number,
                            onAssignSuccess: handleAssignSuccess,
                            className: "text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 hover:bg-green-100 dark:hover:bg-green-900/40"
                        }, void 0, false, {
                            fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                            lineNumber: 349,
                            columnNumber: 11
                        }, this),
                        isAdmin && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                            children: [
                                application.status === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApplicationStatus"].SUBMITTED && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    type: "button",
                                    onClick: ()=>handleStatusUpdate(application.application_id, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApplicationStatus"].UNDER_REVIEW),
                                    className: "inline-flex items-center px-3 py-1 text-xs font-medium text-yellow-600 dark:text-yellow-400 hover:text-yellow-800 dark:hover:text-yellow-300 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md hover:bg-yellow-100 dark:hover:bg-yellow-900/40 transition-colors",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                            className: "ri-search-line mr-1"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                                            lineNumber: 364,
                                            columnNumber: 19
                                        }, this),
                                        "Review"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                                    lineNumber: 359,
                                    columnNumber: 17
                                }, this),
                                application.status === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApplicationStatus"].UNDER_REVIEW && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    type: "button",
                                    onClick: ()=>handleStatusUpdate(application.application_id, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApplicationStatus"].EVALUATION),
                                    className: "inline-flex items-center px-3 py-1 text-xs font-medium text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-md hover:bg-purple-100 dark:hover:bg-purple-900/40 transition-colors",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                            className: "ri-clipboard-line mr-1"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                                            lineNumber: 374,
                                            columnNumber: 19
                                        }, this),
                                        "Evaluate"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                                    lineNumber: 369,
                                    columnNumber: 17
                                }, this),
                                application.status === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApplicationStatus"].EVALUATION && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            type: "button",
                                            onClick: ()=>handleStatusUpdate(application.application_id, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApplicationStatus"].APPROVED),
                                            className: "inline-flex items-center px-3 py-1 text-xs font-medium text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md hover:bg-green-100 dark:hover:bg-green-900/40 transition-colors",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                    className: "ri-check-line mr-1"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                                                    lineNumber: 385,
                                                    columnNumber: 21
                                                }, this),
                                                "Approve"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                                            lineNumber: 380,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            type: "button",
                                            onClick: ()=>handleStatusUpdate(application.application_id, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApplicationStatus"].REJECTED),
                                            className: "inline-flex items-center px-3 py-1 text-xs font-medium text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md hover:bg-red-100 dark:hover:bg-red-900/40 transition-colors",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                    className: "ri-close-line mr-1"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                                                    lineNumber: 393,
                                                    columnNumber: 21
                                                }, this),
                                                "Reject"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                                            lineNumber: 388,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true)
                            ]
                        }, void 0, true)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                    lineNumber: 340,
                    columnNumber: 9
                }, this)
        }
    ];
    const getProgressBar = (percentage)=>{
        const getProgressColor = (percent)=>{
            if (percent >= 100) return 'bg-green-600';
            if (percent >= 75) return 'bg-blue-600';
            if (percent >= 50) return 'bg-yellow-600';
            if (percent >= 25) return 'bg-orange-600';
            return 'bg-red-600';
        };
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "w-16 bg-gray-200 rounded-full h-2 mr-2",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: `h-2 rounded-full ${getProgressColor(percentage)}`,
                        style: {
                            width: `${percentage}%`
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                        lineNumber: 417,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                    lineNumber: 416,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                    className: "text-sm text-gray-500",
                    children: [
                        percentage,
                        "%"
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                    lineNumber: 422,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
            lineNumber: 415,
            columnNumber: 7
        }, this);
    };
    const handleStatusUpdate = async (applicationId, newStatus)=>{
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicationService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["applicationService"].updateApplicationStatus(applicationId, newStatus);
            // Refresh the applications list
            loadApplications({
                page: 1,
                limit: 10
            });
        } catch (error) {
            console.error('Error updating application status:', error);
        }
    };
    const handleAssignSuccess = ()=>{
        // Refresh the applications list when assignment is successful
        loadApplications({
            page: 1,
            limit: 10
        });
    };
    const handleFilterChange = (filterType, value)=>{
        switch(filterType){
            case 'licenseCategory':
                setSelectedLicenseCategory(value);
                break;
            case 'status':
                setStatusFilter(value);
                break;
            case 'dateRange':
                setDateRangeFilter(value);
                break;
        }
        // Reload applications with new filters
        loadApplications({
            page: 1,
            limit: 10
        });
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-6",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "max-w-7xl mx-auto",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mb-8",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-between",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                    className: "text-3xl font-bold text-gray-900 dark:text-gray-100",
                                    children: title
                                }, void 0, false, {
                                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                                    lineNumber: 465,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "mt-2 text-sm text-gray-600 dark:text-gray-400",
                                    children: description
                                }, void 0, false, {
                                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                                    lineNumber: 466,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                            lineNumber: 464,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                        lineNumber: 463,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                    lineNumber: 462,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                            className: "text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4",
                            children: "Filters"
                        }, void 0, false, {
                            fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                            lineNumber: 475,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Select$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    label: "License Category",
                                    value: selectedLicenseCategory,
                                    onChange: (value)=>handleFilterChange('licenseCategory', value),
                                    options: [
                                        {
                                            value: '',
                                            label: 'All Categories'
                                        },
                                        ...licenseCategories.map((category)=>({
                                                value: category.license_category_id,
                                                label: category.name
                                            }))
                                    ]
                                }, void 0, false, {
                                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                                    lineNumber: 478,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Select$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    label: "Application Status",
                                    value: statusFilter,
                                    onChange: (value)=>handleFilterChange('status', value),
                                    options: [
                                        {
                                            value: '',
                                            label: 'All Statuses'
                                        },
                                        {
                                            value: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApplicationStatus"].DRAFT,
                                            label: 'Draft'
                                        },
                                        {
                                            value: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApplicationStatus"].SUBMITTED,
                                            label: 'Submitted'
                                        },
                                        {
                                            value: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApplicationStatus"].UNDER_REVIEW,
                                            label: 'Under Review'
                                        },
                                        {
                                            value: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApplicationStatus"].EVALUATION,
                                            label: 'Evaluation'
                                        },
                                        {
                                            value: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApplicationStatus"].APPROVED,
                                            label: 'Approved'
                                        },
                                        {
                                            value: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApplicationStatus"].REJECTED,
                                            label: 'Rejected'
                                        },
                                        {
                                            value: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApplicationStatus"].WITHDRAWN,
                                            label: 'Withdrawn'
                                        }
                                    ]
                                }, void 0, false, {
                                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                                    lineNumber: 492,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Select$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    label: "Date Range",
                                    value: dateRangeFilter,
                                    onChange: (value)=>handleFilterChange('dateRange', value),
                                    options: [
                                        {
                                            value: '',
                                            label: 'All Time'
                                        },
                                        {
                                            value: 'last-30',
                                            label: 'Last 30 Days'
                                        },
                                        {
                                            value: 'last-90',
                                            label: 'Last 90 Days'
                                        },
                                        {
                                            value: 'last-year',
                                            label: 'Last Year'
                                        }
                                    ]
                                }, void 0, false, {
                                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                                    lineNumber: 509,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                            lineNumber: 476,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                    lineNumber: 474,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$DataTable$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                    columns: applicationColumns,
                    data: applicationsData,
                    loading: loading,
                    onQueryChange: loadApplications,
                    searchPlaceholder: searchPlaceholder
                }, void 0, false, {
                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                    lineNumber: 526,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
            lineNumber: 460,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
        lineNumber: 459,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/app/applications/[license-type]/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>LicenseTypeApplicationsPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$license$2f$LicenseManagementTable$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/license/LicenseManagementTable.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$licenseTypeService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/licenseTypeService.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
function LicenseTypeApplicationsPage() {
    const params = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useParams"])();
    const licenseTypeCode = params['license-type'];
    const [licenseType, setLicenseType] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    // Load required documents and existing uploads
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const fetchLicenseType = async ()=>{
            try {
                const licenseTypeResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$licenseTypeService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["licenseTypeService"].getLicenseTypeByCode(licenseTypeCode);
                setLicenseType(licenseTypeResponse);
            } catch (error) {
                console.error('Error fetching license type:', error);
            }
        };
        if (licenseTypeCode) {
            fetchLicenseType();
        }
    }, [
        licenseTypeCode
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$license$2f$LicenseManagementTable$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
        licenseTypeCode: licenseTypeCode,
        title: `${licenseType?.name || 'License'} Management`,
        description: licenseType?.description || licenseType?.name || 'License management',
        searchPlaceholder: `Search ${licenseType?.name || 'license'} applications...`,
        emptyStateIcon: 'ri-mail-line',
        emptyStateMessage: 'No applications are available.',
        departmentType: licenseType?.name
    }, void 0, false, {
        fileName: "[project]/src/app/applications/[license-type]/page.tsx",
        lineNumber: 31,
        columnNumber: 5
    }, this);
}
}}),

};

//# sourceMappingURL=src_19a992d1._.js.map