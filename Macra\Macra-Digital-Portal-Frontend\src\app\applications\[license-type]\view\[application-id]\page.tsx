'use client';

import { useParams, useRouter } from 'next/navigation';
import ApplicationViewPage from '../../../../../components/applications/ApplicationViewPage';

export default function ViewApplicationPage() {
  const params = useParams();
  const router = useRouter();
  const licenseType = params['license-type'] as string;
  const applicationId = params['application-id'] as string;

  const handleBack = () => {
    router.push(`/applications/${licenseType}`);
  };

  return (
    <ApplicationViewPage
      applicationId={applicationId}
      departmentType={licenseType}
      onBack={handleBack}
    />
  );
}
