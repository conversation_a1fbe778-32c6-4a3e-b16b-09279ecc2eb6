import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Documents, DocumentType } from '../entities/documents.entity';
import { CreateDocumentDto } from '../dto/document/create-document.dto';
import { UpdateDocumentDto } from '../dto/document/update-document.dto';
import { PaginateQuery, Paginated, PaginateConfig, paginate } from 'nestjs-paginate';
import { createReadStream } from 'fs';
import { join } from 'path';

@Injectable()
export class DocumentsService {
  constructor(
    @InjectRepository(Documents)
    private documentsRepository: Repository<Documents>,
  ) {}

  private readonly paginateConfig: PaginateConfig<Documents> = {
    sortableColumns: ['created_at', 'updated_at', 'file_name', 'document_type'],
    searchableColumns: ['file_name', 'document_type', 'entity_type'],
    defaultSortBy: [['created_at', 'DESC']],
    defaultLimit: 10,
    maxLimit: 100,
    relations: ['creator', 'updater'],
  };

  async create(createDocumentDto: CreateDocumentDto, createdBy: string): Promise<Documents> {
    const document = this.documentsRepository.create({
      ...createDocumentDto,
      is_required: createDocumentDto.is_required || false,
      created_by: createdBy,
    });

    return this.documentsRepository.save(document);
  }

  async findAll(query: PaginateQuery, userRoles?: string[], userId?: string): Promise<Paginated<Documents>> {
    // Check if user is a customer (has customer role)
    const isCustomer = userRoles?.includes('customer');

    // If user is a customer, filter by their uploaded documents only
    if (isCustomer && userId) {
      const customerQuery: PaginateQuery = {
        ...query,
        filter: {
          ...query.filter,
          created_by: userId
        }
      };
      return paginate(customerQuery, this.documentsRepository, this.paginateConfig);
    }

    // For staff users, show all documents
    return paginate(query, this.documentsRepository, this.paginateConfig);
  }

  async findOne(id: string): Promise<Documents> {
    const document = await this.documentsRepository.findOne({
      where: { document_id: id },
      relations: ['creator', 'updater'],
    });

    if (!document) {
      throw new NotFoundException(`Document with ID ${id} not found`);
    }

    return document;
  }

  async findByApplication(applicationId: string): Promise<Documents[]> {
    return this.documentsRepository.find({
      where: { entity_type: 'application', entity_id: applicationId },
      relations: ['creator', 'updater'],
      order: { created_at: 'DESC' },
    });
  }

  async findByEntity(entityType: string, entityId: string): Promise<Documents[]> {
    return this.documentsRepository.find({
      where: { entity_type: entityType, entity_id: entityId },
      relations: ['creator', 'updater'],
      order: { created_at: 'DESC' },
    });
  }

  async findByDocumentType(documentType: DocumentType): Promise<Documents[]> {
    return this.documentsRepository.find({
      where: { document_type: documentType },
      relations: ['creator', 'updater'],
      order: { created_at: 'DESC' },
    });
  }

  async findRequiredDocuments(): Promise<Documents[]> {
    return this.documentsRepository.find({
      where: { is_required: true },
      relations: ['creator', 'updater'],
      order: { created_at: 'DESC' },
    });
  }

  async update(id: string, updateDocumentDto: UpdateDocumentDto, updatedBy: string): Promise<Documents> {
    const document = await this.findOne(id);

    Object.assign(document, updateDocumentDto, { updated_by: updatedBy });
    return this.documentsRepository.save(document);
  }

  async remove(id: string): Promise<void> {
    const document = await this.findOne(id);
    await this.documentsRepository.softDelete(document.document_id);
  }

  async getDocumentStats(): Promise<any> {
    const stats = await this.documentsRepository
      .createQueryBuilder('document')
      .select('document.document_type', 'document_type')
      .addSelect('COUNT(*)', 'count')
      .groupBy('document.document_type')
      .getRawMany();

    return stats.reduce((acc, stat) => {
      acc[stat.document_type] = parseInt(stat.count);
      return acc;
    }, {});
  }

  async getDocumentsByMimeType(mimeType: string): Promise<Documents[]> {
    return this.documentsRepository.find({
      where: { mime_type: mimeType },
      relations: ['creator', 'updater'],
      order: { created_at: 'DESC' },
    });
  }

  async getTotalFileSize(): Promise<number> {
    const result = await this.documentsRepository
      .createQueryBuilder('document')
      .select('SUM(document.file_size)', 'total_size')
      .getRawOne();

    return parseInt(result.total_size) || 0;
  }

  async getFileStream(filePath: string) {
    try {
      // Construct the full file path
      const fullPath = join(process.cwd(), filePath);
      return createReadStream(fullPath);
    } catch (error) {
      throw new NotFoundException(`File not found: ${filePath}`);
    }
  }
}
