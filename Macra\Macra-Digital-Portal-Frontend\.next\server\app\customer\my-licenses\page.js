(()=>{var e={};e.id=3913,e.ids=[3913],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},16879:(e,t,r)=>{Promise.resolve().then(r.bind(r,82583))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},51521:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\my-licenses\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\my-licenses\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78637:(e,t,r)=>{"use strict";r.d(t,{k:()=>i});var a=r(51278),s=r(72901);let i={async getApplications(e){let t=new URLSearchParams;e?.page&&t.append("page",e.page.toString()),e?.limit&&t.append("limit",e.limit.toString()),e?.search&&t.append("search",e.search),e?.sortBy&&t.append("sortBy",e.sortBy),e?.sortOrder&&t.append("sortOrder",e.sortOrder),e?.filters?.licenseTypeId&&t.append("filter.license_category.license_type_id",e.filters.licenseTypeId),e?.filters?.licenseCategoryId&&t.append("filter.license_category_id",e.filters.licenseCategoryId),e?.filters?.status&&t.append("filter.status",e.filters.status);let r=await s.uE.get(`/applications?${t.toString()}`);return(0,a.zp)(r)},async getApplicationsByLicenseType(e,t){let r=new URLSearchParams;t?.page&&r.append("page",t.page.toString()),t?.limit&&r.append("limit",t.limit.toString()),t?.search&&r.append("search",t.search),t?.status&&r.append("filter.status",t.status),r.append("filter.license_category.license_type_id",e);let i=await s.uE.get(`/applications?${r.toString()}`);return(0,a.zp)(i)},async getApplication(e){let t=await s.uE.get(`/applications/${e}`);return(0,a.zp)(t)},async getApplicationsByApplicant(e){let t=await s.uE.get(`/applications/by-applicant/${e}`);return(0,a.zp)(t)},async getApplicationsByStatus(e){let t=await s.uE.get(`/applications/by-status/${e}`);return(0,a.zp)(t)},async updateApplicationStatus(e,t){let r=await s.uE.put(`/applications/${e}/status?status=${t}`);return(0,a.zp)(r)},async updateApplicationProgress(e,t,r){let i=await s.uE.put(`/applications/${e}/progress?currentStep=${t}&progressPercentage=${r}`);return(0,a.zp)(i)},async getApplicationStats(){let e=await s.uE.get("/applications/stats");return(0,a.zp)(e)},async createApplication(e){try{let t=await s.uE.post("/applications",e);return(0,a.zp)(t)}catch(e){throw e}},async updateApplication(e,t){let r=await s.uE.put(`/applications/${e}`,t);return(0,a.zp)(r)},async deleteApplication(e){let t=await s.uE.delete(`/applications/${e}`);return(0,a.zp)(t)},async createApplicationWithApplicant(e){try{let t=new Date,r=t.toISOString().slice(0,10).replace(/-/g,""),a=t.toTimeString().slice(0,8).replace(/:/g,""),s=Math.random().toString(36).substr(2,3).toUpperCase(),i=`APP-${r}-${a}-${s}`;if(!/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e.user_id))throw Error(`Invalid user_id format: ${e.user_id}. Expected UUID format.`);return await this.createApplication({application_number:i,applicant_id:e.user_id,license_category_id:e.license_category_id,current_step:1,progress_percentage:0})}catch(e){throw e}},async saveApplicationSection(e,t,r){try{let r=1,a=["applicantInfo","companyProfile","businessInfo","serviceScope","businessPlan","legalHistory","reviewSubmit"].indexOf(t);r=a>=0?a+1:1;let s=Math.min(Math.round(r/6*100),100);await this.updateApplication(e,{progress_percentage:s,current_step:r})}catch(e){throw e}},async getApplicationSection(e,t){try{let r=await s.uE.get(`/applications/${e}/sections/${t}`);return(0,a.zp)(r)}catch(e){throw e}},async submitApplication(e){try{let t=await s.uE.put(`/applications/${e}`,{status:"submitted",submitted_at:new Date().toISOString(),progress_percentage:100,current_step:7});return(0,a.zp)(t)}catch(e){throw e}},async getUserApplications(){try{let e=await s.uE.get("/applications/user-applications"),t=(0,a.zp)(e),r=[];return t?.data?r=Array.isArray(t.data)?t.data:[]:Array.isArray(t)?r=t:t&&(r=[t]),r}catch(e){throw e}},async saveAsDraft(e,t){try{let r=await s.uE.put(`/applications/${e}`,{form_data:t,status:"draft"});return(0,a.zp)(r)}catch(e){throw e}},async validateApplication(e){try{let e={},t=[];for(let r of["applicantInfo","companyProfile","businessInfo","legalHistory"])e[r]&&0!==Object.keys(e[r]).length||t.push(`${r} section is incomplete`);return{isValid:0===t.length,errors:t}}catch(e){throw e}}}},79551:e=>{"use strict";e.exports=require("url")},80087:(e,t,r)=>{Promise.resolve().then(r.bind(r,51521))},81630:e=>{"use strict";e.exports=require("http")},82583:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var a=r(60687),s=r(43210),i=r(16189),n=r(94391),l=r(63213),c=r(78637);let o=({isOpen:e,onClose:t,application:r,onContinueApplication:s,canContinueApplication:i})=>{if(!e||!r)return null;let n=e=>{let t=r.current_step;return"rejected"===r.status?1===e?"complete":"error":"approved"===r.status?"complete":e<=t?e===t?"current":"complete":"upcoming"};return(0,a.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,a.jsxs)("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:t}),(0,a.jsxs)("div",{className:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:[(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Application Status"}),(0,a.jsx)("button",{type:"button",title:"Close modal",onClick:t,className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:(0,a.jsx)("i",{className:"ri-close-line text-xl"})})]}),(0,a.jsxs)("div",{className:"mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 dark:text-gray-100",children:r.application_number}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:r.license_category?.name||"License Category"})]}),(0,a.jsxs)("div",{className:"relative mb-6",children:[(0,a.jsx)("div",{className:"absolute top-6 left-6 right-6 h-0.5 bg-gray-200 dark:bg-gray-600",children:(0,a.jsx)("div",{className:`h-full bg-primary transition-all duration-500 ${"approved"===r.status||4===r.current_step?"w-full":3===r.current_step?"w-2/3":2===r.current_step?"w-1/3":(r.current_step,"w-0")}`})}),(0,a.jsx)("div",{className:"relative flex justify-between",children:[{id:1,name:"Draft",description:"Application incomplete",icon:"ri-file-text-line"},{id:2,name:"Submitted",description:"Application received and logged",icon:"ri-file-text-line"},{id:3,name:"Under Review",description:"Being reviewed by MACRA team",icon:"ri-search-line"},{id:4,name:"Evaluation",description:"Technical evaluation in progress",icon:"ri-clipboard-line"},{id:5,name:"Approved",description:"License approved and issued",icon:"ri-check-line"}].map(e=>{let t=n(e.id);return(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)("div",{className:`
                        w-12 h-12 rounded-full flex items-center justify-center text-sm font-medium border-2 transition-all duration-300
                        ${"complete"===t?"bg-primary border-primary text-white":"current"===t?"bg-primary border-primary text-white animate-pulse":"error"===t?"bg-red-500 border-red-500 text-white":"bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-400"}
                      `,children:(0,a.jsx)("i",{className:e.icon})}),(0,a.jsxs)("div",{className:"mt-3 text-center",children:[(0,a.jsx)("div",{className:`text-sm font-medium ${"complete"===t||"current"===t?"text-gray-900 dark:text-gray-100":"text-gray-500 dark:text-gray-400"}`,children:e.name}),(0,a.jsx)("div",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1 max-w-20",children:e.description})]})]},e.id)})})]}),(0,a.jsx)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"Current Status"}),(0,a.jsx)("p",{className:"font-medium text-gray-900 dark:text-gray-100",children:r.status.replace("_"," ").toUpperCase()})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"Progress"}),(0,a.jsxs)("p",{className:"font-medium text-gray-900 dark:text-gray-100",children:[r.progress_percentage||0,"% Complete"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"Submitted"}),(0,a.jsx)("p",{className:"font-medium text-gray-900 dark:text-gray-100",children:r.submitted_at?new Date(r.submitted_at).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}):new Date(r.created_at).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"Estimated Time"}),(0,a.jsx)("p",{className:"font-medium text-gray-900 dark:text-gray-100",children:"30-45 business days"})]})]})})]}),(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,a.jsx)("button",{type:"button",onClick:t,className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm",children:"Close"}),r&&i&&s&&i(r)&&(0,a.jsxs)("button",{type:"button",onClick:()=>{s(r),t()},className:"w-full inline-flex justify-center rounded-md border border-blue-300 shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mr-3 sm:w-auto sm:text-sm",children:[(0,a.jsx)("i",{className:"ri-edit-line mr-2"}),"Continue Application"]})]})]})]})})},d=()=>{let{isAuthenticated:e,user:t}=(0,l.A)(),r=(0,i.useRouter)(),d=(0,i.useSearchParams)(),[p,u]=(0,s.useState)([]),[x,m]=(0,s.useState)(!0),[g,y]=(0,s.useState)(null),[h,b]=(0,s.useState)("all"),[f,j]=(0,s.useState)(null),[v,w]=(0,s.useState)(!1),[N,k]=(0,s.useState)(!1);(0,s.useEffect)(()=>{if("true"===d.get("submitted")){k(!0);let e=new URL(window.location.href);e.searchParams.delete("submitted"),window.history.replaceState({},"",e.toString()),setTimeout(()=>k(!1),5e3)}},[d]);let _=[{label:"Dashboard",href:"/customer"},{label:"My Licenses",href:"/customer/my-licenses"}],A=e=>{j(e),w(!0)},S=async e=>{if(y(null),!e||!e.license_category_id)return void y("Unable to continue application: License category information is missing. Please contact support.");let t=`/customer/applications/apply/applicant-info?license_category_id=${e.license_category_id}&application_id=${e.application_id}`;r.push(t)},P=e=>["draft"].includes(e.status),C=(0,s.useCallback)(async()=>{m(!0),y(null);try{let e=await c.k.getUserApplications(),t=(Array.isArray(e)?e:[]).map(e=>({...e,status:e.status||"draft",progress_percentage:e.progress_percentage||0,current_step:e.current_step||1,application_number:e.application_number||`APP-${e.application_id?.slice(0,8)}`,license_category:e.license_category?{...e.license_category,name:e.license_category.name||"License Category",description:e.license_category.description||"Category description"}:{name:"License Category",description:"Category description"}}));t.length>0&&t.reduce((e,t)=>(e[t.status]=(e[t.status]||0)+1,e),{}),u(t)}catch(e){if(e.response?.status===404)u([]),y(null);else if(e.response?.status===401){y("Authentication required. Please log in again."),r.push("/customer/auth/login");return}else y(e.response?.data?.message||e.message||"Failed to fetch applications")}finally{m(!1)}},[r,t]);if((0,s.useEffect)(()=>{C()},[r,C]),x)return(0,a.jsx)(n.A,{breadcrumbs:_,children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading your licenses..."})]})})});if(g)return(0,a.jsx)(n.A,{breadcrumbs:_,children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-red-600 dark:text-red-400 mb-4",children:(0,a.jsx)("i",{className:"ri-error-warning-line text-4xl"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Failed to load applications"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:g}),(0,a.jsx)("button",{type:"button",onClick:()=>{setTimeout(()=>C(),500)},className:"bg-primary text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors",children:"Try Again"})]})})});let $=p.filter(e=>"all"===h||("in_progress"===h?P(e):e.status===h));[...new Set(p.map(e=>e.status))];let E=e=>{let t={draft:{color:"bg-blue-100 text-blue-800",label:"Draft"},submitted:{color:"bg-blue-100 text-blue-800",label:"Submitted"},under_review:{color:"bg-yellow-100 text-yellow-800",label:"Under Review"},evaluation:{color:"bg-purple-100 text-purple-800",label:"Evaluation"},approved:{color:"bg-green-100 text-green-800",label:"Approved"},rejected:{color:"bg-red-100 text-red-800",label:"Rejected"}},r=t[e]||t.submitted;return(0,a.jsx)("span",{className:`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${r.color}`,children:r.label})};return(0,a.jsxs)(n.A,{breadcrumbs:_,children:[(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-gray-100",children:"My Licenses"}),(0,a.jsx)("p",{className:"mt-2 text-gray-600 dark:text-gray-400",children:"Track your license applications and manage your approved licenses."})]})}),N&&(0,a.jsx)("div",{className:"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"ri-check-circle-line text-green-600 dark:text-green-400 text-xl mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-green-800 dark:text-green-200",children:"Application Submitted Successfully!"}),(0,a.jsx)("p",{className:"text-sm text-green-700 dark:text-green-300 mt-1",children:"Your license application has been submitted and is now under review. You will receive email notifications about status updates."})]}),(0,a.jsx)("button",{onClick:()=>k(!1),className:"ml-auto text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200",children:(0,a.jsx)("i",{className:"ri-close-line text-lg"})})]})}),(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4",children:"Filter Applications"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:[{key:"all",label:"All Applications"},{key:"in_progress",label:"In Progress"},{key:"submitted",label:"Submitted"},{key:"under_review",label:"Under Review"},{key:"evaluation",label:"Evaluation"},{key:"approved",label:"Approved"},{key:"rejected",label:"Rejected"},{key:"draft",label:"Draft"}].map(e=>(0,a.jsx)("button",{type:"button",onClick:()=>b(e.key),className:`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${h===e.key?"bg-primary text-white":"bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"}`,children:e.label},e.key))})]}),(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700",children:[(0,a.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-700",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:"Applications Overview"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:"View and track all your license applications"})]}),0===$.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-gray-400 dark:text-gray-500 mb-4",children:(0,a.jsx)("i",{className:"ri-file-list-line text-4xl"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"No applications found"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"all"===h?"You haven't submitted any license applications yet.":`No applications with status "${h.replace("_"," ")}" found.`})]}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[(0,a.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Application Details"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"License Category"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Progress"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Submitted"}),(0,a.jsx)("th",{scope:"col",className:"relative px-6 py-3",children:(0,a.jsx)("span",{className:"sr-only",children:"Actions"})})]})}),(0,a.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:$.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-3",children:(0,a.jsx)("i",{className:"ri-file-text-line text-blue-600 dark:text-blue-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:e.application_number}),(0,a.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Application ID: ",e.application_id.slice(0,8),"..."]})]})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:e.license_category?.name||"License Category"}),(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:e.license_category?.description||"Category description"})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:E(e.status)}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2 relative overflow-hidden",children:(0,a.jsx)("div",{className:`${P(e)?"bg-blue-500":"bg-primary"} h-2 rounded-full transition-all duration-300 absolute top-0 left-0 ${(e.progress_percentage||0)>=100?"w-full":(e.progress_percentage||0)>=75?"w-3/4":(e.progress_percentage||0)>=50?"w-1/2":(e.progress_percentage||0)>=25?"w-1/4":(e.progress_percentage||0)>0?"w-1/12":"w-0"}`})}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:[e.progress_percentage||0,"%"]}),P(e)&&(0,a.jsx)("span",{className:"text-xs text-blue-600 dark:text-blue-400 font-medium",children:"In Progress"})]})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:e.submitted_at?new Date(e.submitted_at).toLocaleDateString():new Date(e.created_at).toLocaleDateString()}),(0,a.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Step ",e.current_step," of 6"]})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex items-center justify-end space-x-2",children:[P(e)&&(0,a.jsxs)("button",{type:"button",title:"Continue working on this application",onClick:()=>S(e),className:"inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full hover:bg-blue-200 transition-colors",children:[(0,a.jsx)("i",{className:"ri-edit-line"}),"View Continue"]}),(0,a.jsxs)("button",{type:"button",title:"View application status",onClick:()=>A(e),className:"text-primary hover:text-red-700 transition-colors",children:[(0,a.jsx)("i",{className:"ri-eye-line"})," View"]}),"approved"===e.status&&(0,a.jsx)("button",{type:"button",title:"Download license",className:"text-primary hover:text-red-700 transition-colors",children:(0,a.jsx)("i",{className:"ri-download-line"})})]})})]},e.application_id))})]})})]})]}),(0,a.jsx)(o,{isOpen:v,onClose:()=>{j(null),w(!1)},application:f,onContinueApplication:S,canContinueApplication:P})]})}},83997:e=>{"use strict";e.exports=require("tty")},90065:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>o});var a=r(65239),s=r(48088),i=r(88170),n=r.n(i),l=r(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);r.d(t,c);let o={children:["",{children:["customer",{children:["my-licenses",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,51521)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\my-licenses\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,68736)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\my-licenses\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/customer/my-licenses/page",pathname:"/customer/my-licenses",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,7498,1658,5814,7563,6893],()=>r(90065));module.exports=a})();