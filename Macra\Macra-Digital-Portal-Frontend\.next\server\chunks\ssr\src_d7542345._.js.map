{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/LogoutButton.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { useRouter, usePathname } from 'next/navigation';\r\nimport { useAuth } from '../contexts/AuthContext';\r\n\r\ninterface LogoutButtonProps {\r\n  variant?: 'primary' | 'secondary' | 'text' | 'icon';\r\n  size?: 'sm' | 'md' | 'lg';\r\n  className?: string;\r\n  showConfirmation?: boolean;\r\n  redirectTo?: string;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nexport default function LogoutButton({\r\n  variant = 'primary',\r\n  size = 'md',\r\n  className = '',\r\n  showConfirmation = true,\r\n  redirectTo = '/auth/login',\r\n  children,\r\n}: LogoutButtonProps) {\r\n  const [isLoggingOut, setIsLoggingOut] = useState(false);\r\n  const [showConfirm, setShowConfirm] = useState(false);\r\n  const { logout, user } = useAuth();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  const handleLogout = async () => {\r\n    if (showConfirmation && !showConfirm) {\r\n      setShowConfirm(true);\r\n      return;\r\n    }\r\n\r\n    setIsLoggingOut(true);\r\n    \r\n    try {\r\n      // Call logout from context\r\n      logout();\r\n      \r\n      // Small delay to ensure cleanup is complete\r\n      await new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n      if(pathname.includes('customer')){\r\n        // Redirect to specified page\r\n        router.push('/customer/auth/login');\r\n      } else {\r\n        router.push('/auth/login');\r\n      }\r\n      \r\n\r\n      \r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n    } finally {\r\n      setIsLoggingOut(false);\r\n      setShowConfirm(false);\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    setShowConfirm(false);\r\n  };\r\n\r\n  // Base styles for different variants\r\n  const getVariantStyles = () => {\r\n    switch (variant) {\r\n      case 'primary':\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n      case 'secondary':\r\n        return 'bg-white hover:bg-gray-50 text-red-600 border border-red-600';\r\n      case 'text':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none';\r\n      case 'icon':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none p-2';\r\n      default:\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n    }\r\n  };\r\n\r\n  // Size styles\r\n  const getSizeStyles = () => {\r\n    switch (size) {\r\n      case 'sm':\r\n        return 'px-3 py-1.5 text-sm';\r\n      case 'md':\r\n        return 'px-4 py-2 text-base';\r\n      case 'lg':\r\n        return 'px-6 py-3 text-lg';\r\n      default:\r\n        return 'px-4 py-2 text-base';\r\n    }\r\n  };\r\n\r\n  const baseStyles = 'inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\r\n  \r\n  const buttonStyles = `${baseStyles} ${getVariantStyles()} ${variant !== 'icon' ? getSizeStyles() : ''} ${className}`;\r\n\r\n  // Confirmation dialog\r\n  if (showConfirm) {\r\n    return (\r\n      <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n        <div className=\"bg-white rounded-lg p-6 max-w-sm mx-4 shadow-xl\">\r\n          <div className=\"flex items-center mb-4\">\r\n            <div className=\"flex-shrink-0\">\r\n              <svg className=\"h-6 w-6 text-red-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z\" />\r\n              </svg>\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <h3 className=\"text-lg font-medium text-gray-900\">Confirm Logout</h3>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"mb-4\">\r\n            <p className=\"text-sm text-gray-500\">\r\n              Are you sure you want to logout{user?.first_name ? `, ${user.first_name}` : ''}? You will need to login again to access your account.\r\n            </p>\r\n          </div>\r\n          \r\n          <div className=\"flex space-x-3\">\r\n            <button\r\n              onClick={handleLogout}\r\n              disabled={isLoggingOut}\r\n              className=\"flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 disabled:opacity-50\"\r\n            >\r\n              {isLoggingOut ? 'Logging out...' : 'Yes, Logout'}\r\n            </button>\r\n            <button\r\n              onClick={handleCancel}\r\n              className=\"flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200\"\r\n            >\r\n              Cancel\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <button\r\n      onClick={handleLogout}\r\n      disabled={isLoggingOut}\r\n      className={buttonStyles}\r\n      title=\"Logout\"\r\n    >\r\n      {children || (\r\n        <>\r\n          {variant === 'icon' ? (\r\n            <svg className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n            </svg>\r\n          ) : (\r\n            <>\r\n              <svg className=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n              </svg>\r\n              {isLoggingOut ? 'Logging out...' : 'Logout'}\r\n            </>\r\n          )}\r\n        </>\r\n      )}\r\n    </button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAee,SAAS,aAAa,EACnC,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,mBAAmB,IAAI,EACvB,aAAa,aAAa,EAC1B,QAAQ,EACU;IAClB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe;QACnB,IAAI,oBAAoB,CAAC,aAAa;YACpC,eAAe;YACf;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,2BAA2B;YAC3B;YAEA,4CAA4C;YAC5C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,IAAG,SAAS,QAAQ,CAAC,aAAY;gBAC/B,6BAA6B;gBAC7B,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QAIF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,gBAAgB;YAChB,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,eAAe;IACjB;IAEA,qCAAqC;IACrC,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,cAAc;IACd,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa;IAEnB,MAAM,eAAe,GAAG,WAAW,CAAC,EAAE,mBAAmB,CAAC,EAAE,YAAY,SAAS,kBAAkB,GAAG,CAAC,EAAE,WAAW;IAEpH,sBAAsB;IACtB,IAAI,aAAa;QACf,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAAuB,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CAC3E,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;;;;;;;;;;;;kCAItD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;gCAAwB;gCACH,MAAM,aAAa,CAAC,EAAE,EAAE,KAAK,UAAU,EAAE,GAAG;gCAAG;;;;;;;;;;;;kCAInF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,eAAe,mBAAmB;;;;;;0CAErC,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,UAAU;QACV,WAAW;QACX,OAAM;kBAEL,0BACC;sBACG,YAAY,uBACX,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,SAAQ;gBAAY,QAAO;0BAC9D,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;qCAGvE;;kCACE,8OAAC;wBAAI,WAAU;wBAAe,MAAK;wBAAO,SAAQ;wBAAY,QAAO;kCACnE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAEtE,eAAe,mBAAmB;;;;;;;;;AAOjD", "debugId": null}}, {"offset": {"line": 262, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/types/license.ts"], "sourcesContent": ["export interface LicenseType {\r\n  license_type_id: string;\r\n  name: string;\r\n  code?: string;\r\n  description?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface LicenseCategory {\r\n  license_category_id: string;\r\n  name: string;\r\n  description?: string;\r\n  license_type_id: string;\r\n  license_type?: LicenseType;\r\n  parent_id?: string;\r\n  parent?: LicenseCategory;\r\n  children?: LicenseCategory[];\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface Applicant {\r\n  applicant_id: string;\r\n  name: string;\r\n  business_registration_number: string;\r\n  tpin: string;\r\n  website: string;\r\n  email: string;\r\n  phone: string;\r\n  fax?: string;\r\n  level_of_insurance_cover?: string;\r\n  address_id?: string;\r\n  contact_id?: string;\r\n  date_incorporation: string;\r\n  place_incorporation: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface Application {\r\n  application_id: string;\r\n  application_number: string;\r\n  applicant_id: string;\r\n  license_category_id: string;\r\n  status: ApplicationStatus;\r\n  current_step: number;\r\n  progress_percentage: number;\r\n  submitted_at?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  assigned_to?: string;\r\n  assigned_at?: string;\r\n  application_data?: any;\r\n  applicant?: {\r\n    applicant_id: string;\r\n    name: string;\r\n    business_registration_number: string;\r\n    tpin: string;\r\n    website: string;\r\n    email: string;\r\n    phone: string;\r\n    fax?: string;\r\n    level_of_insurance_cover?: string;\r\n    date_incorporation: string;\r\n    place_incorporation: string;\r\n    created_at: string;\r\n    updated_at: string;\r\n  };\r\n  license_category?: {\r\n    license_category_id: string;\r\n    name: string;\r\n    license_type?: {\r\n      license_type_id: string;\r\n      name: string;\r\n      code: string;\r\n    };\r\n  };\r\n  assignee?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n}\r\n\r\nexport enum ApplicationStatus {\r\n  DRAFT = 'draft',\r\n  SUBMITTED = 'submitted',\r\n  UNDER_REVIEW = 'under_review',\r\n  EVALUATION = 'evaluation',\r\n  APPROVED = 'approved',\r\n  REJECTED = 'rejected',\r\n  WITHDRAWN = 'withdrawn',\r\n}\r\n\r\nexport interface ApplicationFilters {\r\n  licenseTypeId?: string;\r\n  licenseCategoryId?: string;\r\n  status?: ApplicationStatus | '';\r\n  dateRange?: string;\r\n  search?: string;\r\n}\r\n\r\nexport interface PaginatedResponse<T> {\r\n  data: T[];\r\n  meta: {\r\n    itemsPerPage: number;\r\n    totalItems: number;\r\n    currentPage: number;\r\n    totalPages: number;\r\n    sortBy: [string, string][];\r\n    searchBy: string[];\r\n    search: string;\r\n    filter: Record<string, string | string[]>;\r\n  };\r\n  links: {\r\n    first: string;\r\n    previous: string;\r\n    current: string;\r\n    next: string;\r\n    last: string;\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAsFO,IAAA,AAAK,2CAAA;;;;;;;;WAAA", "debugId": null}}, {"offset": {"line": 281, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/services/notificationService.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\r\nimport { processApiResponse } from '@/lib/authUtils';\r\nimport { ApplicationStatus } from '../types/license';\r\n\r\nexport interface AppNotification {\r\n  notification_id: string;\r\n  user_id: string;\r\n  application_id: string;\r\n  application_number: string;\r\n  license_category_name: string;\r\n  title: string;\r\n  message: string;\r\n  type: 'status_change' | 'reminder' | 'document_required' | 'approval' | 'rejection';\r\n  status: 'unread' | 'read';\r\n  priority: 'low' | 'medium' | 'high';\r\n  created_at: string;\r\n  read_at?: string;\r\n  metadata?: {\r\n    old_status?: ApplicationStatus;\r\n    new_status?: ApplicationStatus;\r\n    step?: number;\r\n    progress_percentage?: number;\r\n  };\r\n}\r\n\r\nexport interface NotificationSummary {\r\n  total_count: number;\r\n  unread_count: number;\r\n  notifications: AppNotification[];\r\n}\r\n\r\nexport const notificationService = {\r\n  // Get user notifications\r\n  async getUserNotifications(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    type?: string;\r\n    status?: 'read' | 'unread';\r\n  }): Promise<NotificationSummary> {\r\n    try {\r\n      const queryParams = new URLSearchParams();\r\n      \r\n      if (params?.page) queryParams.append('page', params.page.toString());\r\n      if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n      if (params?.type) queryParams.append('type', params.type);\r\n      if (params?.status) queryParams.append('status', params.status);\r\n\r\n      const endpoint = `/notifications?${queryParams.toString()}`;\r\n      console.log(`[NotificationService] Fetching notifications from: ${endpoint}`);\r\n      \r\n      const response = await apiClient.get(endpoint);\r\n      const data = processApiResponse(response);\r\n      \r\n      // Validate response structure\r\n      if (!data || typeof data !== 'object') {\r\n        console.warn('Invalid notification response format:', data);\r\n        return {\r\n          total_count: 0,\r\n          unread_count: 0,\r\n          notifications: []\r\n        };\r\n      }\r\n      \r\n      return {\r\n        total_count: data.total_count || 0,\r\n        unread_count: data.unread_count || 0,\r\n        notifications: Array.isArray(data.notifications) ? data.notifications : []\r\n      };\r\n    } catch (error: any) {\r\n      console.error('Error fetching user notifications:', {\r\n        error: error.message || 'Unknown error',\r\n        response: error.response?.data || null,\r\n        status: error.response?.status || null,\r\n        code: error.code || null,\r\n        params,\r\n        endpoint: `/notifications?${new URLSearchParams(params || {}).toString()}`\r\n      });\r\n      \r\n      // Return empty result on error instead of throwing\r\n      return {\r\n        total_count: 0,\r\n        unread_count: 0,\r\n        notifications: []\r\n      };\r\n    }\r\n  },\r\n\r\n  // Mark notification as read\r\n  async markAsRead(notificationId: string): Promise<void> {\r\n    const response = await apiClient.patch(`/notifications/${notificationId}/read`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Mark all notifications as read\r\n  async markAllAsRead(): Promise<void> {\r\n    const response = await apiClient.patch('/notifications/mark-all-read');\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Create a status change notification (usually called from backend)\r\n  async createStatusChangeNotification(\r\n    applicationId: string,\r\n    oldStatus: ApplicationStatus,\r\n    newStatus: ApplicationStatus,\r\n    step?: number,\r\n    progressPercentage?: number\r\n  ): Promise<AppNotification> {\r\n    const response = await apiClient.post('/notifications/status-change', {\r\n      application_id: applicationId,\r\n      old_status: oldStatus,\r\n      new_status: newStatus,\r\n      step,\r\n      progress_percentage: progressPercentage\r\n    });\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Delete notification\r\n  async deleteNotification(notificationId: string): Promise<void> {\r\n    const response = await apiClient.delete(`/notifications/${notificationId}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get notification count\r\n  async getNotificationCount(): Promise<{ total: number; unread: number }> {\r\n    const response = await apiClient.get('/notifications/count');\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Manual notification trigger for testing (will be removed in production)\r\n  async triggerTestNotification(\r\n    applicationId: string,\r\n    applicationNumber: string,\r\n    licenseCategoryName: string,\r\n    type: 'status_change' | 'reminder' | 'document_required' | 'approval' | 'rejection',\r\n    status: ApplicationStatus\r\n  ): Promise<AppNotification> {\r\n    const response = await apiClient.post('/notifications/test', {\r\n      application_id: applicationId,\r\n      application_number: applicationNumber,\r\n      license_category_name: licenseCategoryName,\r\n      type,\r\n      status\r\n    });\r\n    return processApiResponse(response);\r\n  }\r\n};\r\n\r\n// Status change message templates\r\nexport const getStatusChangeMessage = (\r\n  applicationNumber: string,\r\n  licenseCategoryName: string,\r\n  oldStatus: ApplicationStatus,\r\n  newStatus: ApplicationStatus,\r\n  step?: number,\r\n  progressPercentage?: number\r\n): { title: string; message: string; type: 'info' | 'success' | 'warning' | 'error' } => {\r\n  const progressText = progressPercentage ? ` (${progressPercentage}% complete)` : '';\r\n  const stepText = step ? ` - Step ${step}` : '';\r\n  \r\n  const messages = {\r\n    [ApplicationStatus.SUBMITTED]: {\r\n      title: 'Application Submitted',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) has been successfully submitted and is now being processed.`,\r\n      type: 'success' as const\r\n    },\r\n    [ApplicationStatus.UNDER_REVIEW]: {\r\n      title: 'Application Under Review',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) is now under review by our team${progressText}${stepText}. We'll notify you of any updates.`,\r\n      type: 'info' as const\r\n    },\r\n    [ApplicationStatus.EVALUATION]: {\r\n      title: 'Application Being Evaluated',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) is currently being evaluated${progressText}${stepText}. This may take several business days.`,\r\n      type: 'info' as const\r\n    },\r\n    [ApplicationStatus.APPROVED]: {\r\n      title: 'Application Approved! 🎉',\r\n      message: `Congratulations! Your ${licenseCategoryName} application (${applicationNumber}) has been approved. You can now download your license.`,\r\n      type: 'success' as const\r\n    },\r\n    [ApplicationStatus.REJECTED]: {\r\n      title: 'Application Status Update',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) requires attention. Please review the feedback and resubmit if needed.`,\r\n      type: 'warning' as const\r\n    },\r\n    [ApplicationStatus.WITHDRAWN]: {\r\n      title: 'Application Withdrawn',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) has been withdrawn as requested.`,\r\n      type: 'info' as const\r\n    }\r\n  };\r\n\r\n  const defaultMessage = {\r\n    title: 'Application Status Update',\r\n    message: `Your ${licenseCategoryName} application (${applicationNumber}) status has been updated to ${newStatus.replace('_', ' ')}.`,\r\n    type: 'info' as const\r\n  };\r\n\r\n  return messages[newStatus] || defaultMessage;\r\n};\r\n\r\n// Helper function to manually trigger notification for testing\r\nexport const createTestNotification = (\r\n  applicationNumber: string,\r\n  licenseCategoryName: string,\r\n  status: ApplicationStatus\r\n): AppNotification => {\r\n  const now = new Date().toISOString();\r\n  const notificationId = `test-${Date.now()}`;\r\n  \r\n  return {\r\n    notification_id: notificationId,\r\n    user_id: 'current-user',\r\n    application_id: `app-${applicationNumber}`,\r\n    application_number: applicationNumber,\r\n    license_category_name: licenseCategoryName,\r\n    title: getStatusChangeMessage(applicationNumber, licenseCategoryName, ApplicationStatus.SUBMITTED, status).title,\r\n    message: getStatusChangeMessage(applicationNumber, licenseCategoryName, ApplicationStatus.SUBMITTED, status).message,\r\n    type: 'status_change',\r\n    status: 'unread',\r\n    priority: 'medium',\r\n    created_at: now,\r\n    metadata: {\r\n      old_status: ApplicationStatus.SUBMITTED,\r\n      new_status: status,\r\n      step: 2,\r\n      progress_percentage: 50\r\n    }\r\n  };\r\n};"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AA6BO,MAAM,sBAAsB;IACjC,yBAAyB;IACzB,MAAM,sBAAqB,MAK1B;QACC,IAAI;YACF,MAAM,cAAc,IAAI;YAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;YACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;YACpE,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI;YACxD,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;YAE9D,MAAM,WAAW,CAAC,eAAe,EAAE,YAAY,QAAQ,IAAI;YAC3D,QAAQ,GAAG,CAAC,CAAC,mDAAmD,EAAE,UAAU;YAE5E,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,MAAM,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;YAEhC,8BAA8B;YAC9B,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;gBACrC,QAAQ,IAAI,CAAC,yCAAyC;gBACtD,OAAO;oBACL,aAAa;oBACb,cAAc;oBACd,eAAe,EAAE;gBACnB;YACF;YAEA,OAAO;gBACL,aAAa,KAAK,WAAW,IAAI;gBACjC,cAAc,KAAK,YAAY,IAAI;gBACnC,eAAe,MAAM,OAAO,CAAC,KAAK,aAAa,IAAI,KAAK,aAAa,GAAG,EAAE;YAC5E;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,sCAAsC;gBAClD,OAAO,MAAM,OAAO,IAAI;gBACxB,UAAU,MAAM,QAAQ,EAAE,QAAQ;gBAClC,QAAQ,MAAM,QAAQ,EAAE,UAAU;gBAClC,MAAM,MAAM,IAAI,IAAI;gBACpB;gBACA,UAAU,CAAC,eAAe,EAAE,IAAI,gBAAgB,UAAU,CAAC,GAAG,QAAQ,IAAI;YAC5E;YAEA,mDAAmD;YACnD,OAAO;gBACL,aAAa;gBACb,cAAc;gBACd,eAAe,EAAE;YACnB;QACF;IACF;IAEA,4BAA4B;IAC5B,MAAM,YAAW,cAAsB;QACrC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,KAAK,CAAC,CAAC,eAAe,EAAE,eAAe,KAAK,CAAC;QAC9E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,iCAAiC;IACjC,MAAM;QACJ,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,KAAK,CAAC;QACvC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,oEAAoE;IACpE,MAAM,gCACJ,aAAqB,EACrB,SAA4B,EAC5B,SAA4B,EAC5B,IAAa,EACb,kBAA2B;QAE3B,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,gCAAgC;YACpE,gBAAgB;YAChB,YAAY;YACZ,YAAY;YACZ;YACA,qBAAqB;QACvB;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,sBAAsB;IACtB,MAAM,oBAAmB,cAAsB;QAC7C,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,eAAe,EAAE,gBAAgB;QAC1E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,yBAAyB;IACzB,MAAM;QACJ,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,0EAA0E;IAC1E,MAAM,yBACJ,aAAqB,EACrB,iBAAyB,EACzB,mBAA2B,EAC3B,IAAmF,EACnF,MAAyB;QAEzB,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,uBAAuB;YAC3D,gBAAgB;YAChB,oBAAoB;YACpB,uBAAuB;YACvB;YACA;QACF;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;AACF;AAGO,MAAM,yBAAyB,CACpC,mBACA,qBACA,WACA,WACA,MACA;IAEA,MAAM,eAAe,qBAAqB,CAAC,EAAE,EAAE,mBAAmB,WAAW,CAAC,GAAG;IACjF,MAAM,WAAW,OAAO,CAAC,QAAQ,EAAE,MAAM,GAAG;IAE5C,MAAM,WAAW;QACf,CAAC,uHAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC,EAAE;YAC7B,OAAO;YACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,6DAA6D,CAAC;YACrI,MAAM;QACR;QACA,CAAC,uHAAA,CAAA,oBAAiB,CAAC,YAAY,CAAC,EAAE;YAChC,OAAO;YACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,iCAAiC,EAAE,eAAe,SAAS,kCAAkC,CAAC;YACrK,MAAM;QACR;QACA,CAAC,uHAAA,CAAA,oBAAiB,CAAC,UAAU,CAAC,EAAE;YAC9B,OAAO;YACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,8BAA8B,EAAE,eAAe,SAAS,sCAAsC,CAAC;YACtK,MAAM;QACR;QACA,CAAC,uHAAA,CAAA,oBAAiB,CAAC,QAAQ,CAAC,EAAE;YAC5B,OAAO;YACP,SAAS,CAAC,sBAAsB,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,uDAAuD,CAAC;YAChJ,MAAM;QACR;QACA,CAAC,uHAAA,CAAA,oBAAiB,CAAC,QAAQ,CAAC,EAAE;YAC5B,OAAO;YACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,wEAAwE,CAAC;YAChJ,MAAM;QACR;QACA,CAAC,uHAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC,EAAE;YAC7B,OAAO;YACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,kCAAkC,CAAC;YAC1G,MAAM;QACR;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAO;QACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,6BAA6B,EAAE,UAAU,OAAO,CAAC,KAAK,KAAK,CAAC,CAAC;QACpI,MAAM;IACR;IAEA,OAAO,QAAQ,CAAC,UAAU,IAAI;AAChC;AAGO,MAAM,yBAAyB,CACpC,mBACA,qBACA;IAEA,MAAM,MAAM,IAAI,OAAO,WAAW;IAClC,MAAM,iBAAiB,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;IAE3C,OAAO;QACL,iBAAiB;QACjB,SAAS;QACT,gBAAgB,CAAC,IAAI,EAAE,mBAAmB;QAC1C,oBAAoB;QACpB,uBAAuB;QACvB,OAAO,uBAAuB,mBAAmB,qBAAqB,uHAAA,CAAA,oBAAiB,CAAC,SAAS,EAAE,QAAQ,KAAK;QAChH,SAAS,uBAAuB,mBAAmB,qBAAqB,uHAAA,CAAA,oBAAiB,CAAC,SAAS,EAAE,QAAQ,OAAO;QACpH,MAAM;QACN,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,UAAU;YACR,YAAY,uHAAA,CAAA,oBAAiB,CAAC,SAAS;YACvC,YAAY;YACZ,MAAM;YACN,qBAAqB;QACvB;IACF;AACF", "debugId": null}}, {"offset": {"line": 450, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/common/NotificationModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useCallback } from 'react';\r\nimport { notificationService, AppNotification } from '@/services/notificationService';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useToast } from '@/contexts/ToastContext';\r\n\r\ninterface NotificationModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n}\r\n\r\nconst NotificationModal: React.FC<NotificationModalProps> = ({ isOpen, onClose }) => {\r\n  const { user } = useAuth();\r\n  const { showError, showSuccess } = useToast();\r\n  const [notifications, setNotifications] = useState<AppNotification[]>([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [filter, setFilter] = useState<'all' | 'unread'>('all');\r\n\r\n  // Fetch notifications\r\n  const fetchNotifications = useCallback(async () => {\r\n    if (!user) return;\r\n    \r\n    setLoading(true);\r\n    try {\r\n      const data = await notificationService.getUserNotifications({ \r\n        limit: 50,\r\n        status: filter === 'unread' ? 'unread' : undefined\r\n      });\r\n      \r\n      // Validate response data\r\n      if (data && Array.isArray(data.notifications)) {\r\n        setNotifications(data.notifications);\r\n      } else {\r\n        console.warn('Invalid notification data received:', data);\r\n        setNotifications([]);\r\n      }\r\n    } catch (error: unknown) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Unknown error';\r\n      const errorResponse = error && typeof error === 'object' && 'response' in error ? error.response : null;\r\n      \r\n      console.error('Error fetching notifications:', {\r\n        error: errorMessage,\r\n        response: errorResponse && typeof errorResponse === 'object' && 'data' in errorResponse ? errorResponse.data : null,\r\n        status: errorResponse && typeof errorResponse === 'object' && 'status' in errorResponse ? errorResponse.status : null,\r\n        filter\r\n      });\r\n      \r\n      setNotifications([]);\r\n      showError('Failed to load notifications. Please try again.');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [user, filter, showError]);\r\n\r\n  // Initial fetch and refetch when filter changes\r\n  useEffect(() => {\r\n    if (isOpen) {\r\n      fetchNotifications();\r\n    }\r\n  }, [isOpen, fetchNotifications]);\r\n\r\n  // Mark notification as read\r\n  const markAsRead = async (notificationId: string) => {\r\n    try {\r\n      await notificationService.markAsRead(notificationId);\r\n      setNotifications(prev => \r\n        prev.map(notification => \r\n          notification.notification_id === notificationId \r\n            ? { ...notification, status: 'read' as const }\r\n            : notification\r\n        )\r\n      );\r\n    } catch (error) {\r\n      console.error('Error marking notification as read:', error);\r\n      showError('Failed to mark notification as read');\r\n    }\r\n  };\r\n\r\n  // Mark all as read\r\n  const markAllAsRead = async () => {\r\n    try {\r\n      await notificationService.markAllAsRead();\r\n      setNotifications(prev => \r\n        prev.map(notification => ({ ...notification, status: 'read' as const }))\r\n      );\r\n      showSuccess('All notifications marked as read');\r\n    } catch (error) {\r\n      console.error('Error marking all notifications as read:', error);\r\n      showError('Failed to mark all notifications as read');\r\n    }\r\n  };\r\n\r\n  // Delete notification\r\n  const deleteNotification = async (notificationId: string) => {\r\n    try {\r\n      await notificationService.deleteNotification(notificationId);\r\n      setNotifications(prev => \r\n        prev.filter(notification => notification.notification_id !== notificationId)\r\n      );\r\n      showSuccess('Notification deleted');\r\n    } catch (error) {\r\n      console.error('Error deleting notification:', error);\r\n      showError('Failed to delete notification');\r\n    }\r\n  };\r\n\r\n  // Get notification icon based on type\r\n  const getNotificationIcon = (type: AppNotification['type']) => {\r\n    switch (type) {\r\n      case 'status_change':\r\n        return 'ri-information-line';\r\n      case 'approval':\r\n        return 'ri-check-double-line';\r\n      case 'rejection':\r\n        return 'ri-close-circle-line';\r\n      case 'document_required':\r\n        return 'ri-file-text-line';\r\n      case 'reminder':\r\n        return 'ri-alarm-line';\r\n      default:\r\n        return 'ri-notification-line';\r\n    }\r\n  };\r\n\r\n  // Get notification color based on type\r\n  const getNotificationColor = (type: AppNotification['type']) => {\r\n    switch (type) {\r\n      case 'approval':\r\n        return 'text-green-600';\r\n      case 'rejection':\r\n        return 'text-red-600';\r\n      case 'document_required':\r\n        return 'text-orange-600';\r\n      case 'reminder':\r\n        return 'text-yellow-600';\r\n      default:\r\n        return 'text-blue-600';\r\n    }\r\n  };\r\n\r\n  // Format time ago\r\n  const formatTimeAgo = (dateString: string) => {\r\n    const now = new Date();\r\n    const date = new Date(dateString);\r\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\r\n\r\n    if (diffInSeconds < 60) return 'Just now';\r\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;\r\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;\r\n    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`;\r\n    \r\n    return date.toLocaleDateString();\r\n  };\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\r\n      <div className=\"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800\">\r\n        <div className=\"mt-3\">\r\n          {/* Header */}\r\n          <div className=\"flex items-center justify-between mb-6\">\r\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\r\n              <i className=\"ri-notification-line mr-2\"></i>\r\n              Notifications\r\n            </h3>\r\n            <button\r\n              type=\"button\"\r\n              onClick={onClose}\r\n              className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\r\n              aria-label=\"Close modal\"\r\n            >\r\n              <i className=\"ri-close-line text-xl\"></i>\r\n            </button>\r\n          </div>\r\n\r\n          {/* Filters and Actions */}\r\n          <div className=\"flex items-center justify-between mb-4\">\r\n            <div className=\"flex space-x-2\">\r\n              <button\r\n                onClick={() => setFilter('all')}\r\n                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${\r\n                  filter === 'all'\r\n                    ? 'bg-primary text-white'\r\n                    : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'\r\n                }`}\r\n              >\r\n                All\r\n              </button>\r\n              <button\r\n                onClick={() => setFilter('unread')}\r\n                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${\r\n                  filter === 'unread'\r\n                    ? 'bg-primary text-white'\r\n                    : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'\r\n                }`}\r\n              >\r\n                Unread\r\n              </button>\r\n            </div>\r\n\r\n            {notifications.some(n => n.status === 'unread') && (\r\n              <button\r\n                onClick={markAllAsRead}\r\n                className=\"text-sm text-primary hover:text-primary-dark focus:outline-none\"\r\n              >\r\n                Mark all as read\r\n              </button>\r\n            )}\r\n          </div>\r\n\r\n          {/* Notifications List */}\r\n          <div className=\"max-h-96 overflow-y-auto\">\r\n            {loading ? (\r\n              <div className=\"p-8 text-center\">\r\n                <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto\"></div>\r\n                <p className=\"mt-4 text-sm text-gray-500 dark:text-gray-400\">Loading notifications...</p>\r\n              </div>\r\n            ) : notifications.length === 0 ? (\r\n              <div className=\"p-8 text-center\">\r\n                <i className=\"ri-notification-off-line text-4xl text-gray-400 mb-4\"></i>\r\n                <p className=\"text-gray-500 dark:text-gray-400\">\r\n                  {filter === 'unread' ? 'No unread notifications' : 'No notifications yet'}\r\n                </p>\r\n              </div>\r\n            ) : (\r\n              <div className=\"space-y-2\">\r\n                {notifications.map((notification) => (\r\n                  <div\r\n                    key={notification.notification_id}\r\n                    className={`p-4 rounded-lg border transition-colors ${\r\n                      notification.status === 'unread' \r\n                        ? 'bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800' \r\n                        : 'bg-white border-gray-200 dark:bg-gray-700 dark:border-gray-600'\r\n                    }`}\r\n                  >\r\n                    <div className=\"flex items-start justify-between\">\r\n                      <div className=\"flex items-start space-x-3\">\r\n                        <div className={`flex-shrink-0 ${getNotificationColor(notification.type)}`}>\r\n                          <i className={`${getNotificationIcon(notification.type)} text-xl`}></i>\r\n                        </div>\r\n                        <div className=\"flex-1 min-w-0\">\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                              {notification.title}\r\n                            </h4>\r\n                            {notification.status === 'unread' && (\r\n                              <div className=\"w-2 h-2 bg-primary rounded-full flex-shrink-0 ml-2\"></div>\r\n                            )}\r\n                          </div>\r\n                          <p className=\"mt-1 text-sm text-gray-600 dark:text-gray-400\">\r\n                            {notification.message}\r\n                          </p>\r\n                          <div className=\"mt-2 flex items-center justify-between\">\r\n                            <p className=\"text-xs text-gray-500 dark:text-gray-500\">\r\n                              {formatTimeAgo(notification.created_at)}\r\n                            </p>\r\n                            <div className=\"flex space-x-2\">\r\n                              {notification.status === 'unread' && (\r\n                                <button\r\n                                  onClick={() => markAsRead(notification.notification_id)}\r\n                                  className=\"text-xs text-primary hover:text-primary-dark focus:outline-none\"\r\n                                >\r\n                                  Mark as read\r\n                                </button>\r\n                              )}\r\n                              <button\r\n                                onClick={() => deleteNotification(notification.notification_id)}\r\n                                className=\"text-xs text-red-600 hover:text-red-700 focus:outline-none\"\r\n                              >\r\n                                Delete\r\n                              </button>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Footer */}\r\n          <div className=\"flex justify-end mt-6 pt-4 border-t border-gray-200 dark:border-gray-600\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={onClose}\r\n              className=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n            >\r\n              Close\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default NotificationModal;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAYA,MAAM,oBAAsD,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE;IAC9E,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAC1C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IACxE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAEvD,sBAAsB;IACtB,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI,CAAC,MAAM;QAEX,WAAW;QACX,IAAI;YACF,MAAM,OAAO,MAAM,sIAAA,CAAA,sBAAmB,CAAC,oBAAoB,CAAC;gBAC1D,OAAO;gBACP,QAAQ,WAAW,WAAW,WAAW;YAC3C;YAEA,yBAAyB;YACzB,IAAI,QAAQ,MAAM,OAAO,CAAC,KAAK,aAAa,GAAG;gBAC7C,iBAAiB,KAAK,aAAa;YACrC,OAAO;gBACL,QAAQ,IAAI,CAAC,uCAAuC;gBACpD,iBAAiB,EAAE;YACrB;QACF,EAAE,OAAO,OAAgB;YACvB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,MAAM,gBAAgB,SAAS,OAAO,UAAU,YAAY,cAAc,QAAQ,MAAM,QAAQ,GAAG;YAEnG,QAAQ,KAAK,CAAC,iCAAiC;gBAC7C,OAAO;gBACP,UAAU,iBAAiB,OAAO,kBAAkB,YAAY,UAAU,gBAAgB,cAAc,IAAI,GAAG;gBAC/G,QAAQ,iBAAiB,OAAO,kBAAkB,YAAY,YAAY,gBAAgB,cAAc,MAAM,GAAG;gBACjH;YACF;YAEA,iBAAiB,EAAE;YACnB,UAAU;QACZ,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;QAAM;QAAQ;KAAU;IAE5B,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV;QACF;IACF,GAAG;QAAC;QAAQ;KAAmB;IAE/B,4BAA4B;IAC5B,MAAM,aAAa,OAAO;QACxB,IAAI;YACF,MAAM,sIAAA,CAAA,sBAAmB,CAAC,UAAU,CAAC;YACrC,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,eACP,aAAa,eAAe,KAAK,iBAC7B;wBAAE,GAAG,YAAY;wBAAE,QAAQ;oBAAgB,IAC3C;QAGV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,UAAU;QACZ;IACF;IAEA,mBAAmB;IACnB,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,sIAAA,CAAA,sBAAmB,CAAC,aAAa;YACvC,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,eAAgB,CAAC;wBAAE,GAAG,YAAY;wBAAE,QAAQ;oBAAgB,CAAC;YAExE,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,UAAU;QACZ;IACF;IAEA,sBAAsB;IACtB,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,MAAM,sIAAA,CAAA,sBAAmB,CAAC,kBAAkB,CAAC;YAC7C,iBAAiB,CAAA,OACf,KAAK,MAAM,CAAC,CAAA,eAAgB,aAAa,eAAe,KAAK;YAE/D,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,UAAU;QACZ;IACF;IAEA,sCAAsC;IACtC,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,uCAAuC;IACvC,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,kBAAkB;IAClB,MAAM,gBAAgB,CAAC;QACrB,MAAM,MAAM,IAAI;QAChB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;QAEpE,IAAI,gBAAgB,IAAI,OAAO;QAC/B,IAAI,gBAAgB,MAAM,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,IAAI,KAAK,CAAC;QACzE,IAAI,gBAAgB,OAAO,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,MAAM,KAAK,CAAC;QAC5E,IAAI,gBAAgB,SAAS,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,OAAO,KAAK,CAAC;QAE/E,OAAO,KAAK,kBAAkB;IAChC;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAE,WAAU;;;;;;oCAAgC;;;;;;;0CAG/C,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,cAAW;0CAEX,cAAA,8OAAC;oCAAE,WAAU;;;;;;;;;;;;;;;;;kCAKjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAW,CAAC,2DAA2D,EACrE,WAAW,QACP,0BACA,0GACJ;kDACH;;;;;;kDAGD,8OAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAW,CAAC,2DAA2D,EACrE,WAAW,WACP,0BACA,0GACJ;kDACH;;;;;;;;;;;;4BAKF,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,2BACpC,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;kCAOL,8OAAC;wBAAI,WAAU;kCACZ,wBACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAE,WAAU;8CAAgD;;;;;;;;;;;mCAE7D,cAAc,MAAM,KAAK,kBAC3B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;;;;;8CACb,8OAAC;oCAAE,WAAU;8CACV,WAAW,WAAW,4BAA4B;;;;;;;;;;;iDAIvD,8OAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,6BAClB,8OAAC;oCAEC,WAAW,CAAC,wCAAwC,EAClD,aAAa,MAAM,KAAK,WACpB,wEACA,kEACJ;8CAEF,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAW,CAAC,cAAc,EAAE,qBAAqB,aAAa,IAAI,GAAG;8DACxE,cAAA,8OAAC;wDAAE,WAAW,GAAG,oBAAoB,aAAa,IAAI,EAAE,QAAQ,CAAC;;;;;;;;;;;8DAEnE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EACX,aAAa,KAAK;;;;;;gEAEpB,aAAa,MAAM,KAAK,0BACvB,8OAAC;oEAAI,WAAU;;;;;;;;;;;;sEAGnB,8OAAC;4DAAE,WAAU;sEACV,aAAa,OAAO;;;;;;sEAEvB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EACV,cAAc,aAAa,UAAU;;;;;;8EAExC,8OAAC;oEAAI,WAAU;;wEACZ,aAAa,MAAM,KAAK,0BACvB,8OAAC;4EACC,SAAS,IAAM,WAAW,aAAa,eAAe;4EACtD,WAAU;sFACX;;;;;;sFAIH,8OAAC;4EACC,SAAS,IAAM,mBAAmB,aAAa,eAAe;4EAC9D,WAAU;sFACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCAxCN,aAAa,eAAe;;;;;;;;;;;;;;;kCAuD3C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;uCAEe", "debugId": null}}, {"offset": {"line": 914, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/common/NotificationBell.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { notificationService } from '@/services/notificationService';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useToast } from '@/contexts/ToastContext';\r\nimport NotificationModal from './NotificationModal';\r\n\r\ninterface NotificationBellProps {\r\n  className?: string;\r\n}\r\n\r\nconst NotificationBell: React.FC<NotificationBellProps> = ({ className = '' }) => {\r\n  const { user } = useAuth();\r\n  const { showError } = useToast();\r\n  const [unreadCount, setUnreadCount] = useState(0);\r\n  const [isModalOpen, setIsModalOpen] = useState(false);\r\n\r\n  // Fetch notification count\r\n  const fetchNotificationCount = async () => {\r\n    if (!user) return;\r\n    \r\n    try {\r\n      const data = await notificationService.getNotificationCount();\r\n      setUnreadCount(data.unread);\r\n    } catch (error) {\r\n      console.error('Error fetching notification count:', error);\r\n      showError('Failed to load notification count');\r\n    }\r\n  };\r\n\r\n  // Initial fetch\r\n  useEffect(() => {\r\n    fetchNotificationCount();\r\n  }, [user]);\r\n\r\n  // Poll for new notifications every 30 seconds\r\n  useEffect(() => {\r\n    const interval = setInterval(fetchNotificationCount, 30000);\r\n    return () => clearInterval(interval);\r\n  }, [user]);\r\n\r\n  // Handle modal close and refresh count\r\n  const handleModalClose = () => {\r\n    setIsModalOpen(false);\r\n    fetchNotificationCount(); // Refresh count when modal closes\r\n  };\r\n\r\n  if (!user) return null;\r\n\r\n  return (\r\n    <>\r\n      <div className={`relative ${className}`}>\r\n        {/* Notification Bell */}\r\n        <button\r\n          onClick={() => setIsModalOpen(true)}\r\n          className=\"relative p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 dark:focus:ring-offset-gray-800\"\r\n          title=\"Notifications\"\r\n        >\r\n          <i className=\"ri-notification-line text-xl\"></i>\r\n          {unreadCount > 0 && (\r\n            <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full min-w-[1.25rem] h-5 flex items-center justify-center px-1\">\r\n              {unreadCount > 99 ? '99+' : unreadCount}\r\n            </span>\r\n          )}\r\n        </button>\r\n      </div>\r\n\r\n      {/* Notification Modal */}\r\n      <NotificationModal\r\n        isOpen={isModalOpen}\r\n        onClose={handleModalClose}\r\n      />\r\n    </>\r\n  );\r\n};\r\n\r\nexport default NotificationBell;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAYA,MAAM,mBAAoD,CAAC,EAAE,YAAY,EAAE,EAAE;IAC3E,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAC7B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,2BAA2B;IAC3B,MAAM,yBAAyB;QAC7B,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,MAAM,OAAO,MAAM,sIAAA,CAAA,sBAAmB,CAAC,oBAAoB;YAC3D,eAAe,KAAK,MAAM;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,UAAU;QACZ;IACF;IAEA,gBAAgB;IAChB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAK;IAET,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY,wBAAwB;QACrD,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAK;IAET,uCAAuC;IACvC,MAAM,mBAAmB;QACvB,eAAe;QACf,0BAA0B,kCAAkC;IAC9D;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE;;0BACE,8OAAC;gBAAI,WAAW,CAAC,SAAS,EAAE,WAAW;0BAErC,cAAA,8OAAC;oBACC,SAAS,IAAM,eAAe;oBAC9B,WAAU;oBACV,OAAM;;sCAEN,8OAAC;4BAAE,WAAU;;;;;;wBACZ,cAAc,mBACb,8OAAC;4BAAK,WAAU;sCACb,cAAc,KAAK,QAAQ;;;;;;;;;;;;;;;;;0BAOpC,8OAAC,iJAAA,CAAA,UAAiB;gBAChB,QAAQ;gBACR,SAAS;;;;;;;;AAIjB;uCAEe", "debugId": null}}, {"offset": {"line": 1018, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/customer/CustomerLayout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { usePathname, useRouter } from 'next/navigation';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useLoading } from '@/contexts/LoadingContext';\r\nimport LogoutButton from '../LogoutButton';\r\nimport NotificationBell from '../common/NotificationBell';\r\n\r\ninterface BreadcrumbItem {\r\n  label: string;\r\n  href?: string;\r\n}\r\n\r\ninterface CustomerLayoutProps {\r\n  children: React.ReactNode;\r\n  breadcrumbs?: BreadcrumbItem[];\r\n}\r\n\r\nconst CustomerLayout: React.FC<CustomerLayoutProps> = ({ children, breadcrumbs }) => {\r\n  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);\r\n  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);\r\n  const pathname = usePathname();\r\n  const router = useRouter();\r\n  const { user } = useAuth();\r\n  const { showLoader } = useLoading();\r\n\r\n  // Memoize navigation items to prevent unnecessary re-renders\r\n  const navigationItems = useMemo(() => [\r\n    {\r\n      name: 'Dashboard',\r\n      href: '/customer',\r\n      icon: 'ri-dashboard-line',\r\n      current: pathname === '/customer'\r\n    },\r\n    {\r\n      name: 'My Licenses',\r\n      href: '/customer/my-licenses',\r\n      icon: 'ri-key-line',\r\n      current: pathname === '/customer/my-licenses'\r\n    },\r\n    {\r\n      name: 'New Applications',\r\n      href: '/customer/applications',\r\n      icon: 'ri-file-list-3-line',\r\n      current: pathname === '/customer/applications'\r\n    },\r\n    {\r\n      name: 'Payments',\r\n      href: '/customer/payments',\r\n      icon: 'ri-bank-card-line',\r\n      current: pathname === '/customer/payments'\r\n    },\r\n    {\r\n      name: 'Documents',\r\n      href: '/customer/documents',\r\n      icon: 'ri-file-text-line',\r\n      current: pathname === '/customer/documents'\r\n    },\r\n    {\r\n      name: 'Procurement',\r\n      href: '/customer/procurement',\r\n      icon: 'ri-auction-line',\r\n      current: pathname === '/customer/procurement'\r\n    },\r\n    {\r\n      name: 'Request Resource',\r\n      href: '/customer/resources',\r\n      icon: 'ri-hand-heart-line',\r\n      current: pathname === '/customer/resources'\r\n    }\r\n  ], [pathname]);\r\n\r\n  const supportItems = useMemo(() => [\r\n    {\r\n      name: 'Data Protection',\r\n      href: '/customer/data-protection',\r\n      icon: 'ri-shield-keyhole-line'\r\n    },\r\n    {\r\n      name: 'Help Center',\r\n      href: '/customer/help',\r\n      icon: 'ri-question-line'\r\n    }\r\n  ], []);\r\n\r\n  // Prefetch customer pages on mount for faster navigation\r\n  useEffect(() => {\r\n    const prefetchPages = () => {\r\n      const customerPages = [\r\n        '/customer',\r\n        '/customer/applications',\r\n        '/customer/applications/standards',\r\n        '/customer/payments',\r\n        '/customer/my-licenses',\r\n        '/customer/procurement',\r\n        '/customer/profile',\r\n        '/customer/data-protection',\r\n        '/customer/resources',\r\n        '/customer/help'\r\n      ];\r\n\r\n      customerPages.forEach(page => {\r\n        router.prefetch(page);\r\n      });\r\n    };\r\n\r\n    // Delay prefetching to not interfere with initial page load\r\n    const timer = setTimeout(prefetchPages, 1000);\r\n    return () => clearTimeout(timer);\r\n  }, [router]);\r\n\r\n  const toggleMobileSidebar = useCallback(() => {\r\n    setIsMobileSidebarOpen(!isMobileSidebarOpen);\r\n  }, [isMobileSidebarOpen]);\r\n\r\n  const handleNavClick = useCallback((href: string, name: string) => {\r\n    const pageMessages: { [key: string]: string } = {\r\n      '/customer': 'Loading Dashboard...',\r\n      '/customer/my-licenses': 'Loading My Licenses...',\r\n      '/customer/applications': 'Loading Applications...',\r\n      '/customer/applications/apply/': 'Loading Standards License Options...',\r\n      '/customer/payments': 'Loading Payments...',\r\n      '/customer/documents': 'Loading Documents...',\r\n      '/customer/procurement': 'Loading Procurement...',\r\n      '/customer/resources': 'Loading Resources...',\r\n      '/customer/data-protection': 'Loading Data Protection...',\r\n      '/customer/help': 'Loading Help Center...',\r\n      '/customer/profile': 'Loading Profile...',\r\n      '/customer/settings': 'Loading Settings...'\r\n    };\r\n\r\n    const message = pageMessages[href] || `Loading ${name}...`;\r\n    showLoader(message);\r\n    setIsMobileSidebarOpen(false);\r\n  }, [showLoader]);\r\n\r\n  const handleNavHover = useCallback((href: string) => {\r\n    // Prefetch on hover for instant navigation\r\n    router.prefetch(href);\r\n  }, [router]);\r\n\r\n  const toggleUserDropdown = useCallback(() => {\r\n    setIsUserDropdownOpen(!isUserDropdownOpen);\r\n  }, [isUserDropdownOpen]);\r\n\r\n  return (\r\n    <div className=\"flex h-screen overflow-hidden\">\r\n      {/* Mobile sidebar overlay */}\r\n      {isMobileSidebarOpen && (\r\n        <div \r\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden\"\r\n          onClick={() => setIsMobileSidebarOpen(false)}\r\n        />\r\n      )}\r\n\r\n      {/* Sidebar */}\r\n      <aside className={`\r\n        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out gpu-accelerated\r\n        ${isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}\r\n      `}>\r\n        <div className=\"flex flex-col h-full\">\r\n          {/* Logo/Header */}\r\n          <div className=\"h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700\">\r\n            <div className=\"flex items-center\">\r\n              <Image \r\n                src=\"/images/macra-logo.png\" \r\n                alt=\"MACRA Logo\" \r\n                className=\"max-h-12 w-auto\" \r\n                width={120}\r\n                height={48}\r\n                priority\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Navigation */}\r\n          <nav className=\"mt-6 px-4 side-nav\">\r\n            {/* Main Navigation */}\r\n            <div className=\"space-y-1\">\r\n              {navigationItems.map((item) => (\r\n                <Link\r\n                  key={item.name}\r\n                  href={item.href}\r\n                  onClick={() => handleNavClick(item.href, item.name)}\r\n                  onMouseEnter={() => handleNavHover(item.href)}\r\n                  className={`\r\n                    flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated\r\n                    ${item.current\r\n                      ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-r-2 border-red-600 dark:border-red-400'\r\n                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100'\r\n                    }\r\n                  `}\r\n                >\r\n                  <div className={`w-5 h-5 flex items-center justify-center mr-3 ${item.current ? 'text-red-600 dark:text-red-400' : ''}`}>\r\n                    <i className={item.icon}></i>\r\n                  </div>\r\n                  {item.name}\r\n                </Link>\r\n              ))}\r\n            </div>\r\n\r\n            {/* Support Section */}\r\n            <div className=\"mt-8\">\r\n              <h3 className=\"px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                Support\r\n              </h3>\r\n              <div className=\"mt-2 space-y-1\">\r\n                {supportItems.map((item) => (\r\n                  <Link\r\n                    key={item.name}\r\n                    href={item.href}\r\n                    onClick={() => handleNavClick(item.href, item.name)}\r\n                    onMouseEnter={() => handleNavHover(item.href)}\r\n                    className=\"flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100\"\r\n                  >\r\n                    <div className=\"w-5 h-5 flex items-center justify-center mr-3\">\r\n                      <i className={item.icon}></i>\r\n                    </div>\r\n                    {item.name}\r\n                  </Link>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </nav>\r\n\r\n          {/* User Info */}\r\n          <div className=\"absolute bottom-0 w-64 p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800\">\r\n            <div className=\"flex items-center space-x-3\">\r\n              <Image\r\n                className=\"h-10 w-10 rounded-full object-cover\"\r\n                src={user?.profile_image || \"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp\"}\r\n                alt=\"Profile\"\r\n                width={40}\r\n                height={40}\r\n              />\r\n              <Link \r\n                href='/customer/profile' \r\n                className=\"flex-1 min-w-0\"\r\n                onClick={() => handleNavClick('/customer/profile', 'Profile')}\r\n              >\r\n                <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100 truncate\">\r\n                  {user ? `${user.first_name} ${user.last_name}` : 'Customer'}\r\n                </p>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 truncate\">\r\n                  {/* {user?.organizationName || 'Organization'} */}\r\n                </p>\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </aside>\r\n\r\n      {/* Main content */}\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        {/* Top header */}\r\n        <header className=\"bg-white dark:bg-gray-800 shadow-sm z-10\">\r\n          <div className=\"flex items-center justify-between h-16 px-4 sm:px-6\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={toggleMobileSidebar}\r\n              className=\"md:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none\"\r\n              aria-label=\"Open mobile menu\"\r\n            >\r\n              <div className=\"w-6 h-6 flex items-center justify-center\">\r\n                <i className=\"ri-menu-line ri-lg\"></i>\r\n              </div>\r\n            </button>\r\n            \r\n            <div className=\"flex-1\">\r\n              {breadcrumbs && breadcrumbs.length > 0 && (\r\n                <nav className=\"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400\">\r\n                  {breadcrumbs.map((item, index) => (\r\n                    <React.Fragment key={index}>\r\n                      {index > 0 && <i className=\"ri-arrow-right-s-line\"></i>}\r\n                      {item.href ? (\r\n                        <Link href={item.href} className=\"hover:text-primary\">\r\n                          {item.label}\r\n                        </Link>\r\n                      ) : (\r\n                        <span className=\"text-gray-900 dark:text-gray-100\">{item.label}</span>\r\n                      )}\r\n                    </React.Fragment>\r\n                  ))}\r\n                </nav>\r\n              )}\r\n            </div>\r\n            \r\n            <div className=\"flex items-center\">\r\n              <NotificationBell className=\"mr-4\" />\r\n              \r\n              <div className=\"relative\">\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={toggleUserDropdown}\r\n                  className=\"flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n                >\r\n                  <span className=\"sr-only\">Open user menu</span>\r\n                  <Image\r\n                    className=\"h-8 w-8 rounded-full\"\r\n                    src={user?.profile_image || \"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp\"}\r\n                    alt=\"Profile\"\r\n                    width={32}\r\n                    height={32}\r\n                  />\r\n                </button>\r\n                \r\n                {isUserDropdownOpen && (\r\n                  <div className=\"absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 dark:ring-gray-700 z-50\">\r\n                    <div className=\"py-1\">\r\n                      <Link\r\n                        href=\"/customer/profile\"\r\n                        className=\"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        onClick={() => {\r\n                          handleNavClick('/customer/profile', 'Profile');\r\n                          setIsUserDropdownOpen(false);\r\n                        }}\r\n                      >\r\n                        Your Profile\r\n                      </Link>\r\n          \r\n                      <LogoutButton\r\n                        variant=\"text\"\r\n                        size=\"sm\"\r\n                        className=\"w-full justify-start text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        showConfirmation={true}\r\n                      >\r\n                        Sign out\r\n                      </LogoutButton>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </header>\r\n\r\n        {/* Main content area */}\r\n        <main className=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900\">\r\n          <div className=\"py-6\">\r\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n              {children}\r\n            </div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CustomerLayout;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAqBA,MAAM,iBAAgD,CAAC,EAAE,QAAQ,EAAE,WAAW,EAAE;IAC9E,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,aAAU,AAAD;IAEhC,6DAA6D;IAC7D,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM;YACpC;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;SACD,EAAE;QAAC;KAAS;IAEb,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM;YACjC;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;YACR;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;YACR;SACD,EAAE,EAAE;IAEL,yDAAyD;IACzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,MAAM,gBAAgB;gBACpB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YAED,cAAc,OAAO,CAAC,CAAA;gBACpB,OAAO,QAAQ,CAAC;YAClB;QACF;QAEA,4DAA4D;QAC5D,MAAM,QAAQ,WAAW,eAAe;QACxC,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAO;IAEX,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,uBAAuB,CAAC;IAC1B,GAAG;QAAC;KAAoB;IAExB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,MAAc;QAChD,MAAM,eAA0C;YAC9C,aAAa;YACb,yBAAyB;YACzB,0BAA0B;YAC1B,iCAAiC;YACjC,sBAAsB;YACtB,uBAAuB;YACvB,yBAAyB;YACzB,uBAAuB;YACvB,6BAA6B;YAC7B,kBAAkB;YAClB,qBAAqB;YACrB,sBAAsB;QACxB;QAEA,MAAM,UAAU,YAAY,CAAC,KAAK,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,CAAC;QAC1D,WAAW;QACX,uBAAuB;IACzB,GAAG;QAAC;KAAW;IAEf,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,2CAA2C;QAC3C,OAAO,QAAQ,CAAC;IAClB,GAAG;QAAC;KAAO;IAEX,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,sBAAsB,CAAC;IACzB,GAAG;QAAC;KAAmB;IAEvB,qBACE,8OAAC;QAAI,WAAU;;YAEZ,qCACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,uBAAuB;;;;;;0BAK1C,8OAAC;gBAAM,WAAW,CAAC;;QAEjB,EAAE,sBAAsB,kBAAkB,qCAAqC;MACjF,CAAC;0BACC,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,WAAU;oCACV,OAAO;oCACP,QAAQ;oCACR,QAAQ;;;;;;;;;;;;;;;;sCAMd,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;4CAClD,cAAc,IAAM,eAAe,KAAK,IAAI;4CAC5C,WAAW,CAAC;;oBAEV,EAAE,KAAK,OAAO,GACV,8GACA,wHACH;kBACH,CAAC;;8DAED,8OAAC;oDAAI,WAAW,CAAC,8CAA8C,EAAE,KAAK,OAAO,GAAG,mCAAmC,IAAI;8DACrH,cAAA,8OAAC;wDAAE,WAAW,KAAK,IAAI;;;;;;;;;;;gDAExB,KAAK,IAAI;;2CAfL,KAAK,IAAI;;;;;;;;;;8CAqBpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuF;;;;;;sDAGrG,8OAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,4JAAA,CAAA,UAAI;oDAEH,MAAM,KAAK,IAAI;oDACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;oDAClD,cAAc,IAAM,eAAe,KAAK,IAAI;oDAC5C,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAE,WAAW,KAAK,IAAI;;;;;;;;;;;wDAExB,KAAK,IAAI;;mDATL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;sCAiBxB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,6HAAA,CAAA,UAAK;wCACJ,WAAU;wCACV,KAAK,MAAM,iBAAiB;wCAC5B,KAAI;wCACJ,OAAO;wCACP,QAAQ;;;;;;kDAEV,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,eAAe,qBAAqB;;0DAEnD,8OAAC;gDAAE,WAAU;0DACV,OAAO,GAAG,KAAK,UAAU,CAAC,CAAC,EAAE,KAAK,SAAS,EAAE,GAAG;;;;;;0DAEnD,8OAAC;gDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUvB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAO,WAAU;kCAChB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,cAAW;8CAEX,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;;;;;;;;;;;;;;;;8CAIjB,8OAAC;oCAAI,WAAU;8CACZ,eAAe,YAAY,MAAM,GAAG,mBACnC,8OAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;;oDACZ,QAAQ,mBAAK,8OAAC;wDAAE,WAAU;;;;;;oDAC1B,KAAK,IAAI,iBACR,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAM,KAAK,IAAI;wDAAE,WAAU;kEAC9B,KAAK,KAAK;;;;;6EAGb,8OAAC;wDAAK,WAAU;kEAAoC,KAAK,KAAK;;;;;;;+CAP7C;;;;;;;;;;;;;;;8CAe7B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gJAAA,CAAA,UAAgB;4CAAC,WAAU;;;;;;sDAE5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;;sEAEV,8OAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,8OAAC,6HAAA,CAAA,UAAK;4DACJ,WAAU;4DACV,KAAK,MAAM,iBAAiB;4DAC5B,KAAI;4DACJ,OAAO;4DACP,QAAQ;;;;;;;;;;;;gDAIX,oCACC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,4JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;gEACV,SAAS;oEACP,eAAe,qBAAqB;oEACpC,sBAAsB;gEACxB;0EACD;;;;;;0EAID,8OAAC,kIAAA,CAAA,UAAY;gEACX,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,kBAAkB;0EACnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAYf,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;uCAEe", "debugId": null}}, {"offset": {"line": 1599, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/forms/FileUpload.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useRef } from 'react';\r\n\r\ninterface FileUploadProps {\r\n  id: string;\r\n  label: string;\r\n  accept?: string;\r\n  maxSize?: number; // in MB\r\n  required?: boolean;\r\n  value?: File | null;\r\n  onChange: (file: File | null) => void;\r\n  description?: string;\r\n  className?: string;\r\n}\r\n\r\nconst FileUpload: React.FC<FileUploadProps> = ({\r\n  id,\r\n  label,\r\n  accept = '.pdf',\r\n  maxSize = 10,\r\n  required = false,\r\n  value,\r\n  onChange,\r\n  description,\r\n  className = ''\r\n}) => {\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n    const file = event.target.files?.[0] || null;\r\n    \r\n    if (file) {\r\n      // Check file size\r\n      if (file.size > maxSize * 1024 * 1024) {\r\n        alert(`File size must be less than ${maxSize}MB`);\r\n        return;\r\n      }\r\n      \r\n      // Check file type\r\n      if (accept && !accept.split(',').some(type => file.name.toLowerCase().endsWith(type.trim().replace('*', '')))) {\r\n        alert(`File type must be: ${accept}`);\r\n        return;\r\n      }\r\n    }\r\n    \r\n    onChange(file);\r\n  };\r\n\r\n  const handleClick = () => {\r\n    fileInputRef.current?.click();\r\n  };\r\n\r\n  const handleRemove = (e: React.MouseEvent) => {\r\n    e.stopPropagation();\r\n    onChange(null);\r\n    if (fileInputRef.current) {\r\n      fileInputRef.current.value = '';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={className}>\r\n      <label htmlFor={id} className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n        {label} {required && <span className=\"text-red-500\">*</span>}\r\n      </label>\r\n      \r\n      <div\r\n        onClick={handleClick}\r\n        className=\"border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-3 text-center cursor-pointer hover:border-primary hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors\"\r\n      >\r\n        <input\r\n          ref={fileInputRef}\r\n          type=\"file\"\r\n          accept={accept}\r\n          onChange={handleFileChange}\r\n          className=\"hidden\"\r\n          id={id}\r\n          required={required}\r\n        />\r\n        \r\n        {value ? (\r\n          <div className=\"space-y-1\">\r\n            <div className=\"flex items-center justify-center\">\r\n              <i className=\"ri-file-text-line text-2xl text-green-500\"></i>\r\n            </div>\r\n            <p className=\"text-sm font-medium text-green-600 dark:text-green-400\">\r\n              {value.name}\r\n            </p>\r\n            <p className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n              {(value.size / 1024 / 1024).toFixed(2)} MB\r\n            </p>\r\n            <button\r\n              type=\"button\"\r\n              onClick={handleRemove}\r\n              className=\"inline-flex items-center px-3 py-1 bg-red-100 text-red-700 hover:bg-red-200 rounded-md text-xs font-medium transition-colors\"\r\n            >\r\n              <i className=\"ri-delete-bin-line mr-1\"></i>\r\n              Remove\r\n            </button>\r\n          </div>\r\n        ) : (\r\n          <div className=\"space-y-1\">\r\n            <div className=\"flex items-center justify-center\">\r\n              <i className=\"ri-upload-cloud-2-line text-2xl text-gray-400\"></i>\r\n            </div>\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              Click to upload {label.toLowerCase()}\r\n            </p>\r\n            {description && (\r\n              <p className=\"text-xs text-gray-500 dark:text-gray-500\">\r\n                {description}\r\n              </p>\r\n            )}\r\n            <div className=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors\">\r\n              <i className=\"ri-folder-upload-line mr-2\"></i>\r\n              Choose File\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n      \r\n      {description && !value && (\r\n        <p className=\"mt-2 text-xs text-gray-500 dark:text-gray-400\">\r\n          {description}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FileUpload;\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAgBA,MAAM,aAAwC,CAAC,EAC7C,EAAE,EACF,KAAK,EACL,SAAS,MAAM,EACf,UAAU,EAAE,EACZ,WAAW,KAAK,EAChB,KAAK,EACL,QAAQ,EACR,WAAW,EACX,YAAY,EAAE,EACf;IACC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI;QAExC,IAAI,MAAM;YACR,kBAAkB;YAClB,IAAI,KAAK,IAAI,GAAG,UAAU,OAAO,MAAM;gBACrC,MAAM,CAAC,4BAA4B,EAAE,QAAQ,EAAE,CAAC;gBAChD;YACF;YAEA,kBAAkB;YAClB,IAAI,UAAU,CAAC,OAAO,KAAK,CAAC,KAAK,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,KAAK,IAAI,GAAG,OAAO,CAAC,KAAK,OAAO;gBAC7G,MAAM,CAAC,mBAAmB,EAAE,QAAQ;gBACpC;YACF;QACF;QAEA,SAAS;IACX;IAEA,MAAM,cAAc;QAClB,aAAa,OAAO,EAAE;IACxB;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,eAAe;QACjB,SAAS;QACT,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK,GAAG;QAC/B;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW;;0BACd,8OAAC;gBAAM,SAAS;gBAAI,WAAU;;oBAC3B;oBAAM;oBAAE,0BAAY,8OAAC;wBAAK,WAAU;kCAAe;;;;;;;;;;;;0BAGtD,8OAAC;gBACC,SAAS;gBACT,WAAU;;kCAEV,8OAAC;wBACC,KAAK;wBACL,MAAK;wBACL,QAAQ;wBACR,UAAU;wBACV,WAAU;wBACV,IAAI;wBACJ,UAAU;;;;;;oBAGX,sBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;;;;;;;;;;0CAEf,8OAAC;gCAAE,WAAU;0CACV,MAAM,IAAI;;;;;;0CAEb,8OAAC;gCAAE,WAAU;;oCACV,CAAC,MAAM,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;oCAAG;;;;;;;0CAEzC,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;;kDAEV,8OAAC;wCAAE,WAAU;;;;;;oCAA8B;;;;;;;;;;;;6CAK/C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;;;;;;;;;;0CAEf,8OAAC;gCAAE,WAAU;;oCAA2C;oCACrC,MAAM,WAAW;;;;;;;4BAEnC,6BACC,8OAAC;gCAAE,WAAU;0CACV;;;;;;0CAGL,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;;;;;;oCAAiC;;;;;;;;;;;;;;;;;;;YAOrD,eAAe,CAAC,uBACf,8OAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;uCAEe", "debugId": null}}, {"offset": {"line": 1822, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/forms/FormMessages.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\n\r\ninterface FormMessagesProps {\r\n  successMessage?: string | null;\r\n  errorMessage?: string | null;\r\n  validationErrors?: Record<string, string>;\r\n  className?: string;\r\n}\r\n\r\nexport const FormMessages: React.FC<FormMessagesProps> = ({\r\n  successMessage,\r\n  errorMessage,\r\n  validationErrors = {},\r\n  className = ''\r\n}) => {\r\n  const hasErrors = errorMessage || Object.keys(validationErrors).length > 0;\r\n\r\n  return (\r\n    <div className={className}>\r\n      {/* Success Message */}\r\n      {successMessage && (\r\n        <div className=\"mb-6 bg-green-50 border border-green-200 rounded-lg p-4\">\r\n          <div className=\"flex items-center\">\r\n            <i className=\"ri-check-circle-line text-green-500 mr-2\"></i>\r\n            <p className=\"text-green-700\">{successMessage}</p>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Error Message */}\r\n      {errorMessage && (\r\n        <div className=\"mb-6 bg-red-50 border border-red-200 rounded-lg p-4\">\r\n          <div className=\"flex items-center\">\r\n            <i className=\"ri-error-warning-line text-red-500 mr-2\"></i>\r\n            <p className=\"text-red-700\">{errorMessage}</p>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Validation Errors */}\r\n      {Object.keys(validationErrors).length > 0 && !errorMessage && (\r\n        <div className=\"mb-6 bg-red-50 border border-red-200 rounded-lg p-4\">\r\n          <div className=\"flex items-start\">\r\n            <i className=\"ri-error-warning-line text-red-500 mr-2 mt-0.5\"></i>\r\n            <div>\r\n              <h4 className=\"text-sm font-medium text-red-900 mb-2\">\r\n                Please fix the following issues:\r\n              </h4>\r\n              <ul className=\"text-sm text-red-700 space-y-1\">\r\n                {Object.entries(validationErrors).map(([field, error]) => (\r\n                  <li key={field}>• {error}</li>\r\n                ))}\r\n              </ul>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\ninterface SuccessMessageProps {\r\n  message: string;\r\n  onDismiss?: () => void;\r\n  autoHide?: boolean;\r\n  autoHideDelay?: number;\r\n  className?: string;\r\n}\r\n\r\nexport const SuccessMessage: React.FC<SuccessMessageProps> = ({\r\n  message,\r\n  onDismiss,\r\n  autoHide = true,\r\n  autoHideDelay = 5000,\r\n  className = ''\r\n}) => {\r\n  React.useEffect(() => {\r\n    if (autoHide && onDismiss) {\r\n      const timer = setTimeout(() => {\r\n        onDismiss();\r\n      }, autoHideDelay);\r\n\r\n      return () => clearTimeout(timer);\r\n    }\r\n  }, [autoHide, autoHideDelay, onDismiss]);\r\n\r\n  return (\r\n    <div className={`bg-green-50 border border-green-200 rounded-lg p-4 ${className}`}>\r\n      <div className=\"flex items-center justify-between\">\r\n        <div className=\"flex items-center\">\r\n          <i className=\"ri-check-circle-line text-green-500 mr-2\"></i>\r\n          <p className=\"text-green-700\">{message}</p>\r\n        </div>\r\n        {onDismiss && (\r\n          <button\r\n            onClick={onDismiss}\r\n            className=\"text-green-500 hover:text-green-700 ml-4\"\r\n            aria-label=\"Dismiss\"\r\n          >\r\n            <i className=\"ri-close-line\"></i>\r\n          </button>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\ninterface ErrorMessageProps {\r\n  message: string;\r\n  onDismiss?: () => void;\r\n  className?: string;\r\n}\r\n\r\nexport const ErrorMessage: React.FC<ErrorMessageProps> = ({\r\n  message,\r\n  onDismiss,\r\n  className = ''\r\n}) => {\r\n  return (\r\n    <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>\r\n      <div className=\"flex items-center justify-between\">\r\n        <div className=\"flex items-center\">\r\n          <i className=\"ri-error-warning-line text-red-500 mr-2\"></i>\r\n          <p className=\"text-red-700\">{message}</p>\r\n        </div>\r\n        {onDismiss && (\r\n          <button\r\n            onClick={onDismiss}\r\n            className=\"text-red-500 hover:text-red-700 ml-4\"\r\n            aria-label=\"Dismiss\"\r\n          >\r\n            <i className=\"ri-close-line\"></i>\r\n          </button>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\ninterface ValidationErrorsProps {\r\n  errors: Record<string, string>;\r\n  className?: string;\r\n}\r\n\r\nexport const ValidationErrors: React.FC<ValidationErrorsProps> = ({\r\n  errors,\r\n  className = ''\r\n}) => {\r\n  if (Object.keys(errors).length === 0) return null;\r\n\r\n  return (\r\n    <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>\r\n      <div className=\"flex items-start\">\r\n        <i className=\"ri-error-warning-line text-red-500 mr-2 mt-0.5\"></i>\r\n        <div>\r\n          <h4 className=\"text-sm font-medium text-red-900 mb-2\">\r\n            Please fix the following issues:\r\n          </h4>\r\n          <ul className=\"text-sm text-red-700 space-y-1\">\r\n            {Object.entries(errors).map(([field, error]) => (\r\n              <li key={field}>• {error}</li>\r\n            ))}\r\n          </ul>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FormMessages;\r\n"], "names": [], "mappings": ";;;;;;;;AAEA;AAFA;;;AAWO,MAAM,eAA4C,CAAC,EACxD,cAAc,EACd,YAAY,EACZ,mBAAmB,CAAC,CAAC,EACrB,YAAY,EAAE,EACf;IACC,MAAM,YAAY,gBAAgB,OAAO,IAAI,CAAC,kBAAkB,MAAM,GAAG;IAEzE,qBACE,8OAAC;QAAI,WAAW;;YAEb,gCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;;;;;sCACb,8OAAC;4BAAE,WAAU;sCAAkB;;;;;;;;;;;;;;;;;YAMpC,8BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;;;;;sCACb,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;YAMlC,OAAO,IAAI,CAAC,kBAAkB,MAAM,GAAG,KAAK,CAAC,8BAC5C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;;;;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,8OAAC;oCAAG,WAAU;8CACX,OAAO,OAAO,CAAC,kBAAkB,GAAG,CAAC,CAAC,CAAC,OAAO,MAAM,iBACnD,8OAAC;;gDAAe;gDAAG;;2CAAV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3B;AAUO,MAAM,iBAAgD,CAAC,EAC5D,OAAO,EACP,SAAS,EACT,WAAW,IAAI,EACf,gBAAgB,IAAI,EACpB,YAAY,EAAE,EACf;IACC,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,YAAY,WAAW;YACzB,MAAM,QAAQ,WAAW;gBACvB;YACF,GAAG;YAEH,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;QAAU;QAAe;KAAU;IAEvC,qBACE,8OAAC;QAAI,WAAW,CAAC,mDAAmD,EAAE,WAAW;kBAC/E,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;;;;;sCACb,8OAAC;4BAAE,WAAU;sCAAkB;;;;;;;;;;;;gBAEhC,2BACC,8OAAC;oBACC,SAAS;oBACT,WAAU;oBACV,cAAW;8BAEX,cAAA,8OAAC;wBAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMzB;AAQO,MAAM,eAA4C,CAAC,EACxD,OAAO,EACP,SAAS,EACT,YAAY,EAAE,EACf;IACC,qBACE,8OAAC;QAAI,WAAW,CAAC,+CAA+C,EAAE,WAAW;kBAC3E,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;;;;;sCACb,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;gBAE9B,2BACC,8OAAC;oBACC,SAAS;oBACT,WAAU;oBACV,cAAW;8BAEX,cAAA,8OAAC;wBAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMzB;AAOO,MAAM,mBAAoD,CAAC,EAChE,MAAM,EACN,YAAY,EAAE,EACf;IACC,IAAI,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK,GAAG,OAAO;IAE7C,qBACE,8OAAC;QAAI,WAAW,CAAC,+CAA+C,EAAE,WAAW;kBAC3E,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAE,WAAU;;;;;;8BACb,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAG,WAAU;sCACX,OAAO,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,OAAO,MAAM,iBACzC,8OAAC;;wCAAe;wCAAG;;mCAAV;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvB;uCAEe", "debugId": null}}, {"offset": {"line": 2158, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/app/customer/payments/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport CustomerLayout from '@/components/customer/CustomerLayout';\r\nimport Loader from '@/components/Loader';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport FileUpload from '@/components/forms/FileUpload';\r\nimport { FormMessages } from '@/components/forms/FormMessages';\r\n\r\ninterface Payment {\r\n  id: string;\r\n  invoiceNumber: string;\r\n  amount: number;\r\n  currency: string;\r\n  status: 'pending' | 'paid' | 'overdue' | 'cancelled';\r\n  dueDate: string;\r\n  paidDate?: string;\r\n  issueDate: string;\r\n  description: string;\r\n  paymentType: string;\r\n  clientName: string;\r\n  clientEmail: string;\r\n  paymentMethod?: string;\r\n  notes?: string;\r\n}\r\n\r\ninterface ProofOfPayment {\r\n  id: string;\r\n  invoiceNumber: string;\r\n  uploadedFile: File | null;\r\n  paymentMethod: string;\r\n  transactionReference: string;\r\n  amount: number;\r\n  currency: string;\r\n  paymentDate: string;\r\n  notes?: string;\r\n  status: 'pending' | 'approved' | 'rejected';\r\n  submittedAt: string;\r\n}\r\n\r\n// Sample payment data with different types including procurement\r\nconst samplePayments: Payment[] = [\r\n  {\r\n    id: 'PAY-2025-001',\r\n    invoiceNumber: 'INV-2025-001',\r\n    amount: ********,\r\n    currency: 'MWK',\r\n    status: 'paid',\r\n    dueDate: '2025-04-11',\r\n    paidDate: '2025-03-15',\r\n    issueDate: '2025-03-11',\r\n    description: 'Internet Service Provider License - 5 year license for telecommunications services',\r\n    paymentType: 'License Fee',\r\n    clientName: 'Airtel Malawi',\r\n    clientEmail: '<EMAIL>',\r\n    paymentMethod: 'Bank Transfer',\r\n    notes: 'Payment for internet service provider license renewal'\r\n  },\r\n  {\r\n    id: 'PAY-2025-002',\r\n    invoiceNumber: 'INV-2025-002',\r\n    amount: 150000,\r\n    currency: 'MWK',\r\n    status: 'paid',\r\n    dueDate: '2025-02-15',\r\n    paidDate: '2025-02-10',\r\n    issueDate: '2025-01-15',\r\n    description: 'Tender document for procurement - ICT equipment procurement tender documentation',\r\n    paymentType: 'Procurement Fee',\r\n    clientName: 'TechSolutions Ltd',\r\n    clientEmail: '<EMAIL>',\r\n    paymentMethod: 'Mobile Money',\r\n    notes: 'Payment for tender document access and procurement process participation'\r\n  },\r\n  {\r\n    id: 'PAY-2025-003',\r\n    invoiceNumber: 'INV-2025-003',\r\n    amount: 6565000,\r\n    currency: 'MWK',\r\n    status: 'pending',\r\n    dueDate: '2025-01-25',\r\n    issueDate: '2024-12-25',\r\n    description: 'Radio Broadcasting License - Commercial radio broadcasting license for FM frequency',\r\n    paymentType: 'License Fee',\r\n    clientName: 'Dawn FM',\r\n    clientEmail: '<EMAIL>',\r\n    notes: 'Radio broadcasting license for 3 years'\r\n  },\r\n  {\r\n    id: 'PAY-2025-004',\r\n    invoiceNumber: 'INV-2025-004',\r\n    amount: 75000,\r\n    currency: 'MWK',\r\n    status: 'paid',\r\n    dueDate: '2025-03-01',\r\n    paidDate: '2025-02-28',\r\n    issueDate: '2025-02-01',\r\n    description: 'Tender document for procurement - Network infrastructure upgrade tender',\r\n    paymentType: 'Procurement Fee',\r\n    clientName: 'NetworkPro Systems',\r\n    clientEmail: '<EMAIL>',\r\n    paymentMethod: 'Credit Card',\r\n    notes: 'Tender documentation fee for network infrastructure procurement'\r\n  },\r\n  {\r\n    id: 'PAY-2025-005',\r\n    invoiceNumber: 'INV-2025-005',\r\n    amount: 50000000,\r\n    currency: 'MWK',\r\n    status: 'overdue',\r\n    dueDate: '2025-02-01',\r\n    issueDate: '2025-01-01',\r\n    description: 'TV Broadcasting License - Digital terrestrial television broadcasting license',\r\n    paymentType: 'License Fee',\r\n    clientName: 'Crunchyroll TV',\r\n    clientEmail: '<EMAIL>',\r\n    notes: 'TV broadcasting license for digital terrestrial services'\r\n  },\r\n  {\r\n    id: 'PAY-2025-006',\r\n    invoiceNumber: 'INV-2025-006',\r\n    amount: 25000,\r\n    currency: 'MWK',\r\n    status: 'paid',\r\n    dueDate: '2025-01-20',\r\n    paidDate: '2025-01-18',\r\n    issueDate: '2024-12-20',\r\n    description: 'Tender document for procurement - Regulatory compliance software procurement',\r\n    paymentType: 'Procurement Fee',\r\n    clientName: 'ComplianceTech Solutions',\r\n    clientEmail: '<EMAIL>',\r\n    paymentMethod: 'Bank Transfer',\r\n    notes: 'Procurement tender for regulatory compliance management system'\r\n  },\r\n  {\r\n    id: 'PAY-2024-007',\r\n    invoiceNumber: 'INV-2024-007',\r\n    amount: 2500000,\r\n    currency: 'MWK',\r\n    status: 'paid',\r\n    dueDate: '2024-10-31',\r\n    paidDate: '2024-10-25',\r\n    issueDate: '2024-10-01',\r\n    description: 'Mobile Network License - Mobile virtual network operator license',\r\n    paymentType: 'License Fee',\r\n    clientName: 'MobileConnect Ltd',\r\n    clientEmail: '<EMAIL>',\r\n    paymentMethod: 'Bank Transfer',\r\n    notes: 'MVNO license for mobile telecommunications services'\r\n  }\r\n];\r\n\r\nconst paymentTypes = [\r\n  'All Types',\r\n  'License Fee',\r\n  'Procurement Fee',\r\n  'Application Fee',\r\n  'Renewal Fee',\r\n  'Penalty Fee'\r\n];\r\n\r\nconst CustomerPaymentsPage = () => {\r\n  const { isAuthenticated} = useAuth();\r\n  const router = useRouter();\r\n\r\n  // Tab state\r\n  const [activeTab, setActiveTab] = useState<'payments' | 'upload'>('payments');\r\n\r\n  const [payments, setPayments] = useState<Payment[]>([]);\r\n  const [filteredPayments, setFilteredPayments] = useState<Payment[]>([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [error, setError] = useState('');\r\n\r\n  // Filter states\r\n  const [statusFilter, setStatusFilter] = useState('');\r\n  const [typeFilter, setTypeFilter] = useState('');\r\n  const [dateFilter, setDateFilter] = useState('');\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n\r\n  // Pagination\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const itemsPerPage = 10;\r\n\r\n  // Proof of Payment Upload States\r\n  const [uploadFormData, setUploadFormData] = useState({\r\n    invoiceNumber: '',\r\n    paymentMethod: '',\r\n    transactionReference: '',\r\n    amount: '',\r\n    currency: 'MWK',\r\n    paymentDate: '',\r\n    notes: ''\r\n  });\r\n  const [uploadedFile, setUploadedFile] = useState<File | null>(null);\r\n  const [uploadLoading, setUploadLoading] = useState(false);\r\n  const [uploadError, setUploadError] = useState('');\r\n  const [uploadSuccess, setUploadSuccess] = useState('');\r\n\r\n  // Redirect to customer login if not authenticated\r\n  useEffect(() => {\r\n    if (!isAuthenticated) {\r\n      router.push('/customer/auth/login');\r\n    }\r\n  }, [isAuthenticated, router]);\r\n\r\n  // Fetch payments data\r\n  useEffect(() => {\r\n    const fetchPayments = async () => {\r\n      if (!isAuthenticated) return;\r\n\r\n      try {\r\n        setIsLoading(true);\r\n        setError('');\r\n\r\n        // In a real implementation, this would be an API call\r\n        // const response = await customerApi.getPayments();\r\n        \r\n        // For now, use sample data\r\n        setPayments(samplePayments);\r\n        setFilteredPayments(samplePayments);\r\n\r\n      } catch (err) {\r\n        console.error('Error fetching payments:', err);\r\n        setError('Failed to load payments. Please try refreshing the page.');\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchPayments();\r\n  }, [isAuthenticated]);\r\n\r\n  // Apply filters\r\n  useEffect(() => {\r\n    let filtered = payments;\r\n\r\n    // Status filter\r\n    if (statusFilter) {\r\n      filtered = filtered.filter(payment => payment.status === statusFilter);\r\n    }\r\n\r\n    // Type filter\r\n    if (typeFilter && typeFilter !== 'All Types') {\r\n      filtered = filtered.filter(payment => payment.paymentType === typeFilter);\r\n    }\r\n\r\n    // Date filter\r\n    if (dateFilter) {\r\n      const now = new Date();\r\n      const filterDate = new Date();\r\n      \r\n      switch (dateFilter) {\r\n        case 'last-30':\r\n          filterDate.setDate(now.getDate() - 30);\r\n          break;\r\n        case 'last-90':\r\n          filterDate.setDate(now.getDate() - 90);\r\n          break;\r\n        case 'last-year':\r\n          filterDate.setFullYear(now.getFullYear() - 1);\r\n          break;\r\n        default:\r\n          filterDate.setFullYear(1970); // Show all\r\n      }\r\n      \r\n      filtered = filtered.filter(payment => \r\n        new Date(payment.issueDate) >= filterDate\r\n      );\r\n    }\r\n\r\n    // Search filter\r\n    if (searchTerm) {\r\n      const term = searchTerm.toLowerCase();\r\n      filtered = filtered.filter(payment =>\r\n        payment.invoiceNumber.toLowerCase().includes(term) ||\r\n        payment.clientName.toLowerCase().includes(term) ||\r\n        payment.description.toLowerCase().includes(term) ||\r\n        payment.paymentType.toLowerCase().includes(term)\r\n      );\r\n    }\r\n\r\n    setFilteredPayments(filtered);\r\n    setCurrentPage(1); // Reset to first page when filters change\r\n  }, [payments, statusFilter, typeFilter, dateFilter, searchTerm]);\r\n\r\n  const getStatusColor = (status: string) => {\r\n    switch (status) {\r\n      case 'paid': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';\r\n      case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';\r\n      case 'overdue': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';\r\n      case 'cancelled': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';\r\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';\r\n    }\r\n  };\r\n\r\n  const getTypeColor = (type: string) => {\r\n    switch (type) {\r\n      case 'License Fee': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';\r\n      case 'Procurement Fee': return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';\r\n      case 'Application Fee': return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400';\r\n      case 'Renewal Fee': return 'bg-teal-100 text-teal-800 dark:bg-teal-900/20 dark:text-teal-400';\r\n      case 'Penalty Fee': return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';\r\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';\r\n    }\r\n  };\r\n\r\n  const formatDate = (dateString: string) => {\r\n    return new Date(dateString).toLocaleDateString('en-US', {\r\n      year: 'numeric',\r\n      month: 'short',\r\n      day: 'numeric'\r\n    });\r\n  };\r\n\r\n  const formatAmount = (amount: number, currency: string) => {\r\n    return `${currency} ${amount.toLocaleString()}`;\r\n  };\r\n\r\n  // Pagination calculations\r\n  const totalPages = Math.ceil(filteredPayments.length / itemsPerPage);\r\n  const startIndex = (currentPage - 1) * itemsPerPage;\r\n  const endIndex = startIndex + itemsPerPage;\r\n  const currentPayments = filteredPayments.slice(startIndex, endIndex);\r\n\r\n  const clearFilters = () => {\r\n    setStatusFilter('');\r\n    setTypeFilter('');\r\n    setDateFilter('');\r\n    setSearchTerm('');\r\n  };\r\n\r\n  // Handle upload form changes\r\n  const handleUploadFormChange = (field: string, value: string) => {\r\n    setUploadFormData(prev => ({ ...prev, [field]: value }));\r\n    if (uploadError) setUploadError('');\r\n    if (uploadSuccess) setUploadSuccess('');\r\n  };\r\n\r\n  // Handle file upload\r\n  const handleFileUpload = (file: File | null) => {\r\n    setUploadedFile(file);\r\n    if (uploadError) setUploadError('');\r\n    if (uploadSuccess) setUploadSuccess('');\r\n  };\r\n\r\n  // Handle proof of payment submission\r\n  const handleProofOfPaymentSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    \r\n    if (!uploadedFile) {\r\n      setUploadError('Please upload a proof of payment document');\r\n      return;\r\n    }\r\n\r\n    if (!uploadFormData.invoiceNumber || !uploadFormData.paymentMethod || \r\n        !uploadFormData.transactionReference || !uploadFormData.amount || \r\n        !uploadFormData.paymentDate) {\r\n      setUploadError('Please fill in all required fields');\r\n      return;\r\n    }\r\n\r\n    setUploadLoading(true);\r\n    setUploadError('');\r\n    setUploadSuccess('');\r\n\r\n    try {\r\n      // In a real implementation, you would upload the file and form data to your API\r\n      console.log('Uploading proof of payment:', {\r\n        ...uploadFormData,\r\n        file: uploadedFile\r\n      });\r\n\r\n      // Simulate API call\r\n      await new Promise(resolve => setTimeout(resolve, 2000));\r\n\r\n      // Reset form\r\n      setUploadFormData({\r\n        invoiceNumber: '',\r\n        paymentMethod: '',\r\n        transactionReference: '',\r\n        amount: '',\r\n        currency: 'MWK',\r\n        paymentDate: '',\r\n        notes: ''\r\n      });\r\n      setUploadedFile(null);\r\n      setUploadSuccess('Proof of payment uploaded successfully! It will be reviewed within 1-2 business days.');\r\n      \r\n      // Auto-hide success message after 5 seconds\r\n      setTimeout(() => {\r\n        setUploadSuccess('');\r\n      }, 5000);\r\n\r\n    } catch (err) {\r\n      console.error('Error uploading proof of payment:', err);\r\n      setUploadError('Failed to upload proof of payment. Please try again.');\r\n    } finally {\r\n      setUploadLoading(false);\r\n    }\r\n  };\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <CustomerLayout>\r\n        <div className=\"flex items-center justify-center min-h-96\">\r\n          <Loader message=\"Loading payments...\" />\r\n        </div>\r\n      </CustomerLayout>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <CustomerLayout>\r\n        <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-300 px-4 py-3 rounded mb-6\">\r\n          <p>{error}</p>\r\n          <button\r\n            type=\"button\"\r\n            onClick={() => window.location.reload()}\r\n            className=\"mt-2 text-sm underline hover:no-underline\"\r\n          >\r\n            Try again\r\n          </button>\r\n        </div>\r\n      </CustomerLayout>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <CustomerLayout>\r\n      <div className=\"max-w-7xl mx-auto\">\r\n        {/* Header */}\r\n        <div className=\"mb-6\">\r\n          <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0\">\r\n            <div>\r\n              <h1 className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">Payments</h1>\r\n              <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                View all your payment records and upload proof of payment documents.\r\n              </p>\r\n            </div>\r\n            <div className=\"flex items-center space-x-3\">\r\n              <span className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                Total: {filteredPayments.length} payment{filteredPayments.length !== 1 ? 's' : ''}\r\n              </span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Tabs */}\r\n        <div className=\"mb-6\">\r\n          <nav className=\"-mb-px flex space-x-8\">\r\n            <button\r\n              onClick={() => setActiveTab('payments')}\r\n              className={`py-2 px-1 border-b-2 font-medium text-sm ${\r\n                activeTab === 'payments'\r\n                  ? 'border-primary text-primary'\r\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\r\n              }`}\r\n            >\r\n              <i className=\"ri-file-list-line mr-2\"></i>\r\n              Payment History\r\n            </button>\r\n            <button\r\n              onClick={() => setActiveTab('upload')}\r\n              className={`py-2 px-1 border-b-2 font-medium text-sm ${\r\n                activeTab === 'upload'\r\n                  ? 'border-primary text-primary'\r\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\r\n              }`}\r\n            >\r\n              <i className=\"ri-upload-2-line mr-2\"></i>\r\n              Upload Proof of Payment\r\n            </button>\r\n          </nav>\r\n        </div>\r\n\r\n        {/* Tab Content */}\r\n        {activeTab === 'payments' && (\r\n          <>\r\n            {/* Search and Filters */}\r\n            <div className=\"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md mb-6\">\r\n          <div className=\"px-4 py-5 sm:p-6\">\r\n            {/* Search Bar */}\r\n            <div className=\"mb-4\">\r\n              <label htmlFor=\"search\" className=\"sr-only\">Search payments</label>\r\n              <div className=\"relative\">\r\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                  <i className=\"ri-search-line text-gray-400\"></i>\r\n                </div>\r\n                <input\r\n                  id=\"search\"\r\n                  type=\"text\"\r\n                  value={searchTerm}\r\n                  onChange={(e) => setSearchTerm(e.target.value)}\r\n                  className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-sm text-gray-900 dark:text-gray-100\"\r\n                  placeholder=\"Search by invoice number, description, or payment type...\"\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            {/* Filter Controls */}\r\n            <div className=\"grid grid-cols-1 gap-y-4 sm:grid-cols-4 sm:gap-x-6\">\r\n              <div>\r\n                <label htmlFor=\"status-filter\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">Status</label>\r\n                <select\r\n                  id=\"status-filter\"\r\n                  value={statusFilter}\r\n                  onChange={(e) => setStatusFilter(e.target.value)}\r\n                  className=\"mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 p-2 shadow-sm focus:ring-primary focus:border-primary sm:text-sm\"\r\n                >\r\n                  <option value=\"\">All Statuses</option>\r\n                  <option value=\"paid\">Paid</option>\r\n                  <option value=\"pending\">Pending</option>\r\n                  <option value=\"overdue\">Overdue</option>\r\n                  <option value=\"cancelled\">Cancelled</option>\r\n                </select>\r\n              </div>\r\n              <div>\r\n                <label htmlFor=\"type-filter\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">Payment Type</label>\r\n                <select\r\n                  id=\"type-filter\"\r\n                  value={typeFilter}\r\n                  onChange={(e) => setTypeFilter(e.target.value)}\r\n                  className=\"mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 p-2 shadow-sm focus:ring-primary focus:border-primary sm:text-sm\"\r\n                >\r\n                  {paymentTypes.map(type => (\r\n                    <option key={type} value={type}>{type}</option>\r\n                  ))}\r\n                </select>\r\n              </div>\r\n              <div>\r\n                <label htmlFor=\"date-filter\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">Date Range</label>\r\n                <select\r\n                  id=\"date-filter\"\r\n                  value={dateFilter}\r\n                  onChange={(e) => setDateFilter(e.target.value)}\r\n                  className=\"mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 p-2 shadow-sm focus:ring-primary focus:border-primary sm:text-sm\"\r\n                >\r\n                  <option value=\"\">All Time</option>\r\n                  <option value=\"last-30\">Last 30 Days</option>\r\n                  <option value=\"last-90\">Last 90 Days</option>\r\n                  <option value=\"last-year\">Last Year</option>\r\n                </select>\r\n              </div>\r\n              <div className=\"flex items-end\">\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={clearFilters}\r\n                  className=\"inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 dark:text-gray-400 hover:text-white bg-white dark:bg-gray-700 border border-primary rounded-full hover:bg-red-700 dark:hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors duration-300\"\r\n                >\r\n                  Clear Filters\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Payments Table */}\r\n        <div className=\"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md\">\r\n          <div className=\"px-4 py-5 sm:p-6\">\r\n            <div className=\"flex flex-col\">\r\n              <div className=\"-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8\">\r\n                <div className=\"py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8\">\r\n                  <div className=\"overflow-hidden border border-gray-200 dark:border-gray-700 sm:rounded-lg\">\r\n                    <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\r\n                      <thead className=\"bg-gray-50 dark:bg-gray-900\">\r\n                        <tr>\r\n                          <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                            Invoice #\r\n                          </th>\r\n                          <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                            Client\r\n                          </th>\r\n                          <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                            Payment Type\r\n                          </th>\r\n                          <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                            Description\r\n                          </th>\r\n                          <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                            Issue Date\r\n                          </th>\r\n                          <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                            Due Date\r\n                          </th>\r\n                          <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                            Amount\r\n                          </th>\r\n                          <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                            Status\r\n                          </th>\r\n                          <th scope=\"col\" className=\"relative px-6 py-3\">\r\n                            <span className=\"sr-only\">Actions</span>\r\n                          </th>\r\n                        </tr>\r\n                      </thead>\r\n                      <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\r\n                        {currentPayments.length === 0 ? (\r\n                          <tr>\r\n                            <td colSpan={9} className=\"px-6 py-12 text-center\">\r\n                              <div className=\"text-gray-500 dark:text-gray-400\">\r\n                                <i className=\"ri-file-list-line text-4xl mb-4\"></i>\r\n                                <p className=\"text-lg font-medium\">No payments found</p>\r\n                                <p className=\"text-sm\">Try adjusting your search criteria or filters.</p>\r\n                              </div>\r\n                            </td>\r\n                          </tr>\r\n                        ) : (\r\n                          currentPayments.map((payment) => (\r\n                            <tr key={payment.id} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\r\n                              <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">{payment.invoiceNumber}</div>\r\n                              </td>\r\n                              <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                <div className=\"flex items-center\">\r\n                                  <div>\r\n                                    <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">{payment.clientName}</div>\r\n                                    <div className=\"text-sm text-gray-500 dark:text-gray-400\">{payment.clientEmail}</div>\r\n                                  </div>\r\n                                </div>\r\n                              </td>\r\n                              <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getTypeColor(payment.paymentType)}`}>\r\n                                  {payment.paymentType}\r\n                                </span>\r\n                              </td>\r\n                              <td className=\"px-6 py-4\">\r\n                                <div className=\"text-sm text-gray-900 dark:text-gray-100 max-w-xs truncate\" title={payment.description}>\r\n                                  {payment.description}\r\n                                </div>\r\n                              </td>\r\n                              <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                <div className=\"text-sm text-gray-900 dark:text-gray-100\">{formatDate(payment.issueDate)}</div>\r\n                              </td>\r\n                              <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                <div className=\"text-sm text-gray-900 dark:text-gray-100\">{formatDate(payment.dueDate)}</div>\r\n                              </td>\r\n                              <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                                  {formatAmount(payment.amount, payment.currency)}\r\n                                </div>\r\n                              </td>\r\n                              <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(payment.status)}`}>\r\n                                  {payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}\r\n                                </span>\r\n                              </td>\r\n                              <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\r\n                                <button className=\"text-primary hover:text-primary-dark mr-3 transition-colors\">\r\n                                  View\r\n                                </button>\r\n                                <button className=\"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 transition-colors\">\r\n                                  Download\r\n                                </button>\r\n                              </td>\r\n                            </tr>\r\n                          ))\r\n                        )}\r\n                      </tbody>\r\n                    </table>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Pagination */}\r\n            {totalPages > 1 && (\r\n              <div className=\"bg-white dark:bg-gray-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 sm:px-6\">\r\n                <div className=\"flex-1 flex justify-between sm:hidden\">\r\n                  <button\r\n                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}\r\n                    disabled={currentPage === 1}\r\n                    className=\"relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                  >\r\n                    Previous\r\n                  </button>\r\n                  <button\r\n                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}\r\n                    disabled={currentPage === totalPages}\r\n                    className=\"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                  >\r\n                    Next\r\n                  </button>\r\n                </div>\r\n                <div className=\"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between\">\r\n                  <div>\r\n                    <p className=\"text-sm text-gray-700 dark:text-gray-300\">\r\n                      Showing <span className=\"font-medium\">{startIndex + 1}</span> to{' '}\r\n                      <span className=\"font-medium\">{Math.min(endIndex, filteredPayments.length)}</span> of{' '}\r\n                      <span className=\"font-medium\">{filteredPayments.length}</span> payments\r\n                    </p>\r\n                  </div>\r\n                  <div>\r\n                    <nav className=\"relative z-0 inline-flex rounded-md shadow-sm -space-x-px\" aria-label=\"Pagination\">\r\n                      <button\r\n                        onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}\r\n                        disabled={currentPage === 1}\r\n                        className=\"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                      >\r\n                        <span className=\"sr-only\">Previous</span>\r\n                        <i className=\"ri-arrow-left-s-line\"></i>\r\n                      </button>\r\n                      \r\n                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {\r\n                        let pageNum;\r\n                        if (totalPages <= 5) {\r\n                          pageNum = i + 1;\r\n                        } else if (currentPage <= 3) {\r\n                          pageNum = i + 1;\r\n                        } else if (currentPage >= totalPages - 2) {\r\n                          pageNum = totalPages - 4 + i;\r\n                        } else {\r\n                          pageNum = currentPage - 2 + i;\r\n                        }\r\n                        \r\n                        return (\r\n                          <button\r\n                            key={pageNum}\r\n                            onClick={() => setCurrentPage(pageNum)}\r\n                            className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${\r\n                              currentPage === pageNum\r\n                                ? 'z-10 bg-primary border-primary text-white'\r\n                                : 'bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600'\r\n                            }`}\r\n                          >\r\n                            {pageNum}\r\n                          </button>\r\n                        );\r\n                      })}\r\n                      \r\n                      <button\r\n                        onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}\r\n                        disabled={currentPage === totalPages}\r\n                        className=\"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                      >\r\n                        <span className=\"sr-only\">Next</span>\r\n                        <i className=\"ri-arrow-right-s-line\"></i>\r\n                      </button>\r\n                    </nav>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n          </>\r\n        )}\r\n\r\n        {/* Upload Proof of Payment Tab */}\r\n        {activeTab === 'upload' && (\r\n          <div className=\"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md\">\r\n            <div className=\"px-4 py-5 sm:p-6\">\r\n              <div className=\"max-w-2xl mx-auto\">\r\n                <div className=\"mb-6\">\r\n                  <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">Upload Proof of Payment</h3>\r\n                  <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n                    Upload your payment receipt or proof of payment document. Our team will review and update your payment status within 1-2 business days.\r\n                  </p>\r\n                </div>\r\n\r\n                {/* Form Messages */}\r\n                <FormMessages error={uploadError} success={uploadSuccess} />\r\n\r\n                <form onSubmit={handleProofOfPaymentSubmit} className=\"space-y-6\">\r\n                  {/* Invoice Number */}\r\n                  <div>\r\n                    <label htmlFor=\"invoiceNumber\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                      Invoice Number <span className=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <input\r\n                      type=\"text\"\r\n                      id=\"invoiceNumber\"\r\n                      value={uploadFormData.invoiceNumber}\r\n                      onChange={(e) => handleUploadFormChange('invoiceNumber', e.target.value)}\r\n                      className=\"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary\"\r\n                      placeholder=\"e.g., INV-2025-001\"\r\n                      required\r\n                    />\r\n                  </div>\r\n\r\n                  {/* Payment Method */}\r\n                  <div>\r\n                    <label htmlFor=\"paymentMethod\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                      Payment Method <span className=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <select\r\n                      id=\"paymentMethod\"\r\n                      value={uploadFormData.paymentMethod}\r\n                      onChange={(e) => handleUploadFormChange('paymentMethod', e.target.value)}\r\n                      className=\"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary\"\r\n                      required\r\n                    >\r\n                      <option value=\"\">Select payment method</option>\r\n                      <option value=\"Bank Transfer\">Bank Transfer</option>\r\n                      <option value=\"Mobile Money\">Mobile Money</option>\r\n                      <option value=\"Credit Card\">Credit Card</option>\r\n                      <option value=\"Cash\">Cash</option>\r\n                    </select>\r\n                  </div>\r\n\r\n                  {/* Transaction Reference */}\r\n                  <div>\r\n                    <label htmlFor=\"transactionReference\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                      Transaction Reference <span className=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <input\r\n                      type=\"text\"\r\n                      id=\"transactionReference\"\r\n                      value={uploadFormData.transactionReference}\r\n                      onChange={(e) => handleUploadFormChange('transactionReference', e.target.value)}\r\n                      className=\"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary\"\r\n                      placeholder=\"e.g., TXN123456789\"\r\n                      required\r\n                    />\r\n                  </div>\r\n\r\n                  {/* Amount and Currency */}\r\n                  <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-3\">\r\n                    <div className=\"sm:col-span-2\">\r\n                      <label htmlFor=\"amount\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                        Amount <span className=\"text-red-500\">*</span>\r\n                      </label>\r\n                      <input\r\n                        type=\"number\"\r\n                        id=\"amount\"\r\n                        value={uploadFormData.amount}\r\n                        onChange={(e) => handleUploadFormChange('amount', e.target.value)}\r\n                        className=\"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary\"\r\n                        placeholder=\"0.00\"\r\n                        step=\"0.01\"\r\n                        min=\"0\"\r\n                        required\r\n                      />\r\n                    </div>\r\n                    <div>\r\n                      <label htmlFor=\"currency\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                        Currency <span className=\"text-red-500\">*</span>\r\n                      </label>\r\n                      <select\r\n                        id=\"currency\"\r\n                        value={uploadFormData.currency}\r\n                        onChange={(e) => handleUploadFormChange('currency', e.target.value)}\r\n                        className=\"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary\"\r\n                        required\r\n                      >\r\n                        <option value=\"MWK\">MWK</option>\r\n                        <option value=\"USD\">USD</option>\r\n                        <option value=\"EUR\">EUR</option>\r\n                      </select>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Payment Date */}\r\n                  <div>\r\n                    <label htmlFor=\"paymentDate\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                      Payment Date <span className=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <input\r\n                      type=\"date\"\r\n                      id=\"paymentDate\"\r\n                      value={uploadFormData.paymentDate}\r\n                      onChange={(e) => handleUploadFormChange('paymentDate', e.target.value)}\r\n                      className=\"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary\"\r\n                      required\r\n                    />\r\n                  </div>\r\n\r\n                  {/* File Upload */}\r\n                  <FileUpload\r\n                    id=\"proof-of-payment\"\r\n                    label=\"Proof of Payment Document\"\r\n                    accept=\".pdf,.jpg,.jpeg,.png\"\r\n                    maxSize={5}\r\n                    required\r\n                    value={uploadedFile}\r\n                    onChange={handleFileUpload}\r\n                    description=\"Upload a clear image or PDF of your payment receipt (max 5MB)\"\r\n                  />\r\n\r\n                  {/* Notes */}\r\n                  <div>\r\n                    <label htmlFor=\"notes\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                      Additional Notes\r\n                    </label>\r\n                    <textarea\r\n                      id=\"notes\"\r\n                      value={uploadFormData.notes}\r\n                      onChange={(e) => handleUploadFormChange('notes', e.target.value)}\r\n                      rows={3}\r\n                      className=\"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary\"\r\n                      placeholder=\"Any additional information about this payment...\"\r\n                    />\r\n                  </div>\r\n\r\n                  {/* Submit Button */}\r\n                  <div className=\"flex justify-end\">\r\n                    <button\r\n                      type=\"submit\"\r\n                      disabled={uploadLoading}\r\n                      className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                    >\r\n                      {uploadLoading ? (\r\n                        <>\r\n                          <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                            <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                          </svg>\r\n                          Uploading...\r\n                        </>\r\n                      ) : (\r\n                        <>\r\n                          <i className=\"ri-upload-2-line mr-2\"></i>\r\n                          Upload Proof of Payment\r\n                        </>\r\n                      )}\r\n                    </button>\r\n                  </div>\r\n                </form>\r\n\r\n                {/* Help Text */}\r\n                <div className=\"mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md\">\r\n                  <div className=\"flex\">\r\n                    <div className=\"flex-shrink-0\">\r\n                      <i className=\"ri-information-line text-blue-400\"></i>\r\n                    </div>\r\n                    <div className=\"ml-3\">\r\n                      <h4 className=\"text-sm font-medium text-blue-800 dark:text-blue-200\">\r\n                        Important Information\r\n                      </h4>\r\n                      <div className=\"mt-2 text-sm text-blue-700 dark:text-blue-300\">\r\n                        <ul className=\"list-disc list-inside space-y-1\">\r\n                          <li>Ensure your payment document is clear and readable</li>\r\n                          <li>Include all relevant transaction details</li>\r\n                          <li>Processing time: 1-2 business days</li>\r\n                          <li>You'll receive an email notification once reviewed</li>\r\n                        </ul>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </CustomerLayout>\r\n  );\r\n};\r\n\r\nexport default CustomerPaymentsPage;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAyCA,iEAAiE;AACjE,MAAM,iBAA4B;IAChC;QACE,IAAI;QACJ,eAAe;QACf,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;QACV,WAAW;QACX,aAAa;QACb,aAAa;QACb,YAAY;QACZ,aAAa;QACb,eAAe;QACf,OAAO;IACT;IACA;QACE,IAAI;QACJ,eAAe;QACf,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;QACV,WAAW;QACX,aAAa;QACb,aAAa;QACb,YAAY;QACZ,aAAa;QACb,eAAe;QACf,OAAO;IACT;IACA;QACE,IAAI;QACJ,eAAe;QACf,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,SAAS;QACT,WAAW;QACX,aAAa;QACb,aAAa;QACb,YAAY;QACZ,aAAa;QACb,OAAO;IACT;IACA;QACE,IAAI;QACJ,eAAe;QACf,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;QACV,WAAW;QACX,aAAa;QACb,aAAa;QACb,YAAY;QACZ,aAAa;QACb,eAAe;QACf,OAAO;IACT;IACA;QACE,IAAI;QACJ,eAAe;QACf,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,SAAS;QACT,WAAW;QACX,aAAa;QACb,aAAa;QACb,YAAY;QACZ,aAAa;QACb,OAAO;IACT;IACA;QACE,IAAI;QACJ,eAAe;QACf,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;QACV,WAAW;QACX,aAAa;QACb,aAAa;QACb,YAAY;QACZ,aAAa;QACb,eAAe;QACf,OAAO;IACT;IACA;QACE,IAAI;QACJ,eAAe;QACf,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;QACV,WAAW;QACX,aAAa;QACb,aAAa;QACb,YAAY;QACZ,aAAa;QACb,eAAe;QACf,OAAO;IACT;CACD;AAED,MAAM,eAAe;IACnB;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,uBAAuB;IAC3B,MAAM,EAAE,eAAe,EAAC,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACjC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,YAAY;IACZ,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAElE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,gBAAgB;IAChB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,aAAa;IACb,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,eAAe;IAErB,iCAAiC;IACjC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACnD,eAAe;QACf,eAAe;QACf,sBAAsB;QACtB,QAAQ;QACR,UAAU;QACV,aAAa;QACb,OAAO;IACT;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,kDAAkD;IAClD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,iBAAiB;YACpB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAiB;KAAO;IAE5B,sBAAsB;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,IAAI,CAAC,iBAAiB;YAEtB,IAAI;gBACF,aAAa;gBACb,SAAS;gBAET,sDAAsD;gBACtD,oDAAoD;gBAEpD,2BAA2B;gBAC3B,YAAY;gBACZ,oBAAoB;YAEtB,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,SAAS;YACX,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG;QAAC;KAAgB;IAEpB,gBAAgB;IAChB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;QAEf,gBAAgB;QAChB,IAAI,cAAc;YAChB,WAAW,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,MAAM,KAAK;QAC3D;QAEA,cAAc;QACd,IAAI,cAAc,eAAe,aAAa;YAC5C,WAAW,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,WAAW,KAAK;QAChE;QAEA,cAAc;QACd,IAAI,YAAY;YACd,MAAM,MAAM,IAAI;YAChB,MAAM,aAAa,IAAI;YAEvB,OAAQ;gBACN,KAAK;oBACH,WAAW,OAAO,CAAC,IAAI,OAAO,KAAK;oBACnC;gBACF,KAAK;oBACH,WAAW,OAAO,CAAC,IAAI,OAAO,KAAK;oBACnC;gBACF,KAAK;oBACH,WAAW,WAAW,CAAC,IAAI,WAAW,KAAK;oBAC3C;gBACF;oBACE,WAAW,WAAW,CAAC,OAAO,WAAW;YAC7C;YAEA,WAAW,SAAS,MAAM,CAAC,CAAA,UACzB,IAAI,KAAK,QAAQ,SAAS,KAAK;QAEnC;QAEA,gBAAgB;QAChB,IAAI,YAAY;YACd,MAAM,OAAO,WAAW,WAAW;YACnC,WAAW,SAAS,MAAM,CAAC,CAAA,UACzB,QAAQ,aAAa,CAAC,WAAW,GAAG,QAAQ,CAAC,SAC7C,QAAQ,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,SAC1C,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,SAC3C,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC;QAE/C;QAEA,oBAAoB;QACpB,eAAe,IAAI,0CAA0C;IAC/D,GAAG;QAAC;QAAU;QAAc;QAAY;QAAY;KAAW;IAE/D,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAmB,OAAO;YAC/B,KAAK;gBAAmB,OAAO;YAC/B,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAe,OAAO;YAC3B;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,eAAe,CAAC,QAAgB;QACpC,OAAO,GAAG,SAAS,CAAC,EAAE,OAAO,cAAc,IAAI;IACjD;IAEA,0BAA0B;IAC1B,MAAM,aAAa,KAAK,IAAI,CAAC,iBAAiB,MAAM,GAAG;IACvD,MAAM,aAAa,CAAC,cAAc,CAAC,IAAI;IACvC,MAAM,WAAW,aAAa;IAC9B,MAAM,kBAAkB,iBAAiB,KAAK,CAAC,YAAY;IAE3D,MAAM,eAAe;QACnB,gBAAgB;QAChB,cAAc;QACd,cAAc;QACd,cAAc;IAChB;IAEA,6BAA6B;IAC7B,MAAM,yBAAyB,CAAC,OAAe;QAC7C,kBAAkB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QACtD,IAAI,aAAa,eAAe;QAChC,IAAI,eAAe,iBAAiB;IACtC;IAEA,qBAAqB;IACrB,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;QAChB,IAAI,aAAa,eAAe;QAChC,IAAI,eAAe,iBAAiB;IACtC;IAEA,qCAAqC;IACrC,MAAM,6BAA6B,OAAO;QACxC,EAAE,cAAc;QAEhB,IAAI,CAAC,cAAc;YACjB,eAAe;YACf;QACF;QAEA,IAAI,CAAC,eAAe,aAAa,IAAI,CAAC,eAAe,aAAa,IAC9D,CAAC,eAAe,oBAAoB,IAAI,CAAC,eAAe,MAAM,IAC9D,CAAC,eAAe,WAAW,EAAE;YAC/B,eAAe;YACf;QACF;QAEA,iBAAiB;QACjB,eAAe;QACf,iBAAiB;QAEjB,IAAI;YACF,gFAAgF;YAChF,QAAQ,GAAG,CAAC,+BAA+B;gBACzC,GAAG,cAAc;gBACjB,MAAM;YACR;YAEA,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,aAAa;YACb,kBAAkB;gBAChB,eAAe;gBACf,eAAe;gBACf,sBAAsB;gBACtB,QAAQ;gBACR,UAAU;gBACV,aAAa;gBACb,OAAO;YACT;YACA,gBAAgB;YAChB,iBAAiB;YAEjB,4CAA4C;YAC5C,WAAW;gBACT,iBAAiB;YACnB,GAAG;QAEL,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,qCAAqC;YACnD,eAAe;QACjB,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC,gJAAA,CAAA,UAAc;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,4HAAA,CAAA,UAAM;oBAAC,SAAQ;;;;;;;;;;;;;;;;IAIxB;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC,gJAAA,CAAA,UAAc;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;kCAAG;;;;;;kCACJ,8OAAC;wBACC,MAAK;wBACL,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;wBACrC,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,8OAAC,gJAAA,CAAA,UAAc;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA0D;;;;;;kDACxE,8OAAC;wCAAE,WAAU;kDAA2C;;;;;;;;;;;;0CAI1D,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;;wCAA2C;wCACjD,iBAAiB,MAAM;wCAAC;wCAAS,iBAAiB,MAAM,KAAK,IAAI,MAAM;;;;;;;;;;;;;;;;;;;;;;;8BAOvF,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,aACV,gCACA,0HACJ;;kDAEF,8OAAC;wCAAE,WAAU;;;;;;oCAA6B;;;;;;;0CAG5C,8OAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,WACV,gCACA,0HACJ;;kDAEF,8OAAC;wCAAE,WAAU;;;;;;oCAA4B;;;;;;;;;;;;;;;;;;gBAO9C,cAAc,4BACb;;sCAEE,8OAAC;4BAAI,WAAU;sCACjB,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,SAAQ;gDAAS,WAAU;0DAAU;;;;;;0DAC5C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAE,WAAU;;;;;;;;;;;kEAEf,8OAAC;wDACC,IAAG;wDACH,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC7C,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;kDAMlB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAgB,WAAU;kEAA6D;;;;;;kEACtG,8OAAC;wDACC,IAAG;wDACH,OAAO;wDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wDAC/C,WAAU;;0EAEV,8OAAC;gEAAO,OAAM;0EAAG;;;;;;0EACjB,8OAAC;gEAAO,OAAM;0EAAO;;;;;;0EACrB,8OAAC;gEAAO,OAAM;0EAAU;;;;;;0EACxB,8OAAC;gEAAO,OAAM;0EAAU;;;;;;0EACxB,8OAAC;gEAAO,OAAM;0EAAY;;;;;;;;;;;;;;;;;;0DAG9B,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAc,WAAU;kEAA6D;;;;;;kEACpG,8OAAC;wDACC,IAAG;wDACH,OAAO;wDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC7C,WAAU;kEAET,aAAa,GAAG,CAAC,CAAA,qBAChB,8OAAC;gEAAkB,OAAO;0EAAO;+DAApB;;;;;;;;;;;;;;;;0DAInB,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAc,WAAU;kEAA6D;;;;;;kEACpG,8OAAC;wDACC,IAAG;wDACH,OAAO;wDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC7C,WAAU;;0EAEV,8OAAC;gEAAO,OAAM;0EAAG;;;;;;0EACjB,8OAAC;gEAAO,OAAM;0EAAU;;;;;;0EACxB,8OAAC;gEAAO,OAAM;0EAAU;;;;;;0EACxB,8OAAC;gEAAO,OAAM;0EAAY;;;;;;;;;;;;;;;;;;0DAG9B,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAST,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAM,WAAU;;0EACf,8OAAC;gEAAM,WAAU;0EACf,cAAA,8OAAC;;sFACC,8OAAC;4EAAG,OAAM;4EAAM,WAAU;sFAAoG;;;;;;sFAG9H,8OAAC;4EAAG,OAAM;4EAAM,WAAU;sFAAoG;;;;;;sFAG9H,8OAAC;4EAAG,OAAM;4EAAM,WAAU;sFAAoG;;;;;;sFAG9H,8OAAC;4EAAG,OAAM;4EAAM,WAAU;sFAAoG;;;;;;sFAG9H,8OAAC;4EAAG,OAAM;4EAAM,WAAU;sFAAoG;;;;;;sFAG9H,8OAAC;4EAAG,OAAM;4EAAM,WAAU;sFAAoG;;;;;;sFAG9H,8OAAC;4EAAG,OAAM;4EAAM,WAAU;sFAAoG;;;;;;sFAG9H,8OAAC;4EAAG,OAAM;4EAAM,WAAU;sFAAoG;;;;;;sFAG9H,8OAAC;4EAAG,OAAM;4EAAM,WAAU;sFACxB,cAAA,8OAAC;gFAAK,WAAU;0FAAU;;;;;;;;;;;;;;;;;;;;;;0EAIhC,8OAAC;gEAAM,WAAU;0EACd,gBAAgB,MAAM,KAAK,kBAC1B,8OAAC;8EACC,cAAA,8OAAC;wEAAG,SAAS;wEAAG,WAAU;kFACxB,cAAA,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAE,WAAU;;;;;;8FACb,8OAAC;oFAAE,WAAU;8FAAsB;;;;;;8FACnC,8OAAC;oFAAE,WAAU;8FAAU;;;;;;;;;;;;;;;;;;;;;2EAK7B,gBAAgB,GAAG,CAAC,CAAC,wBACnB,8OAAC;wEAAoB,WAAU;;0FAC7B,8OAAC;gFAAG,WAAU;0FACZ,cAAA,8OAAC;oFAAI,WAAU;8FAAwD,QAAQ,aAAa;;;;;;;;;;;0FAE9F,8OAAC;gFAAG,WAAU;0FACZ,cAAA,8OAAC;oFAAI,WAAU;8FACb,cAAA,8OAAC;;0GACC,8OAAC;gGAAI,WAAU;0GAAwD,QAAQ,UAAU;;;;;;0GACzF,8OAAC;gGAAI,WAAU;0GAA4C,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;;;;0FAIpF,8OAAC;gFAAG,WAAU;0FACZ,cAAA,8OAAC;oFAAK,WAAW,CAAC,8DAA8D,EAAE,aAAa,QAAQ,WAAW,GAAG;8FAClH,QAAQ,WAAW;;;;;;;;;;;0FAGxB,8OAAC;gFAAG,WAAU;0FACZ,cAAA,8OAAC;oFAAI,WAAU;oFAA6D,OAAO,QAAQ,WAAW;8FACnG,QAAQ,WAAW;;;;;;;;;;;0FAGxB,8OAAC;gFAAG,WAAU;0FACZ,cAAA,8OAAC;oFAAI,WAAU;8FAA4C,WAAW,QAAQ,SAAS;;;;;;;;;;;0FAEzF,8OAAC;gFAAG,WAAU;0FACZ,cAAA,8OAAC;oFAAI,WAAU;8FAA4C,WAAW,QAAQ,OAAO;;;;;;;;;;;0FAEvF,8OAAC;gFAAG,WAAU;0FACZ,cAAA,8OAAC;oFAAI,WAAU;8FACZ,aAAa,QAAQ,MAAM,EAAE,QAAQ,QAAQ;;;;;;;;;;;0FAGlD,8OAAC;gFAAG,WAAU;0FACZ,cAAA,8OAAC;oFAAK,WAAW,CAAC,8DAA8D,EAAE,eAAe,QAAQ,MAAM,GAAG;8FAC/G,QAAQ,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,QAAQ,MAAM,CAAC,KAAK,CAAC;;;;;;;;;;;0FAGnE,8OAAC;gFAAG,WAAU;;kGACZ,8OAAC;wFAAO,WAAU;kGAA8D;;;;;;kGAGhF,8OAAC;wFAAO,WAAU;kGAAkG;;;;;;;;;;;;;uEA1C/G,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAyDlC,aAAa,mBACZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,GAAG,cAAc;wDACxD,UAAU,gBAAgB;wDAC1B,WAAU;kEACX;;;;;;kEAGD,8OAAC;wDACC,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,YAAY,cAAc;wDACjE,UAAU,gBAAgB;wDAC1B,WAAU;kEACX;;;;;;;;;;;;0DAIH,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEACC,cAAA,8OAAC;4DAAE,WAAU;;gEAA2C;8EAC9C,8OAAC;oEAAK,WAAU;8EAAe,aAAa;;;;;;gEAAS;gEAAI;8EACjE,8OAAC;oEAAK,WAAU;8EAAe,KAAK,GAAG,CAAC,UAAU,iBAAiB,MAAM;;;;;;gEAAS;gEAAI;8EACtF,8OAAC;oEAAK,WAAU;8EAAe,iBAAiB,MAAM;;;;;;gEAAQ;;;;;;;;;;;;kEAGlE,8OAAC;kEACC,cAAA,8OAAC;4DAAI,WAAU;4DAA4D,cAAW;;8EACpF,8OAAC;oEACC,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,GAAG,cAAc;oEACxD,UAAU,gBAAgB;oEAC1B,WAAU;;sFAEV,8OAAC;4EAAK,WAAU;sFAAU;;;;;;sFAC1B,8OAAC;4EAAE,WAAU;;;;;;;;;;;;gEAGd,MAAM,IAAI,CAAC;oEAAE,QAAQ,KAAK,GAAG,CAAC,GAAG;gEAAY,GAAG,CAAC,GAAG;oEACnD,IAAI;oEACJ,IAAI,cAAc,GAAG;wEACnB,UAAU,IAAI;oEAChB,OAAO,IAAI,eAAe,GAAG;wEAC3B,UAAU,IAAI;oEAChB,OAAO,IAAI,eAAe,aAAa,GAAG;wEACxC,UAAU,aAAa,IAAI;oEAC7B,OAAO;wEACL,UAAU,cAAc,IAAI;oEAC9B;oEAEA,qBACE,8OAAC;wEAEC,SAAS,IAAM,eAAe;wEAC9B,WAAW,CAAC,uEAAuE,EACjF,gBAAgB,UACZ,8CACA,2IACJ;kFAED;uEARI;;;;;gEAWX;8EAEA,8OAAC;oEACC,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,YAAY,cAAc;oEACjE,UAAU,gBAAgB;oEAC1B,WAAU;;sFAEV,8OAAC;4EAAK,WAAU;sFAAU;;;;;;sFAC1B,8OAAC;4EAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAa5B,cAAc,0BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuD;;;;;;sDACrE,8OAAC;4CAAE,WAAU;sDAAgD;;;;;;;;;;;;8CAM/D,8OAAC,2IAAA,CAAA,eAAY;oCAAC,OAAO;oCAAa,SAAS;;;;;;8CAE3C,8OAAC;oCAAK,UAAU;oCAA4B,WAAU;;sDAEpD,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAgB,WAAU;;wDAA6D;sEACrF,8OAAC;4DAAK,WAAU;sEAAe;;;;;;;;;;;;8DAEhD,8OAAC;oDACC,MAAK;oDACL,IAAG;oDACH,OAAO,eAAe,aAAa;oDACnC,UAAU,CAAC,IAAM,uBAAuB,iBAAiB,EAAE,MAAM,CAAC,KAAK;oDACvE,WAAU;oDACV,aAAY;oDACZ,QAAQ;;;;;;;;;;;;sDAKZ,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAgB,WAAU;;wDAA6D;sEACrF,8OAAC;4DAAK,WAAU;sEAAe;;;;;;;;;;;;8DAEhD,8OAAC;oDACC,IAAG;oDACH,OAAO,eAAe,aAAa;oDACnC,UAAU,CAAC,IAAM,uBAAuB,iBAAiB,EAAE,MAAM,CAAC,KAAK;oDACvE,WAAU;oDACV,QAAQ;;sEAER,8OAAC;4DAAO,OAAM;sEAAG;;;;;;sEACjB,8OAAC;4DAAO,OAAM;sEAAgB;;;;;;sEAC9B,8OAAC;4DAAO,OAAM;sEAAe;;;;;;sEAC7B,8OAAC;4DAAO,OAAM;sEAAc;;;;;;sEAC5B,8OAAC;4DAAO,OAAM;sEAAO;;;;;;;;;;;;;;;;;;sDAKzB,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAuB,WAAU;;wDAA6D;sEACrF,8OAAC;4DAAK,WAAU;sEAAe;;;;;;;;;;;;8DAEvD,8OAAC;oDACC,MAAK;oDACL,IAAG;oDACH,OAAO,eAAe,oBAAoB;oDAC1C,UAAU,CAAC,IAAM,uBAAuB,wBAAwB,EAAE,MAAM,CAAC,KAAK;oDAC9E,WAAU;oDACV,aAAY;oDACZ,QAAQ;;;;;;;;;;;;sDAKZ,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,SAAQ;4DAAS,WAAU;;gEAA6D;8EACtF,8OAAC;oEAAK,WAAU;8EAAe;;;;;;;;;;;;sEAExC,8OAAC;4DACC,MAAK;4DACL,IAAG;4DACH,OAAO,eAAe,MAAM;4DAC5B,UAAU,CAAC,IAAM,uBAAuB,UAAU,EAAE,MAAM,CAAC,KAAK;4DAChE,WAAU;4DACV,aAAY;4DACZ,MAAK;4DACL,KAAI;4DACJ,QAAQ;;;;;;;;;;;;8DAGZ,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAW,WAAU;;gEAA6D;8EACtF,8OAAC;oEAAK,WAAU;8EAAe;;;;;;;;;;;;sEAE1C,8OAAC;4DACC,IAAG;4DACH,OAAO,eAAe,QAAQ;4DAC9B,UAAU,CAAC,IAAM,uBAAuB,YAAY,EAAE,MAAM,CAAC,KAAK;4DAClE,WAAU;4DACV,QAAQ;;8EAER,8OAAC;oEAAO,OAAM;8EAAM;;;;;;8EACpB,8OAAC;oEAAO,OAAM;8EAAM;;;;;;8EACpB,8OAAC;oEAAO,OAAM;8EAAM;;;;;;;;;;;;;;;;;;;;;;;;sDAM1B,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAc,WAAU;;wDAA6D;sEACrF,8OAAC;4DAAK,WAAU;sEAAe;;;;;;;;;;;;8DAE9C,8OAAC;oDACC,MAAK;oDACL,IAAG;oDACH,OAAO,eAAe,WAAW;oDACjC,UAAU,CAAC,IAAM,uBAAuB,eAAe,EAAE,MAAM,CAAC,KAAK;oDACrE,WAAU;oDACV,QAAQ;;;;;;;;;;;;sDAKZ,8OAAC,yIAAA,CAAA,UAAU;4CACT,IAAG;4CACH,OAAM;4CACN,QAAO;4CACP,SAAS;4CACT,QAAQ;4CACR,OAAO;4CACP,UAAU;4CACV,aAAY;;;;;;sDAId,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAQ,WAAU;8DAA6D;;;;;;8DAG9F,8OAAC;oDACC,IAAG;oDACH,OAAO,eAAe,KAAK;oDAC3B,UAAU,CAAC,IAAM,uBAAuB,SAAS,EAAE,MAAM,CAAC,KAAK;oDAC/D,MAAM;oDACN,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAKhB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,MAAK;gDACL,UAAU;gDACV,WAAU;0DAET,8BACC;;sEACE,8OAAC;4DAAI,WAAU;4DAA6C,OAAM;4DAA6B,MAAK;4DAAO,SAAQ;;8EACjH,8OAAC;oEAAO,WAAU;oEAAa,IAAG;oEAAK,IAAG;oEAAK,GAAE;oEAAK,QAAO;oEAAe,aAAY;;;;;;8EACxF,8OAAC;oEAAK,WAAU;oEAAa,MAAK;oEAAe,GAAE;;;;;;;;;;;;wDAC/C;;iFAIR;;sEACE,8OAAC;4DAAE,WAAU;;;;;;wDAA4B;;;;;;;;;;;;;;;;;;;8CASnD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAuD;;;;;;kEAGrE,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;8EAAG;;;;;;8EACJ,8OAAC;8EAAG;;;;;;8EACJ,8OAAC;8EAAG;;;;;;8EACJ,8OAAC;8EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAa9B;uCAEe", "debugId": null}}]}