(()=>{var e={};e.id=5009,e.ids=[5009],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10413:(e,r,t)=>{Promise.resolve().then(t.bind(t,51778))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},14103:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\users\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\users\\page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28973:(e,r,t)=>{Promise.resolve().then(t.bind(t,73888))},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35530:(e,r,t)=>{Promise.resolve().then(t.bind(t,14103))},49048:(e,r,t)=>{"use strict";t.d(r,{D:()=>i});var a=t(12234),s=t(51278);let i={async getUsers(e={}){let r=new URLSearchParams;e.page&&r.set("page",e.page.toString()),e.limit&&r.set("limit",e.limit.toString()),e.search&&r.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>r.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>r.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(([e,t])=>{Array.isArray(t)?t.forEach(t=>r.append(`filter.${e}`,t)):r.set(`filter.${e}`,t)});let t=await a.Gf.get(`?${r.toString()}`);return(0,s.zp)(t)},async getUser(e){let r=await a.Gf.get(`/${e}`);return(0,s.zp)(r)},async getUserById(e){return this.getUser(e)},async getProfile(){let e=await a.Gf.get("/profile");return(0,s.zp)(e)},async createUser(e){let r=await a.Gf.post("",e);return(0,s.zp)(r)},async updateUser(e,r){let t=await a.Gf.put(`/${e}`,r);return(0,s.zp)(t)},async updateProfile(e){let r=await a.Gf.put("/profile",e);return(0,s.zp)(r)},async changePassword(e){let r=await a.Gf.put("/profile/password",e);return(0,s.zp)(r)},async uploadAvatar(e){let r=new FormData;r.append("avatar",e);try{let e=await a.Gf.post("/profile/avatar",r,{headers:{"Content-Type":"multipart/form-data"}});return(0,s.zp)(e)}catch(e){throw e}},async removeAvatar(){let e=await a.Gf.delete("/profile/avatar");return(0,s.zp)(e)},async deleteUser(e){await a.Gf.delete(`/${e}`)}}},51778:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var a=t(60687),s=t(43210),i=t(16189),n=t(63213),l=t(21891),d=t(60417);function o({children:e}){let{isAuthenticated:r,loading:t}=(0,n.A)();(0,i.useRouter)();let[o,c]=(0,s.useState)("overview"),[m,x]=(0,s.useState)(!1);return t?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"})}):r?(0,a.jsxs)("div",{className:"flex h-screen overflow-hidden",children:[(0,a.jsx)("div",{id:"mobileSidebarOverlay",className:`mobile-sidebar-overlay ${m?"show":""}`,onClick:()=>x(!1)}),(0,a.jsx)(d.default,{}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,a.jsx)(l.default,{activeTab:o,onTabChange:c,onMobileMenuToggle:()=>{x(!m)}}),e]})]}):null}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73888:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\users\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\users\\layout.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86732:(e,r,t)=>{"use strict";t.d(r,{A:()=>i});var a=t(60687);let s=(0,t(43210).forwardRef)(({label:e,error:r,helperText:t,variant:s="default",fullWidth:i=!0,className:n="",required:l,disabled:d,options:o,children:c,...m},x)=>{let g=`px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white transition-colors duration-200 ${i?"w-full":""} ${"small"===s?"py-1.5 text-sm":"py-2"}`,u=`${g} ${r?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"} ${d?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""} ${n}`,p=`block font-medium text-gray-700 dark:text-gray-300 mb-2 ${"small"===s?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"}`;return(0,a.jsxs)("div",{className:"w-full",children:[e&&(0,a.jsxs)("label",{className:p,children:[e,l&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,a.jsx)("select",{ref:x,className:u,disabled:d,required:l,...m,children:o?o.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value)):c}),r&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:r}),t&&!r&&(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:t})]})});s.displayName="Select";let i=s},93247:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>D});var a=t(60687),s=t(43210),i=t(63224),n=t(85177),l=t(12234),d=t(51278);let o={async createDepartment(e){try{let r=await l.uE.post("/department",e);return(0,d.zp)(r)}catch(e){throw e}},async getDepartments(e={}){try{let r=new URLSearchParams;e.page&&r.set("page",e.page.toString()),e.limit&&r.set("limit",e.limit.toString()),e.search&&r.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>r.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>r.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(([e,t])=>{Array.isArray(t)?t.forEach(t=>r.append(`filter.${e}`,t)):r.set(`filter.${e}`,t)});let t=await l.uE.get(`/department?${r.toString()}`);return(0,d.zp)(t)}catch(e){throw e}},async getAllDepartments(){try{return(await this.getDepartments()).data}catch(e){throw e}},async getDepartment(e){let r=await l.uE.get(`/department/${e}`);return(0,d.zp)(r)},async getDepartmentById(e){return this.getDepartment(e)},async updateDepartment(e,r){let t=await l.uE.put(`/department/${e}`,r);return(0,d.zp)(t)},async deleteDepartment(e){await l.uE.delete(`/department/${e}`)},formatDepartmentCode:e=>e.toUpperCase(),formatDepartmentName:e=>e.charAt(0).toUpperCase()+e.slice(1),validateDepartmentCode:e=>/^[A-Z]{1,5}$/.test(e),validateEmail:e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)};var c=t(49048),m=t(22145),x=t(86732);let g=function({roles:e,formData:r,handleRoleToggle:t}){let[i,n]=(0,s.useState)(!1),l=(0,s.useRef)(null),d=e=>e.length<=3?e.toUpperCase():e.charAt(0).toUpperCase()+e.slice(1).toLowerCase(),o=r.role_ids.length;return(0,a.jsxs)("div",{className:"w-full",ref:l,children:[(0,a.jsx)("label",{className:"block font-medium text-gray-700 dark:text-gray-300 mb-2 text-sm",children:"Roles"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("button",{type:"button",onClick:()=>n(!i),className:`
            px-3 py-2 border rounded-md shadow-sm transition-colors duration-200 text-left cursor-pointer
            bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
            focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary
            w-full
            ${i?"border-red-500 ring-2 ring-red-500":"border-gray-300 dark:border-gray-600"}
          `,"aria-haspopup":"listbox","aria-expanded":i,"aria-labelledby":"roles-label",children:[(0,a.jsx)("span",{className:"block truncate",children:0===o?"Select roles...":`${o} role${o>1?"s":""} selected`}),(0,a.jsx)("span",{className:"absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none",children:(0,a.jsx)("svg",{className:`h-5 w-5 text-gray-400 transition-transform duration-200 ${i?"rotate-180":""}`,xmlns:"http://www.w3.org/2000/svg",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 111.06 1.06l-4.24 4.24a.75.75 0 01-1.06 0L5.21 8.27a.75.75 0 01.02-1.06z",clipRule:"evenodd"})})})]}),i&&(0,a.jsx)("ul",{className:"absolute z-[9999] bottom-full mb-1 w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-40 py-1 text-sm ring-1 ring-black ring-opacity-5 overflow-auto",role:"listbox","aria-labelledby":"roles-label",children:e.length>0?e.map(e=>{let s=r.role_ids.includes(e.role_id);return(0,a.jsxs)("li",{className:"cursor-pointer select-none relative py-2 pl-10 pr-4 hover:bg-red-50 dark:hover:bg-red-700",onClick:()=>t(e.role_id),role:"option","aria-selected":s,children:[(0,a.jsx)("span",{className:`block truncate ${s?"font-semibold text-red-600 dark:text-red-300":"font-normal text-gray-900 dark:text-gray-100"}`,children:d(e.name)}),s&&(0,a.jsx)("span",{className:"absolute inset-y-0 left-0 flex items-center pl-3 text-red-600 dark:text-red-300",children:(0,a.jsx)("svg",{className:"h-5 w-5",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M16.704 5.293a1 1 0 00-1.414-1.414L7 12.172l-2.293-2.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l8-8z",clipRule:"evenodd"})})})]},e.role_id)}):(0,a.jsx)("li",{className:"text-gray-500 dark:text-gray-400 px-4 py-2",children:"Loading roles..."})})]})]})},u=function({departments:e,selectedDepartmentId:r,onSelect:t}){let[i,n]=(0,s.useState)(!1),l=(0,s.useRef)(null),d=e.find(e=>e.department_id===r);return(0,a.jsxs)("div",{className:"w-full",ref:l,children:[(0,a.jsx)("label",{className:"block font-medium text-gray-700 dark:text-gray-300 mb-2 text-sm",children:"Department"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("button",{type:"button",onClick:()=>n(!i),className:`w-full px-3 py-2 border rounded-md shadow-sm transition-colors duration-200 text-left
            bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
            focus:outline-none focus:ring-2 focus:ring-primary
            ${i?"border-red-500 ring-2 ring-red-500":"border-gray-300 dark:border-gray-600"}
          `,"aria-haspopup":"listbox","aria-expanded":i,children:[(0,a.jsx)("span",{className:"block truncate",children:d?d.name:"Select department..."}),(0,a.jsx)("span",{className:"absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none",children:(0,a.jsx)("svg",{className:`h-5 w-5 text-gray-400 transition-transform duration-200 ${i?"rotate-180":""}`,xmlns:"http://www.w3.org/2000/svg",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 111.06 1.06l-4.24 4.24a.75.75 0 01-1.06 0L5.21 8.27a.75.75 0 01.02-1.06z",clipRule:"evenodd"})})})]}),i&&(0,a.jsx)("ul",{className:"absolute z-50 bottom-full mb-1 w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 py-1 text-sm ring-1 ring-black ring-opacity-5 overflow-auto",role:"listbox",children:e.length>0?e.map(e=>(0,a.jsxs)("li",{onClick:()=>{t(e.department_id),n(!1)},className:`cursor-pointer select-none relative py-2 pl-10 pr-4 hover:bg-red-50 dark:hover:bg-red-700 ${r===e.department_id?"font-semibold text-red-600 dark:text-red-300":"text-gray-900 dark:text-gray-100"}`,role:"option","aria-selected":r===e.department_id,children:[e.name,r===e.department_id&&(0,a.jsx)("span",{className:"absolute inset-y-0 left-0 flex items-center pl-3 text-red-600 dark:text-red-300",children:(0,a.jsx)("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M16.704 5.293a1 1 0 00-1.414-1.414L7 12.172l-2.293-2.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l8-8z",clipRule:"evenodd"})})})]},e.department_id)):(0,a.jsx)("li",{className:"text-gray-500 dark:text-gray-400 px-4 py-2",children:"No departments found"})})]})]})},p=function({organizations:e,selectedOrganizationId:r,onSelect:t,onNavigateToOrganizations:i}){let[n,l]=(0,s.useState)(!1),[d,o]=(0,s.useState)(""),c=(0,s.useRef)(null),m=(0,s.useRef)(null),x=e.find(e=>e.organization_id===r),g=[...e.filter(e=>""===d||e.name.toLowerCase().includes(d.toLowerCase())||e.registration_number.toLowerCase().includes(d.toLowerCase())||e.tpin&&e.tpin.toLowerCase().includes(d.toLowerCase())),{organization_id:"other",name:"Other (Create New)",registration_number:"",tpin:"",website:"",email:"",phone:"",postal_address:"",physical_address:"",date_incorporation:"",place_incorporation:"",profile_description:"",created_at:"",updated_at:""}];return(0,a.jsxs)("div",{className:"w-full",ref:c,children:[(0,a.jsx)("label",{className:"block font-medium text-gray-700 dark:text-gray-300 mb-2 text-sm",children:"Organization"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{ref:m,type:"text",value:d,onChange:e=>{o(e.target.value),l(!0)},onFocus:()=>l(!0),placeholder:"Search organizations...",className:`w-full px-3 py-2 pr-10 border rounded-md shadow-sm
            bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
            focus:outline-none focus:ring-2 focus:ring-primary
            ${n?"border-red-500 ring-2 ring-red-500":"border-gray-300 dark:border-gray-600"}
          `,"aria-haspopup":"listbox","aria-expanded":n}),(0,a.jsx)("button",{type:"button",onClick:()=>l(!n),className:"absolute inset-y-0 right-0 flex items-center pr-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:(0,a.jsx)("svg",{className:`h-5 w-5 transition-transform duration-200 ${n?"rotate-180":""}`,xmlns:"http://www.w3.org/2000/svg",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 111.06 1.06l-4.24 4.24a.75.75 0 01-1.06 0L5.21 8.27a.75.75 0 01.02-1.06z",clipRule:"evenodd"})})}),x&&(0,a.jsx)("div",{className:"mt-2 p-2 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("span",{className:"text-sm font-medium text-green-800 dark:text-green-200",children:["Selected: ",x.name]}),"other"!==x.organization_id&&(0,a.jsxs)("div",{className:"text-xs text-green-600 dark:text-green-400",children:[x.registration_number," ",x.tpin&&`• ${x.tpin}`]})]}),(0,a.jsx)("button",{type:"button",onClick:()=>t(""),className:"text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200",title:"Clear selection",children:(0,a.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})]})}),n&&(0,a.jsx)("ul",{className:"absolute z-50 bottom-full mb-1 w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 py-1 text-sm ring-1 ring-black ring-opacity-5 overflow-auto",role:"listbox",children:g.length>1?g.map(e=>(0,a.jsxs)("li",{onClick:()=>{"other"===e.organization_id&&i?i():t(e.organization_id),l(!1)},className:`cursor-pointer select-none relative py-2 pl-10 pr-4 hover:bg-red-50 dark:hover:bg-red-700 ${r===e.organization_id?"font-semibold text-red-600 dark:text-red-300":"text-gray-900 dark:text-gray-100"} ${"other"===e.organization_id?"border-t border-gray-200 dark:border-gray-600 mt-1 pt-3":""}`,role:"option","aria-selected":r===e.organization_id,children:[(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"font-medium",children:e.name}),"other"!==e.organization_id&&(0,a.jsxs)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:[e.registration_number," ",e.tpin&&`• ${e.tpin}`]})]}),r===e.organization_id&&(0,a.jsx)("span",{className:"absolute inset-y-0 left-0 flex items-center pl-3 text-red-600 dark:text-red-300",children:(0,a.jsx)("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M16.704 5.293a1 1 0 00-1.414-1.414L7 12.172l-2.293-2.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l8-8z",clipRule:"evenodd"})})})]},e.organization_id)):(0,a.jsxs)("li",{className:"text-gray-500 dark:text-gray-400 px-4 py-2",children:["No organizations found",(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)("button",{onClick:()=>{i?i():t("other"),l(!1)},className:"text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 underline",children:"Create New Organization"})})]})})]})]})};function h({isOpen:e,onClose:r,onSave:t,user:i,roles:n=[],departments:l=[],organizations:d=[],onNavigateToOrganizations:o}){let[h,b]=(0,s.useState)({email:"",password:"",first_name:"",last_name:"",middle_name:"",phone:"",department_id:"",organization_id:"",status:"active",role_ids:[]}),[f,y]=(0,s.useState)(!1),[v,j]=(0,s.useState)(null),[k,w]=(0,s.useState)(!1),[N,_]=(0,s.useState)(!1),[C,z]=(0,s.useState)(!1),P=()=>h.email&&h.email.trim()?h.first_name&&h.first_name.trim()?h.last_name&&h.last_name.trim()?h.phone&&h.phone.trim()?i||h.password&&h.password.trim()?!k||h.department_id&&h.department_id.trim()?0==h.role_ids.length?"Staff user can not be created without roles":h.role_ids.some(e=>{let r=n.find(r=>r.role_id===e);if(!r)return!1;let t=r.name.toLowerCase();return"administrator"===t||"admin"===t||"super_admin"===t})&&!h.email.endsWith("@macra.mw")?"Only MACRA staff can be assigned administrator roles":h.role_ids.some(e=>{let r=n.find(r=>r.role_id===e);return!!r&&"evaluator"===r.name.toLowerCase()})&&!h.email.endsWith("@macra.mw")?"Only MACRA staff can be assigned evaluator roles":k||h.organization_id&&h.organization_id.trim()?null:"Please select an organization for non-MACRA staff members":"Please select a department for MACRA staff members":"Password is required for new users":"Phone number is required":"Last name is required":"First name is required":"Email is required",S=async e=>{e.preventDefault(),y(!0),j(null);let r=P();if(r){j(r),y(!1);return}try{if(i){let e={email:h.email,first_name:h.first_name,last_name:h.last_name,middle_name:h.middle_name||void 0,phone:h.phone,status:h.status,role_ids:h.role_ids.length>0?h.role_ids:void 0,department_id:k&&h.department_id||void 0,organization_id:!k&&h.organization_id||void 0};h.password.trim()&&(e.password=h.password),await c.D.updateUser(i.user_id,e)}else{let e={email:h.email,password:h.password,first_name:h.first_name,last_name:h.last_name,middle_name:h.middle_name||void 0,phone:h.phone,status:h.status,role_ids:h.role_ids.length>0?h.role_ids:void 0,department_id:k&&h.department_id||void 0,organization_id:!k&&h.organization_id||void 0};await c.D.createUser(e)}t()}catch(e){j(e instanceof Error&&"response"in e&&"object"==typeof e.response&&null!==e.response&&"data"in e.response&&"object"==typeof e.response.data&&null!==e.response.data&&"message"in e.response.data&&"string"==typeof e.response.data.message?e.response.data.message:"Failed to save user")}finally{y(!1)}},D=e=>{let{name:r,value:t}=e.target;b(e=>({...e,[r]:t}))};return e?(0,a.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,a.jsxs)("div",{className:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-gray-500 dark:bg-gray-900 bg-opacity-75 dark:bg-opacity-75 transition-opacity",onClick:r}),(0,a.jsx)("div",{className:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-visible shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:(0,a.jsxs)("form",{onSubmit:S,children:[(0,a.jsx)("div",{className:"bg-white rounded-lg dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:(0,a.jsx)("div",{className:"sm:flex sm:items-start",children:(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4",children:i?"Edit User":"Create New User"}),v&&(0,a.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded",children:v}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(m.A,{type:"email",name:"email",label:"Email",required:!0,value:h.email,onChange:D,placeholder:"Enter email address"}),(0,a.jsx)(m.A,{type:"password",name:"password",label:`Password ${!i?"*":""}`,required:!i,value:h.password,onChange:D,placeholder:i?"Leave blank to keep current password":"Enter password"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(m.A,{type:"text",name:"first_name",label:"First Name",required:!0,value:h.first_name,onChange:D,placeholder:"Enter first name"}),(0,a.jsx)(m.A,{type:"text",name:"last_name",label:"Last Name",required:!0,value:h.last_name,onChange:D,placeholder:"Enter last name"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(m.A,{type:"text",name:"middle_name",label:"Middle Name",value:h.middle_name,onChange:D,placeholder:"Enter middle name (optional)"}),(0,a.jsx)(m.A,{type:"tel",name:"phone",label:"Phone",required:!0,value:h.phone,onChange:D,placeholder:"Enter phone number"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)(x.A,{name:"status",label:"Status",value:h.status,onChange:D,children:[(0,a.jsx)("option",{value:"active",children:"Active"}),(0,a.jsx)("option",{value:"inactive",children:"Inactive"}),(0,a.jsx)("option",{value:"suspended",children:"Suspended"})]}),(0,a.jsxs)(x.A,{name:"isStaff",label:"MACRA Staff Member?",value:k?"true":"false",onChange:e=>{w("true"===e.target.value),z(!0)},children:[(0,a.jsx)("option",{value:"true",children:"Yes"}),(0,a.jsx)("option",{value:"false",children:"No"})]}),N&&(0,a.jsxs)(a.Fragment,{children:[k?(0,a.jsx)(u,{selectedDepartmentId:h.department_id,onSelect:e=>{b(r=>({...r,department_id:e}))},departments:l}):(0,a.jsx)(p,{selectedOrganizationId:h.organization_id,onSelect:e=>{b(r=>({...r,organization_id:e}))},organizations:d,onNavigateToOrganizations:o}),(0,a.jsx)(g,{roles:n,formData:h,handleRoleToggle:e=>{let r=String(e);b(e=>({...e,role_ids:e.role_ids.includes(r)?e.role_ids.filter(e=>e!==r):[...e.role_ids,r]}))}})]})]})]})]})})}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,a.jsx)("button",{type:"submit",disabled:f,className:"main-button inline-flex items-center justify-center w-full sm:w-auto sm:ml-3 disabled:opacity-50",children:f?"Saving...":i?"Update User":"Create User"}),(0,a.jsx)("button",{type:"button",onClick:r,className:"secondary-main-button inline-flex items-center justify-center mt-3 w-full sm:mt-0 sm:ml-3 sm:w-auto",children:"Cancel"})]})]})})]})}):null}var b=t(20539);let f=({isOpen:e,onClose:r,onSave:t,department:i})=>{let[n,l]=(0,s.useState)({code:"",name:"",description:"",email:""}),[d,c]=(0,s.useState)({}),[m,x]=(0,s.useState)(!1),g=!!i;(0,s.useEffect)(()=>{e&&(i?l({code:i.code,name:i.name,description:i.description,email:i.email}):l({code:"",name:"",description:"",email:""}),c({}))},[e,i]);let u=()=>{let e={};return n.code.trim()?n.code.length>5?e.code="Department code must be 5 characters or less":/^[A-Z0-9]+$/.test(n.code.toUpperCase())||(e.code="Department code must contain only letters and numbers"):e.code="Department code is required",n.name.trim()?n.name.length>100&&(e.name="Department name must be 100 characters or less"):e.name="Department name is required",n.description.trim()||(e.description="Description is required"),n.email.trim()?o.validateEmail(n.email)||(e.email="Please enter a valid email address"):e.email="Email is required",c(e),0===Object.keys(e).length},p=async e=>{if(e.preventDefault(),u())try{let e;if(x(!0),g&&i){let r={code:n.code.toUpperCase(),name:n.name,description:n.description,email:n.email};e=await o.updateDepartment(i.department_id,r)}else{let r={...n,code:n.code.toUpperCase()};e=await o.createDepartment(r)}t(e),r()}catch(e){e.response?.data?.message?e.response.data.message.includes("code")?c({code:"Department code already exists"}):e.response.data.message.includes("name")?c({name:"Department name already exists"}):e.response.data.message.includes("email")?c({email:"Email already exists"}):c({general:e.response.data.message}):c({general:"Failed to save department. Please try again."})}finally{x(!1)}},h=(e,r)=>{l(t=>({...t,[e]:r})),d[e]&&c(r=>({...r,[e]:""}))};return e?(0,a.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,a.jsxs)("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:r}),(0,a.jsx)("div",{className:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:(0,a.jsxs)("form",{onSubmit:p,children:[(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900 dark:text-gray-100",children:g?"Edit Department":"Add New Department"}),(0,a.jsx)("button",{type:"button",onClick:r,className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:(0,a.jsx)("i",{className:"ri-close-line text-xl"})})]}),d.general&&(0,a.jsx)("div",{className:"mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md",children:(0,a.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:d.general})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"code",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Department Code *"}),(0,a.jsx)("input",{type:"text",id:"code",value:n.code,onChange:e=>h("code",e.target.value.toUpperCase()),className:`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 font-mono ${d.code?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"} bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100`,placeholder:"e.g., HR, IT, FIN",maxLength:5,disabled:m}),d.code&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:d.code}),(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:"Maximum 5 characters, letters and numbers only"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Department Name *"}),(0,a.jsx)("input",{type:"text",id:"name",value:n.name,onChange:e=>h("name",e.target.value),className:`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ${d.name?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"} bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100`,placeholder:"e.g., Human Resources",maxLength:100,disabled:m}),d.name&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:d.name})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Description *"}),(0,a.jsx)("textarea",{id:"description",value:n.description,onChange:e=>h("description",e.target.value),rows:3,className:`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ${d.description?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"} bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100`,placeholder:"Brief description of the department's role and responsibilities",disabled:m}),d.description&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:d.description})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Department Email *"}),(0,a.jsx)("input",{type:"email",id:"email",value:n.email,onChange:e=>h("email",e.target.value),className:`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ${d.email?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"} bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100`,placeholder:"<EMAIL>",disabled:m}),d.email&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:d.email})]})]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,a.jsx)("button",{type:"submit",disabled:m,className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:m?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"ri-loader-4-line animate-spin mr-2"}),g?"Updating...":"Creating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"ri-save-line mr-2"}),g?"Update Department":"Create Department"]})}),(0,a.jsx)("button",{type:"button",onClick:r,disabled:m,className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"Cancel"})]})]})})]})}):null},y={async createOrganization(e){try{let r=await l.uE.post("/organization",e);return(0,d.zp)(r)}catch(e){throw e}},async getOrganizations(e={}){try{let r=new URLSearchParams;e.page&&r.set("page",e.page.toString()),e.limit&&r.set("limit",e.limit.toString()),e.search&&r.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>r.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>r.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(([e,t])=>{Array.isArray(t)?t.forEach(t=>r.append(`filter.${e}`,t)):r.set(`filter.${e}`,t)});let t=await l.uE.get(`/organization?${r.toString()}`);return(0,d.zp)(t)}catch(e){throw e}},async getAllOrganizations(){try{return(await this.getOrganizations()).data}catch(e){throw e}},async getOrganization(e){let r=await l.uE.get(`/organization/${e}`);return(0,d.zp)(r)},async getOrganizationById(e){return this.getOrganization(e)},async updateOrganization(e,r){let t=await l.uE.put(`/organization/${e}`,r);return(0,d.zp)(t)},async deleteOrganization(e){await l.uE.delete(`/organization/${e}`)},formatOrganizationName:e=>e.charAt(0).toUpperCase()+e.slice(1),formatRegistrationNumber:e=>e.toUpperCase().trim(),validateRegistrationNumber:e=>/^[A-Z0-9\-]+$/.test(e)&&e.length>=3,validateEmail:e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),validatePhone:e=>/^[\d\s\-\+\(\)]{5,20}$/.test(e),validateWebsite(e){try{return new URL(e),!0}catch{return!1}},formatWebsite:e=>e.startsWith("http://")||e.startsWith("https://")?e:`https://${e}`,formatIncorporationDate:e=>new Date(e).toLocaleDateString(),validateIncorporationDate(e){let r=new Date(e);return r<=new Date&&r.getFullYear()>1800},validateTpin:e=>/^[A-Z0-9\-]+$/.test(e)&&e.length>=8,formatTpin:e=>e.toUpperCase().trim()},v=({isOpen:e,onClose:r,onSave:t,organization:i})=>{let n={name:"",registration_number:"",tpin:"",website:"",email:"",phone:"",fax:"",postal_address:"",physical_address:"",date_incorporation:"",place_incorporation:"",profile_description:""},[l,d]=(0,s.useState)(n),[o,c]=(0,s.useState)({}),[m,x]=(0,s.useState)(!1),g=!!i;(0,s.useEffect)(()=>{e&&(i?d({name:i.name||"",registration_number:i.registration_number||"",tpin:i.tpin||"",website:i.website||"",email:i.email||"",phone:i.phone||"",fax:i.fax||"",postal_address:i.postal_address||"",physical_address:i.physical_address||"",date_incorporation:i.date_incorporation?i.date_incorporation.split("T")[0]:"",place_incorporation:i.place_incorporation||"",profile_description:i.profile_description||""}):d(n),c({}))},[e,i]);let u=()=>{let e={};return l?(l.name&&l.name.trim()?l.name.length>200&&(e.name="Organization name must be 200 characters or less"):e.name="Organization name is required",l.registration_number&&l.registration_number.trim()?y.validateRegistrationNumber(l.registration_number)||(e.registration_number="Registration number must be at least 3 characters and contain only letters, numbers, and hyphens"):e.registration_number="Registration number is required",l.tpin&&l.tpin.trim()?y.validateTpin(l.tpin)||(e.tpin="TPIN must be at least 8 characters and contain only letters, numbers, and hyphens"):e.tpin="TPIN is required",l.website&&l.website.trim()?y.validateWebsite(y.formatWebsite(l.website))||(e.website="Please enter a valid website URL"):e.website="Website is required",l.email&&l.email.trim()?y.validateEmail(l.email)||(e.email="Please enter a valid email address"):e.email="Email is required",l.phone&&l.phone.trim()?y.validatePhone(l.phone)||(e.phone="Please enter a valid phone number (5-20 characters)"):e.phone="Phone number is required",l.fax&&l.fax.trim()&&!y.validatePhone(l.fax)&&(e.fax="Please enter a valid fax number"),l.postal_address&&l.postal_address.trim()||(e.postal_address="Postal address is required"),l.physical_address&&l.physical_address.trim()||(e.physical_address="Physical address is required"),l.date_incorporation&&l.date_incorporation.trim()?y.validateIncorporationDate(l.date_incorporation)||(e.date_incorporation="Please enter a valid date of incorporation (not in the future)"):e.date_incorporation="Date of incorporation is required",l.place_incorporation&&l.place_incorporation.trim()||(e.place_incorporation="Place of incorporation is required"),l.profile_description&&l.profile_description.trim()?l.profile_description.length>1e3&&(e.profile_description="Profile description must be 1000 characters or less"):e.profile_description="Profile description is required",c(e),0===Object.keys(e).length):(e.general="Form data is not properly initialized",c(e),!1)},p=async e=>{if(e.preventDefault(),u())try{let e;if(x(!0),g&&i){let r={name:l.name,registration_number:y.formatRegistrationNumber(l.registration_number||""),tpin:y.formatTpin(l.tpin||""),website:y.formatWebsite(l.website||""),email:l.email,phone:l.phone,fax:l.fax||void 0,postal_address:l.postal_address,physical_address:l.physical_address,date_incorporation:l.date_incorporation,place_incorporation:l.place_incorporation,profile_description:l.profile_description};e=await y.updateOrganization(i.organization_id,r)}else{let r={...l,registration_number:y.formatRegistrationNumber(l.registration_number||""),tpin:y.formatTpin(l.tpin||""),website:y.formatWebsite(l.website||""),fax:l.fax||void 0};e=await y.createOrganization(r)}t(e),r()}catch(e){e.response?.data?.message?e.response.data.message.includes("registration_number")?c({registration_number:"Registration number already exists"}):e.response.data.message.includes("tpin")?c({tpin:"TPIN already exists"}):e.response.data.message.includes("name")?c({name:"Organization name already exists"}):e.response.data.message.includes("email")?c({email:"Email already exists"}):c({general:e.response.data.message}):c({general:"Failed to save organization. Please try again."})}finally{x(!1)}},h=(e,r)=>{d(t=>({...t,[e]:r})),o[e]&&c(r=>({...r,[e]:""}))};return e?(0,a.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,a.jsxs)("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:r}),(0,a.jsx)("div",{className:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full",children:(0,a.jsxs)("form",{onSubmit:p,children:[(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900 dark:text-gray-100",children:g?"Edit Organization":"Add New Organization"}),(0,a.jsx)("button",{type:"button",onClick:r,className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:(0,a.jsx)("i",{className:"ri-close-line text-xl"})})]}),o.general&&(0,a.jsx)("div",{className:"mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md",children:(0,a.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:o.general})}),(0,a.jsxs)("div",{className:"space-y-4 max-h-96 overflow-y-auto",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Organization Name *"}),(0,a.jsx)("input",{type:"text",id:"name",value:l.name,onChange:e=>h("name",e.target.value),className:`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ${o.name?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"} bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100`,placeholder:"e.g., ABC Company Limited",maxLength:200,disabled:m}),o.name&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:o.name})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"registration_number",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Registration Number *"}),(0,a.jsx)("input",{type:"text",id:"registration_number",value:l.registration_number,onChange:e=>h("registration_number",e.target.value.toUpperCase()),className:`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 font-mono ${o.registration_number?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"} bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100`,placeholder:"e.g., BN123456",disabled:m}),o.registration_number&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:o.registration_number}),(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:"Business registration number from registrar"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"tpin",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"TPIN *"}),(0,a.jsx)("input",{type:"text",id:"tpin",value:l.tpin,onChange:e=>h("tpin",e.target.value.toUpperCase()),className:`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 font-mono ${o.tpin?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"} bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100`,placeholder:"e.g., 12345678-90",disabled:m}),o.tpin&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:o.tpin}),(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:"Tax Payer Identification Number"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"website",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Website *"}),(0,a.jsx)("input",{type:"url",id:"website",value:l.website,onChange:e=>h("website",e.target.value),className:`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ${o.website?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"} bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100`,placeholder:"e.g., www.company.com",disabled:m}),o.website&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:o.website})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Email Address *"}),(0,a.jsx)("input",{type:"email",id:"email",value:l.email,onChange:e=>h("email",e.target.value),className:`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ${o.email?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"} bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100`,placeholder:"<EMAIL>",disabled:m}),o.email&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:o.email})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Phone Number *"}),(0,a.jsx)("input",{type:"tel",id:"phone",value:l.phone,onChange:e=>h("phone",e.target.value),className:`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ${o.phone?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"} bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100`,placeholder:"+265 1 234 567",disabled:m}),o.phone&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:o.phone})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"fax",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Fax Number"}),(0,a.jsx)("input",{type:"tel",id:"fax",value:l.fax,onChange:e=>h("fax",e.target.value),className:`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ${o.fax?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"} bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100`,placeholder:"+265 1 234 568 (optional)",disabled:m}),o.fax&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:o.fax})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"postal_address",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Postal Address *"}),(0,a.jsx)("textarea",{id:"postal_address",value:l.postal_address,onChange:e=>h("postal_address",e.target.value),rows:2,className:`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ${o.postal_address?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"} bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100`,placeholder:"P.O. Box 123, City, Country",disabled:m}),o.postal_address&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:o.postal_address})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"physical_address",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Physical Address *"}),(0,a.jsx)("textarea",{id:"physical_address",value:l.physical_address,onChange:e=>h("physical_address",e.target.value),rows:2,className:`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ${o.physical_address?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"} bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100`,placeholder:"Street address, building, city",disabled:m}),o.physical_address&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:o.physical_address})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"date_incorporation",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Date of Incorporation *"}),(0,a.jsx)("input",{type:"date",id:"date_incorporation",value:l.date_incorporation,onChange:e=>h("date_incorporation",e.target.value),className:`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ${o.date_incorporation?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"} bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100`,disabled:m}),o.date_incorporation&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:o.date_incorporation})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"place_incorporation",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Place of Incorporation *"}),(0,a.jsx)("input",{type:"text",id:"place_incorporation",value:l.place_incorporation,onChange:e=>h("place_incorporation",e.target.value),className:`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ${o.place_incorporation?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"} bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100`,placeholder:"e.g., Lilongwe, Malawi",disabled:m}),o.place_incorporation&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:o.place_incorporation})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"profile_description",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Profile Description *"}),(0,a.jsx)("textarea",{id:"profile_description",value:l.profile_description,onChange:e=>h("profile_description",e.target.value),rows:4,className:`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ${o.profile_description?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"} bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100`,placeholder:"Brief description of the organization's business activities and profile",maxLength:1e3,disabled:m}),o.profile_description&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:o.profile_description}),(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:"Maximum 1000 characters"})]})]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,a.jsx)("button",{type:"submit",disabled:m,className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:m?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"ri-loader-4-line animate-spin mr-2"}),g?"Updating...":"Creating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"ri-save-line mr-2"}),g?"Update Organization":"Create Organization"]})}),(0,a.jsx)("button",{type:"button",onClick:r,disabled:m,className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"Cancel"})]})]})})]})}):null},j=({tabs:e,activeTab:r,onTabChange:t})=>(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-700",children:(0,a.jsx)("nav",{className:"-mb-px flex space-x-8 px-6","aria-label":"Tabs",children:e.map(e=>(0,a.jsx)("button",{onClick:()=>t(e.id),className:`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${r===e.id?"border-red-500 text-red-600 dark:text-red-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"}`,"aria-current":r===e.id?"page":void 0,children:e.label},e.id))})}),(0,a.jsx)("div",{className:"mt-6",children:e.map(e=>(0,a.jsx)("div",{className:`tab-content ${r===e.id?"":"hidden"}`,children:e.content},e.id))})]});var k=t(70088);function w({isOpen:e,onClose:r,onConfirm:t,title:s,message:i,confirmText:n="Confirm",cancelText:l="Cancel",confirmVariant:d="danger",loading:o=!1,icon:c}){return e?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 flex items-center justify-center z-50 p-4",children:(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 transform transition-all",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-start mb-4",children:[c||(()=>{switch(d){case"danger":return(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"h-10 w-10 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center",children:(0,a.jsx)("i",{className:"ri-delete-bin-line text-red-600 dark:text-red-400 text-xl"})})});case"warning":return(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"h-10 w-10 bg-yellow-100 dark:bg-yellow-900/20 rounded-full flex items-center justify-center",children:(0,a.jsx)("i",{className:"ri-alert-line text-yellow-600 dark:text-yellow-400 text-xl"})})});default:return(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"h-10 w-10 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center",children:(0,a.jsx)("i",{className:"ri-information-line text-blue-600 dark:text-blue-400 text-xl"})})})}})(),(0,a.jsxs)("div",{className:"ml-4 flex-1",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2",children:s}),(0,a.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"string"==typeof i?(0,a.jsx)("p",{children:i}):i})]})]}),(0,a.jsxs)("div",{className:"flex space-x-3 mt-6",children:[(0,a.jsx)("button",{onClick:t,disabled:o,className:`flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed ${(()=>{switch(d){case"danger":default:return"bg-red-600 hover:bg-red-700 text-white focus:ring-red-500";case"primary":return"bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500";case"warning":return"bg-yellow-600 hover:bg-yellow-700 text-white focus:ring-yellow-500"}})()}`,children:o?(0,a.jsxs)("div",{className:"flex items-center justify-center",children:[(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Processing..."]}):n}),(0,a.jsx)("button",{onClick:r,disabled:o,className:"flex-1 px-4 py-2 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-offset-gray-800 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:l})]})]})})}):null}let N=(0,s.forwardRef)(({label:e,error:r,helperText:t,required:s=!1,options:i=[],placeholder:n="Select an option...",className:l="",containerClassName:d="",onChange:o,id:c,value:m,...x},g)=>{let u=c||`select-${Math.random().toString(36).substr(2,9)}`,p=`
    w-full px-3 py-2 border rounded-md shadow-sm 
    focus:outline-none focus:ring-2 focus:ring-offset-2 
    disabled:opacity-50 disabled:cursor-not-allowed
    dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600
    transition-colors duration-200
    appearance-none bg-white
    bg-no-repeat bg-right bg-[length:16px_16px]
    pr-10
  `,h=r?`${p} border-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-600`:`${p} border-gray-300 focus:border-primary focus:ring-primary dark:focus:border-primary dark:focus:ring-primary`;return(0,a.jsxs)("div",{className:`space-y-1 ${d}`,children:[e&&(0,a.jsxs)("label",{htmlFor:u,className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:[e,s&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("select",{ref:g,id:u,value:m||"",onChange:e=>{o&&o(e.target.value)},className:`${h} ${l}`,...x,children:[n&&(0,a.jsx)("option",{value:"",disabled:!0,children:n}),i.map(e=>(0,a.jsx)("option",{value:e.value,disabled:e.disabled,children:e.label},e.value))]}),(0,a.jsx)("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none",children:(0,a.jsx)("i",{className:"ri-arrow-down-s-line text-gray-400 dark:text-gray-500"})})]}),r&&(0,a.jsxs)("p",{className:"text-sm text-red-600 dark:text-red-400 flex items-center",children:[(0,a.jsx)("i",{className:"ri-error-warning-line mr-1"}),r]}),t&&!r&&(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:t})]})});N.displayName="Select";let _=({onEditUser:e,onCreateUser:r})=>{let[t,i]=(0,s.useState)(null),[l,d]=(0,s.useState)(!0),[m,x]=(0,s.useState)(null),[g,u]=(0,s.useState)(!1),[p,h]=(0,s.useState)(null),[b,f]=(0,s.useState)(!1),[v,j]=(0,s.useState)({}),[_,C]=(0,s.useState)([]),[z,P]=(0,s.useState)([]),[S,D]=(0,s.useState)([]),[A,$]=(0,s.useState)({page:1,limit:10}),R=(0,s.useCallback)(async e=>{try{d(!0),x(null),$(e);let r=await c.D.getUsers(e);i(r)}catch(t){let r="Failed to load users";if(t&&"object"==typeof t)if("code"in t&&"ERR_NETWORK"===t.code)r="Unable to connect to the server. Please check if the backend is running or contact your administrator.";else if("message"in t&&"Network Error"===t.message)r="Unable to connect to the server. Please check if the backend is running or contact your administrator.";else if("response"in t&&t.response&&"object"==typeof t.response){if("status"in t.response){let e=t.response.status;401===e?r="Authentication required. Please log in again.":403===e?r="You do not have permission to view users.":500===e?r="Server error. Please try again later.":"data"in t.response&&t.response.data&&"object"==typeof t.response.data&&"message"in t.response.data&&"string"==typeof t.response.data.message&&(r=t.response.data.message)}}else"message"in t&&"string"==typeof t.message&&(r=t.message);x(r),i({data:[],meta:{itemsPerPage:e.limit||10,totalItems:0,currentPage:e.page||1,totalPages:0,sortBy:[],searchBy:[],search:"",select:[]},links:{current:""}})}finally{d(!1)}},[]);(0,s.useEffect)(()=>{R({page:1,limit:10}),E(),O(),L()},[R]),(0,s.useEffect)(()=>{let e={};Object.entries(v).forEach(([r,t])=>{void 0!==t&&""!==t.trim()&&(e[r]=t)}),R({page:1,limit:A.limit||10,filter:Object.keys(e).length>0?e:void 0})},[v,R,A.limit]);let E=async()=>{try{let e=(await o.getDepartments({page:1,limit:100})).data;C(e)}catch(e){C([])}},O=async()=>{try{let e=await n.O.getRoles({page:1,limit:100});P(e.data)}catch(e){}},L=async()=>{try{let e=await y.getOrganizations({page:1,limit:100});D(e.data)}catch(e){}},U=(e,r)=>{let t={...v};r&&""!==r.trim()?t[e]=r:delete t[e],j(t)},F=e=>{h(e),u(!0)},M=async()=>{if(p){f(!0);try{await c.D.deleteUser(p.user_id),t&&R({page:t.meta.currentPage,limit:t.meta.itemsPerPage}),u(!1),h(null)}catch(e){x("Failed to delete user")}finally{f(!1)}}},B=[{key:"user",label:"User",sortable:!0,render:(e,r)=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0 h-10 w-10",children:r.profile_image?(0,a.jsx)("img",{className:"h-10 w-10 rounded-full",src:r.profile_image,alt:`${r.first_name} ${r.last_name}`}):(0,a.jsx)("div",{className:"h-10 w-10 rounded-full bg-red-600 dark:bg-red-700 flex items-center justify-center",children:(0,a.jsxs)("span",{className:"text-sm font-medium text-white",children:[r.first_name.charAt(0),r.last_name.charAt(0)]})})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsxs)("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:[r.first_name," ",r.last_name]}),(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:r.email})]})]})},{key:"department",label:"Department",render:(e,r)=>(0,a.jsx)("span",{className:"text-sm text-gray-900 dark:text-gray-100",children:(()=>{if(r.department?.name)return r.department.name;if(r.department_id&&_.length>0){let e=_.find(e=>e.department_id===r.department_id);return e?e.name:"Department Not Found"}return r.department_id&&0===_.length?"Loading...":"No Department"})()})},{key:"roles",label:"Roles",render:(e,r)=>(0,a.jsx)("span",{className:"text-sm text-gray-900 dark:text-gray-100",children:r.roles&&r.roles.length>0?r.roles.map(e=>e?.name).join(", "):"No Roles"})},{key:"status",label:"Status",sortable:!0,render:e=>(0,a.jsx)("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${"active"===e?"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200":"suspended"===e?"bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200":"bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200"}`,children:e.charAt(0).toUpperCase()+e.slice(1)})},{key:"last_login",label:"Last Login",sortable:!0,render:e=>(0,a.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:e?new Date(e).toLocaleString():"Never"})},{key:"actions",label:"Actions",render:(r,t)=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>e(t),className:"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 p-1 rounded hover:bg-indigo-50 dark:hover:bg-indigo-900",title:"Edit user",children:(0,a.jsx)("i",{className:"ri-edit-line"})}),(0,a.jsx)("button",{onClick:()=>F(t),className:"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 p-1 rounded hover:bg-red-50 dark:hover:bg-red-900",title:"Delete user",children:(0,a.jsx)("i",{className:"ri-delete-bin-line"})})]})}];return(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:"Users"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Manage users and their access permissions."})]}),(0,a.jsx)("div",{children:(0,a.jsx)("div",{className:"flex space-x-2 place-content-start",children:(0,a.jsx)("div",{className:"relative",children:(0,a.jsxs)("button",{type:"button",onClick:r,className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-900",children:[(0,a.jsx)("div",{className:"w-5 h-5 flex items-center justify-center mr-2",children:(0,a.jsx)("i",{className:"ri-user-add-line"})}),"Add User"]})})})})]})}),m&&(0,a.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded",children:m}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4",children:"Filters"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[_.length>0&&(0,a.jsx)(N,{label:"Department",value:v.department_id||"",onChange:e=>U("department_id",e),options:[{value:"",label:"All Departments"},..._.map(e=>({value:e.department_id,label:e.name}))]}),S.length>0&&(0,a.jsx)(N,{label:"Organization",value:v.organization_id||"",onChange:e=>U("organization_id",e),options:[{value:"",label:"All Organizations"},...S.map(e=>({value:e.organization_id,label:e.name}))]}),(0,a.jsx)(N,{label:"Role",value:v.role||"",onChange:e=>U("role",e),options:[{value:"",label:"All Roles"},...z.map(e=>({value:e.role_id,label:e.name}))]}),(0,a.jsx)(N,{label:"Status",value:v.status||"",onChange:e=>U("status",e),options:[{value:"",label:"All Statuses"},{value:"active",label:"Active"},{value:"inactive",label:"Inactive"},{value:"suspended",label:"Suspended"}]})]})]}),(0,a.jsx)(k.A,{columns:B,data:t,loading:l,onQueryChange:R,searchPlaceholder:"Search users by name or email..."}),(0,a.jsx)(w,{isOpen:g,onClose:()=>{u(!1),h(null)},onConfirm:M,title:"Delete User",message:p?(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"mb-2",children:["Are you sure you want to delete ",(0,a.jsxs)("strong",{children:[p.first_name," ",p.last_name]}),"?"]}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"This action cannot be undone. All data associated with this user will be permanently removed."})]}):"Are you sure you want to delete this user?",confirmText:"Yes, Delete User",cancelText:"Cancel",confirmVariant:"danger",loading:b})]})},C=({onEditRole:e,onCreateRole:r})=>{let[t,i]=(0,s.useState)(null),[l,d]=(0,s.useState)(!0),[o,c]=(0,s.useState)(null),[m,x]=(0,s.useState)(!1),[g,u]=(0,s.useState)(null),[p,h]=(0,s.useState)(!1);(0,s.useEffect)(()=>{b({page:1,limit:10})},[]);let b=async e=>{try{d(!0);let r=await n.O.getRoles(e);i(r)}catch(r){i({data:[],meta:{itemsPerPage:e.limit||10,totalItems:0,currentPage:e.page||1,totalPages:0,sortBy:[],searchBy:[],search:"",select:[]},links:{current:""}})}finally{d(!1)}},f=e=>{u(e),x(!0)},y=async()=>{if(g){h(!0);try{await n.O.deleteRole(g.role_id),t&&b({page:t.meta.currentPage,limit:t.meta.itemsPerPage}),x(!1),u(null)}catch(e){c("Failed to delete role")}finally{h(!1)}}},v=[{key:"name",label:"Role Name",sortable:!0,render:e=>(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 capitalize",children:e})},{key:"description",label:"Description",render:e=>(0,a.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:e||"No description"})},{key:"created_at",label:"Created Date",sortable:!0,render:e=>(0,a.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:e?new Date(e).toLocaleDateString():"N/A"})},{key:"actions",label:"Actions",render:(r,t)=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>e(t),className:"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 p-1 rounded hover:bg-indigo-50 dark:hover:bg-indigo-900",title:"Edit role",children:(0,a.jsx)("i",{className:"ri-edit-line"})}),(0,a.jsx)("button",{onClick:()=>f(t),className:"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 p-1 rounded hover:bg-red-50 dark:hover:bg-red-900",title:"Delete role",children:(0,a.jsx)("i",{className:"ri-delete-bin-line"})})]})}];return(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:"Roles"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Manage user roles and their permissions."})]}),(0,a.jsx)("div",{className:"flex space-x-3 place-content-start",children:(0,a.jsx)("div",{className:"relative",children:(0,a.jsxs)("button",{onClick:r,className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-900",children:[(0,a.jsx)("div",{className:"w-5 h-5 flex items-center justify-center mr-2",children:(0,a.jsx)("i",{className:"ri-add-line"})}),"Add Role"]})})})]})}),o&&(0,a.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded",children:o}),(0,a.jsx)(k.A,{columns:v,data:t,loading:l,onQueryChange:b,searchPlaceholder:"Search roles by name or description..."}),(0,a.jsx)(w,{isOpen:m,onClose:()=>{x(!1),u(null)},onConfirm:y,title:"Delete Role",message:g?(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"mb-2",children:["Are you sure you want to delete the role ",(0,a.jsx)("strong",{children:g.name}),"?"]}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"This action cannot be undone. Users with this role will lose their associated permissions."})]}):"Are you sure you want to delete this role?",confirmText:"Yes, Delete Role",cancelText:"Cancel",confirmVariant:"danger",loading:p})]})},z=({onEditPermission:e,onCreatePermission:r})=>{let[t,n]=(0,s.useState)(null),[l,d]=(0,s.useState)(!0),[o,c]=(0,s.useState)(null);(0,s.useEffect)(()=>{m({page:1,limit:10})},[]);let m=async e=>{try{d(!0);let r=await i.p.getPermissions(e);n(r)}catch(r){n({data:[],meta:{itemsPerPage:e.limit||10,totalItems:0,currentPage:e.page||1,totalPages:0,sortBy:[],searchBy:[],search:"",select:[]},links:{current:""}})}finally{d(!1)}},x=[{key:"name",label:"Permission Name",sortable:!0,render:e=>(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:e.replace(/[_:]/g," ").replace(/\b\w/g,e=>e.toUpperCase())})},{key:"description",label:"Description",render:e=>(0,a.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:e||"No description"})},{key:"category",label:"Category",sortable:!0,render:e=>(0,a.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200",children:e})},{key:"roles",label:"Assigned Roles",render:(e,r)=>(0,a.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:r.roles&&r.roles.length>0?r.roles.map(e=>e.name).join(", "):"None"})},{key:"created_at",label:"Created Date",sortable:!0,render:e=>(0,a.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:e?new Date(e).toLocaleDateString():"N/A"})}];return(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:"Permissions"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Manage system permissions and their assignments."})]}),r&&(0,a.jsx)("div",{className:"flex space-x-3 place-content-start",children:(0,a.jsx)("div",{className:"relative",children:(0,a.jsxs)("button",{onClick:r,className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-900",children:[(0,a.jsx)("div",{className:"w-5 h-5 flex items-center justify-center mr-2",children:(0,a.jsx)("i",{className:"ri-add-line"})}),"Add Permission"]})})})]})}),o&&(0,a.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded",children:o}),(0,a.jsx)(k.A,{columns:x,data:t,loading:l,onQueryChange:m,searchPlaceholder:"Search permissions by name, description, or category..."})]})},P=({onEditDepartment:e,onCreateDepartment:r})=>{let[t,i]=(0,s.useState)([]),[n,l]=(0,s.useState)(!0),[d,c]=(0,s.useState)(null),[m,x]=(0,s.useState)(!1),[g,u]=(0,s.useState)(null),[p,h]=(0,s.useState)(!1);(0,s.useEffect)(()=>{b()},[]);let b=async()=>{try{l(!0),c(null);let e=await o.getAllDepartments();Array.isArray(e)?i(e):(i([]),c("Invalid data format received from server"))}catch(e){c("Failed to load departments"),i([])}finally{l(!1)}},f=e=>{u(e),x(!0)},y=async()=>{if(g)try{h(!0),await o.deleteDepartment(g.department_id),i(e=>e.filter(e=>e.department_id!==g.department_id)),x(!1),u(null)}catch(e){c("Failed to delete department")}finally{h(!1)}},v=Array.isArray(t)?t:[],j={data:v,meta:{itemsPerPage:v.length,totalItems:v.length,currentPage:1,totalPages:1,sortBy:[["created_at","DESC"]],searchBy:["name","code","email"],search:"",select:[],filter:{}},links:{first:"",previous:"",current:"",next:"",last:""}},N=[{key:"code",label:"Code",render:e=>(0,a.jsx)("span",{className:"font-mono text-sm font-medium text-gray-900 dark:text-gray-100",children:e})},{key:"name",label:"Name",render:e=>(0,a.jsx)("span",{className:"font-medium text-gray-900 dark:text-gray-100",children:e})},{key:"description",label:"Description",render:e=>(0,a.jsx)("span",{className:"text-gray-600 dark:text-gray-400 max-w-xs truncate",children:e})},{key:"email",label:"Email",render:e=>(0,a.jsx)("span",{className:"text-blue-600 dark:text-blue-400",children:e})},{key:"created_at",label:"Created",render:e=>(0,a.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:new Date(e).toLocaleDateString()})},{key:"actions",label:"Actions",render:(r,t)=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>e(t),className:"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 p-1 rounded hover:bg-indigo-50 dark:hover:bg-indigo-900",title:"Edit department",children:(0,a.jsx)("i",{className:"ri-edit-line"})}),(0,a.jsx)("button",{onClick:()=>f(t),className:"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 p-1 rounded hover:bg-red-50 dark:hover:bg-red-900",title:"Delete department",children:(0,a.jsx)("i",{className:"ri-delete-bin-line"})})]})}];return d?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"text-red-600 dark:text-red-400 mb-4",children:(0,a.jsx)("i",{className:"ri-error-warning-line text-4xl"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Error Loading Departments"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:d}),(0,a.jsxs)("button",{onClick:b,className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:[(0,a.jsx)("i",{className:"ri-refresh-line mr-2"}),"Try Again"]})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-gray-100",children:"Departments"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Manage organizational departments and their information."})]}),(0,a.jsx)("div",{children:(0,a.jsxs)("button",{onClick:r,className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:[(0,a.jsx)("i",{className:"ri-add-line mr-2"}),"Add Department"]})})]}),(0,a.jsx)(k.A,{columns:N,data:j,loading:n,onQueryChange:()=>{},searchPlaceholder:"Search departments by name, code, or email..."}),(0,a.jsx)(w,{isOpen:m,onClose:()=>{x(!1),u(null)},onConfirm:y,title:"Delete Department",message:g?`Are you sure you want to delete the department "${g.name}" (${g.code})? This action cannot be undone.`:"",confirmText:"Delete",cancelText:"Cancel",loading:p,confirmVariant:"danger"})]})},S=({onEditOrganization:e,onCreateOrganization:r})=>{let[t,i]=(0,s.useState)(null),[n,l]=(0,s.useState)(!0),[d,o]=(0,s.useState)(null),[c,m]=(0,s.useState)(!1),[x,g]=(0,s.useState)(null),[u,p]=(0,s.useState)(!1),[h,b]=(0,s.useState)({page:1,limit:10}),f=(0,s.useCallback)(async e=>{try{l(!0),o(null),b(e);let r=await y.getOrganizations(e);i(r)}catch(t){let r="Failed to load organizations";if(t&&"object"==typeof t)if("code"in t&&"ERR_NETWORK"===t.code)r="Unable to connect to the server. Please check if the backend is running or contact your administrator.";else if("message"in t&&"Network Error"===t.message)r="Unable to connect to the server. Please check if the backend is running or contact your administrator.";else if("response"in t&&t.response&&"object"==typeof t.response){if("status"in t.response){let e=t.response.status;401===e?r="Authentication required. Please log in again.":403===e?r="You do not have permission to view organizations.":500===e?r="Server error. Please try again later.":"data"in t.response&&t.response.data&&"object"==typeof t.response.data&&"message"in t.response.data&&"string"==typeof t.response.data.message&&(r=t.response.data.message)}}else"message"in t&&"string"==typeof t.message&&(r=t.message);o(r),i({data:[],meta:{itemsPerPage:e.limit||10,totalItems:0,currentPage:e.page||1,totalPages:0,sortBy:[],searchBy:[],search:"",select:[],filter:{}},links:{first:"",previous:"",current:"",next:"",last:""}})}finally{l(!1)}},[]);(0,s.useEffect)(()=>{f({page:1,limit:10})},[f]);let v=e=>{g(e),m(!0)},j=async()=>{if(x){p(!0);try{await y.deleteOrganization(x.organization_id),t&&f({page:t.meta.currentPage,limit:t.meta.itemsPerPage}),m(!1),g(null)}catch(e){o("Failed to delete organization")}finally{p(!1)}}},N=[{key:"organization",label:"Organization",sortable:!0,render:(e,r)=>(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("div",{className:"font-medium text-gray-900 dark:text-gray-100",children:r.name}),(0,a.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Reg: ",r.registration_number]})]})},{key:"contact",label:"Contact",render:(e,r)=>(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("div",{className:"text-sm text-blue-600 dark:text-blue-400",children:r.email}),(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:r.phone})]})},{key:"tpin",label:"TPIN",render:e=>(0,a.jsx)("span",{className:"font-mono text-sm font-medium text-gray-900 dark:text-gray-100",children:e})},{key:"website",label:"Website",render:e=>(0,a.jsx)("span",{className:"text-sm text-blue-600 dark:text-blue-400 hover:underline",children:e?(0,a.jsx)("a",{href:e,target:"_blank",rel:"noopener noreferrer",children:e.replace(/^https?:\/\//,"")}):(0,a.jsx)("span",{className:"text-gray-400",children:"-"})})},{key:"date_incorporation",label:"Incorporated",sortable:!0,render:e=>(0,a.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:new Date(e).toLocaleDateString()})},{key:"created_at",label:"Created",sortable:!0,render:e=>(0,a.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:new Date(e).toLocaleDateString()})},{key:"actions",label:"Actions",render:(r,t)=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>e(t),className:"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 p-1 rounded hover:bg-indigo-50 dark:hover:bg-indigo-900",title:"Edit organization",children:(0,a.jsx)("i",{className:"ri-edit-line"})}),(0,a.jsx)("button",{onClick:()=>v(t),className:"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 p-1 rounded hover:bg-red-50 dark:hover:bg-red-900",title:"Delete organization",children:(0,a.jsx)("i",{className:"ri-delete-bin-line"})})]})}];return d?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"text-red-600 dark:text-red-400 mb-4",children:(0,a.jsx)("i",{className:"ri-error-warning-line text-4xl"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Error Loading Organizations"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:d}),(0,a.jsxs)("button",{onClick:()=>f({page:1,limit:10}),className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:[(0,a.jsx)("i",{className:"ri-refresh-line mr-2"}),"Try Again"]})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-gray-100",children:"Organizations"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Manage organizations and their information."})]}),(0,a.jsx)("div",{children:(0,a.jsxs)("button",{onClick:r,className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:[(0,a.jsx)("i",{className:"ri-add-line mr-2"}),"Add Organization"]})})]}),(0,a.jsx)(k.A,{columns:N,data:t,loading:n,onQueryChange:f,searchPlaceholder:"Search organizations by name, registration number, or email..."}),(0,a.jsx)(w,{isOpen:c,onClose:()=>{m(!1),g(null)},onConfirm:j,title:"Delete Organization",message:x?(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"mb-2",children:["Are you sure you want to delete ",(0,a.jsx)("strong",{children:x.name}),"?"]}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"This action cannot be undone. All data associated with this organization will be permanently removed."})]}):"Are you sure you want to delete this organization?",confirmText:"Yes, Delete Organization",cancelText:"Cancel",confirmVariant:"danger",loading:u})]})};function D(){let[e,r]=(0,s.useState)([]),[t,i]=(0,s.useState)([]),[l,d]=(0,s.useState)([]),[o,c]=(0,s.useState)([]),[m,x]=(0,s.useState)(!1),[g,u]=(0,s.useState)(!1),[p,y]=(0,s.useState)(!1),[k,w]=(0,s.useState)(!1),[N,D]=(0,s.useState)(null),[A,$]=(0,s.useState)(null),[R,E]=(0,s.useState)(null),[O,L]=(0,s.useState)(null),[U,F]=(0,s.useState)("users"),[M,B]=(0,s.useState)(""),[q,T]=(0,s.useState)(""),I=async e=>{try{let r=await n.O.getRoleWithPermissions(e.role_id);$(r),u(!0)}catch(r){$(e),u(!0)}},W=()=>{x(!1),D(null)},G=()=>{u(!1),$(null)},Y=()=>{y(!1),E(null)},Q=()=>{w(!1),L(null)},V=[{id:"users",label:"Users",content:(0,a.jsx)(_,{onEditUser:e=>{D(e),x(!0)},onCreateUser:()=>{D(null),x(!0)}})},{id:"roles",label:"Roles",content:(0,a.jsx)(C,{onEditRole:I,onCreateRole:()=>{$(null),u(!0)}})},{id:"permissions",label:"Permissions",content:(0,a.jsx)(z,{})},{id:"departments",label:"Departments",content:(0,a.jsx)(P,{onEditDepartment:e=>{E(e),y(!0)},onCreateDepartment:()=>{E(null),y(!0)}})},{id:"organizations",label:"Organizations",content:(0,a.jsx)(S,{onEditOrganization:e=>{L(e),w(!0)},onCreateOrganization:()=>{L(null),w(!0)}})}];return(0,a.jsxs)("main",{className:"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-4 sm:p-6",children:[(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[M&&(0,a.jsx)("div",{className:"mb-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 relative animate-in slide-in-from-top-2 duration-300",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"h-5 w-5 text-green-400 dark:text-green-500",children:(0,a.jsx)("svg",{fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})})}),(0,a.jsxs)("div",{className:"ml-3 flex-1",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-green-800 dark:text-green-200 mb-1",children:"Success"}),(0,a.jsx)("p",{className:"text-sm text-green-700 dark:text-green-300",children:M})]}),(0,a.jsx)("div",{className:"ml-4 flex-shrink-0",children:(0,a.jsx)("button",{type:"button",onClick:()=>B(""),className:"inline-flex text-green-400 hover:text-green-600 dark:text-green-500 dark:hover:text-green-300 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 rounded-md p-1 transition-colors duration-200","aria-label":"Dismiss success message",children:(0,a.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})})]})}),q&&(0,a.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 relative animate-in slide-in-from-top-2 duration-300",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"h-5 w-5 text-red-400 dark:text-red-500",children:(0,a.jsx)("svg",{fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})})}),(0,a.jsxs)("div",{className:"ml-3 flex-1",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-red-800 dark:text-red-200 mb-1",children:"Error"}),(0,a.jsx)("p",{className:"text-sm text-red-700 dark:text-red-300",children:q})]}),(0,a.jsx)("div",{className:"ml-4 flex-shrink-0",children:(0,a.jsx)("button",{type:"button",onClick:()=>T(""),className:"inline-flex text-red-400 hover:text-red-600 dark:text-red-500 dark:hover:text-red-300 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 rounded-md p-1 transition-colors duration-200","aria-label":"Dismiss error message",children:(0,a.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})})]})}),(0,a.jsx)(j,{tabs:V,activeTab:U,onTabChange:e=>{F(e)}})]}),(0,a.jsx)(h,{isOpen:m,onClose:W,onSave:()=>{W()},user:N,roles:o,departments:t,organizations:l,onNavigateToOrganizations:()=>{F("organizations"),m&&(x(!1),D(null))}}),(0,a.jsx)(b.A,{isOpen:g,onClose:G,onSave:(e,r=!1)=>{G(),B(`Role "${e}" has been ${r?"updated":"created"} successfully!`),T(""),setTimeout(()=>{B("")},5e3)},role:A,permissions:e}),(0,a.jsx)(f,{isOpen:p,onClose:Y,onSave:e=>{Y();let r=R?"updated":"created";B(`Department "${e.name}" has been ${r} successfully!`),T(""),setTimeout(()=>{B("")},5e3)},department:R}),(0,a.jsx)(v,{isOpen:k,onClose:Q,onSave:e=>{Q();let r=O?"updated":"created";B(`Organization "${e.name}" has been ${r} successfully!`),T(""),setTimeout(()=>{B("")},5e3)},organization:O})]})}},94735:e=>{"use strict";e.exports=require("events")},96409:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var a=t(65239),s=t(48088),i=t(88170),n=t.n(i),l=t(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(r,d);let o={children:["",{children:["users",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,14103)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\users\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,73888)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\users\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\users\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/users/page",pathname:"/users",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},98578:(e,r,t)=>{Promise.resolve().then(t.bind(t,93247))}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,7498,1658,5814,2335,6606,88,4182],()=>t(96409));module.exports=a})();