{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/app/customer/auth/setup-2fa/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useState } from 'react';\r\nimport Image from 'next/image';\r\nimport { useRouter } from 'next/navigation';\r\nimport { authService } from '@/services/auth.service';\r\nimport { XCircleIcon } from '@heroicons/react/24/solid';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport Loader from '@/components/Loader';\r\n\r\nexport default function CustomerSetupTwoFactorPage() {\r\n  const router = useRouter();\r\n  const { user, token: access_token, loading: authLoading } = useAuth();\r\n\r\n  const [qrCodeUrl, setQrCodeUrl] = useState('');\r\n  const [secret, setSecret] = useState('');\r\n  const [error, setError] = useState('');\r\n  const [success, setSuccess] = useState('');\r\n  const [loadingMessage, setLoadingMessage] = useState('Initializing 2FA setup...');\r\n  const [alreadyEnabled, setAlreadyEnabled] = useState(false);\r\n  const [setUpComplete, setSetUpComplete] = useState(false);\r\n  const [loading, setLoading] = useState(true);\r\n  const [unauthorizedAccess, setUnauthorizedAccess] = useState(false);\r\n\r\n  const redirectLogin = () => router.replace('/customer/auth/login');\r\n  const redirectDashboard = () => router.push('/customer');\r\n\r\n  useEffect(() => {\r\n    if (authLoading) return;\r\n\r\n    const setupUser = sessionStorage.getItem('2fa_setup_user');\r\n    const setupToken = sessionStorage.getItem('2fa_setup_token');\r\n\r\n    let currentUser = user;\r\n    let currentToken = access_token;\r\n\r\n    if (setupUser && setupToken) {\r\n      try {\r\n        currentUser = JSON.parse(setupUser);\r\n        currentToken = setupToken;\r\n        authService.setAuthToken(setupToken);\r\n      } catch (err) {\r\n        console.error('Failed to parse 2FA setup session data:', err);\r\n      }\r\n    }\r\n\r\n    if (!currentUser || !currentToken) {\r\n      setLoading(false);\r\n      const hasSetupData = setupUser && setupToken;\r\n      const hasAuthContext = user && access_token;\r\n\r\n      if (!hasSetupData && !hasAuthContext) {\r\n        setUnauthorizedAccess(true);\r\n        setError('Unauthorized access. This page can only be accessed during 2FA setup after login.');\r\n        setLoadingMessage('Please login to continue.');\r\n      } else {\r\n        setError('Your session has expired or is invalid. Please login again to continue.');\r\n        setLoadingMessage('Session expired. Redirecting...');\r\n      }\r\n\r\n      setLoading(true);\r\n      setTimeout(redirectLogin, 5000);\r\n      return;\r\n    }\r\n\r\n    const initiate2FA = async () => {\r\n      try {\r\n        const { qr_code_data_url, secret, message } = await authService.setupTwoFactorAuth({\r\n          access_token: currentToken,\r\n          user_id: currentUser.user_id,\r\n        });\r\n\r\n        setQrCodeUrl(qr_code_data_url);\r\n        setSecret(secret);\r\n        setSuccess(message || '2FA setup successful');\r\n        setSetUpComplete(true);\r\n\r\n        sessionStorage.removeItem('2fa_setup_user');\r\n        sessionStorage.removeItem('2fa_setup_token');\r\n\r\n        setTimeout(redirectLogin, 7000);\r\n      } catch (err: any) {\r\n        const msg: string =\r\n          err?.response?.data?.message ||\r\n          err?.message ||\r\n          'Failed to initiate 2FA setup. Redirecting...';\r\n\r\n        const isEnabled = msg.toLowerCase().includes('enabled');\r\n        const isInitiated = msg.toLowerCase().includes('initiation');\r\n\r\n        setAlreadyEnabled(isEnabled);\r\n        setSetUpComplete(isInitiated);\r\n\r\n        if (isEnabled) {\r\n          setSuccess(msg);\r\n          setTimeout(redirectDashboard, 2000);\r\n        } else {\r\n          setError(msg);\r\n          setTimeout(redirectLogin, 5000);\r\n        }\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    initiate2FA();\r\n  }, [authLoading, user, access_token]);\r\n\r\n  if (authLoading || loading) {\r\n    return (\r\n      <div className=\"min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900\">\r\n        <div className=\"sm:mx-auto sm:w-full sm:max-w-md\">\r\n          <Loader message={loadingMessage} />\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900\">\r\n      <div className=\"sm:mx-auto sm:w-full sm:max-w-md text-center\">\r\n        <Image\r\n          src=\"/images/macra-logo.png\"\r\n          alt=\"MACRA Logo\"\r\n          width={50}\r\n          height={50}\r\n          className=\"mx-auto h-16 w-auto\"\r\n        />\r\n        <h2 className=\"mt-6 text-3xl font-extrabold text-gray-900 dark:text-white\">\r\n          {setUpComplete && !alreadyEnabled ? (\r\n            <span className=\"text-green-600 dark:text-green-300\">2FA Setup Complete</span>\r\n          ) : alreadyEnabled ? (\r\n            <span className=\"text-green-600 dark:text-green-300\">2FA Already Enabled</span>\r\n          ) : (\r\n            'Two Factor Authentication Setup'\r\n          )}\r\n        </h2>\r\n      </div>\r\n\r\n      <div className=\"mt-8 sm:mx-auto sm:w-full sm:max-w-md\">\r\n        <div className=\"bg-white dark:bg-gray-800 py-8 px-6 shadow rounded-lg sm:px-10\">\r\n          {error && !alreadyEnabled && (\r\n            <div className=\"flex flex-col items-center justify-center\">\r\n              <div className=\"w-16 h-16 mb-4 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center\">\r\n                <XCircleIcon className=\"w-10 h-10 animate-pulse shadow-md text-red-600 dark:text-red-300\" />\r\n              </div>\r\n              <div className=\"mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-md text-center\">\r\n                {error}\r\n              </div>\r\n              {unauthorizedAccess && (\r\n                <div className=\"mt-4\">\r\n                  <button\r\n                    onClick={redirectLogin}\r\n                    className=\"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n                  >\r\n                    Go to Login\r\n                  </button>\r\n                </div>\r\n              )}\r\n            </div>\r\n          )}\r\n\r\n          {(success || alreadyEnabled || setUpComplete) && (\r\n            <div className=\"flex flex-col items-center justify-center text-center text-gray-600 dark:text-gray-400\">\r\n              <div className=\"w-16 h-16 mb-4 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center animate-bounce shadow-md\">\r\n                <svg\r\n                  className=\"w-8 h-8 text-green-600 dark:text-green-300\"\r\n                  fill=\"none\"\r\n                  stroke=\"currentColor\"\r\n                  strokeWidth=\"3\"\r\n                  viewBox=\"0 0 24 24\"\r\n                >\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M5 13l4 4L19 7\" />\r\n                </svg>\r\n              </div>\r\n              <div>\r\n                {success}\r\n                {alreadyEnabled && (\r\n                  <p className=\"text-sm text-gray-400 dark:text-gray-200 p-2\">\r\n                    Two-Factor Authentication is already enabled. Please contact support if you need to reset it.\r\n                  </p>\r\n                )}\r\n                {setUpComplete && (\r\n                  <p className=\"text-sm text-gray-400 dark:text-gray-200 p-2\">\r\n                    This link is valid for 5 minutes and can only be used once.\r\n                  </p>\r\n                )}\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAUe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,OAAO,YAAY,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAElE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,MAAM,gBAAgB,IAAM,OAAO,OAAO,CAAC;IAC3C,MAAM,oBAAoB,IAAM,OAAO,IAAI,CAAC;IAE5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa;QAEjB,MAAM,YAAY,eAAe,OAAO,CAAC;QACzC,MAAM,aAAa,eAAe,OAAO,CAAC;QAE1C,IAAI,cAAc;QAClB,IAAI,eAAe;QAEnB,IAAI,aAAa,YAAY;YAC3B,IAAI;gBACF,cAAc,KAAK,KAAK,CAAC;gBACzB,eAAe;gBACf,kIAAA,CAAA,cAAW,CAAC,YAAY,CAAC;YAC3B,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,2CAA2C;YAC3D;QACF;QAEA,IAAI,CAAC,eAAe,CAAC,cAAc;YACjC,WAAW;YACX,MAAM,eAAe,aAAa;YAClC,MAAM,iBAAiB,QAAQ;YAE/B,IAAI,CAAC,gBAAgB,CAAC,gBAAgB;gBACpC,sBAAsB;gBACtB,SAAS;gBACT,kBAAkB;YACpB,OAAO;gBACL,SAAS;gBACT,kBAAkB;YACpB;YAEA,WAAW;YACX,WAAW,eAAe;YAC1B;QACF;QAEA,MAAM,cAAc;YAClB,IAAI;gBACF,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,kIAAA,CAAA,cAAW,CAAC,kBAAkB,CAAC;oBACjF,cAAc;oBACd,SAAS,YAAY,OAAO;gBAC9B;gBAEA,aAAa;gBACb,UAAU;gBACV,WAAW,WAAW;gBACtB,iBAAiB;gBAEjB,eAAe,UAAU,CAAC;gBAC1B,eAAe,UAAU,CAAC;gBAE1B,WAAW,eAAe;YAC5B,EAAE,OAAO,KAAU;gBACjB,MAAM,MACJ,KAAK,UAAU,MAAM,WACrB,KAAK,WACL;gBAEF,MAAM,YAAY,IAAI,WAAW,GAAG,QAAQ,CAAC;gBAC7C,MAAM,cAAc,IAAI,WAAW,GAAG,QAAQ,CAAC;gBAE/C,kBAAkB;gBAClB,iBAAiB;gBAEjB,IAAI,WAAW;oBACb,WAAW;oBACX,WAAW,mBAAmB;gBAChC,OAAO;oBACL,SAAS;oBACT,WAAW,eAAe;gBAC5B;YACF,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG;QAAC;QAAa;QAAM;KAAa;IAEpC,IAAI,eAAe,SAAS;QAC1B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,4HAAA,CAAA,UAAM;oBAAC,SAAS;;;;;;;;;;;;;;;;IAIzB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;;kCAEZ,8OAAC;wBAAG,WAAU;kCACX,iBAAiB,CAAC,+BACjB,8OAAC;4BAAK,WAAU;sCAAqC;;;;;mCACnD,+BACF,8OAAC;4BAAK,WAAU;sCAAqC;;;;;mCAErD;;;;;;;;;;;;0BAKN,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;wBACZ,SAAS,CAAC,gCACT,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,mNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;8CAEzB,8OAAC;oCAAI,WAAU;8CACZ;;;;;;gCAEF,oCACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;wBAQR,CAAC,WAAW,kBAAkB,aAAa,mBAC1C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,WAAU;wCACV,MAAK;wCACL,QAAO;wCACP,aAAY;wCACZ,SAAQ;kDAER,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,GAAE;;;;;;;;;;;;;;;;8CAGzD,8OAAC;;wCACE;wCACA,gCACC,8OAAC;4CAAE,WAAU;sDAA+C;;;;;;wCAI7D,+BACC,8OAAC;4CAAE,WAAU;sDAA+C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW9E", "debugId": null}}, {"offset": {"line": 313, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/node_modules/%40heroicons/react/24/solid/esm/XCircleIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction XCircleIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm-1.72 6.97a.75.75 0 1 0-1.06 1.06L10.94 12l-1.72 1.72a.75.75 0 1 0 1.06 1.06L12 13.06l1.72 1.72a.75.75 0 1 0 1.06-1.06L13.06 12l1.72-1.72a.75.75 0 1 0-1.06-1.06L12 10.94l-1.72-1.72Z\",\n    clipRule: \"evenodd\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(XCircleIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,YAAY,EACnB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,SAAS;QACT,MAAM;QACN,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,UAAU;QACV,GAAG;QACH,UAAU;IACZ;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}]}