import { apiClient } from '../lib/apiClient';

export interface ScopeOfServiceData {
  scope_of_service_id?: string;
  application_id?: string;
  nature_of_service: string;
  premises: string;
  transport_type: string;
  customer_assistance: string;
  created_at?: string;
  updated_at?: string;
}

export interface CreateScopeOfServiceData {
  application_id: string;
  nature_of_service: string;
  premises: string;
  transport_type: string;
  customer_assistance: string;
}

export interface UpdateScopeOfServiceData {
  scope_of_service_id: string;
  nature_of_service?: string;
  premises?: string;
  transport_type?: string;
  customer_assistance?: string;
}

export const scopeOfServiceService = {
  // Create new scope of service
  async createScopeOfService(data: CreateScopeOfServiceData): Promise<ScopeOfServiceData> {
    try {
      console.log('Creating scope of service with data:', data);
      // TODO: Backend scope of service endpoints not available yet
      console.warn('Scope of service endpoints not implemented yet - returning mock data');
      return {
        scope_of_service_id: `mock-${Date.now()}`,
        ...data,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      // const response = await apiClient.post('/scope-of-service', data);
      // return processApiResponse(response);
    } catch (error) {
      console.error('ScopeOfServiceService.createScopeOfService error:', error);
      throw error;
    }
  },

  // Get scope of service by ID
  async getScopeOfService(id: string): Promise<ScopeOfServiceData> {
    try {
      // TODO: Backend scope of service endpoints not available yet
      console.warn('Scope of service endpoints not implemented yet - returning empty data');
      throw new Error('Scope of service not found');
      // const response = await apiClient.get(`/scope-of-service/${id}`);
      // return processApiResponse(response);
    } catch (error) {
      console.error('ScopeOfServiceService.getScopeOfService error:', error);
      throw error;
    }
  },

  // Get scope of service by application ID
  async getScopeOfServiceByApplication(applicationId: string): Promise<ScopeOfServiceData | null> {
    try {
      // TODO: Backend scope of service endpoints not available yet
      console.warn('Scope of service endpoints not implemented yet - returning null');
      return null;
      // const response = await apiClient.get(`/scope-of-service/application/${applicationId}`);
      // return processApiResponse(response);
    } catch (error) {
      console.error('ScopeOfServiceService.getScopeOfServiceByApplication error:', error);
      return null; // Return null instead of throwing
    }
  },

  // Update scope of service
  async updateScopeOfService(data: UpdateScopeOfServiceData): Promise<ScopeOfServiceData> {
    try {
      console.log('Updating scope of service:', data.scope_of_service_id, data);
      // TODO: Backend scope of service endpoints not available yet
      console.warn('Scope of service endpoints not implemented yet - returning mock data');
      return {
        scope_of_service_id: data.scope_of_service_id,
        application_id: '',
        nature_of_service: data.nature_of_service || '',
        premises: data.premises || '',
        transport_type: data.transport_type || '',
        customer_assistance: data.customer_assistance || '',
        updated_at: new Date().toISOString()
      };
      // const response = await apiClient.put(`/scope-of-service/${data.scope_of_service_id}`, data);
      // return processApiResponse(response);
    } catch (error) {
      console.error('ScopeOfServiceService.updateScopeOfService error:', error);
      throw error;
    }
  },

  // Create or update scope of service for an application
  async createOrUpdateScopeOfService(applicationId: string, data: Omit<CreateScopeOfServiceData, 'application_id'>): Promise<ScopeOfServiceData> {
    try {
      // Check if scope of service already exists for this application
      const existing = await this.getScopeOfServiceByApplication(applicationId);
      
      if (existing) {
        // Update existing scope of service
        return await this.updateScopeOfService({
          scope_of_service_id: existing.scope_of_service_id!,
          ...data
        });
      } else {
        // Create new scope of service
        return await this.createScopeOfService({
          application_id: applicationId,
          ...data
        });
      }
    } catch (error) {
      console.error('ScopeOfServiceService.createOrUpdateScopeOfService error:', error);
      throw error;
    }
  }
};
