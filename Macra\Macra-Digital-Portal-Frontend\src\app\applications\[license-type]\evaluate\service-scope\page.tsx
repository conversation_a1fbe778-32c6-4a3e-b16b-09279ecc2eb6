'use client';

import React, { useState, useEffect, use } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { ApplicationLayout } from '@/components/applications';
import { useDynamicNavigation } from '@/hooks/useDynamicNavigation';
import { applicationService } from '@/services/applicationService';
import { scopeOfServiceService } from '@/services/scopeOfServiceService';

// Define ApplicationStatus enum locally
enum ApplicationStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  UNDER_REVIEW = 'under_review',
  EVALUATION = 'evaluation',
  APPROVED = 'approved',
  REJECTED = 'rejected'
}

interface EvaluateServiceScopePageProps {
  params: Promise<{
    'license-type': string;
  }>;
}

const EvaluateServiceScopePage: React.FC<EvaluateServiceScopePageProps> = ({ params }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated, loading: authLoading, user } = useAuth();

  // Unwrap params using React.use()
  const resolvedParams = use(params);
  const licenseType = resolvedParams['license-type'];
  const applicationId = searchParams.get('application_id');

  // State
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [application, setApplication] = useState<any>(null);
  const [scopeData, setScopeData] = useState<any>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [licenseCategoryId, setLicenseCategoryId] = useState<string | null>(null);

  // Dynamic navigation hook - same as apply pages
  const {
    nextStep,
    previousStep,
  } = useDynamicNavigation({
    currentStepRoute: 'service-scope',
    licenseCategoryId,
    applicationId
  });

  // Load application and scope data
  useEffect(() => {
    const loadData = async () => {
      if (!applicationId || !isAuthenticated) return;

      try {
        setLoading(true);
        setError(null);

        // Load application details
        const appResponse = await applicationService.getApplication(applicationId);
        setApplication(appResponse);

        // Set license category ID for navigation
        if (appResponse?.license_category_id) {
          setLicenseCategoryId(appResponse.license_category_id);
        }

        // Load scope of service data if available
        if (applicationId) {
          try {
            const scopeResponse = await scopeOfServiceService.getScopeOfServiceByApplication(applicationId);
            setScopeData(scopeResponse);
          } catch (err) {
            console.error('Error loading scope data:', err);
            // Continue without scope data
          }
        }
      } catch (err: any) {
        console.error('Error loading application data:', err);
        setError('Failed to load application data');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [applicationId, isAuthenticated]);

  // Navigation handlers - modified for evaluation
  const handleNext = () => {
    if (!applicationId || !nextStep) return;

    const params = new URLSearchParams();
    params.set('application_id', applicationId);
    if (licenseCategoryId) {
      params.set('license_category_id', licenseCategoryId);
    }
    router.push(`/applications/${licenseType}/evaluate/${nextStep.route}?${params.toString()}`);
  };

  const handlePrevious = () => {
    if (!applicationId || !previousStep) return;

    const params = new URLSearchParams();
    params.set('application_id', applicationId);
    if (licenseCategoryId) {
      params.set('license_category_id', licenseCategoryId);
    }
    router.push(`/applications/${licenseType}/evaluate/${previousStep.route}?${params.toString()}`);
  };

  // Evaluation handlers
  const handleStatusUpdate = async (status: ApplicationStatus, comment: string) => {
    if (!applicationId) return;

    try {
      setIsSubmitting(true);
      console.log('Status update:', { applicationId, status, comment });
      console.log('Status updated successfully');
    } catch (err) {
      console.error('Error updating status:', err);
      setError('Failed to update application status');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCommentSave = async (comment: string) => {
    if (!applicationId) return;

    try {
      setIsSubmitting(true);
      console.log('Comment save:', { applicationId, step: 'service-scope', comment });
      console.log('Comment saved successfully');
    } catch (err) {
      console.error('Error saving comment:', err);
      setError('Failed to save comment');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAttachmentUpload = async (file: File) => {
    if (!applicationId) return;

    try {
      setIsSubmitting(true);
      console.log('Attachment upload:', { applicationId, step: 'service-scope', fileName: file.name });
      console.log('Attachment uploaded successfully');
    } catch (err) {
      console.error('Error uploading attachment:', err);
      setError('Failed to upload attachment');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Loading state
  if (authLoading || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading application data...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <i className="ri-error-warning-line text-4xl text-red-500 mb-4"></i>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Error Loading Application</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
          <button
            onClick={() => router.push('/applications')}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            Back to Applications
          </button>
        </div>
      </div>
    );
  }

  // No application found
  if (!application) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <i className="ri-file-search-line text-4xl text-gray-400 mb-4"></i>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Application Not Found</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">The requested application could not be found.</p>
          <button
            onClick={() => router.push('/applications')}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            Back to Applications
          </button>
        </div>
      </div>
    );
  }

  return (
    <ApplicationLayout
      onNext={handleNext}
      onPrevious={handlePrevious}
      showNextButton={!!nextStep}
      showPreviousButton={!!previousStep}
      nextButtonDisabled={isSubmitting}
      previousButtonDisabled={isSubmitting}
      nextButtonText={nextStep ? `Continue to ${nextStep.name}` : "Continue"}
      previousButtonText={previousStep ? `Back to ${previousStep.name}` : "Back"}
    >
      {/* Service Scope Information Display */}
      <div className="space-y-6">
        {scopeData ? (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Service Type
                </label>
                <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-md border">
                  <p className="text-gray-900 dark:text-gray-100">
                    {scopeData?.service_type || 'Not provided'}
                  </p>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Coverage Area
                </label>
                <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-md border">
                  <p className="text-gray-900 dark:text-gray-100">
                    {scopeData?.coverage_area || 'Not provided'}
                  </p>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Target Market
                </label>
                <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-md border">
                  <p className="text-gray-900 dark:text-gray-100">
                    {scopeData?.target_market || 'Not provided'}
                  </p>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Expected Launch Date
                </label>
                <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-md border">
                  <p className="text-gray-900 dark:text-gray-100">
                    {scopeData?.expected_launch_date ? 
                      new Date(scopeData.expected_launch_date).toLocaleDateString() : 
                      'Not provided'
                    }
                  </p>
                </div>
              </div>
            </div>

            {/* Service Description */}
            {scopeData?.service_description && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Service Description
                </label>
                <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-md border">
                  <p className="text-gray-900 dark:text-gray-100 whitespace-pre-wrap">
                    {scopeData.service_description}
                  </p>
                </div>
              </div>
            )}

            {/* Technical Specifications */}
            {scopeData?.technical_specifications && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Technical Specifications
                </label>
                <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-md border">
                  <p className="text-gray-900 dark:text-gray-100 whitespace-pre-wrap">
                    {scopeData.technical_specifications}
                  </p>
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-8">
            <i className="ri-file-search-line text-4xl text-gray-400 mb-4"></i>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No Service Scope Data</h3>
            <p className="text-gray-600 dark:text-gray-400">
              No service scope information has been provided for this application.
            </p>
          </div>
        )}

        {/* Evaluation Section */}
        <div className="mt-8 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-6 border border-yellow-200 dark:border-yellow-800">
          <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200 mb-4 flex items-center">
            <i className="ri-clipboard-line mr-2"></i>
            Evaluation Notes
          </h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-yellow-700 dark:text-yellow-300 mb-2">
                Evaluator Comments
              </label>
              <textarea
                rows={4}
                className="w-full px-3 py-2 border border-yellow-300 dark:border-yellow-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 dark:bg-yellow-900/30 dark:text-yellow-100"
                placeholder="Add your evaluation comments for the service scope information..."
              />
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <label className="block text-sm font-medium text-yellow-700 dark:text-yellow-300">
                  Status:
                </label>
                <select className="px-3 py-1 border border-yellow-300 dark:border-yellow-600 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 dark:bg-yellow-900/30 dark:text-yellow-100">
                  <option value="pending">Pending Review</option>
                  <option value="approved">Approved</option>
                  <option value="needs_revision">Needs Revision</option>
                  <option value="rejected">Rejected</option>
                </select>
              </div>
              <button
                type="button"
                className="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 transition-colors"
              >
                Save Evaluation
              </button>
            </div>
          </div>
        </div>
      </div>
    </ApplicationLayout>
  );
};

export default EvaluateServiceScopePage;
