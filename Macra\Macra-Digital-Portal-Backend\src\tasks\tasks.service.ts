import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, IsNull, Not, LessThan } from 'typeorm';
import { Task, TaskStatus } from '../entities/tasks.entity';
import { CreateTaskDto } from '../dto/tasks/create-task.dto';
import { UpdateTaskDto } from '../dto/tasks/update-task.dto';
import { AssignTaskDto } from '../dto/tasks/assign-task.dto';
import { PaginateQuery, Paginated, PaginateConfig, paginate } from 'nestjs-paginate';

@Injectable()
export class TasksService {
  constructor(
    @InjectRepository(Task)
    private readonly tasksRepository: Repository<Task>,
  ) {}

  private readonly paginateConfig: PaginateConfig<Task> = {
    sortableColumns: ['created_at', 'updated_at', 'task_number', 'title', 'status', 'priority'],
    searchableColumns: ['task_number', 'title', 'description'],
    defaultSortBy: [['created_at', 'DESC']],
    defaultLimit: 20,
    maxLimit: 100,
    relations: ['assignee', 'assigner', 'application'],
    select: [
      'task_id',
      'task_number',
      'title',
      'description',
      'task_type',
      'status',
      'priority',
      'assigned_to',
      'assigned_by',
      'assigned_at',
      'due_date',
      'completed_at',
      'application_id',
      'review_notes',
      'completion_notes',
      'created_at',
      'updated_at',
      'assignee.user_id',
      'assignee.first_name',
      'assignee.last_name',
      'assignee.email',
      'assigner.user_id',
      'assigner.first_name',
      'assigner.last_name',
      'assigner.email',
      'application.application_id',
      'application.application_number',
    ],
  };

  async create(createTaskDto: CreateTaskDto, assignerId: string): Promise<Task> {
    // Generate task number
    const taskCount = await this.tasksRepository.count();
    const taskNumber = `TASK-${String(taskCount + 1).padStart(6, '0')}`;

    const taskData: Partial<Task> = {
      task_type: createTaskDto.task_type,
      title: createTaskDto.title,
      description: createTaskDto.description,
      priority: createTaskDto.priority,
      status: createTaskDto.status,
      task_number: taskNumber,
      assigned_by: assignerId,
      assigned_to: createTaskDto.assigned_to,
    };

    if (createTaskDto.due_date) {
      taskData.due_date = new Date(createTaskDto.due_date);
    }

    if (createTaskDto.assigned_to) {
      taskData.assigned_at = new Date();
    }

    const task = this.tasksRepository.create(taskData);
    return this.tasksRepository.save(task);
  }

  async findAll(query: PaginateQuery): Promise<Paginated<Task>> {
    return paginate(query, this.tasksRepository, this.paginateConfig);
  }

  async findUnassigned(query: PaginateQuery): Promise<Paginated<Task>> {
    const config: PaginateConfig<Task> = {
      ...this.paginateConfig,
      where: { assigned_to: IsNull() },
    };

    return paginate(query, this.tasksRepository, config);
  }

  async findAssigned(query: PaginateQuery): Promise<Paginated<Task>> {
    const config: PaginateConfig<Task> = {
      ...this.paginateConfig,
      where: { assigned_to: Not(IsNull()) },
    };

    return paginate(query, this.tasksRepository, config);
  }

  async findAssignedToUser(userId: string, query: PaginateQuery): Promise<Paginated<Task>> {
    const config: PaginateConfig<Task> = {
      ...this.paginateConfig,
      where: { assigned_to: userId },
    };

    return paginate(query, this.tasksRepository, config);
  }

  async findOne(id: string): Promise<Task> {
    const task = await this.tasksRepository.findOne({
      where: { task_id: id },
      relations: ['assignee', 'assigner', 'application'],
    });

    if (!task) {
      throw new NotFoundException(`Task with ID ${id} not found`);
    }

    return task;
  }

  async update(id: string, updateTaskDto: UpdateTaskDto): Promise<Task> {
    const task = await this.findOne(id);

    // Update completion timestamp if status is changed to completed
    if (updateTaskDto.status === 'completed' && task.status !== 'completed') {
      task.completed_at = new Date();
    }

    Object.assign(task, updateTaskDto);
    return this.tasksRepository.save(task);
  }

  async assign(id: string, assignTaskDto: AssignTaskDto, assignerId: string): Promise<Task> {
    const task = await this.findOne(id);

    if (task.assigned_to) {
      throw new BadRequestException('Task is already assigned to another user');
    }

    task.assigned_to = assignTaskDto.assignedTo;
    task.assigned_by = assignerId;
    task.assigned_at = new Date();

    if (assignTaskDto.comment) {
      task.review_notes = assignTaskDto.comment;
    }

    return this.tasksRepository.save(task);
  }

  async reassign(id: string, assignTaskDto: AssignTaskDto, assignerId: string): Promise<Task> {
    const task = await this.findOne(id);

    task.assigned_to = assignTaskDto.assignedTo;
    task.assigned_by = assignerId;
    task.assigned_at = new Date();

    if (assignTaskDto.comment) {
      task.review_notes = assignTaskDto.comment;
    }

    return this.tasksRepository.save(task);
  }

  async remove(id: string): Promise<void> {
    await this.findOne(id); // Verify task exists
    await this.tasksRepository.softDelete(id);
  }

  async getTaskStats(): Promise<{
    total: number;
    unassigned: number;
    assigned: number;
    completed: number;
    overdue: number;
  }> {
    const [total, unassigned, assigned, completed, overdue] = await Promise.all([
      this.tasksRepository.count(),
      this.tasksRepository.count({ where: { assigned_to: IsNull() } }),
      this.tasksRepository.count({ where: { assigned_to: Not(IsNull()) } }),
      this.tasksRepository.count({ where: { status: TaskStatus.COMPLETED } }),
      this.tasksRepository.count({
        where: {
          due_date: Not(IsNull()),
          status: Not(TaskStatus.COMPLETED),
        },
      }),
    ]);

    return {
      total,
      unassigned,
      assigned,
      completed,
      overdue,
    };
  }
}