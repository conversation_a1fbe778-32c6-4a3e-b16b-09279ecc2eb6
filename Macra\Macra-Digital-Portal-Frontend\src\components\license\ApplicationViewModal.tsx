'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { applicationService } from '@/services/applicationService';
import { applicantService } from '@/services/applicantService';
import { userService } from '@/services/userService';
import { useToast } from '@/contexts/ToastContext';
import { Application, Applicant } from '@/types/license';
import Loader from '@/components/Loader';
import { useApplicationStatusTracker } from '@/hooks/useApplicationNotifications';

interface ApplicationViewModalProps {
  isOpen: boolean;
  onClose: () => void;
  applicationId: string | null;
  departmentType?: string;
  licenseTypeCode?: string;
  onUpdate?: () => void;
}

const ApplicationViewModal: React.FC<ApplicationViewModalProps> = ({
  isOpen,
  onClose,
  applicationId,
  departmentType,
  licenseTypeCode,
  onUpdate
}) => {
  const { showSuccess, showError } = useToast();
  const router = useRouter();
  const { trackStatusChange } = useApplicationStatusTracker();
  
  const [application, setApplication] = useState<Application | null>(null);
  const [applicantDetails, setApplicantDetails] = useState<Applicant | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen && applicationId) {
      fetchApplicationDetails();
    }
  }, [isOpen, applicationId]);

  const fetchApplicationDetails = async () => {
    if (!applicationId) return;

    setLoading(true);
    setError(null);

    try {
      // Fetch application details
      const applicationResponse = await applicationService.getApplication(applicationId);
      setApplication(applicationResponse);

      // Fetch applicant details separately if applicant_id exists
      if (applicationResponse.applicant_id) {
        try {
          const applicantResponse = await applicantService.getApplicant(applicationResponse.applicant_id);
          setApplicantDetails(applicantResponse);
          console.log('✅ Applicant details fetched from database:', applicantResponse);
        } catch (applicantError) {
          console.warn('⚠️ Could not fetch applicant details from database:', applicantError);
          // Try to get user details if applicant fetch fails
          try {
            const userResponse = await userService.getUser(applicationResponse.applicant_id);
            // Convert user data to applicant-like structure
            setApplicantDetails({
              applicant_id: userResponse.user_id,
              name: `${userResponse.first_name} ${userResponse.last_name}`,
              email: userResponse.email,
              phone: userResponse.phone,
              business_registration_number: '',
              tpin: '',
              website: '',
              fax: '',
              date_incorporation: '',
              place_incorporation: '',
              created_at: userResponse.created_at,
              updated_at: userResponse.updated_at
            } as Applicant);
            console.log('✅ User details fetched as fallback:', userResponse);
          } catch (userError) {
            console.warn('⚠️ Could not fetch user details either:', userError);
          }
        }
      }
    } catch (err: unknown) {
      console.error('Error fetching application details:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(`Failed to load application details: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const handleEvaluate = () => {
    if (!application) return;

    // Get the license type code from the application data or use departmentType/licenseTypeCode
    const resolvedLicenseTypeCode = application?.license_category?.license_type?.code ||
                                   licenseTypeCode ||
                                   departmentType ||
                                   'telecommunications'; // fallback

    // Create URL with both application_id and license_category_id
    const params = new URLSearchParams();
    params.set('application_id', applicationId!);

    // Get license category ID from application
    const licenseCategoryId = application?.license_category_id;

    if (licenseCategoryId) {
      params.set('license_category_id', licenseCategoryId);
    }

    // Close modal and navigate to evaluation page
    onClose();
    router.push(`/applications/${resolvedLicenseTypeCode}/evaluate?${params.toString()}`);
  };





  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div className="mt-3">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              Application Details
            </h3>
            <button
              type="button"
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              aria-label="Close modal"
            >
              <i className="ri-close-line text-xl"></i>
            </button>
          </div>

          {/* Content */}
          {loading ? (
            <div className="py-8">
              <Loader message="Loading application details..." />
            </div>
          ) : error ? (
            <div className="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4">
              <div className="flex">
                <i className="ri-error-warning-line text-red-400 mr-2"></i>
                <p className="text-red-700 dark:text-red-200">{error}</p>
              </div>
            </div>
          ) : application ? (
            <div className="space-y-6">
              {/* Applicant Information */}
              {(applicantDetails || application.applicant) && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-4">
                    Applicant Information
                  </h4>
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                    {applicantDetails ? (
                      // Use database-fetched applicant details (preferred)
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {/* Left Column - Basic Information */}
                        <div className="space-y-4">
                          <h5 className="text-sm font-semibold text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-600 pb-2">
                            Basic Information
                          </h5>

                          <div>
                            <label className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                              Company/Organization
                            </label>
                            <p className="text-sm text-gray-900 dark:text-gray-100 mt-1">
                              {applicantDetails.name}
                            </p>
                          </div>

                          <div>
                            <label className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                              Email Address
                            </label>
                            <p className="text-sm text-gray-900 dark:text-gray-100 mt-1">
                              {applicantDetails.email || 'Not provided'}
                            </p>
                          </div>

                          <div>
                            <label className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                              Phone Number
                            </label>
                            <p className="text-sm text-gray-900 dark:text-gray-100 mt-1">
                              {applicantDetails.phone || 'Not provided'}
                            </p>
                          </div>

                          {applicantDetails.fax && (
                            <div>
                              <label className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                                Fax Number
                              </label>
                              <p className="text-sm text-gray-900 dark:text-gray-100 mt-1">
                                {applicantDetails.fax}
                              </p>
                            </div>
                          )}

                          {applicantDetails.website && (
                            <div>
                              <label className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                                Website
                              </label>
                              <p className="text-sm text-gray-900 dark:text-gray-100 mt-1">
                                <a
                                  href={applicantDetails.website.startsWith('http') ? applicantDetails.website : `https://${applicantDetails.website}`}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-blue-600 dark:text-blue-400 hover:underline"
                                >
                                  {applicantDetails.website}
                                </a>
                              </p>
                            </div>
                          )}
                        </div>

                        {/* Right Column - Business Information */}
                        <div className="space-y-4">
                          <h5 className="text-sm font-semibold text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-600 pb-2">
                            Business Information
                          </h5>

                          <div>
                            <label className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                              Business Registration Number
                            </label>
                            <p className="text-sm text-gray-900 dark:text-gray-100 mt-1">
                              {applicantDetails.business_registration_number || 'Not provided'}
                            </p>
                          </div>

                          <div>
                            <label className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                              TPIN
                            </label>
                            <p className="text-sm text-gray-900 dark:text-gray-100 mt-1">
                              {applicantDetails.tpin || 'Not provided'}
                            </p>
                          </div>

                          <div>
                            <label className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                              Date of Incorporation
                            </label>
                            <p className="text-sm text-gray-900 dark:text-gray-100 mt-1">
                              {applicantDetails.date_incorporation
                                ? new Date(applicantDetails.date_incorporation).toLocaleDateString('en-US', {
                                    year: 'numeric',
                                    month: 'long',
                                    day: 'numeric'
                                  })
                                : 'Not provided'
                              }
                            </p>
                          </div>

                          <div>
                            <label className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                              Place of Incorporation
                            </label>
                            <p className="text-sm text-gray-900 dark:text-gray-100 mt-1">
                              {applicantDetails.place_incorporation || 'Not provided'}
                            </p>
                          </div>

                          {applicantDetails.level_of_insurance_cover && (
                            <div>
                              <label className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                                Insurance Cover Level
                              </label>
                              <p className="text-sm text-gray-900 dark:text-gray-100 mt-1">
                                {applicantDetails.level_of_insurance_cover}
                              </p>
                            </div>
                          )}
                        </div>
                      </div>
                    ) : application.applicant ? (
                      // Fallback to application.applicant data
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-4">
                          <h5 className="text-sm font-semibold text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-600 pb-2">
                            Basic Information
                          </h5>

                          <div>
                            <label className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                              Company/Organization
                            </label>
                            <p className="text-sm text-gray-900 dark:text-gray-100 mt-1">
                              {application.applicant.name}
                            </p>
                          </div>

                          <div>
                            <label className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                              Email Address
                            </label>
                            <p className="text-sm text-gray-900 dark:text-gray-100 mt-1">
                              {application.applicant.email || 'Not provided'}
                            </p>
                          </div>

                          <div>
                            <label className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                              Phone Number
                            </label>
                            <p className="text-sm text-gray-900 dark:text-gray-100 mt-1">
                              {application.applicant.phone || 'Not provided'}
                            </p>
                          </div>
                        </div>

                        <div className="space-y-4">
                          <h5 className="text-sm font-semibold text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-600 pb-2">
                            Business Information
                          </h5>

                          <div>
                            <label className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                              Business Registration Number
                            </label>
                            <p className="text-sm text-gray-900 dark:text-gray-100 mt-1">
                              {application.applicant.business_registration_number || 'Not provided'}
                            </p>
                          </div>

                          <div>
                            <label className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                              TPIN
                            </label>
                            <p className="text-sm text-gray-900 dark:text-gray-100 mt-1">
                              {application.applicant.tpin || 'Not provided'}
                            </p>
                          </div>
                        </div>
                      </div>
                    ) : (
                      // No applicant data available
                      <div className="text-center py-8">
                        <div className="text-gray-400 dark:text-gray-500">
                          <i className="ri-user-line text-4xl mb-2"></i>
                          <p className="text-sm">No applicant information available</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}


            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500 dark:text-gray-400">No application data available</p>
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              Close
            </button>
            {application && (
              <button
                type="button"
                onClick={handleEvaluate}
                className="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              >
                <i className="ri-clipboard-line mr-2"></i>
                Evaluate
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApplicationViewModal;
