'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { applicationService } from '@/services/applicationService';
import { useToast } from '@/contexts/ToastContext';
import { Application, ApplicationStatus } from '@/types/license';
import Loader from '@/components/Loader';

interface ApplicationViewModalProps {
  isOpen: boolean;
  onClose: () => void;
  applicationId: string | null;
  departmentType?: string;
  licenseTypeCode?: string;
  onUpdate?: () => void;
}

const ApplicationViewModal: React.FC<ApplicationViewModalProps> = ({
  isOpen,
  onClose,
  applicationId,
  departmentType,
  licenseTypeCode,
  onUpdate
}) => {
  const { showSuccess, showError } = useToast();
  const router = useRouter();
  const [application, setApplication] = useState<Application | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen && applicationId) {
      fetchApplicationDetails();
    }
  }, [isOpen, applicationId]);

  const fetchApplicationDetails = async () => {
    if (!applicationId) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await applicationService.getApplicationById(applicationId);
      setApplication(response);
    } catch (err: unknown) {
      console.error('Error fetching application details:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(`Failed to load application details: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const handleEvaluate = () => {
    if (!application) return;

    // Get the license type code from the application data or use departmentType/licenseTypeCode
    const resolvedLicenseTypeCode = application?.license_category?.license_type?.code ||
                                   licenseTypeCode ||
                                   departmentType ||
                                   'telecommunications'; // fallback

    // Create URL with both application_id and license_category_id
    const params = new URLSearchParams();
    params.set('application_id', applicationId!);

    // Get license category ID from application
    const licenseCategoryId = application?.license_category_id;

    if (licenseCategoryId) {
      params.set('license_category_id', licenseCategoryId);
    }

    // Close modal and navigate to evaluation page
    onClose();
    router.push(`/applications/${resolvedLicenseTypeCode}/evaluate?${params.toString()}`);
  };

  const getStatusColor = (status: ApplicationStatus) => {
    switch (status) {
      case ApplicationStatus.SUBMITTED: return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case ApplicationStatus.UNDER_REVIEW: return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case ApplicationStatus.EVALUATION: return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case ApplicationStatus.APPROVED: return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case ApplicationStatus.REJECTED: return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case ApplicationStatus.PENDING_PAYMENT: return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case ApplicationStatus.ISSUED: return 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div className="mt-3">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              Application Details
            </h3>
            <button
              type="button"
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <i className="ri-close-line text-xl"></i>
            </button>
          </div>

          {/* Content */}
          {loading ? (
            <div className="py-8">
              <Loader message="Loading application details..." />
            </div>
          ) : error ? (
            <div className="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4">
              <div className="flex">
                <i className="ri-error-warning-line text-red-400 mr-2"></i>
                <p className="text-red-700 dark:text-red-200">{error}</p>
              </div>
            </div>
          ) : application ? (
            <div className="space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    Application Number
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {application.application_number}
                  </p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    Status
                  </h4>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(application.status)}`}>
                    {application.status.replace('_', ' ').toUpperCase()}
                  </span>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    License Category
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {application.license_category?.name || 'N/A'}
                  </p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    License Type
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {application.license_category?.license_type?.name || 'N/A'}
                  </p>
                </div>
              </div>

              {/* Applicant Information */}
              {application.applicant && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    Applicant
                  </h4>
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      <strong>Name:</strong> {application.applicant.first_name} {application.applicant.last_name}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      <strong>Email:</strong> {application.applicant.email}
                    </p>
                  </div>
                </div>
              )}

              {/* Application Data */}
              {application.application_data && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    Application Details
                  </h4>
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <pre className="text-sm text-gray-600 dark:text-gray-400 whitespace-pre-wrap">
                      {JSON.stringify(application.application_data, null, 2)}
                    </pre>
                  </div>
                </div>
              )}

              {/* Timestamps */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 pt-4 border-t border-gray-200 dark:border-gray-600">
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    Submitted
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {formatDate(application.created_at)}
                  </p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    Last Updated
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {formatDate(application.updated_at)}
                  </p>
                </div>
                {application.submitted_at && (
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                      Submitted At
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {formatDate(application.submitted_at)}
                    </p>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500 dark:text-gray-400">No application data available</p>
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              Close
            </button>
            {application && (
              <button
                type="button"
                onClick={handleEvaluate}
                className="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              >
                <i className="ri-clipboard-line mr-2"></i>
                Evaluate
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApplicationViewModal;
