'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { applicationService } from '@/services/applicationService';
import { applicantService } from '@/services/applicantService';
import { userService } from '@/services/userService';
import { useToast } from '@/contexts/ToastContext';
import { Application, ApplicationStatus, Applicant } from '@/types/license';
import Loader from '@/components/Loader';

interface ApplicationViewModalProps {
  isOpen: boolean;
  onClose: () => void;
  applicationId: string | null;
  departmentType?: string;
  licenseTypeCode?: string;
  onUpdate?: () => void;
}

const ApplicationViewModal: React.FC<ApplicationViewModalProps> = ({
  isOpen,
  onClose,
  applicationId,
  departmentType,
  licenseTypeCode,
  onUpdate
}) => {
  const { showSuccess, showError } = useToast();
  const router = useRouter();
  const [application, setApplication] = useState<Application | null>(null);
  const [applicantDetails, setApplicantDetails] = useState<Applicant | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen && applicationId) {
      fetchApplicationDetails();
    }
  }, [isOpen, applicationId]);

  const fetchApplicationDetails = async () => {
    if (!applicationId) return;

    setLoading(true);
    setError(null);

    try {
      // Fetch application details
      const applicationResponse = await applicationService.getApplication(applicationId);
      setApplication(applicationResponse);

      // Fetch applicant details separately if applicant_id exists
      if (applicationResponse.applicant_id) {
        try {
          const applicantResponse = await applicantService.getApplicant(applicationResponse.applicant_id);
          setApplicantDetails(applicantResponse);
          console.log('✅ Applicant details fetched from database:', applicantResponse);
        } catch (applicantError) {
          console.warn('⚠️ Could not fetch applicant details from database:', applicantError);
          // Try to get user details if applicant fetch fails
          try {
            const userResponse = await userService.getUser(applicationResponse.applicant_id);
            // Convert user data to applicant-like structure
            setApplicantDetails({
              applicant_id: userResponse.user_id,
              name: `${userResponse.first_name} ${userResponse.last_name}`,
              email: userResponse.email,
              phone: userResponse.phone,
              business_registration_number: '',
              tpin: '',
              website: '',
              fax: '',
              date_incorporation: '',
              place_incorporation: '',
              created_at: userResponse.created_at,
              updated_at: userResponse.updated_at
            } as Applicant);
            console.log('✅ User details fetched as fallback:', userResponse);
          } catch (userError) {
            console.warn('⚠️ Could not fetch user details either:', userError);
          }
        }
      }
    } catch (err: unknown) {
      console.error('Error fetching application details:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(`Failed to load application details: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const handleEvaluate = () => {
    if (!application) return;

    // Get the license type code from the application data or use departmentType/licenseTypeCode
    const resolvedLicenseTypeCode = application?.license_category?.license_type?.code ||
                                   licenseTypeCode ||
                                   departmentType ||
                                   'telecommunications'; // fallback

    // Create URL with both application_id and license_category_id
    const params = new URLSearchParams();
    params.set('application_id', applicationId!);

    // Get license category ID from application
    const licenseCategoryId = application?.license_category_id;

    if (licenseCategoryId) {
      params.set('license_category_id', licenseCategoryId);
    }

    // Close modal and navigate to evaluation page
    onClose();
    router.push(`/applications/${resolvedLicenseTypeCode}/evaluate?${params.toString()}`);
  };

  const getStatusColor = (status: ApplicationStatus) => {
    switch (status) {
      case ApplicationStatus.SUBMITTED: return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case ApplicationStatus.UNDER_REVIEW: return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case ApplicationStatus.EVALUATION: return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case ApplicationStatus.APPROVED: return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case ApplicationStatus.REJECTED: return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case ApplicationStatus.PENDING_PAYMENT: return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case ApplicationStatus.ISSUED: return 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div className="mt-3">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              Application Details
            </h3>
            <button
              type="button"
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              aria-label="Close modal"
            >
              <i className="ri-close-line text-xl"></i>
            </button>
          </div>

          {/* Content */}
          {loading ? (
            <div className="py-8">
              <Loader message="Loading application details..." />
            </div>
          ) : error ? (
            <div className="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4">
              <div className="flex">
                <i className="ri-error-warning-line text-red-400 mr-2"></i>
                <p className="text-red-700 dark:text-red-200">{error}</p>
              </div>
            </div>
          ) : application ? (
            <div className="space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    Application Number
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {application.application_number}
                  </p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    Status
                  </h4>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(application.status)}`}>
                    {application.status.replace('_', ' ').toUpperCase()}
                  </span>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    License Category
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {application.license_category?.name || 'N/A'}
                  </p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    License Type
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {application.license_category?.license_type?.name || 'N/A'}
                  </p>
                </div>
              </div>

              {/* Applicant Information */}
              {(applicantDetails || application.applicant || application.application_data?.step_2_applicant_details) && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    Applicant Information
                  </h4>
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    {applicantDetails ? (
                      // Use database-fetched applicant details (preferred)
                      <>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          <strong>Company/Organization:</strong> {applicantDetails.name}
                        </p>

                        {applicantDetails.email && (
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            <strong>Email:</strong> {applicantDetails.email}
                          </p>
                        )}

                        {applicantDetails.phone && (
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            <strong>Phone:</strong> {applicantDetails.phone}
                          </p>
                        )}

                        {applicantDetails.business_registration_number && (
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            <strong>Registration Number:</strong> {applicantDetails.business_registration_number}
                          </p>
                        )}

                        {applicantDetails.tpin && (
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            <strong>TPIN:</strong> {applicantDetails.tpin}
                          </p>
                        )}

                        {applicantDetails.website && (
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            <strong>Website:</strong> {applicantDetails.website}
                          </p>
                        )}

                        {applicantDetails.fax && (
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            <strong>Fax:</strong> {applicantDetails.fax}
                          </p>
                        )}

                        {applicantDetails.date_incorporation && (
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            <strong>Date of Incorporation:</strong> {new Date(applicantDetails.date_incorporation).toLocaleDateString()}
                          </p>
                        )}

                        {applicantDetails.place_incorporation && (
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            <strong>Place of Incorporation:</strong> {applicantDetails.place_incorporation}
                          </p>
                        )}
                      </>
                    ) : (
                      // Fallback to application data (legacy)
                      (() => {
                        const applicantData = application.applicant || {};
                        const applicationDetails = application.application_data?.step_2_applicant_details || {};

                        const firstName = applicantData.first_name || applicationDetails.contact_name?.split(' ')[0] || '';
                        const lastName = applicantData.last_name || applicationDetails.contact_name?.split(' ').slice(1).join(' ') || '';
                        const fullName = applicantData.name || applicationDetails.contact_name || `${firstName} ${lastName}`.trim();

                        const companyName = applicantData.company_name ||
                                           applicantData.organization_name ||
                                           applicationDetails.company_name ||
                                           applicationDetails.organization_name ||
                                           applicantData.name;

                        const email = applicantData.email || applicationDetails.contact_email || applicationDetails.company_email;
                        const phone = applicantData.phone || applicationDetails.contact_phone || applicationDetails.company_phone;

                        return (
                          <>
                            <div className="mb-2 p-2 bg-yellow-50 dark:bg-yellow-900 rounded text-xs">
                              <strong>Note:</strong> Using fallback data from application. Database applicant details not available.
                            </div>

                            {fullName && (
                              <p className="text-sm text-gray-600 dark:text-gray-400">
                                <strong>Contact Person:</strong> {fullName}
                              </p>
                            )}

                            {companyName && companyName !== fullName && (
                              <p className="text-sm text-gray-600 dark:text-gray-400">
                                <strong>Company/Organization:</strong> {companyName}
                              </p>
                            )}

                            {email && (
                              <p className="text-sm text-gray-600 dark:text-gray-400">
                                <strong>Email:</strong> {email}
                              </p>
                            )}

                            {phone && (
                              <p className="text-sm text-gray-600 dark:text-gray-400">
                                <strong>Phone:</strong> {phone}
                              </p>
                            )}
                          </>
                        );
                      })()
                    )}
                  </div>
                </div>
              )}

              {/* Application Data */}
              {application.application_data && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    Application Data
                  </h4>
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <pre className="text-sm text-gray-600 dark:text-gray-400 whitespace-pre-wrap">
                      {JSON.stringify(application.application_data, null, 2)}
                    </pre>
                  </div>
                </div>
              )}

              {/* Timestamps */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 pt-4 border-t border-gray-200 dark:border-gray-600">
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    Submitted
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {formatDate(application.created_at)}
                  </p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    Last Updated
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {formatDate(application.updated_at)}
                  </p>
                </div>
                {application.submitted_at && (
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                      Submitted At
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {formatDate(application.submitted_at)}
                    </p>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500 dark:text-gray-400">No application data available</p>
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              Close
            </button>
            {application && (
              <button
                type="button"
                onClick={handleEvaluate}
                className="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              >
                <i className="ri-clipboard-line mr-2"></i>
                Evaluate
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApplicationViewModal;
