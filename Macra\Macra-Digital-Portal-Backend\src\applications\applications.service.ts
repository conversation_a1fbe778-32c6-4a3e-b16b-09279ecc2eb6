import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, IsNull } from 'typeorm';
import { Applications } from '../entities/applications.entity';
import { CreateApplicationDto } from '../dto/application/create-application.dto';
import { UpdateApplicationDto } from '../dto/application/update-application.dto';
import { PaginateQuery, Paginated, PaginateConfig, paginate } from 'nestjs-paginate';
import { TasksService } from '../tasks/tasks.service';
import { CreateTaskDto } from '../dto/tasks/create-task.dto';
import { TaskType, TaskPriority, TaskStatus } from '../entities/tasks.entity';
import { NotificationHelperService } from '../notifications/notification-helper.service';

@Injectable()
export class ApplicationsService {
  constructor(
    @InjectRepository(Applications)
    private applicationsRepository: Repository<Applications>,
    private tasksService: TasksService,
    private notificationHelper: NotificationHelperService,
  ) {}

  private readonly paginateConfig: PaginateConfig<Applications> = {
    sortableColumns: ['created_at', 'updated_at', 'application_number', 'status'],
    searchableColumns: ['application_number', 'status'],
    defaultSortBy: [['created_at', 'DESC']],
    defaultLimit: 10,
    maxLimit: 100,
    relations: ['applicant', 'license_category', 'license_category.license_type', 'creator', 'updater', 'assignee'],
    filterableColumns: {
      status: true,
      created_by: true,
      license_category_id: true,
      applicant_id: true,
      assigned_to: true,
      'license_category.license_type_id': true,
    },
  };

  async create(createApplicationDto: CreateApplicationDto, createdBy: string): Promise<Applications> {
    // Check if application number already exists
    const existingApplication = await this.applicationsRepository.findOne({
      where: { application_number: createApplicationDto.application_number },
    });

    if (existingApplication) {
      throw new ConflictException('Application number already exists');
    }

    const application = this.applicationsRepository.create({
      ...createApplicationDto,
      current_step: createApplicationDto.current_step || 1,
      progress_percentage: createApplicationDto.progress_percentage || 0,
      status: createApplicationDto.status || 'draft',
      created_by: createdBy,
    });

    return this.applicationsRepository.save(application);
  }

  async findAll(query: PaginateQuery, userRoles?: string[], userId?: string): Promise<Paginated<Applications>> {
    // Check if user is a customer (has customer role)
    const isCustomer = userRoles?.includes('customer');

    // If user is a customer, filter by their created applications only
    if (isCustomer && userId) {
      const customerQuery: PaginateQuery = {
        ...query,
        filter: {
          ...query.filter,
          created_by: userId
        }
      };
      return paginate(customerQuery, this.applicationsRepository, this.paginateConfig);
    }

    // For department users, exclude draft applications unless explicitly requested
    const includeDraftValue = query.filter?.['include_draft'];
    const shouldIncludeDrafts = includeDraftValue === 'true' ||
                               (typeof includeDraftValue === 'boolean' && includeDraftValue === true);

    if (shouldIncludeDrafts) {
      // Include all applications including drafts
      return paginate(query, this.applicationsRepository, this.paginateConfig);
    } else {
      // Exclude draft applications by using a custom query builder
      const queryBuilder = this.applicationsRepository
        .createQueryBuilder('application')
        .leftJoinAndSelect('application.applicant', 'applicant')
        .leftJoinAndSelect('application.license_category', 'license_category')
        .leftJoinAndSelect('license_category.license_type', 'license_type')
        .leftJoinAndSelect('application.creator', 'creator')
        .leftJoinAndSelect('application.updater', 'updater')
        .leftJoinAndSelect('application.assignee', 'assignee')
        .where('application.status != :draftStatus', { draftStatus: 'draft' });

      // Apply additional filters from the query
      if (query.filter) {
        Object.entries(query.filter).forEach(([key, value]) => {
          if (key !== 'include_draft' && value !== undefined && value !== '') {
            if (key.includes('.')) {
              // Handle nested filters like 'license_category.license_type_id'
              queryBuilder.andWhere(`${key} = :${key.replace('.', '_')}`, { [key.replace('.', '_')]: value });
            } else {
              queryBuilder.andWhere(`application.${key} = :${key}`, { [key]: value });
            }
          }
        });
      }

      // Apply search if provided
      if (query.search) {
        queryBuilder.andWhere(
          '(application.application_number LIKE :search OR application.status LIKE :search)',
          { search: `%${query.search}%` }
        );
      }

      return paginate(query, queryBuilder, this.paginateConfig);
    }
  }

  async findUserApplications(query: PaginateQuery): Promise<Paginated<Applications>> {
    // This method specifically filters applications by the authenticated user
    // It's used for customer-facing endpoints like /user-applications
    return paginate(query, this.applicationsRepository, this.paginateConfig);
  }

  async findOne(id: string): Promise<Applications> {
    const application = await this.applicationsRepository.findOne({
      where: { application_id: id },
      relations: ['applicant', 'license_category', 'license_category.license_type', 'creator', 'updater'],
    });

    if (!application) {
      throw new NotFoundException(`Application with ID ${id} not found`);
    }

    return application;
  }

  async findByApplicant(applicantId: string): Promise<Applications[]> {
    return this.applicationsRepository.find({
      where: { applicant_id: applicantId },
      relations: ['applicant', 'license_category', 'license_category.license_type', 'creator', 'updater'],
      order: { created_at: 'DESC' },
    });
  }

  async findByStatus(status: string): Promise<Applications[]> {
    return this.applicationsRepository.find({
      where: { status },
      relations: ['applicant', 'license_category', 'license_category.license_type', 'creator', 'updater'],
      order: { created_at: 'DESC' },
    });
  }

  async update(id: string, updateApplicationDto: UpdateApplicationDto, updatedBy: string): Promise<Applications> {
    const application = await this.findOne(id);
    const previousStatus = application.status;

    // If updating application number, check for conflicts
    if (updateApplicationDto.application_number && updateApplicationDto.application_number !== application.application_number) {
      const existingApplication = await this.applicationsRepository.findOne({
        where: { application_number: updateApplicationDto.application_number },
      });

      if (existingApplication) {
        throw new ConflictException('Application number already exists');
      }
    }

    Object.assign(application, updateApplicationDto, { updated_by: updatedBy });

    // Set submitted_at if status is being changed to submitted for the first time
    if (updateApplicationDto.status === 'submitted' && !application.submitted_at) {
      application.submitted_at = new Date();
    }

    const savedApplication = await this.applicationsRepository.save(application);

    // Create a task when application status changes to submitted for the first time
    if (updateApplicationDto.status === 'submitted' && previousStatus !== 'submitted') {
      console.log(`🔄 Application status changed to submitted: ${application.application_number} (previous: ${previousStatus})`);

      try {
        const applicantName = application.applicant
          ? application.applicant.name
          : 'Unknown Applicant';

        const licenseTypeName = application.license_category?.license_type?.name || 'Unknown License Type';

        const taskData: CreateTaskDto = {
          title: `Review Application - ${application.application_number}`,
          description: `Application submitted by ${applicantName} for ${licenseTypeName} license. Please review and process this application.`,
          task_type: TaskType.APPLICATION,
          priority: TaskPriority.MEDIUM,
          status: TaskStatus.PENDING,
          entity_type: 'application',
          entity_id: application.application_id,
        };

        console.log(`📝 Creating task for application: ${application.application_number}`, taskData);
        const createdTask = await this.tasksService.create(taskData, updatedBy);
        console.log(`✅ Task created successfully for application: ${application.application_number}, Task ID: ${createdTask.task_id}`);

        // Send notification to applicant about submission
        if (application.applicant) {
          try {
            console.log(`📧 Sending notification for application: ${application.application_number}`);
            await this.notificationHelper.notifyApplicationStatus(
              application.application_id,
              application.applicant_id,
              application.applicant.email,
              application.applicant.phone,
              application.application_number,
              'submitted',
              updatedBy
            );
            console.log(`✅ Notification sent for submitted application: ${application.application_number}`);
          } catch (notificationError) {
            console.error('❌ Error sending notification for submitted application:', notificationError);
            // Don't fail if notification fails
          }
        }
      } catch (error) {
        console.error('❌ Error creating task for submitted application:', error);
        console.error('Error details:', error.message, error.stack);
        // Don't fail the status update if task creation fails
      }
    } else {
      console.log(`ℹ️ Application update - no task creation needed. Status: ${updateApplicationDto.status}, Previous: ${previousStatus}`);
    }

    return savedApplication;
  }

  async remove(id: string): Promise<void> {
    const application = await this.findOne(id);
    await this.applicationsRepository.softDelete(application.application_id);
  }

  async updateStatus(id: string, status: string, updatedBy: string): Promise<Applications> {
    const application = await this.findOne(id);
    const previousStatus = application.status;

    application.status = status;
    application.updated_by = updatedBy;

    if (status === 'submitted' && !application.submitted_at) {
      application.submitted_at = new Date();
    }

    const savedApplication = await this.applicationsRepository.save(application);

    // Create a task when application status changes to submitted for the first time
    if (status === 'submitted' && previousStatus !== 'submitted') {
      try {
        const applicantName = application.applicant
          ? `${application.applicant.name}`
          : 'Unknown Applicant';

        const licenseTypeName = application.license_category?.license_type?.name || 'Unknown License Type';

        const taskData: CreateTaskDto = {
          title: `Review Application - ${application.application_number}`,
          description: `Application submitted by ${applicantName} for ${licenseTypeName} license. Please review and process this application.`,
          task_type: TaskType.APPLICATION,
          priority: TaskPriority.MEDIUM,
          status: TaskStatus.PENDING,
          entity_type: 'application',
          entity_id: application.application_id,
        };

        await this.tasksService.create(taskData, updatedBy);
        console.log(`Task created for submitted application: ${application.application_number}`);

        // Send notification to applicant about submission
        if (application.applicant) {
          try {
            await this.notificationHelper.notifyApplicationStatus(
              application.application_id,
              application.applicant_id,
              application.applicant.email,
              application.applicant.phone,
              application.application_number,
              'submitted',
              updatedBy
            );
            console.log(`Notification sent for submitted application: ${application.application_number}`);
          } catch (notificationError) {
            console.error('Error sending notification for submitted application:', notificationError);
            // Don't fail if notification fails
          }
        }
      } catch (error) {
        console.error('Error creating task for submitted application:', error);
        // Don't fail the status update if task creation fails
      }
    }

    return savedApplication;
  }

  async updateProgress(id: string, currentStep: number, progressPercentage: number, updatedBy: string): Promise<Applications> {
    const application = await this.findOne(id);
    application.current_step = currentStep;
    application.progress_percentage = progressPercentage;
    application.updated_by = updatedBy;

    return this.applicationsRepository.save(application);
  }

  async getApplicationStats(): Promise<any> {
    const stats = await this.applicationsRepository
      .createQueryBuilder('application')
      .select('application.status', 'status')
      .addSelect('COUNT(*)', 'count')
      .groupBy('application.status')
      .getRawMany();

    return stats.reduce((acc, stat) => {
      acc[stat.status] = parseInt(stat.count);
      return acc;
    }, {});
  }

  async assignApplication(applicationId: string, assignedTo: string, assignedBy: string): Promise<Applications> {
    const application = await this.findOne(applicationId);
    application.assigned_to = assignedTo;
    application.assigned_at = new Date();
    application.updated_by = assignedBy;

    return this.applicationsRepository.save(application);
  }

  async getUnassignedApplications(query: PaginateQuery): Promise<Paginated<Applications>> {
    const config: PaginateConfig<Applications> = {
      ...this.paginateConfig,
      where: { assigned_to: IsNull() },
    };

    return paginate(query, this.applicationsRepository, config);
  }

  async getAssignedApplications(userId: string, query: PaginateQuery): Promise<Paginated<Applications>> {
    const config: PaginateConfig<Applications> = {
      ...this.paginateConfig,
      where: { assigned_to: userId },
    };

    return paginate(query, this.applicationsRepository, config);
  }

  // Debug method to get all applications without filtering
  async findAllDebug(query: PaginateQuery): Promise<Paginated<Applications>> {
    console.log('🐛 Debug: Getting all applications without role filtering');
    const count = await this.applicationsRepository.count();
    console.log('🐛 Total applications in database:', count);
    
    // Get status distribution
    const statusStats = await this.applicationsRepository
      .createQueryBuilder('app')
      .select('app.status', 'status')
      .addSelect('COUNT(*)', 'count')
      .groupBy('app.status')
      .getRawMany();
    
    console.log('🐛 Status distribution:', statusStats);
    
    const debugConfig: PaginateConfig<Applications> = {
      ...this.paginateConfig,
      // Remove any status filtering for debug
    };
    
    return paginate(query, this.applicationsRepository, debugConfig);
  }
}
