import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Applications } from '../entities/applications.entity';
import { CreateApplicationDto } from '../dto/application/create-application.dto';
import { UpdateApplicationDto } from '../dto/application/update-application.dto';
import { PaginateQuery, Paginated, PaginateConfig, paginate } from 'nestjs-paginate';
import { RoleName } from '../entities/role.entity';

@Injectable()
export class ApplicationsService {
  constructor(
    @InjectRepository(Applications)
    private applicationsRepository: Repository<Applications>,
  ) {}

  private readonly paginateConfig: PaginateConfig<Applications> = {
    sortableColumns: ['created_at', 'updated_at', 'application_number', 'status'],
    searchableColumns: ['application_number', 'status'],
    defaultSortBy: [['created_at', 'DESC']],
    defaultLimit: 10,
    maxLimit: 100,
    relations: ['applicant', 'license_category', 'license_category.license_type', 'creator', 'updater'],
    filterableColumns: {
      status: true,
      created_by: true,
      license_category_id: true,
      applicant_id: true,
    },
  };

  async create(createApplicationDto: CreateApplicationDto, createdBy: string): Promise<Applications> {
    // Check if application number already exists
    const existingApplication = await this.applicationsRepository.findOne({
      where: { application_number: createApplicationDto.application_number },
    });

    if (existingApplication) {
      throw new ConflictException('Application number already exists');
    }

    const application = this.applicationsRepository.create({
      ...createApplicationDto,
      current_step: createApplicationDto.current_step || 1,
      progress_percentage: createApplicationDto.progress_percentage || 0,
      status: createApplicationDto.status || 'draft',
      created_by: createdBy,
    });

    return this.applicationsRepository.save(application);
  }

  async findAll(query: PaginateQuery, userRoles?: string[], userId?: string): Promise<Paginated<Applications>> {
    // Check if user is a customer (has customer role)
    const isCustomer = userRoles?.includes('customer');

    // If user is a customer, filter by their created applications only
    if (isCustomer && userId) {
      const customerQuery: PaginateQuery = {
        ...query,
        filter: {
          ...query.filter,
          created_by: userId
        }
      };
      return paginate(customerQuery, this.applicationsRepository, this.paginateConfig);
    }

    // If user has department-specific roles, filter applications by license type
    const departmentRoles = userRoles?.filter(role =>
      [RoleName.POSTAL, RoleName.TELECOMMUNICATIONS, RoleName.STANDARDS, RoleName.CLF].includes(role as RoleName)
    );

    // If user has admin or evaluator role, show all applications
    const hasAdminAccess = userRoles?.some(role =>
      [RoleName.ADMINISTRATOR, RoleName.EVALUATOR].includes(role as RoleName)
    );

    // For staff portal users, only show submitted applications (not draft applications)
    const submittedStatusFilter = {
      status: ['submitted', 'under_review', 'evaluation', 'approved', 'rejected', 'withdrawn']
    };

    if (hasAdminAccess || !departmentRoles?.length) {
      // Admin/evaluator or no department roles - show all submitted applications
      const modifiedQuery: PaginateQuery = {
        ...query,
        filter: {
          ...query.filter,
          ...submittedStatusFilter
        }
      };
      return paginate(modifiedQuery, this.applicationsRepository, this.paginateConfig);
    }

    // Filter by department-specific license types
    const licenseTypeFilters = departmentRoles.map(role => {
      switch (role) {
        case RoleName.POSTAL:
          return 'postal';
        case RoleName.TELECOMMUNICATIONS:
          return 'telecommunications';
        case RoleName.STANDARDS:
          return 'standards';
        case RoleName.CLF:
          return 'clf';
        default:
          return null;
      }
    }).filter(Boolean);

    if (licenseTypeFilters.length > 0) {
      // Add license type filter and status filter to the query
      const filterValue = licenseTypeFilters.length === 1
        ? licenseTypeFilters[0] as string
        : licenseTypeFilters as string[];

      const modifiedQuery: PaginateQuery = {
        ...query,
        filter: {
          ...query.filter,
          'license_category.license_type.name': filterValue,
          ...submittedStatusFilter
        }
      };

      const currentRelations = Array.isArray(this.paginateConfig.relations)
        ? this.paginateConfig.relations
        : [];

      const modifiedConfig: PaginateConfig<Applications> = {
        ...this.paginateConfig,
        relations: [...currentRelations, 'license_category.license_type'],
      };

      return paginate(modifiedQuery, this.applicationsRepository, modifiedConfig);
    }

    // Fallback: apply status filter for department users without specific license type filters
    const modifiedQuery: PaginateQuery = {
      ...query,
      filter: {
        ...query.filter,
        ...submittedStatusFilter
      }
    };

    return paginate(modifiedQuery, this.applicationsRepository, this.paginateConfig);
  }

  async findUserApplications(query: PaginateQuery): Promise<Paginated<Applications>> {
    // This method specifically filters applications by the authenticated user
    // It's used for customer-facing endpoints like /user-applications
    return paginate(query, this.applicationsRepository, this.paginateConfig);
  }

  async findOne(id: string): Promise<Applications> {
    const application = await this.applicationsRepository.findOne({
      where: { application_id: id },
      relations: ['applicant', 'license_category', 'license_category.license_type', 'creator', 'updater'],
    });

    if (!application) {
      throw new NotFoundException(`Application with ID ${id} not found`);
    }

    return application;
  }

  async findByApplicant(applicantId: string): Promise<Applications[]> {
    return this.applicationsRepository.find({
      where: { applicant_id: applicantId },
      relations: ['applicant', 'license_category', 'license_category.license_type', 'creator', 'updater'],
      order: { created_at: 'DESC' },
    });
  }

  async findByStatus(status: string): Promise<Applications[]> {
    return this.applicationsRepository.find({
      where: { status },
      relations: ['applicant', 'license_category', 'license_category.license_type', 'creator', 'updater'],
      order: { created_at: 'DESC' },
    });
  }

  async update(id: string, updateApplicationDto: UpdateApplicationDto, updatedBy: string): Promise<Applications> {
    const application = await this.findOne(id);

    // If updating application number, check for conflicts
    if (updateApplicationDto.application_number && updateApplicationDto.application_number !== application.application_number) {
      const existingApplication = await this.applicationsRepository.findOne({
        where: { application_number: updateApplicationDto.application_number },
      });

      if (existingApplication) {
        throw new ConflictException('Application number already exists');
      }
    }

    Object.assign(application, updateApplicationDto, { updated_by: updatedBy });
    return this.applicationsRepository.save(application);
  }

  async remove(id: string): Promise<void> {
    const application = await this.findOne(id);
    await this.applicationsRepository.softDelete(application.application_id);
  }

  async updateStatus(id: string, status: string, updatedBy: string): Promise<Applications> {
    const application = await this.findOne(id);
    application.status = status;
    application.updated_by = updatedBy;

    if (status === 'submitted' && !application.submitted_at) {
      application.submitted_at = new Date();
    }

    return this.applicationsRepository.save(application);
  }

  async updateProgress(id: string, currentStep: number, progressPercentage: number, updatedBy: string): Promise<Applications> {
    const application = await this.findOne(id);
    application.current_step = currentStep;
    application.progress_percentage = progressPercentage;
    application.updated_by = updatedBy;

    return this.applicationsRepository.save(application);
  }

  async getApplicationStats(): Promise<any> {
    const stats = await this.applicationsRepository
      .createQueryBuilder('application')
      .select('application.status', 'status')
      .addSelect('COUNT(*)', 'count')
      .groupBy('application.status')
      .getRawMany();

    return stats.reduce((acc, stat) => {
      acc[stat.status] = parseInt(stat.count);
      return acc;
    }, {});
  }
}
