import { User } from './user.entity';
export declare enum AuditAction {
    LOGIN = "login",
    LOGOUT = "logout",
    CREATE = "create",
    UPDATE = "update",
    DELETE = "delete",
    VIEW = "view",
    EXPORT = "export",
    IMPORT = "import"
}
export declare enum AuditModule {
    AUTHENTICATION = "authentication",
    USER_MANAGEMENT = "user_management",
    ROLE_MANAGEMENT = "role_management",
    PERMISSION_MANAGEMENT = "permission_management",
    LICENSE_MANAGEMENT = "license_management",
    DOCUMENT_MANAGEMENT = "document_management",
    SPECTRUM_MANAGEMENT = "spectrum_management",
    TRANSACTION_MANAGEMENT = "transaction_management",
    SYSTEM_SETTINGS = "system_settings",
    SYSTEM_MANAGEMENT = "system_management",
    POSTAL_SERVICES = "postal_services",
    TYPE_APPROVAL_SERVICES = "type_approval_services",
    SHORT_CODE_SERVICES = "short_code_services",
    TELECOMMUNICATION_SERVICES = "telecommunication_services",
    BROADCASTING_SERVICES = "broadcasting_services",
    ADDRESS_MANAGEMENT = "address_management",
    DASHBOARD = "dashboard"
}
export declare enum AuditStatus {
    SUCCESS = "success",
    FAILURE = "failure",
    WARNING = "warning"
}
export declare class AuditTrail {
    audit_id: string;
    action: string;
    module: string;
    status: string;
    resource_type: string;
    resource_id: string;
    description: string;
    old_values: Record<string, any>;
    new_values: Record<string, any>;
    metadata: Record<string, any>;
    ip_address: string;
    user_agent: string;
    session_id: string;
    error_message: string;
    user: User;
    user_id: string;
    created_at: Date;
    generateId(): void;
}
export declare const Audit: (options: {
    action: AuditAction;
    module: AuditModule;
    resourceType: string;
    description: string;
}) => import("@nestjs/common").CustomDecorator<string>;
