(()=>{var e={};e.id=688,e.ids=[688],e.modules={2124:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\auth\\\\verify-2fa\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\auth\\verify-2fa\\page.tsx","default")},2818:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var a=r(60687),s=r(43210),i=r(30474),n=r(16189);r(63443);var o=r(53862),l=r(55192);function c(){(0,n.useRouter)();let[e,t]=(0,s.useState)(""),[r,c]=(0,s.useState)(""),[d,u]=(0,s.useState)(""),[p,m]=(0,s.useState)(""),[x,f]=(0,s.useState)(""),[h,g]=(0,s.useState)(!0),v=(0,n.useSearchParams)();if(v.get("i"),v.get("unique"),v.get("c"),h)return(0,a.jsxs)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:[(0,a.jsx)(o.A,{className:"w-16 h-16 animate-spin text-gray-500 dark:text-gray-300"})," ",(0,a.jsx)("br",{}),(0,a.jsx)("span",{className:"place-content-center justify-center text-center text-gray-500 dark:text-gray-300",children:"Verifying your OTP..."})]});let b=x.toLowerCase().includes("enabled");return(0,a.jsxs)("div",{className:"min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900",children:[(0,a.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md text-center",children:[(0,a.jsx)(i.default,{src:"/images/macra-logo.png",alt:"MACRA Logo",width:50,height:50,className:"mx-auto h-16 w-auto"}),(0,a.jsx)("h2",{className:"mt-6 text-3xl font-extrabold text-gray-900 dark:text-white",children:x?"Account Verification Success!":"Account Verification"})]}),(0,a.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 py-8 px-6 shadow rounded-lg sm:px-10",children:[p&&!b&&(0,a.jsxs)("div",{className:"flex flex-col flex-auto items-center justify-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 mb-4 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center shadow-md",children:(0,a.jsx)(l.A,{className:"w-10 h-10 animate-pulse"})}),(0,a.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-md flex items-center",children:p})]}),(x||b)&&(0,a.jsxs)("div",{className:"flex flex-col flex-auto items-center justify-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 mb-4 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center animate-bounce shadow-md",children:(0,a.jsx)("svg",{className:"w-8 h-8 text-green-600 dark:text-green-300",fill:"none",stroke:"currentColor",strokeWidth:"3",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5 13l4 4L19 7"})})}),(0,a.jsxs)("div",{className:"text-center text-gray-600 dark:text-gray-400",children:[x," ",(0,a.jsx)("br",{})]})]})]})})]})}r(15659)},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},47943:(e,t,r)=>{Promise.resolve().then(r.bind(r,2818))},53862:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{fillRule:"evenodd",d:"M4.755 10.059a7.5 7.5 0 0 1 12.548-3.364l1.903 1.903h-3.183a.75.75 0 1 0 0 1.5h4.992a.75.75 0 0 0 .75-.75V4.356a.75.75 0 0 0-1.5 0v3.18l-1.9-1.9A9 9 0 0 0 3.306 9.67a.75.75 0 1 0 1.45.388Zm15.408 3.352a.75.75 0 0 0-.919.53 7.5 7.5 0 0 1-12.548 3.364l-1.902-1.903h3.183a.75.75 0 0 0 0-1.5H2.984a.75.75 0 0 0-.75.75v4.992a.75.75 0 0 0 1.5 0v-3.18l1.9 1.9a9 9 0 0 0 15.059-*********** 0 0 0-.53-.918Z",clipRule:"evenodd"}))})},55192:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{fillRule:"evenodd",d:"M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm-1.72 6.97a.75.75 0 1 0-1.06 1.06L10.94 12l-1.72 1.72a.75.75 0 1 0 1.06 1.06L12 13.06l1.72 1.72a.75.75 0 1 0 1.06-1.06L13.06 12l1.72-1.72a.75.75 0 1 0-1.06-1.06L12 10.94l-1.72-1.72Z",clipRule:"evenodd"}))})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64815:(e,t,r)=>{Promise.resolve().then(r.bind(r,2124))},68736:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,metadata:()=>s});var a=r(37413);let s={title:"Customer Dashboard - Digital Portal",description:"Customer portal for managing licenses and applications"};function i({children:e}){return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50",children:e})}},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(31658);let s=async e=>[{type:"image/x-icon",sizes:"256x256",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86765:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var a=r(65239),s=r(48088),i=r(88170),n=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["customer",{children:["auth",{children:["verify-2fa",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,2124)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\auth\\verify-2fa\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,68736)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\auth\\verify-2fa\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/customer/auth/verify-2fa/page",pathname:"/customer/auth/verify-2fa",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,7498,1658,7563],()=>r(86765));module.exports=a})();