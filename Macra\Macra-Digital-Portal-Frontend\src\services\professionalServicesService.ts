import { apiClient } from '../lib/apiClient';
import { processApiResponse } from '../lib/authUtils';

// Types following backend entity structure
export interface ProfessionalServicesData {
  professional_services_id?: string;
  application_id: string;
  consultants: string;
  service_providers: string;
  technical_support: string;
  maintenance_arrangements: string;
  professional_partnerships?: string;
  outsourced_services?: string;
  quality_assurance?: string;
  training_programs?: string;
  created_at?: string;
  updated_at?: string;
  created_by?: string;
  updated_by?: string;
}

export interface CreateProfessionalServicesData {
  application_id: string;
  consultants: string;
  service_providers: string;
  technical_support: string;
  maintenance_arrangements: string;
  professional_partnerships?: string;
  outsourced_services?: string;
  quality_assurance?: string;
  training_programs?: string;
}

export interface UpdateProfessionalServicesData {
  professional_services_id: string;
  consultants: string;
  service_providers: string;
  technical_support: string;
  maintenance_arrangements: string;
  professional_partnerships?: string;
  outsourced_services?: string;
  quality_assurance?: string;
  training_programs?: string;
}

class ProfessionalServicesService {
  private baseUrl = '/professional-services';

  // Create professional services record
  async createProfessionalServices(data: CreateProfessionalServicesData): Promise<ProfessionalServicesData> {
    try {
      console.log('🔧 Creating professional services record:', data);
      const response = await apiClient.post(this.baseUrl, data);
      return processApiResponse(response);
    } catch (error: any) {
      console.error('❌ Error creating professional services:', error);
      throw error;
    }
  }

  // Update professional services record
  async updateProfessionalServices(data: UpdateProfessionalServicesData): Promise<ProfessionalServicesData> {
    try {
      console.log('🔧 Updating professional services record:', data.professional_services_id);
      const response = await apiClient.put(`${this.baseUrl}/${data.professional_services_id}`, data);
      return processApiResponse(response);
    } catch (error: any) {
      console.error('❌ Error updating professional services:', error);
      throw error;
    }
  }

  // Get professional services by application ID
  async getProfessionalServicesByApplication(applicationId: string): Promise<ProfessionalServicesData | null> {
    try {
      console.log('🔧 Getting professional services for application:', applicationId);
      const response = await apiClient.get(`${this.baseUrl}/application/${applicationId}`);
      return processApiResponse(response);
    } catch (error: any) {
      if (error.response?.status === 404) {
        console.log('📝 No professional services found for application:', applicationId);
        return null;
      }
      console.error('❌ Error getting professional services:', error);
      throw error;
    }
  }

  // Get professional services by ID
  async getProfessionalServices(professionalServicesId: string): Promise<ProfessionalServicesData> {
    try {
      console.log('🔧 Getting professional services:', professionalServicesId);
      const response = await apiClient.get(`${this.baseUrl}/${professionalServicesId}`);
      return processApiResponse(response);
    } catch (error: any) {
      console.error('❌ Error getting professional services:', error);
      throw error;
    }
  }

  // Delete professional services record
  async deleteProfessionalServices(professionalServicesId: string): Promise<void> {
    try {
      console.log('🔧 Deleting professional services:', professionalServicesId);
      const response = await apiClient.delete(`${this.baseUrl}/${professionalServicesId}`);
      return processApiResponse(response);
    } catch (error: any) {
      console.error('❌ Error deleting professional services:', error);
      throw error;
    }
  }

  // Get all professional services (admin only)
  async getAllProfessionalServices(): Promise<ProfessionalServicesData[]> {
    try {
      console.log('🔧 Getting all professional services');
      const response = await apiClient.get(this.baseUrl);
      return processApiResponse(response);
    } catch (error: any) {
      console.error('❌ Error getting all professional services:', error);
      throw error;
    }
  }
}

export const professionalServicesService = new ProfessionalServicesService();
