(()=>{var e={};e.id=2855,e.ids=[2855],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23173:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var a=r(65239),s=r(48088),i=r(88170),n=r.n(i),l=r(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);r.d(t,d);let o={children:["",{children:["customer",{children:["payments",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,74881)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\payments\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,68736)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\payments\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/customer/payments/page",pathname:"/customer/payments",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29751:(e,t,r)=>{Promise.resolve().then(r.bind(r,74881))},29795:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m});var a=r(60687),s=r(43210),i=r(16189),n=r(94391),l=r(71773),d=r(63213);let o=[{id:"PAY-2025-001",invoiceNumber:"INV-2025-001",amount:2e7,currency:"MWK",status:"paid",dueDate:"2025-04-11",paidDate:"2025-03-15",issueDate:"2025-03-11",description:"Internet Service Provider License - 5 year license for telecommunications services",paymentType:"License Fee",clientName:"Airtel Malawi",clientEmail:"<EMAIL>",paymentMethod:"Bank Transfer",notes:"Payment for internet service provider license renewal"},{id:"PAY-2025-002",invoiceNumber:"INV-2025-002",amount:15e4,currency:"MWK",status:"paid",dueDate:"2025-02-15",paidDate:"2025-02-10",issueDate:"2025-01-15",description:"Tender document for procurement - ICT equipment procurement tender documentation",paymentType:"Procurement Fee",clientName:"TechSolutions Ltd",clientEmail:"<EMAIL>",paymentMethod:"Mobile Money",notes:"Payment for tender document access and procurement process participation"},{id:"PAY-2025-003",invoiceNumber:"INV-2025-003",amount:6565e3,currency:"MWK",status:"pending",dueDate:"2025-01-25",issueDate:"2024-12-25",description:"Radio Broadcasting License - Commercial radio broadcasting license for FM frequency",paymentType:"License Fee",clientName:"Dawn FM",clientEmail:"<EMAIL>",notes:"Radio broadcasting license for 3 years"},{id:"PAY-2025-004",invoiceNumber:"INV-2025-004",amount:75e3,currency:"MWK",status:"paid",dueDate:"2025-03-01",paidDate:"2025-02-28",issueDate:"2025-02-01",description:"Tender document for procurement - Network infrastructure upgrade tender",paymentType:"Procurement Fee",clientName:"NetworkPro Systems",clientEmail:"<EMAIL>",paymentMethod:"Credit Card",notes:"Tender documentation fee for network infrastructure procurement"},{id:"PAY-2025-005",invoiceNumber:"INV-2025-005",amount:5e7,currency:"MWK",status:"overdue",dueDate:"2025-02-01",issueDate:"2025-01-01",description:"TV Broadcasting License - Digital terrestrial television broadcasting license",paymentType:"License Fee",clientName:"Crunchyroll TV",clientEmail:"<EMAIL>",notes:"TV broadcasting license for digital terrestrial services"},{id:"PAY-2025-006",invoiceNumber:"INV-2025-006",amount:25e3,currency:"MWK",status:"paid",dueDate:"2025-01-20",paidDate:"2025-01-18",issueDate:"2024-12-20",description:"Tender document for procurement - Regulatory compliance software procurement",paymentType:"Procurement Fee",clientName:"ComplianceTech Solutions",clientEmail:"<EMAIL>",paymentMethod:"Bank Transfer",notes:"Procurement tender for regulatory compliance management system"},{id:"PAY-2024-007",invoiceNumber:"INV-2024-007",amount:25e5,currency:"MWK",status:"paid",dueDate:"2024-10-31",paidDate:"2024-10-25",issueDate:"2024-10-01",description:"Mobile Network License - Mobile virtual network operator license",paymentType:"License Fee",clientName:"MobileConnect Ltd",clientEmail:"<EMAIL>",paymentMethod:"Bank Transfer",notes:"MVNO license for mobile telecommunications services"}],c=["All Types","License Fee","Procurement Fee","Application Fee","Renewal Fee","Penalty Fee"],m=()=>{let{isAuthenticated:e}=(0,d.A)(),t=(0,i.useRouter)(),[r,m]=(0,s.useState)([]),[x,p]=(0,s.useState)([]),[u,g]=(0,s.useState)(!0),[y,h]=(0,s.useState)(""),[b,f]=(0,s.useState)(""),[v,k]=(0,s.useState)(""),[j,N]=(0,s.useState)(""),[w,P]=(0,s.useState)(""),[D,M]=(0,s.useState)(1);(0,s.useEffect)(()=>{e||t.push("/customer/auth/login")},[e,t]),(0,s.useEffect)(()=>{(async()=>{if(e)try{g(!0),h(""),m(o),p(o)}catch(e){h("Failed to load payments. Please try refreshing the page.")}finally{g(!1)}})()},[e]),(0,s.useEffect)(()=>{let e=r;if(b&&(e=e.filter(e=>e.status===b)),v&&"All Types"!==v&&(e=e.filter(e=>e.paymentType===v)),j){let t=new Date,r=new Date;switch(j){case"last-30":r.setDate(t.getDate()-30);break;case"last-90":r.setDate(t.getDate()-90);break;case"last-year":r.setFullYear(t.getFullYear()-1);break;default:r.setFullYear(1970)}e=e.filter(e=>new Date(e.issueDate)>=r)}if(w){let t=w.toLowerCase();e=e.filter(e=>e.invoiceNumber.toLowerCase().includes(t)||e.clientName.toLowerCase().includes(t)||e.description.toLowerCase().includes(t)||e.paymentType.toLowerCase().includes(t))}p(e),M(1)},[r,b,v,j,w]);let C=e=>{switch(e){case"paid":return"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";case"pending":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";case"overdue":return"bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400"}},F=e=>{switch(e){case"License Fee":return"bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";case"Procurement Fee":return"bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400";case"Application Fee":return"bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400";case"Renewal Fee":return"bg-teal-100 text-teal-800 dark:bg-teal-900/20 dark:text-teal-400";case"Penalty Fee":return"bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400"}},T=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),A=(e,t)=>`${t} ${e.toLocaleString()}`,L=Math.ceil(x.length/10),S=(D-1)*10,q=S+10,E=x.slice(S,q);return u?(0,a.jsx)(n.A,{children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,a.jsx)(l.A,{message:"Loading payments..."})})}):y?(0,a.jsx)(n.A,{children:(0,a.jsxs)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-300 px-4 py-3 rounded mb-6",children:[(0,a.jsx)("p",{children:y}),(0,a.jsx)("button",{type:"button",onClick:()=>window.location.reload(),className:"mt-2 text-sm underline hover:no-underline",children:"Try again"})]})}):(0,a.jsx)(n.A,{children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:"Payments"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"View all your payment records including license fees, procurement payments, and other transactions."})]}),(0,a.jsx)("div",{className:"flex items-center space-x-3",children:(0,a.jsxs)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Total: ",x.length," payment",1!==x.length?"s":""]})})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md mb-6",children:(0,a.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{htmlFor:"search",className:"sr-only",children:"Search payments"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)("i",{className:"ri-search-line text-gray-400"})}),(0,a.jsx)("input",{id:"search",type:"text",value:w,onChange:e=>P(e.target.value),className:"block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-sm text-gray-900 dark:text-gray-100",placeholder:"Search by invoice number, description, or payment type..."})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-y-4 sm:grid-cols-4 sm:gap-x-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"status-filter",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Status"}),(0,a.jsxs)("select",{id:"status-filter",value:b,onChange:e=>f(e.target.value),className:"mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 p-2 shadow-sm focus:ring-primary focus:border-primary sm:text-sm",children:[(0,a.jsx)("option",{value:"",children:"All Statuses"}),(0,a.jsx)("option",{value:"paid",children:"Paid"}),(0,a.jsx)("option",{value:"pending",children:"Pending"}),(0,a.jsx)("option",{value:"overdue",children:"Overdue"}),(0,a.jsx)("option",{value:"cancelled",children:"Cancelled"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"type-filter",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Payment Type"}),(0,a.jsx)("select",{id:"type-filter",value:v,onChange:e=>k(e.target.value),className:"mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 p-2 shadow-sm focus:ring-primary focus:border-primary sm:text-sm",children:c.map(e=>(0,a.jsx)("option",{value:e,children:e},e))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"date-filter",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Date Range"}),(0,a.jsxs)("select",{id:"date-filter",value:j,onChange:e=>N(e.target.value),className:"mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 p-2 shadow-sm focus:ring-primary focus:border-primary sm:text-sm",children:[(0,a.jsx)("option",{value:"",children:"All Time"}),(0,a.jsx)("option",{value:"last-30",children:"Last 30 Days"}),(0,a.jsx)("option",{value:"last-90",children:"Last 90 Days"}),(0,a.jsx)("option",{value:"last-year",children:"Last Year"})]})]}),(0,a.jsx)("div",{className:"flex items-end",children:(0,a.jsx)("button",{type:"button",onClick:()=>{f(""),k(""),N(""),P("")},className:"inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 dark:text-gray-400 hover:text-white bg-white dark:bg-gray-700 border border-primary rounded-full hover:bg-red-700 dark:hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors duration-300",children:"Clear Filters"})})]})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md",children:(0,a.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,a.jsx)("div",{className:"flex flex-col",children:(0,a.jsx)("div",{className:"-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8",children:(0,a.jsx)("div",{className:"py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8",children:(0,a.jsx)("div",{className:"overflow-hidden border border-gray-200 dark:border-gray-700 sm:rounded-lg",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[(0,a.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-900",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Invoice #"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Client"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Payment Type"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Description"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Issue Date"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Due Date"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Amount"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{scope:"col",className:"relative px-6 py-3",children:(0,a.jsx)("span",{className:"sr-only",children:"Actions"})})]})}),(0,a.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:0===E.length?(0,a.jsx)("tr",{children:(0,a.jsx)("td",{colSpan:9,className:"px-6 py-12 text-center",children:(0,a.jsxs)("div",{className:"text-gray-500 dark:text-gray-400",children:[(0,a.jsx)("i",{className:"ri-file-list-line text-4xl mb-4"}),(0,a.jsx)("p",{className:"text-lg font-medium",children:"No payments found"}),(0,a.jsx)("p",{className:"text-sm",children:"Try adjusting your search criteria or filters."})]})})}):E.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:e.invoiceNumber})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:e.clientName}),(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:e.clientEmail})]})})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${F(e.paymentType)}`,children:e.paymentType})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100 max-w-xs truncate",title:e.description,children:e.description})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:T(e.issueDate)})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:T(e.dueDate)})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:A(e.amount,e.currency)})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${C(e.status)}`,children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:[(0,a.jsx)("button",{className:"text-primary hover:text-primary-dark mr-3 transition-colors",children:"View"}),(0,a.jsx)("button",{className:"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 transition-colors",children:"Download"})]})]},e.id))})]})})})})}),L>1&&(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 sm:px-6",children:[(0,a.jsxs)("div",{className:"flex-1 flex justify-between sm:hidden",children:[(0,a.jsx)("button",{onClick:()=>M(Math.max(1,D-1)),disabled:1===D,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,a.jsx)("button",{onClick:()=>M(Math.min(L,D+1)),disabled:D===L,className:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]}),(0,a.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,a.jsx)("div",{children:(0,a.jsxs)("p",{className:"text-sm text-gray-700 dark:text-gray-300",children:["Showing ",(0,a.jsx)("span",{className:"font-medium",children:S+1})," to"," ",(0,a.jsx)("span",{className:"font-medium",children:Math.min(q,x.length)})," of"," ",(0,a.jsx)("span",{className:"font-medium",children:x.length})," payments"]})}),(0,a.jsx)("div",{children:(0,a.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[(0,a.jsxs)("button",{onClick:()=>M(Math.max(1,D-1)),disabled:1===D,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,a.jsx)("span",{className:"sr-only",children:"Previous"}),(0,a.jsx)("i",{className:"ri-arrow-left-s-line"})]}),Array.from({length:Math.min(5,L)},(e,t)=>{let r;return r=L<=5||D<=3?t+1:D>=L-2?L-4+t:D-2+t,(0,a.jsx)("button",{onClick:()=>M(r),className:`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${D===r?"z-10 bg-primary border-primary text-white":"bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600"}`,children:r},r)}),(0,a.jsxs)("button",{onClick:()=>M(Math.min(L,D+1)),disabled:D===L,className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,a.jsx)("span",{className:"sr-only",children:"Next"}),(0,a.jsx)("i",{className:"ri-arrow-right-s-line"})]})]})})]})]})]})})]})})}},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},74881:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\payments\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\payments\\page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82895:(e,t,r)=>{Promise.resolve().then(r.bind(r,29795))},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,7498,1658,5814,2335,6893],()=>r(23173));module.exports=a})();