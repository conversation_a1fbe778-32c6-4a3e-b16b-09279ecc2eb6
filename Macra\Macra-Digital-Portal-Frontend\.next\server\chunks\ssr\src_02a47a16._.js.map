{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/services/consumer-affairs/consumerAffairsService.ts"], "sourcesContent": ["import { apiClient } from '@/lib/apiClient';\r\nimport { processApiResponse } from '@/lib/authUtils';\r\n\r\n// Types following backend entity structure\r\nexport interface ConsumerAffairsComplaint {\r\n  complaint_id: string;\r\n  complaint_number: string;\r\n  complainant_id: string;\r\n  title: string;\r\n  description: string;\r\n  category: ComplaintCategory;\r\n  status: ComplaintStatus;\r\n  priority: ComplaintPriority;\r\n  assigned_to?: string;\r\n  resolution?: string;\r\n  internal_notes?: string;\r\n  resolved_at?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  deleted_at?: string;\r\n  created_by?: string;\r\n  updated_by?: string;\r\n\r\n  // Related data\r\n  complainant?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n\r\n  assignee?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n\r\n  attachments?: ConsumerAffairsComplaintAttachment[];\r\n  status_history?: ConsumerAffairsComplaintStatusHistory[];\r\n}\r\n\r\nexport interface ConsumerAffairsComplaintAttachment {\r\n  attachment_id: string;\r\n  complaint_id: string;\r\n  file_name: string;\r\n  file_type: string;\r\n  file_size: number;\r\n  file_path: string;\r\n  uploaded_at: string;\r\n  uploaded_by: string;\r\n}\r\n\r\nexport interface ConsumerAffairsComplaintStatusHistory {\r\n  history_id: string;\r\n  complaint_id: string;\r\n  status: ComplaintStatus;\r\n  comment?: string;\r\n  created_at: string;\r\n  created_by: string;\r\n}\r\n\r\n// Enums matching backend\r\nexport enum ComplaintCategory {\r\n  BILLING_CHARGES = 'Billing & Charges',\r\n  SERVICE_QUALITY = 'Service Quality',\r\n  NETWORK_ISSUES = 'Network Issues',\r\n  CUSTOMER_SERVICE = 'Customer Service',\r\n  CONTRACT_DISPUTES = 'Contract Disputes',\r\n  ACCESSIBILITY = 'Accessibility',\r\n  FRAUD_SCAMS = 'Fraud & Scams',\r\n  OTHER = 'Other',\r\n}\r\n\r\nexport enum ComplaintStatus {\r\n  SUBMITTED = 'submitted',\r\n  UNDER_REVIEW = 'under_review',\r\n  INVESTIGATING = 'investigating',\r\n  RESOLVED = 'resolved',\r\n  CLOSED = 'closed',\r\n}\r\n\r\nexport enum ComplaintPriority {\r\n  LOW = 'low',\r\n  MEDIUM = 'medium',\r\n  HIGH = 'high',\r\n  URGENT = 'urgent',\r\n}\r\n\r\nexport interface CreateConsumerAffairsComplaintData {\r\n  title: string;\r\n  description: string;\r\n  category: ComplaintCategory;\r\n  priority?: ComplaintPriority;\r\n  attachments?: File[];\r\n}\r\n\r\nexport interface UpdateConsumerAffairsComplaintData {\r\n  title?: string;\r\n  description?: string;\r\n  category?: ComplaintCategory;\r\n  status?: ComplaintStatus;\r\n  priority?: ComplaintPriority;\r\n  assigned_to?: string;\r\n  resolution?: string;\r\n  internal_notes?: string;\r\n  resolved_at?: string;\r\n}\r\n\r\n// Pagination interfaces following user service pattern\r\nexport interface PaginatedResponse<T> {\r\n  data: T[];\r\n  meta: {\r\n    itemsPerPage: number;\r\n    totalItems: number;\r\n    currentPage: number;\r\n    totalPages: number;\r\n    sortBy: string[][];\r\n    searchBy: string[];\r\n    search: string;\r\n    filter: Record<string, string | string[]>;\r\n  };\r\n  links: {\r\n    first: string;\r\n    previous: string;\r\n    current: string;\r\n    next: string;\r\n    last: string;\r\n  };\r\n}\r\n\r\nexport interface PaginateQuery {\r\n  page?: number;\r\n  limit?: number;\r\n  sortBy?: string[];\r\n  searchBy?: string[];\r\n  search?: string;\r\n  filter?: Record<string, string | string[]>;\r\n}\r\n\r\nexport type ConsumerAffairsComplaintsResponse = PaginatedResponse<ConsumerAffairsComplaint>;\r\n\r\nexport const consumerAffairsService = {\r\n\r\n  // Create new complaint\r\n  async createComplaint(data: CreateConsumerAffairsComplaintData): Promise<ConsumerAffairsComplaint> {\r\n    try {\r\n      console.log('🔄 Creating consumer affairs complaint:', {\r\n        title: data.title,\r\n        category: data.category,\r\n        hasAttachments: data.attachments && data.attachments.length > 0\r\n      });\r\n\r\n      const formData = new FormData();\r\n      formData.append('title', data.title);\r\n      formData.append('description', data.description);\r\n      formData.append('category', data.category);\r\n\r\n      if (data.priority) {\r\n        formData.append('priority', data.priority);\r\n      }\r\n\r\n      // Add attachments if provided\r\n      if (data.attachments && data.attachments.length > 0) {\r\n        data.attachments.forEach((file) => {\r\n          formData.append('attachments', file);\r\n        });\r\n      }\r\n\r\n      const response = await apiClient.post('/consumer-affairs-complaints', formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data',\r\n        },\r\n      });\r\n\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get all complaints with pagination\r\n  async getComplaints(query: PaginateQuery = {}): Promise<ConsumerAffairsComplaintsResponse> {\r\n    const params = new URLSearchParams();\r\n\r\n    if (query.page) params.set('page', query.page.toString());\r\n    if (query.limit) params.set('limit', query.limit.toString());\r\n    if (query.search) params.set('search', query.search);\r\n    if (query.sortBy) {\r\n      query.sortBy.forEach(sort => params.append('sortBy', sort));\r\n    }\r\n    if (query.searchBy) {\r\n      query.searchBy.forEach(search => params.append('searchBy', search));\r\n    }\r\n    if (query.filter) {\r\n      Object.entries(query.filter).forEach(([key, value]) => {\r\n        if (Array.isArray(value)) {\r\n          value.forEach(v => params.append(`filter.${key}`, v));\r\n        } else {\r\n          params.set(`filter.${key}`, value);\r\n        }\r\n      });\r\n    }\r\n\r\n    const response = await apiClient.get(`/consumer-affairs-complaints?${params.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get complaint by ID\r\n  async getComplaint(id: string): Promise<ConsumerAffairsComplaint> {\r\n    const response = await apiClient.get(`/consumer-affairs-complaints/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get complaint by ID (alias for consistency)\r\n  async getComplaintById(id: string): Promise<ConsumerAffairsComplaint> {\r\n    return this.getComplaint(id);\r\n  },\r\n\r\n  // Update complaint\r\n  async updateComplaint(id: string, data: UpdateConsumerAffairsComplaintData): Promise<ConsumerAffairsComplaint> {\r\n    const response = await apiClient.put(`/consumer-affairs-complaints/${id}`, data);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Delete complaint\r\n  async deleteComplaint(id: string): Promise<void> {\r\n    await apiClient.delete(`/consumer-affairs-complaints/${id}`);\r\n  },\r\n\r\n  // Update complaint status (for staff)\r\n  async updateComplaintStatus(id: string, status: ComplaintStatus, comment?: string): Promise<ConsumerAffairsComplaint> {\r\n    const response = await apiClient.put(`/consumer-affairs-complaints/${id}/status`, {\r\n      status,\r\n      comment\r\n    });\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Add attachment to complaint\r\n  async addAttachment(id: string, file: File): Promise<ConsumerAffairsComplaintAttachment> {\r\n    const formData = new FormData();\r\n    formData.append('files', file);\r\n\r\n    const response = await apiClient.post(`/consumer-affairs-complaints/${id}/attachments`, formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Remove attachment from complaint\r\n  async removeAttachment(complaintId: string, attachmentId: string): Promise<void> {\r\n    await apiClient.delete(`/consumer-affairs-complaints/${complaintId}/attachments/${attachmentId}`);\r\n  },\r\n\r\n\r\n\r\n  // Helper methods\r\n  getStatusColor(status: string): string {\r\n    switch (status?.toLowerCase()) {\r\n      case 'submitted': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';\r\n      case 'under_review': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\r\n      case 'investigating': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\r\n      case 'resolved': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\r\n      case 'closed': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\r\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\r\n    }\r\n  },\r\n\r\n  getPriorityColor(priority: string): string {\r\n    switch (priority?.toLowerCase()) {\r\n      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\r\n      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\r\n      case 'high': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\r\n      case 'urgent': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';\r\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\r\n    }\r\n  },\r\n\r\n  getStatusOptions(): Array<{ value: string; label: string }> {\r\n    return [\r\n      { value: 'submitted', label: 'Submitted' },\r\n      { value: 'under_review', label: 'Under Review' },\r\n      { value: 'investigating', label: 'Investigating' },\r\n      { value: 'resolved', label: 'Resolved' },\r\n      { value: 'closed', label: 'Closed' }\r\n    ];\r\n  },\r\n\r\n  getCategoryOptions(): Array<{ value: string; label: string }> {\r\n    return [\r\n      { value: 'Billing & Charges', label: 'Billing & Charges' },\r\n      { value: 'Service Quality', label: 'Service Quality' },\r\n      { value: 'Network Issues', label: 'Network Issues' },\r\n      { value: 'Customer Service', label: 'Customer Service' },\r\n      { value: 'Contract Disputes', label: 'Contract Disputes' },\r\n      { value: 'Accessibility', label: 'Accessibility' },\r\n      { value: 'Fraud & Scams', label: 'Fraud & Scams' },\r\n      { value: 'Other', label: 'Other' }\r\n    ];\r\n  },\r\n\r\n  getPriorityOptions(): Array<{ value: string; label: string }> {\r\n    return [\r\n      { value: 'low', label: 'Low' },\r\n      { value: 'medium', label: 'Medium' },\r\n      { value: 'high', label: 'High' },\r\n      { value: 'urgent', label: 'Urgent' }\r\n    ];\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AA8DO,IAAA,AAAK,2CAAA;;;;;;;;;WAAA;;AAWL,IAAA,AAAK,yCAAA;;;;;;WAAA;;AAQL,IAAA,AAAK,2CAAA;;;;;WAAA;;AA4DL,MAAM,yBAAyB;IAEpC,uBAAuB;IACvB,MAAM,iBAAgB,IAAwC;QAC5D,IAAI;YACF,QAAQ,GAAG,CAAC,2CAA2C;gBACrD,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;gBACvB,gBAAgB,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG;YAChE;YAEA,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,SAAS,KAAK,KAAK;YACnC,SAAS,MAAM,CAAC,eAAe,KAAK,WAAW;YAC/C,SAAS,MAAM,CAAC,YAAY,KAAK,QAAQ;YAEzC,IAAI,KAAK,QAAQ,EAAE;gBACjB,SAAS,MAAM,CAAC,YAAY,KAAK,QAAQ;YAC3C;YAEA,8BAA8B;YAC9B,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG,GAAG;gBACnD,KAAK,WAAW,CAAC,OAAO,CAAC,CAAC;oBACxB,SAAS,MAAM,CAAC,eAAe;gBACjC;YACF;YAEA,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,gCAAgC,UAAU;gBAC9E,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,qCAAqC;IACrC,MAAM,eAAc,QAAuB,CAAC,CAAC;QAC3C,MAAM,SAAS,IAAI;QAEnB,IAAI,MAAM,IAAI,EAAE,OAAO,GAAG,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ;QACtD,IAAI,MAAM,KAAK,EAAE,OAAO,GAAG,CAAC,SAAS,MAAM,KAAK,CAAC,QAAQ;QACzD,IAAI,MAAM,MAAM,EAAE,OAAO,GAAG,CAAC,UAAU,MAAM,MAAM;QACnD,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,MAAM,CAAC,OAAO,CAAC,CAAA,OAAQ,OAAO,MAAM,CAAC,UAAU;QACvD;QACA,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAA,SAAU,OAAO,MAAM,CAAC,YAAY;QAC7D;QACA,IAAI,MAAM,MAAM,EAAE;YAChB,OAAO,OAAO,CAAC,MAAM,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAChD,IAAI,MAAM,OAAO,CAAC,QAAQ;oBACxB,MAAM,OAAO,CAAC,CAAA,IAAK,OAAO,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBACpD,OAAO;oBACL,OAAO,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBAC9B;YACF;QACF;QAEA,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,OAAO,QAAQ,IAAI;QACxF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,sBAAsB;IACtB,MAAM,cAAa,EAAU;QAC3B,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,IAAI;QACzE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8CAA8C;IAC9C,MAAM,kBAAiB,EAAU;QAC/B,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B;IAEA,mBAAmB;IACnB,MAAM,iBAAgB,EAAU,EAAE,IAAwC;QACxE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,IAAI,EAAE;QAC3E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,mBAAmB;IACnB,MAAM,iBAAgB,EAAU;QAC9B,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,6BAA6B,EAAE,IAAI;IAC7D;IAEA,sCAAsC;IACtC,MAAM,uBAAsB,EAAU,EAAE,MAAuB,EAAE,OAAgB;QAC/E,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,GAAG,OAAO,CAAC,EAAE;YAChF;YACA;QACF;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8BAA8B;IAC9B,MAAM,eAAc,EAAU,EAAE,IAAU;QACxC,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS;QAEzB,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,CAAC,6BAA6B,EAAE,GAAG,YAAY,CAAC,EAAE,UAAU;YAChG,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,mCAAmC;IACnC,MAAM,kBAAiB,WAAmB,EAAE,YAAoB;QAC9D,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,6BAA6B,EAAE,YAAY,aAAa,EAAE,cAAc;IAClG;IAIA,iBAAiB;IACjB,gBAAe,MAAc;QAC3B,OAAQ,QAAQ;YACd,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAiB,OAAO;YAC7B,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,kBAAiB,QAAgB;QAC/B,OAAQ,UAAU;YAChB,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA;QACE,OAAO;YACL;gBAAE,OAAO;gBAAa,OAAO;YAAY;YACzC;gBAAE,OAAO;gBAAgB,OAAO;YAAe;YAC/C;gBAAE,OAAO;gBAAiB,OAAO;YAAgB;YACjD;gBAAE,OAAO;gBAAY,OAAO;YAAW;YACvC;gBAAE,OAAO;gBAAU,OAAO;YAAS;SACpC;IACH;IAEA;QACE,OAAO;YACL;gBAAE,OAAO;gBAAqB,OAAO;YAAoB;YACzD;gBAAE,OAAO;gBAAmB,OAAO;YAAkB;YACrD;gBAAE,OAAO;gBAAkB,OAAO;YAAiB;YACnD;gBAAE,OAAO;gBAAoB,OAAO;YAAmB;YACvD;gBAAE,OAAO;gBAAqB,OAAO;YAAoB;YACzD;gBAAE,OAAO;gBAAiB,OAAO;YAAgB;YACjD;gBAAE,OAAO;gBAAiB,OAAO;YAAgB;YACjD;gBAAE,OAAO;gBAAS,OAAO;YAAQ;SAClC;IACH;IAEA;QACE,OAAO;YACL;gBAAE,OAAO;gBAAO,OAAO;YAAM;YAC7B;gBAAE,OAAO;gBAAU,OAAO;YAAS;YACnC;gBAAE,OAAO;gBAAQ,OAAO;YAAO;YAC/B;gBAAE,OAAO;gBAAU,OAAO;YAAS;SACpC;IACH;AACF", "debugId": null}}, {"offset": {"line": 258, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/services/consumer-affairs/index.ts"], "sourcesContent": ["// Consumer Affairs Services\nexport * from './consumerAffairsService';\nexport { consumerAffairsService } from './consumerAffairsService';\n"], "names": [], "mappings": "AAAA,4BAA4B;;AAC5B", "debugId": null}}, {"offset": {"line": 278, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/services/task-assignment.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\r\n\r\n// Generic Task interface for different types of tasks\r\nexport interface GenericTask {\r\n  task_id: string;\r\n  task_type: 'application' | 'complaint' | 'data_breach' | 'evaluation' | 'inspection';\r\n  task_number: string;\r\n  title: string;\r\n  description: string;\r\n  status: string;\r\n  priority?: 'low' | 'medium' | 'high' | 'urgent';\r\n  created_at: string;\r\n  updated_at: string;\r\n  assigned_to?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n  assigned_by?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n  assigned_at?: string;\r\n  due_date?: string;\r\n  metadata?: {\r\n    [key: string]: unknown;\r\n  };\r\n}\r\n\r\n// Legacy interface for backward compatibility\r\nexport interface TaskAssignmentApplication {\r\n  application_id: string;\r\n  application_number: string;\r\n  status: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  applicant: {\r\n    applicant_id: string;\r\n    company_name: string;\r\n    first_name: string;\r\n    last_name: string;\r\n  };\r\n  license_category: {\r\n    license_category_id: string;\r\n    name: string;\r\n    license_type: {\r\n      license_type_id: string;\r\n      name: string;\r\n    };\r\n  };\r\n  assignee?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n  assigned_at?: string;\r\n}\r\n\r\nexport interface TaskAssignmentOfficer {\r\n  user_id: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  email: string;\r\n  department?: string;\r\n}\r\n\r\nexport interface AssignApplicationRequest {\r\n  assignedTo: string;\r\n}\r\n\r\nexport interface PaginatedResponse<T> {\r\n  data: T[];\r\n  meta: {\r\n    itemCount: number;\r\n    totalItems: number;\r\n    itemsPerPage: number;\r\n    totalPages: number;\r\n    currentPage: number;\r\n  };\r\n  links: {\r\n    first: string;\r\n    previous: string;\r\n    next: string;\r\n    last: string;\r\n  };\r\n}\r\n\r\nexport const taskAssignmentService = {\r\n  // Generic task management methods\r\n  getUnassignedTasks: async (params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    task_type?: string;\r\n  }) => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n    if (params?.task_type) searchParams.append('task_type', params.task_type);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/tasks/unassigned${queryString ? `?${queryString}` : ''}`;\r\n\r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  getAssignedTasks: async (params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    task_type?: string;\r\n  }) => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n    if (params?.task_type) searchParams.append('task_type', params.task_type);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/tasks/assigned${queryString ? `?${queryString}` : ''}`;\r\n\r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  assignTask: async (taskId: string, assignData: { assignedTo: string; comment?: string }) => {\r\n    const response = await apiClient.put(`/tasks/${taskId}/assign`, assignData);\r\n    return response.data;\r\n  },\r\n\r\n  getTaskById: async (taskId: string) => {\r\n    const response = await apiClient.get(`/tasks/${taskId}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Legacy application-specific methods (for backward compatibility)\r\n  getUnassignedApplications: async (params?: { page?: number; limit?: number; search?: string }) => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/applications/unassigned${queryString ? `?${queryString}` : ''}`;\r\n    \r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  // Get all applications (including assigned)\r\n  getAllApplications: async (params?: { page?: number; limit?: number; search?: string }) => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/applications${queryString ? `?${queryString}` : ''}`;\r\n    \r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  // Get applications assigned to current user\r\n  getMyAssignedApplications: async (params?: { page?: number; limit?: number; search?: string }) => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/applications/assigned/me${queryString ? `?${queryString}` : ''}`;\r\n    \r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  // Get officers for assignment\r\n  getOfficers: async () => {\r\n    try {\r\n      const response = await apiClient.get('/users');\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error fetching officers:', error);\r\n      return { data: [] };\r\n    }\r\n  },\r\n\r\n  // Assign application to officer\r\n  assignApplication: async (applicationId: string, assignData: AssignApplicationRequest) => {\r\n    const response = await apiClient.put(`/applications/${applicationId}/assign`, assignData);\r\n    return response.data;\r\n  },\r\n\r\n  // Get application details\r\n  getApplication: async (applicationId: string) => {\r\n    const response = await apiClient.get(`/applications/${applicationId}`);\r\n    return response.data;\r\n  },\r\n};"], "names": [], "mappings": ";;;AAAA;;AA2FO,MAAM,wBAAwB;IACnC,kCAAkC;IAClC,oBAAoB,OAAO;QAMzB,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAC/D,IAAI,QAAQ,WAAW,aAAa,MAAM,CAAC,aAAa,OAAO,SAAS;QAExE,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAEtE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB,OAAO;QAMvB,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAC/D,IAAI,QAAQ,WAAW,aAAa,MAAM,CAAC,aAAa,OAAO,SAAS;QAExE,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAEpE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,OAAO,OAAO,CAAC,EAAE;QAChE,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ;QACvD,OAAO,SAAS,IAAI;IACtB;IAEA,mEAAmE;IACnE,2BAA2B,OAAO;QAChC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAE/D,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,wBAAwB,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAE7E,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,4CAA4C;IAC5C,oBAAoB,OAAO;QACzB,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAE/D,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAElE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,4CAA4C;IAC5C,2BAA2B,OAAO;QAChC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAE/D,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,yBAAyB,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAE9E,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,8BAA8B;IAC9B,aAAa;QACX,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;gBAAE,MAAM,EAAE;YAAC;QACpB;IACF;IAEA,gCAAgC;IAChC,mBAAmB,OAAO,eAAuB;QAC/C,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,cAAc,OAAO,CAAC,EAAE;QAC9E,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe;QACrE,OAAO,SAAS,IAAI;IACtB;AACF", "debugId": null}}, {"offset": {"line": 377, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/common/AssignModal.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useToast } from '@/contexts/ToastContext';\nimport { taskAssignmentService } from '@/services/task-assignment';\n\ninterface Officer {\n  user_id: string;\n  first_name: string;\n  last_name: string;\n  email: string;\n  department?: string;\n}\n\ninterface AssignModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  itemId: string | null;\n  itemType: 'data_breach' | 'application' | 'complaint';\n  itemTitle?: string;\n  onAssignSuccess?: () => void;\n}\n\nconst AssignModal: React.FC<AssignModalProps> = ({\n  isOpen,\n  onClose,\n  itemId,\n  itemType,\n  itemTitle,\n  onAssignSuccess\n}) => {\n  const { showSuccess, showError } = useToast();\n  const [officers, setOfficers] = useState<Officer[]>([]);\n  const [filteredOfficers, setFilteredOfficers] = useState<Officer[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [assigning, setAssigning] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedOfficer, setSelectedOfficer] = useState<string>('');\n  const [comment, setComment] = useState('');\n\n  useEffect(() => {\n    if (isOpen) {\n      fetchOfficers();\n      setSearchQuery('');\n      setSelectedOfficer('');\n      setComment('');\n    }\n  }, [isOpen]);\n\n  useEffect(() => {\n    // Filter officers based on search query\n    if (searchQuery.trim() === '') {\n      setFilteredOfficers(officers);\n    } else {\n      const filtered = officers.filter(officer =>\n        `${officer.first_name} ${officer.last_name}`.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        officer.email.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        officer.department?.toLowerCase().includes(searchQuery.toLowerCase())\n      );\n      setFilteredOfficers(filtered);\n    }\n  }, [officers, searchQuery]);\n\n  const fetchOfficers = async () => {\n    setLoading(true);\n    try {\n      const response = await taskAssignmentService.getOfficers();\n      setOfficers(response.data || []);\n    } catch (error) {\n      console.error('Error fetching officers:', error);\n      showError('Failed to load officers');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleAssign = async () => {\n    if (!selectedOfficer || !itemId) {\n      showError('Please select an officer');\n      return;\n    }\n\n    setAssigning(true);\n    try {\n      // Create a task for the assignment\n      await taskAssignmentService.assignTask(itemId, {\n        assignedTo: selectedOfficer,\n        comment: comment.trim() || undefined\n      });\n\n      showSuccess('Successfully assigned to officer');\n      onAssignSuccess?.();\n      onClose();\n    } catch (error) {\n      console.error('Error assigning task:', error);\n      showError('Failed to assign task');\n    } finally {\n      setAssigning(false);\n    }\n  };\n\n  const getSelectedOfficerDetails = () => {\n    return officers.find(officer => officer.user_id === selectedOfficer);\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n      <div className=\"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800\">\n        <div className=\"mt-3\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between mb-6\">\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n              Assign {itemType.replace('_', ' ').toUpperCase()}\n            </h3>\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            >\n              <i className=\"ri-close-line text-xl\"></i>\n            </button>\n          </div>\n\n          {/* Item Details */}\n          {itemTitle && (\n            <div className=\"mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n              <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\n                Item to Assign:\n              </h4>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">{itemTitle}</p>\n            </div>\n          )}\n\n          {/* Search Officers */}\n          <div className=\"mb-6\">\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Search Officers\n            </label>\n            <input\n              type=\"text\"\n              placeholder=\"Search by name, email, or department...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n            />\n          </div>\n\n          {/* Officers List */}\n          <div className=\"mb-6\">\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Select Officer ({filteredOfficers.length} found)\n            </label>\n            <div className=\"max-h-64 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md\">\n              {loading ? (\n                <div className=\"p-4 text-center text-gray-500 dark:text-gray-400\">\n                  Loading officers...\n                </div>\n              ) : filteredOfficers.length === 0 ? (\n                <div className=\"p-4 text-center text-gray-500 dark:text-gray-400\">\n                  No officers found\n                </div>\n              ) : (\n                filteredOfficers.map((officer) => (\n                  <div\n                    key={officer.user_id}\n                    className={`p-3 border-b border-gray-200 dark:border-gray-600 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 ${\n                      selectedOfficer === officer.user_id ? 'bg-blue-50 dark:bg-blue-900' : ''\n                    }`}\n                    onClick={() => setSelectedOfficer(officer.user_id)}\n                  >\n                    <div className=\"flex items-center\">\n                      <input\n                        type=\"radio\"\n                        name=\"officer\"\n                        value={officer.user_id}\n                        checked={selectedOfficer === officer.user_id}\n                        onChange={() => setSelectedOfficer(officer.user_id)}\n                        className=\"mr-3\"\n                      />\n                      <div className=\"flex-1\">\n                        <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\n                          {officer.first_name} {officer.last_name}\n                        </div>\n                        <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                          {officer.email}\n                        </div>\n                        {officer.department && (\n                          <div className=\"text-xs text-gray-400 dark:text-gray-500\">\n                            {officer.department}\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                ))\n              )}\n            </div>\n          </div>\n\n          {/* Comment */}\n          <div className=\"mb-6\">\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Assignment Comment (Optional)\n            </label>\n            <textarea\n              value={comment}\n              onChange={(e) => setComment(e.target.value)}\n              placeholder=\"Add any notes or instructions for the assigned officer...\"\n              rows={3}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n            />\n          </div>\n\n          {/* Selected Officer Summary */}\n          {selectedOfficer && (\n            <div className=\"mb-6 p-4 bg-green-50 dark:bg-green-900 rounded-lg\">\n              <h4 className=\"text-sm font-medium text-green-900 dark:text-green-100 mb-2\">\n                Selected Officer:\n              </h4>\n              <div className=\"text-sm text-green-700 dark:text-green-200\">\n                {getSelectedOfficerDetails()?.first_name} {getSelectedOfficerDetails()?.last_name}\n                <br />\n                {getSelectedOfficerDetails()?.email}\n                {getSelectedOfficerDetails()?.department && (\n                  <>\n                    <br />\n                    Department: {getSelectedOfficerDetails()?.department}\n                  </>\n                )}\n              </div>\n            </div>\n          )}\n\n          {/* Actions */}\n          <div className=\"flex justify-end space-x-3\">\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"button\"\n              onClick={handleAssign}\n              disabled={!selectedOfficer || assigning}\n              className=\"px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {assigning ? 'Assigning...' : 'Assign'}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AssignModal;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAuBA,MAAM,cAA0C,CAAC,EAC/C,MAAM,EACN,OAAO,EACP,MAAM,EACN,QAAQ,EACR,SAAS,EACT,eAAe,EAChB;IACC,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAC1C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV;YACA,eAAe;YACf,mBAAmB;YACnB,WAAW;QACb;IACF,GAAG;QAAC;KAAO;IAEX,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAwC;QACxC,IAAI,YAAY,IAAI,OAAO,IAAI;YAC7B,oBAAoB;QACtB,OAAO;YACL,MAAM,WAAW,SAAS,MAAM,CAAC,CAAA,UAC/B,GAAG,QAAQ,UAAU,CAAC,CAAC,EAAE,QAAQ,SAAS,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC3F,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC5D,QAAQ,UAAU,EAAE,cAAc,SAAS,YAAY,WAAW;YAEpE,oBAAoB;QACtB;IACF,GAAG;QAAC;QAAU;KAAY;IAE1B,MAAM,gBAAgB;QACpB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,qIAAA,CAAA,wBAAqB,CAAC,WAAW;YACxD,YAAY,SAAS,IAAI,IAAI,EAAE;QACjC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,UAAU;QACZ,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,mBAAmB,CAAC,QAAQ;YAC/B,UAAU;YACV;QACF;QAEA,aAAa;QACb,IAAI;YACF,mCAAmC;YACnC,MAAM,qIAAA,CAAA,wBAAqB,CAAC,UAAU,CAAC,QAAQ;gBAC7C,YAAY;gBACZ,SAAS,QAAQ,IAAI,MAAM;YAC7B;YAEA,YAAY;YACZ;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,UAAU;QACZ,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,4BAA4B;QAChC,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,OAAO,KAAK;IACtD;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAuD;oCAC3D,SAAS,OAAO,CAAC,KAAK,KAAK,WAAW;;;;;;;0CAEhD,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC;oCAAE,WAAU;;;;;;;;;;;;;;;;;oBAKhB,2BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA4D;;;;;;0CAG1E,8OAAC;gCAAE,WAAU;0CAA4C;;;;;;;;;;;;kCAK7D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,WAAU;;;;;;;;;;;;kCAKd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,WAAU;;oCAAkE;oCAChE,iBAAiB,MAAM;oCAAC;;;;;;;0CAE3C,8OAAC;gCAAI,WAAU;0CACZ,wBACC,8OAAC;oCAAI,WAAU;8CAAmD;;;;;2CAGhE,iBAAiB,MAAM,KAAK,kBAC9B,8OAAC;oCAAI,WAAU;8CAAmD;;;;;2CAIlE,iBAAiB,GAAG,CAAC,CAAC,wBACpB,8OAAC;wCAEC,WAAW,CAAC,yGAAyG,EACnH,oBAAoB,QAAQ,OAAO,GAAG,gCAAgC,IACtE;wCACF,SAAS,IAAM,mBAAmB,QAAQ,OAAO;kDAEjD,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAO,QAAQ,OAAO;oDACtB,SAAS,oBAAoB,QAAQ,OAAO;oDAC5C,UAAU,IAAM,mBAAmB,QAAQ,OAAO;oDAClD,WAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;gEACZ,QAAQ,UAAU;gEAAC;gEAAE,QAAQ,SAAS;;;;;;;sEAEzC,8OAAC;4DAAI,WAAU;sEACZ,QAAQ,KAAK;;;;;;wDAEf,QAAQ,UAAU,kBACjB,8OAAC;4DAAI,WAAU;sEACZ,QAAQ,UAAU;;;;;;;;;;;;;;;;;;uCAxBtB,QAAQ,OAAO;;;;;;;;;;;;;;;;kCAoC9B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,8OAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;gCAC1C,aAAY;gCACZ,MAAM;gCACN,WAAU;;;;;;;;;;;;oBAKb,iCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA8D;;;;;;0CAG5E,8OAAC;gCAAI,WAAU;;oCACZ,6BAA6B;oCAAW;oCAAE,6BAA6B;kDACxE,8OAAC;;;;;oCACA,6BAA6B;oCAC7B,6BAA6B,4BAC5B;;0DACE,8OAAC;;;;;4CAAK;4CACO,6BAA6B;;;;;;;;;;;;;;;kCAQpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,UAAU,CAAC,mBAAmB;gCAC9B,WAAU;0CAET,YAAY,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5C;uCAEe", "debugId": null}}, {"offset": {"line": 790, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/common/AssignButton.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport AssignModal from './AssignModal';\n\ninterface AssignButtonProps {\n  itemId: string;\n  itemType: 'data_breach' | 'application' | 'complaint';\n  itemTitle?: string;\n  onAssignSuccess?: () => void;\n  className?: string;\n  size?: 'sm' | 'md' | 'lg';\n  variant?: 'primary' | 'secondary' | 'success';\n  disabled?: boolean;\n  children?: React.ReactNode;\n}\n\nconst AssignButton: React.FC<AssignButtonProps> = ({\n  itemId,\n  itemType,\n  itemTitle,\n  onAssignSuccess,\n  className = '',\n  size = 'sm',\n  variant = 'success',\n  disabled = false,\n  children\n}) => {\n  const [showAssignModal, setShowAssignModal] = useState(false);\n\n  const handleAssignClick = () => {\n    setShowAssignModal(true);\n  };\n\n  const handleCloseModal = () => {\n    setShowAssignModal(false);\n  };\n\n  const handleAssignSuccess = () => {\n    setShowAssignModal(false);\n    onAssignSuccess?.();\n  };\n\n  // Size classes\n  const sizeClasses = {\n    sm: 'px-3 py-1 text-xs',\n    md: 'px-4 py-2 text-sm',\n    lg: 'px-6 py-3 text-base'\n  };\n\n  // Variant classes\n  const variantClasses = {\n    primary: 'text-white bg-primary hover:bg-primary-dark focus:ring-primary',\n    secondary: 'text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 focus:ring-gray-500',\n    success: 'text-white bg-green-600 hover:bg-green-700 focus:ring-green-500'\n  };\n\n  // Base classes\n  const baseClasses = 'inline-flex items-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed';\n\n  // Combine all classes\n  const buttonClasses = `${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]} ${className}`;\n\n  return (\n    <>\n      <button\n        type=\"button\"\n        onClick={handleAssignClick}\n        disabled={disabled}\n        className={buttonClasses}\n        title={`Assign ${itemType.replace('_', ' ')} to officer`}\n      >\n        <i className=\"ri-user-add-line mr-1\"></i>\n        {children || 'Assign'}\n      </button>\n\n      <AssignModal\n        isOpen={showAssignModal}\n        onClose={handleCloseModal}\n        itemId={itemId}\n        itemType={itemType}\n        itemTitle={itemTitle}\n        onAssignSuccess={handleAssignSuccess}\n      />\n    </>\n  );\n};\n\nexport default AssignButton;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAiBA,MAAM,eAA4C,CAAC,EACjD,MAAM,EACN,QAAQ,EACR,SAAS,EACT,eAAe,EACf,YAAY,EAAE,EACd,OAAO,IAAI,EACX,UAAU,SAAS,EACnB,WAAW,KAAK,EAChB,QAAQ,EACT;IACC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,oBAAoB;QACxB,mBAAmB;IACrB;IAEA,MAAM,mBAAmB;QACvB,mBAAmB;IACrB;IAEA,MAAM,sBAAsB;QAC1B,mBAAmB;QACnB;IACF;IAEA,eAAe;IACf,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,kBAAkB;IAClB,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,SAAS;IACX;IAEA,eAAe;IACf,MAAM,cAAc;IAEpB,sBAAsB;IACtB,MAAM,gBAAgB,GAAG,YAAY,CAAC,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC,EAAE,WAAW;IAEnG,qBACE;;0BACE,8OAAC;gBACC,MAAK;gBACL,SAAS;gBACT,UAAU;gBACV,WAAW;gBACX,OAAO,CAAC,OAAO,EAAE,SAAS,OAAO,CAAC,KAAK,KAAK,WAAW,CAAC;;kCAExD,8OAAC;wBAAE,WAAU;;;;;;oBACZ,YAAY;;;;;;;0BAGf,8OAAC,2IAAA,CAAA,UAAW;gBACV,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,UAAU;gBACV,WAAW;gBACX,iBAAiB;;;;;;;;AAIzB;uCAEe", "debugId": null}}, {"offset": {"line": 873, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/consumer-affairs/ConsumerAffairsViewModal.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { consumerAffairsService, ConsumerAffairsComplaint } from '@/services/consumer-affairs';\nimport { useToast } from '@/contexts/ToastContext';\nimport Loader from '@/components/Loader';\n\ninterface ConsumerAffairsViewModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  complaintId: string | null;\n  onUpdate?: () => void;\n}\n\nconst ConsumerAffairsViewModal: React.FC<ConsumerAffairsViewModalProps> = ({\n  isOpen,\n  onClose,\n  complaintId,\n  onUpdate\n}) => {\n  const { showSuccess, showError } = useToast();\n  const [complaint, setComplaint] = useState<ConsumerAffairsComplaint | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    if (isOpen && complaintId) {\n      fetchComplaintDetails();\n    }\n  }, [isOpen, complaintId]);\n\n  const fetchComplaintDetails = async () => {\n    if (!complaintId) return;\n    \n    setLoading(true);\n    setError(null);\n    \n    try {\n      const response = await consumerAffairsService.getComplaintById(complaintId);\n      setComplaint(response);\n    } catch (err: unknown) {\n      console.error('Error fetching complaint details:', err);\n      const errorMessage = err instanceof Error ? err.message : 'Unknown error';\n      setError(`Failed to load complaint details: ${errorMessage}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status?.toLowerCase()) {\n      case 'submitted': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';\n      case 'under_review': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\n      case 'investigating': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\n      case 'resolved': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\n      case 'closed': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n    }\n  };\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority?.toLowerCase()) {\n      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\n      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\n      case 'high': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\n      case 'urgent': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n      <div className=\"relative top-20 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white dark:bg-gray-800\">\n        <div className=\"mt-3\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between mb-6\">\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n              Consumer Affairs Complaint Details\n            </h3>\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            >\n              <i className=\"ri-close-line text-xl\"></i>\n            </button>\n          </div>\n\n          {/* Content */}\n          {loading ? (\n            <div className=\"py-8\">\n              <Loader message=\"Loading complaint details...\" />\n            </div>\n          ) : error ? (\n            <div className=\"bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4\">\n              <div className=\"flex\">\n                <i className=\"ri-error-warning-line text-red-400 mr-2\"></i>\n                <p className=\"text-red-700 dark:text-red-200\">{error}</p>\n              </div>\n            </div>\n          ) : complaint ? (\n            <div className=\"space-y-6\">\n              {/* Basic Information */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\n                    Complaint Number\n                  </h4>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    {complaint.complaint_number}\n                  </p>\n                </div>\n                <div>\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\n                    Status\n                  </h4>\n                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(complaint.status)}`}>\n                    {complaint.status?.replace('_', ' ').toUpperCase()}\n                  </span>\n                </div>\n                <div>\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\n                    Category\n                  </h4>\n                  <span className=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\">\n                    {complaint.category}\n                  </span>\n                </div>\n                <div>\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\n                    Priority\n                  </h4>\n                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(complaint.priority)}`}>\n                    {complaint.priority?.toUpperCase() || 'MEDIUM'}\n                  </span>\n                </div>\n              </div>\n\n              {/* Title and Description */}\n              <div>\n                <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\n                  Title\n                </h4>\n                <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                  {complaint.title}\n                </p>\n              </div>\n\n              <div>\n                <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\n                  Description\n                </h4>\n                <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400 whitespace-pre-wrap\">\n                    {complaint.description}\n                  </p>\n                </div>\n              </div>\n\n              {/* Complainant Information */}\n              {complaint.complainant && (\n                <div>\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\n                    Complainant\n                  </h4>\n                  <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                      <strong>Name:</strong> {complaint.complainant.first_name} {complaint.complainant.last_name}\n                    </p>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                      <strong>Email:</strong> {complaint.complainant.email}\n                    </p>\n                  </div>\n                </div>\n              )}\n\n              {/* Assignment Information */}\n              {complaint.assignee && (\n                <div>\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\n                    Assigned To\n                  </h4>\n                  <div className=\"bg-green-50 dark:bg-green-900 rounded-lg p-4\">\n                    <p className=\"text-sm text-green-700 dark:text-green-200\">\n                      <strong>Officer:</strong> {complaint.assignee.first_name} {complaint.assignee.last_name}\n                    </p>\n                    <p className=\"text-sm text-green-700 dark:text-green-200\">\n                      <strong>Email:</strong> {complaint.assignee.email}\n                    </p>\n                  </div>\n                </div>\n              )}\n\n              {/* Resolution */}\n              {complaint.resolution && (\n                <div>\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\n                    Resolution\n                  </h4>\n                  <div className=\"bg-green-50 dark:bg-green-900 rounded-lg p-4\">\n                    <p className=\"text-sm text-green-700 dark:text-green-200 whitespace-pre-wrap\">\n                      {complaint.resolution}\n                    </p>\n                  </div>\n                </div>\n              )}\n\n              {/* Internal Notes */}\n              {complaint.internal_notes && (\n                <div>\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\n                    Internal Notes\n                  </h4>\n                  <div className=\"bg-yellow-50 dark:bg-yellow-900 rounded-lg p-4\">\n                    <p className=\"text-sm text-yellow-700 dark:text-yellow-200 whitespace-pre-wrap\">\n                      {complaint.internal_notes}\n                    </p>\n                  </div>\n                </div>\n              )}\n\n              {/* Timestamps */}\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 pt-4 border-t border-gray-200 dark:border-gray-600\">\n                <div>\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\n                    Submitted\n                  </h4>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    {new Date(complaint.created_at).toLocaleString()}\n                  </p>\n                </div>\n                <div>\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\n                    Last Updated\n                  </h4>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    {new Date(complaint.updated_at).toLocaleString()}\n                  </p>\n                </div>\n                {complaint.resolved_at && (\n                  <div>\n                    <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\n                      Resolved\n                    </h4>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                      {new Date(complaint.resolved_at).toLocaleString()}\n                    </p>\n                  </div>\n                )}\n              </div>\n            </div>\n          ) : (\n            <div className=\"text-center py-8\">\n              <p className=\"text-gray-500 dark:text-gray-400\">No complaint data available</p>\n            </div>\n          )}\n\n          {/* Actions */}\n          <div className=\"flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-600\">\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\n            >\n              Close\n            </button>\n            {complaint && (\n              <button\n                type=\"button\"\n                onClick={() => {\n                  // For now, just show a message. In the future, this could navigate to an evaluation page\n                  showSuccess('Complaint evaluation feature coming soon');\n                }}\n                className=\"px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\n              >\n                <i className=\"ri-clipboard-line mr-2\"></i>\n                Evaluate\n              </button>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ConsumerAffairsViewModal;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AALA;;;;;;AAcA,MAAM,2BAAoE,CAAC,EACzE,MAAM,EACN,OAAO,EACP,WAAW,EACX,QAAQ,EACT;IACC,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAC1C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmC;IAC5E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,aAAa;YACzB;QACF;IACF,GAAG;QAAC;QAAQ;KAAY;IAExB,MAAM,wBAAwB;QAC5B,IAAI,CAAC,aAAa;QAElB,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,gKAAA,CAAA,yBAAsB,CAAC,gBAAgB,CAAC;YAC/D,aAAa;QACf,EAAE,OAAO,KAAc;YACrB,QAAQ,KAAK,CAAC,qCAAqC;YACnD,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS,CAAC,kCAAkC,EAAE,cAAc;QAC9D,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ,QAAQ;YACd,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAiB,OAAO;YAC7B,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ,UAAU;YAChB,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAuD;;;;;;0CAGrE,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC;oCAAE,WAAU;;;;;;;;;;;;;;;;;oBAKhB,wBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4HAAA,CAAA,UAAM;4BAAC,SAAQ;;;;;;;;;;+BAEhB,sBACF,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;;;;;8CACb,8OAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;;;;;+BAGjD,0BACF,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,8OAAC;gDAAE,WAAU;0DACV,UAAU,gBAAgB;;;;;;;;;;;;kDAG/B,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,8OAAC;gDAAK,WAAW,CAAC,yDAAyD,EAAE,eAAe,UAAU,MAAM,GAAG;0DAC5G,UAAU,MAAM,EAAE,QAAQ,KAAK,KAAK;;;;;;;;;;;;kDAGzC,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,8OAAC;gDAAK,WAAU;0DACb,UAAU,QAAQ;;;;;;;;;;;;kDAGvB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,8OAAC;gDAAK,WAAW,CAAC,yDAAyD,EAAE,iBAAiB,UAAU,QAAQ,GAAG;0DAChH,UAAU,QAAQ,EAAE,iBAAiB;;;;;;;;;;;;;;;;;;0CAM5C,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,8OAAC;wCAAE,WAAU;kDACV,UAAU,KAAK;;;;;;;;;;;;0CAIpB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDACV,UAAU,WAAW;;;;;;;;;;;;;;;;;4BAM3B,UAAU,WAAW,kBACpB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;;kEACX,8OAAC;kEAAO;;;;;;oDAAc;oDAAE,UAAU,WAAW,CAAC,UAAU;oDAAC;oDAAE,UAAU,WAAW,CAAC,SAAS;;;;;;;0DAE5F,8OAAC;gDAAE,WAAU;;kEACX,8OAAC;kEAAO;;;;;;oDAAe;oDAAE,UAAU,WAAW,CAAC,KAAK;;;;;;;;;;;;;;;;;;;4BAO3D,UAAU,QAAQ,kBACjB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;;kEACX,8OAAC;kEAAO;;;;;;oDAAiB;oDAAE,UAAU,QAAQ,CAAC,UAAU;oDAAC;oDAAE,UAAU,QAAQ,CAAC,SAAS;;;;;;;0DAEzF,8OAAC;gDAAE,WAAU;;kEACX,8OAAC;kEAAO;;;;;;oDAAe;oDAAE,UAAU,QAAQ,CAAC,KAAK;;;;;;;;;;;;;;;;;;;4BAOxD,UAAU,UAAU,kBACnB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDACV,UAAU,UAAU;;;;;;;;;;;;;;;;;4BAO5B,UAAU,cAAc,kBACvB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDACV,UAAU,cAAc;;;;;;;;;;;;;;;;;0CAOjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,8OAAC;gDAAE,WAAU;0DACV,IAAI,KAAK,UAAU,UAAU,EAAE,cAAc;;;;;;;;;;;;kDAGlD,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,8OAAC;gDAAE,WAAU;0DACV,IAAI,KAAK,UAAU,UAAU,EAAE,cAAc;;;;;;;;;;;;oCAGjD,UAAU,WAAW,kBACpB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,8OAAC;gDAAE,WAAU;0DACV,IAAI,KAAK,UAAU,WAAW,EAAE,cAAc;;;;;;;;;;;;;;;;;;;;;;;6CAOzD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;kCAKpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CACX;;;;;;4BAGA,2BACC,8OAAC;gCACC,MAAK;gCACL,SAAS;oCACP,yFAAyF;oCACzF,YAAY;gCACd;gCACA,WAAU;;kDAEV,8OAAC;wCAAE,WAAU;;;;;;oCAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS1D;uCAEe", "debugId": null}}, {"offset": {"line": 1547, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/app/consumer-affairs/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { consumerAffairsService, ConsumerAffairsComplaint } from '@/services/consumer-affairs';\nimport Loader from '@/components/Loader';\nimport AssignButton from '@/components/common/AssignButton';\nimport ConsumerAffairsViewModal from '@/components/consumer-affairs/ConsumerAffairsViewModal';\n\nconst ConsumerAffairsPage: React.FC = () => {\n  const { isAuthenticated } = useAuth();\n  const [complaints, setComplaints] = useState<ConsumerAffairsComplaint[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string>('');\n  const [selectedComplaintId, setSelectedComplaintId] = useState<string | null>(null);\n  const [showViewModal, setShowViewModal] = useState(false);\n  const [filter, setFilter] = useState({\n    status: '',\n    category: '',\n    priority: '',\n    search: ''\n  });\n\n  const fetchComplaints = useCallback(async () => {\n    if (!isAuthenticated) return;\n\n    try {\n      setLoading(true);\n      console.log('🔄 Fetching all consumer affairs complaints for staff...');\n\n      // For staff, fetch all complaints (not filtered by user)\n      const response = await consumerAffairsService.getComplaints({\n        limit: 100,\n        ...filter\n      });\n\n      console.log('✅ Consumer Affairs complaints fetched:', response);\n\n      if (Array.isArray(response.data)) {\n        setComplaints(response.data);\n      } else {\n        setComplaints([]);\n      }\n    } catch (err: unknown) {\n      console.error('❌ Error fetching complaints:', err);\n      if (err instanceof Error) {\n        setError(`Failed to load complaints: ${err.message}`);\n      } else {\n        setError('Failed to load complaints: Unknown error');\n      }\n    } finally {\n      setLoading(false);\n    }\n  }, [isAuthenticated, filter]);\n\n  // Fetch complaints\n  useEffect(() => {\n    fetchComplaints();\n  }, [fetchComplaints]);\n\n  const handleViewComplaint = (complaintId: string) => {\n    setSelectedComplaintId(complaintId);\n    setShowViewModal(true);\n  };\n\n  const handleCloseViewModal = () => {\n    setShowViewModal(false);\n    setSelectedComplaintId(null);\n  };\n\n  const handleAssignSuccess = () => {\n    // Refresh the complaints list when assignment is successful\n    fetchComplaints();\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status?.toLowerCase()) {\n      case 'submitted': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';\n      case 'under_review': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\n      case 'investigating': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\n      case 'resolved': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\n      case 'closed': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n    }\n  };\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority?.toLowerCase()) {\n      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\n      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\n      case 'high': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\n      case 'urgent': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"p-6 min-h-full bg-gray-50 dark:bg-gray-900\">\n        <Loader message=\"Loading consumer affairs complaints...\" />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"p-6 min-h-full bg-gray-50 dark:bg-gray-900\">\n      {/* Header */}\n      <div className=\"mb-6\">\n        <h1 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">\n          Consumer Affairs Management\n        </h1>\n        <p className=\"text-gray-600 dark:text-gray-400 mt-2\">\n          Manage and respond to customer complaints and service issues\n        </p>\n      </div>\n\n      {/* Statistics Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <i className=\"ri-file-list-line text-2xl text-blue-600\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Total Complaints</p>\n              <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">{complaints.length}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <i className=\"ri-time-line text-2xl text-yellow-600\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Submitted</p>\n              <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">\n                {complaints.filter(c => c.status === 'submitted').length}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <i className=\"ri-progress-line text-2xl text-blue-600\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Under Review</p>\n              <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">\n                {complaints.filter(c => c.status === 'under_review').length}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <i className=\"ri-check-line text-2xl text-green-600\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Resolved</p>\n              <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">\n                {complaints.filter(c => c.status === 'resolved').length}\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Filters */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Search\n            </label>\n            <input\n              type=\"text\"\n              placeholder=\"Search complaints...\"\n              value={filter.search}\n              onChange={(e) => setFilter(prev => ({ ...prev, search: e.target.value }))}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-gray-100\"\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Status\n            </label>\n            <select\n              value={filter.status}\n              onChange={(e) => setFilter(prev => ({ ...prev, status: e.target.value }))}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-gray-100\"\n              aria-label=\"Filter by status\"\n              title=\"Filter complaints by status\"\n            >\n              <option value=\"\">All Statuses</option>\n              <option value=\"submitted\">Submitted</option>\n              <option value=\"under_review\">Under Review</option>\n              <option value=\"investigating\">Investigating</option>\n              <option value=\"resolved\">Resolved</option>\n              <option value=\"closed\">Closed</option>\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Category\n            </label>\n            <select\n              value={filter.category}\n              onChange={(e) => setFilter(prev => ({ ...prev, category: e.target.value }))}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-gray-100\"\n              aria-label=\"Filter by category\"\n              title=\"Filter complaints by category\"\n            >\n              <option value=\"\">All Categories</option>\n              <option value=\"Billing & Charges\">Billing & Charges</option>\n              <option value=\"Service Quality\">Service Quality</option>\n              <option value=\"Network Issues\">Network Issues</option>\n              <option value=\"Customer Service\">Customer Service</option>\n              <option value=\"Other\">Other</option>\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Priority\n            </label>\n            <select\n              value={filter.priority}\n              onChange={(e) => setFilter(prev => ({ ...prev, priority: e.target.value }))}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-gray-100\"\n              aria-label=\"Filter by priority\"\n              title=\"Filter complaints by priority\"\n            >\n              <option value=\"\">All Priorities</option>\n              <option value=\"low\">Low</option>\n              <option value=\"medium\">Medium</option>\n              <option value=\"high\">High</option>\n              <option value=\"urgent\">Urgent</option>\n            </select>\n          </div>\n        </div>\n      </div>\n\n      {/* Error Message */}\n      {error && (\n        <div className=\"bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4 mb-6\">\n          <div className=\"flex\">\n            <i className=\"ri-error-warning-line text-red-400 mr-2\"></i>\n            <p className=\"text-red-700 dark:text-red-200\">{error}</p>\n          </div>\n        </div>\n      )}\n\n      {/* Complaints Table */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden\">\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n            Consumer Affairs Complaints ({complaints.length})\n          </h3>\n        </div>\n\n        {complaints.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <i className=\"ri-file-search-line text-4xl text-gray-400 mb-4\"></i>\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\">No complaints found</h3>\n            <p className=\"text-gray-500 dark:text-gray-400\">\n              No consumer affairs complaints have been submitted yet.\n            </p>\n          </div>\n        ) : (\n          <div className=\"overflow-x-auto\">\n            <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n              <thead className=\"bg-gray-50 dark:bg-gray-900\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    Complaint\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    Category\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    Status\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    Priority\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    Submitted\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    Actions\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n                {complaints.map((complaint) => (\n                  <tr key={complaint.complaint_id} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div>\n                        <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\n                          {complaint.title}\n                        </div>\n                        <div className=\"text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs\">\n                          {complaint.description}\n                        </div>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\">\n                        {complaint.category}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(complaint.status)}`}>\n                        {complaint.status?.replace('_', ' ').toUpperCase()}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(complaint.priority)}`}>\n                        {complaint.priority?.toUpperCase() || 'MEDIUM'}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\n                      {new Date(complaint.created_at).toLocaleDateString()}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                      <div className=\"flex items-center space-x-2\">\n                        <button\n                          type=\"button\"\n                          onClick={() => handleViewComplaint(complaint.complaint_id)}\n                          className=\"inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\"\n                          title=\"View complaint details\"\n                        >\n                          <i className=\"ri-eye-line mr-1\"></i>\n                          View\n                        </button>\n                        <AssignButton\n                          itemId={complaint.complaint_id}\n                          itemType=\"complaint\"\n                          itemTitle={complaint.title}\n                          onAssignSuccess={handleAssignSuccess}\n                        />\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        )}\n      </div>\n\n      {/* View Modal */}\n      <ConsumerAffairsViewModal\n        isOpen={showViewModal}\n        onClose={handleCloseViewModal}\n        complaintId={selectedComplaintId}\n      />\n    </div>\n  );\n};\n\nexport default ConsumerAffairsPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AAPA;;;;;;;;AASA,MAAM,sBAAgC;IACpC,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAClC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8B,EAAE;IAC3E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACnC,QAAQ;QACR,UAAU;QACV,UAAU;QACV,QAAQ;IACV;IAEA,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,IAAI,CAAC,iBAAiB;QAEtB,IAAI;YACF,WAAW;YACX,QAAQ,GAAG,CAAC;YAEZ,yDAAyD;YACzD,MAAM,WAAW,MAAM,gKAAA,CAAA,yBAAsB,CAAC,aAAa,CAAC;gBAC1D,OAAO;gBACP,GAAG,MAAM;YACX;YAEA,QAAQ,GAAG,CAAC,0CAA0C;YAEtD,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBAChC,cAAc,SAAS,IAAI;YAC7B,OAAO;gBACL,cAAc,EAAE;YAClB;QACF,EAAE,OAAO,KAAc;YACrB,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,IAAI,eAAe,OAAO;gBACxB,SAAS,CAAC,2BAA2B,EAAE,IAAI,OAAO,EAAE;YACtD,OAAO;gBACL,SAAS;YACX;QACF,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;QAAiB;KAAO;IAE5B,mBAAmB;IACnB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAgB;IAEpB,MAAM,sBAAsB,CAAC;QAC3B,uBAAuB;QACvB,iBAAiB;IACnB;IAEA,MAAM,uBAAuB;QAC3B,iBAAiB;QACjB,uBAAuB;IACzB;IAEA,MAAM,sBAAsB;QAC1B,4DAA4D;QAC5D;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ,QAAQ;YACd,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAiB,OAAO;YAC7B,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ,UAAU;YAChB,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,4HAAA,CAAA,UAAM;gBAAC,SAAQ;;;;;;;;;;;IAGtB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsD;;;;;;kCAGpE,8OAAC;wBAAE,WAAU;kCAAwC;;;;;;;;;;;;0BAMvD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;;;;;;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAuD;;;;;;sDACpE,8OAAC;4CAAE,WAAU;sDAA2D,WAAW,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAK/F,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;;;;;;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAuD;;;;;;sDACpE,8OAAC;4CAAE,WAAU;sDACV,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAMhE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;;;;;;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAuD;;;;;;sDACpE,8OAAC;4CAAE,WAAU;sDACV,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,gBAAgB,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAMnE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;;;;;;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAuD;;;;;;sDACpE,8OAAC;4CAAE,WAAU;sDACV,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,YAAY,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQjE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,8OAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO,OAAO,MAAM;oCACpB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCACvE,WAAU;;;;;;;;;;;;sCAId,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,8OAAC;oCACC,OAAO,OAAO,MAAM;oCACpB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCACvE,WAAU;oCACV,cAAW;oCACX,OAAM;;sDAEN,8OAAC;4CAAO,OAAM;sDAAG;;;;;;sDACjB,8OAAC;4CAAO,OAAM;sDAAY;;;;;;sDAC1B,8OAAC;4CAAO,OAAM;sDAAe;;;;;;sDAC7B,8OAAC;4CAAO,OAAM;sDAAgB;;;;;;sDAC9B,8OAAC;4CAAO,OAAM;sDAAW;;;;;;sDACzB,8OAAC;4CAAO,OAAM;sDAAS;;;;;;;;;;;;;;;;;;sCAI3B,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,8OAAC;oCACC,OAAO,OAAO,QAAQ;oCACtB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCACzE,WAAU;oCACV,cAAW;oCACX,OAAM;;sDAEN,8OAAC;4CAAO,OAAM;sDAAG;;;;;;sDACjB,8OAAC;4CAAO,OAAM;sDAAoB;;;;;;sDAClC,8OAAC;4CAAO,OAAM;sDAAkB;;;;;;sDAChC,8OAAC;4CAAO,OAAM;sDAAiB;;;;;;sDAC/B,8OAAC;4CAAO,OAAM;sDAAmB;;;;;;sDACjC,8OAAC;4CAAO,OAAM;sDAAQ;;;;;;;;;;;;;;;;;;sCAI1B,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,8OAAC;oCACC,OAAO,OAAO,QAAQ;oCACtB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCACzE,WAAU;oCACV,cAAW;oCACX,OAAM;;sDAEN,8OAAC;4CAAO,OAAM;sDAAG;;;;;;sDACjB,8OAAC;4CAAO,OAAM;sDAAM;;;;;;sDACpB,8OAAC;4CAAO,OAAM;sDAAS;;;;;;sDACvB,8OAAC;4CAAO,OAAM;sDAAO;;;;;;sDACrB,8OAAC;4CAAO,OAAM;sDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAO9B,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;;;;;sCACb,8OAAC;4BAAE,WAAU;sCAAkC;;;;;;;;;;;;;;;;;0BAMrD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;;gCAAuD;gCACrC,WAAW,MAAM;gCAAC;;;;;;;;;;;;oBAInD,WAAW,MAAM,KAAK,kBACrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;;;;;0CACb,8OAAC;gCAAG,WAAU;0CAA4D;;;;;;0CAC1E,8OAAC;gCAAE,WAAU;0CAAmC;;;;;;;;;;;6CAKlD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCAAM,WAAU;8CACf,cAAA,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,8OAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,8OAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,8OAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,8OAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,8OAAC;gDAAG,WAAU;0DAAoG;;;;;;;;;;;;;;;;;8CAKtH,8OAAC;oCAAM,WAAU;8CACd,WAAW,GAAG,CAAC,CAAC,0BACf,8OAAC;4CAAgC,WAAU;;8DACzC,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;;0EACC,8OAAC;gEAAI,WAAU;0EACZ,UAAU,KAAK;;;;;;0EAElB,8OAAC;gEAAI,WAAU;0EACZ,UAAU,WAAW;;;;;;;;;;;;;;;;;8DAI5B,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAK,WAAU;kEACb,UAAU,QAAQ;;;;;;;;;;;8DAGvB,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAK,WAAW,CAAC,yDAAyD,EAAE,eAAe,UAAU,MAAM,GAAG;kEAC5G,UAAU,MAAM,EAAE,QAAQ,KAAK,KAAK;;;;;;;;;;;8DAGzC,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAK,WAAW,CAAC,yDAAyD,EAAE,iBAAiB,UAAU,QAAQ,GAAG;kEAChH,UAAU,QAAQ,EAAE,iBAAiB;;;;;;;;;;;8DAG1C,8OAAC;oDAAG,WAAU;8DACX,IAAI,KAAK,UAAU,UAAU,EAAE,kBAAkB;;;;;;8DAEpD,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,MAAK;gEACL,SAAS,IAAM,oBAAoB,UAAU,YAAY;gEACzD,WAAU;gEACV,OAAM;;kFAEN,8OAAC;wEAAE,WAAU;;;;;;oEAAuB;;;;;;;0EAGtC,8OAAC,4IAAA,CAAA,UAAY;gEACX,QAAQ,UAAU,YAAY;gEAC9B,UAAS;gEACT,WAAW,UAAU,KAAK;gEAC1B,iBAAiB;;;;;;;;;;;;;;;;;;2CA5ChB,UAAU,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAyD3C,8OAAC,qKAAA,CAAA,UAAwB;gBACvB,QAAQ;gBACR,SAAS;gBACT,aAAa;;;;;;;;;;;;AAIrB;uCAEe", "debugId": null}}]}