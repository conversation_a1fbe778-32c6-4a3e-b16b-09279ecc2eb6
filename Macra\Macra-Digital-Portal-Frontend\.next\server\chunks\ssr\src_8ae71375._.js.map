{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/LogoutButton.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { useRouter, usePathname } from 'next/navigation';\r\nimport { useAuth } from '../contexts/AuthContext';\r\n\r\ninterface LogoutButtonProps {\r\n  variant?: 'primary' | 'secondary' | 'text' | 'icon';\r\n  size?: 'sm' | 'md' | 'lg';\r\n  className?: string;\r\n  showConfirmation?: boolean;\r\n  redirectTo?: string;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nexport default function LogoutButton({\r\n  variant = 'primary',\r\n  size = 'md',\r\n  className = '',\r\n  showConfirmation = true,\r\n  redirectTo = '/auth/login',\r\n  children,\r\n}: LogoutButtonProps) {\r\n  const [isLoggingOut, setIsLoggingOut] = useState(false);\r\n  const [showConfirm, setShowConfirm] = useState(false);\r\n  const { logout, user } = useAuth();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  const handleLogout = async () => {\r\n    if (showConfirmation && !showConfirm) {\r\n      setShowConfirm(true);\r\n      return;\r\n    }\r\n\r\n    setIsLoggingOut(true);\r\n    \r\n    try {\r\n      // Call logout from context\r\n      logout();\r\n      \r\n      // Small delay to ensure cleanup is complete\r\n      await new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n      if(pathname.includes('customer')){\r\n        // Redirect to specified page\r\n        router.push('/customer/auth/login');\r\n      } else {\r\n        router.push('/auth/login');\r\n      }\r\n      \r\n\r\n      \r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n    } finally {\r\n      setIsLoggingOut(false);\r\n      setShowConfirm(false);\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    setShowConfirm(false);\r\n  };\r\n\r\n  // Base styles for different variants\r\n  const getVariantStyles = () => {\r\n    switch (variant) {\r\n      case 'primary':\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n      case 'secondary':\r\n        return 'bg-white hover:bg-gray-50 text-red-600 border border-red-600';\r\n      case 'text':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none';\r\n      case 'icon':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none p-2';\r\n      default:\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n    }\r\n  };\r\n\r\n  // Size styles\r\n  const getSizeStyles = () => {\r\n    switch (size) {\r\n      case 'sm':\r\n        return 'px-3 py-1.5 text-sm';\r\n      case 'md':\r\n        return 'px-4 py-2 text-base';\r\n      case 'lg':\r\n        return 'px-6 py-3 text-lg';\r\n      default:\r\n        return 'px-4 py-2 text-base';\r\n    }\r\n  };\r\n\r\n  const baseStyles = 'inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\r\n  \r\n  const buttonStyles = `${baseStyles} ${getVariantStyles()} ${variant !== 'icon' ? getSizeStyles() : ''} ${className}`;\r\n\r\n  // Confirmation dialog\r\n  if (showConfirm) {\r\n    return (\r\n      <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n        <div className=\"bg-white rounded-lg p-6 max-w-sm mx-4 shadow-xl\">\r\n          <div className=\"flex items-center mb-4\">\r\n            <div className=\"flex-shrink-0\">\r\n              <svg className=\"h-6 w-6 text-red-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z\" />\r\n              </svg>\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <h3 className=\"text-lg font-medium text-gray-900\">Confirm Logout</h3>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"mb-4\">\r\n            <p className=\"text-sm text-gray-500\">\r\n              Are you sure you want to logout{user?.first_name ? `, ${user.first_name}` : ''}? You will need to login again to access your account.\r\n            </p>\r\n          </div>\r\n          \r\n          <div className=\"flex space-x-3\">\r\n            <button\r\n              onClick={handleLogout}\r\n              disabled={isLoggingOut}\r\n              className=\"flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 disabled:opacity-50\"\r\n            >\r\n              {isLoggingOut ? 'Logging out...' : 'Yes, Logout'}\r\n            </button>\r\n            <button\r\n              onClick={handleCancel}\r\n              className=\"flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200\"\r\n            >\r\n              Cancel\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <button\r\n      onClick={handleLogout}\r\n      disabled={isLoggingOut}\r\n      className={buttonStyles}\r\n      title=\"Logout\"\r\n    >\r\n      {children || (\r\n        <>\r\n          {variant === 'icon' ? (\r\n            <svg className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n            </svg>\r\n          ) : (\r\n            <>\r\n              <svg className=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n              </svg>\r\n              {isLoggingOut ? 'Logging out...' : 'Logout'}\r\n            </>\r\n          )}\r\n        </>\r\n      )}\r\n    </button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAee,SAAS,aAAa,EACnC,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,mBAAmB,IAAI,EACvB,aAAa,aAAa,EAC1B,QAAQ,EACU;IAClB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe;QACnB,IAAI,oBAAoB,CAAC,aAAa;YACpC,eAAe;YACf;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,2BAA2B;YAC3B;YAEA,4CAA4C;YAC5C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,IAAG,SAAS,QAAQ,CAAC,aAAY;gBAC/B,6BAA6B;gBAC7B,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QAIF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,gBAAgB;YAChB,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,eAAe;IACjB;IAEA,qCAAqC;IACrC,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,cAAc;IACd,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa;IAEnB,MAAM,eAAe,GAAG,WAAW,CAAC,EAAE,mBAAmB,CAAC,EAAE,YAAY,SAAS,kBAAkB,GAAG,CAAC,EAAE,WAAW;IAEpH,sBAAsB;IACtB,IAAI,aAAa;QACf,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAAuB,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CAC3E,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;;;;;;;;;;;;kCAItD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;gCAAwB;gCACH,MAAM,aAAa,CAAC,EAAE,EAAE,KAAK,UAAU,EAAE,GAAG;gCAAG;;;;;;;;;;;;kCAInF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,eAAe,mBAAmB;;;;;;0CAErC,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,UAAU;QACV,WAAW;QACX,OAAM;kBAEL,0BACC;sBACG,YAAY,uBACX,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,SAAQ;gBAAY,QAAO;0BAC9D,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;qCAGvE;;kCACE,8OAAC;wBAAI,WAAU;wBAAe,MAAK;wBAAO,SAAQ;wBAAY,QAAO;kCACnE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAEtE,eAAe,mBAAmB;;;;;;;;;AAOjD", "debugId": null}}, {"offset": {"line": 262, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/types/license.ts"], "sourcesContent": ["export interface LicenseType {\r\n  license_type_id: string;\r\n  name: string;\r\n  code?: string;\r\n  description?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface LicenseCategory {\r\n  license_category_id: string;\r\n  name: string;\r\n  description?: string;\r\n  license_type_id: string;\r\n  license_type?: LicenseType;\r\n  parent_id?: string;\r\n  parent?: LicenseCategory;\r\n  children?: LicenseCategory[];\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface Applicant {\r\n  applicant_id: string;\r\n  name: string;\r\n  business_registration_number: string;\r\n  tpin: string;\r\n  website: string;\r\n  email: string;\r\n  phone: string;\r\n  fax?: string;\r\n  level_of_insurance_cover?: string;\r\n  address_id?: string;\r\n  contact_id?: string;\r\n  date_incorporation: string;\r\n  place_incorporation: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface Application {\r\n  application_id: string;\r\n  application_number: string;\r\n  applicant_id: string;\r\n  license_category_id: string;\r\n  status: ApplicationStatus;\r\n  current_step: number;\r\n  progress_percentage: number;\r\n  submitted_at?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  assigned_to?: string;\r\n  assigned_at?: string;\r\n  application_data?: any;\r\n  applicant?: {\r\n    applicant_id: string;\r\n    name: string;\r\n    business_registration_number: string;\r\n    tpin: string;\r\n    website: string;\r\n    email: string;\r\n    phone: string;\r\n    fax?: string;\r\n    level_of_insurance_cover?: string;\r\n    date_incorporation: string;\r\n    place_incorporation: string;\r\n    created_at: string;\r\n    updated_at: string;\r\n  };\r\n  license_category?: {\r\n    license_category_id: string;\r\n    name: string;\r\n    license_type?: {\r\n      license_type_id: string;\r\n      name: string;\r\n      code: string;\r\n    };\r\n  };\r\n  assignee?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n}\r\n\r\nexport enum ApplicationStatus {\r\n  DRAFT = 'draft',\r\n  SUBMITTED = 'submitted',\r\n  UNDER_REVIEW = 'under_review',\r\n  EVALUATION = 'evaluation',\r\n  APPROVED = 'approved',\r\n  REJECTED = 'rejected',\r\n  WITHDRAWN = 'withdrawn',\r\n}\r\n\r\nexport interface ApplicationFilters {\r\n  licenseTypeId?: string;\r\n  licenseCategoryId?: string;\r\n  status?: ApplicationStatus | '';\r\n  dateRange?: string;\r\n  search?: string;\r\n}\r\n\r\nexport interface PaginatedResponse<T> {\r\n  data: T[];\r\n  meta: {\r\n    itemsPerPage: number;\r\n    totalItems: number;\r\n    currentPage: number;\r\n    totalPages: number;\r\n    sortBy: [string, string][];\r\n    searchBy: string[];\r\n    search: string;\r\n    filter: Record<string, string | string[]>;\r\n  };\r\n  links: {\r\n    first: string;\r\n    previous: string;\r\n    current: string;\r\n    next: string;\r\n    last: string;\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAsFO,IAAA,AAAK,2CAAA;;;;;;;;WAAA", "debugId": null}}, {"offset": {"line": 281, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/services/notificationService.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\r\nimport { processApiResponse } from '@/lib/authUtils';\r\nimport { ApplicationStatus } from '../types/license';\r\n\r\nexport interface AppNotification {\r\n  notification_id: string;\r\n  user_id: string;\r\n  application_id: string;\r\n  application_number: string;\r\n  license_category_name: string;\r\n  title: string;\r\n  message: string;\r\n  type: 'status_change' | 'reminder' | 'document_required' | 'approval' | 'rejection';\r\n  status: 'unread' | 'read';\r\n  priority: 'low' | 'medium' | 'high';\r\n  created_at: string;\r\n  read_at?: string;\r\n  metadata?: {\r\n    old_status?: ApplicationStatus;\r\n    new_status?: ApplicationStatus;\r\n    step?: number;\r\n    progress_percentage?: number;\r\n  };\r\n}\r\n\r\nexport interface NotificationSummary {\r\n  total_count: number;\r\n  unread_count: number;\r\n  notifications: AppNotification[];\r\n}\r\n\r\nexport const notificationService = {\r\n  // Get user notifications\r\n  async getUserNotifications(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    type?: string;\r\n    status?: 'read' | 'unread';\r\n  }): Promise<NotificationSummary> {\r\n    try {\r\n      const queryParams = new URLSearchParams();\r\n      \r\n      if (params?.page) queryParams.append('page', params.page.toString());\r\n      if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n      if (params?.type) queryParams.append('type', params.type);\r\n      if (params?.status) queryParams.append('status', params.status);\r\n\r\n      const endpoint = `/notifications?${queryParams.toString()}`;\r\n      console.log(`[NotificationService] Fetching notifications from: ${endpoint}`);\r\n      \r\n      const response = await apiClient.get(endpoint);\r\n      const data = processApiResponse(response);\r\n      \r\n      // Validate response structure\r\n      if (!data || typeof data !== 'object') {\r\n        console.warn('Invalid notification response format:', data);\r\n        return {\r\n          total_count: 0,\r\n          unread_count: 0,\r\n          notifications: []\r\n        };\r\n      }\r\n      \r\n      return {\r\n        total_count: data.total_count || 0,\r\n        unread_count: data.unread_count || 0,\r\n        notifications: Array.isArray(data.notifications) ? data.notifications : []\r\n      };\r\n    } catch (error: any) {\r\n      console.error('Error fetching user notifications:', {\r\n        error: error.message || 'Unknown error',\r\n        response: error.response?.data || null,\r\n        status: error.response?.status || null,\r\n        code: error.code || null,\r\n        params,\r\n        endpoint: `/notifications?${new URLSearchParams(params || {}).toString()}`\r\n      });\r\n      \r\n      // Return empty result on error instead of throwing\r\n      return {\r\n        total_count: 0,\r\n        unread_count: 0,\r\n        notifications: []\r\n      };\r\n    }\r\n  },\r\n\r\n  // Mark notification as read\r\n  async markAsRead(notificationId: string): Promise<void> {\r\n    const response = await apiClient.patch(`/notifications/${notificationId}/read`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Mark all notifications as read\r\n  async markAllAsRead(): Promise<void> {\r\n    const response = await apiClient.patch('/notifications/mark-all-read');\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Create a status change notification (usually called from backend)\r\n  async createStatusChangeNotification(\r\n    applicationId: string,\r\n    oldStatus: ApplicationStatus,\r\n    newStatus: ApplicationStatus,\r\n    step?: number,\r\n    progressPercentage?: number\r\n  ): Promise<AppNotification> {\r\n    const response = await apiClient.post('/notifications/status-change', {\r\n      application_id: applicationId,\r\n      old_status: oldStatus,\r\n      new_status: newStatus,\r\n      step,\r\n      progress_percentage: progressPercentage\r\n    });\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Delete notification\r\n  async deleteNotification(notificationId: string): Promise<void> {\r\n    const response = await apiClient.delete(`/notifications/${notificationId}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get notification count\r\n  async getNotificationCount(): Promise<{ total: number; unread: number }> {\r\n    const response = await apiClient.get('/notifications/count');\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Manual notification trigger for testing (will be removed in production)\r\n  async triggerTestNotification(\r\n    applicationId: string,\r\n    applicationNumber: string,\r\n    licenseCategoryName: string,\r\n    type: 'status_change' | 'reminder' | 'document_required' | 'approval' | 'rejection',\r\n    status: ApplicationStatus\r\n  ): Promise<AppNotification> {\r\n    const response = await apiClient.post('/notifications/test', {\r\n      application_id: applicationId,\r\n      application_number: applicationNumber,\r\n      license_category_name: licenseCategoryName,\r\n      type,\r\n      status\r\n    });\r\n    return processApiResponse(response);\r\n  }\r\n};\r\n\r\n// Status change message templates\r\nexport const getStatusChangeMessage = (\r\n  applicationNumber: string,\r\n  licenseCategoryName: string,\r\n  oldStatus: ApplicationStatus,\r\n  newStatus: ApplicationStatus,\r\n  step?: number,\r\n  progressPercentage?: number\r\n): { title: string; message: string; type: 'info' | 'success' | 'warning' | 'error' } => {\r\n  const progressText = progressPercentage ? ` (${progressPercentage}% complete)` : '';\r\n  const stepText = step ? ` - Step ${step}` : '';\r\n  \r\n  const messages = {\r\n    [ApplicationStatus.SUBMITTED]: {\r\n      title: 'Application Submitted',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) has been successfully submitted and is now being processed.`,\r\n      type: 'success' as const\r\n    },\r\n    [ApplicationStatus.UNDER_REVIEW]: {\r\n      title: 'Application Under Review',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) is now under review by our team${progressText}${stepText}. We'll notify you of any updates.`,\r\n      type: 'info' as const\r\n    },\r\n    [ApplicationStatus.EVALUATION]: {\r\n      title: 'Application Being Evaluated',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) is currently being evaluated${progressText}${stepText}. This may take several business days.`,\r\n      type: 'info' as const\r\n    },\r\n    [ApplicationStatus.APPROVED]: {\r\n      title: 'Application Approved! 🎉',\r\n      message: `Congratulations! Your ${licenseCategoryName} application (${applicationNumber}) has been approved. You can now download your license.`,\r\n      type: 'success' as const\r\n    },\r\n    [ApplicationStatus.REJECTED]: {\r\n      title: 'Application Status Update',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) requires attention. Please review the feedback and resubmit if needed.`,\r\n      type: 'warning' as const\r\n    },\r\n    [ApplicationStatus.WITHDRAWN]: {\r\n      title: 'Application Withdrawn',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) has been withdrawn as requested.`,\r\n      type: 'info' as const\r\n    }\r\n  };\r\n\r\n  const defaultMessage = {\r\n    title: 'Application Status Update',\r\n    message: `Your ${licenseCategoryName} application (${applicationNumber}) status has been updated to ${newStatus.replace('_', ' ')}.`,\r\n    type: 'info' as const\r\n  };\r\n\r\n  return messages[newStatus] || defaultMessage;\r\n};\r\n\r\n// Helper function to manually trigger notification for testing\r\nexport const createTestNotification = (\r\n  applicationNumber: string,\r\n  licenseCategoryName: string,\r\n  status: ApplicationStatus\r\n): AppNotification => {\r\n  const now = new Date().toISOString();\r\n  const notificationId = `test-${Date.now()}`;\r\n  \r\n  return {\r\n    notification_id: notificationId,\r\n    user_id: 'current-user',\r\n    application_id: `app-${applicationNumber}`,\r\n    application_number: applicationNumber,\r\n    license_category_name: licenseCategoryName,\r\n    title: getStatusChangeMessage(applicationNumber, licenseCategoryName, ApplicationStatus.SUBMITTED, status).title,\r\n    message: getStatusChangeMessage(applicationNumber, licenseCategoryName, ApplicationStatus.SUBMITTED, status).message,\r\n    type: 'status_change',\r\n    status: 'unread',\r\n    priority: 'medium',\r\n    created_at: now,\r\n    metadata: {\r\n      old_status: ApplicationStatus.SUBMITTED,\r\n      new_status: status,\r\n      step: 2,\r\n      progress_percentage: 50\r\n    }\r\n  };\r\n};"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AA6BO,MAAM,sBAAsB;IACjC,yBAAyB;IACzB,MAAM,sBAAqB,MAK1B;QACC,IAAI;YACF,MAAM,cAAc,IAAI;YAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;YACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;YACpE,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI;YACxD,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;YAE9D,MAAM,WAAW,CAAC,eAAe,EAAE,YAAY,QAAQ,IAAI;YAC3D,QAAQ,GAAG,CAAC,CAAC,mDAAmD,EAAE,UAAU;YAE5E,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,MAAM,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;YAEhC,8BAA8B;YAC9B,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;gBACrC,QAAQ,IAAI,CAAC,yCAAyC;gBACtD,OAAO;oBACL,aAAa;oBACb,cAAc;oBACd,eAAe,EAAE;gBACnB;YACF;YAEA,OAAO;gBACL,aAAa,KAAK,WAAW,IAAI;gBACjC,cAAc,KAAK,YAAY,IAAI;gBACnC,eAAe,MAAM,OAAO,CAAC,KAAK,aAAa,IAAI,KAAK,aAAa,GAAG,EAAE;YAC5E;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,sCAAsC;gBAClD,OAAO,MAAM,OAAO,IAAI;gBACxB,UAAU,MAAM,QAAQ,EAAE,QAAQ;gBAClC,QAAQ,MAAM,QAAQ,EAAE,UAAU;gBAClC,MAAM,MAAM,IAAI,IAAI;gBACpB;gBACA,UAAU,CAAC,eAAe,EAAE,IAAI,gBAAgB,UAAU,CAAC,GAAG,QAAQ,IAAI;YAC5E;YAEA,mDAAmD;YACnD,OAAO;gBACL,aAAa;gBACb,cAAc;gBACd,eAAe,EAAE;YACnB;QACF;IACF;IAEA,4BAA4B;IAC5B,MAAM,YAAW,cAAsB;QACrC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,KAAK,CAAC,CAAC,eAAe,EAAE,eAAe,KAAK,CAAC;QAC9E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,iCAAiC;IACjC,MAAM;QACJ,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,KAAK,CAAC;QACvC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,oEAAoE;IACpE,MAAM,gCACJ,aAAqB,EACrB,SAA4B,EAC5B,SAA4B,EAC5B,IAAa,EACb,kBAA2B;QAE3B,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,gCAAgC;YACpE,gBAAgB;YAChB,YAAY;YACZ,YAAY;YACZ;YACA,qBAAqB;QACvB;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,sBAAsB;IACtB,MAAM,oBAAmB,cAAsB;QAC7C,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,eAAe,EAAE,gBAAgB;QAC1E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,yBAAyB;IACzB,MAAM;QACJ,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,0EAA0E;IAC1E,MAAM,yBACJ,aAAqB,EACrB,iBAAyB,EACzB,mBAA2B,EAC3B,IAAmF,EACnF,MAAyB;QAEzB,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,uBAAuB;YAC3D,gBAAgB;YAChB,oBAAoB;YACpB,uBAAuB;YACvB;YACA;QACF;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;AACF;AAGO,MAAM,yBAAyB,CACpC,mBACA,qBACA,WACA,WACA,MACA;IAEA,MAAM,eAAe,qBAAqB,CAAC,EAAE,EAAE,mBAAmB,WAAW,CAAC,GAAG;IACjF,MAAM,WAAW,OAAO,CAAC,QAAQ,EAAE,MAAM,GAAG;IAE5C,MAAM,WAAW;QACf,CAAC,uHAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC,EAAE;YAC7B,OAAO;YACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,6DAA6D,CAAC;YACrI,MAAM;QACR;QACA,CAAC,uHAAA,CAAA,oBAAiB,CAAC,YAAY,CAAC,EAAE;YAChC,OAAO;YACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,iCAAiC,EAAE,eAAe,SAAS,kCAAkC,CAAC;YACrK,MAAM;QACR;QACA,CAAC,uHAAA,CAAA,oBAAiB,CAAC,UAAU,CAAC,EAAE;YAC9B,OAAO;YACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,8BAA8B,EAAE,eAAe,SAAS,sCAAsC,CAAC;YACtK,MAAM;QACR;QACA,CAAC,uHAAA,CAAA,oBAAiB,CAAC,QAAQ,CAAC,EAAE;YAC5B,OAAO;YACP,SAAS,CAAC,sBAAsB,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,uDAAuD,CAAC;YAChJ,MAAM;QACR;QACA,CAAC,uHAAA,CAAA,oBAAiB,CAAC,QAAQ,CAAC,EAAE;YAC5B,OAAO;YACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,wEAAwE,CAAC;YAChJ,MAAM;QACR;QACA,CAAC,uHAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC,EAAE;YAC7B,OAAO;YACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,kCAAkC,CAAC;YAC1G,MAAM;QACR;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAO;QACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,6BAA6B,EAAE,UAAU,OAAO,CAAC,KAAK,KAAK,CAAC,CAAC;QACpI,MAAM;IACR;IAEA,OAAO,QAAQ,CAAC,UAAU,IAAI;AAChC;AAGO,MAAM,yBAAyB,CACpC,mBACA,qBACA;IAEA,MAAM,MAAM,IAAI,OAAO,WAAW;IAClC,MAAM,iBAAiB,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;IAE3C,OAAO;QACL,iBAAiB;QACjB,SAAS;QACT,gBAAgB,CAAC,IAAI,EAAE,mBAAmB;QAC1C,oBAAoB;QACpB,uBAAuB;QACvB,OAAO,uBAAuB,mBAAmB,qBAAqB,uHAAA,CAAA,oBAAiB,CAAC,SAAS,EAAE,QAAQ,KAAK;QAChH,SAAS,uBAAuB,mBAAmB,qBAAqB,uHAAA,CAAA,oBAAiB,CAAC,SAAS,EAAE,QAAQ,OAAO;QACpH,MAAM;QACN,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,UAAU;YACR,YAAY,uHAAA,CAAA,oBAAiB,CAAC,SAAS;YACvC,YAAY;YACZ,MAAM;YACN,qBAAqB;QACvB;IACF;AACF", "debugId": null}}, {"offset": {"line": 450, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/common/NotificationModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useCallback } from 'react';\r\nimport { notificationService, AppNotification } from '@/services/notificationService';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useToast } from '@/contexts/ToastContext';\r\n\r\ninterface NotificationModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n}\r\n\r\nconst NotificationModal: React.FC<NotificationModalProps> = ({ isOpen, onClose }) => {\r\n  const { user } = useAuth();\r\n  const { showError, showSuccess } = useToast();\r\n  const [notifications, setNotifications] = useState<AppNotification[]>([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [filter, setFilter] = useState<'all' | 'unread'>('all');\r\n\r\n  // Fetch notifications\r\n  const fetchNotifications = useCallback(async () => {\r\n    if (!user) return;\r\n    \r\n    setLoading(true);\r\n    try {\r\n      const data = await notificationService.getUserNotifications({ \r\n        limit: 50,\r\n        status: filter === 'unread' ? 'unread' : undefined\r\n      });\r\n      \r\n      // Validate response data\r\n      if (data && Array.isArray(data.notifications)) {\r\n        setNotifications(data.notifications);\r\n      } else {\r\n        console.warn('Invalid notification data received:', data);\r\n        setNotifications([]);\r\n      }\r\n    } catch (error: unknown) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Unknown error';\r\n      const errorResponse = error && typeof error === 'object' && 'response' in error ? error.response : null;\r\n      \r\n      console.error('Error fetching notifications:', {\r\n        error: errorMessage,\r\n        response: errorResponse && typeof errorResponse === 'object' && 'data' in errorResponse ? errorResponse.data : null,\r\n        status: errorResponse && typeof errorResponse === 'object' && 'status' in errorResponse ? errorResponse.status : null,\r\n        filter\r\n      });\r\n      \r\n      setNotifications([]);\r\n      showError('Failed to load notifications. Please try again.');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [user, filter, showError]);\r\n\r\n  // Initial fetch and refetch when filter changes\r\n  useEffect(() => {\r\n    if (isOpen) {\r\n      fetchNotifications();\r\n    }\r\n  }, [isOpen, fetchNotifications]);\r\n\r\n  // Mark notification as read\r\n  const markAsRead = async (notificationId: string) => {\r\n    try {\r\n      await notificationService.markAsRead(notificationId);\r\n      setNotifications(prev => \r\n        prev.map(notification => \r\n          notification.notification_id === notificationId \r\n            ? { ...notification, status: 'read' as const }\r\n            : notification\r\n        )\r\n      );\r\n    } catch (error) {\r\n      console.error('Error marking notification as read:', error);\r\n      showError('Failed to mark notification as read');\r\n    }\r\n  };\r\n\r\n  // Mark all as read\r\n  const markAllAsRead = async () => {\r\n    try {\r\n      await notificationService.markAllAsRead();\r\n      setNotifications(prev => \r\n        prev.map(notification => ({ ...notification, status: 'read' as const }))\r\n      );\r\n      showSuccess('All notifications marked as read');\r\n    } catch (error) {\r\n      console.error('Error marking all notifications as read:', error);\r\n      showError('Failed to mark all notifications as read');\r\n    }\r\n  };\r\n\r\n  // Delete notification\r\n  const deleteNotification = async (notificationId: string) => {\r\n    try {\r\n      await notificationService.deleteNotification(notificationId);\r\n      setNotifications(prev => \r\n        prev.filter(notification => notification.notification_id !== notificationId)\r\n      );\r\n      showSuccess('Notification deleted');\r\n    } catch (error) {\r\n      console.error('Error deleting notification:', error);\r\n      showError('Failed to delete notification');\r\n    }\r\n  };\r\n\r\n  // Get notification icon based on type\r\n  const getNotificationIcon = (type: AppNotification['type']) => {\r\n    switch (type) {\r\n      case 'status_change':\r\n        return 'ri-information-line';\r\n      case 'approval':\r\n        return 'ri-check-double-line';\r\n      case 'rejection':\r\n        return 'ri-close-circle-line';\r\n      case 'document_required':\r\n        return 'ri-file-text-line';\r\n      case 'reminder':\r\n        return 'ri-alarm-line';\r\n      default:\r\n        return 'ri-notification-line';\r\n    }\r\n  };\r\n\r\n  // Get notification color based on type\r\n  const getNotificationColor = (type: AppNotification['type']) => {\r\n    switch (type) {\r\n      case 'approval':\r\n        return 'text-green-600';\r\n      case 'rejection':\r\n        return 'text-red-600';\r\n      case 'document_required':\r\n        return 'text-orange-600';\r\n      case 'reminder':\r\n        return 'text-yellow-600';\r\n      default:\r\n        return 'text-blue-600';\r\n    }\r\n  };\r\n\r\n  // Format time ago\r\n  const formatTimeAgo = (dateString: string) => {\r\n    const now = new Date();\r\n    const date = new Date(dateString);\r\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\r\n\r\n    if (diffInSeconds < 60) return 'Just now';\r\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;\r\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;\r\n    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`;\r\n    \r\n    return date.toLocaleDateString();\r\n  };\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\r\n      <div className=\"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800\">\r\n        <div className=\"mt-3\">\r\n          {/* Header */}\r\n          <div className=\"flex items-center justify-between mb-6\">\r\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\r\n              <i className=\"ri-notification-line mr-2\"></i>\r\n              Notifications\r\n            </h3>\r\n            <button\r\n              type=\"button\"\r\n              onClick={onClose}\r\n              className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\r\n              aria-label=\"Close modal\"\r\n            >\r\n              <i className=\"ri-close-line text-xl\"></i>\r\n            </button>\r\n          </div>\r\n\r\n          {/* Filters and Actions */}\r\n          <div className=\"flex items-center justify-between mb-4\">\r\n            <div className=\"flex space-x-2\">\r\n              <button\r\n                onClick={() => setFilter('all')}\r\n                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${\r\n                  filter === 'all'\r\n                    ? 'bg-primary text-white'\r\n                    : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'\r\n                }`}\r\n              >\r\n                All\r\n              </button>\r\n              <button\r\n                onClick={() => setFilter('unread')}\r\n                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${\r\n                  filter === 'unread'\r\n                    ? 'bg-primary text-white'\r\n                    : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'\r\n                }`}\r\n              >\r\n                Unread\r\n              </button>\r\n            </div>\r\n\r\n            {notifications.some(n => n.status === 'unread') && (\r\n              <button\r\n                onClick={markAllAsRead}\r\n                className=\"text-sm text-primary hover:text-primary-dark focus:outline-none\"\r\n              >\r\n                Mark all as read\r\n              </button>\r\n            )}\r\n          </div>\r\n\r\n          {/* Notifications List */}\r\n          <div className=\"max-h-96 overflow-y-auto\">\r\n            {loading ? (\r\n              <div className=\"p-8 text-center\">\r\n                <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto\"></div>\r\n                <p className=\"mt-4 text-sm text-gray-500 dark:text-gray-400\">Loading notifications...</p>\r\n              </div>\r\n            ) : notifications.length === 0 ? (\r\n              <div className=\"p-8 text-center\">\r\n                <i className=\"ri-notification-off-line text-4xl text-gray-400 mb-4\"></i>\r\n                <p className=\"text-gray-500 dark:text-gray-400\">\r\n                  {filter === 'unread' ? 'No unread notifications' : 'No notifications yet'}\r\n                </p>\r\n              </div>\r\n            ) : (\r\n              <div className=\"space-y-2\">\r\n                {notifications.map((notification) => (\r\n                  <div\r\n                    key={notification.notification_id}\r\n                    className={`p-4 rounded-lg border transition-colors ${\r\n                      notification.status === 'unread' \r\n                        ? 'bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800' \r\n                        : 'bg-white border-gray-200 dark:bg-gray-700 dark:border-gray-600'\r\n                    }`}\r\n                  >\r\n                    <div className=\"flex items-start justify-between\">\r\n                      <div className=\"flex items-start space-x-3\">\r\n                        <div className={`flex-shrink-0 ${getNotificationColor(notification.type)}`}>\r\n                          <i className={`${getNotificationIcon(notification.type)} text-xl`}></i>\r\n                        </div>\r\n                        <div className=\"flex-1 min-w-0\">\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                              {notification.title}\r\n                            </h4>\r\n                            {notification.status === 'unread' && (\r\n                              <div className=\"w-2 h-2 bg-primary rounded-full flex-shrink-0 ml-2\"></div>\r\n                            )}\r\n                          </div>\r\n                          <p className=\"mt-1 text-sm text-gray-600 dark:text-gray-400\">\r\n                            {notification.message}\r\n                          </p>\r\n                          <div className=\"mt-2 flex items-center justify-between\">\r\n                            <p className=\"text-xs text-gray-500 dark:text-gray-500\">\r\n                              {formatTimeAgo(notification.created_at)}\r\n                            </p>\r\n                            <div className=\"flex space-x-2\">\r\n                              {notification.status === 'unread' && (\r\n                                <button\r\n                                  onClick={() => markAsRead(notification.notification_id)}\r\n                                  className=\"text-xs text-primary hover:text-primary-dark focus:outline-none\"\r\n                                >\r\n                                  Mark as read\r\n                                </button>\r\n                              )}\r\n                              <button\r\n                                onClick={() => deleteNotification(notification.notification_id)}\r\n                                className=\"text-xs text-red-600 hover:text-red-700 focus:outline-none\"\r\n                              >\r\n                                Delete\r\n                              </button>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Footer */}\r\n          <div className=\"flex justify-end mt-6 pt-4 border-t border-gray-200 dark:border-gray-600\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={onClose}\r\n              className=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n            >\r\n              Close\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default NotificationModal;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAYA,MAAM,oBAAsD,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE;IAC9E,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAC1C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IACxE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAEvD,sBAAsB;IACtB,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI,CAAC,MAAM;QAEX,WAAW;QACX,IAAI;YACF,MAAM,OAAO,MAAM,sIAAA,CAAA,sBAAmB,CAAC,oBAAoB,CAAC;gBAC1D,OAAO;gBACP,QAAQ,WAAW,WAAW,WAAW;YAC3C;YAEA,yBAAyB;YACzB,IAAI,QAAQ,MAAM,OAAO,CAAC,KAAK,aAAa,GAAG;gBAC7C,iBAAiB,KAAK,aAAa;YACrC,OAAO;gBACL,QAAQ,IAAI,CAAC,uCAAuC;gBACpD,iBAAiB,EAAE;YACrB;QACF,EAAE,OAAO,OAAgB;YACvB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,MAAM,gBAAgB,SAAS,OAAO,UAAU,YAAY,cAAc,QAAQ,MAAM,QAAQ,GAAG;YAEnG,QAAQ,KAAK,CAAC,iCAAiC;gBAC7C,OAAO;gBACP,UAAU,iBAAiB,OAAO,kBAAkB,YAAY,UAAU,gBAAgB,cAAc,IAAI,GAAG;gBAC/G,QAAQ,iBAAiB,OAAO,kBAAkB,YAAY,YAAY,gBAAgB,cAAc,MAAM,GAAG;gBACjH;YACF;YAEA,iBAAiB,EAAE;YACnB,UAAU;QACZ,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;QAAM;QAAQ;KAAU;IAE5B,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV;QACF;IACF,GAAG;QAAC;QAAQ;KAAmB;IAE/B,4BAA4B;IAC5B,MAAM,aAAa,OAAO;QACxB,IAAI;YACF,MAAM,sIAAA,CAAA,sBAAmB,CAAC,UAAU,CAAC;YACrC,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,eACP,aAAa,eAAe,KAAK,iBAC7B;wBAAE,GAAG,YAAY;wBAAE,QAAQ;oBAAgB,IAC3C;QAGV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,UAAU;QACZ;IACF;IAEA,mBAAmB;IACnB,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,sIAAA,CAAA,sBAAmB,CAAC,aAAa;YACvC,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,eAAgB,CAAC;wBAAE,GAAG,YAAY;wBAAE,QAAQ;oBAAgB,CAAC;YAExE,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,UAAU;QACZ;IACF;IAEA,sBAAsB;IACtB,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,MAAM,sIAAA,CAAA,sBAAmB,CAAC,kBAAkB,CAAC;YAC7C,iBAAiB,CAAA,OACf,KAAK,MAAM,CAAC,CAAA,eAAgB,aAAa,eAAe,KAAK;YAE/D,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,UAAU;QACZ;IACF;IAEA,sCAAsC;IACtC,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,uCAAuC;IACvC,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,kBAAkB;IAClB,MAAM,gBAAgB,CAAC;QACrB,MAAM,MAAM,IAAI;QAChB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;QAEpE,IAAI,gBAAgB,IAAI,OAAO;QAC/B,IAAI,gBAAgB,MAAM,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,IAAI,KAAK,CAAC;QACzE,IAAI,gBAAgB,OAAO,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,MAAM,KAAK,CAAC;QAC5E,IAAI,gBAAgB,SAAS,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,OAAO,KAAK,CAAC;QAE/E,OAAO,KAAK,kBAAkB;IAChC;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAE,WAAU;;;;;;oCAAgC;;;;;;;0CAG/C,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,cAAW;0CAEX,cAAA,8OAAC;oCAAE,WAAU;;;;;;;;;;;;;;;;;kCAKjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAW,CAAC,2DAA2D,EACrE,WAAW,QACP,0BACA,0GACJ;kDACH;;;;;;kDAGD,8OAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAW,CAAC,2DAA2D,EACrE,WAAW,WACP,0BACA,0GACJ;kDACH;;;;;;;;;;;;4BAKF,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,2BACpC,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;kCAOL,8OAAC;wBAAI,WAAU;kCACZ,wBACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAE,WAAU;8CAAgD;;;;;;;;;;;mCAE7D,cAAc,MAAM,KAAK,kBAC3B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;;;;;8CACb,8OAAC;oCAAE,WAAU;8CACV,WAAW,WAAW,4BAA4B;;;;;;;;;;;iDAIvD,8OAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,6BAClB,8OAAC;oCAEC,WAAW,CAAC,wCAAwC,EAClD,aAAa,MAAM,KAAK,WACpB,wEACA,kEACJ;8CAEF,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAW,CAAC,cAAc,EAAE,qBAAqB,aAAa,IAAI,GAAG;8DACxE,cAAA,8OAAC;wDAAE,WAAW,GAAG,oBAAoB,aAAa,IAAI,EAAE,QAAQ,CAAC;;;;;;;;;;;8DAEnE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EACX,aAAa,KAAK;;;;;;gEAEpB,aAAa,MAAM,KAAK,0BACvB,8OAAC;oEAAI,WAAU;;;;;;;;;;;;sEAGnB,8OAAC;4DAAE,WAAU;sEACV,aAAa,OAAO;;;;;;sEAEvB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EACV,cAAc,aAAa,UAAU;;;;;;8EAExC,8OAAC;oEAAI,WAAU;;wEACZ,aAAa,MAAM,KAAK,0BACvB,8OAAC;4EACC,SAAS,IAAM,WAAW,aAAa,eAAe;4EACtD,WAAU;sFACX;;;;;;sFAIH,8OAAC;4EACC,SAAS,IAAM,mBAAmB,aAAa,eAAe;4EAC9D,WAAU;sFACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCAxCN,aAAa,eAAe;;;;;;;;;;;;;;;kCAuD3C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;uCAEe", "debugId": null}}, {"offset": {"line": 914, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/common/NotificationBell.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { notificationService } from '@/services/notificationService';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useToast } from '@/contexts/ToastContext';\r\nimport NotificationModal from './NotificationModal';\r\n\r\ninterface NotificationBellProps {\r\n  className?: string;\r\n}\r\n\r\nconst NotificationBell: React.FC<NotificationBellProps> = ({ className = '' }) => {\r\n  const { user } = useAuth();\r\n  const { showError } = useToast();\r\n  const [unreadCount, setUnreadCount] = useState(0);\r\n  const [isModalOpen, setIsModalOpen] = useState(false);\r\n\r\n  // Fetch notification count\r\n  const fetchNotificationCount = async () => {\r\n    if (!user) return;\r\n    \r\n    try {\r\n      const data = await notificationService.getNotificationCount();\r\n      setUnreadCount(data.unread);\r\n    } catch (error) {\r\n      console.error('Error fetching notification count:', error);\r\n      showError('Failed to load notification count');\r\n    }\r\n  };\r\n\r\n  // Initial fetch\r\n  useEffect(() => {\r\n    fetchNotificationCount();\r\n  }, [user]);\r\n\r\n  // Poll for new notifications every 30 seconds\r\n  useEffect(() => {\r\n    const interval = setInterval(fetchNotificationCount, 30000);\r\n    return () => clearInterval(interval);\r\n  }, [user]);\r\n\r\n  // Handle modal close and refresh count\r\n  const handleModalClose = () => {\r\n    setIsModalOpen(false);\r\n    fetchNotificationCount(); // Refresh count when modal closes\r\n  };\r\n\r\n  if (!user) return null;\r\n\r\n  return (\r\n    <>\r\n      <div className={`relative ${className}`}>\r\n        {/* Notification Bell */}\r\n        <button\r\n          onClick={() => setIsModalOpen(true)}\r\n          className=\"relative p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 dark:focus:ring-offset-gray-800\"\r\n          title=\"Notifications\"\r\n        >\r\n          <i className=\"ri-notification-line text-xl\"></i>\r\n          {unreadCount > 0 && (\r\n            <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full min-w-[1.25rem] h-5 flex items-center justify-center px-1\">\r\n              {unreadCount > 99 ? '99+' : unreadCount}\r\n            </span>\r\n          )}\r\n        </button>\r\n      </div>\r\n\r\n      {/* Notification Modal */}\r\n      <NotificationModal\r\n        isOpen={isModalOpen}\r\n        onClose={handleModalClose}\r\n      />\r\n    </>\r\n  );\r\n};\r\n\r\nexport default NotificationBell;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAYA,MAAM,mBAAoD,CAAC,EAAE,YAAY,EAAE,EAAE;IAC3E,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAC7B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,2BAA2B;IAC3B,MAAM,yBAAyB;QAC7B,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,MAAM,OAAO,MAAM,sIAAA,CAAA,sBAAmB,CAAC,oBAAoB;YAC3D,eAAe,KAAK,MAAM;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,UAAU;QACZ;IACF;IAEA,gBAAgB;IAChB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAK;IAET,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY,wBAAwB;QACrD,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAK;IAET,uCAAuC;IACvC,MAAM,mBAAmB;QACvB,eAAe;QACf,0BAA0B,kCAAkC;IAC9D;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE;;0BACE,8OAAC;gBAAI,WAAW,CAAC,SAAS,EAAE,WAAW;0BAErC,cAAA,8OAAC;oBACC,SAAS,IAAM,eAAe;oBAC9B,WAAU;oBACV,OAAM;;sCAEN,8OAAC;4BAAE,WAAU;;;;;;wBACZ,cAAc,mBACb,8OAAC;4BAAK,WAAU;sCACb,cAAc,KAAK,QAAQ;;;;;;;;;;;;;;;;;0BAOpC,8OAAC,iJAAA,CAAA,UAAiB;gBAChB,QAAQ;gBACR,SAAS;;;;;;;;AAIjB;uCAEe", "debugId": null}}, {"offset": {"line": 1018, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/customer/CustomerLayout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { usePathname, useRouter } from 'next/navigation';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useLoading } from '@/contexts/LoadingContext';\r\nimport LogoutButton from '../LogoutButton';\r\nimport NotificationBell from '../common/NotificationBell';\r\n\r\ninterface BreadcrumbItem {\r\n  label: string;\r\n  href?: string;\r\n}\r\n\r\ninterface CustomerLayoutProps {\r\n  children: React.ReactNode;\r\n  breadcrumbs?: BreadcrumbItem[];\r\n}\r\n\r\nconst CustomerLayout: React.FC<CustomerLayoutProps> = ({ children, breadcrumbs }) => {\r\n  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);\r\n  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);\r\n  const pathname = usePathname();\r\n  const router = useRouter();\r\n  const { user } = useAuth();\r\n  const { showLoader } = useLoading();\r\n\r\n  // Memoize navigation items to prevent unnecessary re-renders\r\n  const navigationItems = useMemo(() => [\r\n    {\r\n      name: 'Dashboard',\r\n      href: '/customer',\r\n      icon: 'ri-dashboard-line',\r\n      current: pathname === '/customer'\r\n    },\r\n    {\r\n      name: 'My Licenses',\r\n      href: '/customer/my-licenses',\r\n      icon: 'ri-key-line',\r\n      current: pathname === '/customer/my-licenses'\r\n    },\r\n    {\r\n      name: 'New Applications',\r\n      href: '/customer/applications',\r\n      icon: 'ri-file-list-3-line',\r\n      current: pathname === '/customer/applications'\r\n    },\r\n    {\r\n      name: 'Payments',\r\n      href: '/customer/payments',\r\n      icon: 'ri-bank-card-line',\r\n      current: pathname === '/customer/payments'\r\n    },\r\n    {\r\n      name: 'Documents',\r\n      href: '/customer/documents',\r\n      icon: 'ri-file-text-line',\r\n      current: pathname === '/customer/documents'\r\n    },\r\n    {\r\n      name: 'Procurement',\r\n      href: '/customer/procurement',\r\n      icon: 'ri-auction-line',\r\n      current: pathname === '/customer/procurement'\r\n    },\r\n    {\r\n      name: 'Request Resource',\r\n      href: '/customer/resources',\r\n      icon: 'ri-hand-heart-line',\r\n      current: pathname === '/customer/resources'\r\n    }\r\n  ], [pathname]);\r\n\r\n  const supportItems = useMemo(() => [\r\n    {\r\n      name: 'Data Protection',\r\n      href: '/customer/data-protection',\r\n      icon: 'ri-shield-keyhole-line'\r\n    },\r\n    {\r\n      name: 'Help Center',\r\n      href: '/customer/help',\r\n      icon: 'ri-question-line'\r\n    }\r\n  ], []);\r\n\r\n  // Prefetch customer pages on mount for faster navigation\r\n  useEffect(() => {\r\n    const prefetchPages = () => {\r\n      const customerPages = [\r\n        '/customer',\r\n        '/customer/applications',\r\n        '/customer/applications/standards',\r\n        '/customer/payments',\r\n        '/customer/my-licenses',\r\n        '/customer/procurement',\r\n        '/customer/profile',\r\n        '/customer/data-protection',\r\n        '/customer/resources',\r\n        '/customer/help'\r\n      ];\r\n\r\n      customerPages.forEach(page => {\r\n        router.prefetch(page);\r\n      });\r\n    };\r\n\r\n    // Delay prefetching to not interfere with initial page load\r\n    const timer = setTimeout(prefetchPages, 1000);\r\n    return () => clearTimeout(timer);\r\n  }, [router]);\r\n\r\n  const toggleMobileSidebar = useCallback(() => {\r\n    setIsMobileSidebarOpen(!isMobileSidebarOpen);\r\n  }, [isMobileSidebarOpen]);\r\n\r\n  const handleNavClick = useCallback((href: string, name: string) => {\r\n    const pageMessages: { [key: string]: string } = {\r\n      '/customer': 'Loading Dashboard...',\r\n      '/customer/my-licenses': 'Loading My Licenses...',\r\n      '/customer/applications': 'Loading Applications...',\r\n      '/customer/applications/apply/': 'Loading Standards License Options...',\r\n      '/customer/payments': 'Loading Payments...',\r\n      '/customer/documents': 'Loading Documents...',\r\n      '/customer/procurement': 'Loading Procurement...',\r\n      '/customer/resources': 'Loading Resources...',\r\n      '/customer/data-protection': 'Loading Data Protection...',\r\n      '/customer/help': 'Loading Help Center...',\r\n      '/customer/profile': 'Loading Profile...',\r\n      '/customer/settings': 'Loading Settings...'\r\n    };\r\n\r\n    const message = pageMessages[href] || `Loading ${name}...`;\r\n    showLoader(message);\r\n    setIsMobileSidebarOpen(false);\r\n  }, [showLoader]);\r\n\r\n  const handleNavHover = useCallback((href: string) => {\r\n    // Prefetch on hover for instant navigation\r\n    router.prefetch(href);\r\n  }, [router]);\r\n\r\n  const toggleUserDropdown = useCallback(() => {\r\n    setIsUserDropdownOpen(!isUserDropdownOpen);\r\n  }, [isUserDropdownOpen]);\r\n\r\n  return (\r\n    <div className=\"flex h-screen overflow-hidden\">\r\n      {/* Mobile sidebar overlay */}\r\n      {isMobileSidebarOpen && (\r\n        <div \r\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden\"\r\n          onClick={() => setIsMobileSidebarOpen(false)}\r\n        />\r\n      )}\r\n\r\n      {/* Sidebar */}\r\n      <aside className={`\r\n        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out gpu-accelerated\r\n        ${isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}\r\n      `}>\r\n        <div className=\"flex flex-col h-full\">\r\n          {/* Logo/Header */}\r\n          <div className=\"h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700\">\r\n            <div className=\"flex items-center\">\r\n              <Image \r\n                src=\"/images/macra-logo.png\" \r\n                alt=\"MACRA Logo\" \r\n                className=\"max-h-12 w-auto\" \r\n                width={120}\r\n                height={48}\r\n                priority\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Navigation */}\r\n          <nav className=\"mt-6 px-4 side-nav\">\r\n            {/* Main Navigation */}\r\n            <div className=\"space-y-1\">\r\n              {navigationItems.map((item) => (\r\n                <Link\r\n                  key={item.name}\r\n                  href={item.href}\r\n                  onClick={() => handleNavClick(item.href, item.name)}\r\n                  onMouseEnter={() => handleNavHover(item.href)}\r\n                  className={`\r\n                    flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated\r\n                    ${item.current\r\n                      ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-r-2 border-red-600 dark:border-red-400'\r\n                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100'\r\n                    }\r\n                  `}\r\n                >\r\n                  <div className={`w-5 h-5 flex items-center justify-center mr-3 ${item.current ? 'text-red-600 dark:text-red-400' : ''}`}>\r\n                    <i className={item.icon}></i>\r\n                  </div>\r\n                  {item.name}\r\n                </Link>\r\n              ))}\r\n            </div>\r\n\r\n            {/* Support Section */}\r\n            <div className=\"mt-8\">\r\n              <h3 className=\"px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                Support\r\n              </h3>\r\n              <div className=\"mt-2 space-y-1\">\r\n                {supportItems.map((item) => (\r\n                  <Link\r\n                    key={item.name}\r\n                    href={item.href}\r\n                    onClick={() => handleNavClick(item.href, item.name)}\r\n                    onMouseEnter={() => handleNavHover(item.href)}\r\n                    className=\"flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100\"\r\n                  >\r\n                    <div className=\"w-5 h-5 flex items-center justify-center mr-3\">\r\n                      <i className={item.icon}></i>\r\n                    </div>\r\n                    {item.name}\r\n                  </Link>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </nav>\r\n\r\n          {/* User Info */}\r\n          <div className=\"absolute bottom-0 w-64 p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800\">\r\n            <div className=\"flex items-center space-x-3\">\r\n              <Image\r\n                className=\"h-10 w-10 rounded-full object-cover\"\r\n                src={user?.profile_image || \"https://banner2.cleanpng.com/********/sac/9128e319522f66aba9edf95180a86e7e.webp\"}\r\n                alt=\"Profile\"\r\n                width={40}\r\n                height={40}\r\n              />\r\n              <Link \r\n                href='/customer/profile' \r\n                className=\"flex-1 min-w-0\"\r\n                onClick={() => handleNavClick('/customer/profile', 'Profile')}\r\n              >\r\n                <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100 truncate\">\r\n                  {user ? `${user.first_name} ${user.last_name}` : 'Customer'}\r\n                </p>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 truncate\">\r\n                  {/* {user?.organizationName || 'Organization'} */}\r\n                </p>\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </aside>\r\n\r\n      {/* Main content */}\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        {/* Top header */}\r\n        <header className=\"bg-white dark:bg-gray-800 shadow-sm z-10\">\r\n          <div className=\"flex items-center justify-between h-16 px-4 sm:px-6\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={toggleMobileSidebar}\r\n              className=\"md:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none\"\r\n              aria-label=\"Open mobile menu\"\r\n            >\r\n              <div className=\"w-6 h-6 flex items-center justify-center\">\r\n                <i className=\"ri-menu-line ri-lg\"></i>\r\n              </div>\r\n            </button>\r\n            \r\n            <div className=\"flex-1\">\r\n              {breadcrumbs && breadcrumbs.length > 0 && (\r\n                <nav className=\"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400\">\r\n                  {breadcrumbs.map((item, index) => (\r\n                    <React.Fragment key={index}>\r\n                      {index > 0 && <i className=\"ri-arrow-right-s-line\"></i>}\r\n                      {item.href ? (\r\n                        <Link href={item.href} className=\"hover:text-primary\">\r\n                          {item.label}\r\n                        </Link>\r\n                      ) : (\r\n                        <span className=\"text-gray-900 dark:text-gray-100\">{item.label}</span>\r\n                      )}\r\n                    </React.Fragment>\r\n                  ))}\r\n                </nav>\r\n              )}\r\n            </div>\r\n            \r\n            <div className=\"flex items-center\">\r\n              <NotificationBell className=\"mr-4\" />\r\n              \r\n              <div className=\"relative\">\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={toggleUserDropdown}\r\n                  className=\"flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n                >\r\n                  <span className=\"sr-only\">Open user menu</span>\r\n                  <Image\r\n                    className=\"h-8 w-8 rounded-full\"\r\n                    src={user?.profile_image || \"https://banner2.cleanpng.com/********/sac/9128e319522f66aba9edf95180a86e7e.webp\"}\r\n                    alt=\"Profile\"\r\n                    width={32}\r\n                    height={32}\r\n                  />\r\n                </button>\r\n                \r\n                {isUserDropdownOpen && (\r\n                  <div className=\"absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 dark:ring-gray-700 z-50\">\r\n                    <div className=\"py-1\">\r\n                      <Link\r\n                        href=\"/customer/profile\"\r\n                        className=\"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        onClick={() => {\r\n                          handleNavClick('/customer/profile', 'Profile');\r\n                          setIsUserDropdownOpen(false);\r\n                        }}\r\n                      >\r\n                        Your Profile\r\n                      </Link>\r\n          \r\n                      <LogoutButton\r\n                        variant=\"text\"\r\n                        size=\"sm\"\r\n                        className=\"w-full justify-start text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        showConfirmation={true}\r\n                      >\r\n                        Sign out\r\n                      </LogoutButton>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </header>\r\n\r\n        {/* Main content area */}\r\n        <main className=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900\">\r\n          <div className=\"py-6\">\r\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n              {children}\r\n            </div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CustomerLayout;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAqBA,MAAM,iBAAgD,CAAC,EAAE,QAAQ,EAAE,WAAW,EAAE;IAC9E,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,aAAU,AAAD;IAEhC,6DAA6D;IAC7D,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM;YACpC;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;SACD,EAAE;QAAC;KAAS;IAEb,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM;YACjC;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;YACR;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;YACR;SACD,EAAE,EAAE;IAEL,yDAAyD;IACzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,MAAM,gBAAgB;gBACpB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YAED,cAAc,OAAO,CAAC,CAAA;gBACpB,OAAO,QAAQ,CAAC;YAClB;QACF;QAEA,4DAA4D;QAC5D,MAAM,QAAQ,WAAW,eAAe;QACxC,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAO;IAEX,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,uBAAuB,CAAC;IAC1B,GAAG;QAAC;KAAoB;IAExB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,MAAc;QAChD,MAAM,eAA0C;YAC9C,aAAa;YACb,yBAAyB;YACzB,0BAA0B;YAC1B,iCAAiC;YACjC,sBAAsB;YACtB,uBAAuB;YACvB,yBAAyB;YACzB,uBAAuB;YACvB,6BAA6B;YAC7B,kBAAkB;YAClB,qBAAqB;YACrB,sBAAsB;QACxB;QAEA,MAAM,UAAU,YAAY,CAAC,KAAK,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,CAAC;QAC1D,WAAW;QACX,uBAAuB;IACzB,GAAG;QAAC;KAAW;IAEf,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,2CAA2C;QAC3C,OAAO,QAAQ,CAAC;IAClB,GAAG;QAAC;KAAO;IAEX,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,sBAAsB,CAAC;IACzB,GAAG;QAAC;KAAmB;IAEvB,qBACE,8OAAC;QAAI,WAAU;;YAEZ,qCACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,uBAAuB;;;;;;0BAK1C,8OAAC;gBAAM,WAAW,CAAC;;QAEjB,EAAE,sBAAsB,kBAAkB,qCAAqC;MACjF,CAAC;0BACC,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,WAAU;oCACV,OAAO;oCACP,QAAQ;oCACR,QAAQ;;;;;;;;;;;;;;;;sCAMd,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;4CAClD,cAAc,IAAM,eAAe,KAAK,IAAI;4CAC5C,WAAW,CAAC;;oBAEV,EAAE,KAAK,OAAO,GACV,8GACA,wHACH;kBACH,CAAC;;8DAED,8OAAC;oDAAI,WAAW,CAAC,8CAA8C,EAAE,KAAK,OAAO,GAAG,mCAAmC,IAAI;8DACrH,cAAA,8OAAC;wDAAE,WAAW,KAAK,IAAI;;;;;;;;;;;gDAExB,KAAK,IAAI;;2CAfL,KAAK,IAAI;;;;;;;;;;8CAqBpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuF;;;;;;sDAGrG,8OAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,4JAAA,CAAA,UAAI;oDAEH,MAAM,KAAK,IAAI;oDACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;oDAClD,cAAc,IAAM,eAAe,KAAK,IAAI;oDAC5C,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAE,WAAW,KAAK,IAAI;;;;;;;;;;;wDAExB,KAAK,IAAI;;mDATL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;sCAiBxB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,6HAAA,CAAA,UAAK;wCACJ,WAAU;wCACV,KAAK,MAAM,iBAAiB;wCAC5B,KAAI;wCACJ,OAAO;wCACP,QAAQ;;;;;;kDAEV,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,eAAe,qBAAqB;;0DAEnD,8OAAC;gDAAE,WAAU;0DACV,OAAO,GAAG,KAAK,UAAU,CAAC,CAAC,EAAE,KAAK,SAAS,EAAE,GAAG;;;;;;0DAEnD,8OAAC;gDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUvB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAO,WAAU;kCAChB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,cAAW;8CAEX,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;;;;;;;;;;;;;;;;8CAIjB,8OAAC;oCAAI,WAAU;8CACZ,eAAe,YAAY,MAAM,GAAG,mBACnC,8OAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;;oDACZ,QAAQ,mBAAK,8OAAC;wDAAE,WAAU;;;;;;oDAC1B,KAAK,IAAI,iBACR,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAM,KAAK,IAAI;wDAAE,WAAU;kEAC9B,KAAK,KAAK;;;;;6EAGb,8OAAC;wDAAK,WAAU;kEAAoC,KAAK,KAAK;;;;;;;+CAP7C;;;;;;;;;;;;;;;8CAe7B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gJAAA,CAAA,UAAgB;4CAAC,WAAU;;;;;;sDAE5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;;sEAEV,8OAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,8OAAC,6HAAA,CAAA,UAAK;4DACJ,WAAU;4DACV,KAAK,MAAM,iBAAiB;4DAC5B,KAAI;4DACJ,OAAO;4DACP,QAAQ;;;;;;;;;;;;gDAIX,oCACC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,4JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;gEACV,SAAS;oEACP,eAAe,qBAAqB;oEACpC,sBAAsB;gEACxB;0EACD;;;;;;0EAID,8OAAC,kIAAA,CAAA,UAAY;gEACX,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,kBAAkB;0EACnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAYf,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;uCAEe", "debugId": null}}, {"offset": {"line": 1599, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/forms/Select.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { forwardRef } from 'react';\r\n\r\ninterface SelectOption {\r\n  value: string;\r\n  label: string;\r\n}\r\n\r\ninterface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {\r\n  label?: React.ReactNode;\r\n  error?: string;\r\n  helperText?: string;\r\n  variant?: 'default' | 'small';\r\n  fullWidth?: boolean;\r\n  options?: SelectOption[];\r\n  children?: React.ReactNode;\r\n}\r\n\r\nconst Select = forwardRef<HTMLSelectElement, SelectProps>(({\r\n  label,\r\n  error,\r\n  helperText,\r\n  variant = 'default',\r\n  fullWidth = true,\r\n  className = '',\r\n  required,\r\n  disabled,\r\n  options,\r\n  children,\r\n  ...props\r\n}, ref) => {\r\n  // Base select styling with proper text visibility for all modes\r\n  const baseSelectClass = `px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white transition-colors duration-200 ${\r\n    fullWidth ? 'w-full' : ''\r\n  } ${variant === 'small' ? 'py-1.5 text-sm' : 'py-2'}`;\r\n\r\n  // Error and disabled states\r\n  const selectClass = `${baseSelectClass} ${\r\n    error \r\n      ? \"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500\" \r\n      : \"border-gray-300 dark:border-gray-600\"\r\n  } ${\r\n    disabled \r\n      ? \"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800\" \r\n      : \"\"\r\n  } ${className}`;\r\n\r\n  const labelClass = `block font-medium text-gray-700 dark:text-gray-300 mb-2 ${\r\n    variant === 'small' ? 'text-xs text-gray-600 dark:text-gray-400 mb-1' : 'text-sm'\r\n  }`;\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      {label && (\r\n        <label className={labelClass}>\r\n          {label}\r\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\r\n        </label>\r\n      )}\r\n      \r\n      <select\r\n        ref={ref}\r\n        className={selectClass}\r\n        disabled={disabled}\r\n        required={required}\r\n        {...props}\r\n      >\r\n        {options ? (\r\n          options.map((option) => (\r\n            <option key={option.value} value={option.value}>\r\n              {option.label}\r\n            </option>\r\n          ))\r\n        ) : (\r\n          children\r\n        )}\r\n      </select>\r\n      \r\n      {error && (\r\n        <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\r\n          {error}\r\n        </p>\r\n      )}\r\n      \r\n      {helperText && !error && (\r\n        <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n          {helperText}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n});\r\n\r\nSelect.displayName = 'Select';\r\n\r\nexport default Select;"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAmBA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAkC,CAAC,EACzD,KAAK,EACL,KAAK,EACL,UAAU,EACV,UAAU,SAAS,EACnB,YAAY,IAAI,EAChB,YAAY,EAAE,EACd,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,gEAAgE;IAChE,MAAM,kBAAkB,CAAC,6LAA6L,EACpN,YAAY,WAAW,GACxB,CAAC,EAAE,YAAY,UAAU,mBAAmB,QAAQ;IAErD,4BAA4B;IAC5B,MAAM,cAAc,GAAG,gBAAgB,CAAC,EACtC,QACI,+EACA,uCACL,CAAC,EACA,WACI,8DACA,GACL,CAAC,EAAE,WAAW;IAEf,MAAM,aAAa,CAAC,wDAAwD,EAC1E,YAAY,UAAU,kDAAkD,WACxE;IAEF,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBAAM,WAAW;;oBACf;oBACA,0BAAY,8OAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,8OAAC;gBACC,KAAK;gBACL,WAAW;gBACX,UAAU;gBACV,UAAU;gBACT,GAAG,KAAK;0BAER,UACC,QAAQ,GAAG,CAAC,CAAC,uBACX,8OAAC;wBAA0B,OAAO,OAAO,KAAK;kCAC3C,OAAO,KAAK;uBADF,OAAO,KAAK;;;;gCAK3B;;;;;;YAIH,uBACC,8OAAC;gBAAE,WAAU;0BACV;;;;;;YAIJ,cAAc,CAAC,uBACd,8OAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;AAEA,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 1684, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/app/customer/profile/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport Image from 'next/image';\r\nimport Link from 'next/link';\r\nimport CustomerLayout from '@/components/customer/CustomerLayout';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useTheme } from '@/lib/ThemeContext';\r\n\r\nimport Select from '@/components/forms/Select';\r\n\r\nconst CustomerProfilePage = () => {\r\n  const { user } = useAuth();\r\n  const { theme, setTheme } = useTheme();\r\n  const [activeTab, setActiveTab] = useState('profile');\r\n  const [isEditing, setIsEditing] = useState(false);\r\n  const [formData, setFormData] = useState({\r\n    firstName: user?.first_name || '',\r\n    lastName: user?.last_name || '',\r\n    email: user?.email || '',\r\n    phone: user?.phone || '',\r\n    organizationName:  '',\r\n    address: '',\r\n    city:  '',\r\n    country: 'Malawi'\r\n  });\r\n  const [passwordData, setPasswordData] = useState({\r\n    currentPassword: '',\r\n    newPassword: '',\r\n    confirmPassword: ''\r\n  });\r\n  const [loading, setLoading] = useState(false);\r\n  const [message, setMessage] = useState({ type: '', text: '' });\r\n\r\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\r\n    const { name, value } = e.target;\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [name]: value\r\n    }));\r\n  };\r\n\r\n  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const { name, value } = e.target;\r\n    setPasswordData(prev => ({\r\n      ...prev,\r\n      [name]: value\r\n    }));\r\n  };\r\n\r\n  const handleProfileSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n    setMessage({ type: '', text: '' });\r\n\r\n    try {\r\n      // Simulate API call\r\n      await new Promise(resolve => setTimeout(resolve, 1000));\r\n      setMessage({ type: 'success', text: 'Profile updated successfully!' });\r\n      setIsEditing(false);\r\n    } catch {\r\n      setMessage({ type: 'error', text: 'Failed to update profile. Please try again.' });\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handlePasswordSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    if (passwordData.newPassword !== passwordData.confirmPassword) {\r\n      setMessage({ type: 'error', text: 'New passwords do not match.' });\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    setMessage({ type: '', text: '' });\r\n\r\n    try {\r\n      // Simulate API call\r\n      await new Promise(resolve => setTimeout(resolve, 1000));\r\n      setMessage({ type: 'success', text: 'Password changed successfully!' });\r\n      setPasswordData({ currentPassword: '', newPassword: '', confirmPassword: '' });\r\n    } catch {\r\n      setMessage({ type: 'error', text: 'Failed to change password. Please try again.' });\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const tabs = [\r\n    { id: 'profile', name: 'Profile Information', icon: 'ri-user-line' },\r\n    { id: 'security', name: 'Security', icon: 'ri-shield-line' },\r\n    { id: 'preferences', name: 'Preferences', icon: 'ri-settings-line' }\r\n  ];\r\n\r\n  return (\r\n    <CustomerLayout>\r\n      <div className=\"max-w-7xl mx-auto\">\r\n        {/* Page Header */}\r\n        <div className=\"mb-8\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <h1 className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">\r\n                My Profile\r\n              </h1>\r\n              <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n                Manage your account settings and preferences\r\n              </p>\r\n            </div>\r\n            <div className=\"flex items-center space-x-3\">\r\n              <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400\">\r\n                <i className=\"ri-check-line mr-1\"></i>\r\n                Verified Account\r\n              </span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Profile Overview Card */}\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow mb-6\">\r\n          <div className=\"p-6\">\r\n            <div className=\"flex items-center space-x-6\">\r\n              <div className=\"flex-shrink-0\">\r\n                <div className=\"relative\">\r\n                  <Image\r\n                    className=\"h-20 w-20 rounded-full object-cover ring-4 ring-white dark:ring-gray-800\"\r\n                    src={user?.profile_image || \"https://banner2.cleanpng.com/********/sac/9128e319522f66aba9edf95180a86e7e.webp\"}\r\n                    alt=\"Profile\"\r\n                    width={80}\r\n                    height={80}\r\n                  />\r\n                  <button \r\n                    className=\"absolute bottom-0 right-0 bg-primary hover:bg-red-700 text-white rounded-full p-1.5 shadow-lg transition-colors\"\r\n                    title=\"Change profile picture\"\r\n                    aria-label=\"Change profile picture\"\r\n                  >\r\n                    <i className=\"ri-camera-line text-sm\"></i>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n              <div className=\"flex-1\">\r\n                <h2 className=\"text-xl font-semibold text-gray-900 dark:text-gray-100\">\r\n                  {user ? `${user.last_name} ${user.last_name}` : 'Customer Name'}\r\n                </h2>\r\n                <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                  {/* {user?.organizationName || 'Organization Name'} */}\r\n                </p>\r\n                <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                  {user?.email || '<EMAIL>'}\r\n                </p>\r\n                <div className=\"mt-2 flex items-center space-x-4\">\r\n                  <span className=\"inline-flex items-center text-sm text-gray-500 dark:text-gray-400\">\r\n                    <i className=\"ri-calendar-line mr-1\"></i>\r\n                    Member since {new Date().getFullYear() - 1}\r\n                  </span>\r\n                  {/* <span className=\"inline-flex items-center text-sm text-gray-500 dark:text-gray-400\">\r\n                    <i className=\"ri-map-pin-line mr-1\"></i>\r\n                    {user?.city || 'Lilongwe'}, {user?.country || 'Malawi'}\r\n                  </span> */}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Tab Navigation */}\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow mb-6\">\r\n          <div className=\"border-b border-gray-200 dark:border-gray-700\">\r\n            <nav className=\"-mb-px flex space-x-8 px-6\">\r\n              {tabs.map((tab) => (\r\n                <button\r\n                  key={tab.id}\r\n                  onClick={() => setActiveTab(tab.id)}\r\n                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${\r\n                    activeTab === tab.id\r\n                      ? 'border-red-500 text-red-600 dark:text-red-400'\r\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\r\n                  }`}\r\n                >\r\n                  <i className={`${tab.icon} mr-2`}></i>\r\n                  {tab.name}\r\n                </button>\r\n              ))}\r\n            </nav>\r\n          </div>\r\n\r\n          {/* Tab Content */}\r\n          <div className=\"p-6\">\r\n            {/* Alert Messages */}\r\n            {message.text && (\r\n              <div className={`mb-6 rounded-md p-4 border-l-4 ${\r\n                message.type === 'success' \r\n                  ? 'bg-green-50 dark:bg-green-900/20 border-green-400 dark:border-green-600' \r\n                  : 'bg-red-50 dark:bg-red-900/20 border-red-400 dark:border-red-600'\r\n              }`}>\r\n                <div className=\"flex\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <i className={`${\r\n                      message.type === 'success' \r\n                        ? 'ri-check-line text-green-400 dark:text-green-500' \r\n                        : 'ri-error-warning-line text-red-400 dark:text-red-500'\r\n                    } text-lg`}></i>\r\n                  </div>\r\n                  <div className=\"ml-3\">\r\n                    <p className={`text-sm ${\r\n                      message.type === 'success' \r\n                        ? 'text-green-800 dark:text-green-300' \r\n                        : 'text-red-800 dark:text-red-300'\r\n                    }`}>\r\n                      {message.text}\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Profile Information Tab */}\r\n            {activeTab === 'profile' && (\r\n              <div className=\"space-y-6\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\r\n                    Personal Information\r\n                  </h3>\r\n                  <button\r\n                    onClick={() => setIsEditing(!isEditing)}\r\n                    className=\"inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors\"\r\n                  >\r\n                    <i className={`${isEditing ? 'ri-close-line' : 'ri-edit-line'} mr-2`}></i>\r\n                    {isEditing ? 'Cancel' : 'Edit Profile'}\r\n                  </button>\r\n                </div>\r\n\r\n                <form onSubmit={handleProfileSubmit}>\r\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                    {/* First Name */}\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                        First Name *\r\n                      </label>\r\n                      <div className=\"relative\">\r\n                        <input\r\n                          type=\"text\"\r\n                          name=\"firstName\"\r\n                          value={formData.firstName}\r\n                          onChange={handleInputChange}\r\n                          disabled={!isEditing}\r\n                          className=\"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500\"\r\n                          placeholder=\"Enter your first name\"\r\n                        />\r\n                        <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                          <i className=\"ri-user-line text-gray-400 dark:text-gray-500\"></i>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Last Name */}\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                        Last Name *\r\n                      </label>\r\n                      <div className=\"relative\">\r\n                        <input\r\n                          type=\"text\"\r\n                          name=\"lastName\"\r\n                          value={formData.lastName}\r\n                          onChange={handleInputChange}\r\n                          disabled={!isEditing}\r\n                          className=\"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500\"\r\n                          placeholder=\"Enter your last name\"\r\n                        />\r\n                        <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                          <i className=\"ri-user-line text-gray-400 dark:text-gray-500\"></i>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Email */}\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                        Email Address *\r\n                      </label>\r\n                      <div className=\"relative\">\r\n                        <input\r\n                          type=\"email\"\r\n                          name=\"email\"\r\n                          value={formData.email}\r\n                          onChange={handleInputChange}\r\n                          disabled={!isEditing}\r\n                          className=\"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500\"\r\n                          placeholder=\"Enter your email address\"\r\n                        />\r\n                        <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                          <i className=\"ri-mail-line text-gray-400 dark:text-gray-500\"></i>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Phone */}\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                        Phone Number *\r\n                      </label>\r\n                      <div className=\"relative\">\r\n                        <input\r\n                          type=\"tel\"\r\n                          name=\"phone\"\r\n                          value={formData.phone}\r\n                          onChange={handleInputChange}\r\n                          disabled={!isEditing}\r\n                          className=\"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500\"\r\n                          placeholder=\"Enter your phone number\"\r\n                        />\r\n                        <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                          <i className=\"ri-phone-line text-gray-400 dark:text-gray-500\"></i>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Organization */}\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                        Organization Name *\r\n                      </label>\r\n                      <div className=\"relative\">\r\n                        <input\r\n                          type=\"text\"\r\n                          name=\"organizationName\"\r\n                          value={formData.organizationName}\r\n                          onChange={handleInputChange}\r\n                          disabled={!isEditing}\r\n                          className=\"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500\"\r\n                          placeholder=\"Enter your organization name\"\r\n                        />\r\n                        <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                          <i className=\"ri-building-line text-gray-400 dark:text-gray-500\"></i>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* City */}\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                        City\r\n                      </label>\r\n                      <div className=\"relative\">\r\n                        <input\r\n                          type=\"text\"\r\n                          name=\"city\"\r\n                          value={formData.city}\r\n                          onChange={handleInputChange}\r\n                          disabled={!isEditing}\r\n                          className=\"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500\"\r\n                          placeholder=\"Enter your city\"\r\n                        />\r\n                        <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                          <i className=\"ri-map-pin-line text-gray-400 dark:text-gray-500\"></i>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Address */}\r\n                  <div className=\"mt-6\">\r\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                      Address\r\n                    </label>\r\n                    <div className=\"relative\">\r\n                      <textarea\r\n                        name=\"address\"\r\n                        rows={3}\r\n                        value={formData.address}\r\n                        onChange={handleInputChange}\r\n                        disabled={!isEditing}\r\n                        className=\"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500\"\r\n                        placeholder=\"Enter your full address\"\r\n                      />\r\n                      <div className=\"absolute top-3 left-0 pl-3 flex items-start pointer-events-none\">\r\n                        <i className=\"ri-home-line text-gray-400 dark:text-gray-500\"></i>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {isEditing && (\r\n                    <div className=\"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700\">\r\n                      <button\r\n                        type=\"button\"\r\n                        onClick={() => setIsEditing(false)}\r\n                        className=\"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors\"\r\n                      >\r\n                        Cancel\r\n                      </button>\r\n                      <button\r\n                        type=\"submit\"\r\n                        disabled={loading}\r\n                        className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\r\n                      >\r\n                        {loading ? (\r\n                          <>\r\n                            <i className=\"ri-loader-4-line animate-spin mr-2\"></i>\r\n                            Saving...\r\n                          </>\r\n                        ) : (\r\n                          <>\r\n                            <i className=\"ri-save-line mr-2\"></i>\r\n                            Save Changes\r\n                          </>\r\n                        )}\r\n                      </button>\r\n                    </div>\r\n                  )}\r\n                </form>\r\n              </div>\r\n            )}\r\n\r\n            {/* Security Tab */}\r\n            {activeTab === 'security' && (\r\n              <div className=\"space-y-6\">\r\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\r\n                  Security Settings\r\n                </h3>\r\n\r\n                {/* Change Password */}\r\n                <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-6\">\r\n                  <h4 className=\"text-md font-medium text-gray-900 dark:text-gray-100 mb-4\">\r\n                    Change Password\r\n                  </h4>\r\n                  <form onSubmit={handlePasswordSubmit} className=\"space-y-4\">\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                        Current Password\r\n                      </label>\r\n                      <div className=\"relative\">\r\n                        <input\r\n                          type=\"password\"\r\n                          name=\"currentPassword\"\r\n                          value={passwordData.currentPassword}\r\n                          onChange={handlePasswordChange}\r\n                          className=\"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500\"\r\n                          placeholder=\"Enter current password\"\r\n                        />\r\n                        <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                          <i className=\"ri-lock-line text-gray-400 dark:text-gray-500\"></i>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                        New Password\r\n                      </label>\r\n                      <div className=\"relative\">\r\n                        <input\r\n                          type=\"password\"\r\n                          name=\"newPassword\"\r\n                          value={passwordData.newPassword}\r\n                          onChange={handlePasswordChange}\r\n                          className=\"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500\"\r\n                          placeholder=\"Enter new password\"\r\n                        />\r\n                        <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                          <i className=\"ri-lock-line text-gray-400 dark:text-gray-500\"></i>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                        Confirm New Password\r\n                      </label>\r\n                      <div className=\"relative\">\r\n                        <input\r\n                          type=\"password\"\r\n                          name=\"confirmPassword\"\r\n                          value={passwordData.confirmPassword}\r\n                          onChange={handlePasswordChange}\r\n                          className=\"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500\"\r\n                          placeholder=\"Confirm new password\"\r\n                        />\r\n                        <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                          <i className=\"ri-lock-line text-gray-400 dark:text-gray-500\"></i>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"flex justify-end\">\r\n                      <button\r\n                        type=\"submit\"\r\n                        disabled={loading}\r\n                        className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\r\n                      >\r\n                        {loading ? (\r\n                          <>\r\n                            <i className=\"ri-loader-4-line animate-spin mr-2\"></i>\r\n                            Updating...\r\n                          </>\r\n                        ) : (\r\n                          <>\r\n                            <i className=\"ri-shield-check-line mr-2\"></i>\r\n                            Update Password\r\n                          </>\r\n                        )}\r\n                      </button>\r\n                    </div>\r\n                  </form>\r\n                </div>\r\n\r\n                {/* Account Security Info */}\r\n                <div className=\"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6\">\r\n                  <h4 className=\"text-md font-medium text-gray-900 dark:text-gray-100 mb-4\">\r\n                    Account Security\r\n                  </h4>\r\n                  <div className=\"space-y-4\">\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <div className=\"flex items-center\">\r\n                        <i className=\"ri-shield-check-line text-green-500 mr-3\"></i>\r\n                        <div>\r\n                          <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">Two-Factor Authentication</p>\r\n                          <p className=\"text-xs text-gray-500 dark:text-gray-400\">An extra layer of security for your account</p>\r\n                        </div>\r\n                      </div>\r\n                      <button className=\"text-sm text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300\">\r\n                        {user?.two_factor_enabled ? 'Enabled' : 'Not Enabled'}\r\n                      </button>\r\n                    </div>\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <div className=\"flex items-center\">\r\n                        <i className=\"ri-smartphone-line text-blue-500 mr-3\"></i>\r\n                        <div>\r\n                          <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">Login Notifications</p>\r\n                          <p className=\"text-xs text-gray-500 dark:text-gray-400\">Get notified of new sign-ins</p>\r\n                        </div>\r\n                      </div>\r\n                      <button className=\"text-sm text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300\">\r\n                        Configure\r\n                      </button>\r\n                    </div>\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <div className=\"flex items-center\">\r\n                        <i className=\"ri-user-unfollow-line text-orange-500 mr-3\"></i>\r\n                        <div>\r\n                          <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">Account Deactivation</p>\r\n                          <p className=\"text-xs text-gray-500 dark:text-gray-400\">Temporarily deactivate your account</p>\r\n                        </div>\r\n                      </div>\r\n                      <Link\r\n                        href=\"/customer/auth/deactivate\"\r\n                        className=\"text-sm text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300\"\r\n                      >\r\n                        Manage\r\n                      </Link>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Preferences Tab */}\r\n            {activeTab === 'preferences' && (\r\n              <div className=\"space-y-6\">\r\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\r\n                  Account Preferences\r\n                </h3>\r\n\r\n                {/* Notification Preferences */}\r\n                <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-6\">\r\n                  <h4 className=\"text-md font-medium text-gray-900 dark:text-gray-100 mb-4\">\r\n                    Notification Preferences\r\n                  </h4>\r\n                  <div className=\"space-y-4\">\r\n                    {[\r\n                      { id: 'email_notifications', label: 'Email Notifications', description: 'Receive updates via email' },\r\n                      { id: 'license_expiry', label: 'License Expiry Alerts', description: 'Get notified before licenses expire' },\r\n                      { id: 'payment_reminders', label: 'Payment Reminders', description: 'Receive payment due notifications' },\r\n                      { id: 'application_updates', label: 'Application Updates', description: 'Get updates on application status' }\r\n                    ].map((pref) => (\r\n                      <div key={pref.id} className=\"flex items-start space-x-3 p-3 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors\">\r\n                        <div className=\"flex items-center h-5\">\r\n                          <input\r\n                            id={pref.id}\r\n                            type=\"checkbox\"\r\n                            defaultChecked\r\n                            className=\"h-4 w-4 text-primary focus:ring-primary border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700 dark:focus:ring-primary dark:ring-offset-gray-800\"\r\n                            aria-describedby={`${pref.id}-description`}\r\n                          />\r\n                        </div>\r\n                        <div className=\"flex-1\">\r\n                          <label\r\n                            htmlFor={pref.id}\r\n                            className=\"text-sm font-medium text-gray-900 dark:text-gray-100 cursor-pointer\"\r\n                          >\r\n                            {pref.label}\r\n                          </label>\r\n                          <p\r\n                            id={`${pref.id}-description`}\r\n                            className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\"\r\n                          >\r\n                            {pref.description}\r\n                          </p>\r\n                        </div>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Theme Settings */}\r\n                <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-6\">\r\n                  <h4 className=\"text-md font-medium text-gray-900 dark:text-gray-100 mb-4\">\r\n                    Theme Settings\r\n                  </h4>\r\n                  <div className=\"space-y-4\">\r\n                    <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                      Choose your preferred color scheme for the interface\r\n                    </p>\r\n                    \r\n                    {/* Theme Options Grid */}\r\n                    <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4\">\r\n                      {[\r\n                        {\r\n                          value: 'light',\r\n                          label: 'Light',\r\n                          description: 'Use light theme',\r\n                          icon: 'ri-sun-line'\r\n                        },\r\n                        {\r\n                          value: 'dark',\r\n                          label: 'Dark',\r\n                          description: 'Use dark theme',\r\n                          icon: 'ri-moon-line'\r\n                        },\r\n                        {\r\n                          value: 'system',\r\n                          label: 'System',\r\n                          description: 'Follow system preference',\r\n                          icon: 'ri-computer-line'\r\n                        }\r\n                      ].map((option) => (\r\n                        <button\r\n                          key={option.value}\r\n                          onClick={() => setTheme(option.value)}\r\n                          className={`relative p-4 border-2 rounded-lg transition-all duration-200 ${\r\n                            theme === option.value\r\n                              ? 'border-red-500 bg-red-50 dark:bg-red-900/20'\r\n                              : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500 bg-white dark:bg-gray-800'\r\n                          }`}\r\n                        >\r\n                          <div className=\"flex flex-col items-center text-center\">\r\n                            <div className={`w-10 h-10 rounded-lg flex items-center justify-center mb-3 ${\r\n                              theme === option.value\r\n                                ? 'bg-red-100 dark:bg-red-800 text-red-600 dark:text-red-400'\r\n                                : 'bg-gray-100 dark:bg-gray-600 text-gray-600 dark:text-gray-400'\r\n                            }`}>\r\n                              <i className={`${option.icon} text-xl`}></i>\r\n                            </div>\r\n                            <h5 className={`text-sm font-medium ${\r\n                              theme === option.value\r\n                                ? 'text-red-900 dark:text-red-100'\r\n                                : 'text-gray-900 dark:text-gray-100'\r\n                            }`}>\r\n                              {option.label}\r\n                            </h5>\r\n                            <p className={`text-xs mt-1 ${\r\n                              theme === option.value\r\n                                ? 'text-red-700 dark:text-red-300'\r\n                                : 'text-gray-500 dark:text-gray-400'\r\n                            }`}>\r\n                              {option.description}\r\n                            </p>\r\n                          </div>\r\n                          {theme === option.value && (\r\n                            <div className=\"absolute top-2 right-2\">\r\n                              <div className=\"w-5 h-5 bg-red-500 rounded-full flex items-center justify-center\">\r\n                                <i className=\"ri-check-line text-white text-xs\"></i>\r\n                              </div>\r\n                            </div>\r\n                          )}\r\n                        </button>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Language & Region */}\r\n                <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-6\">\r\n                  <h4 className=\"text-md font-medium text-gray-900 dark:text-gray-100 mb-4\">\r\n                    Language & Region\r\n                  </h4>\r\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                    <Select\r\n                      label=\"Language\"\r\n                      aria-label=\"Select language\"\r\n                    >\r\n                      <option>English</option>\r\n                      <option>Chichewa</option>\r\n                    </Select>\r\n                    <Select\r\n                      label=\"Timezone\"\r\n                      aria-label=\"Select timezone\"\r\n                    >\r\n                      <option>Africa/Blantyre (CAT)</option>\r\n                      <option>UTC</option>\r\n                    </Select>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </CustomerLayout>\r\n  );\r\n};\r\n\r\nexport default CustomerProfilePage;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AATA;;;;;;;;;AAWA,MAAM,sBAAsB;IAC1B,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,WAAW,MAAM,cAAc;QAC/B,UAAU,MAAM,aAAa;QAC7B,OAAO,MAAM,SAAS;QACtB,OAAO,MAAM,SAAS;QACtB,kBAAmB;QACnB,SAAS;QACT,MAAO;QACP,SAAS;IACX;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC/C,iBAAiB;QACjB,aAAa;QACb,iBAAiB;IACnB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,MAAM;QAAI,MAAM;IAAG;IAE5D,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,gBAAgB,CAAA,OAAQ,CAAC;gBACvB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,sBAAsB,OAAO;QACjC,EAAE,cAAc;QAChB,WAAW;QACX,WAAW;YAAE,MAAM;YAAI,MAAM;QAAG;QAEhC,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,WAAW;gBAAE,MAAM;gBAAW,MAAM;YAAgC;YACpE,aAAa;QACf,EAAE,OAAM;YACN,WAAW;gBAAE,MAAM;gBAAS,MAAM;YAA8C;QAClF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB,OAAO;QAClC,EAAE,cAAc;QAChB,IAAI,aAAa,WAAW,KAAK,aAAa,eAAe,EAAE;YAC7D,WAAW;gBAAE,MAAM;gBAAS,MAAM;YAA8B;YAChE;QACF;QAEA,WAAW;QACX,WAAW;YAAE,MAAM;YAAI,MAAM;QAAG;QAEhC,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,WAAW;gBAAE,MAAM;gBAAW,MAAM;YAAiC;YACrE,gBAAgB;gBAAE,iBAAiB;gBAAI,aAAa;gBAAI,iBAAiB;YAAG;QAC9E,EAAE,OAAM;YACN,WAAW;gBAAE,MAAM;gBAAS,MAAM;YAA+C;QACnF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAW,MAAM;YAAuB,MAAM;QAAe;QACnE;YAAE,IAAI;YAAY,MAAM;YAAY,MAAM;QAAiB;QAC3D;YAAE,IAAI;YAAe,MAAM;YAAe,MAAM;QAAmB;KACpE;IAED,qBACE,8OAAC,gJAAA,CAAA,UAAc;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA0D;;;;;;kDAGxE,8OAAC;wCAAE,WAAU;kDAAgD;;;;;;;;;;;;0CAI/D,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;;sDACd,8OAAC;4CAAE,WAAU;;;;;;wCAAyB;;;;;;;;;;;;;;;;;;;;;;;8BAQ9C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,6HAAA,CAAA,UAAK;gDACJ,WAAU;gDACV,KAAK,MAAM,iBAAiB;gDAC5B,KAAI;gDACJ,OAAO;gDACP,QAAQ;;;;;;0DAEV,8OAAC;gDACC,WAAU;gDACV,OAAM;gDACN,cAAW;0DAEX,cAAA,8OAAC;oDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;8CAInB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDACX,OAAO,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,SAAS,EAAE,GAAG;;;;;;sDAElD,8OAAC;4CAAE,WAAU;;;;;;sDAGb,8OAAC;4CAAE,WAAU;sDACV,MAAM,SAAS;;;;;;sDAElB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;;kEACd,8OAAC;wDAAE,WAAU;;;;;;oDAA4B;oDAC3B,IAAI,OAAO,WAAW,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAarD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ,KAAK,GAAG,CAAC,CAAC,oBACT,8OAAC;wCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;wCAClC,WAAW,CAAC,wEAAwE,EAClF,cAAc,IAAI,EAAE,GAChB,kDACA,0HACJ;;0DAEF,8OAAC;gDAAE,WAAW,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC;;;;;;4CAC/B,IAAI,IAAI;;uCATJ,IAAI,EAAE;;;;;;;;;;;;;;;sCAgBnB,8OAAC;4BAAI,WAAU;;gCAEZ,QAAQ,IAAI,kBACX,8OAAC;oCAAI,WAAW,CAAC,+BAA+B,EAC9C,QAAQ,IAAI,KAAK,YACb,4EACA,mEACJ;8CACA,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAW,GACZ,QAAQ,IAAI,KAAK,YACb,qDACA,uDACL,QAAQ,CAAC;;;;;;;;;;;0DAEZ,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAW,CAAC,QAAQ,EACrB,QAAQ,IAAI,KAAK,YACb,uCACA,kCACJ;8DACC,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;gCAQtB,cAAc,2BACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAuD;;;;;;8DAGrE,8OAAC;oDACC,SAAS,IAAM,aAAa,CAAC;oDAC7B,WAAU;;sEAEV,8OAAC;4DAAE,WAAW,GAAG,YAAY,kBAAkB,eAAe,KAAK,CAAC;;;;;;wDACnE,YAAY,WAAW;;;;;;;;;;;;;sDAI5B,8OAAC;4CAAK,UAAU;;8DACd,8OAAC;oDAAI,WAAU;;sEAEb,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAAkE;;;;;;8EAGnF,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EACC,MAAK;4EACL,MAAK;4EACL,OAAO,SAAS,SAAS;4EACzB,UAAU;4EACV,UAAU,CAAC;4EACX,WAAU;4EACV,aAAY;;;;;;sFAEd,8OAAC;4EAAI,WAAU;sFACb,cAAA,8OAAC;gFAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;sEAMnB,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAAkE;;;;;;8EAGnF,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EACC,MAAK;4EACL,MAAK;4EACL,OAAO,SAAS,QAAQ;4EACxB,UAAU;4EACV,UAAU,CAAC;4EACX,WAAU;4EACV,aAAY;;;;;;sFAEd,8OAAC;4EAAI,WAAU;sFACb,cAAA,8OAAC;gFAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;sEAMnB,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAAkE;;;;;;8EAGnF,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EACC,MAAK;4EACL,MAAK;4EACL,OAAO,SAAS,KAAK;4EACrB,UAAU;4EACV,UAAU,CAAC;4EACX,WAAU;4EACV,aAAY;;;;;;sFAEd,8OAAC;4EAAI,WAAU;sFACb,cAAA,8OAAC;gFAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;sEAMnB,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAAkE;;;;;;8EAGnF,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EACC,MAAK;4EACL,MAAK;4EACL,OAAO,SAAS,KAAK;4EACrB,UAAU;4EACV,UAAU,CAAC;4EACX,WAAU;4EACV,aAAY;;;;;;sFAEd,8OAAC;4EAAI,WAAU;sFACb,cAAA,8OAAC;gFAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;sEAMnB,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAAkE;;;;;;8EAGnF,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EACC,MAAK;4EACL,MAAK;4EACL,OAAO,SAAS,gBAAgB;4EAChC,UAAU;4EACV,UAAU,CAAC;4EACX,WAAU;4EACV,aAAY;;;;;;sFAEd,8OAAC;4EAAI,WAAU;sFACb,cAAA,8OAAC;gFAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;sEAMnB,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAAkE;;;;;;8EAGnF,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EACC,MAAK;4EACL,MAAK;4EACL,OAAO,SAAS,IAAI;4EACpB,UAAU;4EACV,UAAU,CAAC;4EACX,WAAU;4EACV,aAAY;;;;;;sFAEd,8OAAC;4EAAI,WAAU;sFACb,cAAA,8OAAC;gFAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8DAOrB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,WAAU;sEAAkE;;;;;;sEAGnF,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEACC,MAAK;oEACL,MAAM;oEACN,OAAO,SAAS,OAAO;oEACvB,UAAU;oEACV,UAAU,CAAC;oEACX,WAAU;oEACV,aAAY;;;;;;8EAEd,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;gDAKlB,2BACC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,MAAK;4DACL,SAAS,IAAM,aAAa;4DAC5B,WAAU;sEACX;;;;;;sEAGD,8OAAC;4DACC,MAAK;4DACL,UAAU;4DACV,WAAU;sEAET,wBACC;;kFACE,8OAAC;wEAAE,WAAU;;;;;;oEAAyC;;6FAIxD;;kFACE,8OAAC;wEAAE,WAAU;;;;;;oEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;gCAYpD,cAAc,4BACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuD;;;;;;sDAKrE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA4D;;;;;;8DAG1E,8OAAC;oDAAK,UAAU;oDAAsB,WAAU;;sEAC9C,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAAkE;;;;;;8EAGnF,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EACC,MAAK;4EACL,MAAK;4EACL,OAAO,aAAa,eAAe;4EACnC,UAAU;4EACV,WAAU;4EACV,aAAY;;;;;;sFAEd,8OAAC;4EAAI,WAAU;sFACb,cAAA,8OAAC;gFAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;sEAKnB,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAAkE;;;;;;8EAGnF,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EACC,MAAK;4EACL,MAAK;4EACL,OAAO,aAAa,WAAW;4EAC/B,UAAU;4EACV,WAAU;4EACV,aAAY;;;;;;sFAEd,8OAAC;4EAAI,WAAU;sFACb,cAAA,8OAAC;gFAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;sEAKnB,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAAkE;;;;;;8EAGnF,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EACC,MAAK;4EACL,MAAK;4EACL,OAAO,aAAa,eAAe;4EACnC,UAAU;4EACV,WAAU;4EACV,aAAY;;;;;;sFAEd,8OAAC;4EAAI,WAAU;sFACb,cAAA,8OAAC;gFAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;sEAKnB,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEACC,MAAK;gEACL,UAAU;gEACV,WAAU;0EAET,wBACC;;sFACE,8OAAC;4EAAE,WAAU;;;;;;wEAAyC;;iGAIxD;;sFACE,8OAAC;4EAAE,WAAU;;;;;;wEAAgC;;;;;;;;;;;;;;;;;;;;;;;;;sDAUzD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA4D;;;;;;8DAG1E,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAE,WAAU;;;;;;sFACb,8OAAC;;8FACC,8OAAC;oFAAE,WAAU;8FAAuD;;;;;;8FACpE,8OAAC;oFAAE,WAAU;8FAA2C;;;;;;;;;;;;;;;;;;8EAG5D,8OAAC;oEAAO,WAAU;8EACf,MAAM,qBAAqB,YAAY;;;;;;;;;;;;sEAG5C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAE,WAAU;;;;;;sFACb,8OAAC;;8FACC,8OAAC;oFAAE,WAAU;8FAAuD;;;;;;8FACpE,8OAAC;oFAAE,WAAU;8FAA2C;;;;;;;;;;;;;;;;;;8EAG5D,8OAAC;oEAAO,WAAU;8EAAoF;;;;;;;;;;;;sEAIxG,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAE,WAAU;;;;;;sFACb,8OAAC;;8FACC,8OAAC;oFAAE,WAAU;8FAAuD;;;;;;8FACpE,8OAAC;oFAAE,WAAU;8FAA2C;;;;;;;;;;;;;;;;;;8EAG5D,8OAAC,4JAAA,CAAA,UAAI;oEACH,MAAK;oEACL,WAAU;8EACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAUV,cAAc,+BACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuD;;;;;;sDAKrE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA4D;;;;;;8DAG1E,8OAAC;oDAAI,WAAU;8DACZ;wDACC;4DAAE,IAAI;4DAAuB,OAAO;4DAAuB,aAAa;wDAA4B;wDACpG;4DAAE,IAAI;4DAAkB,OAAO;4DAAyB,aAAa;wDAAsC;wDAC3G;4DAAE,IAAI;4DAAqB,OAAO;4DAAqB,aAAa;wDAAoC;wDACxG;4DAAE,IAAI;4DAAuB,OAAO;4DAAuB,aAAa;wDAAoC;qDAC7G,CAAC,GAAG,CAAC,CAAC,qBACL,8OAAC;4DAAkB,WAAU;;8EAC3B,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEACC,IAAI,KAAK,EAAE;wEACX,MAAK;wEACL,cAAc;wEACd,WAAU;wEACV,oBAAkB,GAAG,KAAK,EAAE,CAAC,YAAY,CAAC;;;;;;;;;;;8EAG9C,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EACC,SAAS,KAAK,EAAE;4EAChB,WAAU;sFAET,KAAK,KAAK;;;;;;sFAEb,8OAAC;4EACC,IAAI,GAAG,KAAK,EAAE,CAAC,YAAY,CAAC;4EAC5B,WAAU;sFAET,KAAK,WAAW;;;;;;;;;;;;;2DArBb,KAAK,EAAE;;;;;;;;;;;;;;;;sDA8BvB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA4D;;;;;;8DAG1E,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAA2C;;;;;;sEAKxD,8OAAC;4DAAI,WAAU;sEACZ;gEACC;oEACE,OAAO;oEACP,OAAO;oEACP,aAAa;oEACb,MAAM;gEACR;gEACA;oEACE,OAAO;oEACP,OAAO;oEACP,aAAa;oEACb,MAAM;gEACR;gEACA;oEACE,OAAO;oEACP,OAAO;oEACP,aAAa;oEACb,MAAM;gEACR;6DACD,CAAC,GAAG,CAAC,CAAC,uBACL,8OAAC;oEAEC,SAAS,IAAM,SAAS,OAAO,KAAK;oEACpC,WAAW,CAAC,6DAA6D,EACvE,UAAU,OAAO,KAAK,GAClB,gDACA,mHACJ;;sFAEF,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAW,CAAC,2DAA2D,EAC1E,UAAU,OAAO,KAAK,GAClB,8DACA,iEACJ;8FACA,cAAA,8OAAC;wFAAE,WAAW,GAAG,OAAO,IAAI,CAAC,QAAQ,CAAC;;;;;;;;;;;8FAExC,8OAAC;oFAAG,WAAW,CAAC,oBAAoB,EAClC,UAAU,OAAO,KAAK,GAClB,mCACA,oCACJ;8FACC,OAAO,KAAK;;;;;;8FAEf,8OAAC;oFAAE,WAAW,CAAC,aAAa,EAC1B,UAAU,OAAO,KAAK,GAClB,mCACA,oCACJ;8FACC,OAAO,WAAW;;;;;;;;;;;;wEAGtB,UAAU,OAAO,KAAK,kBACrB,8OAAC;4EAAI,WAAU;sFACb,cAAA,8OAAC;gFAAI,WAAU;0FACb,cAAA,8OAAC;oFAAE,WAAU;;;;;;;;;;;;;;;;;mEAlCd,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;sDA6C3B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA4D;;;;;;8DAG1E,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,qIAAA,CAAA,UAAM;4DACL,OAAM;4DACN,cAAW;;8EAEX,8OAAC;8EAAO;;;;;;8EACR,8OAAC;8EAAO;;;;;;;;;;;;sEAEV,8OAAC,qIAAA,CAAA,UAAM;4DACL,OAAM;4DACN,cAAW;;8EAEX,8OAAC;8EAAO;;;;;;8EACR,8OAAC;8EAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW9B;uCAEe", "debugId": null}}]}