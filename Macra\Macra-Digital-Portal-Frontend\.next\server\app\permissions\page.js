(()=>{var e={};e.id=8625,e.ids=[8625],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3362:(e,r,t)=>{Promise.resolve().then(t.bind(t,43325))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},22158:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var s=t(60687),a=t(43210),i=t(16189),n=t(63213),o=t(21891),l=t(60417);function d({children:e}){let{isAuthenticated:r,loading:t}=(0,n.A)();(0,i.useRouter)();let[d,c]=(0,a.useState)("overview"),[p,u]=(0,a.useState)(!1);return t?(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"})}):r?(0,s.jsxs)("div",{className:"flex h-screen overflow-hidden",children:[(0,s.jsx)("div",{id:"mobileSidebarOverlay",className:`mobile-sidebar-overlay ${p?"show":""}`,onClick:()=>u(!1)}),(0,s.jsx)(l.default,{}),(0,s.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,s.jsx)(o.default,{activeTab:d,onTabChange:c,onMobileMenuToggle:()=>{u(!p)}}),e]})]}):null}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38144:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\permissions\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\permissions\\layout.tsx","default")},43325:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var s=t(60687),a=t(63224),i=t(43210),n=t(70088);function o(){let[e,r]=(0,i.useState)(null),[t,o]=(0,i.useState)(!0),[l,d]=(0,i.useState)(null),c=async e=>{try{o(!0),d(null);let t=await a.p.getPermissions(e);r(t)}catch(t){d("Failed to load permissions"),r({data:[],meta:{itemsPerPage:e.limit||10,totalItems:0,currentPage:e.page||1,totalPages:0,sortBy:[],searchBy:[],search:"",select:[]},links:{current:""}})}finally{o(!1)}},p=[{key:"name",label:"Permission Name",sortable:!0,render:e=>(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:e.replace(/[_:]/g," ").replace(/\b\w/g,e=>e.toUpperCase())})},{key:"description",label:"Description",render:e=>(0,s.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:e||"No description"})},{key:"category",label:"Category",sortable:!0,render:e=>(0,s.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200",children:e})},{key:"roles",label:"Assigned Roles",render:(e,r)=>(0,s.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:r.roles&&r.roles.length>0?r.roles.map(e=>e.name).join(", "):"None"})},{key:"created_at",label:"Created Date",sortable:!0,render:e=>(0,s.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:e?new Date(e).toLocaleDateString():"N/A"})}];return(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsx)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:"Permissions"}),(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"View and manage system permissions."})]})})}),l&&(0,s.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded",children:l}),(0,s.jsx)(n.A,{columns:p,data:e,loading:t,onQueryChange:c,searchPlaceholder:"Search permissions by name, description, or category..."})]})}},50199:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\permissions\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\permissions\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57661:(e,r,t)=>{Promise.resolve().then(t.bind(t,38144))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63224:(e,r,t)=>{"use strict";t.d(r,{p:()=>a});var s=t(12234);process.env.NEXT_PUBLIC_API_URL;let a={async getPermissions(e={page:1,limit:10}){let r=new URLSearchParams;return r.set("page",e.page?.toString()||"1"),r.set("limit",e.limit?.toString()||"10"),e.search&&r.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>r.append("sortBy",e)),e.filter&&Object.entries(e.filter).forEach(([e,t])=>{Array.isArray(t)?t.forEach(t=>r.append(`filter.${e}`,t)):r.set(`filter.${e}`,t)}),(await s.uE.get(`/permissions?${r.toString()}`)).data},async getAllPermissions(){try{let e=await s.uE.get("/permissions/by-category"),r=e.data?.data||e.data;if(!r)return[];if(Array.isArray(r))return r;let t=[];return Object.values(r).forEach(e=>{Array.isArray(e)&&t.push(...e)}),t}catch(e){return[]}},async getPermissionsByCategory(){try{let e=await s.uE.get("/permissions/by-category"),r=e.data?.data||e.data;if(!r)return{};return r}catch(e){return{}}},async getPermission(e){let r=await s.uE.get(`/permissions/${e}`);return r.data?.data||r.data},async createPermission(e){let r=await s.uE.post("/permissions",e);return r.data?.data||r.data},async updatePermission(e,r){let t=await s.uE.patch(`/permissions/${e}`,r);return t.data?.data||t.data},async deletePermission(e){await s.uE.delete(`/permissions/${e}`)}}},69277:(e,r,t)=>{Promise.resolve().then(t.bind(t,22158))},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85434:(e,r,t)=>{Promise.resolve().then(t.bind(t,50199))},90013:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=t(65239),a=t(48088),i=t(88170),n=t.n(i),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d={children:["",{children:["permissions",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,50199)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\permissions\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,38144)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\permissions\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\permissions\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/permissions/page",pathname:"/permissions",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,7498,1658,5814,2335,6606,88],()=>t(90013));module.exports=s})();