import { Repository } from 'typeorm';
import { User, UserStatus } from '../entities/user.entity';
import { Role } from '../entities/role.entity';
import { CreateUserDto } from '../dto/user/create-user.dto';
import { UpdateUserDto } from '../dto/user/update-user.dto';
import { UpdateProfileDto } from '../dto/user/update-profile.dto';
import { ChangePasswordDto } from '../dto/user/change-password.dto';
import { PaginateQuery } from 'nestjs-paginate';
import { PaginatedResult } from '../common/interfaces/pagination.interface';
import { UserCreationResult, UserUpdateResult, PasswordChangeResult, AvatarUploadResult } from '../common/types/user.types';
export declare class UsersService {
    private usersRepository;
    private rolesRepository;
    constructor(usersRepository: Repository<User>, rolesRepository: Repository<Role>);
    findByEmail(email: string): Promise<User | null>;
    findById(id: string): Promise<User | null>;
    create(createUserDto: CreateUserDto): UserCreationResult;
    private validateUserDoesNotExist;
    private validateAndGetRoles;
    private hashPassword;
    validatePassword(plainPassword: string, hashedPassword: string): Promise<boolean>;
    updateLastLogin(userId: string): Promise<void>;
    updatePassword(userId: string, newPassword: string): Promise<void>;
    setTwoFactorCode(userId: string, code: string, expiresAt: Date): Promise<void>;
    setTempTwoFactorCode(userId: string, secret: string, expiresAt: Date): Promise<void>;
    clearTempTwoFactorCode(userId: string): Promise<void>;
    clearTwoFactorCode(userId: string): Promise<void>;
    setTwoFactorCodeTempReset(userId: string, secret: string, code: string, expiresAt: Date): Promise<void>;
    disableTwoFactor(userId: string): Promise<void>;
    enableTwoFactor(userId: string, expiresAt: Date): Promise<void>;
    verifyEmail(userId: string): Promise<void>;
    updateStatus(userId: string, status: UserStatus): Promise<void>;
    findAll(query: PaginateQuery): Promise<PaginatedResult<User>>;
    update(userId: string, updateUserDto: UpdateUserDto): UserUpdateResult;
    private validateUserExists;
    private validateRolesForUpdate;
    private getUpdatedUser;
    delete(userId: string): Promise<void>;
    updateProfile(userId: string, updateProfileDto: UpdateProfileDto): Promise<User>;
    private validateEmailNotTaken;
    changePassword(userId: string, changePasswordDto: ChangePasswordDto): PasswordChangeResult;
    uploadAvatar(userId: string, file: Express.Multer.File): AvatarUploadResult;
    removeAvatar(userId: string): Promise<User>;
    mailUser(userEmail: string): Promise<{
        message: string;
    }>;
}
