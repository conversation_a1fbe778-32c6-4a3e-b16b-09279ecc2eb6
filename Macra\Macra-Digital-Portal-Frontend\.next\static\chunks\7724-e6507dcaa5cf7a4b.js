"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7724],{10012:(e,r,t)=>{t.d(r,{Hm:()=>i,Wf:()=>o,_4:()=>l,zp:()=>d});var a=t(57383),s=t(79323);let i=e=>{if(!e)return!0;try{let r=JSON.parse(atob(e.split(".")[1])),t=Math.floor(Date.now()/1e3);return r.exp<t}catch(e){return!0}},n=()=>{let e=(0,s.c4)(),r=a.A.get("auth_user");if(!e||i(e)||!r)return!1;try{return JSON.parse(r),!0}catch(e){return!1}},o=()=>{(0,s.QF)(),a.A.remove("auth_token"),a.A.remove("auth_user");{localStorage.removeItem("auth_token"),localStorage.removeItem("user_preferences"),sessionStorage.clear();let e=window.location.pathname.startsWith("/customer")||window.location.hostname.includes("customer");window.location.href=e?"/customer/auth/login":"/auth/login"}},l=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:6e4;return setInterval(()=>{n()||o()},e)},d=e=>{var r,t;return(null==e||null==(r=e.data)?void 0:r.meta)!==void 0&&e.data.data?e.data:(null==e||null==(t=e.data)?void 0:t.data)?e.data.data:(e.data,e.data)}},30606:(e,r,t)=>{t.d(r,{A:()=>i});var a=t(95155);let s=(0,t(12115).forwardRef)((e,r)=>{let{label:t,error:s,helperText:i,variant:n="default",fullWidth:o=!0,className:l="",required:d,disabled:c,rows:m=3,...u}=e,g="px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 resize-y ".concat(o?"w-full":""," ").concat("small"===n?"py-1.5 text-sm":"py-2"),p="".concat(g," ").concat(s?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"," ").concat(c?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""," ").concat(l);return(0,a.jsxs)("div",{className:"w-full",children:[t&&(0,a.jsxs)("label",{className:"block font-medium text-gray-700 dark:text-gray-300 mb-2 ".concat("small"===n?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"),children:[t,d&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,a.jsx)("textarea",{ref:r,className:p,disabled:c,required:d,rows:m,...u}),s&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:s}),i&&!s&&(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:i})]})});s.displayName="TextArea";let i=s},40165:(e,r,t)=>{t.d(r,{A:()=>l});var a=t(95155),s=t(12115),i=t(69733),n=t(61967),o=t(30606);function l(e){let{isOpen:r,onClose:t,onSave:l,role:d,permissions:c=[]}=e,[m,u]=(0,s.useState)({name:"",description:"",permission_ids:[]}),[g,p]=(0,s.useState)(!1),[y,x]=(0,s.useState)(null),h=(c||[]).reduce((e,r)=>(e[r.category]||(e[r.category]=[]),e[r.category].push(r),e),{});(0,s.useEffect)(()=>{if(d){var e;u({name:d.name||"",description:d.description||"",permission_ids:(null==(e=d.permissions)?void 0:e.map(e=>e.permission_id))||[]})}else u({name:"",description:"",permission_ids:[]});x(null)},[d,r,c]);let b=async e=>{if(e.preventDefault(),p(!0),x(null),!m.name.trim()){x("Role name is required"),p(!1);return}if(m.name.trim().length<2){x("Role name must be at least 2 characters long"),p(!1);return}try{if(d){let e={name:m.name.trim(),description:m.description.trim()||void 0,permission_ids:m.permission_ids};await i.O.updateRole(d.role_id,e)}else{let e={name:m.name.trim(),description:m.description.trim()||void 0,permission_ids:m.permission_ids};await i.O.createRole(e)}l(m.name.trim(),!!d)}catch(e){var r,t;x((null==(t=e.response)||null==(r=t.data)?void 0:r.message)||"Failed to save role")}finally{p(!1)}},f=e=>{let{name:r,value:t}=e.target;u(e=>({...e,[r]:t}))},v=e=>{u(r=>({...r,permission_ids:r.permission_ids.includes(e)?r.permission_ids.filter(r=>r!==e):[...r.permission_ids,e]}))},k=e=>{let r=h[e].map(e=>e.permission_id);r.every(e=>m.permission_ids.includes(e))?u(e=>({...e,permission_ids:e.permission_ids.filter(e=>!r.includes(e))})):u(e=>({...e,permission_ids:[...new Set([...e.permission_ids,...r])]}))};return r?(0,a.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,a.jsxs)("div",{className:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 transition-opacity",onClick:t}),(0,a.jsx)("div",{className:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full",children:(0,a.jsxs)("form",{onSubmit:b,children:[(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:(0,a.jsx)("div",{className:"sm:flex sm:items-start",children:(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4",children:d?"Edit Role":"Create New Role"}),y&&(0,a.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded",children:y}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(n.A,{type:"text",name:"name",label:"Role Name",required:!0,value:m.name,onChange:f,placeholder:"Enter role name (e.g., Administrator, Manager, etc.)"}),(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:"Choose a descriptive name for this role"})]}),(0,a.jsx)(o.A,{name:"description",label:"Description",rows:3,value:m.description,onChange:f,placeholder:"Describe the role and its responsibilities..."}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:["Selected Permissions: ",m.permission_ids.length]}),(0,a.jsx)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Select permissions to assign to this role"})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Permissions"}),(0,a.jsx)("div",{className:"max-h-96 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700",children:c&&c.length>0?Object.keys(h).length>0?Object.entries(h).map(e=>{let[r,t]=e,s=t.map(e=>e.permission_id),i=s.every(e=>m.permission_ids.includes(e)),n=s.some(e=>m.permission_ids.includes(e));return(0,a.jsxs)("div",{className:"border-b border-gray-200 dark:border-gray-600 last:border-b-0",children:[(0,a.jsx)("div",{className:"px-4 py-3 bg-gray-50 dark:bg-gray-600",children:(0,a.jsxs)("label",{className:"flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:i,ref:e=>{e&&(e.indeterminate=n&&!i)},onChange:()=>k(r),className:"h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 dark:border-gray-500 rounded bg-white dark:bg-gray-700"}),(0,a.jsx)("span",{className:"ml-2 text-sm font-medium text-gray-900 dark:text-gray-100",children:r}),(0,a.jsxs)("span",{className:"ml-2 text-xs text-gray-500 dark:text-gray-400",children:["(",t.length,")"]})]})}),(0,a.jsx)("div",{className:"px-4 py-2 space-y-2 bg-white dark:bg-gray-700",children:t.map(e=>(0,a.jsxs)("label",{className:"flex items-start cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-600 p-2 rounded",children:[(0,a.jsx)("input",{type:"checkbox",checked:m.permission_ids.includes(e.permission_id),onChange:()=>v(e.permission_id),className:"h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 dark:border-gray-500 rounded bg-white dark:bg-gray-700 mt-0.5"}),(0,a.jsxs)("div",{className:"ml-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-900 dark:text-gray-100",children:e.name.replace(/[_:]/g," ").replace(/\b\w/g,e=>e.toUpperCase())}),(0,a.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:e.description})]})]},e.permission_id))})]},r)}):(0,a.jsx)("div",{className:"p-4 text-center text-gray-500 dark:text-gray-400",children:"No permissions available"}):(0,a.jsx)("div",{className:"p-4 text-center text-gray-500 dark:text-gray-400",children:"Loading permissions..."})})]})]})]})})}),(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,a.jsx)("button",{type:"submit",disabled:g,className:"inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed w-full sm:w-auto sm:ml-3 transition-colors duration-200",children:g?(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Saving..."]}):d?"Update Role":"Create Role"}),(0,a.jsx)("button",{type:"button",onClick:t,className:"inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 mt-3 w-full sm:mt-0 sm:ml-3 sm:w-auto transition-colors duration-200",children:"Cancel"})]})]})})]})}):null}},52956:(e,r,t)=>{t.d(r,{Gf:()=>c,Y0:()=>d,Zl:()=>u,rV:()=>m,uE:()=>l});var a=t(23464),s=t(79323),i=t(10012);let n=t(49509).env.NEXT_PUBLIC_API_URL||"http://localhost:3001",o=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:n,r=a.A.create({baseURL:e,timeout:12e4,headers:{"Content-Type":"application/json"}});return r.interceptors.request.use(async e=>{let r=(0,s.c4)();return r&&(e.headers.Authorization="Bearer ".concat(r)),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,async e=>{var t,a,s,n,o,l;let d=e.config;if((null==(t=e.response)?void 0:t.status)===429&&d&&!d._retry){d._retry=!0;let t=e.response.headers["retry-after"],a=t?1e3*parseInt(t):Math.min(1e3*Math.pow(2,d._retryCount||0),1e4);if(d._retryCount=(d._retryCount||0)+1,d._retryCount<=10)return await new Promise(e=>setTimeout(e,a)),r(d)}return(null==(a=e.response)?void 0:a.status)===401?((0,i.Wf)(),Promise.reject(Error("Authentication failed. Please log in again."))):(null==(s=e.response)||s.status,((null==(n=e.response)?void 0:n.status)===409||(null==(o=e.response)?void 0:o.status)===422)&&(null==(l=e.response)||l.data),"ERR_NETWORK"===e.code||e.message,e.code,Promise.reject(e))}),r},l=o(),d=o("".concat(n,"/auth")),c=o("".concat(n,"/users")),m=o("".concat(n,"/roles")),u=o("".concat(n,"/audit-trail"))},61967:(e,r,t)=>{t.d(r,{A:()=>i});var a=t(95155);let s=(0,t(12115).forwardRef)((e,r)=>{let{label:t,error:s,helperText:i,variant:n="default",fullWidth:o=!0,className:l="",required:d,disabled:c,...m}=e,u="px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 ".concat(o?"w-full":""," ").concat("small"===n?"py-1.5 text-sm":"py-2"),g="".concat(u," ").concat(s?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"," ").concat(c?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""," ").concat(l);return(0,a.jsxs)("div",{className:"w-full",children:[t&&(0,a.jsxs)("label",{className:"block font-medium text-gray-700 dark:text-gray-300 mb-2 ".concat("small"===n?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"),children:[t,d&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,a.jsx)("input",{ref:r,className:g,disabled:c,required:d,...m}),s&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:s}),i&&!s&&(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:i})]})});s.displayName="TextInput";let i=s},69733:(e,r,t)=>{t.d(r,{O:()=>i});var a=t(52956),s=t(10012);t(49509).env.NEXT_PUBLIC_API_URL;let i={async getRoles(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=new URLSearchParams;e.page&&r.set("page",e.page.toString()),e.limit&&r.set("limit",e.limit.toString()),e.search&&r.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>r.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>r.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(e=>{let[t,a]=e;Array.isArray(a)?a.forEach(e=>r.append("filter.".concat(t),e)):r.set("filter.".concat(t),a)});let t=await a.rV.get("?".concat(r.toString()));return(0,s.zp)(t)},async getRole(e){let r=await a.rV.get("/".concat(e));return(0,s.zp)(r)},async getRoleWithPermissions(e){let r=await a.rV.get("/".concat(e,"?include=permissions"));return(0,s.zp)(r)},async createRole(e){let r=await a.rV.post("",e);return(0,s.zp)(r)},async updateRole(e,r){let t=await a.rV.patch("/".concat(e),r);return(0,s.zp)(t)},async deleteRole(e){await a.rV.delete("/".concat(e))},async assignPermissions(e,r){let t=await a.rV.post("/".concat(e,"/permissions"),{permission_ids:r});return(0,s.zp)(t)},async removePermissions(e,r){let t=await a.rV.delete("/".concat(e,"/permissions"),{data:{permission_ids:r}});return(0,s.zp)(t)},async getPermissions(){let e=await a.uE.get("/permissions");return(0,s.zp)(e)}}},76312:(e,r,t)=>{t.d(r,{p:()=>s});var a=t(52956);t(49509).env.NEXT_PUBLIC_API_URL;let s={async getPermissions(){var e,r;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{page:1,limit:10},s=new URLSearchParams;return s.set("page",(null==(e=t.page)?void 0:e.toString())||"1"),s.set("limit",(null==(r=t.limit)?void 0:r.toString())||"10"),t.search&&s.set("search",t.search),t.sortBy&&t.sortBy.forEach(e=>s.append("sortBy",e)),t.filter&&Object.entries(t.filter).forEach(e=>{let[r,t]=e;Array.isArray(t)?t.forEach(e=>s.append("filter.".concat(r),e)):s.set("filter.".concat(r),t)}),(await a.uE.get("/permissions?".concat(s.toString()))).data},async getAllPermissions(){try{var e;let r=await a.uE.get("/permissions/by-category"),t=(null==(e=r.data)?void 0:e.data)||r.data;if(!t)return[];if(Array.isArray(t))return t;let s=[];return Object.values(t).forEach(e=>{Array.isArray(e)&&s.push(...e)}),s}catch(e){return[]}},async getPermissionsByCategory(){try{var e;let r=await a.uE.get("/permissions/by-category"),t=(null==(e=r.data)?void 0:e.data)||r.data;if(!t)return{};return t}catch(e){return{}}},async getPermission(e){var r;let t=await a.uE.get("/permissions/".concat(e));return(null==(r=t.data)?void 0:r.data)||t.data},async createPermission(e){var r;let t=await a.uE.post("/permissions",e);return(null==(r=t.data)?void 0:r.data)||t.data},async updatePermission(e,r){var t;let s=await a.uE.patch("/permissions/".concat(e),r);return(null==(t=s.data)?void 0:t.data)||s.data},async deletePermission(e){await a.uE.delete("/permissions/".concat(e))}}},79323:(e,r,t)=>{t.d(r,{QF:()=>s,c4:()=>a}),t(49509);let a=()=>localStorage.getItem("auth_token"),s=()=>{localStorage.removeItem("auth_token")}}}]);