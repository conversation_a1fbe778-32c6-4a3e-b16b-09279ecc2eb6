'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import { useAuth } from '@/contexts/AuthContext';
import TextInput from '@/components/common/TextInput';
import { FormMessages } from '@/components/forms/FormMessages';
import { useDynamicNavigation } from '@/hooks/useDynamicNavigation';
import { applicationService } from '@/services/applicationService';
import { applicantService } from '@/services/applicantService';
import { validateSection } from '@/utils/formValidation';

import { CustomerApiService } from '@/lib/customer-api';
import { ApplicationLayout } from '@/components/applications';

const ApplicantInfoPage: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated, loading: authLoading } = useAuth();

  // Create customer API service instance
  const customerApi = new CustomerApiService();

  // Get query parameters
  const licenseCategoryId = searchParams.get('license_category_id');
  const applicationId = searchParams.get('application_id');

  // State


  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Form data state - aligned with Applicant interface
  const [formData, setFormData] = useState({
    name: '',
    business_registration_number: '',
    tpin: '',
    website: '',
    email: '',
    phone: '',
    fax: '',
    level_of_insurance_cover: '',
    date_incorporation: '',
    place_incorporation: ''
  });

  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [isSaving, setIsSaving] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const [applicationCreated, setApplicationCreated] = useState(false);
  const [createdApplicationId, setCreatedApplicationId] = useState<string | null>(null);
  const [loadingWarning, setLoadingWarning] = useState<string | null>(null);

  // Dynamic navigation hook
  const {
    handleNext: dynamicHandleNext,
    handlePrevious: dynamicHandlePrevious,
    nextStep
  } = useDynamicNavigation({
    currentStepRoute: 'applicant-info',
    licenseCategoryId,
    applicationId
  });

  // Form handling functions
  const handleFormChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear success message when user starts making changes
    if (successMessage) {
      setSuccessMessage(null);
    }

    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Save function
  const handleSave = async (navigateAfterSave = false) => {
    console.log('💾 Starting save process...', { navigateAfterSave });
    setIsSaving(true);
    setValidationErrors({});

    try {
      // Validate form data
      console.log('🔍 Validating form data:', formData);
      const validation = validateSection(formData as Record<string, any>, 'applicantInfo');
      if (!validation.isValid) {
        console.error('❌ Validation failed:', validation.errors);
        setValidationErrors(validation.errors || {});
        setIsSaving(false);
        return false;
      }
      console.log('✅ Validation passed');

      let currentApplicationId = applicationId;

      // If no application exists, create one
      if (!currentApplicationId) {
        console.log('Creating new application...');

        // Address handling will be done in the dedicated address page

        // Create applicant data (addresses will be handled in the address page)
        const applicantData = {
          name: formData.name,
          business_registration_number: formData.business_registration_number,
          tpin: formData.tpin,
          website: formData.website,
          email: formData.email,
          phone: formData.phone,
          fax: formData.fax,
          level_of_insurance_cover: formData.level_of_insurance_cover,
          date_incorporation: formData.date_incorporation,
          place_incorporation: formData.place_incorporation
        };

        const applicant = await applicantService.createApplicant(applicantData);
        console.log('Applicant created:', applicant);

        // Then create application
        const applicationNumber = `APP-${Date.now()}-${Math.random().toString(36).substring(2, 11).toUpperCase()}`;
        const applicationData = {
          application_number: applicationNumber,
          license_category_id: licenseCategoryId!,
          applicant_id: applicant.applicant_id,
          status: 'draft' as any
        };

        const application = await applicationService.createApplication(applicationData);
        console.log('✅ Application created:', application);

        currentApplicationId = application.application_id;
        console.log('📝 Setting currentApplicationId:', currentApplicationId);
        setCreatedApplicationId(currentApplicationId);
        setApplicationCreated(true);
      } else {
        // Update existing applicant data
        console.log('Updating existing application...');

        // Get application to find applicant_id
        const application = await applicationService.getApplication(currentApplicationId);
        if (application.applicant_id) {
          // Update existing applicant data




          // Update applicant data (addresses will be handled in the address page)
          const applicantData = {
            name: formData.name,
            business_registration_number: formData.business_registration_number,
            tpin: formData.tpin,
            website: formData.website,
            email: formData.email,
            phone: formData.phone,
            fax: formData.fax,
            level_of_insurance_cover: formData.level_of_insurance_cover,
            date_incorporation: formData.date_incorporation,
            place_incorporation: formData.place_incorporation,
          };

          await applicantService.updateApplicant(application.applicant_id, applicantData);
          console.log('Applicant updated successfully');
        }
      }
      // Update application progress
      try {
        const finalApplicationId = currentApplicationId || createdApplicationId;
        if (finalApplicationId) {
          await applicationService.updateApplication(finalApplicationId, {
            current_step: 2,
            progress_percentage: 18 // ~2/11 steps completed
          });
          console.log('Application progress updated');
        }
      } catch (progressError) {
        console.warn('Failed to update application progress:', progressError);
      }

      // Save form data

      // Show success message
      setSuccessMessage('Applicant information saved successfully!');
      setValidationErrors({}); // Clear any previous errors

      // Auto-hide success message after 5 seconds
      setTimeout(() => {
        setSuccessMessage(null);
      }, 5000);

      console.log('✅ Applicant information saved successfully');

      // Update URL with application_id if it was just created
      const finalApplicationId = currentApplicationId || createdApplicationId;
      if (finalApplicationId && !applicationId) {
        console.log('🔗 Updating URL with new application_id:', finalApplicationId);
        const newParams = new URLSearchParams(searchParams.toString());
        newParams.set('application_id', finalApplicationId);
        const newUrl = `/customer/applications/apply/applicant-info?${newParams.toString()}`;

        // Update the URL without triggering a navigation
        window.history.replaceState({}, '', newUrl);
      }

      // Navigate to next step if requested
      if (navigateAfterSave) {
        console.log('🚀 Navigating to next step...');
        console.log('📋 Navigation IDs - currentApplicationId:', currentApplicationId, 'createdApplicationId:', createdApplicationId, 'finalApplicationId:', finalApplicationId);
        handleNext(finalApplicationId);
      }

      return true; // Indicate success

    } catch (error: any) {
      console.error('❌ Error saving applicant information:', error);
      console.error('Error details:', error.response?.data || error.message);
      setValidationErrors({ save: 'Failed to save application. Please try again.' });
      return false; // Indicate failure
    } finally {
      setIsSaving(false);
    }
  };

  // Save and continue function
  const handleSaveAndContinue = async () => {
    console.log('🚀 Save and Continue clicked');
    const success = await handleSave(true);
    if (!success) {
      console.error('❌ Save failed, not navigating');
    }
    // Navigation is handled in handleSave if successful
  };

  // Authentication check
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/customer/auth/login');
      return;
    }
  }, [isAuthenticated, authLoading, router]);

  // Load data
  useEffect(() => {
    const loadData = async () => {
      try {
        if (!licenseCategoryId) {
          setError('License category ID is required');
          setLoading(false);
          return;
        }
        // Load license category
        const category = await customerApi.getLicenseCategory(licenseCategoryId);
        await customerApi.getLicenseType(category.license_type_id);

        if (!category) {
          setError('License category not found');
          setLoading(false);
          return;
        }

        // License category and type loaded successfully

        if (!category.license_type_id) {
          setError('License category is missing license type information');
          setLoading(false);
          return;
        }

        // Load existing application data if application_id is provided
        if (applicationId) {
          try {
            console.log('🔄 Loading existing application data for ID:', applicationId);
            const application = await applicationService.getApplication(applicationId);
            console.log('📋 Application loaded:', application);

            if (application.applicant_id) {
              console.log('👤 Loading existing applicant data for ID:', application.applicant_id);

              try {
                const applicant = await applicantService.getApplicant(application.applicant_id);
                console.log('👤 Applicant data loaded:', applicant);

                // Auto-populate form with existing applicant data
                const populatedData = {
                  name: applicant.name || '',
                  business_registration_number: applicant.business_registration_number || '',
                  tpin: applicant.tpin || '',
                  website: applicant.website || '',
                  email: applicant.email || '',
                  phone: applicant.phone || '',
                  fax: applicant.fax || '',
                  level_of_insurance_cover: applicant.level_of_insurance_cover || '',
                  date_incorporation: applicant.date_incorporation || '',
                  place_incorporation: applicant.place_incorporation || ''
                };

                console.log('📝 Populating form with data:', populatedData);
                setFormData(prev => {
                  const newData = { ...prev, ...populatedData };
                  console.log('📝 Form data after population:', newData);
                  return newData;
                });

                console.log('✅ Form auto-populated successfully');
              } catch (applicantError: any) {
                console.error('❌ Error loading applicant data:', applicantError);
                console.error('Applicant error details:', {
                  applicantId: application.applicant_id,
                  message: applicantError.message,
                  response: applicantError.response?.data,
                  status: applicantError.response?.status
                });

                // Show a user-friendly message but don't fail the entire page
                console.warn('⚠️ Could not load existing applicant data. You can still edit the application with empty form.');

                // Set warning message for user
                if (applicantError.response?.status === 500) {
                  setLoadingWarning('Unable to load existing applicant data due to a server issue. You can still edit the application, but the form will start empty.');
                  console.warn('🔧 Backend issue detected - applicant service may be temporarily unavailable');
                } else {
                  setLoadingWarning('Could not load existing applicant data. The form will start empty.');
                }
              }
            } else {
              console.log('⚠️ Application exists but no applicant_id found in application:', application);
            }
          } catch (appError: any) {
            console.error('❌ Error loading existing application data:', appError);
            console.error('Error details:', {
              message: appError.message,
              response: appError.response?.data,
              status: appError.response?.status
            });
            // Don't fail the entire load process if application data can't be loaded
          }
        } else {
          console.log('🆕 No application_id provided - creating new application');
        }

        console.log('Data loading completed successfully');
        setLoading(false);
      } catch (err: any) {
        console.error('Error loading data:', err);
        console.error('Error details:', {
          message: err.message,
          response: err.response?.data,
          status: err.response?.status
        });

        // Provide more specific error message
        let errorMessage = 'Failed to load application data';
        if (err.response?.status === 404) {
          errorMessage = `License category not found (ID: ${licenseCategoryId}). Please go back to the applications page and select a valid license category.`;
        } else if (err.response?.status === 401) {
          errorMessage = 'You are not authorized to access this license category. Please log in again.';
        } else if (err.response?.status === 500) {
          errorMessage = 'Server error occurred. Please try again later or contact support.';
        } else if (err.message) {
          errorMessage = `Error: ${err.message}`;
        }

        setError(errorMessage);
        setLoading(false);
      }
    };

    if (isAuthenticated && !authLoading) {
      loadData();
    }
  }, [licenseCategoryId, applicationId, isAuthenticated, authLoading]);

  // Navigation handlers for the ApplicantInfo component
  const handleNext = (overrideApplicationId?: string | null) => {
    console.log('🔄 Navigating to next step...');
    const finalApplicationId = overrideApplicationId || applicationId || createdApplicationId;

    if (finalApplicationId) {
      // Create navigation URL manually since we need to ensure the correct application_id
      const params = new URLSearchParams();
      params.set('license_category_id', licenseCategoryId!);
      params.set('application_id', finalApplicationId);

      // Get the next step from dynamic navigation
      if (nextStep) {
        const nextUrl = `/customer/applications/apply/${nextStep.route}?${params.toString()}`;
        console.log('🔗 Navigating to:', nextUrl);
        router.push(nextUrl);
      } else {
        console.warn('⚠️ No next step available');
      }
    } else {
      console.warn('⚠️ No application ID available for navigation!', {
        overrideApplicationId,
        applicationId,
        createdApplicationId
      });
    }
  };

  const handlePrevious = () => {
    dynamicHandlePrevious();
  };


  // Loading state
  if (authLoading || loading) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading application form...</p>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  // Error state
  if (error) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="mb-4">
              <i className="ri-error-warning-line text-4xl text-red-500"></i>
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              Error Loading Application
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              {error}
            </p>
            <button
              onClick={() => router.push('/customer/applications')}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              <i className="ri-arrow-left-line mr-2"></i>
              Back to Applications
            </button>
          </div>
        </div>
      </CustomerLayout>
    );
  }



  return (
    <CustomerLayout>
      <ApplicationLayout
        onNext={handleSaveAndContinue}
        onPrevious={handlePrevious}
        onSave={async () => {
          console.log('💾 Save button clicked');
          await handleSave(false);
        }}
        showNextButton={true}
        showPreviousButton={true}
        showSaveButton={true}
        nextButtonText={nextStep ? `Save & Continue to ${nextStep.name}` : "Save & Continue"}
        previousButtonText="Back to Applications"
        saveButtonText={applicationId ? 'Save Changes' : 'Create Application'}
        nextButtonDisabled={false}
        isSaving={isSaving}
      >

        {/* Header */}
        <div className="mb-6">
          <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            {applicationId ? 'Edit Applicant Information' : 'Applicant Information'}
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {applicationId
              ? 'Update your applicant information below.'
              : 'Please provide your personal information. This will create your application record.'
            }
          </p>
          {applicationId && !loadingWarning && (
            <div className="mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <p className="text-sm text-green-700 dark:text-green-300">
                ✅ Editing existing application. Your saved information has been loaded.
              </p>
            </div>
          )}
          {loadingWarning && (
            <div className="mt-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
              <p className="text-sm text-yellow-700 dark:text-yellow-300">
                ⚠️ {loadingWarning}
              </p>
            </div>
          )}
          {!applicationId && (
            <div className="mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <p className="text-sm text-blue-700 dark:text-blue-300">
                    <i className="ri-information-line mr-1"></i>
                    Your application will be created when you save this step.
                  </p>
                </div>
              )}
              {applicationCreated && (
                <div className="mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <p className="text-sm text-green-700 dark:text-green-300">
                    <i className="ri-check-line mr-1"></i>
                    Application created: {createdApplicationId?.slice(0, 8)}...
                  </p>
                </div>
              )}
            </div>

            {/* Form Messages */}
            <FormMessages
              successMessage={successMessage}
              errorMessage={validationErrors.save}
              validationErrors={Object.fromEntries(
                Object.entries(validationErrors).filter(([key]) => key !== 'save')
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Business Information */}
              <div className="md:col-span-2">
                <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Business Information</h4>
              </div>

              <div className="md:col-span-2">
                <TextInput
                  label="Business/Organization Name"
                  value={formData.name || ''}
                  onChange={(e) => handleFormChange('name', e.target.value)}
                  placeholder="Enter the full legal name of your business or organization"
                  required
                  error={validationErrors.name}
                />
              </div>

              <TextInput
                label="Business Registration Number"
                value={formData.business_registration_number || ''}
                onChange={(e) => handleFormChange('business_registration_number', e.target.value)}
                placeholder="e.g., BN123456789"
                required
                error={validationErrors.business_registration_number}
              />

              <TextInput
                label="TPIN (Tax Payer Identification Number)"
                value={formData.tpin || ''}
                onChange={(e) => handleFormChange('tpin', e.target.value)}
                placeholder="e.g., 12-345-678-9"
                required
                error={validationErrors.tpin}
              />

              <TextInput
                label="Website"
                type="url"
                value={formData.website || ''}
                onChange={(e) => handleFormChange('website', e.target.value)}
                placeholder="https://www.example.com"
                error={validationErrors.website}
              />

              <TextInput
                label="Date of Incorporation"
                type="date"
                value={formData.date_incorporation || ''}
                onChange={(e) => handleFormChange('date_incorporation', e.target.value)}
                required
                error={validationErrors.date_incorporation}
              />

              <div className="md:col-span-2">
                <TextInput
                  label="Place of Incorporation"
                  value={formData.place_incorporation || ''}
                  onChange={(e) => handleFormChange('place_incorporation', e.target.value)}
                  placeholder="e.g., Blantyre, Malawi"
                  required
                  error={validationErrors.place_incorporation}
                />
              </div>

              {/* Contact Information */}
              <div className="md:col-span-2">
                <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 mt-6">Contact Information</h4>
              </div>

              <TextInput
                label="Email Address"
                type="email"
                value={formData.email || ''}
                onChange={(e) => handleFormChange('email', e.target.value)}
                placeholder="<EMAIL>"
                required
                error={validationErrors.email}
              />

              <TextInput
                label="Phone Number"
                value={formData.phone || ''}
                onChange={(e) => handleFormChange('phone', e.target.value)}
                placeholder="+265 1 234 567"
                required
                error={validationErrors.phone}
              />

              <TextInput
                label="Fax Number"
                value={formData.fax || ''}
                onChange={(e) => handleFormChange('fax', e.target.value)}
                placeholder="+265 1 234 568"
                error={validationErrors.fax}
              />

              <TextInput
                label="Level of Insurance Cover"
                value={formData.level_of_insurance_cover || ''}
                onChange={(e) => handleFormChange('level_of_insurance_cover', e.target.value)}
                placeholder="e.g., $1,000,000 USD"
                error={validationErrors.level_of_insurance_cover}
              />
            </div>



            {/* Error Display */}
            {validationErrors.save && (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                <div className="flex items-center">
                  <i className="ri-error-warning-line text-red-600 dark:text-red-400 text-lg mr-3"></i>
                  <div>
                    <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                      Error Saving Application
                    </h3>
                    <p className="text-red-700 dark:text-red-300 text-sm mt-1">
                      {validationErrors.save}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Success Message */}
            {applicationCreated && !applicationId && (
              <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                <div className="flex items-center">
                  <i className="ri-check-circle-line text-green-600 dark:text-green-400 text-lg mr-3"></i>
                  <div>
                    <h3 className="text-sm font-medium text-green-800 dark:text-green-200">
                      Application Created Successfully!
                    </h3>
                    <p className="text-green-700 dark:text-green-300 text-sm mt-1">
                      Your application has been created. You can now continue to the next step.
                    </p>
                  </div>
                </div>
              </div>
            )}


      </ApplicationLayout>
    </CustomerLayout>
  );
};

export default ApplicantInfoPage;
