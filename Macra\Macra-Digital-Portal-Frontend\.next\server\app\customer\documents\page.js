(()=>{var e={};e.id=7114,e.ids=[7114],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},46354:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\documents\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\documents\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64826:(e,t,r)=>{"use strict";r.d(t,{D:()=>i});var a=r(51278),s=r(12234);let n=new Map,i={async getDocuments(e){try{let t=new URLSearchParams;e?.page&&t.append("page",e.page.toString()),e?.limit&&t.append("limit",e.limit.toString()),e?.search&&t.append("search",e.search),e?.sortBy&&t.append("sortBy",e.sortBy);let r=`/documents?${t.toString()}`;if(n.has(r))return await n.get(r);let i=s.uE.get(r).then(e=>(n.delete(r),(0,a.zp)(e))).catch(e=>{throw n.delete(r),e});return n.set(r,i),await i}catch(e){throw e}},async getDocumentsByEntity(e,t){try{let r=await s.uE.get(`/documents/entity/${e}/${t}`);return(0,a.zp)(r)}catch(e){throw e}},async getDocumentsByApplication(e){try{let t=await s.uE.get(`/documents/by-application/${e}`);return(0,a.zp)(t)}catch(e){throw e}},async getRequiredDocumentsForLicenseCategory(e){try{let t=await s.uE.get(`/license-category-documents/category/${e}`);return(0,a.zp)(t)}catch(e){throw e}},async uploadDocument(e,t){try{let r=new FormData;r.append("file",e),r.append("document_type",t.document_type),r.append("entity_type",t.entity_type),r.append("entity_id",t.entity_id),r.append("is_required",(t.is_required||!1).toString()),r.append("file_name",e.name);let n=await s.uE.post("/documents/upload",r,{headers:{"Content-Type":"multipart/form-data"}}),i=(0,a.zp)(n);return{document:i.data,message:i.message||"Document uploaded successfully"}}catch(e){throw e}},async createDocument(e){try{let t=await s.uE.post("/documents",e);return(0,a.zp)(t)}catch(e){throw e}},async updateDocument(e,t){try{let r=await s.uE.put(`/documents/${e}`,t);return(0,a.zp)(r)}catch(e){throw e}},async deleteDocument(e){try{await s.uE.delete(`/documents/${e}`)}catch(e){throw e}},async getDocument(e){try{let t=await s.uE.get(`/documents/${e}`);return(0,a.zp)(t)}catch(e){throw e}},async downloadDocument(e){try{return(await s.uE.get(`/documents/${e}/download`,{responseType:"blob"})).data}catch(e){throw e}},async previewDocument(e){try{return(await s.uE.get(`/documents/${e}/preview`,{responseType:"blob"})).data}catch(e){throw e}},isPreviewable:(e="")=>!!e&&["application/pdf","image/jpeg","image/jpg","image/png","image/gif","image/webp","text/plain","text/html","text/css","text/javascript","application/json"].includes(e.toLowerCase()),async checkRequiredDocuments(e,t){try{let r=await this.getRequiredDocumentsForLicenseCategory(t),a=(await this.getDocumentsByApplication(e)).data,s=a.map(e=>e.document_type),n=r.filter(e=>e.is_required&&!s.includes(e.name.toLowerCase().replace(/\s+/g,"_")));return{allUploaded:0===n.length,missing:n,uploaded:a}}catch(e){throw e}},getDocumentTypes:()=>["certificate_incorporation","memorandum_association","shareholding_structure","business_plan","financial_statements","technical_proposal","coverage_plan","network_diagram","equipment_specifications","insurance_certificate","tax_clearance","audited_accounts","bank_statement","cv_document","other"],formatDocumentType:e=>e.split("_").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "),mapDocumentNameToType(e){let t={"Certificate of Incorporation":"certificate_incorporation","Memorandum of Association":"memorandum_association","Shareholding Structure":"shareholding_structure","Business Plan":"business_plan","Financial Statements":"financial_statements","Technical Proposal":"technical_proposal","Coverage Plan":"coverage_plan","Network Diagram":"network_diagram","Equipment Specifications":"equipment_specifications","Insurance Certificate":"insurance_certificate","Tax Clearance Certificate":"tax_clearance","Tax Clearance":"tax_clearance","Audited Accounts":"audited_accounts","Bank Statement":"bank_statement","CV Document":"cv_document",Other:"other"};if(t[e])return t[e];let r=e.toLowerCase();for(let[e,a]of Object.entries(t))if(e.toLowerCase()===r)return a;return e.toLowerCase().replace(/\s+/g,"_")},validateFile:(e,t=10,r=[])=>e.size>1024*t*1024?{isValid:!1,error:`File size must be less than ${t}MB`}:r.length>0&&!r.includes(e.type)?{isValid:!1,error:`File type not allowed. Allowed types: ${r.join(", ")}`}:{isValid:!0}}},66818:(e,t,r)=>{Promise.resolve().then(r.bind(r,91325))},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91325:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var a=r(60687),s=r(43210),n=r(16189),i=r(94391),l=r(70088),o=r(64826);let c=({document:e,isOpen:t,onClose:r,onDownload:n})=>{let[i,l]=(0,s.useState)(null),[c,d]=(0,s.useState)(!1),[m,u]=(0,s.useState)(null);(0,s.useEffect)(()=>(e&&t?x():i&&(URL.revokeObjectURL(i),l(null)),()=>{i&&URL.revokeObjectURL(i)}),[e,t]);let x=async()=>{if(e){d(!0),u(null);try{if(!o.D.isPreviewable(e.mime_type)){u(`Preview not available for ${e.mime_type} files. You can download the file instead.`),d(!1);return}let t=await o.D.previewDocument(e.document_id),r=URL.createObjectURL(t);l(r)}catch(e){u(e.message||"Failed to load document preview")}finally{d(!1)}}},p=async()=>{if(e)try{if(n)n(e);else{let t=await o.D.downloadDocument(e.document_id),r=URL.createObjectURL(t),a=window.document.createElement("a");a.href=r,a.download=e.file_name,window.document.body.appendChild(a),a.click(),a.remove(),URL.revokeObjectURL(r)}}catch(e){u("Failed to download document")}};return t&&e?(0,a.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,a.jsxs)("div",{className:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:r}),(0,a.jsxs)("div",{className:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full",children:[(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0 h-10 w-10",children:(0,a.jsx)("div",{className:"h-10 w-10 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center",children:(0,a.jsx)("i",{className:"ri-file-text-line text-gray-500 dark:text-gray-400"})})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:e.file_name}),(0,a.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:[e.mime_type," • ",(e=>{if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]})(e.file_size)," • ",new Date(e.created_at).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("button",{onClick:p,className:"inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,a.jsx)("i",{className:"ri-download-line mr-2"}),"Download"]}),(0,a.jsx)("button",{onClick:r,className:"inline-flex items-center p-2 border border-transparent rounded-md text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:(0,a.jsx)("i",{className:"ri-close-line text-xl"})})]})]})}),(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-900 px-4 py-5 sm:p-6",style:{minHeight:"500px",maxHeight:"70vh"},children:[c&&(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading preview..."})]})}),m&&(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/20 mb-4",children:(0,a.jsx)("i",{className:"ri-error-warning-line text-red-600 dark:text-red-400 text-xl"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Preview Not Available"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mb-4",children:m}),(0,a.jsxs)("button",{onClick:p,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,a.jsx)("i",{className:"ri-download-line mr-2"}),"Download File"]})]})}),i&&!c&&!m&&(0,a.jsx)("div",{className:"w-full h-full",children:e.mime_type.startsWith("image/")?(0,a.jsx)("img",{src:i,alt:e.file_name,className:"max-w-full max-h-full mx-auto object-contain"}):"application/pdf"===e.mime_type?(0,a.jsx)("iframe",{src:i,className:"w-full h-full min-h-96",title:e.file_name}):e.mime_type.startsWith("text/")?(0,a.jsx)("iframe",{src:i,className:"w-full h-full min-h-96 bg-white dark:bg-gray-800",title:e.file_name}):(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"Preview not supported for this file type"})})})]})]})]})}):null},d=({title:e="Documents",subtitle:t="View and manage documents",searchPlaceholder:r="Search documents by name, type, or entity...",showEntityInfo:n=!0,showRequiredColumn:i=!0,showCreatorInfo:d=!1,showActions:m=!0,customActions:u,filterParams:x={},className:p=""})=>{let[h,g]=(0,s.useState)(null),[y,f]=(0,s.useState)(!0),[b,w]=(0,s.useState)(null),[j,v]=(0,s.useState)(null),[_,k]=(0,s.useState)(!1),N=(0,s.useMemo)(()=>x,[JSON.stringify(x)]),D=(0,s.useCallback)(async e=>{f(!0),w(null);try{let t=await o.D.getDocuments({page:e.page,limit:e.limit,search:e.search,sortBy:e.sortBy?.join(","),...N});g(t)}catch(e){e instanceof Error?w(e.message||"Failed to load documents"):w("Failed to load documents")}finally{f(!1)}},[N]);(0,s.useEffect)(()=>{let e=!0;return(async()=>{e&&await D({page:1,limit:10,search:"",sortBy:["created_at:DESC"]})})(),()=>{e=!1}},[]);let C=e=>{if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]},P=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),q=e=>{let t={certificate_incorporation:{color:"bg-blue-100 text-blue-800",label:"Certificate"},memorandum_association:{color:"bg-green-100 text-green-800",label:"Memorandum"},shareholding_structure:{color:"bg-purple-100 text-purple-800",label:"Shareholding"},business_plan:{color:"bg-orange-100 text-orange-800",label:"Business Plan"},financial_statements:{color:"bg-red-100 text-red-800",label:"Financial"},cv:{color:"bg-yellow-100 text-yellow-800",label:"CV"},other:{color:"bg-gray-100 text-gray-800",label:"Other"}},r=t[e?.toLowerCase()||"other"]||t.other;return(0,a.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${r.color}`,children:r.label})},M=async e=>{try{let t=await o.D.downloadDocument(e.document_id),r=window.URL.createObjectURL(t),a=document.createElement("a");a.href=r,a.download=e.file_name,document.body.appendChild(a),a.click(),a.remove(),window.URL.revokeObjectURL(r)}catch(e){w("Failed to download document")}},S=e=>{v(e),k(!0)};return(0,a.jsxs)("div",{className:`space-y-6 ${p}`,children:[(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg",children:(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-700",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:e}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-600 dark:text-gray-400",children:t})]}),(0,a.jsx)("div",{className:"flex items-center space-x-3",children:h?.meta&&(0,a.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:[h.meta.totalItems," document",1!==h.meta.totalItems?"s":""]})})]})})}),b&&(0,a.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-error-warning-line text-red-400"})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-red-800 dark:text-red-200",children:"Error loading documents"}),(0,a.jsx)("div",{className:"mt-2 text-sm text-red-700 dark:text-red-300",children:b})]})]})}),(0,a.jsx)(l.A,{columns:(()=>{let e=[{key:"file_name",label:"Document Name",sortable:!0,render:(e,t)=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0 h-10 w-10",children:(0,a.jsx)("div",{className:"h-10 w-10 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center",children:(0,a.jsx)("i",{className:"ri-file-text-line text-gray-500 dark:text-gray-400"})})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:e}),(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:t.mime_type})]})]})},{key:"document_type",label:"Type",sortable:!0,render:e=>q(e)}];return n&&e.push({key:"entity_type",label:"Related To",sortable:!0,render:(e,t)=>(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100 capitalize",children:e}),t.entity_id&&(0,a.jsxs)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:["ID: ",t.entity_id.substring(0,8),"..."]})]})}),e.push({key:"file_size",label:"Size",sortable:!0,render:e=>(0,a.jsx)("span",{className:"text-sm text-gray-900 dark:text-gray-100",children:C(e)})}),i&&e.push({key:"is_required",label:"Required",sortable:!0,render:e=>(0,a.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${e?"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200":"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"}`,children:e?"Required":"Optional"})}),d&&e.push({key:"creator",label:"Uploaded By",sortable:!1,render:(e,t)=>(0,a.jsx)("div",{children:t.creator?(0,a.jsxs)("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:[t.creator.first_name," ",t.creator.last_name]}):(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Unknown"})})}),e.push({key:"created_at",label:"Uploaded",sortable:!0,render:e=>(0,a.jsx)("span",{className:"text-sm text-gray-900 dark:text-gray-100",children:P(e)})}),m&&e.push({key:"actions",label:"Actions",render:(e,t)=>(0,a.jsx)("div",{className:"flex items-center space-x-2",children:u?u(t):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{onClick:()=>M(t),className:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300",title:"Download",children:(0,a.jsx)("i",{className:"ri-download-line"})}),(0,a.jsx)("button",{onClick:()=>S(t),className:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300",title:"View Details",children:(0,a.jsx)("i",{className:"ri-eye-line"})})]})})}),e})(),data:h,loading:y,onQueryChange:D,searchPlaceholder:r}),(0,a.jsx)(c,{document:j,isOpen:_,onClose:()=>{k(!1),v(null)},onDownload:M})]})};var m=r(63213);let u=()=>{let{isAuthenticated:e,loading:t}=(0,m.A)(),r=(0,n.useRouter)();return((0,s.useEffect)(()=>{t||e||r.push("/customer/auth/login")},[e,t,r]),t)?(0,a.jsx)(i.A,{children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading..."})]})})}):e?(0,a.jsx)(i.A,{children:(0,a.jsx)(d,{title:"My Documents",subtitle:"View and manage all your uploaded documents",searchPlaceholder:"Search documents by name, type, or entity...",showEntityInfo:!0,showRequiredColumn:!0,showCreatorInfo:!1,showActions:!0})}):null}},94735:e=>{"use strict";e.exports=require("events")},98077:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c});var a=r(65239),s=r(48088),n=r(88170),i=r.n(n),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let c={children:["",{children:["customer",{children:["documents",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,46354)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\documents\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,68736)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\documents\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/customer/documents/page",pathname:"/customer/documents",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},98362:(e,t,r)=>{Promise.resolve().then(r.bind(r,46354))}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,7498,1658,5814,2335,6893,88],()=>r(98077));module.exports=a})();