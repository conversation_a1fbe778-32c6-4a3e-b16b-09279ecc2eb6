import { IsEnum, IsOptional, IsString, IsDateString, IsUUID } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ApplicationStatus } from '../../entities/applications.entity';

export class UpdateApplicationStatusDto {
  @ApiProperty({
    description: 'New status for the application',
    example: 'under_review',
    enum: ['draft', 'submitted', 'under_review', 'evaluation', 'approved', 'rejected', 'withdrawn']
  })
  @IsString()
  status: string;

  @ApiPropertyOptional({
    description: 'Comments about the status change',
    example: 'Application has been reviewed and moved to evaluation phase'
  })
  @IsOptional()
  @IsString()
  comments?: string;

  @ApiPropertyOptional({
    description: 'Reason for the status change',
    example: 'All required documents have been submitted and verified'
  })
  @IsOptional()
  @IsString()
  reason?: string;

  @ApiPropertyOptional({
    description: 'Estimated completion date for this status',
    example: '2024-02-15T10:00:00Z'
  })
  @IsOptional()
  @IsDateString()
  estimated_completion_date?: string;

  @ApiPropertyOptional({
    description: 'User ID who is making the status change',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID()
  changed_by?: string;
}

export class ApplicationStatusHistoryResponseDto {
  @ApiProperty({
    description: 'History record ID',
    example: '123e4567-e89b-12d3-a456-************'
  })
  history_id: string;

  @ApiProperty({
    description: 'Application ID',
    example: '123e4567-e89b-12d3-a456-************'
  })
  application_id: string;

  @ApiProperty({
    description: 'Current status',
    example: 'under_review',
    enum: ['draft', 'submitted', 'under_review', 'evaluation', 'approved', 'rejected', 'withdrawn']
  })
  status: string;

  @ApiProperty({
    description: 'Previous status',
    example: 'submitted',
    enum: ['draft', 'submitted', 'under_review', 'evaluation', 'approved', 'rejected', 'withdrawn'],
    nullable: true
  })
  previous_status?: string;

  @ApiProperty({
    description: 'Comments about the status change',
    example: 'Application has been reviewed and moved to evaluation phase',
    nullable: true
  })
  comments?: string;

  @ApiProperty({
    description: 'Reason for the status change',
    example: 'All required documents have been submitted and verified',
    nullable: true
  })
  reason?: string;

  @ApiProperty({
    description: 'User who made the change',
    example: 'John Doe'
  })
  changed_by_name: string;

  @ApiProperty({
    description: 'When the status was changed',
    example: '2024-01-15T10:00:00Z'
  })
  changed_at: Date;

  @ApiProperty({
    description: 'Estimated completion date',
    example: '2024-02-15T10:00:00Z',
    nullable: true
  })
  estimated_completion_date?: Date;
}

export class ApplicationStatusTrackingResponseDto {
  @ApiProperty({
    description: 'Application ID',
    example: '123e4567-e89b-12d3-a456-************'
  })
  application_id: string;

  @ApiProperty({
    description: 'Application number',
    example: 'COU-2024-001'
  })
  application_number: string;

  @ApiProperty({
    description: 'Current status',
    example: 'under_review',
    enum: ['draft', 'submitted', 'under_review', 'evaluation', 'approved', 'rejected', 'withdrawn']
  })
  current_status: string;

  @ApiProperty({
    description: 'Current step in the process (1-6)',
    example: 2
  })
  current_step: number;

  @ApiProperty({
    description: 'Progress percentage (0-100)',
    example: 33
  })
  progress_percentage: number;

  @ApiProperty({
    description: 'When the application was submitted',
    example: '2024-01-10T10:00:00Z',
    nullable: true
  })
  submitted_at?: Date;

  @ApiProperty({
    description: 'When the application was created',
    example: '2024-01-10T09:00:00Z'
  })
  created_at: Date;

  @ApiProperty({
    description: 'When the application was last updated',
    example: '2024-01-15T10:00:00Z'
  })
  updated_at: Date;

  @ApiProperty({
    type: [ApplicationStatusHistoryResponseDto],
    description: 'Status change history'
  })
  status_history: ApplicationStatusHistoryResponseDto[];

  @ApiProperty({
    description: 'Applicant information'
  })
  applicant: {
    name: string;
    email: string;
    business_registration_number: string;
  };

  @ApiProperty({
    description: 'License category information'
  })
  license_category: {
    name: string;
    description: string;
  };
}
