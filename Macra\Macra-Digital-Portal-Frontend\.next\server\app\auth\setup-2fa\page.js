(()=>{var e={};e.id=6297,e.ids=[6297],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55192:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(43210);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{fillRule:"evenodd",d:"M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm-1.72 6.97a.75.75 0 1 0-1.06 1.06L10.94 12l-1.72 1.72a.75.75 0 1 0 1.06 1.06L12 13.06l1.72 1.72a.75.75 0 1 0 1.06-1.06L13.06 12l1.72-1.72a.75.75 0 1 0-1.06-1.06L12 10.94l-1.72-1.72Z",clipRule:"evenodd"}))})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"256x256",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},75361:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["auth",{children:["setup-2fa",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,92125)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\auth\\setup-2fa\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\auth\\setup-2fa\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/setup-2fa/page",pathname:"/auth/setup-2fa",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},79551:e=>{"use strict";e.exports=require("url")},81623:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(60687),a=r(43210),i=r(30474),n=r(16189);r(63443);var o=r(55192),l=r(63213),d=r(71773);function c(){(0,n.useRouter)();let{user:e,token:t,loading:r}=(0,l.A)(),[c,u]=(0,a.useState)(""),[p,x]=(0,a.useState)(""),[m,h]=(0,a.useState)(""),[g,f]=(0,a.useState)(""),[v,b]=(0,a.useState)(""),[j,y]=(0,a.useState)(!1),[w,k]=(0,a.useState)(!1);return r?(0,s.jsx)("div",{className:"min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900",children:(0,s.jsx)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:(0,s.jsx)(d.A,{message:"Checking authentication..."})})}):(0,s.jsxs)("div",{className:"min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900",children:[(0,s.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md text-center",children:[(0,s.jsx)(i.default,{src:"/images/macra-logo.png",alt:"MACRA Logo",width:50,height:50,className:"mx-auto h-16 w-auto"}),(0,s.jsxs)("h2",{className:"mt-6 text-3xl font-extrabold text-gray-900 dark:text-white",children:[w&&!j&&(0,s.jsx)("span",{className:"text-green-600 dark:text-green-300",children:"Success"}),!w&&j&&(0,s.jsx)("span",{className:"text-green-600 dark:text-green-300",children:"Two Factor Authentication Enabled"}),!w&&!j&&"Two Factor Authentication Setup"]})]}),(0,s.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 py-8 px-6 shadow rounded-lg sm:px-10",children:[m&&!j&&(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 mb-4 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center",children:(0,s.jsx)(o.A,{className:"w-10 h-10 animate-pulse shadow-md text-red-600 dark:text-red-300"})}),(0,s.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-md text-center",children:m})]}),(g||j||w)&&(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center text-center text-gray-600 dark:text-gray-400",children:[(0,s.jsx)("div",{className:"w-16 h-16 mb-4 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center animate-bounce shadow-md",children:(0,s.jsx)("svg",{className:"w-8 h-8 text-green-600 dark:text-green-300",fill:"none",stroke:"currentColor",strokeWidth:"3",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5 13l4 4L19 7"})})}),(0,s.jsxs)("div",{children:[g,j&&(0,s.jsx)("p",{className:"text-sm text-gray-400 dark:text-gray-200 p-2",children:"Two-Factor Authentication is already enabled. Please contact support to reset."}),w&&(0,s.jsx)("p",{className:"text-sm text-gray-400 dark:text-gray-200 p-2",children:"This link is valid for 5 minutes and can only be used once."})]})]})]})})]})}},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86731:(e,t,r)=>{Promise.resolve().then(r.bind(r,81623))},92125:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\setup-2fa\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\auth\\setup-2fa\\page.tsx","default")},92307:(e,t,r)=>{Promise.resolve().then(r.bind(r,92125))},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,7498,1658,2335],()=>r(75361));module.exports=s})();