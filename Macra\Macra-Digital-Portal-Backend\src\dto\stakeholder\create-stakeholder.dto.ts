import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>otEmpty,
  <PERSON><PERSON><PERSON><PERSON>,
  IsString,
  <PERSON>U<PERSON><PERSON>,
  Length,
  Max,
} from 'class-validator';
import { StakeholderPosition } from 'src/entities/stakeholders.entity';

export class CreateStakeholderDto {
  @IsUUID('4',{ message: 'Applicant ID not valid!' })
  @IsNotEmpty({ message: 'Applicant ID is required' })
  applicant_id: string;

  @IsString({ message: 'First name contains invalid characters!' })
  @Max(100, { message: 'First name must not exceed 100 characters' })
  @IsNotEmpty({ message: 'First name is required' })
  first_name: string;

  @IsString({ message: 'Last name contains invalid characters!' })
  @Max(100, { message: 'Last name must not exceed 100 characters' })
  @IsNotEmpty({ message: 'Last name is required' })
  last_name: string;

  @IsOptional()
  @IsString({ message: 'Middle name contains invalid characters!' })
  @Length(1, 100)
  middle_name?: string;

  @IsUUID('4',{ message: 'Contact ID is not a valid UUID!' })
  @IsNotEmpty({ message: 'Contact ID is required' })
  contact_id: string;

  @IsString({ message: 'Nationality contains invalid characters!' })
  @Max(50, { message: 'Nationality must not exceed 50 characters' })
  @IsNotEmpty({ message: 'Nationality is required' })
  nationality: string;

  @IsEnum(StakeholderPosition, { message: 'Invalid stakeholder position. Must be one of CEO, Shareholder, Auditor, or Lawyer.' })
  @IsNotEmpty({ message: 'Stakeholder position is required' })
  position: StakeholderPosition;

  @IsString({ message: 'Profile contains invalid characters!' })
  @Max(300, { message: 'Profile must not exceed 300 characters' })
  @IsNotEmpty({ message: 'Profile is required' })
  profile: string;

  @IsUUID('4', { message: 'CV Document ID not valid!' })
  @IsNotEmpty({ message: 'CV Document ID is required' })
  cv_document_id: string;
}
