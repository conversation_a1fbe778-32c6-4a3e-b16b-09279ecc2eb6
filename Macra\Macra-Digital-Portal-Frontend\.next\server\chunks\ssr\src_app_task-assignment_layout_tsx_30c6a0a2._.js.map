{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/app/task-assignment/layout.tsx"], "sourcesContent": ["import { Metadata } from 'next'\r\n\r\nexport const metadata: Metadata = {\r\n  title: 'Task Assignment - MACRA Digital Portal',\r\n  description: 'Assign license applications to officers for processing and evaluation',\r\n}\r\n\r\nexport default function TaskAssignmentLayout({\r\n  children,\r\n}: {\r\n  children: React.ReactNode\r\n}) {\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\r\n      <div className=\"py-6\">\r\n        {children}\r\n      </div>\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,qBAAqB,EAC3C,QAAQ,EAGT;IACC,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACZ;;;;;;;;;;;AAIT", "debugId": null}}]}