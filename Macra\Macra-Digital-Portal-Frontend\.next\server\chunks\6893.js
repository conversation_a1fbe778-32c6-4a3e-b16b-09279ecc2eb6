exports.id=6893,exports.ids=[6893],exports.modules={68736:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n,metadata:()=>a});var s=t(37413);let a={title:"Customer Dashboard - Digital Portal",description:"Customer portal for managing licenses and applications"};function n({children:e}){return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50",children:e})}},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"256x256",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},78335:()=>{},85742:(e,r,t)=>{"use strict";t.d(r,{A:()=>o});var s=t(60687),a=t(43210),n=t(16189),i=t(63213);function o({variant:e="primary",size:r="md",className:t="",showConfirmation:o=!0,redirectTo:c="/auth/login",children:l}){let[d,m]=(0,a.useState)(!1),[u,x]=(0,a.useState)(!1),{logout:h,user:g}=(0,i.A)(),f=(0,n.useRouter)(),p=(0,n.usePathname)(),y=async()=>{if(o&&!u)return void x(!0);m(!0);try{h(),await new Promise(e=>setTimeout(e,100)),p.includes("customer")?f.push("/customer/auth/login"):f.push("/auth/login")}catch(e){}finally{m(!1),x(!1)}},b=`inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed ${(()=>{switch(e){case"primary":default:return"bg-red-600 hover:bg-red-700 text-white border border-red-600";case"secondary":return"bg-white hover:bg-gray-50 text-red-600 border border-red-600";case"text":return"bg-transparent hover:bg-red-50 text-red-600 border-none";case"icon":return"bg-transparent hover:bg-red-50 text-red-600 border-none p-2"}})()} ${"icon"!==e?(()=>{switch(r){case"sm":return"px-3 py-1.5 text-sm";case"md":default:return"px-4 py-2 text-base";case"lg":return"px-6 py-3 text-lg"}})():""} ${t}`;return u?(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-sm mx-4 shadow-xl",children:[(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("svg",{className:"h-6 w-6 text-red-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,s.jsx)("div",{className:"ml-3",children:(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Confirm Logout"})})]}),(0,s.jsx)("div",{className:"mb-4",children:(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:["Are you sure you want to logout",g?.first_name?`, ${g.first_name}`:"","? You will need to login again to access your account."]})}),(0,s.jsxs)("div",{className:"flex space-x-3",children:[(0,s.jsx)("button",{onClick:y,disabled:d,className:"flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 disabled:opacity-50",children:d?"Logging out...":"Yes, Logout"}),(0,s.jsx)("button",{onClick:()=>{x(!1)},className:"flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200",children:"Cancel"})]})]})}):(0,s.jsx)("button",{onClick:y,disabled:d,className:b,title:"Logout",children:l||(0,s.jsx)(s.Fragment,{children:"icon"===e?(0,s.jsx)("svg",{className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"})}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("svg",{className:"h-4 w-4 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"})}),d?"Logging out...":"Logout"]})})})}},94391:(e,r,t)=>{"use strict";t.d(r,{A:()=>x});var s=t(60687),a=t(43210),n=t.n(a),i=t(85814),o=t.n(i),c=t(30474),l=t(16189),d=t(63213),m=t(2677),u=t(85742);let x=({children:e,breadcrumbs:r})=>{let[t,i]=(0,a.useState)(!1),[x,h]=(0,a.useState)(!1),g=(0,l.usePathname)(),f=(0,l.useRouter)(),{user:p,logout:y}=(0,d.A)(),{showLoader:b}=(0,m.M)(),j=(0,a.useMemo)(()=>[{name:"Dashboard",href:"/customer",icon:"ri-dashboard-line",current:"/customer"===g},{name:"My Licenses",href:"/customer/my-licenses",icon:"ri-key-line",current:"/customer/my-licenses"===g},{name:"New Applications",href:"/customer/applications",icon:"ri-file-list-3-line",current:"/customer/applications"===g},{name:"Payments",href:"/customer/payments",icon:"ri-bank-card-line",current:"/customer/payments"===g},{name:"Documents",href:"/customer/documents",icon:"ri-file-text-line",current:"/customer/documents"===g},{name:"Procurement",href:"/customer/procurement",icon:"ri-auction-line",current:"/customer/procurement"===g},{name:"Request Resource",href:"/customer/resources",icon:"ri-hand-heart-line",current:"/customer/resources"===g}],[g]),v=(0,a.useMemo)(()=>[{name:"Data Protection",href:"/customer/data-protection",icon:"ri-shield-keyhole-line"},{name:"Help Center",href:"/customer/help",icon:"ri-question-line"}],[]);(0,a.useEffect)(()=>{let e=setTimeout(()=>{["/customer","/customer/applications","/customer/applications/standards","/customer/payments","/customer/my-licenses","/customer/procurement","/customer/profile","/customer/data-protection","/customer/resources","/customer/help"].forEach(e=>{f.prefetch(e)})},1e3);return()=>clearTimeout(e)},[f]);let k=(0,a.useCallback)(()=>{i(!t)},[t]),N=(0,a.useCallback)((e,r)=>{b({"/customer":"Loading Dashboard...","/customer/my-licenses":"Loading My Licenses...","/customer/applications":"Loading Applications...","/customer/applications/apply/":"Loading Standards License Options...","/customer/payments":"Loading Payments...","/customer/documents":"Loading Documents...","/customer/procurement":"Loading Procurement...","/customer/resources":"Loading Resources...","/customer/data-protection":"Loading Data Protection...","/customer/help":"Loading Help Center...","/customer/profile":"Loading Profile...","/customer/settings":"Loading Settings..."}[e]||`Loading ${r}...`),i(!1)},[b]),w=(0,a.useCallback)(e=>{f.prefetch(e)},[f]),L=(0,a.useCallback)(()=>{h(!x)},[x]);return(0,s.jsxs)("div",{className:"flex h-screen overflow-hidden",children:[t&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden",onClick:()=>i(!1)}),(0,s.jsx)("aside",{className:`
        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out gpu-accelerated
        ${t?"translate-x-0":"-translate-x-full lg:translate-x-0"}
      `,children:(0,s.jsxs)("div",{className:"flex flex-col h-full",children:[(0,s.jsx)("div",{className:"h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700",children:(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)(c.default,{src:"/images/macra-logo.png",alt:"MACRA Logo",className:"max-h-12 w-auto",width:120,height:48,priority:!0})})}),(0,s.jsxs)("nav",{className:"mt-6 px-4 side-nav",children:[(0,s.jsx)("div",{className:"space-y-1",children:j.map(e=>(0,s.jsxs)(o(),{href:e.href,onClick:()=>N(e.href,e.name),onMouseEnter:()=>w(e.href),className:`
                    flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated
                    ${e.current?"bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-r-2 border-red-600 dark:border-red-400":"text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100"}
                  `,children:[(0,s.jsx)("div",{className:`w-5 h-5 flex items-center justify-center mr-3 ${e.current?"text-red-600 dark:text-red-400":""}`,children:(0,s.jsx)("i",{className:e.icon})}),e.name]},e.name))}),(0,s.jsxs)("div",{className:"mt-8",children:[(0,s.jsx)("h3",{className:"px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Support"}),(0,s.jsx)("div",{className:"mt-2 space-y-1",children:v.map(e=>(0,s.jsxs)(o(),{href:e.href,onClick:()=>N(e.href,e.name),onMouseEnter:()=>w(e.href),className:"flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100",children:[(0,s.jsx)("div",{className:"w-5 h-5 flex items-center justify-center mr-3",children:(0,s.jsx)("i",{className:e.icon})}),e.name]},e.name))})]})]}),(0,s.jsx)("div",{className:"absolute bottom-0 w-64 p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(c.default,{className:"h-10 w-10 rounded-full object-cover",src:p?.profile_image||"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp",alt:"Profile",width:40,height:40}),(0,s.jsxs)(o(),{href:"/customer/profile",className:"flex-1 min-w-0",onClick:()=>N("/customer/profile","Profile"),children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 truncate",children:p?`${p.first_name} ${p.last_name}`:"Customer"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 truncate"})]})]})})]})}),(0,s.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,s.jsx)("header",{className:"bg-white dark:bg-gray-800 shadow-sm z-10",children:(0,s.jsxs)("div",{className:"flex items-center justify-between h-16 px-4 sm:px-6",children:[(0,s.jsx)("button",{type:"button",onClick:k,className:"md:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none","aria-label":"Open mobile menu",children:(0,s.jsx)("div",{className:"w-6 h-6 flex items-center justify-center",children:(0,s.jsx)("i",{className:"ri-menu-line ri-lg"})})}),(0,s.jsx)("div",{className:"flex-1",children:r&&r.length>0&&(0,s.jsx)("nav",{className:"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400",children:r.map((e,r)=>(0,s.jsxs)(n().Fragment,{children:[r>0&&(0,s.jsx)("i",{className:"ri-arrow-right-s-line"}),e.href?(0,s.jsx)(o(),{href:e.href,className:"hover:text-primary",children:e.label}):(0,s.jsx)("span",{className:"text-gray-900 dark:text-gray-100",children:e.label})]},r))})}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsxs)("button",{type:"button",className:"flex-shrink-0 p-1 mr-4 text-gray-500 dark:text-gray-400 rounded-full hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none relative",children:[(0,s.jsx)("span",{className:"sr-only",children:"View notifications"}),(0,s.jsx)("div",{className:"w-6 h-6 flex items-center justify-center",children:(0,s.jsx)("i",{className:"ri-notification-3-line ri-lg"})}),(0,s.jsx)("span",{className:"absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white dark:ring-gray-800"})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)("button",{type:"button",onClick:L,className:"flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:[(0,s.jsx)("span",{className:"sr-only",children:"Open user menu"}),(0,s.jsx)(c.default,{className:"h-8 w-8 rounded-full",src:p?.profile_image||"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp",alt:"Profile",width:32,height:32})]}),x&&(0,s.jsx)("div",{className:"absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 dark:ring-gray-700 z-50",children:(0,s.jsxs)("div",{className:"py-1",children:[(0,s.jsx)(o(),{href:"/customer/profile",className:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:()=>{N("/customer/profile","Profile"),h(!1)},children:"Your Profile"}),(0,s.jsx)(u.A,{variant:"text",size:"sm",className:"w-full justify-start text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",showConfirmation:!0,children:"Sign out"})]})})]})]})]})}),(0,s.jsx)("main",{className:"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900",children:(0,s.jsx)("div",{className:"py-6",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e})})})]})]})}},96487:()=>{}};