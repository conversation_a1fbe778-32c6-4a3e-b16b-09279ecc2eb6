(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7114],{6958:(e,t,a)=>{"use strict";a.d(t,{D:()=>i});var r=a(10012),s=a(52956);let n=new Map,i={async getDocuments(e){try{let t=new URLSearchParams;(null==e?void 0:e.page)&&t.append("page",e.page.toString()),(null==e?void 0:e.limit)&&t.append("limit",e.limit.toString()),(null==e?void 0:e.search)&&t.append("search",e.search),(null==e?void 0:e.sortBy)&&t.append("sortBy",e.sortBy);let a="/documents?".concat(t.toString());if(n.has(a))return await n.get(a);let i=s.uE.get(a).then(e=>(n.delete(a),(0,r.zp)(e))).catch(e=>{throw n.delete(a),e});return n.set(a,i),await i}catch(e){throw e}},async getDocumentsByEntity(e,t){try{let a=await s.uE.get("/documents/entity/".concat(e,"/").concat(t));return(0,r.zp)(a)}catch(e){throw e}},async getDocumentsByApplication(e){try{let t=await s.uE.get("/documents/by-application/".concat(e));return(0,r.zp)(t)}catch(e){throw e}},async getRequiredDocumentsForLicenseCategory(e){try{let t=await s.uE.get("/license-category-documents/category/".concat(e));return(0,r.zp)(t)}catch(e){throw e}},async uploadDocument(e,t){try{let a=new FormData;a.append("file",e),a.append("document_type",t.document_type),a.append("entity_type",t.entity_type),a.append("entity_id",t.entity_id),a.append("is_required",(t.is_required||!1).toString()),a.append("file_name",e.name);let n=await s.uE.post("/documents/upload",a,{headers:{"Content-Type":"multipart/form-data"}}),i=(0,r.zp)(n);return{document:i.data,message:i.message||"Document uploaded successfully"}}catch(e){throw e}},async createDocument(e){try{let t=await s.uE.post("/documents",e);return(0,r.zp)(t)}catch(e){throw e}},async updateDocument(e,t){try{let a=await s.uE.put("/documents/".concat(e),t);return(0,r.zp)(a)}catch(e){throw e}},async deleteDocument(e){try{await s.uE.delete("/documents/".concat(e))}catch(e){throw e}},async getDocument(e){try{let t=await s.uE.get("/documents/".concat(e));return(0,r.zp)(t)}catch(e){throw e}},async downloadDocument(e){try{return(await s.uE.get("/documents/".concat(e,"/download"),{responseType:"blob"})).data}catch(e){throw e}},async previewDocument(e){try{return(await s.uE.get("/documents/".concat(e,"/preview"),{responseType:"blob"})).data}catch(e){throw e}},isPreviewable(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return!!e&&["application/pdf","image/jpeg","image/jpg","image/png","image/gif","image/webp","text/plain","text/html","text/css","text/javascript","application/json"].includes(e.toLowerCase())},async checkRequiredDocuments(e,t){try{let a=await this.getRequiredDocumentsForLicenseCategory(t),r=(await this.getDocumentsByApplication(e)).data,s=r.map(e=>e.document_type),n=a.filter(e=>e.is_required&&!s.includes(e.name.toLowerCase().replace(/\s+/g,"_")));return{allUploaded:0===n.length,missing:n,uploaded:r}}catch(e){throw e}},getDocumentTypes:()=>["certificate_incorporation","memorandum_association","shareholding_structure","business_plan","financial_statements","technical_proposal","coverage_plan","network_diagram","equipment_specifications","insurance_certificate","tax_clearance","audited_accounts","bank_statement","cv_document","other"],formatDocumentType:e=>e.split("_").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "),mapDocumentNameToType(e){let t={"Certificate of Incorporation":"certificate_incorporation","Memorandum of Association":"memorandum_association","Shareholding Structure":"shareholding_structure","Business Plan":"business_plan","Financial Statements":"financial_statements","Technical Proposal":"technical_proposal","Coverage Plan":"coverage_plan","Network Diagram":"network_diagram","Equipment Specifications":"equipment_specifications","Insurance Certificate":"insurance_certificate","Tax Clearance Certificate":"tax_clearance","Tax Clearance":"tax_clearance","Audited Accounts":"audited_accounts","Bank Statement":"bank_statement","CV Document":"cv_document",Other:"other"};if(t[e])return t[e];let a=e.toLowerCase();for(let[e,r]of Object.entries(t))if(e.toLowerCase()===a)return r;return e.toLowerCase().replace(/\s+/g,"_")},validateFile(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return e.size>1024*t*1024?{isValid:!1,error:"File size must be less than ".concat(t,"MB")}:a.length>0&&!a.includes(e.type)?{isValid:!1,error:"File type not allowed. Allowed types: ".concat(a.join(", "))}:{isValid:!0}}}},35695:(e,t,a)=>{"use strict";var r=a(18999);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"usePathname")&&a.d(t,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},36990:(e,t,a)=>{Promise.resolve().then(a.bind(a,50182))},50182:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>u});var r=a(95155),s=a(12115),n=a(35695),i=a(73209),l=a(41987),c=a(6958);let o=e=>{let{document:t,isOpen:a,onClose:n,onDownload:i}=e,[l,o]=(0,s.useState)(null),[d,m]=(0,s.useState)(!1),[u,x]=(0,s.useState)(null);(0,s.useEffect)(()=>(t&&a?h():l&&(URL.revokeObjectURL(l),o(null)),()=>{l&&URL.revokeObjectURL(l)}),[t,a]);let h=async()=>{if(t){m(!0),x(null);try{if(!c.D.isPreviewable(t.mime_type)){x("Preview not available for ".concat(t.mime_type," files. You can download the file instead.")),m(!1);return}let e=await c.D.previewDocument(t.document_id),a=URL.createObjectURL(e);o(a)}catch(e){x(e.message||"Failed to load document preview")}finally{m(!1)}}},g=async()=>{if(t)try{if(i)i(t);else{let e=await c.D.downloadDocument(t.document_id),a=URL.createObjectURL(e),r=window.document.createElement("a");r.href=a,r.download=t.file_name,window.document.body.appendChild(r),r.click(),r.remove(),URL.revokeObjectURL(a)}}catch(e){x("Failed to download document")}};return a&&t?(0,r.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,r.jsxs)("div",{className:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,r.jsx)("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:n}),(0,r.jsxs)("div",{className:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full",children:[(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0 h-10 w-10",children:(0,r.jsx)("div",{className:"h-10 w-10 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center",children:(0,r.jsx)("i",{className:"ri-file-text-line text-gray-500 dark:text-gray-400"})})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:t.file_name}),(0,r.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:[t.mime_type," • ",(e=>{if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]})(t.file_size)," • ",new Date(t.created_at).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("button",{onClick:g,className:"inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,r.jsx)("i",{className:"ri-download-line mr-2"}),"Download"]}),(0,r.jsx)("button",{onClick:n,className:"inline-flex items-center p-2 border border-transparent rounded-md text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:(0,r.jsx)("i",{className:"ri-close-line text-xl"})})]})]})}),(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-900 px-4 py-5 sm:p-6",style:{minHeight:"500px",maxHeight:"70vh"},children:[d&&(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading preview..."})]})}),u&&(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/20 mb-4",children:(0,r.jsx)("i",{className:"ri-error-warning-line text-red-600 dark:text-red-400 text-xl"})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Preview Not Available"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mb-4",children:u}),(0,r.jsxs)("button",{onClick:g,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,r.jsx)("i",{className:"ri-download-line mr-2"}),"Download File"]})]})}),l&&!d&&!u&&(0,r.jsx)("div",{className:"w-full h-full",children:t.mime_type.startsWith("image/")?(0,r.jsx)("img",{src:l,alt:t.file_name,className:"max-w-full max-h-full mx-auto object-contain"}):"application/pdf"===t.mime_type?(0,r.jsx)("iframe",{src:l,className:"w-full h-full min-h-96",title:t.file_name}):t.mime_type.startsWith("text/")?(0,r.jsx)("iframe",{src:l,className:"w-full h-full min-h-96 bg-white dark:bg-gray-800",title:t.file_name}):(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"Preview not supported for this file type"})})})]})]})]})}):null},d=e=>{let{title:t="Documents",subtitle:a="View and manage documents",searchPlaceholder:n="Search documents by name, type, or entity...",showEntityInfo:i=!0,showRequiredColumn:d=!0,showCreatorInfo:m=!1,showActions:u=!0,customActions:x,filterParams:h={},className:g=""}=e,[y,p]=(0,s.useState)(null),[f,b]=(0,s.useState)(!0),[w,j]=(0,s.useState)(null),[v,k]=(0,s.useState)(null),[N,_]=(0,s.useState)(!1),D=(0,s.useMemo)(()=>h,[JSON.stringify(h)]),C=(0,s.useCallback)(async e=>{b(!0),j(null);try{var t;let a=await c.D.getDocuments({page:e.page,limit:e.limit,search:e.search,sortBy:null==(t=e.sortBy)?void 0:t.join(","),...D});p(a)}catch(e){e instanceof Error?j(e.message||"Failed to load documents"):j("Failed to load documents")}finally{b(!1)}},[D]);(0,s.useEffect)(()=>{let e=!0;return(async()=>{e&&await C({page:1,limit:10,search:"",sortBy:["created_at:DESC"]})})(),()=>{e=!1}},[]);let S=e=>{if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]},L=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),R=e=>{let t={certificate_incorporation:{color:"bg-blue-100 text-blue-800",label:"Certificate"},memorandum_association:{color:"bg-green-100 text-green-800",label:"Memorandum"},shareholding_structure:{color:"bg-purple-100 text-purple-800",label:"Shareholding"},business_plan:{color:"bg-orange-100 text-orange-800",label:"Business Plan"},financial_statements:{color:"bg-red-100 text-red-800",label:"Financial"},cv:{color:"bg-yellow-100 text-yellow-800",label:"CV"},other:{color:"bg-gray-100 text-gray-800",label:"Other"}},a=t[(null==e?void 0:e.toLowerCase())||"other"]||t.other;return(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(a.color),children:a.label})},E=async e=>{try{let t=await c.D.downloadDocument(e.document_id),a=window.URL.createObjectURL(t),r=document.createElement("a");r.href=a,r.download=e.file_name,document.body.appendChild(r),r.click(),r.remove(),window.URL.revokeObjectURL(a)}catch(e){j("Failed to download document")}},B=e=>{k(e),_(!0)};return(0,r.jsxs)("div",{className:"space-y-6 ".concat(g),children:[(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg",children:(0,r.jsx)("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-700",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:t}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-600 dark:text-gray-400",children:a})]}),(0,r.jsx)("div",{className:"flex items-center space-x-3",children:(null==y?void 0:y.meta)&&(0,r.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:[y.meta.totalItems," document",1!==y.meta.totalItems?"s":""]})})]})})}),w&&(0,r.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("i",{className:"ri-error-warning-line text-red-400"})}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-red-800 dark:text-red-200",children:"Error loading documents"}),(0,r.jsx)("div",{className:"mt-2 text-sm text-red-700 dark:text-red-300",children:w})]})]})}),(0,r.jsx)(l.A,{columns:(()=>{let e=[{key:"file_name",label:"Document Name",sortable:!0,render:(e,t)=>(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0 h-10 w-10",children:(0,r.jsx)("div",{className:"h-10 w-10 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center",children:(0,r.jsx)("i",{className:"ri-file-text-line text-gray-500 dark:text-gray-400"})})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:e}),(0,r.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:t.mime_type})]})]})},{key:"document_type",label:"Type",sortable:!0,render:e=>R(e)}];return i&&e.push({key:"entity_type",label:"Related To",sortable:!0,render:(e,t)=>(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100 capitalize",children:e}),t.entity_id&&(0,r.jsxs)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:["ID: ",t.entity_id.substring(0,8),"..."]})]})}),e.push({key:"file_size",label:"Size",sortable:!0,render:e=>(0,r.jsx)("span",{className:"text-sm text-gray-900 dark:text-gray-100",children:S(e)})}),d&&e.push({key:"is_required",label:"Required",sortable:!0,render:e=>(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(e?"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200":"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"),children:e?"Required":"Optional"})}),m&&e.push({key:"creator",label:"Uploaded By",sortable:!1,render:(e,t)=>(0,r.jsx)("div",{children:t.creator?(0,r.jsxs)("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:[t.creator.first_name," ",t.creator.last_name]}):(0,r.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Unknown"})})}),e.push({key:"created_at",label:"Uploaded",sortable:!0,render:e=>(0,r.jsx)("span",{className:"text-sm text-gray-900 dark:text-gray-100",children:L(e)})}),u&&e.push({key:"actions",label:"Actions",render:(e,t)=>(0,r.jsx)("div",{className:"flex items-center space-x-2",children:x?x(t):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("button",{onClick:()=>E(t),className:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300",title:"Download",children:(0,r.jsx)("i",{className:"ri-download-line"})}),(0,r.jsx)("button",{onClick:()=>B(t),className:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300",title:"View Details",children:(0,r.jsx)("i",{className:"ri-eye-line"})})]})})}),e})(),data:y,loading:f,onQueryChange:C,searchPlaceholder:n}),(0,r.jsx)(o,{document:v,isOpen:N,onClose:()=>{_(!1),k(null)},onDownload:E})]})};var m=a(40283);let u=()=>{let{isAuthenticated:e,loading:t}=(0,m.A)(),a=(0,n.useRouter)();return((0,s.useEffect)(()=>{t||e||a.push("/customer/auth/login")},[e,t,a]),t)?(0,r.jsx)(i.A,{children:(0,r.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading..."})]})})}):e?(0,r.jsx)(i.A,{children:(0,r.jsx)(d,{title:"My Documents",subtitle:"View and manage all your uploaded documents",searchPlaceholder:"Search documents by name, type, or entity...",showEntityInfo:!0,showRequiredColumn:!0,showCreatorInfo:!1,showActions:!0})}):null}}},e=>{var t=t=>e(e.s=t);e.O(0,[8122,6766,6874,283,3209,1987,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(36990)),_N_E=e.O()}]);