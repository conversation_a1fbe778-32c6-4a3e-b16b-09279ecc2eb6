{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/app/customer/layout.tsx"], "sourcesContent": ["import { Metadata } from 'next';\r\n\r\nexport const metadata: Metadata = {\r\n  title: 'Customer Dashboard - Digital Portal',\r\n  description: 'Customer portal for managing licenses and applications',\r\n};\r\n\r\nexport default function CustomerLayout({\r\n  children,\r\n}: {\r\n  children: React.ReactNode;\r\n}) {\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50\">\r\n      {children}\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,eAAe,EACrC,QAAQ,EAGT;IACC,qBACE,8OAAC;QAAI,WAAU;kBACZ;;;;;;AAGP", "debugId": null}}]}