import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LicenseTypesController } from './license-types.controller';
import { LicenseTypesService } from './license-types.service';
import { LicenseTypes } from '../entities/license-types.entity';

@Module({
  imports: [TypeOrmModule.forFeature([LicenseTypes])],
  controllers: [LicenseTypesController],
  providers: [LicenseTypesService],
  exports: [LicenseTypesService],
})
export class LicenseTypesModule {}
