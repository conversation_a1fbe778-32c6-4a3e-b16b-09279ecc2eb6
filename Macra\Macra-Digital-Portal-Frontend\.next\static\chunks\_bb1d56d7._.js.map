{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/LogoutButton.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { useRouter, usePathname } from 'next/navigation';\r\nimport { useAuth } from '../contexts/AuthContext';\r\n\r\ninterface LogoutButtonProps {\r\n  variant?: 'primary' | 'secondary' | 'text' | 'icon';\r\n  size?: 'sm' | 'md' | 'lg';\r\n  className?: string;\r\n  showConfirmation?: boolean;\r\n  redirectTo?: string;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nexport default function LogoutButton({\r\n  variant = 'primary',\r\n  size = 'md',\r\n  className = '',\r\n  showConfirmation = true,\r\n  redirectTo = '/auth/login',\r\n  children,\r\n}: LogoutButtonProps) {\r\n  const [isLoggingOut, setIsLoggingOut] = useState(false);\r\n  const [showConfirm, setShowConfirm] = useState(false);\r\n  const { logout, user } = useAuth();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  const handleLogout = async () => {\r\n    if (showConfirmation && !showConfirm) {\r\n      setShowConfirm(true);\r\n      return;\r\n    }\r\n\r\n    setIsLoggingOut(true);\r\n    \r\n    try {\r\n      // Call logout from context\r\n      logout();\r\n      \r\n      // Small delay to ensure cleanup is complete\r\n      await new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n      if(pathname.includes('customer')){\r\n        // Redirect to specified page\r\n        router.push('/customer/auth/login');\r\n      } else {\r\n        router.push('/auth/login');\r\n      }\r\n      \r\n\r\n      \r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n    } finally {\r\n      setIsLoggingOut(false);\r\n      setShowConfirm(false);\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    setShowConfirm(false);\r\n  };\r\n\r\n  // Base styles for different variants\r\n  const getVariantStyles = () => {\r\n    switch (variant) {\r\n      case 'primary':\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n      case 'secondary':\r\n        return 'bg-white hover:bg-gray-50 text-red-600 border border-red-600';\r\n      case 'text':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none';\r\n      case 'icon':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none p-2';\r\n      default:\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n    }\r\n  };\r\n\r\n  // Size styles\r\n  const getSizeStyles = () => {\r\n    switch (size) {\r\n      case 'sm':\r\n        return 'px-3 py-1.5 text-sm';\r\n      case 'md':\r\n        return 'px-4 py-2 text-base';\r\n      case 'lg':\r\n        return 'px-6 py-3 text-lg';\r\n      default:\r\n        return 'px-4 py-2 text-base';\r\n    }\r\n  };\r\n\r\n  const baseStyles = 'inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\r\n  \r\n  const buttonStyles = `${baseStyles} ${getVariantStyles()} ${variant !== 'icon' ? getSizeStyles() : ''} ${className}`;\r\n\r\n  // Confirmation dialog\r\n  if (showConfirm) {\r\n    return (\r\n      <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n        <div className=\"bg-white rounded-lg p-6 max-w-sm mx-4 shadow-xl\">\r\n          <div className=\"flex items-center mb-4\">\r\n            <div className=\"flex-shrink-0\">\r\n              <svg className=\"h-6 w-6 text-red-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z\" />\r\n              </svg>\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <h3 className=\"text-lg font-medium text-gray-900\">Confirm Logout</h3>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"mb-4\">\r\n            <p className=\"text-sm text-gray-500\">\r\n              Are you sure you want to logout{user?.first_name ? `, ${user.first_name}` : ''}? You will need to login again to access your account.\r\n            </p>\r\n          </div>\r\n          \r\n          <div className=\"flex space-x-3\">\r\n            <button\r\n              onClick={handleLogout}\r\n              disabled={isLoggingOut}\r\n              className=\"flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 disabled:opacity-50\"\r\n            >\r\n              {isLoggingOut ? 'Logging out...' : 'Yes, Logout'}\r\n            </button>\r\n            <button\r\n              onClick={handleCancel}\r\n              className=\"flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200\"\r\n            >\r\n              Cancel\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <button\r\n      onClick={handleLogout}\r\n      disabled={isLoggingOut}\r\n      className={buttonStyles}\r\n      title=\"Logout\"\r\n    >\r\n      {children || (\r\n        <>\r\n          {variant === 'icon' ? (\r\n            <svg className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n            </svg>\r\n          ) : (\r\n            <>\r\n              <svg className=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n              </svg>\r\n              {isLoggingOut ? 'Logging out...' : 'Logout'}\r\n            </>\r\n          )}\r\n        </>\r\n      )}\r\n    </button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAee,SAAS,aAAa,EACnC,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,mBAAmB,IAAI,EACvB,aAAa,aAAa,EAC1B,QAAQ,EACU;;IAClB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe;QACnB,IAAI,oBAAoB,CAAC,aAAa;YACpC,eAAe;YACf;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,2BAA2B;YAC3B;YAEA,4CAA4C;YAC5C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,IAAG,SAAS,QAAQ,CAAC,aAAY;gBAC/B,6BAA6B;gBAC7B,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QAIF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,gBAAgB;YAChB,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,eAAe;IACjB;IAEA,qCAAqC;IACrC,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,cAAc;IACd,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa;IAEnB,MAAM,eAAe,GAAG,WAAW,CAAC,EAAE,mBAAmB,CAAC,EAAE,YAAY,SAAS,kBAAkB,GAAG,CAAC,EAAE,WAAW;IAEpH,sBAAsB;IACtB,IAAI,aAAa;QACf,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAAuB,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CAC3E,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;;;;;;;;;;;;kCAItD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;gCAAwB;gCACH,MAAM,aAAa,CAAC,EAAE,EAAE,KAAK,UAAU,EAAE,GAAG;gCAAG;;;;;;;;;;;;kCAInF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,eAAe,mBAAmB;;;;;;0CAErC,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,6LAAC;QACC,SAAS;QACT,UAAU;QACV,WAAW;QACX,OAAM;kBAEL,0BACC;sBACG,YAAY,uBACX,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,SAAQ;gBAAY,QAAO;0BAC9D,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;qCAGvE;;kCACE,6LAAC;wBAAI,WAAU;wBAAe,MAAK;wBAAO,SAAQ;wBAAY,QAAO;kCACnE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAEtE,eAAe,mBAAmB;;;;;;;;;AAOjD;GAvJwB;;QAUG,kIAAA,CAAA,UAAO;QACjB,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAZN", "debugId": null}}, {"offset": {"line": 277, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/utils/imageUtils.ts"], "sourcesContent": ["/**\r\n * Utility functions for handling profile images\r\n */\r\n\r\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';\r\n\r\n/**\r\n * Get the full URL for a profile image\r\n * @param profileImage - The profile image path from the API\r\n * @returns Full URL for the profile image\r\n */\r\nexport const getProfileImageUrl = (profileImage?: string): string | null => {\r\n  if (!profileImage) return null;\r\n  \r\n  // If it's already a full URL (starts with http), return as is\r\n  if (profileImage.startsWith('http://') || profileImage.startsWith('https://')) {\r\n    return profileImage;\r\n  }\r\n  \r\n  // If it's a relative path, prepend the API base URL\r\n  if (profileImage.startsWith('/')) {\r\n    return `${API_BASE_URL}${profileImage}`;\r\n  }\r\n  \r\n  // If it's just a filename, assume it's in the uploads directory\r\n  return `${API_BASE_URL}/uploads/avatars/${profileImage}`;\r\n};\r\n\r\n/**\r\n * Get user initials for fallback display\r\n * @param firstName - User's first name\r\n * @param lastName - User's last name\r\n * @returns Initials string (e.g., \"JD\")\r\n */\r\nexport const getUserInitials = (firstName?: string, lastName?: string): string => {\r\n  const first = firstName?.charAt(0)?.toUpperCase() || '';\r\n  const last = lastName?.charAt(0)?.toUpperCase() || '';\r\n  return `${first}${last}` || 'U';\r\n};\r\n\r\n/**\r\n * Validate image file for upload\r\n * @param file - File to validate\r\n * @returns Validation result with error message if invalid\r\n */\r\nexport const validateImageFile = (file: File): { valid: boolean; error?: string } => {\r\n  // Check file type\r\n  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];\r\n  if (!allowedTypes.includes(file.type)) {\r\n    return {\r\n      valid: false,\r\n      error: 'Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.'\r\n    };\r\n  }\r\n\r\n  // Check file size (5MB max)\r\n  const maxSize = 5 * 1024 * 1024; // 5MB\r\n  if (file.size > maxSize) {\r\n    return {\r\n      valid: false,\r\n      error: 'File size too large. Maximum size is 5MB.'\r\n    };\r\n  }\r\n\r\n  return { valid: true };\r\n};\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;AAEoB;AAArB,MAAM,eAAe,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;AAOjD,MAAM,qBAAqB,CAAC;IACjC,IAAI,CAAC,cAAc,OAAO;IAE1B,8DAA8D;IAC9D,IAAI,aAAa,UAAU,CAAC,cAAc,aAAa,UAAU,CAAC,aAAa;QAC7E,OAAO;IACT;IAEA,oDAAoD;IACpD,IAAI,aAAa,UAAU,CAAC,MAAM;QAChC,OAAO,GAAG,eAAe,cAAc;IACzC;IAEA,gEAAgE;IAChE,OAAO,GAAG,aAAa,iBAAiB,EAAE,cAAc;AAC1D;AAQO,MAAM,kBAAkB,CAAC,WAAoB;IAClD,MAAM,QAAQ,WAAW,OAAO,IAAI,iBAAiB;IACrD,MAAM,OAAO,UAAU,OAAO,IAAI,iBAAiB;IACnD,OAAO,GAAG,QAAQ,MAAM,IAAI;AAC9B;AAOO,MAAM,oBAAoB,CAAC;IAChC,kBAAkB;IAClB,MAAM,eAAe;QAAC;QAAc;QAAa;QAAa;KAAa;IAC3E,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;QACrC,OAAO;YACL,OAAO;YACP,OAAO;QACT;IACF;IAEA,4BAA4B;IAC5B,MAAM,UAAU,IAAI,OAAO,MAAM,MAAM;IACvC,IAAI,KAAK,IAAI,GAAG,SAAS;QACvB,OAAO;YACL,OAAO;YACP,OAAO;QACT;IACF;IAEA,OAAO;QAAE,OAAO;IAAK;AACvB", "debugId": null}}, {"offset": {"line": 339, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/Header.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport Link from 'next/link';\r\nimport { usePathname } from 'next/navigation';\r\nimport { useAuth } from '../contexts/AuthContext';\r\nimport UserMenu from './UserMenu';\r\nimport LogoutButton from './LogoutButton';\r\nimport { getUserInitials } from '../utils/imageUtils';\r\n\r\ninterface HeaderProps {\r\n  activeTab?: string;\r\n  onTabChange?: (tab: string) => void;\r\n  onMobileMenuToggle?: () => void;\r\n}\r\n\r\nconst Header = ({ activeTab = 'overview', onTabChange, onMobileMenuToggle }: HeaderProps) => {\r\n  const { isAuthenticated, user } = useAuth();\r\n  const [dropdownOpen, setDropdownOpen] = useState(false);\r\n  const pathname = usePathname();\r\n\r\n  // Only show dashboard tabs when on dashboard routes\r\n  const showDashboardTabs = pathname.startsWith('/dashboard');\r\n\r\n  const toggleDropdown = () => {\r\n    setDropdownOpen(!dropdownOpen);\r\n  };\r\n\r\n  const tabs = [\r\n    { id: 'overview', label: 'Overview' },\r\n    { id: 'licenses', label: 'Licenses' },\r\n    { id: 'users', label: 'Users' },\r\n    { id: 'transactions', label: 'Transactions' },\r\n    { id: 'spectrum', label: 'Spectrum' },\r\n    { id: 'compliance', label: 'Compliance' },\r\n  ];\r\n\r\n  return (\r\n    <header className=\"bg-white dark:bg-gray-800 shadow-sm z-10\">\r\n      <div className=\"flex items-center justify-between h-16 px-4 sm:px-6\">\r\n        <button\r\n          id=\"mobileMenuBtn\"\r\n          type=\"button\"\r\n          onClick={onMobileMenuToggle}\r\n          className=\"md:hidden text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none\"\r\n        >\r\n          <div className=\"w-6 h-6 flex items-center justify-center\">\r\n            <i className=\"ri-menu-line ri-lg\"></i>\r\n          </div>\r\n        </button>\r\n        <div className=\"flex-1 flex items-center justify-center px-2 lg:ml-6 lg:justify-start\">\r\n          <div className=\"max-w-lg w-full\">\r\n            <label htmlFor=\"search\" className=\"sr-only\">Search</label>\r\n            <div className=\"relative\">\r\n              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                <div className=\"w-5 h-5 flex items-center justify-center text-gray-400 dark:text-gray-500\">\r\n                  <i className=\"ri-search-line\"></i>\r\n                </div>\r\n              </div>\r\n              <input\r\n                id=\"search\"\r\n                name=\"search\"\r\n                className=\"block w-full pl-10 pr-3 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-sm hover:bg-white dark:hover:bg-gray-600 transition-colors\"\r\n                placeholder=\"Search for licenses, users, or transactions...\"\r\n                type=\"search\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"flex items-center\">\r\n          <button\r\n            type=\"button\"\r\n            className=\"flex-shrink-0 p-1 mr-4 text-gray-500 dark:text-gray-400 rounded-full hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none relative\"\r\n          >\r\n            <span className=\"sr-only\">View notifications</span>\r\n            <div className=\"w-6 h-6 flex items-center justify-center\">\r\n              <i className=\"ri-notification-3-line ri-lg\"></i>\r\n            </div>\r\n            <span className=\"absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white\"></span>\r\n          </button>\r\n          <div className=\"dropdown relative\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={toggleDropdown}\r\n              className=\"flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n            >\r\n              <span className=\"sr-only\">Open user menu</span>\r\n              {user?.profile_image ? (\r\n                <img\r\n                  className=\"h-8 w-8 rounded-full object-cover\"\r\n                  src={user.profile_image}\r\n                  alt=\"Profile\"\r\n                  onError={(e) => {\r\n                    // Fallback to initials if image fails to load\r\n                    const target = e.target as HTMLImageElement;\r\n                    target.style.display = 'none';\r\n                    target.nextElementSibling?.classList.remove('hidden');\r\n                  }}\r\n                />\r\n              ) : null}\r\n              <div className={`h-8 w-8 rounded-full bg-red-600 flex items-center justify-center text-white text-sm font-medium ${user?.profile_image ? 'hidden' : ''}`}>\r\n                {user ? getUserInitials(user.first_name, user.last_name) : 'U'}\r\n              </div>\r\n            </button>\r\n            <div\r\n              className={`dropdown-content mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black dark:ring-gray-600 ring-opacity-5 ${\r\n                dropdownOpen ? 'show' : ''\r\n              }`}\r\n            >\r\n              <div className=\"py-1\">\r\n                <Link\r\n                  href=\"/profile\"\r\n                  className=\"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                >\r\n                  Your Profile\r\n                </Link>\r\n                <Link\r\n                  href=\"/settings\"\r\n                  className=\"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                >\r\n                  Settings\r\n                </Link>\r\n                <div className=\"px-4 py-2\">\r\n                  <LogoutButton\r\n                    variant=\"text\"\r\n                    size=\"sm\"\r\n                    className=\"w-full justify-start text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                    showConfirmation={true}\r\n                    redirectTo=\"/auth/login\"\r\n                  >\r\n                    Sign out\r\n                  </LogoutButton>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      {/* Secondary navigation - only show on dashboard routes */}\r\n      {showDashboardTabs && (\r\n        <div className=\"border-t border-gray-200 dark:border-gray-700 px-4 sm:px-6\">\r\n          <div className=\"py-3 flex space-x-8\">\r\n            {tabs.map((tab) => (\r\n              <button\r\n                key={tab.id}\r\n                type=\"button\"\r\n                onClick={() => {\r\n                  onTabChange?.(tab.id);\r\n                  // Dispatch custom event for dashboard to listen to\r\n                  window.dispatchEvent(new CustomEvent('tabChange', { detail: { tab: tab.id } }));\r\n                }}\r\n                className={`tab-button text-sm px-1 py-2 ${\r\n                  activeTab === tab.id ? 'active' : 'text-gray-500'\r\n                }`}\r\n              >\r\n                {tab.label}\r\n              </button>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      )}\r\n    </header>\r\n  );\r\n};\r\n\r\nexport default Header;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;;;AARA;;;;;;;AAgBA,MAAM,SAAS,CAAC,EAAE,YAAY,UAAU,EAAE,WAAW,EAAE,kBAAkB,EAAe;;IACtF,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACxC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,oDAAoD;IACpD,MAAM,oBAAoB,SAAS,UAAU,CAAC;IAE9C,MAAM,iBAAiB;QACrB,gBAAgB,CAAC;IACnB;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAY,OAAO;QAAW;QACpC;YAAE,IAAI;YAAY,OAAO;QAAW;QACpC;YAAE,IAAI;YAAS,OAAO;QAAQ;QAC9B;YAAE,IAAI;YAAgB,OAAO;QAAe;QAC5C;YAAE,IAAI;YAAY,OAAO;QAAW;QACpC;YAAE,IAAI;YAAc,OAAO;QAAa;KACzC;IAED,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,IAAG;wBACH,MAAK;wBACL,SAAS;wBACT,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;;;;;;;;;;;;;;;;kCAGjB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,SAAQ;oCAAS,WAAU;8CAAU;;;;;;8CAC5C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;;;;;;sDAGjB,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,WAAU;4CACV,aAAY;4CACZ,MAAK;;;;;;;;;;;;;;;;;;;;;;;kCAKb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,WAAU;;kDAEV,6LAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;;;;;;;;;;;kDAEf,6LAAC;wCAAK,WAAU;;;;;;;;;;;;0CAElB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,6LAAC;gDAAK,WAAU;0DAAU;;;;;;4CACzB,MAAM,8BACL,6LAAC;gDACC,WAAU;gDACV,KAAK,KAAK,aAAa;gDACvB,KAAI;gDACJ,SAAS,CAAC;oDACR,8CAA8C;oDAC9C,MAAM,SAAS,EAAE,MAAM;oDACvB,OAAO,KAAK,CAAC,OAAO,GAAG;oDACvB,OAAO,kBAAkB,EAAE,UAAU,OAAO;gDAC9C;;;;;uDAEA;0DACJ,6LAAC;gDAAI,WAAW,CAAC,gGAAgG,EAAE,MAAM,gBAAgB,WAAW,IAAI;0DACrJ,OAAO,CAAA,GAAA,6HAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,UAAU,EAAE,KAAK,SAAS,IAAI;;;;;;;;;;;;kDAG/D,6LAAC;wCACC,WAAW,CAAC,8HAA8H,EACxI,eAAe,SAAS,IACxB;kDAEF,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;8DAGD,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;8DAGD,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,qIAAA,CAAA,UAAY;wDACX,SAAQ;wDACR,MAAK;wDACL,WAAU;wDACV,kBAAkB;wDAClB,YAAW;kEACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUZ,mCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC,oBACT,6LAAC;4BAEC,MAAK;4BACL,SAAS;gCACP,cAAc,IAAI,EAAE;gCACpB,mDAAmD;gCACnD,OAAO,aAAa,CAAC,IAAI,YAAY,aAAa;oCAAE,QAAQ;wCAAE,KAAK,IAAI,EAAE;oCAAC;gCAAE;4BAC9E;4BACA,WAAW,CAAC,6BAA6B,EACvC,cAAc,IAAI,EAAE,GAAG,WAAW,iBAClC;sCAED,IAAI,KAAK;2BAXL,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;AAmB3B;GAnJM;;QAC8B,kIAAA,CAAA,UAAO;QAExB,qIAAA,CAAA,cAAW;;;KAHxB;uCAqJS", "debugId": null}}, {"offset": {"line": 706, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/NavItem.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport { useLoading } from '@/contexts/LoadingContext';\r\n\r\ninterface NavItemProps {\r\n  href: string;\r\n  icon: string;\r\n  label: string;\r\n  isActive?: boolean;\r\n  onClick?: () => void;\r\n}\r\n\r\nconst NavItem: React.FC<NavItemProps> = ({ \r\n  href, \r\n  icon, \r\n  label, \r\n  isActive = false,\r\n  onClick \r\n}) => {\r\n  const { showLoader } = useLoading();\r\n\r\n  const handleClick = () => {\r\n    // Show loader with specific message based on the page\r\n    const pageMessages: { [key: string]: string } = {\r\n      '/dashboard': 'Loading Dashboard...',\r\n      '/applications/telecommunications': 'Loading Telecommunications...',\r\n      '/applications/postal': 'Loading Postal Services...',\r\n      '/applications/standards': 'Loading Standards...',\r\n      '/applications/clf': 'Loading CLF...',\r\n      '/resources': 'Loading Resources...',\r\n      '/procurement': 'Loading Procurement...',\r\n      '/spectrum': 'Loading Spectrum Management...',\r\n      '/financial': 'Loading Financial Data...',\r\n      '/reports': 'Loading Reports...',\r\n      '/users': 'Loading User Management...',\r\n      '/audit-trail': 'Loading Audit Trail...',\r\n      '/help': 'Loading Help & Support...'\r\n    };\r\n\r\n    const message = pageMessages[href] || 'Loading page...';\r\n    showLoader(message);\r\n\r\n    if (onClick) {\r\n      onClick();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Link\r\n      href={href}\r\n      onClick={handleClick}\r\n      className={`\r\n        flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200\r\n        ${isActive\r\n          ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-r-2 border-red-600 dark:border-red-400'\r\n          : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100'\r\n        }\r\n      `}\r\n    >\r\n      <div className={`w-5 h-5 flex items-center justify-center mr-3 ${isActive ? 'text-red-600 dark:text-red-400' : ''}`}>\r\n        <i className={icon}></i>\r\n      </div>\r\n      {label}\r\n    </Link>\r\n  );\r\n};\r\n\r\nexport default NavItem;"], "names": [], "mappings": ";;;;AAGA;AACA;;;AAJA;;;AAcA,MAAM,UAAkC,CAAC,EACvC,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,WAAW,KAAK,EAChB,OAAO,EACR;;IACC,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,aAAU,AAAD;IAEhC,MAAM,cAAc;QAClB,sDAAsD;QACtD,MAAM,eAA0C;YAC9C,cAAc;YACd,oCAAoC;YACpC,wBAAwB;YACxB,2BAA2B;YAC3B,qBAAqB;YACrB,cAAc;YACd,gBAAgB;YAChB,aAAa;YACb,cAAc;YACd,YAAY;YACZ,UAAU;YACV,gBAAgB;YAChB,SAAS;QACX;QAEA,MAAM,UAAU,YAAY,CAAC,KAAK,IAAI;QACtC,WAAW;QAEX,IAAI,SAAS;YACX;QACF;IACF;IAEA,qBACE,6LAAC,+JAAA,CAAA,UAAI;QACH,MAAM;QACN,SAAS;QACT,WAAW,CAAC;;QAEV,EAAE,WACE,8GACA,wHACH;MACH,CAAC;;0BAED,6LAAC;gBAAI,WAAW,CAAC,8CAA8C,EAAE,WAAW,mCAAmC,IAAI;0BACjH,cAAA,6LAAC;oBAAE,WAAW;;;;;;;;;;;YAEf;;;;;;;AAGP;GArDM;;QAOmB,qIAAA,CAAA,aAAU;;;KAP7B;uCAuDS", "debugId": null}}, {"offset": {"line": 791, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/services/cacheService.ts"], "sourcesContent": ["// Cache service for API responses to reduce rate limiting\r\n\r\ninterface CacheItem<T> {\r\n  data: T;\r\n  timestamp: number;\r\n  expiresAt: number;\r\n}\r\n\r\nclass CacheService {\r\n  private cache = new Map<string, CacheItem<any>>();\r\n  private defaultTTL = 5 * 60 * 1000; // 5 minutes default TTL\r\n\r\n  /**\r\n   * Set cache item with TTL\r\n   */\r\n  set<T>(key: string, data: T, ttl: number = this.defaultTTL): void {\r\n    const now = Date.now();\r\n    const item: CacheItem<T> = {\r\n      data,\r\n      timestamp: now,\r\n      expiresAt: now + ttl\r\n    };\r\n    \r\n    this.cache.set(key, item);\r\n    console.log(`Cache SET: ${key} (expires in ${ttl}ms)`);\r\n  }\r\n\r\n  /**\r\n   * Get cache item if not expired\r\n   */\r\n  get<T>(key: string): T | null {\r\n    const item = this.cache.get(key);\r\n    \r\n    if (!item) {\r\n      console.log(`Cache MISS: ${key}`);\r\n      return null;\r\n    }\r\n\r\n    const now = Date.now();\r\n    if (now > item.expiresAt) {\r\n      console.log(`Cache EXPIRED: ${key}`);\r\n      this.cache.delete(key);\r\n      return null;\r\n    }\r\n\r\n    console.log(`Cache HIT: ${key} (age: ${now - item.timestamp}ms)`);\r\n    return item.data as T;\r\n  }\r\n\r\n  /**\r\n   * Check if cache has valid item\r\n   */\r\n  has(key: string): boolean {\r\n    return this.get(key) !== null;\r\n  }\r\n\r\n  /**\r\n   * Delete cache item\r\n   */\r\n  delete(key: string): boolean {\r\n    console.log(`Cache DELETE: ${key}`);\r\n    return this.cache.delete(key);\r\n  }\r\n\r\n  /**\r\n   * Clear all cache\r\n   */\r\n  clear(): void {\r\n    console.log('Cache CLEAR: All items');\r\n    this.cache.clear();\r\n  }\r\n\r\n  /**\r\n   * Get cache stats\r\n   */\r\n  getStats(): { size: number; keys: string[] } {\r\n    return {\r\n      size: this.cache.size,\r\n      keys: Array.from(this.cache.keys())\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Clean expired items\r\n   */\r\n  cleanup(): void {\r\n    const now = Date.now();\r\n    let cleaned = 0;\r\n\r\n    for (const [key, item] of this.cache.entries()) {\r\n      if (now > item.expiresAt) {\r\n        this.cache.delete(key);\r\n        cleaned++;\r\n      }\r\n    }\r\n\r\n    if (cleaned > 0) {\r\n      console.log(`Cache CLEANUP: Removed ${cleaned} expired items`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get or set pattern - fetch data if not cached\r\n   */\r\n  async getOrSet<T>(\r\n    key: string,\r\n    fetcher: () => Promise<T>,\r\n    ttl: number = this.defaultTTL\r\n  ): Promise<T> {\r\n    // Try to get from cache first\r\n    const cached = this.get<T>(key);\r\n    if (cached !== null) {\r\n      return cached;\r\n    }\r\n\r\n    // Fetch fresh data\r\n    console.log(`Cache FETCH: ${key}`);\r\n    const data = await fetcher();\r\n    \r\n    // Store in cache\r\n    this.set(key, data, ttl);\r\n    \r\n    return data;\r\n  }\r\n\r\n  /**\r\n   * Invalidate cache by pattern\r\n   */\r\n  invalidatePattern(pattern: string): void {\r\n    const regex = new RegExp(pattern);\r\n    let invalidated = 0;\r\n\r\n    for (const key of this.cache.keys()) {\r\n      if (regex.test(key)) {\r\n        this.cache.delete(key);\r\n        invalidated++;\r\n      }\r\n    }\r\n\r\n    if (invalidated > 0) {\r\n      console.log(`Cache INVALIDATE: Removed ${invalidated} items matching pattern: ${pattern}`);\r\n    }\r\n  }\r\n}\r\n\r\n// Create singleton instance\r\nexport const cacheService = new CacheService();\r\n\r\n// Cache keys constants\r\nexport const CACHE_KEYS = {\r\n  LICENSE_TYPES: 'license-types',\r\n  LICENSE_CATEGORIES: 'license-categories',\r\n  LICENSE_CATEGORIES_BY_TYPE: (typeId: string) => `license-categories-type-${typeId}`,\r\n  USER_APPLICATIONS: 'user-applications',\r\n  APPLICATION: (id: string) => `application-${id}`,\r\n} as const;\r\n\r\n// Cache TTL constants (in milliseconds)\r\nexport const CACHE_TTL = {\r\n  SHORT: 2 * 60 * 1000,      // 2 minutes\r\n  MEDIUM: 5 * 60 * 1000,     // 5 minutes\r\n  LONG: 15 * 60 * 1000,      // 15 minutes\r\n  VERY_LONG: 60 * 60 * 1000, // 1 hour\r\n} as const;\r\n\r\n// Auto cleanup every 5 minutes\r\nsetInterval(() => {\r\n  cacheService.cleanup();\r\n}, 5 * 60 * 1000);\r\n\r\nexport default cacheService;\r\n"], "names": [], "mappings": "AAAA,0DAA0D;;;;;;;AAQ1D,MAAM;IACI,QAAQ,IAAI,MAA8B;IAC1C,aAAa,IAAI,KAAK,KAAK;IAEnC;;GAEC,GACD,IAAO,GAAW,EAAE,IAAO,EAAE,MAAc,IAAI,CAAC,UAAU,EAAQ;QAChE,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,OAAqB;YACzB;YACA,WAAW;YACX,WAAW,MAAM;QACnB;QAEA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK;QACpB,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,IAAI,aAAa,EAAE,IAAI,GAAG,CAAC;IACvD;IAEA;;GAEC,GACD,IAAO,GAAW,EAAY;QAC5B,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAE5B,IAAI,CAAC,MAAM;YACT,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,KAAK;YAChC,OAAO;QACT;QAEA,MAAM,MAAM,KAAK,GAAG;QACpB,IAAI,MAAM,KAAK,SAAS,EAAE;YACxB,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,KAAK;YACnC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YAClB,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,IAAI,OAAO,EAAE,MAAM,KAAK,SAAS,CAAC,GAAG,CAAC;QAChE,OAAO,KAAK,IAAI;IAClB;IAEA;;GAEC,GACD,IAAI,GAAW,EAAW;QACxB,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS;IAC3B;IAEA;;GAEC,GACD,OAAO,GAAW,EAAW;QAC3B,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,KAAK;QAClC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IAC3B;IAEA;;GAEC,GACD,QAAc;QACZ,QAAQ,GAAG,CAAC;QACZ,IAAI,CAAC,KAAK,CAAC,KAAK;IAClB;IAEA;;GAEC,GACD,WAA6C;QAC3C,OAAO;YACL,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI;QAClC;IACF;IAEA;;GAEC,GACD,UAAgB;QACd,MAAM,MAAM,KAAK,GAAG;QACpB,IAAI,UAAU;QAEd,KAAK,MAAM,CAAC,KAAK,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,GAAI;YAC9C,IAAI,MAAM,KAAK,SAAS,EAAE;gBACxB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;gBAClB;YACF;QACF;QAEA,IAAI,UAAU,GAAG;YACf,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,QAAQ,cAAc,CAAC;QAC/D;IACF;IAEA;;GAEC,GACD,MAAM,SACJ,GAAW,EACX,OAAyB,EACzB,MAAc,IAAI,CAAC,UAAU,EACjB;QACZ,8BAA8B;QAC9B,MAAM,SAAS,IAAI,CAAC,GAAG,CAAI;QAC3B,IAAI,WAAW,MAAM;YACnB,OAAO;QACT;QAEA,mBAAmB;QACnB,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,KAAK;QACjC,MAAM,OAAO,MAAM;QAEnB,iBAAiB;QACjB,IAAI,CAAC,GAAG,CAAC,KAAK,MAAM;QAEpB,OAAO;IACT;IAEA;;GAEC,GACD,kBAAkB,OAAe,EAAQ;QACvC,MAAM,QAAQ,IAAI,OAAO;QACzB,IAAI,cAAc;QAElB,KAAK,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,GAAI;YACnC,IAAI,MAAM,IAAI,CAAC,MAAM;gBACnB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;gBAClB;YACF;QACF;QAEA,IAAI,cAAc,GAAG;YACnB,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,YAAY,yBAAyB,EAAE,SAAS;QAC3F;IACF;AACF;AAGO,MAAM,eAAe,IAAI;AAGzB,MAAM,aAAa;IACxB,eAAe;IACf,oBAAoB;IACpB,4BAA4B,CAAC,SAAmB,CAAC,wBAAwB,EAAE,QAAQ;IACnF,mBAAmB;IACnB,aAAa,CAAC,KAAe,CAAC,YAAY,EAAE,IAAI;AAClD;AAGO,MAAM,YAAY;IACvB,OAAO,IAAI,KAAK;IAChB,QAAQ,IAAI,KAAK;IACjB,MAAM,KAAK,KAAK;IAChB,WAAW,KAAK,KAAK;AACvB;AAEA,+BAA+B;AAC/B,YAAY;IACV,aAAa,OAAO;AACtB,GAAG,IAAI,KAAK;uCAEG", "debugId": null}}, {"offset": {"line": 929, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/services/licenseTypeService.ts"], "sourcesContent": ["import { processApiResponse } from '@/lib/authUtils';\r\nimport { apiClient } from '../lib/apiClient';\r\nimport { cacheService, CACHE_KEYS, CACHE_TTL } from './cacheService';\r\n\r\n// Types\r\nexport interface LicenseType {\r\n  license_type_id: string;\r\n  name: string;\r\n  code?: string;\r\n  description: string;\r\n  validity: number;\r\n  created_at: string;\r\n  updated_at: string;\r\n  created_by?: string;\r\n  updated_by?: string;\r\n  creator?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n  updater?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n}\r\n\r\nexport interface NavigationItem {\r\n  license_type_id: string;\r\n  name: string;\r\n  code: string;\r\n  href: string;\r\n  label: string;\r\n  roles: string[];\r\n}\r\n\r\nexport interface CreateLicenseTypeDto {\r\n  name: string;\r\n  description: string;\r\n  validity: number;\r\n}\r\n\r\nexport interface UpdateLicenseTypeDto {\r\n  name?: string;\r\n  description?: string;\r\n  validity?: number;\r\n}\r\n\r\nexport interface PaginatedResponse<T> {\r\n  data: T[];\r\n  meta: {\r\n    itemsPerPage: number;\r\n    totalItems?: number;\r\n    currentPage?: number;\r\n    totalPages?: number;\r\n    sortBy: [string, string][];\r\n    searchBy: string[];\r\n    search: string;\r\n    select: string[];\r\n    filter?: Record<string, string | string[]>;\r\n  };\r\n  links: {\r\n    first?: string;\r\n    previous?: string;\r\n    current: string;\r\n    next?: string;\r\n    last?: string;\r\n  };\r\n}\r\n\r\nexport type LicenseTypesResponse = PaginatedResponse<LicenseType>;\r\n\r\nexport interface PaginateQuery {\r\n  page?: number;\r\n  limit?: number;\r\n  sortBy?: string[];\r\n  searchBy?: string[];\r\n  search?: string;\r\n  filter?: Record<string, string | string[]>;\r\n}\r\n\r\nexport const licenseTypeService = {\r\n  // Get all license types with pagination\r\n  async getLicenseTypes(query: PaginateQuery = {}): Promise<LicenseTypesResponse> {\r\n    const params = new URLSearchParams();\r\n\r\n    if (query.page) params.set('page', query.page.toString());\r\n    if (query.limit) params.set('limit', query.limit.toString());\r\n    if (query.search) params.set('search', query.search);\r\n    if (query.sortBy) {\r\n      query.sortBy.forEach(sort => params.append('sortBy', sort));\r\n    }\r\n    if (query.searchBy) {\r\n      query.searchBy.forEach(search => params.append('searchBy', search));\r\n    }\r\n    if (query.filter) {\r\n      Object.entries(query.filter).forEach(([key, value]) => {\r\n        if (Array.isArray(value)) {\r\n          value.forEach(v => params.append(`filter.${key}`, v));\r\n        } else {\r\n          params.set(`filter.${key}`, value);\r\n        }\r\n      });\r\n    }\r\n\r\n    const response = await apiClient.get(`/license-types?${params.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get license type by ID\r\n  async getLicenseType(id: string): Promise<LicenseType> {\r\n    const response = await apiClient.get(`/license-types/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  \r\n  // Get license type by ID\r\n  async getLicenseTypeByCode(code: string): Promise<LicenseType> {\r\n    const response = await apiClient.get(`/license-types/by-code/${code}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n\r\n  // Create new license type\r\n  async createLicenseType(licenseTypeData: CreateLicenseTypeDto): Promise<LicenseType> {\r\n    const response = await apiClient.post('/license-types', licenseTypeData);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update license type\r\n  async updateLicenseType(id: string, licenseTypeData: UpdateLicenseTypeDto): Promise<LicenseType> {\r\n    const response = await apiClient.put(`/license-types/${id}`, licenseTypeData);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Delete license type\r\n  async deleteLicenseType(id: string): Promise<{ message: string }> {\r\n    const response = await apiClient.delete(`/license-types/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get all license types (simple list for dropdowns) with caching\r\n  async getAllLicenseTypes(): Promise<any> {\r\n    return cacheService.getOrSet(\r\n      CACHE_KEYS.LICENSE_TYPES,\r\n      async () => {\r\n        console.log('Fetching license types from API...');\r\n        // Reduce limit to avoid rate limiting\r\n        const response = await this.getLicenseTypes({ limit: 100 });\r\n        return processApiResponse(response);\r\n      },\r\n      CACHE_TTL.LONG // Cache for 15 minutes\r\n    );\r\n  },\r\n\r\n  // Get license types for sidebar navigation\r\n  async getNavigationItems(): Promise<any> {\r\n    try {\r\n      const response = await apiClient.get('/license-types/navigation/sidebar');\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('LicenseTypeService.getNavigationItems error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAiFO,MAAM,qBAAqB;IAChC,wCAAwC;IACxC,MAAM,iBAAgB,QAAuB,CAAC,CAAC;QAC7C,MAAM,SAAS,IAAI;QAEnB,IAAI,MAAM,IAAI,EAAE,OAAO,GAAG,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ;QACtD,IAAI,MAAM,KAAK,EAAE,OAAO,GAAG,CAAC,SAAS,MAAM,KAAK,CAAC,QAAQ;QACzD,IAAI,MAAM,MAAM,EAAE,OAAO,GAAG,CAAC,UAAU,MAAM,MAAM;QACnD,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,MAAM,CAAC,OAAO,CAAC,CAAA,OAAQ,OAAO,MAAM,CAAC,UAAU;QACvD;QACA,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAA,SAAU,OAAO,MAAM,CAAC,YAAY;QAC7D;QACA,IAAI,MAAM,MAAM,EAAE;YAChB,OAAO,OAAO,CAAC,MAAM,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAChD,IAAI,MAAM,OAAO,CAAC,QAAQ;oBACxB,MAAM,OAAO,CAAC,CAAA,IAAK,OAAO,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBACpD,OAAO;oBACL,OAAO,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBAC9B;YACF;QACF;QAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,OAAO,QAAQ,IAAI;QAC1E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,yBAAyB;IACzB,MAAM,gBAAe,EAAU;QAC7B,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,IAAI;QAC3D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAGA,yBAAyB;IACzB,MAAM,sBAAqB,IAAY;QACrC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,MAAM;QACrE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAGA,0BAA0B;IAC1B,MAAM,mBAAkB,eAAqC;QAC3D,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,kBAAkB;QACxD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,sBAAsB;IACtB,MAAM,mBAAkB,EAAU,EAAE,eAAqC;QACvE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,IAAI,EAAE;QAC7D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,sBAAsB;IACtB,MAAM,mBAAkB,EAAU;QAChC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,eAAe,EAAE,IAAI;QAC9D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,iEAAiE;IACjE,MAAM;QACJ,OAAO,kIAAA,CAAA,eAAY,CAAC,QAAQ,CAC1B,kIAAA,CAAA,aAAU,CAAC,aAAa,EACxB;YACE,QAAQ,GAAG,CAAC;YACZ,sCAAsC;YACtC,MAAM,WAAW,MAAM,IAAI,CAAC,eAAe,CAAC;gBAAE,OAAO;YAAI;YACzD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,GACA,kIAAA,CAAA,YAAS,CAAC,IAAI,CAAC,uBAAuB;;IAE1C;IAEA,2CAA2C;IAC3C,MAAM;QACJ,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gDAAgD;YAC9D,MAAM;QACR;IACF;AAGF", "debugId": null}}, {"offset": {"line": 1020, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/Sidebar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { usePathname } from 'next/navigation';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport NavItem from './NavItem';\r\nimport Link from 'next/link';\r\nimport { getUserInitials } from '@/utils/imageUtils';\r\nimport { licenseTypeService, NavigationItem } from '@/services/licenseTypeService';\r\n\r\nconst Sidebar: React.FC = () => {\r\n  const { user } = useAuth();\r\n  const pathname = usePathname();\r\n  const [isMobileOpen, setIsMobileOpen] = useState(false);\r\n  const [licenseTypeNavItems, setLicenseTypeNavItems] = useState<NavigationItem[]>([]);\r\n  const [loadingLicenseTypes, setLoadingLicenseTypes] = useState(true);\r\n\r\n  // Close mobile sidebar when route changes\r\n  useEffect(() => {\r\n    setIsMobileOpen(false);\r\n  }, [pathname]);\r\n\r\n  // Load license types for navigation\r\n  useEffect(() => {\r\n    const loadLicenseTypes = async () => {\r\n      try {\r\n        setLoadingLicenseTypes(true);\r\n        const data = await licenseTypeService.getNavigationItems();\r\n        const navigationItems: NavigationItem[] = data.data;\r\n        setLicenseTypeNavItems(navigationItems);\r\n      } catch (error) {\r\n        console.error('Failed to load license types for navigation:', error);\r\n        // Fallback to empty array if API fails\r\n        setLicenseTypeNavItems([]);\r\n      } finally {\r\n        setLoadingLicenseTypes(false);\r\n      }\r\n    };\r\n\r\n    loadLicenseTypes();\r\n  }, []);\r\n\r\n  // Close mobile sidebar when clicking outside\r\n  useEffect(() => {\r\n    const handleClickOutside = (event: MouseEvent) => {\r\n      const sidebar = document.getElementById('mobile-sidebar');\r\n      const toggleButton = document.getElementById('mobile-sidebar-toggle');\r\n      \r\n      if (\r\n        isMobileOpen &&\r\n        sidebar &&\r\n        !sidebar.contains(event.target as Node) &&\r\n        toggleButton &&\r\n        !toggleButton.contains(event.target as Node)\r\n      ) {\r\n        setIsMobileOpen(false);\r\n      }\r\n    };\r\n\r\n    document.addEventListener('mousedown', handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener('mousedown', handleClickOutside);\r\n    };\r\n  }, [isMobileOpen]);\r\n\r\n  const toggleMobileSidebar = () => {\r\n    setIsMobileOpen(!isMobileOpen);\r\n  };\r\n\r\n  // Static navigation items\r\n  const topStaticNavigationItems = [\r\n    {\r\n      href: '/dashboard',\r\n      icon: 'ri-dashboard-line',\r\n      label: 'Dashboard',\r\n      roles: ['administrator', 'evaluator', 'customer']\r\n    },\r\n    {\r\n      href: '/task-assignment',\r\n      icon: 'ri-user-add-line',\r\n      label: 'Task Assignment',\r\n      roles: ['administrator', 'evaluator']\r\n    }\r\n  ];\r\n\r\n  const staticNavigationItems = [\r\n\r\n    {\r\n      href: '/consumer-affairs',\r\n      icon: 'ri-shield-user-line',\r\n      label: 'Consumer Affairs',\r\n      roles: ['administrator', 'evaluator', 'customer']\r\n    },\r\n    {\r\n      href: '/data-breach',\r\n      icon: 'ri-shield-cross-line',\r\n      label: 'Data Breach',\r\n      roles: ['administrator', 'evaluator']\r\n    },\r\n    {\r\n      href: '/resources',\r\n      icon: 'ri-folder-line',\r\n      label: 'Resources',\r\n      roles: ['administrator', 'evaluator']\r\n    },\r\n    {\r\n      href: '/procurement',\r\n      icon: 'ri-shopping-bag-line',\r\n      label: 'Procurement',\r\n      roles: ['administrator', 'evaluator']\r\n    },\r\n    {\r\n      href: '/financial',\r\n      icon: 'ri-money-dollar-circle-line',\r\n      label: 'Accounts & Finance',\r\n      roles: ['administrator', 'evaluator']\r\n    },\r\n    {\r\n      href: '/reports',\r\n      icon: 'ri-file-chart-line',\r\n      label: 'Reports & Analytics',\r\n      roles: ['administrator', 'evaluator']\r\n    }\r\n  ];\r\n\r\n  const settingsNavigationItems = [\r\n    {\r\n      href: '/users',\r\n      icon: 'ri-user-settings-line',\r\n      label: 'User Management',\r\n      roles: ['administrator']\r\n    },\r\n    {\r\n      href: '/settings',\r\n      icon: 'ri-settings-3-line',\r\n      label: 'Management Settings',\r\n      roles: ['administrator']\r\n    },\r\n    {\r\n      href: '/audit-trail',\r\n      icon: 'ri-shield-line',\r\n      label: 'Audit Trail',\r\n      roles: ['administrator', 'evaluator']\r\n    },\r\n    {\r\n      href: '/help',\r\n      icon: 'ri-question-line',\r\n      label: 'Help & Support',\r\n      roles: ['administrator', 'evaluator', 'customer']\r\n    }\r\n  ];\r\n\r\n  // Combine static navigation items with dynamic license types\r\n  const mainNavigationItems = [\r\n    ...licenseTypeNavItems.map(item => ({\r\n      href: item.href,\r\n      icon: 'ri-file-list-line', // Generic icon for all license types\r\n      label: item.label,\r\n      roles: item.roles\r\n    })),\r\n    ...staticNavigationItems\r\n  ];\r\n\r\n  const filteredMainNavItems = mainNavigationItems.filter(item =>\r\n    user?.roles?.some(role => item.roles.includes(role)) ||\r\n    item.roles.includes('customer')\r\n  );\r\n\r\n  const filteredSettingsNavItems = settingsNavigationItems.filter(item =>\r\n    user?.roles?.some(role => item.roles.includes(role)) ||\r\n    item.roles.includes('customer')\r\n  );\r\n\r\n  return (\r\n    <>\r\n      {/* Mobile Sidebar Toggle Button */}\r\n      <button\r\n        id=\"mobile-sidebar-toggle\"\r\n        onClick={toggleMobileSidebar}\r\n        className=\"lg:hidden fixed top-4 left-4 z-50 p-2 bg-primary text-white rounded-md shadow-lg hover:bg-red-700 transition-colors\"\r\n        aria-label=\"Toggle mobile sidebar\"\r\n      >\r\n        <i className={`fas ${isMobileOpen ? 'fa-times' : 'fa-bars'}`}></i>\r\n      </button>\r\n\r\n      {/* Mobile Overlay */}\r\n      {isMobileOpen && (\r\n        <div \r\n          className=\"lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40\"\r\n          onClick={() => setIsMobileOpen(false)}\r\n        />\r\n      )}\r\n\r\n      {/* Sidebar */}\r\n      <aside\r\n        id=\"mobile-sidebar\"\r\n        className={`\r\n          fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out\r\n          ${isMobileOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}\r\n        `}\r\n      >\r\n        <div className=\"flex flex-col h-full\">\r\n          {/* Logo/Header */}\r\n          <div className=\"h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700\">\r\n            <div className=\"flex items-center\">\r\n              <img src=\"/images/macra-logo.png\" alt=\"MACRA Logo\" className=\"max-h-12 w-auto\" />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Navigation */}\r\n          <nav className=\"mt-6 px-4 side-nav\">\r\n            {/* Main Navigation */}\r\n            {topStaticNavigationItems.length > 0 && (\r\n              <div className=\"mt-2 space-y-1\">\r\n                {topStaticNavigationItems.map((item) => (\r\n                  <NavItem\r\n                    key={item.href}\r\n                    href={item.href}\r\n                    icon={item.icon}\r\n                    label={item.label}\r\n                    isActive={pathname === item.href}\r\n                    onClick={() => setIsMobileOpen(false)}\r\n                  />\r\n              ))}\r\n            </div>\r\n            )}\r\n\r\n            {/* Main Navigation */}\r\n            {filteredMainNavItems.length > 0 && (\r\n              <div className=\"mt-8\">\r\n                <h3 className=\"px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Main Menu\r\n                </h3>\r\n                <div className=\"mt-2 space-y-1\">\r\n                  {filteredMainNavItems.map((item) => (\r\n                    <NavItem\r\n                      key={item.href}\r\n                      href={item.href}\r\n                      icon={item.icon}\r\n                      label={item.label}\r\n                      isActive={pathname === item.href}\r\n                      onClick={() => setIsMobileOpen(false)}\r\n                    />\r\n                ))}\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Settings Section */}\r\n            {filteredSettingsNavItems.length > 0 && (\r\n              <div className=\"mt-8\">\r\n                <h3 className=\"px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Settings\r\n                </h3>\r\n                <div className=\"mt-2 space-y-1\">\r\n                  {filteredSettingsNavItems.map((item) => (\r\n                    <NavItem\r\n                      key={item.href}\r\n                      href={item.href}\r\n                      icon={item.icon}\r\n                      label={item.label}\r\n                      isActive={pathname === item.href}\r\n                      onClick={() => setIsMobileOpen(false)}\r\n                    />\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            )}\r\n          </nav>\r\n\r\n          {/* User Info */}\r\n          {user && (\r\n            <div className=\"absolute bottom-0 w-64 p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800\">\r\n              <div className=\"flex items-center space-x-3\">\r\n                {user.profile_image ? (\r\n                  <img\r\n                    className=\"h-10 w-10 rounded-full object-cover\"\r\n                    src={user.profile_image}\r\n                    alt=\"Profile\"\r\n                    onError={(e) => {\r\n                      // Fallback to initials if image fails to load\r\n                      const target = e.target as HTMLImageElement;\r\n                      target.style.display = 'none';\r\n                      target.nextElementSibling?.classList.remove('hidden');\r\n                    }}\r\n                  />\r\n                ) : null}\r\n                <div className={`h-10 w-10 rounded-full bg-red-600 flex items-center justify-center text-white text-sm font-medium ${user.profile_image ? 'hidden' : ''}`}>\r\n                  {getUserInitials(user.first_name, user.last_name)}\r\n                </div>\r\n                <Link href='/profile' className=\"flex-1 min-w-0\">\r\n                  <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100 truncate\">\r\n                    {user.first_name || 'Unknown'} {user.last_name || 'User'}\r\n                  </p>\r\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400 truncate\">\r\n                    {user.roles && user.roles.length > 0\r\n                      ? user.roles.map(role =>\r\n                          typeof role === 'string'\r\n                            ? role.replace(/_/g, ' ').replace(/\\b\\w/g, (l: string) => l.toUpperCase())\r\n                            : 'Unknown'\r\n                        ).join(', ')\r\n                      : 'User'\r\n                    }\r\n                  </p>\r\n                </Link>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </aside>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Sidebar;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAUA,MAAM,UAAoB;;IACxB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IACnF,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,0CAA0C;IAC1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,gBAAgB;QAClB;4BAAG;QAAC;KAAS;IAEb,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,MAAM;sDAAmB;oBACvB,IAAI;wBACF,uBAAuB;wBACvB,MAAM,OAAO,MAAM,wIAAA,CAAA,qBAAkB,CAAC,kBAAkB;wBACxD,MAAM,kBAAoC,KAAK,IAAI;wBACnD,uBAAuB;oBACzB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,gDAAgD;wBAC9D,uCAAuC;wBACvC,uBAAuB,EAAE;oBAC3B,SAAU;wBACR,uBAAuB;oBACzB;gBACF;;YAEA;QACF;4BAAG,EAAE;IAEL,6CAA6C;IAC7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,MAAM;wDAAqB,CAAC;oBAC1B,MAAM,UAAU,SAAS,cAAc,CAAC;oBACxC,MAAM,eAAe,SAAS,cAAc,CAAC;oBAE7C,IACE,gBACA,WACA,CAAC,QAAQ,QAAQ,CAAC,MAAM,MAAM,KAC9B,gBACA,CAAC,aAAa,QAAQ,CAAC,MAAM,MAAM,GACnC;wBACA,gBAAgB;oBAClB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;qCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;4BAAG;QAAC;KAAa;IAEjB,MAAM,sBAAsB;QAC1B,gBAAgB,CAAC;IACnB;IAEA,0BAA0B;IAC1B,MAAM,2BAA2B;QAC/B;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;gBAAa;aAAW;QACnD;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;aAAY;QACvC;KACD;IAED,MAAM,wBAAwB;QAE5B;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;gBAAa;aAAW;QACnD;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;aAAY;QACvC;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;aAAY;QACvC;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;aAAY;QACvC;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;aAAY;QACvC;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;aAAY;QACvC;KACD;IAED,MAAM,0BAA0B;QAC9B;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;aAAgB;QAC1B;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;aAAgB;QAC1B;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;aAAY;QACvC;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;gBAAa;aAAW;QACnD;KACD;IAED,6DAA6D;IAC7D,MAAM,sBAAsB;WACvB,oBAAoB,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAClC,MAAM,KAAK,IAAI;gBACf,MAAM;gBACN,OAAO,KAAK,KAAK;gBACjB,OAAO,KAAK,KAAK;YACnB,CAAC;WACE;KACJ;IAED,MAAM,uBAAuB,oBAAoB,MAAM,CAAC,CAAA,OACtD,MAAM,OAAO,KAAK,CAAA,OAAQ,KAAK,KAAK,CAAC,QAAQ,CAAC,UAC9C,KAAK,KAAK,CAAC,QAAQ,CAAC;IAGtB,MAAM,2BAA2B,wBAAwB,MAAM,CAAC,CAAA,OAC9D,MAAM,OAAO,KAAK,CAAA,OAAQ,KAAK,KAAK,CAAC,QAAQ,CAAC,UAC9C,KAAK,KAAK,CAAC,QAAQ,CAAC;IAGtB,qBACE;;0BAEE,6LAAC;gBACC,IAAG;gBACH,SAAS;gBACT,WAAU;gBACV,cAAW;0BAEX,cAAA,6LAAC;oBAAE,WAAW,CAAC,IAAI,EAAE,eAAe,aAAa,WAAW;;;;;;;;;;;YAI7D,8BACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,gBAAgB;;;;;;0BAKnC,6LAAC;gBACC,IAAG;gBACH,WAAW,CAAC;;UAEV,EAAE,eAAe,kBAAkB,qCAAqC;QAC1E,CAAC;0BAED,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,KAAI;oCAAyB,KAAI;oCAAa,WAAU;;;;;;;;;;;;;;;;sCAKjE,6LAAC;4BAAI,WAAU;;gCAEZ,yBAAyB,MAAM,GAAG,mBACjC,6LAAC;oCAAI,WAAU;8CACZ,yBAAyB,GAAG,CAAC,CAAC,qBAC7B,6LAAC,gIAAA,CAAA,UAAO;4CAEN,MAAM,KAAK,IAAI;4CACf,MAAM,KAAK,IAAI;4CACf,OAAO,KAAK,KAAK;4CACjB,UAAU,aAAa,KAAK,IAAI;4CAChC,SAAS,IAAM,gBAAgB;2CAL1B,KAAK,IAAI;;;;;;;;;;gCAYrB,qBAAqB,MAAM,GAAG,mBAC7B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAuF;;;;;;sDAGrG,6LAAC;4CAAI,WAAU;sDACZ,qBAAqB,GAAG,CAAC,CAAC,qBACzB,6LAAC,gIAAA,CAAA,UAAO;oDAEN,MAAM,KAAK,IAAI;oDACf,MAAM,KAAK,IAAI;oDACf,OAAO,KAAK,KAAK;oDACjB,UAAU,aAAa,KAAK,IAAI;oDAChC,SAAS,IAAM,gBAAgB;mDAL1B,KAAK,IAAI;;;;;;;;;;;;;;;;gCAavB,yBAAyB,MAAM,GAAG,mBACjC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAuF;;;;;;sDAGrG,6LAAC;4CAAI,WAAU;sDACZ,yBAAyB,GAAG,CAAC,CAAC,qBAC7B,6LAAC,gIAAA,CAAA,UAAO;oDAEN,MAAM,KAAK,IAAI;oDACf,MAAM,KAAK,IAAI;oDACf,OAAO,KAAK,KAAK;oDACjB,UAAU,aAAa,KAAK,IAAI;oDAChC,SAAS,IAAM,gBAAgB;mDAL1B,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;wBAczB,sBACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;oCACZ,KAAK,aAAa,iBACjB,6LAAC;wCACC,WAAU;wCACV,KAAK,KAAK,aAAa;wCACvB,KAAI;wCACJ,SAAS,CAAC;4CACR,8CAA8C;4CAC9C,MAAM,SAAS,EAAE,MAAM;4CACvB,OAAO,KAAK,CAAC,OAAO,GAAG;4CACvB,OAAO,kBAAkB,EAAE,UAAU,OAAO;wCAC9C;;;;;+CAEA;kDACJ,6LAAC;wCAAI,WAAW,CAAC,kGAAkG,EAAE,KAAK,aAAa,GAAG,WAAW,IAAI;kDACtJ,CAAA,GAAA,6HAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,UAAU,EAAE,KAAK,SAAS;;;;;;kDAElD,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;;0DAC9B,6LAAC;gDAAE,WAAU;;oDACV,KAAK,UAAU,IAAI;oDAAU;oDAAE,KAAK,SAAS,IAAI;;;;;;;0DAEpD,6LAAC;gDAAE,WAAU;0DACV,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,IAC/B,KAAK,KAAK,CAAC,GAAG,CAAC,CAAA,OACb,OAAO,SAAS,WACZ,KAAK,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,SAAS,CAAC,IAAc,EAAE,WAAW,MACrE,WACJ,IAAI,CAAC,QACP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWxB;GA9SM;;QACa,kIAAA,CAAA,UAAO;QACP,qIAAA,CAAA,cAAW;;;KAFxB;uCAgTS", "debugId": null}}, {"offset": {"line": 1489, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/app/consumer-affairs/layout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useState } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { useAuth } from '../../contexts/AuthContext';\r\nimport Header from \"@/components/Header\";\r\nimport Sidebar from \"@/components/Sidebar\";\r\nimport AuthDebug from \"@/components/AuthDebug\";\r\n\r\nexport default function ConsumerAffairsLayout({ children }: { children: React.ReactNode }) {\r\n  const { isAuthenticated, loading } = useAuth();\r\n  const router = useRouter();\r\n  const [activeTab, setActiveTab] = useState('overview');\r\n  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);\r\n\r\n  useEffect(() => {\r\n    if (!loading && !isAuthenticated) {\r\n      router.push('/auth/login');\r\n    }\r\n  }, [isAuthenticated, loading, router]);\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\">\r\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600\"></div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (!isAuthenticated) {\r\n    return null; // Will redirect\r\n  }\r\n\r\n  const toggleMobileSidebar = () => {\r\n    setIsMobileSidebarOpen(!isMobileSidebarOpen);\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex h-screen overflow-hidden bg-gray-50 dark:bg-gray-900\">\r\n      {/* Mobile sidebar overlay */}\r\n      <div\r\n        id=\"mobileSidebarOverlay\"\r\n        className={`mobile-sidebar-overlay ${isMobileSidebarOpen ? 'show' : ''}`}\r\n        onClick={() => setIsMobileSidebarOpen(false)}\r\n      ></div>\r\n\r\n      <Sidebar />\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        <Header\r\n          activeTab={activeTab}\r\n          onTabChange={setActiveTab}\r\n          onMobileMenuToggle={toggleMobileSidebar}\r\n        />\r\n        <main className=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900\">\r\n          {children}\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AASe,SAAS,sBAAsB,EAAE,QAAQ,EAAiC;;IACvF,MAAM,EAAE,eAAe,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,IAAI,CAAC,WAAW,CAAC,iBAAiB;gBAChC,OAAO,IAAI,CAAC;YACd;QACF;0CAAG;QAAC;QAAiB;QAAS;KAAO;IAErC,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO,MAAM,gBAAgB;IAC/B;IAEA,MAAM,sBAAsB;QAC1B,uBAAuB,CAAC;IAC1B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBACC,IAAG;gBACH,WAAW,CAAC,uBAAuB,EAAE,sBAAsB,SAAS,IAAI;gBACxE,SAAS,IAAM,uBAAuB;;;;;;0BAGxC,6LAAC,gIAAA,CAAA,UAAO;;;;;0BACR,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+HAAA,CAAA,UAAM;wBACL,WAAW;wBACX,aAAa;wBACb,oBAAoB;;;;;;kCAEtB,6LAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX;GAlDwB;;QACe,kIAAA,CAAA,UAAO;QAC7B,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}, {"offset": {"line": 1612, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/node_modules/next/src/shared/lib/router/utils/querystring.ts"], "sourcesContent": ["import type { ParsedUrlQuery } from 'querystring'\n\nexport function searchParamsToUrlQuery(\n  searchParams: URLSearchParams\n): ParsedUrlQuery {\n  const query: ParsedUrlQuery = {}\n  for (const [key, value] of searchParams.entries()) {\n    const existing = query[key]\n    if (typeof existing === 'undefined') {\n      query[key] = value\n    } else if (Array.isArray(existing)) {\n      existing.push(value)\n    } else {\n      query[key] = [existing, value]\n    }\n  }\n  return query\n}\n\nfunction stringifyUrlQueryParam(param: unknown): string {\n  if (typeof param === 'string') {\n    return param\n  }\n\n  if (\n    (typeof param === 'number' && !isNaN(param)) ||\n    typeof param === 'boolean'\n  ) {\n    return String(param)\n  } else {\n    return ''\n  }\n}\n\nexport function urlQueryToSearchParams(query: ParsedUrlQuery): URLSearchParams {\n  const searchParams = new URLSearchParams()\n  for (const [key, value] of Object.entries(query)) {\n    if (Array.isArray(value)) {\n      for (const item of value) {\n        searchParams.append(key, stringifyUrlQueryParam(item))\n      }\n    } else {\n      searchParams.set(key, stringifyUrlQueryParam(value))\n    }\n  }\n  return searchParams\n}\n\nexport function assign(\n  target: URLSearchParams,\n  ...searchParamsList: URLSearchParams[]\n): URLSearchParams {\n  for (const searchParams of searchParamsList) {\n    for (const key of searchParams.keys()) {\n      target.delete(key)\n    }\n\n    for (const [key, value] of searchParams.entries()) {\n      target.append(key, value)\n    }\n  }\n\n  return target\n}\n"], "names": ["assign", "searchParamsToUrlQuery", "urlQueryToSearchParams", "searchParams", "query", "key", "value", "entries", "existing", "Array", "isArray", "push", "stringifyUrlQueryParam", "param", "isNaN", "String", "URLSearchParams", "Object", "item", "append", "set", "target", "searchParamsList", "keys", "delete"], "mappings": ";;;;;;;;;;;;;;;;IAgDgBA,MAAM,EAAA;eAANA;;IA9CAC,sBAAsB,EAAA;eAAtBA;;IAgCAC,sBAAsB,EAAA;eAAtBA;;;AAhCT,SAASD,uBACdE,YAA6B;IAE7B,MAAMC,QAAwB,CAAC;IAC/B,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIH,aAAaI,OAAO,GAAI;QACjD,MAAMC,WAAWJ,KAAK,CAACC,IAAI;QAC3B,IAAI,OAAOG,aAAa,aAAa;YACnCJ,KAAK,CAACC,IAAI,GAAGC;QACf,OAAO,IAAIG,MAAMC,OAAO,CAACF,WAAW;YAClCA,SAASG,IAAI,CAACL;QAChB,OAAO;YACLF,KAAK,CAACC,IAAI,GAAG;gBAACG;gBAAUF;aAAM;QAChC;IACF;IACA,OAAOF;AACT;AAEA,SAASQ,uBAAuBC,KAAc;IAC5C,IAAI,OAAOA,UAAU,UAAU;QAC7B,OAAOA;IACT;IAEA,IACG,OAAOA,UAAU,YAAY,CAACC,MAAMD,UACrC,OAAOA,UAAU,WACjB;QACA,OAAOE,OAAOF;IAChB,OAAO;QACL,OAAO;IACT;AACF;AAEO,SAASX,uBAAuBE,KAAqB;IAC1D,MAAMD,eAAe,IAAIa;IACzB,KAAK,MAAM,CAACX,KAAKC,MAAM,IAAIW,OAAOV,OAAO,CAACH,OAAQ;QAChD,IAAIK,MAAMC,OAAO,CAACJ,QAAQ;YACxB,KAAK,MAAMY,QAAQZ,MAAO;gBACxBH,aAAagB,MAAM,CAACd,KAAKO,uBAAuBM;YAClD;QACF,OAAO;YACLf,aAAaiB,GAAG,CAACf,KAAKO,uBAAuBN;QAC/C;IACF;IACA,OAAOH;AACT;AAEO,SAASH,OACdqB,MAAuB;IACvB,IAAA,IAAA,OAAA,UAAA,MAAA,EAAGC,mBAAH,IAAA,MAAA,OAAA,IAAA,OAAA,IAAA,IAAA,OAAA,GAAA,OAAA,MAAA,OAAA;QAAGA,gBAAAA,CAAH,OAAA,EAAA,GAAA,SAAA,CAAA,KAAsC;;IAEtC,KAAK,MAAMnB,gBAAgBmB,iBAAkB;QAC3C,KAAK,MAAMjB,OAAOF,aAAaoB,IAAI,GAAI;YACrCF,OAAOG,MAAM,CAACnB;QAChB;QAEA,KAAK,MAAM,CAACA,KAAKC,MAAM,IAAIH,aAAaI,OAAO,GAAI;YACjDc,OAAOF,MAAM,CAACd,KAAKC;QACrB;IACF;IAEA,OAAOe;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1697, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/node_modules/next/src/shared/lib/router/utils/format-url.ts"], "sourcesContent": ["// Format function modified from nodejs\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nimport type { UrlObject } from 'url'\nimport type { ParsedUrlQuery } from 'querystring'\nimport * as querystring from './querystring'\n\nconst slashedProtocols = /https?|ftp|gopher|file/\n\nexport function formatUrl(urlObj: UrlObject) {\n  let { auth, hostname } = urlObj\n  let protocol = urlObj.protocol || ''\n  let pathname = urlObj.pathname || ''\n  let hash = urlObj.hash || ''\n  let query = urlObj.query || ''\n  let host: string | false = false\n\n  auth = auth ? encodeURIComponent(auth).replace(/%3A/i, ':') + '@' : ''\n\n  if (urlObj.host) {\n    host = auth + urlObj.host\n  } else if (hostname) {\n    host = auth + (~hostname.indexOf(':') ? `[${hostname}]` : hostname)\n    if (urlObj.port) {\n      host += ':' + urlObj.port\n    }\n  }\n\n  if (query && typeof query === 'object') {\n    query = String(querystring.urlQueryToSearchParams(query as ParsedUrlQuery))\n  }\n\n  let search = urlObj.search || (query && `?${query}`) || ''\n\n  if (protocol && !protocol.endsWith(':')) protocol += ':'\n\n  if (\n    urlObj.slashes ||\n    ((!protocol || slashedProtocols.test(protocol)) && host !== false)\n  ) {\n    host = '//' + (host || '')\n    if (pathname && pathname[0] !== '/') pathname = '/' + pathname\n  } else if (!host) {\n    host = ''\n  }\n\n  if (hash && hash[0] !== '#') hash = '#' + hash\n  if (search && search[0] !== '?') search = '?' + search\n\n  pathname = pathname.replace(/[?#]/g, encodeURIComponent)\n  search = search.replace('#', '%23')\n\n  return `${protocol}${host}${pathname}${search}${hash}`\n}\n\nexport const urlObjectKeys = [\n  'auth',\n  'hash',\n  'host',\n  'hostname',\n  'href',\n  'path',\n  'pathname',\n  'port',\n  'protocol',\n  'query',\n  'search',\n  'slashes',\n]\n\nexport function formatWithValidation(url: UrlObject): string {\n  if (process.env.NODE_ENV === 'development') {\n    if (url !== null && typeof url === 'object') {\n      Object.keys(url).forEach((key) => {\n        if (!urlObjectKeys.includes(key)) {\n          console.warn(\n            `Unknown key passed via urlObject into url.format: ${key}`\n          )\n        }\n      })\n    }\n  }\n\n  return formatUrl(url)\n}\n"], "names": ["formatUrl", "formatWithValidation", "urlObjectKeys", "slashedProtocols", "url<PERSON>bj", "auth", "hostname", "protocol", "pathname", "hash", "query", "host", "encodeURIComponent", "replace", "indexOf", "port", "String", "querystring", "urlQueryToSearchParams", "search", "endsWith", "slashes", "test", "url", "process", "env", "NODE_ENV", "Object", "keys", "for<PERSON>ach", "key", "includes", "console", "warn"], "mappings": "AAAA,uCAAuC;AACvC,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AAsEnCwB,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;;;;;;;;;;;;;IA9Df1B,SAAS,EAAA;eAATA;;IA6DAC,oBAAoB,EAAA;eAApBA;;IAfHC,aAAa,EAAA;eAAbA;;;;uEAlDgB;AAE7B,MAAMC,mBAAmB;AAElB,SAASH,UAAUI,MAAiB;IACzC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAE,GAAGF;IACzB,IAAIG,WAAWH,OAAOG,QAAQ,IAAI;IAClC,IAAIC,WAAWJ,OAAOI,QAAQ,IAAI;IAClC,IAAIC,OAAOL,OAAOK,IAAI,IAAI;IAC1B,IAAIC,QAAQN,OAAOM,KAAK,IAAI;IAC5B,IAAIC,OAAuB;IAE3BN,OAAOA,OAAOO,mBAAmBP,MAAMQ,OAAO,CAAC,QAAQ,OAAO,MAAM;IAEpE,IAAIT,OAAOO,IAAI,EAAE;QACfA,OAAON,OAAOD,OAAOO,IAAI;IAC3B,OAAO,IAAIL,UAAU;QACnBK,OAAON,OAAQ,CAAA,CAACC,SAASQ,OAAO,CAAC,OAAQ,MAAGR,WAAS,MAAKA,QAAO;QACjE,IAAIF,OAAOW,IAAI,EAAE;YACfJ,QAAQ,MAAMP,OAAOW,IAAI;QAC3B;IACF;IAEA,IAAIL,SAAS,OAAOA,UAAU,UAAU;QACtCA,QAAQM,OAAOC,aAAYC,sBAAsB,CAACR;IACpD;IAEA,IAAIS,SAASf,OAAOe,MAAM,IAAKT,SAAU,MAAGA,SAAY;IAExD,IAAIH,YAAY,CAACA,SAASa,QAAQ,CAAC,MAAMb,YAAY;IAErD,IACEH,OAAOiB,OAAO,IACZ,CAAA,CAACd,YAAYJ,iBAAiBmB,IAAI,CAACf,SAAQ,KAAMI,SAAS,OAC5D;QACAA,OAAO,OAAQA,CAAAA,QAAQ,EAAC;QACxB,IAAIH,YAAYA,QAAQ,CAAC,EAAE,KAAK,KAAKA,WAAW,MAAMA;IACxD,OAAO,IAAI,CAACG,MAAM;QAChBA,OAAO;IACT;IAEA,IAAIF,QAAQA,IAAI,CAAC,EAAE,KAAK,KAAKA,OAAO,MAAMA;IAC1C,IAAIU,UAAUA,MAAM,CAAC,EAAE,KAAK,KAAKA,SAAS,MAAMA;IAEhDX,WAAWA,SAASK,OAAO,CAAC,SAASD;IACrCO,SAASA,OAAON,OAAO,CAAC,KAAK;IAE7B,OAAQ,KAAEN,WAAWI,OAAOH,WAAWW,SAASV;AAClD;AAEO,MAAMP,gBAAgB;IAC3B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,SAASD,qBAAqBsB,GAAc;IACjD,wCAA4C;QAC1C,IAAIA,QAAQ,QAAQ,OAAOA,QAAQ,UAAU;YAC3CI,OAAOC,IAAI,CAACL,KAAKM,OAAO,CAAC,CAACC;gBACxB,IAAI,CAAC5B,cAAc6B,QAAQ,CAACD,MAAM;oBAChCE,QAAQC,IAAI,CACT,uDAAoDH;gBAEzD;YACF;QACF;IACF;IAEA,OAAO9B,UAAUuB;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1812, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/node_modules/next/src/shared/lib/utils.ts"], "sourcesContent": ["import type { HtmlProps } from './html-context.shared-runtime'\nimport type { ComponentType, JSX } from 'react'\nimport type { DomainLocale } from '../../server/config'\nimport type { Env } from '@next/env'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { NextRouter } from './router/router'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { PreviewData } from '../../types'\nimport type { COMPILER_NAMES } from './constants'\nimport type fs from 'fs'\n\nexport type NextComponentType<\n  Context extends BaseContext = NextPageContext,\n  InitialProps = {},\n  Props = {},\n> = ComponentType<Props> & {\n  /**\n   * Used for initial page load data population. Data returned from `getInitialProps` is serialized when server rendered.\n   * Make sure to return plain `Object` without using `Date`, `Map`, `Set`.\n   * @param context Context of `page`\n   */\n  getInitialProps?(context: Context): InitialProps | Promise<InitialProps>\n}\n\nexport type DocumentType = NextComponentType<\n  DocumentContext,\n  DocumentInitialProps,\n  DocumentProps\n>\n\nexport type AppType<P = {}> = NextComponentType<\n  AppContextType,\n  P,\n  AppPropsType<any, P>\n>\n\nexport type AppTreeType = ComponentType<\n  AppInitialProps & { [name: string]: any }\n>\n\n/**\n * Web vitals provided to _app.reportWebVitals by Core Web Vitals plugin developed by Google Chrome team.\n * https://nextjs.org/blog/next-9-4#integrated-web-vitals-reporting\n */\nexport const WEB_VITALS = ['CLS', 'FCP', 'FID', 'INP', 'LCP', 'TTFB'] as const\nexport type NextWebVitalsMetric = {\n  id: string\n  startTime: number\n  value: number\n  attribution?: { [key: string]: unknown }\n} & (\n  | {\n      label: 'web-vital'\n      name: (typeof WEB_VITALS)[number]\n    }\n  | {\n      label: 'custom'\n      name:\n        | 'Next.js-hydration'\n        | 'Next.js-route-change-to-render'\n        | 'Next.js-render'\n    }\n)\n\nexport type Enhancer<C> = (Component: C) => C\n\nexport type ComponentsEnhancer =\n  | {\n      enhanceApp?: Enhancer<AppType>\n      enhanceComponent?: Enhancer<NextComponentType>\n    }\n  | Enhancer<NextComponentType>\n\nexport type RenderPageResult = {\n  html: string\n  head?: Array<JSX.Element | null>\n}\n\nexport type RenderPage = (\n  options?: ComponentsEnhancer\n) => DocumentInitialProps | Promise<DocumentInitialProps>\n\nexport type BaseContext = {\n  res?: ServerResponse\n  [k: string]: any\n}\n\nexport type NEXT_DATA = {\n  props: Record<string, any>\n  page: string\n  query: ParsedUrlQuery\n  buildId: string\n  assetPrefix?: string\n  runtimeConfig?: { [key: string]: any }\n  nextExport?: boolean\n  autoExport?: boolean\n  isFallback?: boolean\n  isExperimentalCompile?: boolean\n  dynamicIds?: (string | number)[]\n  err?: Error & {\n    statusCode?: number\n    source?: typeof COMPILER_NAMES.server | typeof COMPILER_NAMES.edgeServer\n  }\n  gsp?: boolean\n  gssp?: boolean\n  customServer?: boolean\n  gip?: boolean\n  appGip?: boolean\n  locale?: string\n  locales?: readonly string[]\n  defaultLocale?: string\n  domainLocales?: readonly DomainLocale[]\n  scriptLoader?: any[]\n  isPreview?: boolean\n  notFoundSrcPage?: string\n}\n\n/**\n * `Next` context\n */\nexport interface NextPageContext {\n  /**\n   * Error object if encountered during rendering\n   */\n  err?: (Error & { statusCode?: number }) | null\n  /**\n   * `HTTP` request object.\n   */\n  req?: IncomingMessage\n  /**\n   * `HTTP` response object.\n   */\n  res?: ServerResponse\n  /**\n   * Path section of `URL`.\n   */\n  pathname: string\n  /**\n   * Query string section of `URL` parsed as an object.\n   */\n  query: ParsedUrlQuery\n  /**\n   * `String` of the actual path including query.\n   */\n  asPath?: string\n  /**\n   * The currently active locale\n   */\n  locale?: string\n  /**\n   * All configured locales\n   */\n  locales?: readonly string[]\n  /**\n   * The configured default locale\n   */\n  defaultLocale?: string\n  /**\n   * `Component` the tree of the App to use if needing to render separately\n   */\n  AppTree: AppTreeType\n}\n\nexport type AppContextType<Router extends NextRouter = NextRouter> = {\n  Component: NextComponentType<NextPageContext>\n  AppTree: AppTreeType\n  ctx: NextPageContext\n  router: Router\n}\n\nexport type AppInitialProps<PageProps = any> = {\n  pageProps: PageProps\n}\n\nexport type AppPropsType<\n  Router extends NextRouter = NextRouter,\n  PageProps = {},\n> = AppInitialProps<PageProps> & {\n  Component: NextComponentType<NextPageContext, any, any>\n  router: Router\n  __N_SSG?: boolean\n  __N_SSP?: boolean\n}\n\nexport type DocumentContext = NextPageContext & {\n  renderPage: RenderPage\n  defaultGetInitialProps(\n    ctx: DocumentContext,\n    options?: { nonce?: string }\n  ): Promise<DocumentInitialProps>\n}\n\nexport type DocumentInitialProps = RenderPageResult & {\n  styles?: React.ReactElement[] | Iterable<React.ReactNode> | JSX.Element\n}\n\nexport type DocumentProps = DocumentInitialProps & HtmlProps\n\n/**\n * Next `API` route request\n */\nexport interface NextApiRequest extends IncomingMessage {\n  /**\n   * Object of `query` values from url\n   */\n  query: Partial<{\n    [key: string]: string | string[]\n  }>\n  /**\n   * Object of `cookies` from header\n   */\n  cookies: Partial<{\n    [key: string]: string\n  }>\n\n  body: any\n\n  env: Env\n\n  draftMode?: boolean\n\n  preview?: boolean\n  /**\n   * Preview data set on the request, if any\n   * */\n  previewData?: PreviewData\n}\n\n/**\n * Send body of response\n */\ntype Send<T> = (body: T) => void\n\n/**\n * Next `API` route response\n */\nexport type NextApiResponse<Data = any> = ServerResponse & {\n  /**\n   * Send data `any` data in response\n   */\n  send: Send<Data>\n  /**\n   * Send data `json` data in response\n   */\n  json: Send<Data>\n  status: (statusCode: number) => NextApiResponse<Data>\n  redirect(url: string): NextApiResponse<Data>\n  redirect(status: number, url: string): NextApiResponse<Data>\n\n  /**\n   * Set draft mode\n   */\n  setDraftMode: (options: { enable: boolean }) => NextApiResponse<Data>\n\n  /**\n   * Set preview data for Next.js' prerender mode\n   */\n  setPreviewData: (\n    data: object | string,\n    options?: {\n      /**\n       * Specifies the number (in seconds) for the preview session to last for.\n       * The given number will be converted to an integer by rounding down.\n       * By default, no maximum age is set and the preview session finishes\n       * when the client shuts down (browser is closed).\n       */\n      maxAge?: number\n      /**\n       * Specifies the path for the preview session to work under. By default,\n       * the path is considered the \"default path\", i.e., any pages under \"/\".\n       */\n      path?: string\n    }\n  ) => NextApiResponse<Data>\n\n  /**\n   * Clear preview data for Next.js' prerender mode\n   */\n  clearPreviewData: (options?: { path?: string }) => NextApiResponse<Data>\n\n  /**\n   * Revalidate a specific page and regenerate it using On-Demand Incremental\n   * Static Regeneration.\n   * The path should be an actual path, not a rewritten path. E.g. for\n   * \"/blog/[slug]\" this should be \"/blog/post-1\".\n   * @link https://nextjs.org/docs/app/building-your-application/data-fetching/incremental-static-regeneration#on-demand-revalidation-with-revalidatepath\n   */\n  revalidate: (\n    urlPath: string,\n    opts?: {\n      unstable_onlyGenerated?: boolean\n    }\n  ) => Promise<void>\n}\n\n/**\n * Next `API` route handler\n */\nexport type NextApiHandler<T = any> = (\n  req: NextApiRequest,\n  res: NextApiResponse<T>\n) => unknown | Promise<unknown>\n\n/**\n * Utils\n */\nexport function execOnce<T extends (...args: any[]) => ReturnType<T>>(\n  fn: T\n): T {\n  let used = false\n  let result: ReturnType<T>\n\n  return ((...args: any[]) => {\n    if (!used) {\n      used = true\n      result = fn(...args)\n    }\n    return result\n  }) as T\n}\n\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/\nexport const isAbsoluteUrl = (url: string) => ABSOLUTE_URL_REGEX.test(url)\n\nexport function getLocationOrigin() {\n  const { protocol, hostname, port } = window.location\n  return `${protocol}//${hostname}${port ? ':' + port : ''}`\n}\n\nexport function getURL() {\n  const { href } = window.location\n  const origin = getLocationOrigin()\n  return href.substring(origin.length)\n}\n\nexport function getDisplayName<P>(Component: ComponentType<P>) {\n  return typeof Component === 'string'\n    ? Component\n    : Component.displayName || Component.name || 'Unknown'\n}\n\nexport function isResSent(res: ServerResponse) {\n  return res.finished || res.headersSent\n}\n\nexport function normalizeRepeatedSlashes(url: string) {\n  const urlParts = url.split('?')\n  const urlNoQuery = urlParts[0]\n\n  return (\n    urlNoQuery\n      // first we replace any non-encoded backslashes with forward\n      // then normalize repeated forward slashes\n      .replace(/\\\\/g, '/')\n      .replace(/\\/\\/+/g, '/') +\n    (urlParts[1] ? `?${urlParts.slice(1).join('?')}` : '')\n  )\n}\n\nexport async function loadGetInitialProps<\n  C extends BaseContext,\n  IP = {},\n  P = {},\n>(App: NextComponentType<C, IP, P>, ctx: C): Promise<IP> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (App.prototype?.getInitialProps) {\n      const message = `\"${getDisplayName(\n        App\n      )}.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.`\n      throw new Error(message)\n    }\n  }\n  // when called from _app `ctx` is nested in `ctx`\n  const res = ctx.res || (ctx.ctx && ctx.ctx.res)\n\n  if (!App.getInitialProps) {\n    if (ctx.ctx && ctx.Component) {\n      // @ts-ignore pageProps default\n      return {\n        pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx),\n      }\n    }\n    return {} as IP\n  }\n\n  const props = await App.getInitialProps(ctx)\n\n  if (res && isResSent(res)) {\n    return props\n  }\n\n  if (!props) {\n    const message = `\"${getDisplayName(\n      App\n    )}.getInitialProps()\" should resolve to an object. But found \"${props}\" instead.`\n    throw new Error(message)\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (Object.keys(props).length === 0 && !ctx.ctx) {\n      console.warn(\n        `${getDisplayName(\n          App\n        )} returned an empty object from \\`getInitialProps\\`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps`\n      )\n    }\n  }\n\n  return props\n}\n\nexport const SP = typeof performance !== 'undefined'\nexport const ST =\n  SP &&\n  (['mark', 'measure', 'getEntriesByName'] as const).every(\n    (method) => typeof performance[method] === 'function'\n  )\n\nexport class DecodeError extends Error {}\nexport class NormalizeError extends Error {}\nexport class PageNotFoundError extends Error {\n  code: string\n\n  constructor(page: string) {\n    super()\n    this.code = 'ENOENT'\n    this.name = 'PageNotFoundError'\n    this.message = `Cannot find module for page: ${page}`\n  }\n}\n\nexport class MissingStaticPage extends Error {\n  constructor(page: string, message: string) {\n    super()\n    this.message = `Failed to load static file for page: ${page} ${message}`\n  }\n}\n\nexport class MiddlewareNotFoundError extends Error {\n  code: string\n  constructor() {\n    super()\n    this.code = 'ENOENT'\n    this.message = `Cannot find the middleware module`\n  }\n}\n\nexport interface CacheFs {\n  existsSync: typeof fs.existsSync\n  readFile: typeof fs.promises.readFile\n  readFileSync: typeof fs.readFileSync\n  writeFile(f: string, d: any): Promise<void>\n  mkdir(dir: string): Promise<void | string>\n  stat(f: string): Promise<{ mtime: Date }>\n}\n\nexport function stringifyError(error: Error) {\n  return JSON.stringify({ message: error.message, stack: error.stack })\n}\n"], "names": ["DecodeError", "MiddlewareNotFoundError", "MissingStaticPage", "NormalizeError", "PageNotFoundError", "SP", "ST", "WEB_VITALS", "execOnce", "getDisplayName", "getLocationOrigin", "getURL", "isAbsoluteUrl", "isResSent", "loadGetInitialProps", "normalizeRepeatedSlashes", "stringifyError", "fn", "used", "result", "args", "ABSOLUTE_URL_REGEX", "url", "test", "protocol", "hostname", "port", "window", "location", "href", "origin", "substring", "length", "Component", "displayName", "name", "res", "finished", "headersSent", "urlParts", "split", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "replace", "slice", "join", "App", "ctx", "process", "env", "NODE_ENV", "prototype", "getInitialProps", "message", "Error", "pageProps", "props", "Object", "keys", "console", "warn", "performance", "every", "method", "constructor", "page", "code", "error", "JSON", "stringify", "stack"], "mappings": "AA8WM+C,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAsDlBjD,WAAW,EAAA;eAAXA;;IAoBAC,uBAAuB,EAAA;eAAvBA;;IAPAC,iBAAiB,EAAA;eAAjBA;;IAZAC,cAAc,EAAA;eAAdA;;IACAC,iBAAiB,EAAA;eAAjBA;;IATAC,EAAE,EAAA;eAAFA;;IACAC,EAAE,EAAA;eAAFA;;IAlXAC,UAAU,EAAA;eAAVA;;IAsQGC,QAAQ,EAAA;eAARA;;IA+BAC,cAAc,EAAA;eAAdA;;IAXAC,iBAAiB,EAAA;eAAjBA;;IAKAC,MAAM,EAAA;eAANA;;IAPHC,aAAa,EAAA;eAAbA;;IAmBGC,SAAS,EAAA;eAATA;;IAkBMC,mBAAmB,EAAA;eAAnBA;;IAdNC,wBAAwB,EAAA;eAAxBA;;IA+GAC,cAAc,EAAA;eAAdA;;;AA9ZT,MAAMT,aAAa;IAAC;IAAO;IAAO;IAAO;IAAO;IAAO;CAAO;AAsQ9D,SAASC,SACdS,EAAK;IAEL,IAAIC,OAAO;IACX,IAAIC;IAEJ,OAAQ;yCAAIC,OAAAA,IAAAA,MAAAA,OAAAA,OAAAA,GAAAA,OAAAA,MAAAA,OAAAA;YAAAA,IAAAA,CAAAA,KAAAA,GAAAA,SAAAA,CAAAA,KAAAA;;QACV,IAAI,CAACF,MAAM;YACTA,OAAO;YACPC,SAASF,MAAMG;QACjB;QACA,OAAOD;IACT;AACF;AAEA,0DAA0D;AAC1D,gEAAgE;AAChE,MAAME,qBAAqB;AACpB,MAAMT,gBAAgB,CAACU,MAAgBD,mBAAmBE,IAAI,CAACD;AAE/D,SAASZ;IACd,MAAM,EAAEc,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,EAAE,GAAGC,OAAOC,QAAQ;IACpD,OAAUJ,WAAS,OAAIC,WAAWC,CAAAA,OAAO,MAAMA,OAAO,EAAC;AACzD;AAEO,SAASf;IACd,MAAM,EAAEkB,IAAI,EAAE,GAAGF,OAAOC,QAAQ;IAChC,MAAME,SAASpB;IACf,OAAOmB,KAAKE,SAAS,CAACD,OAAOE,MAAM;AACrC;AAEO,SAASvB,eAAkBwB,SAA2B;IAC3D,OAAO,OAAOA,cAAc,WACxBA,YACAA,UAAUC,WAAW,IAAID,UAAUE,IAAI,IAAI;AACjD;AAEO,SAAStB,UAAUuB,GAAmB;IAC3C,OAAOA,IAAIC,QAAQ,IAAID,IAAIE,WAAW;AACxC;AAEO,SAASvB,yBAAyBO,GAAW;IAClD,MAAMiB,WAAWjB,IAAIkB,KAAK,CAAC;IAC3B,MAAMC,aAAaF,QAAQ,CAAC,EAAE;IAE9B,OACEE,WACE,4DAA4D;IAC5D,0CAA0C;KACzCC,OAAO,CAAC,OAAO,KACfA,OAAO,CAAC,UAAU,OACpBH,CAAAA,QAAQ,CAAC,EAAE,GAAI,MAAGA,SAASI,KAAK,CAAC,GAAGC,IAAI,CAAC,OAAS,EAAC;AAExD;AAEO,eAAe9B,oBAIpB+B,GAAgC,EAAEC,GAAM;IACxC,wCAA2C;YACrCD;QAAJ,IAAA,CAAIA,iBAAAA,IAAIK,SAAS,KAAA,OAAA,KAAA,IAAbL,eAAeM,eAAe,EAAE;YAClC,MAAMC,UAAW,MAAG3C,eAClBoC,OACA;YACF,MAAM,OAAA,cAAkB,CAAlB,IAAIQ,MAAMD,UAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAiB;QACzB;IACF;IACA,iDAAiD;IACjD,MAAMhB,MAAMU,IAAIV,GAAG,IAAKU,IAAIA,GAAG,IAAIA,IAAIA,GAAG,CAACV,GAAG;IAE9C,IAAI,CAACS,IAAIM,eAAe,EAAE;QACxB,IAAIL,IAAIA,GAAG,IAAIA,IAAIb,SAAS,EAAE;YAC5B,+BAA+B;YAC/B,OAAO;gBACLqB,WAAW,MAAMxC,oBAAoBgC,IAAIb,SAAS,EAAEa,IAAIA,GAAG;YAC7D;QACF;QACA,OAAO,CAAC;IACV;IAEA,MAAMS,QAAQ,MAAMV,IAAIM,eAAe,CAACL;IAExC,IAAIV,OAAOvB,UAAUuB,MAAM;QACzB,OAAOmB;IACT;IAEA,IAAI,CAACA,OAAO;QACV,MAAMH,UAAW,MAAG3C,eAClBoC,OACA,iEAA8DU,QAAM;QACtE,MAAM,OAAA,cAAkB,CAAlB,IAAIF,MAAMD,UAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAiB;IACzB;IAEA,IAAIL,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,IAAIO,OAAOC,IAAI,CAACF,OAAOvB,MAAM,KAAK,KAAK,CAACc,IAAIA,GAAG,EAAE;YAC/CY,QAAQC,IAAI,CACT,KAAElD,eACDoC,OACA;QAEN;IACF;IAEA,OAAOU;AACT;AAEO,MAAMlD,KAAK,OAAOuD,gBAAgB;AAClC,MAAMtD,KACXD,MACC;IAAC;IAAQ;IAAW;CAAmB,CAAWwD,KAAK,CACtD,CAACC,SAAW,OAAOF,WAAW,CAACE,OAAO,KAAK;AAGxC,MAAM9D,oBAAoBqD;AAAO;AACjC,MAAMlD,uBAAuBkD;AAAO;AACpC,MAAMjD,0BAA0BiD;IAGrCU,YAAYC,IAAY,CAAE;QACxB,KAAK;QACL,IAAI,CAACC,IAAI,GAAG;QACZ,IAAI,CAAC9B,IAAI,GAAG;QACZ,IAAI,CAACiB,OAAO,GAAI,kCAA+BY;IACjD;AACF;AAEO,MAAM9D,0BAA0BmD;IACrCU,YAAYC,IAAY,EAAEZ,OAAe,CAAE;QACzC,KAAK;QACL,IAAI,CAACA,OAAO,GAAI,0CAAuCY,OAAK,MAAGZ;IACjE;AACF;AAEO,MAAMnD,gCAAgCoD;IAE3CU,aAAc;QACZ,KAAK;QACL,IAAI,CAACE,IAAI,GAAG;QACZ,IAAI,CAACb,OAAO,GAAI;IAClB;AACF;AAWO,SAASpC,eAAekD,KAAY;IACzC,OAAOC,KAAKC,SAAS,CAAC;QAAEhB,SAASc,MAAMd,OAAO;QAAEiB,OAAOH,MAAMG,KAAK;IAAC;AACrE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2027, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/node_modules/next/src/shared/lib/router/utils/is-local-url.ts"], "sourcesContent": ["import { isAbsoluteUrl, getLocationOrigin } from '../../utils'\nimport { hasBasePath } from '../../../../client/has-base-path'\n\n/**\n * Detects whether a given url is routable by the Next.js router (browser only).\n */\nexport function isLocalURL(url: string): boolean {\n  // prevent a hydration mismatch on href for url with anchor refs\n  if (!isAbsoluteUrl(url)) return true\n  try {\n    // absolute urls can be local if they are on the same origin\n    const locationOrigin = getLocationOrigin()\n    const resolved = new URL(url, locationOrigin)\n    return resolved.origin === locationOrigin && hasBasePath(resolved.pathname)\n  } catch (_) {\n    return false\n  }\n}\n"], "names": ["isLocalURL", "url", "isAbsoluteUrl", "locationOrigin", "getLocationOrigin", "resolved", "URL", "origin", "has<PERSON>ase<PERSON><PERSON>", "pathname", "_"], "mappings": ";;;;+BAMgBA,cAAAA;;;eAAAA;;;uBANiC;6BACrB;AAKrB,SAASA,WAAWC,GAAW;IACpC,gEAAgE;IAChE,IAAI,CAACC,CAAAA,GAAAA,OAAAA,aAAa,EAACD,MAAM,OAAO;IAChC,IAAI;QACF,4DAA4D;QAC5D,MAAME,iBAAiBC,CAAAA,GAAAA,OAAAA,iBAAiB;QACxC,MAAMC,WAAW,IAAIC,IAAIL,KAAKE;QAC9B,OAAOE,SAASE,MAAM,KAAKJ,kBAAkBK,CAAAA,GAAAA,aAAAA,WAAW,EAACH,SAASI,QAAQ;IAC5E,EAAE,OAAOC,GAAG;QACV,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2056, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/node_modules/next/src/shared/lib/utils/error-once.ts"], "sourcesContent": ["let errorOnce = (_: string) => {}\nif (process.env.NODE_ENV !== 'production') {\n  const errors = new Set<string>()\n  errorOnce = (msg: string) => {\n    if (!errors.has(msg)) {\n      console.error(msg)\n    }\n    errors.add(msg)\n  }\n}\n\nexport { errorOnce }\n"], "names": ["errorOnce", "_", "process", "env", "NODE_ENV", "errors", "Set", "msg", "has", "console", "error", "add"], "mappings": "AACIE,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;+BAUpBJ,aAAAA;;;eAAAA;;;AAXT,IAAIA,YAAY,CAACC,KAAe;AAChC,wCAA2C;IACzC,MAAMI,SAAS,IAAIC;IACnBN,YAAY,CAACO;QACX,IAAI,CAACF,OAAOG,GAAG,CAACD,MAAM;YACpBE,QAAQC,KAAK,CAACH;QAChB;QACAF,OAAOM,GAAG,CAACJ;IACb;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2082, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/node_modules/next/src/client/app-dir/link.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useOptimistic, useRef } from 'react'\nimport type { UrlObject } from 'url'\nimport { formatUrl } from '../../shared/lib/router/utils/format-url'\nimport { AppRouterContext } from '../../shared/lib/app-router-context.shared-runtime'\nimport { PrefetchKind } from '../components/router-reducer/router-reducer-types'\nimport { useMergedRef } from '../use-merged-ref'\nimport { isAbsoluteUrl } from '../../shared/lib/utils'\nimport { addBasePath } from '../add-base-path'\nimport { warnOnce } from '../../shared/lib/utils/warn-once'\nimport type { PENDING_LINK_STATUS } from '../components/links'\nimport {\n  IDLE_LINK_STATUS,\n  mountLinkInstance,\n  onNavigationIntent,\n  unmountLinkForCurrentNavigation,\n  unmountPrefetchableInstance,\n  type LinkInstance,\n} from '../components/links'\nimport { isLocalURL } from '../../shared/lib/router/utils/is-local-url'\nimport { dispatchNavigateAction } from '../components/app-router-instance'\nimport { errorOnce } from '../../shared/lib/utils/error-once'\n\ntype Url = string | UrlObject\ntype RequiredKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? never : K\n}[keyof T]\ntype OptionalKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? K : never\n}[keyof T]\n\ntype OnNavigateEventHandler = (event: { preventDefault: () => void }) => void\n\ntype InternalLinkProps = {\n  /**\n   * **Required**. The path or URL to navigate to. It can also be an object (similar to `URL`).\n   *\n   * @example\n   * ```tsx\n   * // Navigate to /dashboard:\n   * <Link href=\"/dashboard\">Dashboard</Link>\n   *\n   * // Navigate to /about?name=test:\n   * <Link href={{ pathname: '/about', query: { name: 'test' } }}>\n   *   About\n   * </Link>\n   * ```\n   *\n   * @remarks\n   * - For external URLs, use a fully qualified URL such as `https://...`.\n   * - In the App Router, dynamic routes must not include bracketed segments in `href`.\n   */\n  href: Url\n\n  /**\n   * @deprecated v10.0.0: `href` props pointing to a dynamic route are\n   * automatically resolved and no longer require the `as` prop.\n   */\n  as?: Url\n\n  /**\n   * Replace the current `history` state instead of adding a new URL into the stack.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/about\" replace>\n   *   About (replaces the history state)\n   * </Link>\n   * ```\n   */\n  replace?: boolean\n\n  /**\n   * Whether to override the default scroll behavior. If `true`, Next.js attempts to maintain\n   * the scroll position if the newly navigated page is still visible. If not, it scrolls to the top.\n   *\n   * If `false`, Next.js will not modify the scroll behavior at all.\n   *\n   * @defaultValue `true`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" scroll={false}>\n   *   No auto scroll\n   * </Link>\n   * ```\n   */\n  scroll?: boolean\n\n  /**\n   * Update the path of the current page without rerunning data fetching methods\n   * like `getStaticProps`, `getServerSideProps`, or `getInitialProps`.\n   *\n   * @remarks\n   * `shallow` only applies to the Pages Router. For the App Router, see the\n   * [following documentation](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#using-the-native-history-api).\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/blog\" shallow>\n   *   Shallow navigation\n   * </Link>\n   * ```\n   */\n  shallow?: boolean\n\n  /**\n   * Forces `Link` to pass its `href` to the child component. Useful if the child is a custom\n   * component that wraps an `<a>` tag, or if you're using certain styling libraries.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" passHref>\n   *   <MyStyledAnchor>Dashboard</MyStyledAnchor>\n   * </Link>\n   * ```\n   */\n  passHref?: boolean\n\n  /**\n   * Prefetch the page in the background.\n   * Any `<Link />` that is in the viewport (initially or through scroll) will be prefetched.\n   * Prefetch can be disabled by passing `prefetch={false}`.\n   *\n   * @remarks\n   * Prefetching is only enabled in production.\n   *\n   * - In the **App Router**:\n   *   - `null` (default): Prefetch behavior depends on static vs dynamic routes:\n   *     - Static routes: fully prefetched\n   *     - Dynamic routes: partial prefetch to the nearest segment with a `loading.js`\n   *   - `true`: Always prefetch the full route and data.\n   *   - `false`: Disable prefetching on both viewport and hover.\n   * - In the **Pages Router**:\n   *   - `true` (default): Prefetches the route and data in the background on viewport or hover.\n   *   - `false`: Prefetch only on hover, not on viewport.\n   *\n   * @defaultValue `true` (Pages Router) or `null` (App Router)\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" prefetch={false}>\n   *   Dashboard\n   * </Link>\n   * ```\n   */\n  prefetch?: boolean | null\n\n  /**\n   * (unstable) Switch to a dynamic prefetch on hover. Effectively the same as\n   * updating the prefetch prop to `true` in a mouse event.\n   */\n  unstable_dynamicOnHover?: boolean\n\n  /**\n   * The active locale is automatically prepended in the Pages Router. `locale` allows for providing\n   * a different locale, or can be set to `false` to opt out of automatic locale behavior.\n   *\n   * @remarks\n   * Note: locale only applies in the Pages Router and is ignored in the App Router.\n   *\n   * @example\n   * ```tsx\n   * // Use the 'fr' locale:\n   * <Link href=\"/about\" locale=\"fr\">\n   *   About (French)\n   * </Link>\n   *\n   * // Disable locale prefix:\n   * <Link href=\"/about\" locale={false}>\n   *   About (no locale prefix)\n   * </Link>\n   * ```\n   */\n  locale?: string | false\n\n  /**\n   * Enable legacy link behavior, requiring an `<a>` tag to wrap the child content\n   * if the child is a string or number.\n   *\n   * @deprecated This will be removed in v16\n   * @defaultValue `false`\n   * @see https://github.com/vercel/next.js/commit/489e65ed98544e69b0afd7e0cfc3f9f6c2b803b7\n   */\n  legacyBehavior?: boolean\n\n  /**\n   * Optional event handler for when the mouse pointer is moved onto the `<Link>`.\n   */\n  onMouseEnter?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is touched.\n   */\n  onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is clicked.\n   */\n  onClick?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is navigated.\n   */\n  onNavigate?: OnNavigateEventHandler\n}\n\n// TODO-APP: Include the full set of Anchor props\n// adding this to the publicly exported type currently breaks existing apps\n\n// `RouteInferType` is a stub here to avoid breaking `typedRoutes` when the type\n// isn't generated yet. It will be replaced when the webpack plugin runs.\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type LinkProps<RouteInferType = any> = InternalLinkProps\ntype LinkPropsRequired = RequiredKeys<LinkProps>\ntype LinkPropsOptional = OptionalKeys<Omit<InternalLinkProps, 'locale'>>\n\nfunction isModifiedEvent(event: React.MouseEvent): boolean {\n  const eventTarget = event.currentTarget as HTMLAnchorElement | SVGAElement\n  const target = eventTarget.getAttribute('target')\n  return (\n    (target && target !== '_self') ||\n    event.metaKey ||\n    event.ctrlKey ||\n    event.shiftKey ||\n    event.altKey || // triggers resource download\n    (event.nativeEvent && event.nativeEvent.which === 2)\n  )\n}\n\nfunction linkClicked(\n  e: React.MouseEvent,\n  href: string,\n  as: string,\n  linkInstanceRef: React.RefObject<LinkInstance | null>,\n  replace?: boolean,\n  scroll?: boolean,\n  onNavigate?: OnNavigateEventHandler\n): void {\n  const { nodeName } = e.currentTarget\n\n  // anchors inside an svg have a lowercase nodeName\n  const isAnchorNodeName = nodeName.toUpperCase() === 'A'\n\n  if (\n    (isAnchorNodeName && isModifiedEvent(e)) ||\n    e.currentTarget.hasAttribute('download')\n  ) {\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  if (!isLocalURL(href)) {\n    if (replace) {\n      // browser default behavior does not replace the history state\n      // so we need to do it manually\n      e.preventDefault()\n      location.replace(href)\n    }\n\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  e.preventDefault()\n\n  const navigate = () => {\n    if (onNavigate) {\n      let isDefaultPrevented = false\n\n      onNavigate({\n        preventDefault: () => {\n          isDefaultPrevented = true\n        },\n      })\n\n      if (isDefaultPrevented) {\n        return\n      }\n    }\n\n    dispatchNavigateAction(\n      as || href,\n      replace ? 'replace' : 'push',\n      scroll ?? true,\n      linkInstanceRef.current\n    )\n  }\n\n  React.startTransition(navigate)\n}\n\nfunction formatStringOrUrl(urlObjOrString: UrlObject | string): string {\n  if (typeof urlObjOrString === 'string') {\n    return urlObjOrString\n  }\n\n  return formatUrl(urlObjOrString)\n}\n\n/**\n * A React component that extends the HTML `<a>` element to provide\n * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation. This is the primary way to navigate between routes in Next.js.\n *\n * @remarks\n * - Prefetching is only enabled in production.\n *\n * @see https://nextjs.org/docs/app/api-reference/components/link\n */\nexport default function LinkComponent(\n  props: LinkProps & {\n    children: React.ReactNode\n    ref: React.Ref<HTMLAnchorElement>\n  }\n) {\n  const [linkStatus, setOptimisticLinkStatus] = useOptimistic(IDLE_LINK_STATUS)\n\n  let children: React.ReactNode\n\n  const linkInstanceRef = useRef<LinkInstance | null>(null)\n\n  const {\n    href: hrefProp,\n    as: asProp,\n    children: childrenProp,\n    prefetch: prefetchProp = null,\n    passHref,\n    replace,\n    shallow,\n    scroll,\n    onClick,\n    onMouseEnter: onMouseEnterProp,\n    onTouchStart: onTouchStartProp,\n    legacyBehavior = false,\n    onNavigate,\n    ref: forwardedRef,\n    unstable_dynamicOnHover,\n    ...restProps\n  } = props\n\n  children = childrenProp\n\n  if (\n    legacyBehavior &&\n    (typeof children === 'string' || typeof children === 'number')\n  ) {\n    children = <a>{children}</a>\n  }\n\n  const router = React.useContext(AppRouterContext)\n\n  const prefetchEnabled = prefetchProp !== false\n  /**\n   * The possible states for prefetch are:\n   * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n   * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n   * - false: we will not prefetch if in the viewport at all\n   * - 'unstable_dynamicOnHover': this starts in \"auto\" mode, but switches to \"full\" when the link is hovered\n   */\n  const appPrefetchKind =\n    prefetchProp === null ? PrefetchKind.AUTO : PrefetchKind.FULL\n\n  if (process.env.NODE_ENV !== 'production') {\n    function createPropError(args: {\n      key: string\n      expected: string\n      actual: string\n    }) {\n      return new Error(\n        `Failed prop type: The prop \\`${args.key}\\` expects a ${args.expected} in \\`<Link>\\`, but got \\`${args.actual}\\` instead.` +\n          (typeof window !== 'undefined'\n            ? \"\\nOpen your browser's console to view the Component stack trace.\"\n            : '')\n      )\n    }\n\n    // TypeScript trick for type-guarding:\n    const requiredPropsGuard: Record<LinkPropsRequired, true> = {\n      href: true,\n    } as const\n    const requiredProps: LinkPropsRequired[] = Object.keys(\n      requiredPropsGuard\n    ) as LinkPropsRequired[]\n    requiredProps.forEach((key: LinkPropsRequired) => {\n      if (key === 'href') {\n        if (\n          props[key] == null ||\n          (typeof props[key] !== 'string' && typeof props[key] !== 'object')\n        ) {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: props[key] === null ? 'null' : typeof props[key],\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n\n    // TypeScript trick for type-guarding:\n    const optionalPropsGuard: Record<LinkPropsOptional, true> = {\n      as: true,\n      replace: true,\n      scroll: true,\n      shallow: true,\n      passHref: true,\n      prefetch: true,\n      unstable_dynamicOnHover: true,\n      onClick: true,\n      onMouseEnter: true,\n      onTouchStart: true,\n      legacyBehavior: true,\n      onNavigate: true,\n    } as const\n    const optionalProps: LinkPropsOptional[] = Object.keys(\n      optionalPropsGuard\n    ) as LinkPropsOptional[]\n    optionalProps.forEach((key: LinkPropsOptional) => {\n      const valType = typeof props[key]\n\n      if (key === 'as') {\n        if (props[key] && valType !== 'string' && valType !== 'object') {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'onClick' ||\n        key === 'onMouseEnter' ||\n        key === 'onTouchStart' ||\n        key === 'onNavigate'\n      ) {\n        if (props[key] && valType !== 'function') {\n          throw createPropError({\n            key,\n            expected: '`function`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'replace' ||\n        key === 'scroll' ||\n        key === 'shallow' ||\n        key === 'passHref' ||\n        key === 'prefetch' ||\n        key === 'legacyBehavior' ||\n        key === 'unstable_dynamicOnHover'\n      ) {\n        if (props[key] != null && valType !== 'boolean') {\n          throw createPropError({\n            key,\n            expected: '`boolean`',\n            actual: valType,\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (props.locale) {\n      warnOnce(\n        'The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization'\n      )\n    }\n    if (!asProp) {\n      let href: string | undefined\n      if (typeof hrefProp === 'string') {\n        href = hrefProp\n      } else if (\n        typeof hrefProp === 'object' &&\n        typeof hrefProp.pathname === 'string'\n      ) {\n        href = hrefProp.pathname\n      }\n\n      if (href) {\n        const hasDynamicSegment = href\n          .split('/')\n          .some((segment) => segment.startsWith('[') && segment.endsWith(']'))\n\n        if (hasDynamicSegment) {\n          throw new Error(\n            `Dynamic href \\`${href}\\` found in <Link> while using the \\`/app\\` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href`\n          )\n        }\n      }\n    }\n  }\n\n  const { href, as } = React.useMemo(() => {\n    const resolvedHref = formatStringOrUrl(hrefProp)\n    return {\n      href: resolvedHref,\n      as: asProp ? formatStringOrUrl(asProp) : resolvedHref,\n    }\n  }, [hrefProp, asProp])\n\n  // This will return the first child, if multiple are provided it will throw an error\n  let child: any\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      if (onClick) {\n        console.warn(\n          `\"onClick\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link`\n        )\n      }\n      if (onMouseEnterProp) {\n        console.warn(\n          `\"onMouseEnter\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link`\n        )\n      }\n      try {\n        child = React.Children.only(children)\n      } catch (err) {\n        if (!children) {\n          throw new Error(\n            `No children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but one child is required https://nextjs.org/docs/messages/link-no-children`\n          )\n        }\n        throw new Error(\n          `Multiple children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children` +\n            (typeof window !== 'undefined'\n              ? \" \\nOpen your browser's console to view the Component stack trace.\"\n              : '')\n        )\n      }\n    } else {\n      child = React.Children.only(children)\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if ((children as any)?.type === 'a') {\n        throw new Error(\n          'Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'\n        )\n      }\n    }\n  }\n\n  const childRef: any = legacyBehavior\n    ? child && typeof child === 'object' && child.ref\n    : forwardedRef\n\n  // Use a callback ref to attach an IntersectionObserver to the anchor tag on\n  // mount. In the future we will also use this to keep track of all the\n  // currently mounted <Link> instances, e.g. so we can re-prefetch them after\n  // a revalidation or refresh.\n  const observeLinkVisibilityOnMount = React.useCallback(\n    (element: HTMLAnchorElement | SVGAElement) => {\n      if (router !== null) {\n        linkInstanceRef.current = mountLinkInstance(\n          element,\n          href,\n          router,\n          appPrefetchKind,\n          prefetchEnabled,\n          setOptimisticLinkStatus\n        )\n      }\n\n      return () => {\n        if (linkInstanceRef.current) {\n          unmountLinkForCurrentNavigation(linkInstanceRef.current)\n          linkInstanceRef.current = null\n        }\n        unmountPrefetchableInstance(element)\n      }\n    },\n    [prefetchEnabled, href, router, appPrefetchKind, setOptimisticLinkStatus]\n  )\n\n  const mergedRef = useMergedRef(observeLinkVisibilityOnMount, childRef)\n\n  const childProps: {\n    onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n    onMouseEnter: React.MouseEventHandler<HTMLAnchorElement>\n    onClick: React.MouseEventHandler<HTMLAnchorElement>\n    href?: string\n    ref?: any\n  } = {\n    ref: mergedRef,\n    onClick(e) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (!e) {\n          throw new Error(\n            `Component rendered inside next/link has to pass click event to \"onClick\" prop.`\n          )\n        }\n      }\n\n      if (!legacyBehavior && typeof onClick === 'function') {\n        onClick(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onClick === 'function'\n      ) {\n        child.props.onClick(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (e.defaultPrevented) {\n        return\n      }\n\n      linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate)\n    },\n    onMouseEnter(e) {\n      if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n        onMouseEnterProp(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onMouseEnter === 'function'\n      ) {\n        child.props.onMouseEnter(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (!prefetchEnabled || process.env.NODE_ENV === 'development') {\n        return\n      }\n\n      const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n      onNavigationIntent(\n        e.currentTarget as HTMLAnchorElement | SVGAElement,\n        upgradeToDynamicPrefetch\n      )\n    },\n    onTouchStart: process.env.__NEXT_LINK_NO_TOUCH_START\n      ? undefined\n      : function onTouchStart(e) {\n          if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n            onTouchStartProp(e)\n          }\n\n          if (\n            legacyBehavior &&\n            child.props &&\n            typeof child.props.onTouchStart === 'function'\n          ) {\n            child.props.onTouchStart(e)\n          }\n\n          if (!router) {\n            return\n          }\n\n          if (!prefetchEnabled) {\n            return\n          }\n\n          const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n          onNavigationIntent(\n            e.currentTarget as HTMLAnchorElement | SVGAElement,\n            upgradeToDynamicPrefetch\n          )\n        },\n  }\n\n  // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n  // defined, we specify the current 'href', so that repetition is not needed by the user.\n  // If the url is absolute, we can bypass the logic to prepend the basePath.\n  if (isAbsoluteUrl(as)) {\n    childProps.href = as\n  } else if (\n    !legacyBehavior ||\n    passHref ||\n    (child.type === 'a' && !('href' in child.props))\n  ) {\n    childProps.href = addBasePath(as)\n  }\n\n  let link: React.ReactNode\n\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      errorOnce(\n        '`legacyBehavior` is deprecated and will be removed in a future ' +\n          'release. A codemod is available to upgrade your components:\\n\\n' +\n          'npx @next/codemod@latest new-link .\\n\\n' +\n          'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components'\n      )\n    }\n    link = React.cloneElement(child, childProps)\n  } else {\n    link = (\n      <a {...restProps} {...childProps}>\n        {children}\n      </a>\n    )\n  }\n\n  return (\n    <LinkStatusContext.Provider value={linkStatus}>\n      {link}\n    </LinkStatusContext.Provider>\n  )\n}\n\nconst LinkStatusContext = createContext<\n  typeof PENDING_LINK_STATUS | typeof IDLE_LINK_STATUS\n>(IDLE_LINK_STATUS)\n\nexport const useLinkStatus = () => {\n  return useContext(LinkStatusContext)\n}\n"], "names": ["LinkComponent", "useLinkStatus", "isModifiedEvent", "event", "eventTarget", "currentTarget", "target", "getAttribute", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "nativeEvent", "which", "linkClicked", "e", "href", "as", "linkInstanceRef", "replace", "scroll", "onNavigate", "nodeName", "isAnchorNodeName", "toUpperCase", "hasAttribute", "isLocalURL", "preventDefault", "location", "navigate", "isDefaultPrevented", "dispatchNavigateAction", "current", "React", "startTransition", "formatStringOrUrl", "urlObjOrString", "formatUrl", "props", "linkStatus", "setOptimisticLinkStatus", "useOptimistic", "IDLE_LINK_STATUS", "children", "useRef", "hrefProp", "asProp", "childrenProp", "prefetch", "prefetchProp", "passHref", "shallow", "onClick", "onMouseEnter", "onMouseEnterProp", "onTouchStart", "onTouchStartProp", "legacyBeh<PERSON>or", "ref", "forwardedRef", "unstable_dynamicOnHover", "restProps", "a", "router", "useContext", "AppRouterContext", "prefetchEnabled", "appPrefetchKind", "PrefetchKind", "AUTO", "FULL", "process", "env", "NODE_ENV", "createPropError", "args", "Error", "key", "expected", "actual", "window", "requiredPropsGuard", "requiredProps", "Object", "keys", "for<PERSON>ach", "_", "optionalPropsGuard", "optionalProps", "valType", "locale", "warnOnce", "pathname", "hasDynamicSegment", "split", "some", "segment", "startsWith", "endsWith", "useMemo", "resolvedHref", "child", "console", "warn", "Children", "only", "err", "type", "childRef", "observeLinkVisibilityOnMount", "useCallback", "element", "mountLinkInstance", "unmountLinkForCurrentNavigation", "unmountPrefetchableInstance", "mergedRef", "useMergedRef", "childProps", "defaultPrevented", "upgradeToDynamicPrefetch", "onNavigationIntent", "__NEXT_LINK_NO_TOUCH_START", "undefined", "isAbsoluteUrl", "addBasePath", "link", "errorOnce", "cloneElement", "LinkStatusContext", "Provider", "value", "createContext"], "mappings": "AAkXMuE,QAAQC,GAAG,CAACC,QAAQ,KAAK;AAlX/B;;;;;;;;;;;;;;;;IAmTA;;;;;;;;;CASC,GACD,OAyZC,EAAA;eAzZuBzE;;IA+ZXC,aAAa,EAAA;eAAbA;;;;;iEA1tB2D;2BAE9C;+CACO;oCACJ;8BACA;uBACC;6BACF;0BACH;uBASlB;4BACoB;mCACY;2BACb;AA0M1B,SAASC,gBAAgBC,KAAuB;IAC9C,MAAMC,cAAcD,MAAME,aAAa;IACvC,MAAMC,SAASF,YAAYG,YAAY,CAAC;IACxC,OACGD,UAAUA,WAAW,WACtBH,MAAMK,OAAO,IACbL,MAAMM,OAAO,IACbN,MAAMO,QAAQ,IACdP,MAAMQ,MAAM,IAAI,6BAA6B;IAC5CR,MAAMS,WAAW,IAAIT,MAAMS,WAAW,CAACC,KAAK,KAAK;AAEtD;AAEA,SAASC,YACPC,CAAmB,EACnBC,IAAY,EACZC,EAAU,EACVC,eAAqD,EACrDC,OAAiB,EACjBC,MAAgB,EAChBC,UAAmC;IAEnC,MAAM,EAAEC,QAAQ,EAAE,GAAGP,EAAEV,aAAa;IAEpC,kDAAkD;IAClD,MAAMkB,mBAAmBD,SAASE,WAAW,OAAO;IAEpD,IACGD,oBAAoBrB,gBAAgBa,MACrCA,EAAEV,aAAa,CAACoB,YAAY,CAAC,aAC7B;QACA,8CAA8C;QAC9C;IACF;IAEA,IAAI,CAACC,CAAAA,GAAAA,YAAAA,UAAU,EAACV,OAAO;QACrB,IAAIG,SAAS;YACX,8DAA8D;YAC9D,+BAA+B;YAC/BJ,EAAEY,cAAc;YAChBC,SAAST,OAAO,CAACH;QACnB;QAEA,8CAA8C;QAC9C;IACF;IAEAD,EAAEY,cAAc;IAEhB,MAAME,WAAW;QACf,IAAIR,YAAY;YACd,IAAIS,qBAAqB;YAEzBT,WAAW;gBACTM,gBAAgB;oBACdG,qBAAqB;gBACvB;YACF;YAEA,IAAIA,oBAAoB;gBACtB;YACF;QACF;QAEAC,CAAAA,GAAAA,mBAAAA,sBAAsB,EACpBd,MAAMD,MACNG,UAAU,YAAY,QACtBC,UAAAA,OAAAA,SAAU,MACVF,gBAAgBc,OAAO;IAE3B;IAEAC,OAAAA,OAAK,CAACC,eAAe,CAACL;AACxB;AAEA,SAASM,kBAAkBC,cAAkC;IAC3D,IAAI,OAAOA,mBAAmB,UAAU;QACtC,OAAOA;IACT;IAEA,OAAOC,CAAAA,GAAAA,WAAAA,SAAS,EAACD;AACnB;AAYe,SAASpC,cACtBsC,KAGC;IAED,MAAM,CAACC,YAAYC,wBAAwB,GAAGC,CAAAA,GAAAA,OAAAA,aAAa,EAACC,OAAAA,gBAAgB;IAE5E,IAAIC;IAEJ,MAAMzB,kBAAkB0B,CAAAA,GAAAA,OAAAA,MAAM,EAAsB;IAEpD,MAAM,EACJ5B,MAAM6B,QAAQ,EACd5B,IAAI6B,MAAM,EACVH,UAAUI,YAAY,EACtBC,UAAUC,eAAe,IAAI,EAC7BC,QAAQ,EACR/B,OAAO,EACPgC,OAAO,EACP/B,MAAM,EACNgC,OAAO,EACPC,cAAcC,gBAAgB,EAC9BC,cAAcC,gBAAgB,EAC9BC,iBAAiB,KAAK,EACtBpC,UAAU,EACVqC,KAAKC,YAAY,EACjBC,uBAAuB,EACvB,GAAGC,WACJ,GAAGvB;IAEJK,WAAWI;IAEX,IACEU,kBACC,CAAA,OAAOd,aAAa,YAAY,OAAOA,aAAa,QAAO,GAC5D;QACAA,WAAAA,WAAAA,GAAW,CAAA,GAAA,YAAA,GAAA,EAACmB,KAAAA;sBAAGnB;;IACjB;IAEA,MAAMoB,SAAS9B,OAAAA,OAAK,CAAC+B,UAAU,CAACC,+BAAAA,gBAAgB;IAEhD,MAAMC,kBAAkBjB,iBAAiB;IACzC;;;;;;GAMC,GACD,MAAMkB,kBACJlB,iBAAiB,OAAOmB,oBAAAA,YAAY,CAACC,IAAI,GAAGD,oBAAAA,YAAY,CAACE,IAAI;IAE/D,wCAA2C;QACzC,SAASI,gBAAgBC,IAIxB;YACC,OAAO,OAAA,cAKN,CALM,IAAIC,MACR,iCAA+BD,KAAKE,GAAG,GAAC,iBAAeF,KAAKG,QAAQ,GAAC,4BAA4BH,KAAKI,MAAM,GAAC,eAC3G,CAAA,OAAOC,WAAW,cACf,qEACA,EAAC,IAJF,qBAAA;uBAAA;4BAAA;8BAAA;YAKP;QACF;QAEA,sCAAsC;QACtC,MAAMC,qBAAsD;YAC1DjE,MAAM;QACR;QACA,MAAMkE,gBAAqCC,OAAOC,IAAI,CACpDH;QAEFC,cAAcG,OAAO,CAAC,CAACR;YACrB,IAAIA,QAAQ,QAAQ;gBAClB,IACEvC,KAAK,CAACuC,IAAI,IAAI,QACb,OAAOvC,KAAK,CAACuC,IAAI,KAAK,YAAY,OAAOvC,KAAK,CAACuC,IAAI,KAAK,UACzD;oBACA,MAAMH,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQzC,KAAK,CAACuC,IAAI,KAAK,OAAO,SAAS,OAAOvC,KAAK,CAACuC,IAAI;oBAC1D;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMS,IAAWT;YACnB;QACF;QAEA,sCAAsC;QACtC,MAAMU,qBAAsD;YAC1DtE,IAAI;YACJE,SAAS;YACTC,QAAQ;YACR+B,SAAS;YACTD,UAAU;YACVF,UAAU;YACVY,yBAAyB;YACzBR,SAAS;YACTC,cAAc;YACdE,cAAc;YACdE,gBAAgB;YAChBpC,YAAY;QACd;QACA,MAAMmE,gBAAqCL,OAAOC,IAAI,CACpDG;QAEFC,cAAcH,OAAO,CAAC,CAACR;YACrB,MAAMY,UAAU,OAAOnD,KAAK,CAACuC,IAAI;YAEjC,IAAIA,QAAQ,MAAM;gBAChB,IAAIvC,KAAK,CAACuC,IAAI,IAAIY,YAAY,YAAYA,YAAY,UAAU;oBAC9D,MAAMf,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQU;oBACV;gBACF;YACF,OAAO,IACLZ,QAAQ,aACRA,QAAQ,kBACRA,QAAQ,kBACRA,QAAQ,cACR;gBACA,IAAIvC,KAAK,CAACuC,IAAI,IAAIY,YAAY,YAAY;oBACxC,MAAMf,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQU;oBACV;gBACF;YACF,OAAO,IACLZ,QAAQ,aACRA,QAAQ,YACRA,QAAQ,aACRA,QAAQ,cACRA,QAAQ,cACRA,QAAQ,oBACRA,QAAQ,2BACR;gBACA,IAAIvC,KAAK,CAACuC,IAAI,IAAI,QAAQY,YAAY,WAAW;oBAC/C,MAAMf,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQU;oBACV;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMH,IAAWT;YACnB;QACF;IACF;IAEA,IAAIN,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,IAAInC,MAAMoD,MAAM,EAAE;YAChBC,CAAAA,GAAAA,UAAAA,QAAQ,EACN;QAEJ;QACA,IAAI,CAAC7C,QAAQ;YACX,IAAI9B;YACJ,IAAI,OAAO6B,aAAa,UAAU;gBAChC7B,OAAO6B;YACT,OAAO,IACL,OAAOA,aAAa,YACpB,OAAOA,SAAS+C,QAAQ,KAAK,UAC7B;gBACA5E,OAAO6B,SAAS+C,QAAQ;YAC1B;YAEA,IAAI5E,MAAM;gBACR,MAAM6E,oBAAoB7E,KACvB8E,KAAK,CAAC,KACNC,IAAI,CAAC,CAACC,UAAYA,QAAQC,UAAU,CAAC,QAAQD,QAAQE,QAAQ,CAAC;gBAEjE,IAAIL,mBAAmB;oBACrB,MAAM,OAAA,cAEL,CAFK,IAAIjB,MACP,mBAAiB5D,OAAK,6IADnB,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;QACF;IACF;IAEA,MAAM,EAAEA,IAAI,EAAEC,EAAE,EAAE,GAAGgB,OAAAA,OAAK,CAACkE,OAAO;iCAAC;YACjC,MAAMC,eAAejE,kBAAkBU;YACvC,OAAO;gBACL7B,MAAMoF;gBACNnF,IAAI6B,SAASX,kBAAkBW,UAAUsD;YAC3C;QACF;gCAAG;QAACvD;QAAUC;KAAO;IAErB,oFAAoF;IACpF,IAAIuD;IACJ,IAAI5C,gBAAgB;QAClB,IAAIc,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;YAC1C,IAAIrB,SAAS;gBACXkD,QAAQC,IAAI,CACT,oDAAoD1D,WAAS;YAElE;YACA,IAAIS,kBAAkB;gBACpBgD,QAAQC,IAAI,CACT,yDAAyD1D,WAAS;YAEvE;YACA,IAAI;gBACFwD,QAAQpE,OAAAA,OAAK,CAACuE,QAAQ,CAACC,IAAI,CAAC9D;YAC9B,EAAE,OAAO+D,KAAK;gBACZ,IAAI,CAAC/D,UAAU;oBACb,MAAM,OAAA,cAEL,CAFK,IAAIiC,MACP,uDAAuD/B,WAAS,kFAD7D,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBACA,MAAM,OAAA,cAKL,CALK,IAAI+B,MACP,6DAA6D/B,WAAS,8FACpE,CAAA,OAAOmC,WAAW,cACf,sEACA,EAAC,IAJH,qBAAA;2BAAA;gCAAA;kCAAA;gBAKN;YACF;QACF,OAAO;;QAEP;IACF,OAAO;QACL,IAAIT,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;YAC1C,IAAI,CAAC9B,YAAAA,OAAAA,KAAAA,IAAAA,SAAkBgE,IAAI,MAAK,KAAK;gBACnC,MAAM,OAAA,cAEL,CAFK,IAAI/B,MACR,oKADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;IACF;IAEA,MAAMgC,WAAgBnD,iBAClB4C,SAAS,OAAOA,UAAU,YAAYA,MAAM3C,GAAG,GAC/CC;IAEJ,4EAA4E;IAC5E,sEAAsE;IACtE,4EAA4E;IAC5E,6BAA6B;IAC7B,MAAMkD,+BAA+B5E,OAAAA,OAAK,CAAC6E,WAAW;mEACpD,CAACC;YACC,IAAIhD,WAAW,MAAM;gBACnB7C,gBAAgBc,OAAO,GAAGgF,CAAAA,GAAAA,OAAAA,iBAAiB,EACzCD,SACA/F,MACA+C,QACAI,iBACAD,iBACA1B;YAEJ;YAEA;2EAAO;oBACL,IAAItB,gBAAgBc,OAAO,EAAE;wBAC3BiF,CAAAA,GAAAA,OAAAA,+BAA+B,EAAC/F,gBAAgBc,OAAO;wBACvDd,gBAAgBc,OAAO,GAAG;oBAC5B;oBACAkF,CAAAA,GAAAA,OAAAA,2BAA2B,EAACH;gBAC9B;;QACF;kEACA;QAAC7C;QAAiBlD;QAAM+C;QAAQI;QAAiB3B;KAAwB;IAG3E,MAAM2E,YAAYC,CAAAA,GAAAA,cAAAA,YAAY,EAACP,8BAA8BD;IAE7D,MAAMS,aAMF;QACF3D,KAAKyD;QACL/D,SAAQrC,CAAC;YACP,IAAIwD,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;gBACzC,IAAI,CAAC1D,GAAG;oBACN,MAAM,OAAA,cAEL,CAFK,IAAI6D,MACP,mFADG,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;YAEA,IAAI,CAACnB,kBAAkB,OAAOL,YAAY,YAAY;gBACpDA,QAAQrC;YACV;YAEA,IACE0C,kBACA4C,MAAM/D,KAAK,IACX,OAAO+D,MAAM/D,KAAK,CAACc,OAAO,KAAK,YAC/B;gBACAiD,MAAM/D,KAAK,CAACc,OAAO,CAACrC;YACtB;YAEA,IAAI,CAACgD,QAAQ;gBACX;YACF;YAEA,IAAIhD,EAAEuG,gBAAgB,EAAE;gBACtB;YACF;YAEAxG,YAAYC,GAAGC,MAAMC,IAAIC,iBAAiBC,SAASC,QAAQC;QAC7D;QACAgC,cAAatC,CAAC;YACZ,IAAI,CAAC0C,kBAAkB,OAAOH,qBAAqB,YAAY;gBAC7DA,iBAAiBvC;YACnB;YAEA,IACE0C,kBACA4C,MAAM/D,KAAK,IACX,OAAO+D,MAAM/D,KAAK,CAACe,YAAY,KAAK,YACpC;gBACAgD,MAAM/D,KAAK,CAACe,YAAY,CAACtC;YAC3B;YAEA,IAAI,CAACgD,QAAQ;gBACX;YACF;YAEA,IAAI,CAACG,mBAAmBK,QAAQC,GAAG,CAACC,IAA4B,IAApB,KAAK;gBAC/C;YACF;;YAEA,MAAM8C,2BAA2B3D,4BAA4B;QAK/D;QACAL,cAAcgB,QAAQC,GAAG,CAACiD,0BAA0B,GAChDC,oCACA,SAASnE,aAAaxC,CAAC;YACrB,IAAI,CAAC0C,kBAAkB,OAAOD,qBAAqB,YAAY;gBAC7DA,iBAAiBzC;YACnB;YAEA,IACE0C,kBACA4C,MAAM/D,KAAK,IACX,OAAO+D,MAAM/D,KAAK,CAACiB,YAAY,KAAK,YACpC;gBACA8C,MAAM/D,KAAK,CAACiB,YAAY,CAACxC;YAC3B;YAEA,IAAI,CAACgD,QAAQ;gBACX;YACF;YAEA,IAAI,CAACG,iBAAiB;gBACpB;YACF;YAEA,MAAMqD,2BAA2B3D,4BAA4B;YAC7D4D,CAAAA,GAAAA,OAAAA,kBAAkB,EAChBzG,EAAEV,aAAa,EACfkH;QAEJ;IACN;IAEA,6FAA6F;IAC7F,wFAAwF;IACxF,2EAA2E;IAC3E,IAAII,CAAAA,GAAAA,OAAAA,aAAa,EAAC1G,KAAK;QACrBoG,WAAWrG,IAAI,GAAGC;IACpB,OAAO,IACL,CAACwC,kBACDP,YACCmD,MAAMM,IAAI,KAAK,OAAO,CAAE,CAAA,UAAUN,MAAM/D,KAAI,GAC7C;QACA+E,WAAWrG,IAAI,GAAG4G,CAAAA,GAAAA,aAAAA,WAAW,EAAC3G;IAChC;IAEA,IAAI4G;IAEJ,IAAIpE,gBAAgB;QAClB,IAAIc,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;YAC1CqD,CAAAA,GAAAA,WAAAA,SAAS,EACP,oEACE,oEACA,4CACA;QAEN;QACAD,OAAAA,WAAAA,GAAO5F,OAAAA,OAAK,CAAC8F,YAAY,CAAC1B,OAAOgB;IACnC,OAAO;QACLQ,OAAAA,WAAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAAC/D,KAAAA;YAAG,GAAGD,SAAS;YAAG,GAAGwD,UAAU;sBAC7B1E;;IAGP;IAEA,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACqF,kBAAkBC,QAAQ,EAAA;QAACC,OAAO3F;kBAChCsF;;AAGP;AAEA,MAAMG,oBAAAA,WAAAA,GAAoBG,CAAAA,GAAAA,OAAAA,aAAa,EAErCzF,OAAAA,gBAAgB;AAEX,MAAMzC,gBAAgB;IAC3B,OAAO+D,CAAAA,GAAAA,OAAAA,UAAU,EAACgE;AACpB", "ignoreList": [0], "debugId": null}}]}