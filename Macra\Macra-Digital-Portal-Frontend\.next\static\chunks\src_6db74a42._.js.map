{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/services/task-assignment.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\r\n\r\n// Generic Task interface for different types of tasks\r\nexport interface GenericTask {\r\n  task_id: string;\r\n  task_type: 'application' | 'complaint' | 'data_breach' | 'evaluation' | 'inspection';\r\n  task_number: string;\r\n  title: string;\r\n  description: string;\r\n  status: string;\r\n  priority?: 'low' | 'medium' | 'high' | 'urgent';\r\n  created_at: string;\r\n  updated_at: string;\r\n  assigned_to?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n  assigned_by?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n  assigned_at?: string;\r\n  due_date?: string;\r\n  metadata?: {\r\n    [key: string]: unknown;\r\n  };\r\n}\r\n\r\n// Legacy interface for backward compatibility\r\nexport interface TaskAssignmentApplication {\r\n  application_id: string;\r\n  application_number: string;\r\n  status: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  applicant: {\r\n    applicant_id: string;\r\n    company_name: string;\r\n    first_name: string;\r\n    last_name: string;\r\n  };\r\n  license_category: {\r\n    license_category_id: string;\r\n    name: string;\r\n    license_type: {\r\n      license_type_id: string;\r\n      name: string;\r\n    };\r\n  };\r\n  assignee?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n  assigned_at?: string;\r\n}\r\n\r\nexport interface TaskAssignmentOfficer {\r\n  user_id: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  email: string;\r\n  department?: string;\r\n}\r\n\r\nexport interface AssignApplicationRequest {\r\n  assignedTo: string;\r\n}\r\n\r\nexport interface PaginatedResponse<T> {\r\n  data: T[];\r\n  meta: {\r\n    itemCount: number;\r\n    totalItems: number;\r\n    itemsPerPage: number;\r\n    totalPages: number;\r\n    currentPage: number;\r\n  };\r\n  links: {\r\n    first: string;\r\n    previous: string;\r\n    next: string;\r\n    last: string;\r\n  };\r\n}\r\n\r\nexport const taskAssignmentService = {\r\n  // Generic task management methods\r\n  getUnassignedTasks: async (params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    task_type?: string;\r\n  }) => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n    if (params?.task_type) searchParams.append('task_type', params.task_type);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/tasks/unassigned${queryString ? `?${queryString}` : ''}`;\r\n\r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  getAssignedTasks: async (params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    task_type?: string;\r\n  }) => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n    if (params?.task_type) searchParams.append('task_type', params.task_type);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/tasks/assigned${queryString ? `?${queryString}` : ''}`;\r\n\r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  assignTask: async (taskId: string, assignData: { assignedTo: string; comment?: string }) => {\r\n    const response = await apiClient.put(`/tasks/${taskId}/assign`, assignData);\r\n    return response.data;\r\n  },\r\n\r\n  getTaskById: async (taskId: string) => {\r\n    const response = await apiClient.get(`/tasks/${taskId}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Legacy application-specific methods (for backward compatibility)\r\n  getUnassignedApplications: async (params?: { page?: number; limit?: number; search?: string }) => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/applications/unassigned${queryString ? `?${queryString}` : ''}`;\r\n    \r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  // Get all applications (including assigned)\r\n  getAllApplications: async (params?: { page?: number; limit?: number; search?: string }) => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/applications${queryString ? `?${queryString}` : ''}`;\r\n    \r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  // Get applications assigned to current user\r\n  getMyAssignedApplications: async (params?: { page?: number; limit?: number; search?: string }) => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/applications/assigned/me${queryString ? `?${queryString}` : ''}`;\r\n    \r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  // Get officers for assignment\r\n  getOfficers: async () => {\r\n    try {\r\n      const response = await apiClient.get('/users');\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error fetching officers:', error);\r\n      return { data: [] };\r\n    }\r\n  },\r\n\r\n  // Assign application to officer\r\n  assignApplication: async (applicationId: string, assignData: AssignApplicationRequest) => {\r\n    const response = await apiClient.put(`/applications/${applicationId}/assign`, assignData);\r\n    return response.data;\r\n  },\r\n\r\n  // Get application details\r\n  getApplication: async (applicationId: string) => {\r\n    const response = await apiClient.get(`/applications/${applicationId}`);\r\n    return response.data;\r\n  },\r\n};"], "names": [], "mappings": ";;;AAAA;;AA2FO,MAAM,wBAAwB;IACnC,kCAAkC;IAClC,oBAAoB,OAAO;QAMzB,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAC/D,IAAI,QAAQ,WAAW,aAAa,MAAM,CAAC,aAAa,OAAO,SAAS;QAExE,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAEtE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB,OAAO;QAMvB,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAC/D,IAAI,QAAQ,WAAW,aAAa,MAAM,CAAC,aAAa,OAAO,SAAS;QAExE,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAEpE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,OAAO,OAAO,CAAC,EAAE;QAChE,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ;QACvD,OAAO,SAAS,IAAI;IACtB;IAEA,mEAAmE;IACnE,2BAA2B,OAAO;QAChC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAE/D,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,wBAAwB,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAE7E,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,4CAA4C;IAC5C,oBAAoB,OAAO;QACzB,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAE/D,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAElE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,4CAA4C;IAC5C,2BAA2B,OAAO;QAChC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAE/D,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,yBAAyB,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAE9E,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,8BAA8B;IAC9B,aAAa;QACX,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;gBAAE,MAAM,EAAE;YAAC;QACpB;IACF;IAEA,gCAAgC;IAChC,mBAAmB,OAAO,eAAuB;QAC/C,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,cAAc,OAAO,CAAC,EAAE;QAC9E,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe;QACrE,OAAO,SAAS,IAAI;IACtB;AACF", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/app/task-assignment/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { useAuth } from '../../contexts/AuthContext';\r\nimport { useRouter } from 'next/navigation';\r\nimport Loader from '../../components/Loader';\r\nimport { taskAssignmentService, GenericTask } from '../../services/task-assignment';\r\nimport { useToast } from '../../contexts/ToastContext';\r\n\r\ninterface User {\r\n  user_id: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  email: string;\r\n  department?: string;\r\n}\r\n\r\nexport default function TaskAssignmentPage() {\r\n  const { isAuthenticated } = useAuth();\r\n  const { showSuccess, showError } = useToast();\r\n  const router = useRouter();\r\n  const [tasks, setTasks] = useState<GenericTask[]>([]);\r\n  const [officers, setOfficers] = useState<User[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState('');\r\n  const [selectedTask, setSelectedTask] = useState<string | null>(null);\r\n  const [selectedOfficer, setSelectedOfficer] = useState<string>('');\r\n  const [isAssigning, setIsAssigning] = useState(false);\r\n  const [activeTab, setActiveTab] = useState<'unassigned' | 'assigned'>('unassigned');\r\n  const [taskTypeFilter, setTaskTypeFilter] = useState<string>('');\r\n  const [searchQuery, setSearchQuery] = useState<string>('');\r\n\r\n  const fetchTasks = useCallback(async () => {\r\n    try {\r\n      setLoading(true);\r\n      setError('');\r\n\r\n      const params = {\r\n        limit: 50,\r\n        search: searchQuery || undefined,\r\n        task_type: taskTypeFilter || undefined\r\n      };\r\n\r\n      const response = activeTab === 'unassigned'\r\n        ? await taskAssignmentService.getUnassignedTasks(params)\r\n        : await taskAssignmentService.getAssignedTasks(params);\r\n\r\n      setTasks(response.data || []);\r\n    } catch (err) {\r\n      console.error('Error fetching tasks:', err);\r\n      setError('Failed to load tasks');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [activeTab, searchQuery, taskTypeFilter]);\r\n\r\n  useEffect(() => {\r\n    if (!isAuthenticated) {\r\n      router.push('/auth/login');\r\n      return;\r\n    }\r\n\r\n    fetchTasks();\r\n    fetchOfficers();\r\n  }, [isAuthenticated, router, fetchTasks]);\r\n\r\n  const fetchOfficers = async () => {\r\n    try {\r\n      const response = await taskAssignmentService.getOfficers();\r\n      setOfficers(response.data || []);\r\n    } catch (err) {\r\n      console.error('Error fetching officers:', err);\r\n    }\r\n  };\r\n\r\n  const handleAssignTask = async (taskId: string, officerId: string) => {\r\n    try {\r\n      setIsAssigning(true);\r\n\r\n      await taskAssignmentService.assignTask(taskId, { assignedTo: officerId });\r\n\r\n      // Refresh tasks list\r\n      await fetchTasks();\r\n      setSelectedTask(null);\r\n      setSelectedOfficer('');\r\n\r\n      // Show success message\r\n      showSuccess('Task assigned successfully!');\r\n      setError('');\r\n    } catch (err) {\r\n      console.error('Error assigning task:', err);\r\n      const errorMessage = err instanceof Error ? err.message : 'Unknown error';\r\n      showError(`Failed to assign task: ${errorMessage}`);\r\n      setError('Failed to assign task');\r\n    } finally {\r\n      setIsAssigning(false);\r\n    }\r\n  };\r\n\r\n  const getStatusColor = (status: string) => {\r\n    switch (status.toLowerCase()) {\r\n      case 'draft': return 'bg-gray-100 text-gray-800';\r\n      case 'pending': return 'bg-yellow-100 text-yellow-800';\r\n      case 'submitted': return 'bg-blue-100 text-blue-800';\r\n      case 'under_review': return 'bg-orange-100 text-orange-800';\r\n      case 'evaluation': return 'bg-purple-100 text-purple-800';\r\n      case 'approved': return 'bg-green-100 text-green-800';\r\n      case 'rejected': return 'bg-red-100 text-red-800';\r\n      default: return 'bg-gray-100 text-gray-800';\r\n    }\r\n  };\r\n\r\n  const formatDate = (dateString: string) => {\r\n    return new Date(dateString).toLocaleDateString('en-US', {\r\n      year: 'numeric',\r\n      month: 'short',\r\n      day: 'numeric',\r\n      hour: '2-digit',\r\n      minute: '2-digit'\r\n    });\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex items-center justify-center min-h-96\">\r\n        <Loader message=\"Loading Task Assignment...\" />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const getTaskTypeIcon = (taskType: string) => {\r\n    switch (taskType) {\r\n      case 'application': return 'ri-file-text-line';\r\n      case 'complaint': return 'ri-feedback-line';\r\n      case 'data_breach': return 'ri-shield-line';\r\n      case 'evaluation': return 'ri-survey-line';\r\n      case 'inspection': return 'ri-search-line';\r\n      default: return 'ri-task-line';\r\n    }\r\n  };\r\n\r\n  const getTaskTypeColor = (taskType: string) => {\r\n    switch (taskType) {\r\n      case 'application': return 'bg-blue-100 text-blue-800';\r\n      case 'complaint': return 'bg-yellow-100 text-yellow-800';\r\n      case 'data_breach': return 'bg-red-100 text-red-800';\r\n      case 'evaluation': return 'bg-green-100 text-green-800';\r\n      case 'inspection': return 'bg-purple-100 text-purple-800';\r\n      default: return 'bg-gray-100 text-gray-800';\r\n    }\r\n  };\r\n\r\n  const getPriorityColor = (priority: string) => {\r\n    switch (priority) {\r\n      case 'urgent': return 'bg-red-100 text-red-800';\r\n      case 'high': return 'bg-orange-100 text-orange-800';\r\n      case 'medium': return 'bg-yellow-100 text-yellow-800';\r\n      case 'low': return 'bg-green-100 text-green-800';\r\n      default: return 'bg-gray-100 text-gray-800';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"p-6\">\r\n      {/* Header */}\r\n      <div className=\"mb-6\">\r\n        <h1 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2\">\r\n          Task Assignment\r\n        </h1>\r\n        <p className=\"text-gray-600 dark:text-gray-400\">\r\n          Assign various types of tasks to officers for processing and evaluation\r\n        </p>\r\n      </div>\r\n\r\n        {/* Error Message */}\r\n        {error && (\r\n          <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6\">\r\n            <p>{error}</p>\r\n          </div>\r\n        )}\r\n\r\n        {/* Filters */}\r\n        <div className=\"bg-white dark:bg-gray-800 shadow rounded-lg p-4 mb-6\">\r\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n            <div>\r\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                Search Tasks\r\n              </label>\r\n              <input\r\n                type=\"text\"\r\n                value={searchQuery}\r\n                onChange={(e) => setSearchQuery(e.target.value)}\r\n                placeholder=\"Search by task ID, title, or description...\"\r\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\r\n              />\r\n            </div>\r\n            <div>\r\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                Task Type\r\n              </label>\r\n              <select\r\n                value={taskTypeFilter}\r\n                onChange={(e) => setTaskTypeFilter(e.target.value)}\r\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\r\n              >\r\n                <option value=\"\">All Types</option>\r\n                <option value=\"application\">Applications</option>\r\n                <option value=\"complaint\">Complaints</option>\r\n                <option value=\"data_breach\">Data Breach Reports</option>\r\n                <option value=\"evaluation\">Evaluations</option>\r\n                <option value=\"inspection\">Inspections</option>\r\n              </select>\r\n            </div>\r\n            <div className=\"flex items-end\">\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => {\r\n                  setSearchQuery('');\r\n                  setTaskTypeFilter('');\r\n                }}\r\n                className=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600\"\r\n              >\r\n                Clear Filters\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Tabs */}\r\n        <div className=\"border-b border-gray-200 dark:border-gray-700 mb-6\">\r\n          <nav className=\"-mb-px flex space-x-8\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => setActiveTab('unassigned')}\r\n              className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center ${\r\n                activeTab === 'unassigned'\r\n                  ? 'border-primary text-primary'\r\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\r\n              }`}\r\n            >\r\n              <i className=\"ri-task-line mr-2\"></i>\r\n              Unassigned Tasks\r\n              <span className=\"ml-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 py-0.5 px-2 rounded-full text-xs\">\r\n                {tasks.length}\r\n              </span>\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => setActiveTab('assigned')}\r\n              className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center ${\r\n                activeTab === 'assigned'\r\n                  ? 'border-primary text-primary'\r\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\r\n              }`}\r\n            >\r\n              <i className=\"ri-user-check-line mr-2\"></i>\r\n              Assigned Tasks\r\n              <span className=\"ml-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 py-0.5 px-2 rounded-full text-xs\">\r\n                {tasks.length}\r\n              </span>\r\n            </button>\r\n          </nav>\r\n        </div>\r\n\r\n        {/* Tasks Table */}\r\n        <div className=\"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md\">\r\n          <div className=\"overflow-x-auto\">\r\n            <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\r\n              <thead className=\"bg-gray-50 dark:bg-gray-700\">\r\n                <tr>\r\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                    Task ID\r\n                  </th>\r\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                    Description\r\n                  </th>\r\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                    Type\r\n                  </th>\r\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                    Priority\r\n                  </th>\r\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                    {activeTab === 'assigned' ? 'Assigned To' : 'Created'}\r\n                  </th>\r\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                    {activeTab === 'assigned' ? 'Assigned By' : 'Status'}\r\n                  </th>\r\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                    Review\r\n                  </th>\r\n                </tr>\r\n              </thead>\r\n              <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\r\n                {tasks.length === 0 ? (\r\n                  <tr>\r\n                    <td colSpan={7} className=\"px-6 py-12 text-center text-gray-500 dark:text-gray-400\">\r\n                      <div className=\"flex flex-col items-center\">\r\n                        <i className=\"ri-task-line text-4xl mb-2\"></i>\r\n                        <p>No {activeTab} tasks found</p>\r\n                        {(searchQuery || taskTypeFilter) && (\r\n                          <p className=\"text-sm mt-1\">Try adjusting your filters</p>\r\n                        )}\r\n                      </div>\r\n                    </td>\r\n                  </tr>\r\n                ) : (\r\n                  tasks.map((task) => (\r\n                    <tr key={task.task_id} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\r\n                      {/* Task ID */}\r\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                        <div className=\"flex items-center\">\r\n                          <div className=\"h-10 w-10 flex-shrink-0\">\r\n                            <div className=\"h-10 w-10 rounded-full bg-primary flex items-center justify-center\">\r\n                              <i className={`${getTaskTypeIcon(task.task_type)} text-white`}></i>\r\n                            </div>\r\n                          </div>\r\n                          <div className=\"ml-4\">\r\n                            <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                              {task.task_number}\r\n                            </div>\r\n                            <div className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                              ID: {task.task_id.slice(0, 8)}...\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </td>\r\n\r\n                      {/* Description */}\r\n                      <td className=\"px-6 py-4\">\r\n                        <div className=\"text-sm text-gray-900 dark:text-gray-100 font-medium\">\r\n                          {task.title}\r\n                        </div>\r\n                        <div className=\"text-sm text-gray-500 dark:text-gray-400 mt-1\">\r\n                          {task.description.length > 100\r\n                            ? `${task.description.substring(0, 100)}...`\r\n                            : task.description}\r\n                        </div>\r\n                      </td>\r\n\r\n                      {/* Type */}\r\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getTaskTypeColor(task.task_type)}`}>\r\n                          {task.task_type.replace('_', ' ').toUpperCase()}\r\n                        </span>\r\n                      </td>\r\n\r\n                      {/* Priority */}\r\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                        {task.priority && (\r\n                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(task.priority)}`}>\r\n                            {task.priority.toUpperCase()}\r\n                          </span>\r\n                        )}\r\n                      </td>\r\n\r\n                      {/* Assigned To / Created */}\r\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\r\n                        {activeTab === 'assigned' && task.assigned_to ? (\r\n                          <div>\r\n                            <div className=\"text-gray-900 dark:text-gray-100\">\r\n                              {task.assigned_to.first_name} {task.assigned_to.last_name}\r\n                            </div>\r\n                            <div className=\"text-xs\">\r\n                              {task.assigned_to.email}\r\n                            </div>\r\n                          </div>\r\n                        ) : (\r\n                          <div>\r\n                            <div className=\"text-gray-900 dark:text-gray-100\">\r\n                              {new Date(task.created_at).toLocaleDateString()}\r\n                            </div>\r\n                            <div className=\"text-xs\">\r\n                              {new Date(task.created_at).toLocaleTimeString()}\r\n                            </div>\r\n                          </div>\r\n                        )}\r\n                      </td>\r\n\r\n                      {/* Assigned By / Status */}\r\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                        {activeTab === 'assigned' && task.assigned_by ? (\r\n                          <div className=\"text-sm\">\r\n                            <div className=\"text-gray-900 dark:text-gray-100\">\r\n                              {task.assigned_by.first_name} {task.assigned_by.last_name}\r\n                            </div>\r\n                            <div className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                              {task.assigned_at ? new Date(task.assigned_at).toLocaleDateString() : ''}\r\n                            </div>\r\n                          </div>\r\n                        ) : (\r\n                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\r\n                            task.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :\r\n                            task.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :\r\n                            task.status === 'completed' ? 'bg-green-100 text-green-800' :\r\n                            task.status === 'cancelled' ? 'bg-red-100 text-red-800' :\r\n                            'bg-gray-100 text-gray-800'\r\n                          }`}>\r\n                            {task.status.replace('_', ' ').toUpperCase()}\r\n                          </span>\r\n                        )}\r\n                      </td>\r\n\r\n                      {/* Review (Actions) */}\r\n                      <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\r\n                        <div className=\"flex items-center justify-end space-x-2\">\r\n                          <button\r\n                            className=\"text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 p-1 rounded hover:bg-indigo-50 dark:hover:bg-indigo-900\"\r\n                            title=\"View Task Details\"\r\n                          >\r\n                            <i className=\"ri-eye-line\"></i>\r\n                          </button>\r\n                          {activeTab === 'unassigned' && (\r\n                            <button\r\n                              onClick={() => setSelectedTask(task.task_id)}\r\n                              className=\"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 p-1 rounded hover:bg-green-50 dark:hover:bg-green-900\"\r\n                              title=\"Assign Task\"\r\n                            >\r\n                              <i className=\"ri-user-add-line\"></i>\r\n                            </button>\r\n                          )}\r\n                          {activeTab === 'assigned' && (\r\n                            <button\r\n                              onClick={() => setSelectedTask(task.task_id)}\r\n                              className=\"text-orange-600 hover:text-orange-900 dark:text-orange-400 dark:hover:text-orange-300 p-1 rounded hover:bg-orange-50 dark:hover:bg-orange-900\"\r\n                              title=\"Reassign Task\"\r\n                            >\r\n                              <i className=\"ri-user-settings-line\"></i>\r\n                            </button>\r\n                          )}\r\n                        </div>\r\n                      </td>\r\n                    </tr>\r\n                  ))\r\n                )}\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Assignment Modal */}\r\n        {selectedTask && (\r\n          <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\r\n            <div className=\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800\">\r\n              <div className=\"mt-3\">\r\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4\">\r\n                  Assign Task to Officer\r\n                </h3>\r\n                \r\n                <div className=\"mb-4\">\r\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                    Select Officer\r\n                  </label>\r\n                  <select\r\n                    value={selectedOfficer}\r\n                    onChange={(e) => setSelectedOfficer(e.target.value)}\r\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\r\n                  >\r\n                    <option value=\"\">Select an officer...</option>\r\n                    {officers.map((officer) => (\r\n                      <option key={officer.user_id} value={officer.user_id}>\r\n                        {officer.first_name} {officer.last_name} - {officer.email}\r\n                      </option>\r\n                    ))}\r\n                  </select>\r\n                </div>\r\n\r\n                <div className=\"flex justify-end space-x-3\">\r\n                  <button\r\n                    type=\"button\"\r\n                    onClick={() => {\r\n                      setSelectedTask(null);\r\n                      setSelectedOfficer('');\r\n                    }}\r\n                    className=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n                  >\r\n                    Cancel\r\n                  </button>\r\n                  <button\r\n                    type=\"button\"\r\n                    onClick={() => handleAssignTask(selectedTask, selectedOfficer)}\r\n                    disabled={!selectedOfficer || isAssigning}\r\n                    className=\"px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                  >\r\n                    {isAssigning ? 'Assigning...' : 'Assign'}\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AAiBe,SAAS;;IACtB,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAClC,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAC1C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IACpD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC/D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;IACtE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEvD,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YAC7B,IAAI;gBACF,WAAW;gBACX,SAAS;gBAET,MAAM,SAAS;oBACb,OAAO;oBACP,QAAQ,eAAe;oBACvB,WAAW,kBAAkB;gBAC/B;gBAEA,MAAM,WAAW,cAAc,eAC3B,MAAM,wIAAA,CAAA,wBAAqB,CAAC,kBAAkB,CAAC,UAC/C,MAAM,wIAAA,CAAA,wBAAqB,CAAC,gBAAgB,CAAC;gBAEjD,SAAS,SAAS,IAAI,IAAI,EAAE;YAC9B,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,yBAAyB;gBACvC,SAAS;YACX,SAAU;gBACR,WAAW;YACb;QACF;qDAAG;QAAC;QAAW;QAAa;KAAe;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,CAAC,iBAAiB;gBACpB,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA;YACA;QACF;uCAAG;QAAC;QAAiB;QAAQ;KAAW;IAExC,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,wIAAA,CAAA,wBAAqB,CAAC,WAAW;YACxD,YAAY,SAAS,IAAI,IAAI,EAAE;QACjC,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,MAAM,mBAAmB,OAAO,QAAgB;QAC9C,IAAI;YACF,eAAe;YAEf,MAAM,wIAAA,CAAA,wBAAqB,CAAC,UAAU,CAAC,QAAQ;gBAAE,YAAY;YAAU;YAEvE,qBAAqB;YACrB,MAAM;YACN,gBAAgB;YAChB,mBAAmB;YAEnB,uBAAuB;YACvB,YAAY;YACZ,SAAS;QACX,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,UAAU,CAAC,uBAAuB,EAAE,cAAc;YAClD,SAAS;QACX,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ,OAAO,WAAW;YACxB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,+HAAA,CAAA,UAAM;gBAAC,SAAQ;;;;;;;;;;;IAGtB;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAc,OAAO;YAC1B;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAc,OAAO;YAC1B;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2D;;;;;;kCAGzE,6LAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;YAM/C,uBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;8BAAG;;;;;;;;;;;0BAKR,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,6LAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,aAAY;oCACZ,WAAU;;;;;;;;;;;;sCAGd,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,6LAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;oCACjD,WAAU;;sDAEV,6LAAC;4CAAO,OAAM;sDAAG;;;;;;sDACjB,6LAAC;4CAAO,OAAM;sDAAc;;;;;;sDAC5B,6LAAC;4CAAO,OAAM;sDAAY;;;;;;sDAC1B,6LAAC;4CAAO,OAAM;sDAAc;;;;;;sDAC5B,6LAAC;4CAAO,OAAM;sDAAa;;;;;;sDAC3B,6LAAC;4CAAO,OAAM;sDAAa;;;;;;;;;;;;;;;;;;sCAG/B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,MAAK;gCACL,SAAS;oCACP,eAAe;oCACf,kBAAkB;gCACpB;gCACA,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,MAAK;4BACL,SAAS,IAAM,aAAa;4BAC5B,WAAW,CAAC,6EAA6E,EACvF,cAAc,eACV,gCACA,0HACJ;;8CAEF,6LAAC;oCAAE,WAAU;;;;;;gCAAwB;8CAErC,6LAAC;oCAAK,WAAU;8CACb,MAAM,MAAM;;;;;;;;;;;;sCAGjB,6LAAC;4BACC,MAAK;4BACL,SAAS,IAAM,aAAa;4BAC5B,WAAW,CAAC,6EAA6E,EACvF,cAAc,aACV,gCACA,0HACJ;;8CAEF,6LAAC;oCAAE,WAAU;;;;;;gCAA8B;8CAE3C,6LAAC;oCAAK,WAAU;8CACb,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;0BAOrB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAM,WAAU;;0CACf,6LAAC;gCAAM,WAAU;0CACf,cAAA,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAoG;;;;;;sDAGlH,6LAAC;4CAAG,WAAU;sDAAoG;;;;;;sDAGlH,6LAAC;4CAAG,WAAU;sDAAoG;;;;;;sDAGlH,6LAAC;4CAAG,WAAU;sDAAoG;;;;;;sDAGlH,6LAAC;4CAAG,WAAU;sDACX,cAAc,aAAa,gBAAgB;;;;;;sDAE9C,6LAAC;4CAAG,WAAU;sDACX,cAAc,aAAa,gBAAgB;;;;;;sDAE9C,6LAAC;4CAAG,WAAU;sDAAoG;;;;;;;;;;;;;;;;;0CAKtH,6LAAC;gCAAM,WAAU;0CACd,MAAM,MAAM,KAAK,kBAChB,6LAAC;8CACC,cAAA,6LAAC;wCAAG,SAAS;wCAAG,WAAU;kDACxB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;;;;;;8DACb,6LAAC;;wDAAE;wDAAI;wDAAU;;;;;;;gDAChB,CAAC,eAAe,cAAc,mBAC7B,6LAAC;oDAAE,WAAU;8DAAe;;;;;;;;;;;;;;;;;;;;;2CAMpC,MAAM,GAAG,CAAC,CAAC,qBACT,6LAAC;wCAAsB,WAAU;;0DAE/B,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAE,WAAW,GAAG,gBAAgB,KAAK,SAAS,EAAE,WAAW,CAAC;;;;;;;;;;;;;;;;sEAGjE,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACZ,KAAK,WAAW;;;;;;8EAEnB,6LAAC;oEAAI,WAAU;;wEAA2C;wEACnD,KAAK,OAAO,CAAC,KAAK,CAAC,GAAG;wEAAG;;;;;;;;;;;;;;;;;;;;;;;;0DAOtC,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;wDAAI,WAAU;kEACZ,KAAK,KAAK;;;;;;kEAEb,6LAAC;wDAAI,WAAU;kEACZ,KAAK,WAAW,CAAC,MAAM,GAAG,MACvB,GAAG,KAAK,WAAW,CAAC,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC,GAC1C,KAAK,WAAW;;;;;;;;;;;;0DAKxB,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;oDAAK,WAAW,CAAC,yDAAyD,EAAE,iBAAiB,KAAK,SAAS,GAAG;8DAC5G,KAAK,SAAS,CAAC,OAAO,CAAC,KAAK,KAAK,WAAW;;;;;;;;;;;0DAKjD,6LAAC;gDAAG,WAAU;0DACX,KAAK,QAAQ,kBACZ,6LAAC;oDAAK,WAAW,CAAC,yDAAyD,EAAE,iBAAiB,KAAK,QAAQ,GAAG;8DAC3G,KAAK,QAAQ,CAAC,WAAW;;;;;;;;;;;0DAMhC,6LAAC;gDAAG,WAAU;0DACX,cAAc,cAAc,KAAK,WAAW,iBAC3C,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;;gEACZ,KAAK,WAAW,CAAC,UAAU;gEAAC;gEAAE,KAAK,WAAW,CAAC,SAAS;;;;;;;sEAE3D,6LAAC;4DAAI,WAAU;sEACZ,KAAK,WAAW,CAAC,KAAK;;;;;;;;;;;yEAI3B,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEACZ,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB;;;;;;sEAE/C,6LAAC;4DAAI,WAAU;sEACZ,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB;;;;;;;;;;;;;;;;;0DAOrD,6LAAC;gDAAG,WAAU;0DACX,cAAc,cAAc,KAAK,WAAW,iBAC3C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;gEACZ,KAAK,WAAW,CAAC,UAAU;gEAAC;gEAAE,KAAK,WAAW,CAAC,SAAS;;;;;;;sEAE3D,6LAAC;4DAAI,WAAU;sEACZ,KAAK,WAAW,GAAG,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB,KAAK;;;;;;;;;;;yEAI1E,6LAAC;oDAAK,WAAW,CAAC,yDAAyD,EACzE,KAAK,MAAM,KAAK,YAAY,kCAC5B,KAAK,MAAM,KAAK,gBAAgB,8BAChC,KAAK,MAAM,KAAK,cAAc,gCAC9B,KAAK,MAAM,KAAK,cAAc,4BAC9B,6BACA;8DACC,KAAK,MAAM,CAAC,OAAO,CAAC,KAAK,KAAK,WAAW;;;;;;;;;;;0DAMhD,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,WAAU;4DACV,OAAM;sEAEN,cAAA,6LAAC;gEAAE,WAAU;;;;;;;;;;;wDAEd,cAAc,8BACb,6LAAC;4DACC,SAAS,IAAM,gBAAgB,KAAK,OAAO;4DAC3C,WAAU;4DACV,OAAM;sEAEN,cAAA,6LAAC;gEAAE,WAAU;;;;;;;;;;;wDAGhB,cAAc,4BACb,6LAAC;4DACC,SAAS,IAAM,gBAAgB,KAAK,OAAO;4DAC3C,WAAU;4DACV,OAAM;sEAEN,cAAA,6LAAC;gEAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;uCAvHd,KAAK,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;YAqIhC,8BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA4D;;;;;;0CAI1E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;wCAClD,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAG;;;;;;4CAChB,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;oDAA6B,OAAO,QAAQ,OAAO;;wDACjD,QAAQ,UAAU;wDAAC;wDAAE,QAAQ,SAAS;wDAAC;wDAAI,QAAQ,KAAK;;mDAD9C,QAAQ,OAAO;;;;;;;;;;;;;;;;;0CAOlC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,SAAS;4CACP,gBAAgB;4CAChB,mBAAmB;wCACrB;wCACA,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,MAAK;wCACL,SAAS,IAAM,iBAAiB,cAAc;wCAC9C,UAAU,CAAC,mBAAmB;wCAC9B,WAAU;kDAET,cAAc,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpD;GA5dwB;;QACM,kIAAA,CAAA,UAAO;QACA,mIAAA,CAAA,WAAQ;QAC5B,qIAAA,CAAA,YAAS;;;KAHF", "debugId": null}}]}