{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/services/task-assignment.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\r\n\r\nexport interface TaskAssignmentApplication {\r\n  application_id: string;\r\n  application_number: string;\r\n  status: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  applicant: {\r\n    applicant_id: string;\r\n    company_name: string;\r\n    first_name: string;\r\n    last_name: string;\r\n  };\r\n  license_category: {\r\n    license_category_id: string;\r\n    name: string;\r\n    license_type: {\r\n      license_type_id: string;\r\n      name: string;\r\n    };\r\n  };\r\n  assignee?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n  assigned_at?: string;\r\n}\r\n\r\nexport interface TaskAssignmentOfficer {\r\n  user_id: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  email: string;\r\n  department?: string;\r\n}\r\n\r\nexport interface AssignApplicationRequest {\r\n  assignedTo: string;\r\n}\r\n\r\nexport interface PaginatedResponse<T> {\r\n  data: T[];\r\n  meta: {\r\n    itemCount: number;\r\n    totalItems: number;\r\n    itemsPerPage: number;\r\n    totalPages: number;\r\n    currentPage: number;\r\n  };\r\n  links: {\r\n    first: string;\r\n    previous: string;\r\n    next: string;\r\n    last: string;\r\n  };\r\n}\r\n\r\nexport const taskAssignmentService = {\r\n  // Get unassigned applications\r\n  getUnassignedApplications: async (params?: { page?: number; limit?: number; search?: string }) => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/applications/unassigned${queryString ? `?${queryString}` : ''}`;\r\n    \r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  // Get all applications (including assigned)\r\n  getAllApplications: async (params?: { page?: number; limit?: number; search?: string }) => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/applications${queryString ? `?${queryString}` : ''}`;\r\n    \r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  // Get applications assigned to current user\r\n  getMyAssignedApplications: async (params?: { page?: number; limit?: number; search?: string }) => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/applications/assigned/me${queryString ? `?${queryString}` : ''}`;\r\n    \r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  // Get officers for assignment\r\n  getOfficers: async () => {\r\n    // Note: This might need to be adjusted based on actual user endpoint\r\n    const response = await apiClient.get('/users?role=officer');\r\n    return response.data;\r\n  },\r\n\r\n  // Assign application to officer\r\n  assignApplication: async (applicationId: string, assignData: AssignApplicationRequest) => {\r\n    const response = await apiClient.put(`/applications/${applicationId}/assign`, assignData);\r\n    return response.data;\r\n  },\r\n\r\n  // Get application details\r\n  getApplication: async (applicationId: string) => {\r\n    const response = await apiClient.get(`/applications/${applicationId}`);\r\n    return response.data;\r\n  },\r\n};"], "names": [], "mappings": ";;;AAAA;;AA4DO,MAAM,wBAAwB;IACnC,8BAA8B;IAC9B,2BAA2B,OAAO;QAChC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAE/D,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,wBAAwB,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAE7E,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,4CAA4C;IAC5C,oBAAoB,OAAO;QACzB,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAE/D,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAElE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,4CAA4C;IAC5C,2BAA2B,OAAO;QAChC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAE/D,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,yBAAyB,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAE9E,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,8BAA8B;IAC9B,aAAa;QACX,qEAAqE;QACrE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,gCAAgC;IAChC,mBAAmB,OAAO,eAAuB;QAC/C,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,cAAc,OAAO,CAAC,EAAE;QAC9E,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe;QACrE,OAAO,SAAS,IAAI;IACtB;AACF", "debugId": null}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/app/task-assignment/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { useAuth } from '../../contexts/AuthContext';\r\nimport { useRouter } from 'next/navigation';\r\nimport Loader from '../../components/Loader';\r\nimport { taskAssignmentService } from '../../services/task-assignment';\r\n\r\ninterface Application {\r\n  application_id: string;\r\n  application_number: string;\r\n  status: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  applicant: {\r\n    applicant_id: string;\r\n    company_name: string;\r\n    first_name: string;\r\n    last_name: string;\r\n  };\r\n  license_category: {\r\n    license_category_id: string;\r\n    name: string;\r\n    license_type: {\r\n      license_type_id: string;\r\n      name: string;\r\n    };\r\n  };\r\n  assignee?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n  assigned_at?: string;\r\n}\r\n\r\ninterface User {\r\n  user_id: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  email: string;\r\n  department?: string;\r\n}\r\n\r\nexport default function TaskAssignmentPage() {\r\n  const { user, isAuthenticated } = useAuth();\r\n  const router = useRouter();\r\n  const [applications, setApplications] = useState<Application[]>([]);\r\n  const [officers, setOfficers] = useState<User[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState('');\r\n  const [selectedApplication, setSelectedApplication] = useState<string | null>(null);\r\n  const [selectedOfficer, setSelectedOfficer] = useState<string>('');\r\n  const [isAssigning, setIsAssigning] = useState(false);\r\n  const [activeTab, setActiveTab] = useState<'unassigned' | 'assigned'>('unassigned');\r\n\r\n  useEffect(() => {\r\n    if (!isAuthenticated) {\r\n      router.push('/auth/login');\r\n      return;\r\n    }\r\n\r\n    fetchApplications();\r\n    fetchOfficers();\r\n  }, [isAuthenticated, router, activeTab]);\r\n\r\n  const fetchApplications = async () => {\r\n    try {\r\n      setLoading(true);\r\n      \r\n      const response = activeTab === 'unassigned' \r\n        ? await taskAssignmentService.getUnassignedApplications({ limit: 50 })\r\n        : await taskAssignmentService.getAllApplications({ limit: 50 });\r\n      \r\n      setApplications(response.data || []);\r\n    } catch (err) {\r\n      console.error('Error fetching applications:', err);\r\n      setError('Failed to load applications');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const fetchOfficers = async () => {\r\n    try {\r\n      const response = await taskAssignmentService.getOfficers();\r\n      setOfficers(response.data || []);\r\n    } catch (err) {\r\n      console.error('Error fetching officers:', err);\r\n    }\r\n  };\r\n\r\n  const handleAssignApplication = async (applicationId: string, officerId: string) => {\r\n    try {\r\n      setIsAssigning(true);\r\n      \r\n      await taskAssignmentService.assignApplication(applicationId, { assignedTo: officerId });\r\n      \r\n      // Refresh applications list\r\n      await fetchApplications();\r\n      setSelectedApplication(null);\r\n      setSelectedOfficer('');\r\n      \r\n      // Show success message\r\n      setError('');\r\n      alert('Application assigned successfully!');\r\n    } catch (err) {\r\n      console.error('Error assigning application:', err);\r\n      setError('Failed to assign application');\r\n    } finally {\r\n      setIsAssigning(false);\r\n    }\r\n  };\r\n\r\n  const getStatusColor = (status: string) => {\r\n    switch (status.toLowerCase()) {\r\n      case 'draft': return 'bg-gray-100 text-gray-800';\r\n      case 'pending': return 'bg-yellow-100 text-yellow-800';\r\n      case 'submitted': return 'bg-blue-100 text-blue-800';\r\n      case 'under_review': return 'bg-orange-100 text-orange-800';\r\n      case 'evaluation': return 'bg-purple-100 text-purple-800';\r\n      case 'approved': return 'bg-green-100 text-green-800';\r\n      case 'rejected': return 'bg-red-100 text-red-800';\r\n      default: return 'bg-gray-100 text-gray-800';\r\n    }\r\n  };\r\n\r\n  const formatDate = (dateString: string) => {\r\n    return new Date(dateString).toLocaleDateString('en-US', {\r\n      year: 'numeric',\r\n      month: 'short',\r\n      day: 'numeric',\r\n      hour: '2-digit',\r\n      minute: '2-digit'\r\n    });\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex items-center justify-center min-h-96\">\r\n        <Loader message=\"Loading Task Assignment...\" />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"max-w-7xl mx-auto p-6\">\r\n      {/* Header */}\r\n      <div className=\"mb-8\">\r\n        <h1 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2\">\r\n          Task Assignment\r\n        </h1>\r\n        <p className=\"text-gray-600 dark:text-gray-400\">\r\n          Assign license applications to officers for processing and evaluation\r\n        </p>\r\n      </div>\r\n\r\n      {/* Error Message */}\r\n      {error && (\r\n        <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6\">\r\n          <p>{error}</p>\r\n        </div>\r\n      )}\r\n\r\n      {/* Tabs */}\r\n      <div className=\"border-b border-gray-200 dark:border-gray-700 mb-6\">\r\n        <nav className=\"-mb-px flex space-x-8\">\r\n          <button\r\n            type=\"button\"\r\n            onClick={() => setActiveTab('unassigned')}\r\n            className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center ${\r\n              activeTab === 'unassigned'\r\n                ? 'border-primary text-primary'\r\n                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\r\n            }`}\r\n          >\r\n            <i className=\"ri-file-list-line mr-2\"></i>\r\n            Unassigned Applications\r\n            <span className=\"ml-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 py-0.5 px-2 rounded-full text-xs\">\r\n              {applications.length}\r\n            </span>\r\n          </button>\r\n          <button\r\n            type=\"button\"\r\n            onClick={() => setActiveTab('assigned')}\r\n            className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center ${\r\n              activeTab === 'assigned'\r\n                ? 'border-primary text-primary'\r\n                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\r\n            }`}\r\n          >\r\n            <i className=\"ri-user-settings-line mr-2\"></i>\r\n            Assigned Applications\r\n          </button>\r\n        </nav>\r\n      </div>\r\n\r\n      {/* Applications Table */}\r\n      <div className=\"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md\">\r\n        <div className=\"overflow-x-auto\">\r\n          <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\r\n            <thead className=\"bg-gray-50 dark:bg-gray-700\">\r\n              <tr>\r\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Application\r\n                </th>\r\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Applicant\r\n                </th>\r\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  License Type\r\n                </th>\r\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Status\r\n                </th>\r\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  {activeTab === 'assigned' ? 'Assigned To' : 'Submitted'}\r\n                </th>\r\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Actions\r\n                </th>\r\n              </tr>\r\n            </thead>\r\n            <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\r\n              {applications.map((application) => (\r\n                <tr key={application.application_id} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\r\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                    <div className=\"flex items-center\">\r\n                      <div className=\"h-10 w-10 flex-shrink-0\">\r\n                        <div className=\"h-10 w-10 rounded-full bg-primary flex items-center justify-center\">\r\n                          <i className=\"ri-file-text-line text-white\"></i>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"ml-4\">\r\n                        <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                          {application.application_number}\r\n                        </div>\r\n                        <div className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                          ID: {application.application_id.slice(0, 8)}...\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                    <div className=\"text-sm text-gray-900 dark:text-gray-100\">\r\n                      {application.applicant.company_name || \r\n                       `${application.applicant.first_name} ${application.applicant.last_name}`}\r\n                    </div>\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                    <div className=\"text-sm text-gray-900 dark:text-gray-100\">\r\n                      {application.license_category.license_type.name}\r\n                    </div>\r\n                    <div className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                      {application.license_category.name}\r\n                    </div>\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(application.status)}`}>\r\n                      {application.status.replace('_', ' ').toUpperCase()}\r\n                    </span>\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                    {activeTab === 'assigned' && application.assignee ? (\r\n                      <div>\r\n                        <div className=\"text-sm text-gray-900 dark:text-gray-100\">\r\n                          {application.assignee.first_name} {application.assignee.last_name}\r\n                        </div>\r\n                        <div className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                          {application.assigned_at ? formatDate(application.assigned_at) : 'Recently assigned'}\r\n                        </div>\r\n                      </div>\r\n                    ) : (\r\n                      <div className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                        {formatDate(application.created_at)}\r\n                      </div>\r\n                    )}\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\r\n                    <div className=\"flex items-center justify-end space-x-2\">\r\n                      <button \r\n                        className=\"text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 p-1 rounded hover:bg-indigo-50 dark:hover:bg-indigo-900\" \r\n                        title=\"View Application\"\r\n                      >\r\n                        <i className=\"ri-eye-line\"></i>\r\n                      </button>\r\n                      {activeTab === 'unassigned' && (\r\n                        <button\r\n                          onClick={() => setSelectedApplication(application.application_id)}\r\n                          className=\"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 p-1 rounded hover:bg-green-50 dark:hover:bg-green-900\"\r\n                          title=\"Assign Application\"\r\n                        >\r\n                          <i className=\"ri-user-add-line\"></i>\r\n                        </button>\r\n                      )}\r\n                      {activeTab === 'assigned' && (\r\n                        <button\r\n                          onClick={() => setSelectedApplication(application.application_id)}\r\n                          className=\"text-orange-600 hover:text-orange-900 dark:text-orange-400 dark:hover:text-orange-300 p-1 rounded hover:bg-orange-50 dark:hover:bg-orange-900\"\r\n                          title=\"Reassign Application\"\r\n                        >\r\n                          <i className=\"ri-user-settings-line\"></i>\r\n                        </button>\r\n                      )}\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n              ))}\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Empty State */}\r\n      {applications.length === 0 && (\r\n        <div className=\"text-center py-12\">\r\n          <i className=\"ri-file-list-line text-4xl text-gray-400 mb-4\"></i>\r\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\">\r\n            No {activeTab} applications found\r\n          </h3>\r\n          <p className=\"text-gray-500 dark:text-gray-400\">\r\n            {activeTab === 'unassigned' \r\n              ? 'All applications have been assigned to officers.' \r\n              : 'No applications have been assigned yet.'}\r\n          </p>\r\n        </div>\r\n      )}\r\n\r\n      {/* Assignment Modal */}\r\n      {selectedApplication && (\r\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\r\n          <div className=\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800\">\r\n            <div className=\"mt-3\">\r\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4\">\r\n                Assign Application to Officer\r\n              </h3>\r\n              \r\n              <div className=\"mb-4\">\r\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                  Select Officer\r\n                </label>\r\n                <select\r\n                  value={selectedOfficer}\r\n                  onChange={(e) => setSelectedOfficer(e.target.value)}\r\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\r\n                >\r\n                  <option value=\"\">Select an officer...</option>\r\n                  {officers.map((officer) => (\r\n                    <option key={officer.user_id} value={officer.user_id}>\r\n                      {officer.first_name} {officer.last_name} - {officer.email}\r\n                    </option>\r\n                  ))}\r\n                </select>\r\n              </div>\r\n\r\n              <div className=\"flex justify-end space-x-3\">\r\n                <button\r\n                  onClick={() => {\r\n                    setSelectedApplication(null);\r\n                    setSelectedOfficer('');\r\n                  }}\r\n                  className=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n                >\r\n                  Cancel\r\n                </button>\r\n                <button\r\n                  onClick={() => handleAssignApplication(selectedApplication, selectedOfficer)}\r\n                  disabled={!selectedOfficer || isAssigning}\r\n                  className=\"px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                >\r\n                  {isAssigning ? 'Assigning...' : 'Assign'}\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AA6Ce,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACxC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC/D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;IAEtE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,CAAC,iBAAiB;gBACpB,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA;YACA;QACF;uCAAG;QAAC;QAAiB;QAAQ;KAAU;IAEvC,MAAM,oBAAoB;QACxB,IAAI;YACF,WAAW;YAEX,MAAM,WAAW,cAAc,eAC3B,MAAM,wIAAA,CAAA,wBAAqB,CAAC,yBAAyB,CAAC;gBAAE,OAAO;YAAG,KAClE,MAAM,wIAAA,CAAA,wBAAqB,CAAC,kBAAkB,CAAC;gBAAE,OAAO;YAAG;YAE/D,gBAAgB,SAAS,IAAI,IAAI,EAAE;QACrC,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,wIAAA,CAAA,wBAAqB,CAAC,WAAW;YACxD,YAAY,SAAS,IAAI,IAAI,EAAE;QACjC,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,MAAM,0BAA0B,OAAO,eAAuB;QAC5D,IAAI;YACF,eAAe;YAEf,MAAM,wIAAA,CAAA,wBAAqB,CAAC,iBAAiB,CAAC,eAAe;gBAAE,YAAY;YAAU;YAErF,4BAA4B;YAC5B,MAAM;YACN,uBAAuB;YACvB,mBAAmB;YAEnB,uBAAuB;YACvB,SAAS;YACT,MAAM;QACR,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,SAAS;QACX,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ,OAAO,WAAW;YACxB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,+HAAA,CAAA,UAAM;gBAAC,SAAQ;;;;;;;;;;;IAGtB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2D;;;;;;kCAGzE,6LAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;YAMjD,uBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;8BAAG;;;;;;;;;;;0BAKR,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,MAAK;4BACL,SAAS,IAAM,aAAa;4BAC5B,WAAW,CAAC,6EAA6E,EACvF,cAAc,eACV,gCACA,0HACJ;;8CAEF,6LAAC;oCAAE,WAAU;;;;;;gCAA6B;8CAE1C,6LAAC;oCAAK,WAAU;8CACb,aAAa,MAAM;;;;;;;;;;;;sCAGxB,6LAAC;4BACC,MAAK;4BACL,SAAS,IAAM,aAAa;4BAC5B,WAAW,CAAC,6EAA6E,EACvF,cAAc,aACV,gCACA,0HACJ;;8CAEF,6LAAC;oCAAE,WAAU;;;;;;gCAAiC;;;;;;;;;;;;;;;;;;0BAOpD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAM,WAAU;;0CACf,6LAAC;gCAAM,WAAU;0CACf,cAAA,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAoG;;;;;;sDAGlH,6LAAC;4CAAG,WAAU;sDAAoG;;;;;;sDAGlH,6LAAC;4CAAG,WAAU;sDAAoG;;;;;;sDAGlH,6LAAC;4CAAG,WAAU;sDAAoG;;;;;;sDAGlH,6LAAC;4CAAG,WAAU;sDACX,cAAc,aAAa,gBAAgB;;;;;;sDAE9C,6LAAC;4CAAG,WAAU;sDAAoG;;;;;;;;;;;;;;;;;0CAKtH,6LAAC;gCAAM,WAAU;0CACd,aAAa,GAAG,CAAC,CAAC,4BACjB,6LAAC;wCAAoC,WAAU;;0DAC7C,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAE,WAAU;;;;;;;;;;;;;;;;sEAGjB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACZ,YAAY,kBAAkB;;;;;;8EAEjC,6LAAC;oEAAI,WAAU;;wEAA2C;wEACnD,YAAY,cAAc,CAAC,KAAK,CAAC,GAAG;wEAAG;;;;;;;;;;;;;;;;;;;;;;;;0DAKpD,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;oDAAI,WAAU;8DACZ,YAAY,SAAS,CAAC,YAAY,IAClC,GAAG,YAAY,SAAS,CAAC,UAAU,CAAC,CAAC,EAAE,YAAY,SAAS,CAAC,SAAS,EAAE;;;;;;;;;;;0DAG7E,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;wDAAI,WAAU;kEACZ,YAAY,gBAAgB,CAAC,YAAY,CAAC,IAAI;;;;;;kEAEjD,6LAAC;wDAAI,WAAU;kEACZ,YAAY,gBAAgB,CAAC,IAAI;;;;;;;;;;;;0DAGtC,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;oDAAK,WAAW,CAAC,wEAAwE,EAAE,eAAe,YAAY,MAAM,GAAG;8DAC7H,YAAY,MAAM,CAAC,OAAO,CAAC,KAAK,KAAK,WAAW;;;;;;;;;;;0DAGrD,6LAAC;gDAAG,WAAU;0DACX,cAAc,cAAc,YAAY,QAAQ,iBAC/C,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;;gEACZ,YAAY,QAAQ,CAAC,UAAU;gEAAC;gEAAE,YAAY,QAAQ,CAAC,SAAS;;;;;;;sEAEnE,6LAAC;4DAAI,WAAU;sEACZ,YAAY,WAAW,GAAG,WAAW,YAAY,WAAW,IAAI;;;;;;;;;;;yEAIrE,6LAAC;oDAAI,WAAU;8DACZ,WAAW,YAAY,UAAU;;;;;;;;;;;0DAIxC,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,WAAU;4DACV,OAAM;sEAEN,cAAA,6LAAC;gEAAE,WAAU;;;;;;;;;;;wDAEd,cAAc,8BACb,6LAAC;4DACC,SAAS,IAAM,uBAAuB,YAAY,cAAc;4DAChE,WAAU;4DACV,OAAM;sEAEN,cAAA,6LAAC;gEAAE,WAAU;;;;;;;;;;;wDAGhB,cAAc,4BACb,6LAAC;4DACC,SAAS,IAAM,uBAAuB,YAAY,cAAc;4DAChE,WAAU;4DACV,OAAM;sEAEN,cAAA,6LAAC;gEAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;uCA5Ed,YAAY,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;YAyF5C,aAAa,MAAM,KAAK,mBACvB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;;;;;;kCACb,6LAAC;wBAAG,WAAU;;4BAA4D;4BACpE;4BAAU;;;;;;;kCAEhB,6LAAC;wBAAE,WAAU;kCACV,cAAc,eACX,qDACA;;;;;;;;;;;;YAMT,qCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA4D;;;;;;0CAI1E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;wCAClD,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAG;;;;;;4CAChB,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;oDAA6B,OAAO,QAAQ,OAAO;;wDACjD,QAAQ,UAAU;wDAAC;wDAAE,QAAQ,SAAS;wDAAC;wDAAI,QAAQ,KAAK;;mDAD9C,QAAQ,OAAO;;;;;;;;;;;;;;;;;0CAOlC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS;4CACP,uBAAuB;4CACvB,mBAAmB;wCACrB;wCACA,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,SAAS,IAAM,wBAAwB,qBAAqB;wCAC5D,UAAU,CAAC,mBAAmB;wCAC9B,WAAU;kDAET,cAAc,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlD;GA/UwB;;QACY,kIAAA,CAAA,UAAO;QAC1B,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}