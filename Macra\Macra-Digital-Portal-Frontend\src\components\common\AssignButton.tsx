'use client';

import React, { useState } from 'react';
import AssignModal from './AssignModal';

interface AssignButtonProps {
  itemId: string;
  itemType: 'data_breach' | 'application' | 'complaint';
  itemTitle?: string;
  onAssignSuccess?: () => void;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'secondary' | 'success';
  disabled?: boolean;
  children?: React.ReactNode;
}

const AssignButton: React.FC<AssignButtonProps> = ({
  itemId,
  itemType,
  itemTitle,
  onAssignSuccess,
  className = '',
  size = 'sm',
  variant = 'success',
  disabled = false,
  children
}) => {
  const [showAssignModal, setShowAssignModal] = useState(false);

  const handleAssignClick = () => {
    setShowAssignModal(true);
  };

  const handleCloseModal = () => {
    setShowAssignModal(false);
  };

  const handleAssignSuccess = () => {
    setShowAssignModal(false);
    onAssignSuccess?.();
  };

  // Size classes
  const sizeClasses = {
    sm: 'px-3 py-1 text-xs',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base'
  };

  // Variant classes
  const variantClasses = {
    primary: 'text-white bg-primary hover:bg-primary-dark focus:ring-primary',
    secondary: 'text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 focus:ring-gray-500',
    success: 'text-white bg-green-600 hover:bg-green-700 focus:ring-green-500'
  };

  // Base classes
  const baseClasses = 'inline-flex items-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed';

  // Combine all classes
  const buttonClasses = `${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]} ${className}`;

  return (
    <>
      <button
        type="button"
        onClick={handleAssignClick}
        disabled={disabled}
        className={buttonClasses}
        title={`Assign ${itemType.replace('_', ' ')} to officer`}
      >
        <i className="ri-user-add-line mr-1"></i>
        {children || 'Assign'}
      </button>

      <AssignModal
        isOpen={showAssignModal}
        onClose={handleCloseModal}
        itemId={itemId}
        itemType={itemType}
        itemTitle={itemTitle}
        onAssignSuccess={handleAssignSuccess}
      />
    </>
  );
};

export default AssignButton;
