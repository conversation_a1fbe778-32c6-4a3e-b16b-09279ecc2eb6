{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/form-fixes.css"], "sourcesContent": ["/* Form input visibility fixes for dark mode browsers */\r\n\r\n/* Force text color for inputs to ensure visibility regardless of browser/system dark mode */\r\ninput, \r\ntextarea, \r\nselect {\r\n  color: black !important;\r\n}\r\n\r\n/* When the app is explicitly in dark mode, use white text */\r\n.dark input,\r\n.dark textarea,\r\n.dark select {\r\n  color: white !important;\r\n}\r\n\r\n/* Ensure labels are visible */\r\nlabel {\r\n  color: black;\r\n}\r\n\r\n.dark label {\r\n  color: white;\r\n}\r\n\r\n/* Fix for placeholder text */\r\n::-moz-placeholder {\r\n  color: #9ca3af !important; /* gray-400 */\r\n  opacity: 1;\r\n}\r\n::placeholder {\r\n  color: #9ca3af !important; /* gray-400 */\r\n  opacity: 1;\r\n}\r\n\r\n.dark ::-moz-placeholder {\r\n  color: #6b7280 !important; /* gray-500 */\r\n  opacity: 1;\r\n}\r\n\r\n.dark ::placeholder {\r\n  color: #6b7280 !important; /* gray-500 */\r\n  opacity: 1;\r\n}"], "names": [], "mappings": "AAGA;;;;AAOA;;;;AAOA;;;;AAIA;;;;AAKA;;;;;AAAA;;;;;AASA;;;;;AAAA"}}]}