'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../contexts/AuthContext';
import Header from "@/components/Header";
import Sidebar from "@/components/Sidebar";
import AuthDebug from "@/components/AuthDebug";

export default function DashboardLayout({ children }: { children: React.ReactNode }) {
  const { isAuthenticated, loading, user, token } = useAuth();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('overview');
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);
  const [redirectAttempted, setRedirectAttempted] = useState(false);

  useEffect(() => {
    console.log('Dashboard Layout - Auth State:', { isAuthenticated, loading, hasUser: !!user, hasToken: !!token });
    
    // Only redirect if we haven't already attempted a redirect and we're sure the user is not authenticated
    if (!loading && !isAuthenticated && !redirectAttempted) {
      console.log('Dashboard Layout - Redirecting to login due to no authentication');
      setRedirectAttempted(true);
      router.push('/auth/login');
    }
  }, [isAuthenticated, loading, router, user, token, redirectAttempted]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect
  }

  const toggleMobileSidebar = () => {
    setIsMobileSidebarOpen(!isMobileSidebarOpen);
  };

  return (
    <div className="flex h-screen overflow-hidden">
      {/* Mobile sidebar overlay */}
      <div
        id="mobileSidebarOverlay"
        className={`mobile-sidebar-overlay ${isMobileSidebarOpen ? 'show' : ''}`}
        onClick={() => setIsMobileSidebarOpen(false)}
      ></div>

      <Sidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header
          activeTab={activeTab}
          onTabChange={setActiveTab}
          onMobileMenuToggle={toggleMobileSidebar}
        />
        {children}
      </div>
      <AuthDebug />
    </div>
  );
}