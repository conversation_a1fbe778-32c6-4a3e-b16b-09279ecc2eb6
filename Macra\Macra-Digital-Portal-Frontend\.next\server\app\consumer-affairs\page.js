(()=>{var e={};e.id=1436,e.ids=[1436],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20563:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\consumer-affairs\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\consumer-affairs\\layout.tsx","default")},21473:(e,t,r)=>{Promise.resolve().then(r.bind(r,80008))},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34625:(e,t,r)=>{Promise.resolve().then(r.bind(r,34862))},34862:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var a=r(60687),s=r(43210),i=r(63213),l=r(56703),n=r(71773);let d=()=>{let{isAuthenticated:e}=(0,i.A)(),[t,r]=(0,s.useState)([]),[d,o]=(0,s.useState)(!0),[c,u]=(0,s.useState)(""),[g,x]=(0,s.useState)({status:"",category:"",priority:"",search:""});(0,s.useEffect)(()=>{(async()=>{if(e)try{o(!0);let e=await l.Ck.getComplaints({limit:100,...g});e.success&&Array.isArray(e.data)?r(e.data):r([])}catch(e){e instanceof Error?u(`Failed to load complaints: ${e.message}`):u("Failed to load complaints: Unknown error")}finally{o(!1)}})()},[e,g]);let m=async(e,t)=>{try{r(r=>r.map(r=>r.complaint_id===e?{...r,status:t}:r))}catch(e){}},p=e=>{switch(e?.toLowerCase()){case"submitted":return"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";case"under_review":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";case"investigating":return"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";case"resolved":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}},h=e=>{switch(e?.toLowerCase()){case"low":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"medium":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";case"high":return"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";case"urgent":return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}};return d?(0,a.jsx)("div",{className:"p-6 min-h-full bg-gray-50 dark:bg-gray-900",children:(0,a.jsx)(n.A,{message:"Loading consumer affairs complaints..."})}):(0,a.jsxs)("div",{className:"p-6 min-h-full bg-gray-50 dark:bg-gray-900",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:"Consumer Affairs Management"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"Manage and respond to customer complaints and service issues"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6",children:[(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-file-list-line text-2xl text-blue-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Total Complaints"}),(0,a.jsx)("p",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:t.length})]})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-time-line text-2xl text-yellow-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Submitted"}),(0,a.jsx)("p",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:t.filter(e=>"submitted"===e.status).length})]})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-progress-line text-2xl text-blue-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Under Review"}),(0,a.jsx)("p",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:t.filter(e=>"under_review"===e.status).length})]})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-check-line text-2xl text-green-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Resolved"}),(0,a.jsx)("p",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:t.filter(e=>"resolved"===e.status).length})]})]})})]}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Search"}),(0,a.jsx)("input",{type:"text",placeholder:"Search complaints...",value:g.search,onChange:e=>x(t=>({...t,search:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-gray-100"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Status"}),(0,a.jsxs)("select",{value:g.status,onChange:e=>x(t=>({...t,status:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-gray-100","aria-label":"Filter by status",title:"Filter complaints by status",children:[(0,a.jsx)("option",{value:"",children:"All Statuses"}),(0,a.jsx)("option",{value:"submitted",children:"Submitted"}),(0,a.jsx)("option",{value:"under_review",children:"Under Review"}),(0,a.jsx)("option",{value:"investigating",children:"Investigating"}),(0,a.jsx)("option",{value:"resolved",children:"Resolved"}),(0,a.jsx)("option",{value:"closed",children:"Closed"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Category"}),(0,a.jsxs)("select",{value:g.category,onChange:e=>x(t=>({...t,category:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-gray-100","aria-label":"Filter by category",title:"Filter complaints by category",children:[(0,a.jsx)("option",{value:"",children:"All Categories"}),(0,a.jsx)("option",{value:"Billing & Charges",children:"Billing & Charges"}),(0,a.jsx)("option",{value:"Service Quality",children:"Service Quality"}),(0,a.jsx)("option",{value:"Network Issues",children:"Network Issues"}),(0,a.jsx)("option",{value:"Customer Service",children:"Customer Service"}),(0,a.jsx)("option",{value:"Other",children:"Other"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Priority"}),(0,a.jsxs)("select",{value:g.priority,onChange:e=>x(t=>({...t,priority:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-gray-100","aria-label":"Filter by priority",title:"Filter complaints by priority",children:[(0,a.jsx)("option",{value:"",children:"All Priorities"}),(0,a.jsx)("option",{value:"low",children:"Low"}),(0,a.jsx)("option",{value:"medium",children:"Medium"}),(0,a.jsx)("option",{value:"high",children:"High"}),(0,a.jsx)("option",{value:"urgent",children:"Urgent"})]})]})]})}),c&&(0,a.jsx)("div",{className:"bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("i",{className:"ri-error-warning-line text-red-400 mr-2"}),(0,a.jsx)("p",{className:"text-red-700 dark:text-red-200",children:c})]})}),(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-700",children:(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:["Consumer Affairs Complaints (",t.length,")"]})}),0===t.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("i",{className:"ri-file-search-line text-4xl text-gray-400 mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"No complaints found"}),(0,a.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"No consumer affairs complaints have been submitted yet."})]}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[(0,a.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-900",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Complaint"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Category"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Priority"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Submitted"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:t.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:e.title}),(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs",children:e.description})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",children:e.category})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${p(e.status)}`,children:e.status?.replace("_"," ").toUpperCase()})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${h(e.priority)}`,children:e.priority?.toUpperCase()||"MEDIUM"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:new Date(e.created_at).toLocaleDateString()}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[(0,a.jsx)("button",{type:"button",className:"text-primary hover:text-red-700 mr-3",disabled:!0,title:"View Details modal not implemented",children:"View Details"}),(0,a.jsxs)("select",{value:e.status,onChange:t=>m(e.complaint_id,t.target.value),className:"text-xs border border-gray-300 dark:border-gray-600 rounded px-2 py-1 dark:bg-gray-700 dark:text-gray-100","aria-label":"Update complaint status",title:"Update complaint status",children:[(0,a.jsx)("option",{value:"submitted",children:"Submitted"}),(0,a.jsx)("option",{value:"under_review",children:"Under Review"}),(0,a.jsx)("option",{value:"investigating",children:"Investigating"}),(0,a.jsx)("option",{value:"resolved",children:"Resolved"}),(0,a.jsx)("option",{value:"closed",children:"Closed"})]})]})]},e.complaint_id))})]})})]})]})}},35393:(e,t,r)=>{"use strict";r.d(t,{A:()=>s}),r(60687);var a=r(63213);function s(){let{user:e,token:t,loading:r,isAuthenticated:s}=(0,a.A)();return null}},40026:(e,t,r)=>{Promise.resolve().then(r.bind(r,93313))},44453:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>g,tree:()=>o});var a=r(65239),s=r(48088),i=r(88170),l=r.n(i),n=r(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(t,d);let o={children:["",{children:["consumer-affairs",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,80008)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\consumer-affairs\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,20563)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\consumer-affairs\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\consumer-affairs\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},g=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/consumer-affairs/page",pathname:"/consumer-affairs",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},50642:(e,t,r)=>{Promise.resolve().then(r.bind(r,20563))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56703:(e,t,r)=>{"use strict";r.d(t,{Ck:()=>i});var a=r(12234),s=r(51278);let i={async createComplaint(e){try{let t=new FormData;t.append("title",e.title),t.append("description",e.description),t.append("category",e.category),e.priority&&t.append("priority",e.priority),e.attachments&&e.attachments.length>0&&e.attachments.forEach(e=>{t.append("attachments",e)});let r=await a.uE.post("/consumer-affairs-complaints",t,{headers:{"Content-Type":"multipart/form-data"}});return(0,s.zp)(r)}catch(e){throw e}},async getComplaints(e={}){let t=new URLSearchParams;e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(([e,r])=>{Array.isArray(r)?r.forEach(r=>t.append(`filter.${e}`,r)):t.set(`filter.${e}`,r)});let r=await a.uE.get(`/consumer-affairs-complaints?${t.toString()}`);return(0,s.zp)(r)},async getComplaint(e){let t=await a.uE.get(`/consumer-affairs-complaints/${e}`);return(0,s.zp)(t)},async getComplaintById(e){return this.getComplaint(e)},async updateComplaint(e,t){let r=await a.uE.put(`/consumer-affairs-complaints/${e}`,t);return(0,s.zp)(r)},async deleteComplaint(e){await a.uE.delete(`/consumer-affairs-complaints/${e}`)},async updateComplaintStatus(e,t,r){let i=await a.uE.put(`/consumer-affairs-complaints/${e}/status`,{status:t,comment:r});return(0,s.zp)(i)},async addAttachment(e,t){let r=new FormData;r.append("files",t);let i=await a.uE.post(`/consumer-affairs-complaints/${e}/attachments`,r,{headers:{"Content-Type":"multipart/form-data"}});return(0,s.zp)(i)},async removeAttachment(e,t){await a.uE.delete(`/consumer-affairs-complaints/${e}/attachments/${t}`)},getStatusColor(e){switch(e?.toLowerCase()){case"submitted":return"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";case"under_review":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";case"investigating":return"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";case"resolved":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}},getPriorityColor(e){switch(e?.toLowerCase()){case"low":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"medium":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";case"high":return"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";case"urgent":return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}},getStatusOptions:()=>[{value:"submitted",label:"Submitted"},{value:"under_review",label:"Under Review"},{value:"investigating",label:"Investigating"},{value:"resolved",label:"Resolved"},{value:"closed",label:"Closed"}],getCategoryOptions:()=>[{value:"Billing & Charges",label:"Billing & Charges"},{value:"Service Quality",label:"Service Quality"},{value:"Network Issues",label:"Network Issues"},{value:"Customer Service",label:"Customer Service"},{value:"Contract Disputes",label:"Contract Disputes"},{value:"Accessibility",label:"Accessibility"},{value:"Fraud & Scams",label:"Fraud & Scams"},{value:"Other",label:"Other"}],getPriorityOptions:()=>[{value:"low",label:"Low"},{value:"medium",label:"Medium"},{value:"high",label:"High"},{value:"urgent",label:"Urgent"}]}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},80008:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\consumer-affairs\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\consumer-affairs\\page.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},93313:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var a=r(60687),s=r(43210),i=r(16189),l=r(63213),n=r(21891),d=r(60417),o=r(35393);function c({children:e}){let{isAuthenticated:t,loading:r}=(0,l.A)();(0,i.useRouter)();let[c,u]=(0,s.useState)("overview"),[g,x]=(0,s.useState)(!1);return r?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"})}):t?(0,a.jsxs)("div",{className:"flex h-screen overflow-hidden bg-gray-50 dark:bg-gray-900",children:[(0,a.jsx)("div",{id:"mobileSidebarOverlay",className:`mobile-sidebar-overlay ${g?"show":""}`,onClick:()=>x(!1)}),(0,a.jsx)(d.A,{}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,a.jsx)(n.A,{activeTab:c,onTabChange:u,onMobileMenuToggle:()=>{x(!g)}}),(0,a.jsx)("main",{className:"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900",children:e})]}),(0,a.jsx)(o.A,{})]}):null}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,7498,1658,5814,7563,6606],()=>r(44453));module.exports=a})();