(()=>{var e={};e.id=6584,e.ids=[6584],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11112:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\data-protection\\page.tsx","default")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},22145:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(60687);let s=(0,r(43210).forwardRef)(({label:e,error:t,helperText:r,variant:s="default",fullWidth:i=!0,className:l="",required:n,disabled:o,...d},c)=>{let m=`px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 ${i?"w-full":""} ${"small"===s?"py-1.5 text-sm":"py-2"}`,u=`${m} ${t?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"} ${o?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""} ${l}`,g=`block font-medium text-gray-700 dark:text-gray-300 mb-2 ${"small"===s?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"}`;return(0,a.jsxs)("div",{className:"w-full",children:[e&&(0,a.jsxs)("label",{className:g,children:[e,n&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,a.jsx)("input",{ref:c,className:u,disabled:o,required:n,...d}),t&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:t}),r&&!t&&(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:r})]})});s.displayName="TextInput";let i=s},23896:(e,t,r)=>{Promise.resolve().then(r.bind(r,80914))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56703:(e,t,r)=>{"use strict";r.d(t,{Ck:()=>i});var a=r(72901),s=r(51278);let i={async createComplaint(e){try{let t=new FormData;t.append("title",e.title),t.append("description",e.description),t.append("category",e.category),e.priority&&t.append("priority",e.priority),e.attachments&&e.attachments.length>0&&e.attachments.forEach(e=>{t.append("attachments",e)});let r=await a.uE.post("/consumer-affairs-complaints",t,{headers:{"Content-Type":"multipart/form-data"}});return(0,s.zp)(r)}catch(e){throw e}},async getComplaints(e={}){let t=new URLSearchParams;e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(([e,r])=>{Array.isArray(r)?r.forEach(r=>t.append(`filter.${e}`,r)):t.set(`filter.${e}`,r)});let r=await a.uE.get(`/consumer-affairs-complaints?${t.toString()}`);return(0,s.zp)(r)},async getComplaint(e){let t=await a.uE.get(`/consumer-affairs-complaints/${e}`);return(0,s.zp)(t)},async getComplaintById(e){return this.getComplaint(e)},async updateComplaint(e,t){let r=await a.uE.put(`/consumer-affairs-complaints/${e}`,t);return(0,s.zp)(r)},async deleteComplaint(e){await a.uE.delete(`/consumer-affairs-complaints/${e}`)},async updateComplaintStatus(e,t,r){let i=await a.uE.put(`/consumer-affairs-complaints/${e}/status`,{status:t,comment:r});return(0,s.zp)(i)},async addAttachment(e,t){let r=new FormData;r.append("files",t);let i=await a.uE.post(`/consumer-affairs-complaints/${e}/attachments`,r,{headers:{"Content-Type":"multipart/form-data"}});return(0,s.zp)(i)},async removeAttachment(e,t){await a.uE.delete(`/consumer-affairs-complaints/${e}/attachments/${t}`)},getStatusColor(e){switch(e?.toLowerCase()){case"submitted":return"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";case"under_review":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";case"investigating":return"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";case"resolved":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}},getPriorityColor(e){switch(e?.toLowerCase()){case"low":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"medium":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";case"high":return"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";case"urgent":return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}},getStatusOptions:()=>[{value:"submitted",label:"Submitted"},{value:"under_review",label:"Under Review"},{value:"investigating",label:"Investigating"},{value:"resolved",label:"Resolved"},{value:"closed",label:"Closed"}],getCategoryOptions:()=>[{value:"Billing & Charges",label:"Billing & Charges"},{value:"Service Quality",label:"Service Quality"},{value:"Network Issues",label:"Network Issues"},{value:"Customer Service",label:"Customer Service"},{value:"Contract Disputes",label:"Contract Disputes"},{value:"Accessibility",label:"Accessibility"},{value:"Fraud & Scams",label:"Fraud & Scams"},{value:"Other",label:"Other"}],getPriorityOptions:()=>[{value:"low",label:"Low"},{value:"medium",label:"Medium"},{value:"high",label:"High"},{value:"urgent",label:"Urgent"}]}},62978:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(60687);let s=(0,r(43210).forwardRef)(({label:e,error:t,helperText:r,variant:s="default",fullWidth:i=!0,className:l="",required:n,disabled:o,rows:d=3,...c},m)=>{let u=`px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 resize-y ${i?"w-full":""} ${"small"===s?"py-1.5 text-sm":"py-2"}`,g=`${u} ${t?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"} ${o?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""} ${l}`,p=`block font-medium text-gray-700 dark:text-gray-300 mb-2 ${"small"===s?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"}`;return(0,a.jsxs)("div",{className:"w-full",children:[e&&(0,a.jsxs)("label",{className:p,children:[e,n&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,a.jsx)("textarea",{ref:m,className:g,disabled:o,required:n,rows:d,...c}),t&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:t}),r&&!t&&(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:r})]})});s.displayName="TextArea";let i=s},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74040:(e,t,r)=>{"use strict";r.d(t,{nU:()=>i});var a=r(72901),s=r(51278);let i={async createReport(e){try{let t=new FormData;t.append("title",e.title),t.append("description",e.description),t.append("category",e.category),t.append("severity",e.severity),t.append("incident_date",e.incident_date),t.append("organization_involved",e.organization_involved),e.priority&&t.append("priority",e.priority),e.affected_data_types&&t.append("affected_data_types",e.affected_data_types),e.contact_attempts&&t.append("contact_attempts",e.contact_attempts),e.attachments&&e.attachments.length>0&&e.attachments.forEach(e=>{t.append("attachments",e)});let r=await a.uE.post("/data-breach-reports",t,{headers:{"Content-Type":"multipart/form-data"}});return(0,s.zp)(r)}catch(e){throw e}},async getReports(e={}){let t=new URLSearchParams;e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(([e,r])=>{Array.isArray(r)?r.forEach(r=>t.append(`filter.${e}`,r)):t.set(`filter.${e}`,r)});let r=await a.uE.get(`/data-breach-reports?${t.toString()}`);return(0,s.zp)(r)},async getReport(e){let t=await a.uE.get(`/data-breach-reports/${e}`);return(0,s.zp)(t)},async getReportById(e){return this.getReport(e)},async updateReport(e,t){let r=await a.uE.put(`/data-breach-reports/${e}`,t);return(0,s.zp)(r)},async deleteReport(e){await a.uE.delete(`/data-breach-reports/${e}`)},async updateReportStatus(e,t,r){let i=await a.uE.put(`/data-breach-reports/${e}/status`,{status:t,comment:r});return(0,s.zp)(i)},async addAttachment(e,t){let r=new FormData;r.append("files",t);let i=await a.uE.post(`/data-breach-reports/${e}/attachments`,r,{headers:{"Content-Type":"multipart/form-data"}});return(0,s.zp)(i)},async removeAttachment(e,t){await a.uE.delete(`/data-breach-reports/${e}/attachments/${t}`)},getStatusColor(e){switch(e?.toLowerCase()){case"submitted":return"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";case"under_review":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";case"investigating":return"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";case"resolved":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}},getSeverityColor(e){switch(e?.toLowerCase()){case"low":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"medium":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";case"high":return"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";case"critical":return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}},getStatusOptions:()=>[{value:"submitted",label:"Submitted"},{value:"under_review",label:"Under Review"},{value:"investigating",label:"Investigating"},{value:"resolved",label:"Resolved"},{value:"closed",label:"Closed"}],getCategoryOptions:()=>[{value:"Personal Data",label:"Personal Data"},{value:"Financial Data",label:"Financial Data"},{value:"Health Data",label:"Health Data"},{value:"Technical Data",label:"Technical Data"},{value:"Communication Data",label:"Communication Data"},{value:"Other",label:"Other"}],getSeverityOptions:()=>[{value:"low",label:"Low"},{value:"medium",label:"Medium"},{value:"high",label:"High"},{value:"critical",label:"Critical"}],getPriorityOptions:()=>[{value:"low",label:"Low"},{value:"medium",label:"Medium"},{value:"high",label:"High"},{value:"urgent",label:"Urgent"}]}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},80914:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>v});var a=r(60687),s=r(43210),i=r(16189),l=r(94391),n=r(71773),o=r(63213),d=r(22145),c=r(62978),m=r(86732),u=r(56703),g=r(87911);let p=["Billing & Charges","Service Quality","Network Issues","Customer Service","Contract Disputes","Accessibility","Fraud & Scams","Other"],x=({onClose:e,onSubmit:t})=>{let{showSuccess:r,showError:i}=(0,g.d)(),[l,n]=(0,s.useState)({title:"",description:"",category:""}),[o,x]=(0,s.useState)({}),[y,h]=(0,s.useState)(!1),[b,f]=(0,s.useState)([]),v=e=>{let{name:t,value:r}=e.target;n(e=>({...e,[t]:r})),o[t]&&x(e=>({...e,[t]:""}))},j=e=>{f(t=>t.filter((t,r)=>r!==e))},k=()=>{let e={};return l.title.trim()||(e.title="Title is required"),l.description.trim()?l.description.trim().length<20&&(e.description="Description must be at least 20 characters"):e.description="Description is required",l.category||(e.category="Category is required"),x(e),0===Object.keys(e).length},w=async e=>{if(e.preventDefault(),k()){h(!0);try{let e={title:l.title,description:l.description,category:l.category,attachments:b},a=await u.Ck.createComplaint(e);if(a.success)r(`Your complaint has been submitted successfully! Reference ID: ${a.data.complaint_id||"N/A"}`,6e3),t(a.data),n({title:"",description:"",category:""}),f([]);else throw Error(a.message||"Failed to submit complaint")}catch(e){i(e instanceof Error?e.message:"Failed to submit complaint. Please try again.")}finally{h(!1)}}},N=e=>{if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]};return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:"Lodge Consumer Affairs Complaint"}),(0,a.jsx)("button",{type:"button",onClick:e,"aria-label":"Close modal",className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:(0,a.jsx)("i",{className:"ri-close-line text-xl"})})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Submit your complaint about telecommunications services, billing issues, or other consumer concerns. Our team will investigate and work to resolve your issue."})}),(0,a.jsxs)("form",{onSubmit:w,className:"space-y-6",children:[(0,a.jsx)(d.A,{label:"Complaint Title *",id:"complaint-title",name:"title",value:l.title,onChange:v,placeholder:"Brief summary of your complaint",error:o.title,required:!0}),(0,a.jsxs)(m.A,{label:"Category",name:"category",value:l.category,onChange:v,error:o.category,required:!0,children:[(0,a.jsx)("option",{value:"",children:"Select a category"}),p.map(e=>(0,a.jsx)("option",{value:e,children:e},e))]}),(0,a.jsx)("div",{children:(0,a.jsx)(c.A,{label:"Detailed Description *",id:"complaint-description",name:"description",value:l.description,onChange:v,rows:6,placeholder:"Please provide a detailed description of your complaint, including dates, times, and any relevant information...",error:o.description,helperText:"Minimum 20 characters required",required:!0})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"complaint-attachments",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Supporting Documents (Optional)"}),(0,a.jsx)("input",{id:"complaint-attachments",type:"file",multiple:!0,accept:".pdf,.doc,.docx,.jpg,.jpeg,.png",onChange:e=>{if(e.target.files){let t=Array.from(e.target.files);f(e=>[...e,...t])}},className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"Accepted formats: PDF, DOC, DOCX, JPG, PNG (Max 5MB per file)"}),b.length>0&&(0,a.jsx)("div",{className:"mt-3 space-y-2",children:b.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center justify-between bg-gray-50 dark:bg-gray-700 p-2 rounded",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"ri-file-line text-gray-400 mr-2"}),(0,a.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:e.name}),(0,a.jsxs)("span",{className:"text-xs text-gray-500 dark:text-gray-400 ml-2",children:["(",N(e.size),")"]})]}),(0,a.jsx)("button",{type:"button",onClick:()=>j(t),className:"text-red-500 hover:text-red-700","aria-label":`Remove ${e.name}`,children:(0,a.jsx)("i",{className:"ri-close-line"})})]},t))})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700",children:[(0,a.jsx)("button",{type:"button",onClick:e,className:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:"Cancel"}),(0,a.jsx)("button",{type:"submit",disabled:y,className:"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-300",children:y?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"ri-loader-4-line animate-spin mr-2"}),"Submitting..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"ri-send-plane-line mr-2"}),"Submit Complaint"]})})]})]})]})]})})};var y=r(74040);let h=["Unauthorized Data Access","Data Misuse or Sharing","Privacy Violations","Identity Theft","Phishing Attempts","Data Loss or Theft","Consent Violations","Other"],b=[{value:"low",label:"Low - Minor privacy concern"},{value:"medium",label:"Medium - Moderate data exposure"},{value:"high",label:"High - Significant data breach"},{value:"critical",label:"Critical - Severe security incident"}],f=({onClose:e,onSubmit:t})=>{let{showSuccess:r,showError:i}=(0,g.d)(),[l,n]=(0,s.useState)({title:"",description:"",category:"",severity:"",incidentDate:"",affectedData:"",organization:"",contactAttempts:""}),[o,u]=(0,s.useState)({}),[p,x]=(0,s.useState)(!1),[f,v]=(0,s.useState)([]),j=e=>{let{name:t,value:r}=e.target;n(e=>({...e,[t]:r})),o[t]&&u(e=>({...e,[t]:""}))},k=e=>{v(t=>t.filter((t,r)=>r!==e))},w=()=>{let e={};return l.title.trim()||(e.title="Title is required"),l.description.trim()?l.description.trim().length<20&&(e.description="Description must be at least 20 characters"):e.description="Description is required",l.category||(e.category="Category is required"),l.severity||(e.severity="Severity level is required"),l.incidentDate||(e.incidentDate="Incident date is required"),l.organization.trim()||(e.organization="Organization involved is required"),u(e),0===Object.keys(e).length},N=async e=>{if(e.preventDefault(),w()){x(!0);try{let e={title:l.title,description:l.description,category:l.category,severity:l.severity,incident_date:l.incidentDate,organization_involved:l.organization,affected_data_types:l.affectedData,contact_attempts:l.contactAttempts,attachments:f},a=await y.nU.createReport(e);if(a.success)r(`Your data breach report has been submitted successfully! Reference ID: ${a.data.report_id||"N/A"}`,6e3),t(a.data),n({title:"",description:"",category:"",severity:"",incidentDate:"",affectedData:"",organization:"",contactAttempts:""}),v([]);else throw Error(a.message||"Failed to submit data breach report")}catch(e){i(e instanceof Error?e.message:"Failed to submit data breach report. Please try again.")}finally{x(!1)}}},C=e=>{if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]};return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:"Report Data Breach"}),(0,a.jsx)("button",{type:"button",onClick:e,"aria-label":"Close modal",className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:(0,a.jsx)("i",{className:"ri-close-line text-xl"})})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("i",{className:"ri-shield-keyhole-line text-red-600 text-lg mr-3 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-red-800 dark:text-red-300 mb-1",children:"Data Breach Reporting"}),(0,a.jsx)("p",{className:"text-sm text-red-700 dark:text-red-400",children:"Report unauthorized access, misuse, or breach of your personal data. This information will be treated confidentially and investigated promptly."})]})]})})}),(0,a.jsxs)("form",{onSubmit:N,className:"space-y-6",children:[(0,a.jsx)(d.A,{label:"Incident Title *",id:"breach-title",name:"title",value:l.title,onChange:j,placeholder:"Brief summary of the data breach incident",error:o.title,required:!0}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)(m.A,{label:"Breach Category *",name:"category",value:l.category,onChange:j,error:o.category,required:!0,children:[(0,a.jsx)("option",{value:"",children:"Select a category"}),h.map(e=>(0,a.jsx)("option",{value:e,children:e},e))]}),(0,a.jsxs)(m.A,{label:"Severity Level *",name:"severity",value:l.severity,onChange:j,error:o.severity,required:!0,children:[(0,a.jsx)("option",{value:"",children:"Select severity level"}),b.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)(d.A,{label:"Incident Date *",id:"incident-date",name:"incidentDate",type:"date",value:l.incidentDate,onChange:j,error:o.incidentDate,required:!0}),(0,a.jsx)(d.A,{label:"Organization Involved *",id:"organization",name:"organization",value:l.organization,onChange:j,placeholder:"Name of the organization responsible",error:o.organization,required:!0})]}),(0,a.jsx)(c.A,{label:"Affected Data Types",id:"affected-data",name:"affectedData",value:l.affectedData,onChange:j,rows:3,placeholder:"Describe what type of personal data was affected (e.g., names, phone numbers, addresses, financial information)",error:o.affectedData}),(0,a.jsx)(c.A,{label:"Detailed Description *",id:"breach-description",name:"description",value:l.description,onChange:j,rows:6,placeholder:"Please provide a detailed description of the incident, including how you discovered it, what happened, and any impact on you...",error:o.description,helperText:"Minimum 20 characters required",required:!0}),(0,a.jsx)(c.A,{label:"Previous Contact Attempts",id:"contact-attempts",name:"contactAttempts",value:l.contactAttempts,onChange:j,rows:3,placeholder:"Describe any attempts you made to contact the organization about this incident",error:o.contactAttempts}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"breach-attachments",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Supporting Evidence (Optional)"}),(0,a.jsx)("input",{id:"breach-attachments",type:"file",multiple:!0,accept:".pdf,.doc,.docx,.jpg,.jpeg,.png",onChange:e=>{if(e.target.files){let t=Array.from(e.target.files);v(e=>[...e,...t])}},className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"Screenshots, emails, documents, or other evidence (Max 5MB per file)"}),f.length>0&&(0,a.jsx)("div",{className:"mt-3 space-y-2",children:f.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center justify-between bg-gray-50 dark:bg-gray-700 p-2 rounded",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"ri-file-line text-gray-400 mr-2"}),(0,a.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:e.name}),(0,a.jsxs)("span",{className:"text-xs text-gray-500 dark:text-gray-400 ml-2",children:["(",C(e.size),")"]})]}),(0,a.jsx)("button",{type:"button",onClick:()=>k(t),className:"text-red-500 hover:text-red-700","aria-label":`Remove ${e.name}`,children:(0,a.jsx)("i",{className:"ri-close-line"})})]},t))})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700",children:[(0,a.jsx)("button",{type:"button",onClick:e,className:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:"Cancel"}),(0,a.jsx)("button",{type:"submit",disabled:p,className:"px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-300",children:p?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"ri-loader-4-line animate-spin mr-2"}),"Submitting..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"ri-shield-keyhole-line mr-2"}),"Submit Report"]})})]})]})]})]})})},v=()=>{let{isAuthenticated:e,loading:t}=(0,o.A)(),r=(0,i.useRouter)(),[d,c]=(0,s.useState)([]),[m,g]=(0,s.useState)(!0),[p,h]=(0,s.useState)(""),[b,v]=(0,s.useState)("overview"),[j,k]=(0,s.useState)(!1),[w,N]=(0,s.useState)(!1);(0,s.useEffect)(()=>{t||e||r.push("/customer/auth/login")},[e,t,r]);let C=async()=>{if(e)try{g(!0),h("");let[e,t]=await Promise.all([u.Ck.getComplaints({limit:100}),y.nU.getReports({limit:100})]);if(!e.success)throw Error(`Consumer Affairs API error: ${e.message}`);if(!t.success)throw Error(`Data Breach API error: ${t.message}`);let r=Array.isArray(e.data)?e.data:[],a=Array.isArray(t.data)?t.data:[],s=[...r.map(e=>({id:e.complaint_id,title:e.title,description:e.description,category:e.category,type:"consumer_affairs",priority:e.priority,status:e.status,submittedAt:e.created_at,updatedAt:e.updated_at,assignedTo:e.assignee?.first_name&&e.assignee?.last_name?`${e.assignee.first_name} ${e.assignee.last_name}`:void 0,resolution:e.resolution,number:e.complaint_number})),...a.map(e=>({id:e.report_id,title:e.title,description:e.description,category:e.category,type:"data_breach",priority:e.priority,status:e.status,submittedAt:e.created_at,updatedAt:e.updated_at,assignedTo:e.assignee?.first_name&&e.assignee?.last_name?`${e.assignee.first_name} ${e.assignee.last_name}`:void 0,resolution:e.resolution,number:e.report_number}))];s.sort((e,t)=>new Date(t.submittedAt).getTime()-new Date(e.submittedAt).getTime()),c(s)}catch(e){e.response?.status===401?h("Authentication required. Please log in again."):e.response?.status===404?h("API endpoints not found. Please check if the backend is running."):h(`Failed to load complaints: ${e.message||"Unknown error"}`)}finally{g(!1)}};(0,s.useEffect)(()=>{C()},[e]);let A=e=>{switch(e){case"submitted":return"bg-blue-100 text-blue-800";case"under_review":return"bg-yellow-100 text-yellow-800";case"investigating":return"bg-orange-100 text-orange-800";case"resolved":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}},D=e=>{switch(e){case"low":default:return"bg-gray-100 text-gray-800";case"medium":return"bg-blue-100 text-blue-800";case"high":return"bg-orange-100 text-orange-800";case"urgent":return"bg-red-100 text-red-800"}},S=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),_=d.filter(e=>"consumer_affairs"===e.type),$=d.filter(e=>"data_breach"===e.type);return t||m?(0,a.jsx)(l.A,{children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,a.jsx)(n.A,{message:"Loading Data Protection..."})})}):p?(0,a.jsx)(l.A,{children:(0,a.jsxs)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:[(0,a.jsx)("p",{children:p}),(0,a.jsx)("button",{type:"button",onClick:()=>window.location.reload(),className:"mt-2 text-sm underline hover:no-underline",children:"Try again"})]})}):(0,a.jsx)(l.A,{children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2",children:"Data Protection"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Submit and track consumer affairs complaints and data breach reports"})]}),(0,a.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-700 mb-6",children:(0,a.jsx)("nav",{className:"-mb-px flex space-x-8",children:[{key:"overview",label:"Overview",icon:"ri-dashboard-line"},{key:"track",label:"Track Complaints",icon:"ri-search-eye-line",count:d.length}].map(e=>(0,a.jsxs)("button",{type:"button",onClick:()=>v(e.key),className:`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center ${b===e.key?"border-primary text-primary":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"}`,children:[(0,a.jsx)("i",{className:`${e.icon} mr-2`}),e.label,void 0!==e.count&&(0,a.jsx)("span",{className:"ml-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 py-0.5 px-2 rounded-full text-xs",children:e.count})]},e.key))})}),"overview"===b&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-shield-user-line text-2xl text-blue-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Consumer Affairs"}),(0,a.jsx)("p",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:_.length})]})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-shield-keyhole-line text-2xl text-red-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Data Breaches"}),(0,a.jsx)("p",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:$.length})]})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-file-list-3-line text-2xl text-green-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Total Complaints"}),(0,a.jsx)("p",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:d.length})]})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-shield-user-line text-3xl text-blue-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Consumer Affairs"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Report issues with telecommunications services, billing, or customer service"})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:[(0,a.jsx)("p",{children:"• Billing disputes and overcharges"}),(0,a.jsx)("p",{children:"• Service quality issues"}),(0,a.jsx)("p",{children:"• Network connectivity problems"}),(0,a.jsx)("p",{children:"• Customer service complaints"})]}),(0,a.jsxs)("button",{type:"button",onClick:()=>k(!0),className:"w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-300",children:[(0,a.jsx)("i",{className:"ri-file-add-line mr-2"}),"Lodge Consumer Affairs Complaint"]})]})]}),(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-shield-keyhole-line text-3xl text-red-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Data Breach Report"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Report unauthorized access, misuse, or breach of your personal data"})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:[(0,a.jsx)("p",{children:"• Unauthorized data access"}),(0,a.jsx)("p",{children:"• Data misuse or sharing"}),(0,a.jsx)("p",{children:"• Privacy violations"}),(0,a.jsx)("p",{children:"• Identity theft concerns"})]}),(0,a.jsxs)("button",{type:"button",onClick:()=>N(!0),className:"w-full bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors duration-300",children:[(0,a.jsx)("i",{className:"ri-shield-keyhole-line mr-2"}),"Report Data Breach"]})]})]})]})]}),"track"===b&&(0,a.jsx)("div",{children:0===d.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("i",{className:"ri-file-search-line text-4xl text-gray-400 mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"No complaints found"}),(0,a.jsx)("p",{className:"text-gray-500 dark:text-gray-400 mb-4",children:"You haven't submitted any complaints yet."}),(0,a.jsx)("button",{type:"button",onClick:()=>v("overview"),className:"px-4 py-2 bg-primary text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors duration-300",children:"Submit Your First Complaint"})]}):(0,a.jsx)("div",{className:"space-y-6",children:d.map(e=>(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center mb-2",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mr-3",children:e.title}),(0,a.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${A(e.status)}`,children:e.status.replace("_"," ").toUpperCase()}),(0,a.jsx)("span",{className:`ml-2 px-2 py-1 rounded-full text-xs font-medium ${D(e.priority)}`,children:e.priority.toUpperCase()}),(0,a.jsx)("span",{className:`ml-2 px-2 py-1 rounded-full text-xs font-medium ${"consumer_affairs"===e.type?"bg-blue-100 text-blue-800":"bg-red-100 text-red-800"}`,children:"consumer_affairs"===e.type?"CONSUMER AFFAIRS":"DATA BREACH"})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-2",children:["ID: ",e.id," | Category: ",e.category]}),(0,a.jsx)("p",{className:"text-sm text-gray-700 dark:text-gray-300 mb-3",children:e.description.length>150?`${e.description.substring(0,150)}...`:e.description}),(0,a.jsxs)("div",{className:"flex items-center text-xs text-gray-500 dark:text-gray-400",children:[(0,a.jsxs)("span",{children:["Submitted: ",S(e.submittedAt)]}),(0,a.jsx)("span",{className:"mx-2",children:"•"}),(0,a.jsxs)("span",{children:["Updated: ",S(e.updatedAt)]}),e.assignedTo&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{className:"mx-2",children:"•"}),(0,a.jsxs)("span",{children:["Assigned to: ",e.assignedTo]})]})]})]}),(0,a.jsx)("button",{type:"button",className:"ml-4 px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600",children:"View Details"})]})},e.id))})}),j&&(0,a.jsx)(x,{onClose:()=>k(!1),onSubmit:e=>{k(!1),C()}}),w&&(0,a.jsx)(f,{onClose:()=>N(!1),onSubmit:e=>{N(!1),C()}})]})})}},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86732:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(60687);let s=(0,r(43210).forwardRef)(({label:e,error:t,helperText:r,variant:s="default",fullWidth:i=!0,className:l="",required:n,disabled:o,options:d,children:c,...m},u)=>{let g=`px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors duration-200 ${i?"w-full":""} ${"small"===s?"py-1.5 text-sm":"py-2"}`,p=`${g} ${t?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"} ${o?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""} ${l}`,x=`block font-medium text-gray-700 dark:text-gray-300 mb-2 ${"small"===s?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"}`;return(0,a.jsxs)("div",{className:"w-full",children:[e&&(0,a.jsxs)("label",{className:x,children:[e,n&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,a.jsx)("select",{ref:u,className:p,disabled:o,required:n,...m,children:d?d.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value)):c}),t&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:t}),r&&!t&&(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:r})]})});s.displayName="Select";let i=s},89048:(e,t,r)=>{Promise.resolve().then(r.bind(r,11112))},91961:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var a=r(65239),s=r(48088),i=r(88170),l=r.n(i),n=r(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(t,o);let d={children:["",{children:["customer",{children:["data-protection",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,11112)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\data-protection\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,68736)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\data-protection\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/customer/data-protection/page",pathname:"/customer/data-protection",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,7498,1658,5814,7563,6893],()=>r(91961));module.exports=a})();