'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../contexts/AuthContext';
import { Application, ApplicationStatus } from '../../types/license';
import { applicationService } from '../../services/applicationService';
import { licenseCategoryService, LicenseCategory } from '../../services/licenseCategoryService';
import { licenseTypeService, LicenseType } from '../../services/licenseTypeService';

interface LicenseManagementTableProps {
  licenseTypeId?: string;
  licenseTypeFilter?: string; // Filter by license type name
  title: string;
  description: string;
  searchPlaceholder: string;
  emptyStateIcon: string;
  emptyStateMessage: string;
  departmentType?: string; // Department type for navigation
}

// Application View Modal Component
interface ApplicationViewModalProps {
  isOpen: boolean;
  onClose: () => void;
  application: Application | null;
  onEvaluate?: (application: Application) => void;
  onStatusUpdate?: (applicationId: string, newStatus: ApplicationStatus) => void;
  canEvaluate?: (application: Application) => boolean;
}

const ApplicationViewModal: React.FC<ApplicationViewModalProps> = ({
  isOpen,
  onClose,
  application,
  onEvaluate,
  onStatusUpdate,
  canEvaluate
}) => {
  if (!isOpen || !application) return null;

  const steps = [
    { id: 1, name: 'Draft', description: 'Application incomplete', icon: 'ri-file-text-line' },
    { id: 2, name: 'Submitted', description: 'Application received and logged', icon: 'ri-file-text-line' },
    { id: 3, name: 'Under Review', description: 'Being reviewed by MACRA team', icon: 'ri-search-line' },
    { id: 4, name: 'Evaluation', description: 'Technical evaluation in progress', icon: 'ri-clipboard-line' },
    { id: 5, name: 'Approved', description: 'License approved and issued', icon: 'ri-check-line' }
  ];

  const getStepStatus = (stepId: number) => {
    const currentStep = application.current_step;
    if (application.status === ApplicationStatus.REJECTED) return stepId === 1 ? 'complete' : 'error';
    if (application.status === ApplicationStatus.APPROVED) return 'complete';
    return stepId <= currentStep ? (stepId === currentStep ? 'current' : 'complete') : 'upcoming';
  };

  const canEvaluateApplication = () => {
    return canEvaluate ? canEvaluate(application) : 
           application.status === ApplicationStatus.SUBMITTED || 
           application.status === ApplicationStatus.UNDER_REVIEW;
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={onClose}
        ></div>

        {/* Modal panel */}
        <div className="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
          <div className="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            {/* Modal header */}
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                Application Details
              </h3>
              <button
                type="button"
                title="Close modal"
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <i className="ri-close-line text-xl"></i>
              </button>
            </div>

            {/* Application info */}
            <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">
                    {application.application_number}
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {application.license_category?.name || 'License Category'}
                  </p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">
                    {application.applicant?.name || 'N/A'}
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    BRN: {application.applicant?.business_registration_number || 'N/A'}
                  </p>
                </div>
              </div>
            </div>

            {/* Status tracker */}
            <div className="relative mb-6">
              {/* Progress line */}
              <div className="absolute top-6 left-6 right-6 h-0.5 bg-gray-200 dark:bg-gray-600">
                <div
                  className={`h-full bg-primary transition-all duration-500 ${
                    application.status === ApplicationStatus.APPROVED ? 'w-full' :
                    application.current_step === 4 ? 'w-full' :
                    application.current_step === 3 ? 'w-2/3' :
                    application.current_step === 2 ? 'w-1/3' :
                    application.current_step === 1 ? 'w-0' : 'w-0'
                  }`}
                />
              </div>

              {/* Steps */}
              <div className="relative flex justify-between">
                {steps.map((step) => {
                  const stepStatus = getStepStatus(step.id);
                  return (
                    <div key={step.id} className="flex flex-col items-center">
                      <div className={`
                        w-12 h-12 rounded-full flex items-center justify-center text-sm font-medium border-2 transition-all duration-300
                        ${stepStatus === 'complete' ? 'bg-primary border-primary text-white' :
                          stepStatus === 'current' ? 'bg-primary border-primary text-white animate-pulse' :
                          stepStatus === 'error' ? 'bg-red-500 border-red-500 text-white' :
                          'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-400'}
                      `}>
                        <i className={step.icon}></i>
                      </div>
                      <div className="mt-3 text-center">
                        <div className={`text-sm font-medium ${
                          stepStatus === 'complete' || stepStatus === 'current' ? 'text-gray-900 dark:text-gray-100' : 'text-gray-500 dark:text-gray-400'
                        }`}>
                          {step.name}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1 max-w-20">
                          {step.description}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Application details */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-gray-500 dark:text-gray-400">Current Status</p>
                  <p className="font-medium text-gray-900 dark:text-gray-100">
                    {application.status.replace('_', ' ').toUpperCase()}
                  </p>
                </div>
                <div>
                  <p className="text-gray-500 dark:text-gray-400">Progress</p>
                  <p className="font-medium text-gray-900 dark:text-gray-100">
                    {application.progress_percentage || 0}% Complete
                  </p>
                </div>
                <div>
                  <p className="text-gray-500 dark:text-gray-400">Submitted</p>
                  <p className="font-medium text-gray-900 dark:text-gray-100">
                    {application.submitted_at
                      ? new Date(application.submitted_at).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })
                      : new Date(application.created_at).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })
                    }
                  </p>
                </div>
                <div>
                  <p className="text-gray-500 dark:text-gray-400">License Type</p>
                  <p className="font-medium text-gray-900 dark:text-gray-100">
                    {application.license_category?.license_type?.name || 'N/A'}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Modal footer */}
          <div className="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="button"
              onClick={onClose}
              className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-gray-600 text-base font-medium text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Close
            </button>

            {/* Evaluate Button */}
            {canEvaluateApplication() && onEvaluate && (
              <button
                type="button"
                onClick={() => {
                  onEvaluate(application);
                  onClose();
                }}
                className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:mr-3 sm:w-auto sm:text-sm"
              >
                <i className="ri-clipboard-line mr-2"></i>
                Evaluate
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default function LicenseManagementTable({
  licenseTypeId,
  licenseTypeFilter,
  title,
  description,
  searchPlaceholder,
  emptyStateIcon,
  emptyStateMessage,
  departmentType,
}: LicenseManagementTableProps) {
  const { user } = useAuth();
  const router = useRouter();
  const [applications, setApplications] = useState<Application[]>([]);
  const [licenseCategories, setLicenseCategories] = useState<LicenseCategory[]>([]);
  const [licenseTypes, setLicenseTypes] = useState<LicenseType[]>([]);
  const [resolvedLicenseTypeId, setResolvedLicenseTypeId] = useState<string | undefined>(undefined);
  const [loading, setLoading] = useState(true);
  const [loadingMessage, setLoadingMessage] = useState('Loading applications...');
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');

  const [selectedLicenseCategory, setSelectedLicenseCategory] = useState('');
  const [statusFilter, setStatusFilter] = useState<ApplicationStatus | ''>('');
  const [dateRangeFilter, setDateRangeFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [itemsPerPage] = useState(10);
  
  // Modal state
  const [selectedApplication, setSelectedApplication] = useState<Application | null>(null);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);

  const isAdmin = user?.isAdmin;

  // Navigation function for viewing applications
  const handleViewApplication = (applicationId: string) => {
    if (departmentType) {
      router.push(`/applications/${departmentType}/view/${applicationId}`);
    } else {
      // Fallback to a generic view if departmentType is not provided
      router.push(`/applications/view/${applicationId}`);
    }
  };

  // Modal functions
  const openApplicationModal = (application: Application) => {
    setSelectedApplication(application);
    setIsModalOpen(true);
  };

  const closeApplicationModal = () => {
    setSelectedApplication(null);
    setIsModalOpen(false);
  };

  // Function to handle evaluation action
  const handleEvaluateApplication = (application: Application) => {
    if (departmentType) {
      router.push(`/applications/${departmentType}/view/${application.application_id}`);
    } else {
      // Fallback to a generic view if departmentType is not provided
      router.push(`/applications/view/${application.application_id}`);
    }
  };

  // Check if application can be evaluated
  const canEvaluateApplication = (application: Application) => {
    return application.status === ApplicationStatus.SUBMITTED || 
           application.status === ApplicationStatus.UNDER_REVIEW;
  };

  // Debounce search term to reduce API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 1200); // Increased to 1200ms delay to reduce API calls

    return () => clearTimeout(timer);
  }, [searchTerm]);



  // Fetch license types and resolve license type ID from filter name
  useEffect(() => {
    const fetchLicenseTypes = async () => {
      try {
        // Add delay to space out API calls
        await new Promise(resolve => setTimeout(resolve, 200));
        const response = await licenseTypeService.getAllLicenseTypes();
        const types = Array.isArray(response) ? response : (response?.data || []);

        // Ensure types is always an array
        if (!Array.isArray(types)) {
          console.warn('License types response is not an array:', types);
          setLicenseTypes([]);
          return;
        }

        setLicenseTypes(types);

        // If we have a licenseTypeFilter, find the matching license type ID
        if (licenseTypeFilter && types.length > 0) {
          const matchingType = types.find(type =>
            type.name.toLowerCase().includes(licenseTypeFilter.toLowerCase())
          );
          if (matchingType) {
            setResolvedLicenseTypeId(matchingType.license_type_id);
          }
        }
      } catch (error) {
        console.error('Error fetching license types:', error);
        setLicenseTypes([]);
      }
    };

    fetchLicenseTypes();
  }, [licenseTypeFilter]);

  // Fetch license categories for the dropdown
  useEffect(() => {
    const fetchLicenseCategories = async () => {
      try {
        // Add delay to space out API calls
        await new Promise(resolve => setTimeout(resolve, 400));
        let categories: LicenseCategory[] = [];

        const effectiveLicenseTypeId = licenseTypeId || resolvedLicenseTypeId;

        if (effectiveLicenseTypeId) {
          // Fetch categories for specific license type
          const response = await licenseCategoryService.getLicenseCategoriesByType(effectiveLicenseTypeId);
          categories = Array.isArray(response) ? response : (response?.data || []);
        } else {
          // Fetch all categories
          const response = await licenseCategoryService.getAllLicenseCategories();
          categories = Array.isArray(response) ? response : (response?.data || []);
        }

        // Ensure categories is always an array
        if (!Array.isArray(categories)) {
          console.warn('License categories response is not an array:', categories);
          categories = [];
        }

        setLicenseCategories(categories);
      } catch (error) {
        console.error('Error fetching license categories:', error);
        setLicenseCategories([]);
      }
    };

    fetchLicenseCategories();
  }, [licenseTypeId, resolvedLicenseTypeId]);

  // Fetch applications
  useEffect(() => {
    const fetchApplications = async () => {
      setLoading(true);
      setLoadingMessage('Loading applications...');

      // Add a minimum loading time to give impression of thorough processing
      const minLoadingTime = 800; // 800ms minimum
      const startTime = Date.now();

      try {
        // Add delay to space out API calls
        await new Promise(resolve => setTimeout(resolve, 600));
        const effectiveLicenseTypeId = licenseTypeId || resolvedLicenseTypeId;

        const params = {
          page: currentPage,
          limit: itemsPerPage,
          search: debouncedSearchTerm || undefined,
          filters: {
            licenseCategoryId: selectedLicenseCategory || undefined,
            licenseTypeId: effectiveLicenseTypeId || undefined,
            status: statusFilter || undefined,
          },
        };

        const response = await applicationService.getApplications(params);

        // Ensure applications data is always an array
        const applications = Array.isArray(response?.data) ? response.data : [];
        setApplications(applications);

        // Set pagination data with fallbacks
        setTotalPages(response?.meta?.totalPages || 1);
        setTotalItems(response?.meta?.totalItems || 0);
      } catch (error: any) {
        console.error('Error fetching applications:', error);
        setApplications([]);
      } finally {
        // Ensure minimum loading time for better UX
        const elapsedTime = Date.now() - startTime;
        const remainingTime = Math.max(0, minLoadingTime - elapsedTime);

        if (remainingTime > 0) {
          setLoadingMessage('Finalizing results...');
          await new Promise(resolve => setTimeout(resolve, remainingTime));
        }

        setLoading(false);
        setLoadingMessage('Loading applications...');
      }
    };

    fetchApplications();
  }, [currentPage, debouncedSearchTerm, selectedLicenseCategory, statusFilter, licenseTypeId, resolvedLicenseTypeId]);

  const getStatusBadge = (status: ApplicationStatus) => {
    const statusClasses: Record<ApplicationStatus, string> = {
      [ApplicationStatus.DRAFT]: 'bg-gray-100 text-gray-800',
      [ApplicationStatus.SUBMITTED]: 'bg-blue-100 text-blue-800',
      [ApplicationStatus.UNDER_REVIEW]: 'bg-yellow-100 text-yellow-800',
      [ApplicationStatus.EVALUATION]: 'bg-purple-100 text-purple-800',
      [ApplicationStatus.APPROVED]: 'bg-green-100 text-green-800',
      [ApplicationStatus.REJECTED]: 'bg-red-100 text-red-800',
      [ApplicationStatus.WITHDRAWN]: 'bg-gray-100 text-gray-800',
    };

    const statusLabels: Record<ApplicationStatus, string> = {
      [ApplicationStatus.DRAFT]: 'Draft',
      [ApplicationStatus.SUBMITTED]: 'Submitted',
      [ApplicationStatus.UNDER_REVIEW]: 'Under Review',
      [ApplicationStatus.EVALUATION]: 'Evaluation',
      [ApplicationStatus.APPROVED]: 'Approved',
      [ApplicationStatus.REJECTED]: 'Rejected',
      [ApplicationStatus.WITHDRAWN]: 'Withdrawn',
    };

    return (
      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClasses[status]}`}>
        {statusLabels[status]}
      </span>
    );
  };

  const getProgressBar = (percentage: number) => {
    const getProgressColor = (percent: number) => {
      if (percent >= 100) return 'bg-green-600';
      if (percent >= 75) return 'bg-blue-600';
      if (percent >= 50) return 'bg-yellow-600';
      if (percent >= 25) return 'bg-orange-600';
      return 'bg-red-600';
    };

    return (
      <div className="flex items-center">
        <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
          <div 
            className={`h-2 rounded-full ${getProgressColor(percentage)}`} 
            style={{ width: `${percentage}%` }}
          ></div>
        </div>
        <span className="text-sm text-gray-500">{percentage}%</span>
      </div>
    );
  };

  const handleStatusUpdate = async (applicationId: string, newStatus: ApplicationStatus) => {
    try {
      await applicationService.updateApplicationStatus(applicationId, newStatus);
      // Refresh the applications list
      const params = {
        page: currentPage,
        limit: itemsPerPage,
        search: searchTerm || undefined,
        filters: {
          licenseCategoryId: selectedLicenseCategory || undefined,
          licenseTypeId: licenseTypeId || undefined,
          status: statusFilter || undefined,
        },
      };
      const response = await applicationService.getApplications(params);
      setApplications(response.data);
    } catch (error) {
      console.error('Error updating application status:', error);
    }
  };

  const applyFilters = () => {
    setCurrentPage(1); // Reset to first page when applying filters
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 dark:border-red-500 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400 text-sm">{loadingMessage}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Page header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">{title}</h1>
              <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                {description}
              </p>
            </div>
          </div>
        </div>

        {/* Filters Section - Matching Audit Trail Style */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">Filters</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* License Category Filter */}
            <div>
              <label htmlFor="license-category" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                License Category
              </label>
              <select
                id="license-category"
                name="license-category"
                value={selectedLicenseCategory}
                onChange={(e) => setSelectedLicenseCategory(e.target.value)}
                className="appearance-none block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-gray-200 sm:text-sm"
              >
                <option value="">All Categories</option>
                {Array.isArray(licenseCategories) && licenseCategories.map((category) => (
                  <option key={category.license_category_id} value={category.license_category_id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Status Filter */}
            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Application Status
              </label>
              <select
                id="status"
                name="status"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as ApplicationStatus | '')}
                className="appearance-none block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-gray-200 sm:text-sm"
              >
                <option value="">All Statuses</option>
                <option value={ApplicationStatus.DRAFT}>Draft</option>
                <option value={ApplicationStatus.SUBMITTED}>Submitted</option>
                <option value={ApplicationStatus.UNDER_REVIEW}>Under Review</option>
                <option value={ApplicationStatus.EVALUATION}>Evaluation</option>
                <option value={ApplicationStatus.APPROVED}>Approved</option>
                <option value={ApplicationStatus.REJECTED}>Rejected</option>
                <option value={ApplicationStatus.WITHDRAWN}>Withdrawn</option>
              </select>
            </div>

            {/* Date Range Filter */}
            <div>
              <label htmlFor="date-range" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Date Range
              </label>
              <select
                id="date-range"
                name="date-range"
                value={dateRangeFilter}
                onChange={(e) => setDateRangeFilter(e.target.value)}
                className="appearance-none block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-gray-200 sm:text-sm"
              >
                <option value="">All Time</option>
                <option value="last-30">Last 30 Days</option>
                <option value="last-90">Last 90 Days</option>
                <option value="last-year">Last Year</option>
              </select>
            </div>

            {/* Search Filter */}
            <div>
              <label htmlFor="search-filter" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Search Applications
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <i className="ri-search-line text-gray-400 dark:text-gray-500"></i>
                </div>
                <input
                  id="search-filter"
                  name="search-filter"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="appearance-none block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-gray-200 sm:text-sm"
                  placeholder={searchPlaceholder}
                  type="search"
                />
              </div>
            </div>
          </div>

          {/* Filter Actions */}
          <div className="mt-4 flex items-center justify-between">
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {applications.length > 0 && (
                <span>
                  Showing {applications.length} of {totalItems} applications
                </span>
              )}
            </div>
            <div className="flex space-x-3">
              <button
                type="button"
                onClick={() => {
                  setSelectedLicenseCategory('');
                  setStatusFilter('');
                  setDateRangeFilter('');
                  setSearchTerm('');
                  setCurrentPage(1);
                }}
                className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                <i className="ri-refresh-line mr-2"></i>
                Clear Filters
              </button>
              <button
                type="button"
                onClick={applyFilters}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                <i className="ri-filter-line mr-2"></i>
                Apply Filters
              </button>
            </div>
          </div>
        </div>



        {/* Applications Table - Matching Audit Trail Style */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Applications</h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Manage and track license applications for {title.toLowerCase()}
            </p>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-900">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Application Number
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Applicant
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    License Category
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Progress
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Submitted Date
                  </th>
                  <th scope="col" className="relative px-6 py-3">
                    <span className="sr-only">Actions</span>
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {applications.map((application) => (
                  <tr key={application.application_id} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {application.application_number}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                            <i className="ri-building-line text-blue-600 dark:text-blue-400"></i>
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {application.applicant?.name || 'N/A'}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            BRN: {application.applicant?.business_registration_number || 'N/A'} |
                            TPIN: {application.applicant?.tpin || 'N/A'}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-gray-100">
                        {application.license_category?.name || 'N/A'}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {application.license_category?.license_type?.name || 'N/A'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(application.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getProgressBar(application.progress_percentage)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {application.submitted_at
                        ? new Date(application.submitted_at).toLocaleDateString()
                        : new Date(application.created_at).toLocaleDateString()
                      }
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        <button
                          type="button"
                          onClick={() => openApplicationModal(application)}
                          className="inline-flex items-center px-3 py-1 text-xs font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/40 transition-colors"
                        >
                          <i className="ri-eye-line mr-1"></i>
                          View
                        </button>
                        {isAdmin && (
                          <>
                            {application.status === ApplicationStatus.SUBMITTED && (
                              <button
                                type="button"
                                onClick={() => handleStatusUpdate(application.application_id, ApplicationStatus.UNDER_REVIEW)}
                                className="inline-flex items-center px-3 py-1 text-xs font-medium text-yellow-600 dark:text-yellow-400 hover:text-yellow-800 dark:hover:text-yellow-300 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md hover:bg-yellow-100 dark:hover:bg-yellow-900/40 transition-colors"
                              >
                                <i className="ri-search-line mr-1"></i>
                                Review
                              </button>
                            )}
                            {application.status === ApplicationStatus.UNDER_REVIEW && (
                              <button
                                type="button"
                                onClick={() => handleStatusUpdate(application.application_id, ApplicationStatus.EVALUATION)}
                                className="inline-flex items-center px-3 py-1 text-xs font-medium text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-md hover:bg-purple-100 dark:hover:bg-purple-900/40 transition-colors"
                              >
                                <i className="ri-clipboard-line mr-1"></i>
                                Evaluate
                              </button>
                            )}
                            {application.status === ApplicationStatus.EVALUATION && (
                              <>
                                <button
                                  type="button"
                                  onClick={() => handleStatusUpdate(application.application_id, ApplicationStatus.APPROVED)}
                                  className="inline-flex items-center px-3 py-1 text-xs font-medium text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md hover:bg-green-100 dark:hover:bg-green-900/40 transition-colors"
                                >
                                  <i className="ri-check-line mr-1"></i>
                                  Approve
                                </button>
                                <button
                                  type="button"
                                  onClick={() => handleStatusUpdate(application.application_id, ApplicationStatus.REJECTED)}
                                  className="inline-flex items-center px-3 py-1 text-xs font-medium text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md hover:bg-red-100 dark:hover:bg-red-900/40 transition-colors"
                                >
                                  <i className="ri-close-line mr-1"></i>
                                  Reject
                                </button>
                              </>
                            )}
                            <button
                              type="button"
                              className="inline-flex items-center px-3 py-1 text-xs font-medium text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300 bg-gray-50 dark:bg-gray-900/20 border border-gray-200 dark:border-gray-800 rounded-md hover:bg-gray-100 dark:hover:bg-gray-900/40 transition-colors"
                            >
                              <i className="ri-file-text-line mr-1"></i>
                            </button>
                          </>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Empty State */}
          {applications.length === 0 && !loading && (
            <div className="text-center py-12">
              <i className={`${emptyStateIcon} text-4xl text-gray-400 dark:text-gray-500 mb-4`}></i>
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No Applications Found</h3>
              <p className="text-gray-500 dark:text-gray-400">{emptyStateMessage}</p>
            </div>
          )}

          {/* Pagination - Matching Audit Trail Style */}
          {applications.length > 0 && (
            <div className="bg-white dark:bg-gray-800 px-6 py-4 flex items-center justify-between border-t border-gray-200 dark:border-gray-700">
              <div className="flex-1 flex justify-between sm:hidden">
                <button
                  onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <i className="ri-arrow-left-line mr-2"></i>
                  Previous
                </button>
                <button
                  onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                  <i className="ri-arrow-right-line ml-2"></i>
                </button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700 dark:text-gray-300">
                    Showing <span className="font-medium">{((currentPage - 1) * itemsPerPage) + 1}</span> to{' '}
                    <span className="font-medium">{Math.min(currentPage * itemsPerPage, totalItems)}</span> of{' '}
                    <span className="font-medium">{totalItems}</span> applications
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <button
                      onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                      className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <span className="sr-only">Previous</span>
                      <i className="ri-arrow-left-s-line"></i>
                    </button>

                    {/* Page numbers */}
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      const pageNumber = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i;
                      if (pageNumber > totalPages) return null;

                      return (
                        <button
                          key={pageNumber}
                          onClick={() => handlePageChange(pageNumber)}
                          className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                            pageNumber === currentPage
                              ? 'z-10 bg-red-50 dark:bg-red-900/20 border-red-500 text-red-600 dark:text-red-400'
                              : 'bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600'
                          }`}
                        >
                          {pageNumber}
                        </button>
                      );
                    })}

                    <button
                      onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                      className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <span className="sr-only">Next</span>
                      <i className="ri-arrow-right-s-line"></i>
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Empty state */}
        {applications.length === 0 && !loading && (
          <div className="text-center py-12 bg-white dark:bg-gray-800 rounded-lg shadow">
            <i className={`${emptyStateIcon} text-4xl text-gray-400 dark:text-gray-500 mb-4`}></i>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No applications found</h3>
            <p className="text-gray-500 dark:text-gray-400">
              {searchTerm || statusFilter !== '' || selectedLicenseCategory !== ''
                ? 'Try adjusting your search or filter criteria.'
                : emptyStateMessage}
            </p>
          </div>
        )}
      </div>
      
      {/* Application View Modal */}
      <ApplicationViewModal
        isOpen={isModalOpen}
        onClose={closeApplicationModal}
        application={selectedApplication}
        onEvaluate={handleEvaluateApplication}
        onStatusUpdate={handleStatusUpdate}
        canEvaluate={canEvaluateApplication}
      />
    </div>
  );
}
