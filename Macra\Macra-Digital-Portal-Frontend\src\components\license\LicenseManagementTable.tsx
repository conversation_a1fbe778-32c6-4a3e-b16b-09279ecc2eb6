'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../contexts/AuthContext';
import { Application, ApplicationStatus } from '../../types/license';
import { applicationService } from '../../services/applicationService';
import { licenseCategoryService, LicenseCategory } from '../../services/licenseCategoryService';
import { licenseTypeService, LicenseType } from '../../services/licenseTypeService';
import AssignButton from '../common/AssignButton';
import ApplicationViewModal from './ApplicationViewModal';
import { PaginateQuery } from '../../services/userService';
import DataTable from '../common/DataTable';
import Select from '../common/Select';

interface LicenseManagementTableProps {
  licenseTypeId?: string;
  licenseTypeCode?: string; // Primary filter by license type code
  licenseTypeFilter?: string; // Filter by license type name (fallback)
  title: string;
  description: string;
  searchPlaceholder: string;
  emptyStateIcon: string;
  emptyStateMessage: string;
  departmentType?: string; // Department type for navigation
}

export default function LicenseManagementTable({
  licenseTypeId,
  licenseTypeCode,
  licenseTypeFilter,
  title,
  description,
  searchPlaceholder,
  emptyStateIcon,
  emptyStateMessage,
  departmentType,
}: LicenseManagementTableProps) {
  const { user } = useAuth();
  const router = useRouter();
  const [applicationsData, setApplicationsData] = useState<any>(null);
  const [licenseCategories, setLicenseCategories] = useState<LicenseCategory[]>([]);
  const [licenseTypes, setLicenseTypes] = useState<LicenseType[]>([]);
  const [resolvedLicenseTypeId, setResolvedLicenseTypeId] = useState<string | undefined>(undefined);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [selectedLicenseCategory, setSelectedLicenseCategory] = useState('');
  const [statusFilter, setStatusFilter] = useState<ApplicationStatus | ''>('');
  const [dateRangeFilter, setDateRangeFilter] = useState('');

  // View modal state
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedApplicationId, setSelectedApplicationId] = useState<string | null>(null);

  const isAdmin = user?.isAdmin;

  // Function for viewing applications - opens modal
  const handleViewApplication = (applicationId: string) => {
    setSelectedApplicationId(applicationId);
    setShowViewModal(true);
  };

  const handleCloseViewModal = () => {
    setShowViewModal(false);
    setSelectedApplicationId(null);
  };

  const handleViewModalUpdate = () => {
    // Refresh the applications list when modal updates data
    loadApplications({ page: 1, limit: 10 });
  };

  // Load applications function for DataTable
  const loadApplications = useCallback(async (query: PaginateQuery) => {
    try {
      setLoading(true);
      setError(null);

      const effectiveLicenseTypeId = licenseTypeId || resolvedLicenseTypeId;

      // Build filters object
      const filters: any = {};

      // Add license category filter if selected
      if (selectedLicenseCategory) {
        filters.licenseCategoryId = selectedLicenseCategory;
      }

      // Add license type filter - prioritize licenseTypeCode search
      if (effectiveLicenseTypeId) {
        filters.licenseTypeId = effectiveLicenseTypeId;
      } else if (licenseTypeCode) {
        // If we have a license type code but no resolved ID yet, add it as a filter
        filters.licenseTypeCode = licenseTypeCode;
      }

      // Add status filter if selected
      if (statusFilter) {
        filters.status = statusFilter;
      }

      const params = {
        page: query.page,
        limit: query.limit,
        search: query.search || undefined,
        filters: Object.keys(filters).length > 0 ? filters : undefined,
      };

      console.log('Loading applications with params:', params);
      const response = await applicationService.getApplications(params);
      setApplicationsData(response);
    } catch (err: any) {
      console.error('Error loading applications:', err);
      setError('Failed to load applications');
      // Set empty data structure to prevent undefined errors
      setApplicationsData({
        data: [],
        meta: {
          itemsPerPage: query.limit || 10,
          totalItems: 0,
          currentPage: query.page || 1,
          totalPages: 0,
          sortBy: [],
          searchBy: [],
          search: '',
          select: [],
        },
        links: {
          current: '',
        },
      });
    } finally {
      setLoading(false);
    }
  }, [licenseTypeId, resolvedLicenseTypeId, selectedLicenseCategory, statusFilter, licenseTypeCode]);



  // Load license types and resolve license type ID from code or filter name
  useEffect(() => {
    const fetchLicenseTypes = async () => {
      try {
        const response = await licenseTypeService.getAllLicenseTypes();
        const types = Array.isArray(response) ? response : (response?.data || []);

        if (!Array.isArray(types)) {
          console.warn('License types response is not an array:', types);
          setLicenseTypes([]);
          return;
        }

        setLicenseTypes(types);

        // Priority 1: If we have a licenseTypeCode, find the matching license type ID
        if (licenseTypeCode && types.length > 0) {
          const matchingType = types.find(type =>
            type.code === licenseTypeCode
          );
          if (matchingType) {
            setResolvedLicenseTypeId(matchingType.license_type_id);
          }
        }
        // Priority 2: If we have a licenseTypeFilter (fallback), find the matching license type ID
        else if (licenseTypeFilter && types.length > 0) {
          const matchingType = types.find(type =>
            type.name.toLowerCase().includes(licenseTypeFilter.toLowerCase())
          );
          if (matchingType) {
            setResolvedLicenseTypeId(matchingType.license_type_id);
          }
        }
      } catch (error) {
        console.error('Error fetching license types:', error);
        setLicenseTypes([]);
      }
    };

    fetchLicenseTypes();
  }, [licenseTypeCode, licenseTypeFilter]);

  // Fetch license categories for the dropdown
  useEffect(() => {
    const fetchLicenseCategories = async () => {
      try {
        let categories: LicenseCategory[] = [];
        const effectiveLicenseTypeId = licenseTypeId || resolvedLicenseTypeId;

        if (effectiveLicenseTypeId) {
          // Fetch categories for specific license type
          const response = await licenseCategoryService.getLicenseCategoriesByType(effectiveLicenseTypeId);
          categories = Array.isArray(response) ? response : (response?.data || []);
        } else {
          // Fetch all categories
          const response = await licenseCategoryService.getAllLicenseCategories();
          categories = Array.isArray(response) ? response : (response?.data || []);
        }

        if (!Array.isArray(categories)) {
          console.warn('License categories response is not an array:', categories);
          categories = [];
        }

        setLicenseCategories(categories);
      } catch (error) {
        console.error('Error fetching license categories:', error);
        setLicenseCategories([]);
      }
    };

    if (resolvedLicenseTypeId || licenseTypeId) {
      fetchLicenseCategories();
    }
  }, [licenseTypeId, resolvedLicenseTypeId]);

  // Load applications on component mount and when filters change
  useEffect(() => {
    if (resolvedLicenseTypeId || licenseTypeId) {
      loadApplications({ page: 1, limit: 10 });
    }
  }, [loadApplications, resolvedLicenseTypeId, licenseTypeId]);

  const getStatusBadge = (status: ApplicationStatus) => {
    const statusClasses: Record<ApplicationStatus, string> = {
      [ApplicationStatus.DRAFT]: 'bg-gray-100 text-gray-800',
      [ApplicationStatus.SUBMITTED]: 'bg-blue-100 text-blue-800',
      [ApplicationStatus.UNDER_REVIEW]: 'bg-yellow-100 text-yellow-800',
      [ApplicationStatus.EVALUATION]: 'bg-purple-100 text-purple-800',
      [ApplicationStatus.APPROVED]: 'bg-green-100 text-green-800',
      [ApplicationStatus.REJECTED]: 'bg-red-100 text-red-800',
      [ApplicationStatus.WITHDRAWN]: 'bg-gray-100 text-gray-800',
    };

    const statusLabels: Record<ApplicationStatus, string> = {
      [ApplicationStatus.DRAFT]: 'Draft',
      [ApplicationStatus.SUBMITTED]: 'Submitted',
      [ApplicationStatus.UNDER_REVIEW]: 'Under Review',
      [ApplicationStatus.EVALUATION]: 'Evaluation',
      [ApplicationStatus.APPROVED]: 'Approved',
      [ApplicationStatus.REJECTED]: 'Rejected',
      [ApplicationStatus.WITHDRAWN]: 'Withdrawn',
    };

    return (
      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClasses[status]}`}>
        {statusLabels[status]}
      </span>
    );
  };

  // Define columns for applications table
  const applicationColumns = [
    {
      key: 'application_number',
      label: 'Application Number',
      sortable: true,
      render: (value: string) => (
        <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
          {value}
        </div>
      ),
    },
    {
      key: 'applicant',
      label: 'Applicant',
      render: (value: any, application: Application) => (
        <div className="flex items-center">
          <div className="flex-shrink-0 h-10 w-10">
            <div className="h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
              <i className="ri-building-line text-blue-600 dark:text-blue-400"></i>
            </div>
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
              {application.applicant?.name || 'N/A'}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              BRN: {application.applicant?.business_registration_number || 'N/A'} |
              TPIN: {application.applicant?.tpin || 'N/A'}
            </div>
          </div>
        </div>
      ),
    },
    {
      key: 'license_category',
      label: 'License Category',
      render: (value: any, application: Application) => (
        <div>
          <div className="text-sm text-gray-900 dark:text-gray-100">
            {application.license_category?.name || 'N/A'}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {application.license_category?.license_type?.name || 'N/A'}
          </div>
        </div>
      ),
    },
    {
      key: 'status',
      label: 'Status',
      render: (value: ApplicationStatus) => getStatusBadge(value),
    },
    {
      key: 'progress_percentage',
      label: 'Progress',
      render: (value: number) => getProgressBar(value),
    },
    {
      key: 'submitted_at',
      label: 'Submitted Date',
      sortable: true,
      render: (value: string, application: Application) => (
        <span className="text-sm text-gray-500 dark:text-gray-400">
          {application.submitted_at
            ? new Date(application.submitted_at).toLocaleDateString()
            : new Date(application.created_at).toLocaleDateString()
          }
        </span>
      ),
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (value: any, application: Application) => (
        <div className="flex items-center justify-end space-x-2">
          <button
            type="button"
            onClick={() => handleViewApplication(application.application_id)}
            className="inline-flex items-center px-3 py-1 text-xs font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/40 transition-colors"
          >
            <i className="ri-eye-line mr-1"></i>
            View
          </button>
          <AssignButton
            itemId={application.application_id}
            itemType="application"
            itemTitle={application.application_number}
            onAssignSuccess={handleAssignSuccess}
            className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 hover:bg-green-100 dark:hover:bg-green-900/40"
          />
        </div>
      ),
    },
  ];

  const getProgressBar = (percentage: number) => {
    const getProgressColor = (percent: number) => {
      if (percent >= 100) return 'bg-green-600';
      if (percent >= 75) return 'bg-blue-600';
      if (percent >= 50) return 'bg-yellow-600';
      if (percent >= 25) return 'bg-orange-600';
      return 'bg-red-600';
    };

    return (
      <div className="flex items-center">
        <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
          <div 
            className={`h-2 rounded-full ${getProgressColor(percentage)}`} 
            style={{ width: `${percentage}%` }}
          ></div>
        </div>
        <span className="text-sm text-gray-500">{percentage}%</span>
      </div>
    );
  };

  const handleAssignSuccess = () => {
    // Refresh the applications list when assignment is successful
    loadApplications({ page: 1, limit: 10 });
  };

  const handleFilterChange = (filterType: string, value: string) => {
    switch (filterType) {
      case 'licenseCategory':
        setSelectedLicenseCategory(value);
        break;
      case 'status':
        setStatusFilter(value as ApplicationStatus | '');
        break;
      case 'dateRange':
        setDateRangeFilter(value);
        break;
    }
    // Reload applications with new filters
    loadApplications({ page: 1, limit: 10 });
  };

  return (
    <div className="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Page header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">{title}</h1>
              <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                {description}
              </p>
            </div>
          </div>
        </div>

        {/* Filters Section */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">Filters</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* License Category Filter */}
            <Select
              label="License Category"
              value={selectedLicenseCategory}
              onChange={(value) => handleFilterChange('licenseCategory', value)}
              options={[
                { value: '', label: 'All Categories' },
                ...licenseCategories.map(category => ({
                  value: category.license_category_id,
                  label: category.name
                }))
              ]}
            />

            {/* Status Filter */}
            <Select
              label="Application Status"
              value={statusFilter}
              onChange={(value) => handleFilterChange('status', value)}
              options={[
                { value: '', label: 'All Statuses' },
                { value: ApplicationStatus.DRAFT, label: 'Draft' },
                { value: ApplicationStatus.SUBMITTED, label: 'Submitted' },
                { value: ApplicationStatus.UNDER_REVIEW, label: 'Under Review' },
                { value: ApplicationStatus.EVALUATION, label: 'Evaluation' },
                { value: ApplicationStatus.APPROVED, label: 'Approved' },
                { value: ApplicationStatus.REJECTED, label: 'Rejected' },
                { value: ApplicationStatus.WITHDRAWN, label: 'Withdrawn' }
              ]}
            />

            {/* Date Range Filter */}
            <Select
              label="Date Range"
              value={dateRangeFilter}
              onChange={(value) => handleFilterChange('dateRange', value)}
              options={[
                { value: '', label: 'All Time' },
                { value: 'last-30', label: 'Last 30 Days' },
                { value: 'last-90', label: 'Last 90 Days' },
                { value: 'last-year', label: 'Last Year' }
              ]}
            />
          </div>
        </div>



        {/* Applications Table */}
        <DataTable
          columns={applicationColumns}
          data={applicationsData}
          loading={loading}
          onQueryChange={loadApplications}
          searchPlaceholder={searchPlaceholder}
        />

        {/* Application View Modal */}
        <ApplicationViewModal
          isOpen={showViewModal}
          onClose={handleCloseViewModal}
          applicationId={selectedApplicationId}
          departmentType={departmentType}
          licenseTypeCode={licenseTypeCode}
          onUpdate={handleViewModalUpdate}
        />
      </div>
    </div>
  );
}
