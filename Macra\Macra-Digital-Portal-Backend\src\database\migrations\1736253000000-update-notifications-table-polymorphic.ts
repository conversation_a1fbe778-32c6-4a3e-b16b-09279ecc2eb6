import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateNotificationsTablePolymorphic1736253000000 implements MigrationInterface {
  name = 'UpdateNotificationsTablePolymorphic1736253000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if notifications table exists
    const tableExists = await queryRunner.hasTable('notifications');
    
    if (tableExists) {
      console.log('Updating existing notifications table for polymorphic support...');
      
      // Add new columns for polymorphic notifications
      const hasStatus = await queryRunner.hasColumn('notifications', 'status');
      if (!hasStatus) {
        await queryRunner.query(`ALTER TABLE notifications ADD COLUMN status VARCHAR(50) DEFAULT 'pending'`);
        await queryRunner.query(`CREATE INDEX idx_notifications_status ON notifications (status)`);
      }

      const hasRecipientType = await queryRunner.hasColumn('notifications', 'recipient_type');
      if (!hasRecipientType) {
        await queryRunner.query(`ALTER TABLE notifications ADD COLUMN recipient_type VARCHAR(50) NOT NULL DEFAULT 'customer'`);
        await queryRunner.query(`CREATE INDEX idx_notifications_recipient_type ON notifications (recipient_type)`);
      }

      const hasRecipientId = await queryRunner.hasColumn('notifications', 'recipient_id');
      if (!hasRecipientId) {
        // Rename user_id to recipient_id if it exists
        const hasUserId = await queryRunner.hasColumn('notifications', 'user_id');
        if (hasUserId) {
          await queryRunner.query(`ALTER TABLE notifications CHANGE COLUMN user_id recipient_id VARCHAR(36) NOT NULL`);
        } else {
          await queryRunner.query(`ALTER TABLE notifications ADD COLUMN recipient_id VARCHAR(36) NOT NULL`);
        }
        await queryRunner.query(`CREATE INDEX idx_notifications_recipient_id ON notifications (recipient_id)`);
      }

      const hasRecipientEmail = await queryRunner.hasColumn('notifications', 'recipient_email');
      if (!hasRecipientEmail) {
        await queryRunner.query(`ALTER TABLE notifications ADD COLUMN recipient_email VARCHAR(255) NULL`);
        await queryRunner.query(`CREATE INDEX idx_notifications_recipient_email ON notifications (recipient_email)`);
      }

      const hasRecipientPhone = await queryRunner.hasColumn('notifications', 'recipient_phone');
      if (!hasRecipientPhone) {
        await queryRunner.query(`ALTER TABLE notifications ADD COLUMN recipient_phone VARCHAR(255) NULL`);
        await queryRunner.query(`CREATE INDEX idx_notifications_recipient_phone ON notifications (recipient_phone)`);
      }

      const hasSubject = await queryRunner.hasColumn('notifications', 'subject');
      if (!hasSubject) {
        // Rename title to subject if it exists
        const hasTitle = await queryRunner.hasColumn('notifications', 'title');
        if (hasTitle) {
          await queryRunner.query(`ALTER TABLE notifications CHANGE COLUMN title subject VARCHAR(255) NOT NULL`);
        } else {
          await queryRunner.query(`ALTER TABLE notifications ADD COLUMN subject VARCHAR(255) NOT NULL DEFAULT 'Notification'`);
        }
      }

      const hasHtmlContent = await queryRunner.hasColumn('notifications', 'html_content');
      if (!hasHtmlContent) {
        await queryRunner.query(`ALTER TABLE notifications ADD COLUMN html_content TEXT NULL`);
      }

      const hasEntityType = await queryRunner.hasColumn('notifications', 'entity_type');
      if (!hasEntityType) {
        // Rename related_entity_type to entity_type if it exists
        const hasRelatedEntityType = await queryRunner.hasColumn('notifications', 'related_entity_type');
        if (hasRelatedEntityType) {
          await queryRunner.query(`ALTER TABLE notifications CHANGE COLUMN related_entity_type entity_type VARCHAR(50) NULL`);
        } else {
          await queryRunner.query(`ALTER TABLE notifications ADD COLUMN entity_type VARCHAR(50) NULL`);
        }
        await queryRunner.query(`CREATE INDEX idx_notifications_entity_type ON notifications (entity_type)`);
      }

      const hasEntityId = await queryRunner.hasColumn('notifications', 'entity_id');
      if (!hasEntityId) {
        // Rename related_entity_id to entity_id if it exists
        const hasRelatedEntityId = await queryRunner.hasColumn('notifications', 'related_entity_id');
        if (hasRelatedEntityId) {
          await queryRunner.query(`ALTER TABLE notifications CHANGE COLUMN related_entity_id entity_id VARCHAR(36) NULL`);
        } else {
          await queryRunner.query(`ALTER TABLE notifications ADD COLUMN entity_id VARCHAR(36) NULL`);
        }
        await queryRunner.query(`CREATE INDEX idx_notifications_entity_id ON notifications (entity_id)`);
      }

      const hasMetadata = await queryRunner.hasColumn('notifications', 'metadata');
      if (!hasMetadata) {
        await queryRunner.query(`ALTER TABLE notifications ADD COLUMN metadata JSON NULL`);
      }

      const hasExternalId = await queryRunner.hasColumn('notifications', 'external_id');
      if (!hasExternalId) {
        await queryRunner.query(`ALTER TABLE notifications ADD COLUMN external_id VARCHAR(255) NULL`);
        await queryRunner.query(`CREATE INDEX idx_notifications_external_id ON notifications (external_id)`);
      }

      const hasErrorMessage = await queryRunner.hasColumn('notifications', 'error_message');
      if (!hasErrorMessage) {
        await queryRunner.query(`ALTER TABLE notifications ADD COLUMN error_message TEXT NULL`);
      }

      const hasRetryCount = await queryRunner.hasColumn('notifications', 'retry_count');
      if (!hasRetryCount) {
        await queryRunner.query(`ALTER TABLE notifications ADD COLUMN retry_count INT DEFAULT 0`);
      }

      const hasSentAt = await queryRunner.hasColumn('notifications', 'sent_at');
      if (!hasSentAt) {
        await queryRunner.query(`ALTER TABLE notifications ADD COLUMN sent_at TIMESTAMP NULL`);
        await queryRunner.query(`CREATE INDEX idx_notifications_sent_at ON notifications (sent_at)`);
      }

      const hasDeliveredAt = await queryRunner.hasColumn('notifications', 'delivered_at');
      if (!hasDeliveredAt) {
        await queryRunner.query(`ALTER TABLE notifications ADD COLUMN delivered_at TIMESTAMP NULL`);
        await queryRunner.query(`CREATE INDEX idx_notifications_delivered_at ON notifications (delivered_at)`);
      }

      // Update type column to support new notification types
      await queryRunner.query(`
        ALTER TABLE notifications 
        MODIFY COLUMN type VARCHAR(50) NOT NULL
      `);

      // Update priority column to use VARCHAR instead of ENUM
      await queryRunner.query(`
        ALTER TABLE notifications 
        MODIFY COLUMN priority VARCHAR(50) DEFAULT 'medium'
      `);

      console.log('Notifications table updated successfully for polymorphic support!');
    } else {
      console.log('Notifications table does not exist, it will be created by TypeORM.');
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // This migration adds columns and modifies types, rollback would be complex
    // For safety, we'll just log that this migration cannot be easily rolled back
    console.log('This migration cannot be easily rolled back as it modifies existing data structure.');
  }
}
