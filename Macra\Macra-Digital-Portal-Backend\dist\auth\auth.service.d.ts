import { JwtService } from '@nestjs/jwt';
import { UsersService } from '../users/users.service';
import { User } from '../entities/user.entity';
import { LoginDto } from '../dto/auth/login.dto';
import { RegisterDto } from '../dto/auth/register.dto';
import { ForgotPasswordDto, ResetPasswordDto } from '../dto/auth/forgot-password.dto';
import { RequestTwoFactorDto, TwoFactorDto } from '../dto/auth/two-factor.dto';
import { Request } from "express";
import { TwoFactorAction } from '../common/constants/auth.constants';
import { EmailService } from 'src/common/services/email.service';
import { DeviceInfoService } from 'src/common/services/device-info.service';
import { JwtPayload, AuthResponse, LoginResult, RegisterResult, PasswordResetResult } from '../common/types/auth.types';
export declare class AuthService {
    private usersService;
    private jwtService;
    private emailService;
    private deviceInfoService;
    private readonly logger;
    constructor(usersService: UsersService, jwtService: JwtService, emailService: EmailService, deviceInfoService: DeviceInfoService);
    validateUser(email: string, password: string): Promise<User | null>;
    login(loginDto: LoginDto, req: Request): LoginResult;
    register(registerDto: RegisterDto): RegisterResult;
    validateJwtPayload(payload: JwtPayload): Promise<User | null>;
    private createJwtPayload;
    private createAuthResponse;
    forgotPassword(forgotPasswordDto: ForgotPasswordDto): Promise<{
        message: string;
    }>;
    resetPassword(resetPasswordDto: ResetPasswordDto): PasswordResetResult;
    setupTwoFactorAuth(requestTwoFactorDto: RequestTwoFactorDto): Promise<{
        otpAuthUrl: string;
        qrCodeDataUrl: string;
        secret: string;
        message: string;
    }>;
    generateTwoFactorCode(userId: string, action: TwoFactorAction): Promise<{
        message: string;
        otpAuthUrl: string;
        hashedToken: string;
        secret: string;
    }>;
    verifyTwoFactorCode(twoFactorDto: TwoFactorDto, req: Request): Promise<AuthResponse | {
        message: string;
    }>;
}
