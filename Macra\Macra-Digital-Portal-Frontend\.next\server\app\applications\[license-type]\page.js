(()=>{var e={};e.id=3983,e.ids=[3983],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5161:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>p});var r=a(60687),s=a(43210),i=a(16189),n=a(63213),l=a(18980),c=a(78637);function o({licenseTypeId:e,licenseTypeFilter:t,title:a,description:o,searchPlaceholder:d,emptyStateIcon:p,emptyStateMessage:g,departmentType:u}){let{user:m}=(0,n.A)(),x=(0,i.useRouter)(),[y,h]=(0,s.useState)([]),[b,f]=(0,s.useState)([]),[v,w]=(0,s.useState)([]),[j,N]=(0,s.useState)(void 0),[k,E]=(0,s.useState)(!0),[A,S]=(0,s.useState)("Loading applications..."),[_,P]=(0,s.useState)(""),[C,T]=(0,s.useState)(""),[I,R]=(0,s.useState)(""),[D,M]=(0,s.useState)(""),[L,$]=(0,s.useState)(""),[O,F]=(0,s.useState)(1),[U,q]=(0,s.useState)(1),[B,z]=(0,s.useState)(0),[V]=(0,s.useState)(10),W=m?.isAdmin,G=e=>{u?x.push(`/applications/${u}/view/${e}`):x.push(`/applications/view/${e}`)},H=e=>{let t={[l.r.DRAFT]:"bg-gray-100 text-gray-800",[l.r.SUBMITTED]:"bg-blue-100 text-blue-800",[l.r.UNDER_REVIEW]:"bg-yellow-100 text-yellow-800",[l.r.EVALUATION]:"bg-purple-100 text-purple-800",[l.r.APPROVED]:"bg-green-100 text-green-800",[l.r.REJECTED]:"bg-red-100 text-red-800",[l.r.WITHDRAWN]:"bg-gray-100 text-gray-800"},a={[l.r.DRAFT]:"Draft",[l.r.SUBMITTED]:"Submitted",[l.r.UNDER_REVIEW]:"Under Review",[l.r.EVALUATION]:"Evaluation",[l.r.APPROVED]:"Approved",[l.r.REJECTED]:"Rejected",[l.r.WITHDRAWN]:"Withdrawn"};return(0,r.jsx)("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${t[e]}`,children:a[e]})},Y=e=>(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-16 bg-gray-200 rounded-full h-2 mr-2",children:(0,r.jsx)("div",{className:`h-2 rounded-full ${e>=100?"bg-green-600":e>=75?"bg-blue-600":e>=50?"bg-yellow-600":e>=25?"bg-orange-600":"bg-red-600"}`,style:{width:`${e}%`}})}),(0,r.jsxs)("span",{className:"text-sm text-gray-500",children:[e,"%"]})]}),J=async(t,a)=>{try{await c.k.updateApplicationStatus(t,a);let r=await c.k.getApplications({page:O,limit:V,search:_||void 0,filters:{licenseCategoryId:I||void 0,licenseTypeId:e||void 0,status:D||void 0}});h(r.data)}catch(e){}},K=e=>{F(e)};return k?(0,r.jsx)("div",{className:"flex items-center justify-center h-64 bg-gray-50 dark:bg-gray-900",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 dark:border-red-500 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:A})]})}):(0,r.jsx)("div",{className:"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-6",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsx)("div",{className:"flex items-center justify-between",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-gray-100",children:a}),(0,r.jsx)("p",{className:"mt-2 text-sm text-gray-600 dark:text-gray-400",children:o})]})})}),(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4",children:"Filters"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"license-category",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"License Category"}),(0,r.jsxs)("select",{id:"license-category",name:"license-category",value:I,onChange:e=>R(e.target.value),className:"appearance-none block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-gray-200 sm:text-sm",children:[(0,r.jsx)("option",{value:"",children:"All Categories"}),Array.isArray(b)&&b.map(e=>(0,r.jsx)("option",{value:e.license_category_id,children:e.name},e.license_category_id))]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"status",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Application Status"}),(0,r.jsxs)("select",{id:"status",name:"status",value:D,onChange:e=>M(e.target.value),className:"appearance-none block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-gray-200 sm:text-sm",children:[(0,r.jsx)("option",{value:"",children:"All Statuses"}),(0,r.jsx)("option",{value:l.r.DRAFT,children:"Draft"}),(0,r.jsx)("option",{value:l.r.SUBMITTED,children:"Submitted"}),(0,r.jsx)("option",{value:l.r.UNDER_REVIEW,children:"Under Review"}),(0,r.jsx)("option",{value:l.r.EVALUATION,children:"Evaluation"}),(0,r.jsx)("option",{value:l.r.APPROVED,children:"Approved"}),(0,r.jsx)("option",{value:l.r.REJECTED,children:"Rejected"}),(0,r.jsx)("option",{value:l.r.WITHDRAWN,children:"Withdrawn"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"date-range",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Date Range"}),(0,r.jsxs)("select",{id:"date-range",name:"date-range",value:L,onChange:e=>$(e.target.value),className:"appearance-none block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-gray-200 sm:text-sm",children:[(0,r.jsx)("option",{value:"",children:"All Time"}),(0,r.jsx)("option",{value:"last-30",children:"Last 30 Days"}),(0,r.jsx)("option",{value:"last-90",children:"Last 90 Days"}),(0,r.jsx)("option",{value:"last-year",children:"Last Year"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"search-filter",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Search Applications"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)("i",{className:"ri-search-line text-gray-400 dark:text-gray-500"})}),(0,r.jsx)("input",{id:"search-filter",name:"search-filter",value:_,onChange:e=>P(e.target.value),className:"appearance-none block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-gray-200 sm:text-sm",placeholder:d,type:"search"})]})]})]}),(0,r.jsxs)("div",{className:"mt-4 flex items-center justify-between",children:[(0,r.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:y.length>0&&(0,r.jsxs)("span",{children:["Showing ",y.length," of ",B," applications"]})}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsxs)("button",{type:"button",onClick:()=>{R(""),M(""),$(""),P(""),F(1)},className:"inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:[(0,r.jsx)("i",{className:"ri-refresh-line mr-2"}),"Clear Filters"]}),(0,r.jsxs)("button",{type:"button",onClick:()=>{F(1)},className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:[(0,r.jsx)("i",{className:"ri-filter-line mr-2"}),"Apply Filters"]})]})]})]}),(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg",children:[(0,r.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-700",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Applications"}),(0,r.jsxs)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:["Manage and track license applications for ",a.toLowerCase()]})]}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[(0,r.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-900",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Application Number"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Applicant"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"License Category"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Progress"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Submitted Date"}),(0,r.jsx)("th",{scope:"col",className:"relative px-6 py-3",children:(0,r.jsx)("span",{className:"sr-only",children:"Actions"})})]})}),(0,r.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:y.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:e.application_number})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0 h-10 w-10",children:(0,r.jsx)("div",{className:"h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center",children:(0,r.jsx)("i",{className:"ri-building-line text-blue-600 dark:text-blue-400"})})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:e.applicant?.name||"N/A"}),(0,r.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:["BRN: ",e.applicant?.business_registration_number||"N/A"," | TPIN: ",e.applicant?.tpin||"N/A"]})]})]})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:e.license_category?.name||"N/A"}),(0,r.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:e.license_category?.license_type?.name||"N/A"})]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:H(e.status)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:Y(e.progress_percentage)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:e.submitted_at?new Date(e.submitted_at).toLocaleDateString():new Date(e.created_at).toLocaleDateString()}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-2",children:[(0,r.jsxs)("button",{type:"button",onClick:()=>G(e.application_id),className:"inline-flex items-center px-3 py-1 text-xs font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/40 transition-colors",children:[(0,r.jsx)("i",{className:"ri-eye-line mr-1"}),"View"]}),W&&(0,r.jsxs)(r.Fragment,{children:[e.status===l.r.SUBMITTED&&(0,r.jsxs)("button",{type:"button",onClick:()=>J(e.application_id,l.r.UNDER_REVIEW),className:"inline-flex items-center px-3 py-1 text-xs font-medium text-yellow-600 dark:text-yellow-400 hover:text-yellow-800 dark:hover:text-yellow-300 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md hover:bg-yellow-100 dark:hover:bg-yellow-900/40 transition-colors",children:[(0,r.jsx)("i",{className:"ri-search-line mr-1"}),"Review"]}),e.status===l.r.UNDER_REVIEW&&(0,r.jsxs)("button",{type:"button",onClick:()=>J(e.application_id,l.r.EVALUATION),className:"inline-flex items-center px-3 py-1 text-xs font-medium text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-md hover:bg-purple-100 dark:hover:bg-purple-900/40 transition-colors",children:[(0,r.jsx)("i",{className:"ri-clipboard-line mr-1"}),"Evaluate"]}),e.status===l.r.EVALUATION&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("button",{type:"button",onClick:()=>J(e.application_id,l.r.APPROVED),className:"inline-flex items-center px-3 py-1 text-xs font-medium text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md hover:bg-green-100 dark:hover:bg-green-900/40 transition-colors",children:[(0,r.jsx)("i",{className:"ri-check-line mr-1"}),"Approve"]}),(0,r.jsxs)("button",{type:"button",onClick:()=>J(e.application_id,l.r.REJECTED),className:"inline-flex items-center px-3 py-1 text-xs font-medium text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md hover:bg-red-100 dark:hover:bg-red-900/40 transition-colors",children:[(0,r.jsx)("i",{className:"ri-close-line mr-1"}),"Reject"]})]}),(0,r.jsx)("button",{type:"button",className:"inline-flex items-center px-3 py-1 text-xs font-medium text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300 bg-gray-50 dark:bg-gray-900/20 border border-gray-200 dark:border-gray-800 rounded-md hover:bg-gray-100 dark:hover:bg-gray-900/40 transition-colors",children:(0,r.jsx)("i",{className:"ri-file-text-line mr-1"})})]})]})})]},e.application_id))})]})}),0===y.length&&!k&&(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("i",{className:`${p} text-4xl text-gray-400 dark:text-gray-500 mb-4`}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"No Applications Found"}),(0,r.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:g})]}),y.length>0&&(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 px-6 py-4 flex items-center justify-between border-t border-gray-200 dark:border-gray-700",children:[(0,r.jsxs)("div",{className:"flex-1 flex justify-between sm:hidden",children:[(0,r.jsxs)("button",{onClick:()=>K(Math.max(1,O-1)),disabled:1===O,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,r.jsx)("i",{className:"ri-arrow-left-line mr-2"}),"Previous"]}),(0,r.jsxs)("button",{onClick:()=>K(Math.min(U,O+1)),disabled:O===U,className:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:["Next",(0,r.jsx)("i",{className:"ri-arrow-right-line ml-2"})]})]}),(0,r.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,r.jsx)("div",{children:(0,r.jsxs)("p",{className:"text-sm text-gray-700 dark:text-gray-300",children:["Showing ",(0,r.jsx)("span",{className:"font-medium",children:(O-1)*V+1})," to"," ",(0,r.jsx)("span",{className:"font-medium",children:Math.min(O*V,B)})," of"," ",(0,r.jsx)("span",{className:"font-medium",children:B})," applications"]})}),(0,r.jsx)("div",{children:(0,r.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[(0,r.jsxs)("button",{onClick:()=>K(Math.max(1,O-1)),disabled:1===O,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,r.jsx)("span",{className:"sr-only",children:"Previous"}),(0,r.jsx)("i",{className:"ri-arrow-left-s-line"})]}),Array.from({length:Math.min(5,U)},(e,t)=>{let a=Math.max(1,Math.min(U-4,O-2))+t;return a>U?null:(0,r.jsx)("button",{onClick:()=>K(a),className:`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${a===O?"z-10 bg-red-50 dark:bg-red-900/20 border-red-500 text-red-600 dark:text-red-400":"bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600"}`,children:a},a)}),(0,r.jsxs)("button",{onClick:()=>K(Math.min(U,O+1)),disabled:O===U,className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,r.jsx)("span",{className:"sr-only",children:"Next"}),(0,r.jsx)("i",{className:"ri-arrow-right-s-line"})]})]})})]})]})]}),0===y.length&&!k&&(0,r.jsxs)("div",{className:"text-center py-12 bg-white dark:bg-gray-800 rounded-lg shadow",children:[(0,r.jsx)("i",{className:`${p} text-4xl text-gray-400 dark:text-gray-500 mb-4`}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"No applications found"}),(0,r.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:_||""!==D||""!==I?"Try adjusting your search or filter criteria.":g})]})]})})}a(34130),a(5618);let d={postal:{name:"Postal Services",description:"Manage postal and courier service license applications for domestic and international operations. View applications, filter by category and status.",searchPlaceholder:"Search postal applications, companies, application numbers...",emptyStateIcon:"ri-mail-line",emptyStateMessage:"No postal license applications are available.",licenseTypeFilter:"postal"},telecommunications:{name:"Telecommunications",description:"Manage telecommunications and spectrum license applications for mobile networks, ISPs, and broadcasting services. View applications, filter by category and status.",searchPlaceholder:"Search telecommunications applications, companies, application numbers...",emptyStateIcon:"ri-signal-tower-line",emptyStateMessage:"No telecommunications license applications are available.",licenseTypeFilter:"telecommunications"},standards:{name:"Standards Compliance",description:"Manage standards compliance and type approval certificate applications. View applications, filter by category and status.",searchPlaceholder:"Search standards applications, companies, application numbers...",emptyStateIcon:"ri-award-line",emptyStateMessage:"No standards applications are available.",licenseTypeFilter:"standards"},clf:{name:"CLF (Converged Licensing Framework)",description:"Manage CLF licenses including Network Facility, Network Service, Application Service, and Content Service licenses.",searchPlaceholder:"Search CLF licenses, companies, license IDs...",emptyStateIcon:"ri-collage-line",emptyStateMessage:"No CLF license applications are available.",licenseTypeFilter:"clf"}};function p(){let e=(0,i.useParams)()["license-type"],t=d[e];return t?(0,r.jsx)(o,{licenseTypeFilter:t.licenseTypeFilter,title:`${t.name} License Management`,description:t.description,searchPlaceholder:t.searchPlaceholder,emptyStateIcon:t.emptyStateIcon,emptyStateMessage:t.emptyStateMessage,departmentType:e}):(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("i",{className:"ri-error-warning-line text-4xl text-red-500 mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Invalid License Type"}),(0,r.jsx)("p",{className:"text-gray-500",children:"The requested license type was not found."})]})})}},5618:(e,t,a)=>{"use strict";a.d(t,{v:()=>i});var r=a(12234),s=a(15885);let i={async getLicenseTypes(e={}){let t=new URLSearchParams;return e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(([e,a])=>{Array.isArray(a)?a.forEach(a=>t.append(`filter.${e}`,a)):t.set(`filter.${e}`,a)}),(await r.uE.get(`/license-types?${t.toString()}`)).data},getLicenseType:async e=>(await r.uE.get(`/license-types/${e}`)).data,createLicenseType:async e=>(await r.uE.post("/license-types",e)).data,updateLicenseType:async(e,t)=>(await r.uE.put(`/license-types/${e}`,t)).data,deleteLicenseType:async e=>(await r.uE.delete(`/license-types/${e}`)).data,async getAllLicenseTypes(){return s.qI.getOrSet(s._l.LICENSE_TYPES,async()=>(await this.getLicenseTypes({limit:100})).data,s.U_.LONG)}}},7538:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i,metadata:()=>s});var r=a(37413);let s={title:"License Applications - MACRA Digital Portal",description:"Manage license applications for various service types"};function i({children:e}){return(0,r.jsx)(r.Fragment,{children:e})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},15885:(e,t,a)=>{"use strict";a.d(t,{U_:()=>n,_l:()=>i,qI:()=>s});class r{set(e,t,a=this.defaultTTL){let r=Date.now();this.cache.set(e,{data:t,timestamp:r,expiresAt:r+a})}get(e){let t=this.cache.get(e);return t?Date.now()>t.expiresAt?(this.cache.delete(e),null):t.data:null}has(e){return null!==this.get(e)}delete(e){return this.cache.delete(e)}clear(){this.cache.clear()}getStats(){return{size:this.cache.size,keys:Array.from(this.cache.keys())}}cleanup(){let e=Date.now(),t=0;for(let[t,a]of this.cache.entries())e>a.expiresAt&&this.cache.delete(t)}async getOrSet(e,t,a=this.defaultTTL){let r=this.get(e);if(null!==r)return r;let s=await t();return this.set(e,s,a),s}invalidatePattern(e){let t=new RegExp(e),a=0;for(let e of this.cache.keys())t.test(e)&&this.cache.delete(e)}constructor(){this.cache=new Map,this.defaultTTL=3e5}}let s=new r,i={LICENSE_TYPES:"license-types",LICENSE_CATEGORIES:"license-categories",LICENSE_CATEGORIES_BY_TYPE:e=>`license-categories-type-${e}`,USER_APPLICATIONS:"user-applications",APPLICATION:e=>`application-${e}`},n={SHORT:12e4,MEDIUM:3e5,LONG:9e5,VERY_LONG:36e5};setInterval(()=>{s.cleanup()},3e5)},18980:(e,t,a)=>{"use strict";a.d(t,{r:()=>r});var r=function(e){return e.DRAFT="draft",e.SUBMITTED="submitted",e.UNDER_REVIEW="under_review",e.EVALUATION="evaluation",e.APPROVED="approved",e.REJECTED="rejected",e.WITHDRAWN="withdrawn",e}({})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19961:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>o});var r=a(60687),s=a(43210),i=a(16189),n=a(63213),l=a(21891),c=a(60417);function o({children:e}){let{isAuthenticated:t,loading:a}=(0,n.A)();(0,i.useRouter)();let[o,d]=(0,s.useState)("overview"),[p,g]=(0,s.useState)(!1);return a?(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"})}):t?(0,r.jsxs)("div",{className:"flex h-screen overflow-hidden",children:[(0,r.jsx)("div",{id:"mobileSidebarOverlay",className:`mobile-sidebar-overlay ${p?"show":""}`,onClick:()=>g(!1)}),(0,r.jsx)(c.A,{}),(0,r.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,r.jsx)(l.A,{activeTab:o,onTabChange:d,onMobileMenuToggle:()=>{g(!p)}}),e]})]}):null}},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33655:(e,t,a)=>{Promise.resolve().then(a.bind(a,5161))},33873:e=>{"use strict";e.exports=require("path")},34130:(e,t,a)=>{"use strict";a.d(t,{TG:()=>l});var r=a(12234),s=a(15885);let i=e=>e.toLowerCase().replace(/[^a-z0-9\s]/g,"").replace(/\s+/g,"-").replace(/^-+|-+$/g,"").substring(0,50),n=e=>e.map(e=>({...e,code:i(e.name),children:e.children?n(e.children):void 0})),l={async getLicenseCategories(e={}){let t=new URLSearchParams;return e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(([e,a])=>{Array.isArray(a)?a.forEach(a=>t.append(`filter.${e}`,a)):t.set(`filter.${e}`,a)}),(await r.uE.get(`/license-categories?${t.toString()}`)).data},async getLicenseCategory(e){try{return(await r.uE.get(`/license-categories/${e}`,{timeout:3e4})).data}catch(e){if("ECONNABORTED"===e.code)throw Error("Request timeout - please try again");throw e}},async getLicenseCategoriesByType(e){try{return(await r.uE.get(`/license-categories/by-license-type/${e}`,{timeout:3e4})).data}catch(e){if("ECONNABORTED"===e.code)throw Error("Request timeout - please try again");if(e.response?.status===429)throw Error("Too many requests - please wait a moment and try again");throw e}},createLicenseCategory:async e=>(await r.uE.post("/license-categories",e)).data,updateLicenseCategory:async(e,t)=>(await r.uE.put(`/license-categories/${e}`,t)).data,deleteLicenseCategory:async e=>(await r.uE.delete(`/license-categories/${e}`)).data,async getAllLicenseCategories(){return s.qI.getOrSet(s._l.LICENSE_CATEGORIES,async()=>n((await this.getLicenseCategories({limit:100})).data),s.U_.LONG)},getCategoryTree:async e=>s.qI.getOrSet(`category-tree-${e}`,async()=>n((await r.uE.get(`/license-categories/license-type/${e}/tree`)).data),s.U_.MEDIUM),getRootCategories:async e=>s.qI.getOrSet(`root-categories-${e}`,async()=>(await r.uE.get(`/license-categories/license-type/${e}/root`)).data,s.U_.MEDIUM),async getCategoriesForParentSelection(e,t){try{try{let a=await r.uE.get(`/license-categories/license-type/${e}/for-parent-selection`,{params:t?{excludeId:t}:{}});if(a.data&&Array.isArray(a.data.data))return a.data.data;return[]}catch(s){let a=await r.uE.get(`/license-categories/by-license-type/${e}`);if(!(a.data&&Array.isArray(a.data)))return[];{let e=a.data;return t&&(e=e.filter(e=>e.license_category_id!==t)),e}}}catch(e){return[]}},getPotentialParents:async(e,t)=>(await r.uE.get(`/license-categories/license-type/${e}/potential-parents`,{params:t?{excludeId:t}:{}})).data}},51366:(e,t,a)=>{Promise.resolve().then(a.bind(a,19961))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59614:(e,t,a)=>{Promise.resolve().then(a.bind(a,68031))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68031:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\applications\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\applications\\layout.tsx","default")},73407:(e,t,a)=>{Promise.resolve().then(a.bind(a,85641))},74075:e=>{"use strict";e.exports=require("zlib")},74673:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>g,tree:()=>o});var r=a(65239),s=a(48088),i=a(88170),n=a.n(i),l=a(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);a.d(t,c);let o={children:["",{children:["applications",{children:["[license-type]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,85641)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\applications\\[license-type]\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,7538)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\applications\\[license-type]\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,68031)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\applications\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\applications\\[license-type]\\page.tsx"],p={require:a,loadChunk:()=>Promise.resolve()},g=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/applications/[license-type]/page",pathname:"/applications/[license-type]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},78335:()=>{},78637:(e,t,a)=>{"use strict";a.d(t,{k:()=>i});var r=a(51278),s=a(12234);let i={async getApplications(e){let t=new URLSearchParams;e?.page&&t.append("page",e.page.toString()),e?.limit&&t.append("limit",e.limit.toString()),e?.search&&t.append("search",e.search),e?.sortBy&&t.append("sortBy",e.sortBy),e?.sortOrder&&t.append("sortOrder",e.sortOrder),e?.filters?.licenseTypeId&&t.append("filter.license_category.license_type_id",e.filters.licenseTypeId),e?.filters?.licenseCategoryId&&t.append("filter.license_category_id",e.filters.licenseCategoryId),e?.filters?.status&&t.append("filter.status",e.filters.status);let a=await s.uE.get(`/applications?${t.toString()}`);return(0,r.zp)(a)},async getApplicationsByLicenseType(e,t){let a=new URLSearchParams;t?.page&&a.append("page",t.page.toString()),t?.limit&&a.append("limit",t.limit.toString()),t?.search&&a.append("search",t.search),t?.status&&a.append("filter.status",t.status),a.append("filter.license_category.license_type_id",e);let i=await s.uE.get(`/applications?${a.toString()}`);return(0,r.zp)(i)},async getApplication(e){let t=await s.uE.get(`/applications/${e}`);return(0,r.zp)(t)},async getApplicationsByApplicant(e){let t=await s.uE.get(`/applications/by-applicant/${e}`);return(0,r.zp)(t)},async getApplicationsByStatus(e){let t=await s.uE.get(`/applications/by-status/${e}`);return(0,r.zp)(t)},async updateApplicationStatus(e,t){let a=await s.uE.put(`/applications/${e}/status?status=${t}`);return(0,r.zp)(a)},async updateApplicationProgress(e,t,a){let i=await s.uE.put(`/applications/${e}/progress?currentStep=${t}&progressPercentage=${a}`);return(0,r.zp)(i)},async getApplicationStats(){let e=await s.uE.get("/applications/stats");return(0,r.zp)(e)},async createApplication(e){try{let t=await s.uE.post("/applications",e);return(0,r.zp)(t)}catch(e){throw e}},async updateApplication(e,t){try{let a=await s.uE.put(`/applications/${e}`,t,{timeout:3e4});return(0,r.zp)(a)}catch(e){if("ECONNABORTED"===e.code)throw Error("Request timeout - please try again");if(e.response?.status===400){let t=e.response?.data?.message||"Invalid application data";throw Error(`Bad Request: ${t}`)}if(e.response?.status===429)throw Error("Too many requests - please wait a moment and try again");throw e}},async deleteApplication(e){let t=await s.uE.delete(`/applications/${e}`);return(0,r.zp)(t)},async createApplicationWithApplicant(e){try{let t=new Date,a=t.toISOString().slice(0,10).replace(/-/g,""),r=t.toTimeString().slice(0,8).replace(/:/g,""),s=Math.random().toString(36).substr(2,3).toUpperCase(),i=`APP-${a}-${r}-${s}`;if(!/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e.user_id))throw Error(`Invalid user_id format: ${e.user_id}. Expected UUID format.`);return await this.createApplication({application_number:i,applicant_id:e.user_id,license_category_id:e.license_category_id,current_step:1,progress_percentage:0})}catch(e){throw e}},async saveApplicationSection(e,t,a){try{let a=1,r=["applicantInfo","companyProfile","businessInfo","serviceScope","businessPlan","legalHistory","reviewSubmit"].indexOf(t);a=r>=0?r+1:1;let s=Math.min(Math.round(a/6*100),100);await this.updateApplication(e,{progress_percentage:s,current_step:a})}catch(e){throw e}},async getApplicationSection(e,t){try{let a=await s.uE.get(`/applications/${e}/sections/${t}`);return(0,r.zp)(a)}catch(e){throw e}},async submitApplication(e){try{let t=await s.uE.put(`/applications/${e}`,{status:"submitted",submitted_at:new Date().toISOString(),progress_percentage:100,current_step:7});return(0,r.zp)(t)}catch(e){throw e}},async getUserApplications(){try{let e=await s.uE.get("/applications/user-applications"),t=(0,r.zp)(e),a=[];return t?.data?a=Array.isArray(t.data)?t.data:[]:Array.isArray(t)?a=t:t&&(a=[t]),a}catch(e){throw e}},async saveAsDraft(e,t){try{let a=await s.uE.put(`/applications/${e}`,{form_data:t,status:"draft"});return(0,r.zp)(a)}catch(e){throw e}},async validateApplication(e){try{let e={},t=[];for(let a of["applicantInfo","companyProfile","businessInfo","legalHistory"])e[a]&&0!==Object.keys(e[a]).length||t.push(`${a} section is incomplete`);return{isValid:0===t.length,errors:t}}catch(e){throw e}}}},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85641:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\applications\\\\[license-type]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\applications\\[license-type]\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[4447,7498,1658,5814,7563,6606],()=>a(74673));module.exports=r})();