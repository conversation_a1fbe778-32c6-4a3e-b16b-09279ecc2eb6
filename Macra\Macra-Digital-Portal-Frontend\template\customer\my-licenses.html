<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>My Licenses - Customer Portal</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: { primary: "#e02b20", secondary: "#6366f1", "primary-subtle": "#e4463c", "secondary-subtle": "#9FE2BF" },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <style type="text/tailwindcss">
      @layer components {
        .custom-form-label {
          @apply block text-sm font-medium text-gray-700 pb-2;
        }
        .enhanced-input {
          @apply appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm;
        }

        .enhanced-select {
          @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm;
        }

        .enhanced-checkbox {
          @apply h-5 w-5 text-primary focus:ring-primary border border-gray-300 rounded;
        }

        .main-button {
          @apply inline-flex py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-subtle hover:bg-primary focus:ring focus:ring-primary-subtle;
        }

        .secondary-main-button {
          @apply inline-flex py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring focus:ring-secondary-subtle/50;
        }

        .license-card {
          @apply bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-lg hover:border-primary/20 transition-all duration-300 transform hover:-translate-y-1;
        }

        .license-card-icon {
          @apply w-12 h-12 rounded-lg flex items-center justify-center text-2xl;
        }

        .license-card-button {
          @apply w-full bg-primary text-white px-4 py-3 rounded-lg font-medium hover:bg-primary-subtle focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all duration-200 transform hover:scale-105;
        }

        .fade-in {
          animation: fadeIn 0.6s ease-in-out;
        }

        .slide-up {
          animation: slideUp 0.8s ease-out;
        }

        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }

        @keyframes slideUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      }

      @layer utilities {
        :root {
          --color-primary: #e02b20;
          --color-secondary: #20d5e0;
          --color-primary-subtle: #e4463c;
          --color-secondary-subtle: #abeff3;
        }
      }
    </style>
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
        font-family: 'Inter', sans-serif;
        background-color: #f9fafb;
      }

      .dropdown-content {
        display: none;
        position: absolute;
        right: 0;
        top: 100%;
        z-index: 50;
      }
      .dropdown-content.show {
        display: block;
      }

      .side-nav{
        overflow: auto;
        -ms-overflow-style: none;
        height: 75vh;
      }
      .side-nav::-webkit-scrollbar {
        display: none;
      }
      /* Hide scrollbar for Firefox */
      .side-nav {
        scrollbar-width: none;
      }

      /* Mobile sidebar styles */
      @media (max-width: 768px) {
        .mobile-sidebar-open {
          display: block !important;
          position: fixed;
          z-index: 50;
          height: 100vh;
          box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        }

        .mobile-sidebar-overlay {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.5);
          z-index: 40;
          display: none;
        }

        .mobile-sidebar-overlay.show {
          display: block;
        }
      }

      .stagger-animation {
        animation-delay: calc(var(--stagger) * 0.1s);
      }
    </style>
  </head>
  <body>
    <div class="flex h-screen overflow-hidden">
      <!-- Mobile sidebar overlay -->
      <div id="mobileSidebarOverlay" class="mobile-sidebar-overlay"></div>

      <!-- Sidebar -->
      <aside id="sidebar" class="w-64 bg-white shadow-lg flex-shrink-0 hidden md:block">
        <div class="h-16 flex items-center px-6 border-b">
          <div class="flex items-center">
            <img src="../images/macra-logo.png" alt="Logo" class="max-h-12 w-auto">
          </div>
        </div>
        <nav class="mt-6 px-4 side-nav">
          <div class="space-y-1">
            <a
              href="index.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-dashboard-line"></i>
              </div>
              Dashboard
            </a>
            <a
              href="my-licenses.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md bg-primary bg-opacity-10 text-primary"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-key-line"></i>
              </div>
              My Licenses
            </a>
            <a
              href="new-application.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-file-list-3-line"></i>
              </div>
              New Application
            </a>
            <a
              href="payments.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z" />
                </svg>
              </div>
              Payments
            </a>
            <a
              href="documents.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-file-text-line"></i>
              </div>
              Documents
            </a>
            <a
              href="request-resource.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-hand-heart-line"></i>
              </div>
              Request Resource
            </a>
          </div>

          <div class="mt-8">
            <h3
              class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider"
            >
              Support
            </h3>
            <div class="mt-2 space-y-1">
              <a
                href="help-center.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-question-line"></i>
                </div>
                Help Center
              </a>
              <a
                href="contact-support.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-customer-service-2-line"></i>
                </div>
                Contact Support
              </a>
            </div>
          </div>
        </nav>
        <div class="absolute bottom-0 w-64 p-4 border-t">
          <a href="profile.html" class="flex items-center hover:bg-gray-50 p-2 rounded-md">
            <img
              class="h-10 w-10 rounded-full"
              src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp"
              alt="Profile"
            />
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-900">John Smith</p>
              <p class="text-xs text-gray-500">Acme Corporation</p>
            </div>
          </a>
        </div>
      </aside>
      <!-- Main content -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <!-- Top header -->
        <header class="bg-white shadow-sm z-10">
          <div class="flex items-center justify-between h-16 px-4 sm:px-6">
            <button
              id="mobileMenuBtn"
              type="button"
              onclick="toggleMobileSidebar()"
              class="md:hidden text-gray-500 hover:text-gray-700 focus:outline-none"
            >
              <div class="w-6 h-6 flex items-center justify-center">
                <i class="ri-menu-line ri-lg"></i>
              </div>
            </button>
            <div class="flex-1 flex items-center justify-between">
              <div class="flex-1 flex justify-center px-2 lg:ml-6 lg:justify-start">
                <h1 class="text-xl font-medium text-gray-900">My Licenses</h1>
              </div>
            <div class="flex items-center">
              <button
                type="button"
                class="flex-shrink-0 p-1 mr-4 text-gray-500 rounded-full hover:text-gray-700 focus:outline-none relative"
              >
                <span class="sr-only">View notifications</span>
                <div class="w-6 h-6 flex items-center justify-center">
                  <i class="ri-notification-3-line ri-lg"></i>
                </div>
                <span
                  class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white"
                ></span>
              </button>
              <div class="dropdown relative">
                <button
                  type="button"
                  onclick="toggleDropdown()"
                  class="flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                >
                  <span class="sr-only">Open user menu</span>
                  <img
                    class="h-8 w-8 rounded-full"
                    src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp"
                    alt="Profile"
                  />
                </button>
                <div
                  id="userDropdown"
                  class="dropdown-content mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5"
                >
                  <div class="py-1">
                    <a
                      href="profile.html"
                      class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >Your Profile</a
                    >
                    <a
                      href="account-settings.html"
                      class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >Settings</a
                    >
                    <a
                      href="../auth/login.html"
                      class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >Sign out</a
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
        </header>


        <!-- Main content area -->
        <main class="flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6">
          <div class="max-w-7xl mx-auto">
            <!-- Page header -->
            <div class="mb-8">
              <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                  <h1 class="text-3xl font-bold text-gray-900">My Licenses</h1>
                  <p class="mt-2 text-gray-600">Manage your licenses, renewals, and track expiration dates.</p>
                </div>
                <div class="mt-4 sm:mt-0">
                  <a
                    href="new-application.html"
                    class="main-button"
                  >
                    <i class="ri-add-circle-line mr-2"></i>
                    Apply for New License
                  </a>
                </div>
              </div>
            </div>

            <!-- License filters -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
              <h2 class="text-lg font-semibold text-gray-900 mb-4">Filter Licenses</h2>
              <div class="grid grid-cols-1 gap-y-4 sm:grid-cols-4 sm:gap-x-6">
                <div>
                  <label for="license-type" class="custom-form-label">License Type</label>
                  <select id="license-type" name="license-type" class="enhanced-select">
                    <option value="">All Types</option>
                    <option value="nfl">Network Facility Licence (NFL)</option>
                    <option value="nsl">Network Service Licence (NSL)</option>
                    <option value="asl">Application Service Licence (ASL)</option>
                    <option value="csl">Content Service Licence (CSL)</option>
                  </select>
                </div>
                <div>
                  <label for="status" class="custom-form-label">Status</label>
                  <select id="status" name="status" class="enhanced-select">
                    <option value="">All Statuses</option>
                    <option value="active">Active</option>
                    <option value="pending">Pending</option>
                    <option value="expired">Expired</option>
                    <option value="suspended">Suspended</option>
                  </select>
                </div>
                <div>
                  <label for="date-range" class="custom-form-label">Date Range</label>
                  <select id="date-range" name="date-range" class="enhanced-select">
                    <option value="">All Time</option>
                    <option value="last-30">Last 30 Days</option>
                    <option value="last-90">Last 90 Days</option>
                    <option value="last-year">Last Year</option>
                  </select>
                </div>
                <div class="flex items-end">
                  <button type="button" class="secondary-main-button w-full">
                    <i class="ri-filter-line mr-2"></i>
                    Apply Filters
                  </button>
                </div>
              </div>
            </div>

            <!-- License table -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200">
              <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">License Overview</h2>
                <p class="text-sm text-gray-600 mt-1">View and manage all your active licenses</p>
              </div>
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        License Details
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Type & Category
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Validity Period
                      </th>
                      <th scope="col" class="relative px-6 py-3">
                        <span class="sr-only">Actions</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <!-- Sample license rows -->
                    <tr class="hover:bg-gray-50 transition-colors">
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                          <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                            <i class="ri-global-line text-blue-600"></i>
                          </div>
                          <div>
                            <div class="text-sm font-medium text-gray-900">ISP-2024-001</div>
                            <div class="text-sm text-gray-500">Internet Service Provider</div>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">Application Service Licence</div>
                        <div class="text-sm text-gray-500">ASL Category</div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                          Active
                        </span>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">Jan 15, 2024 - Jan 15, 2027</div>
                        <div class="text-sm text-gray-500">2 years remaining</div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div class="flex items-center justify-end space-x-2">
                          <button class="text-primary hover:text-primary-subtle">
                            <i class="ri-eye-line"></i>
                          </button>
                          <button class="text-primary hover:text-primary-subtle">
                            <i class="ri-download-line"></i>
                          </button>
                          <button class="text-primary hover:text-primary-subtle">
                            <i class="ri-refresh-line"></i>
                          </button>
                        </div>
                      </td>
                    </tr>

                    <tr class="hover:bg-gray-50 transition-colors">
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                          <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center mr-3">
                            <i class="ri-radio-line text-red-600"></i>
                          </div>
                          <div>
                            <div class="text-sm font-medium text-gray-900">RAD-2024-002</div>
                            <div class="text-sm text-gray-500">Radio Broadcasting</div>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">Content Service Licence</div>
                        <div class="text-sm text-gray-500">CSL Category</div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                          Pending Renewal
                        </span>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">Mar 10, 2022 - Mar 10, 2025</div>
                        <div class="text-sm text-red-500">Expires in 3 months</div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div class="flex items-center justify-end space-x-2">
                          <button class="text-primary hover:text-primary-subtle">
                            <i class="ri-eye-line"></i>
                          </button>
                          <button class="text-primary hover:text-primary-subtle">
                            <i class="ri-download-line"></i>
                          </button>
                          <button class="text-orange-600 hover:text-orange-700">
                            <i class="ri-refresh-line"></i>
                          </button>
                        </div>
                      </td>
                    </tr>

                    <tr class="hover:bg-gray-50 transition-colors">
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                          <div class="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center mr-3">
                            <i class="ri-satellite-dish-line text-indigo-600"></i>
                          </div>
                          <div>
                            <div class="text-sm font-medium text-gray-900">SAT-2023-003</div>
                            <div class="text-sm text-gray-500">Satellite Communications</div>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">Network Facility Licence</div>
                        <div class="text-sm text-gray-500">NFL Category</div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                          Active
                        </span>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">Aug 20, 2023 - Aug 20, 2026</div>
                        <div class="text-sm text-gray-500">1.5 years remaining</div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div class="flex items-center justify-end space-x-2">
                          <button class="text-primary hover:text-primary-subtle">
                            <i class="ri-eye-line"></i>
                          </button>
                          <button class="text-primary hover:text-primary-subtle">
                            <i class="ri-download-line"></i>
                          </button>
                          <button class="text-primary hover:text-primary-subtle">
                            <i class="ri-refresh-line"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <div class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                <div class="flex-1 flex justify-between sm:hidden">
                  <button class="secondary-main-button">
                    <i class="ri-arrow-left-s-line mr-1"></i>
                    Previous
                  </button>
                  <button class="secondary-main-button">
                    Next
                    <i class="ri-arrow-right-s-line ml-1"></i>
                  </button>
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                  <div>
                    <p class="text-sm text-gray-700">
                      Showing <span class="font-medium">1</span> to <span class="font-medium">3</span> of <span class="font-medium">3</span> licenses
                    </p>
                  </div>
                  <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                      <button class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <span class="sr-only">Previous</span>
                        <i class="ri-arrow-left-s-line"></i>
                      </button>
                      <button aria-current="page" class="z-10 bg-primary bg-opacity-10 border-primary text-primary relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                        1
                      </button>
                      <button class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <span class="sr-only">Next</span>
                        <i class="ri-arrow-right-s-line"></i>
                      </button>
                    </nav>
                  </div>
                </div>
              </div>
            </div>

            <!-- Additional Information -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-8">
              <!-- Quick Actions -->
              <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <i class="ri-flashlight-line mr-2 text-primary"></i>
                  Quick Actions
                </h3>
                <div class="space-y-3">
                  <a href="new-application.html" class="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                    <i class="ri-add-circle-line text-primary mr-3"></i>
                    <div>
                      <p class="text-sm font-medium text-gray-900">Apply for New License</p>
                      <p class="text-xs text-gray-500">Start a new license application</p>
                    </div>
                  </a>
                  <a href="#" class="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                    <i class="ri-refresh-line text-primary mr-3"></i>
                    <div>
                      <p class="text-sm font-medium text-gray-900">Renew Existing License</p>
                      <p class="text-xs text-gray-500">Extend your license validity</p>
                    </div>
                  </a>
                  <a href="#" class="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                    <i class="ri-download-line text-primary mr-3"></i>
                    <div>
                      <p class="text-sm font-medium text-gray-900">Download Certificates</p>
                      <p class="text-xs text-gray-500">Get official license documents</p>
                    </div>
                  </a>
                </div>
              </div>

              <!-- Support Information -->
              <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <i class="ri-customer-service-line mr-2 text-primary"></i>
                  Need Help?
                </h3>
                <div class="space-y-4">
                  <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                    <i class="ri-phone-line text-primary mr-3"></i>
                    <div>
                      <p class="text-sm font-medium text-gray-900">Phone Support</p>
                      <p class="text-xs text-gray-500">+265 1 770 100</p>
                    </div>
                  </div>
                  <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                    <i class="ri-mail-line text-primary mr-3"></i>
                    <div>
                      <p class="text-sm font-medium text-gray-900">Email Support</p>
                      <p class="text-xs text-gray-500"><EMAIL></p>
                    </div>
                  </div>
                  <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                    <i class="ri-time-line text-primary mr-3"></i>
                    <div>
                      <p class="text-sm font-medium text-gray-900">Office Hours</p>
                      <p class="text-xs text-gray-500">Mon-Fri, 8:00 AM - 5:00 PM</p>
                    </div>
                  </div>
                  <a href="help-center.html" class="block w-full text-center bg-primary text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-opacity-90 transition-all">
                    Visit Help Center
                  </a>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>

    <script>
      // Toggle dropdown menu
      function toggleDropdown() {
        const dropdown = document.getElementById('userDropdown');
        dropdown.classList.toggle('show');

        // Prevent the click from propagating to the window event
        event.stopPropagation();
      }

      // Close dropdown when clicking outside
      window.addEventListener('click', function(event) {
        if (!event.target.matches('.dropdown button') && !event.target.closest('.dropdown button img')) {
          const dropdowns = document.getElementsByClassName('dropdown-content');
          for (let i = 0; i < dropdowns.length; i++) {
            const openDropdown = dropdowns[i];
            if (openDropdown.classList.contains('show')) {
              openDropdown.classList.remove('show');
            }
          }
        }
      });

      // Make functions globally available
      window.toggleDropdown = toggleDropdown;

      // Toggle mobile sidebar
      function toggleMobileSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('mobileSidebarOverlay');

        sidebar.classList.toggle('mobile-sidebar-open');
        overlay.classList.toggle('show');

        // Close sidebar when clicking on overlay
        overlay.onclick = function() {
          sidebar.classList.remove('mobile-sidebar-open');
          overlay.classList.remove('show');
        };
      }

      // Make toggleMobileSidebar function globally available
      window.toggleMobileSidebar = toggleMobileSidebar;
    </script>
  </body>
</html>