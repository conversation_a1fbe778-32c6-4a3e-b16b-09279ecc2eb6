'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import { useAuth } from '@/contexts/AuthContext';
import { useLicenseData } from '@/hooks/useLicenseData';
import { LicenseType } from '@/services/licenseTypeService';
import { LicenseCategory } from '@/services/licenseCategoryService';
import { getLicenseTypeStepConfig } from '@/config/licenseTypeStepConfig';

const LicenseCategorySelectionPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const { isAuthenticated, loading: authLoading } = useAuth();
  const { licenseTypes, categories, loading: licenseLoading, getCategoriesByType } = useLicenseData();

  const [selectedLicenseType, setSelectedLicenseType] = useState<LicenseType | null>(null);
  const [availableCategories, setAvailableCategories] = useState<LicenseCategory[]>([]);
  const [loading, setLoading] = useState(false);

  const licenseTypeId = params.licenseTypeId as string;

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/customer/auth/login');
      return;
    }

    // Debug logging
    console.log('License Type Page Debug:', {
      licenseTypeId,
      licenseTypesCount: licenseTypes?.length,
      licenseLoading,
      authLoading,
      isAuthenticated
    });

    // Only process if we have license types data and not loading
    if (licenseTypes && licenseTypes.length > 0 && licenseTypeId && !licenseLoading) {
      console.log('Looking for license type:', licenseTypeId);
      console.log('Available license types:', licenseTypes.map(lt => ({ id: lt.license_type_id, name: lt.name })));

      const licenseType = licenseTypes.find(lt => lt.license_type_id === licenseTypeId);
      if (licenseType) {
        console.log('Found license type:', licenseType);
        setSelectedLicenseType(licenseType);
        // Get categories for this license type
        const typeCategories = getCategoriesByType(licenseTypeId);
        console.log('Categories for type:', typeCategories);
        setAvailableCategories(typeCategories);
      } else {
        console.warn('License type not found for ID:', licenseTypeId);
        // Only redirect if we're sure the data has loaded and the type doesn't exist
        if (!licenseLoading) {
          console.log('Redirecting back to applications');
          router.push('/customer/applications');
        }
      }
    } else if (licenseTypes && licenseTypes.length === 0 && !licenseLoading) {
      // No license types available
      console.warn('No license types available');
      router.push('/customer/applications');
    }
  }, [isAuthenticated, authLoading, router, licenseTypes, licenseTypeId, licenseLoading, getCategoriesByType]);

  const handleCategorySelect = (category: LicenseCategory) => {
    setLoading(true);

    // Navigate to the application form using query parameters
    router.push(`/customer/applications/apply/applicant-info?license_category_id=${category.license_category_id}`);
  };

  const handleBackToApplications = () => {
    router.push('/customer/applications');
  };

  const getCategoryIcon = (categoryName: string) => {
    const nameLower = categoryName.toLowerCase();
    
    if (nameLower.includes('postal') || nameLower.includes('mail')) {
      return {
        icon: 'ri-mail-line',
        iconBg: 'bg-blue-100',
        iconColor: 'text-blue-600'
      };
    } else if (nameLower.includes('international')) {
      return {
        icon: 'ri-global-line',
        iconBg: 'bg-purple-100',
        iconColor: 'text-purple-600'
      };
    } else if (nameLower.includes('domestic')) {
      return {
        icon: 'ri-truck-line',
        iconBg: 'bg-green-100',
        iconColor: 'text-green-600'
      };
    } else if (nameLower.includes('district')) {
      return {
        icon: 'ri-map-pin-line',
        iconBg: 'bg-orange-100',
        iconColor: 'text-orange-600'
      };
    } else if (nameLower.includes('telecom') || nameLower.includes('spectrum')) {
      return {
        icon: 'ri-signal-tower-line',
        iconBg: 'bg-indigo-100',
        iconColor: 'text-indigo-600'
      };
    } else if (nameLower.includes('standard') || nameLower.includes('approval')) {
      return {
        icon: 'ri-shield-check-line',
        iconBg: 'bg-emerald-100',
        iconColor: 'text-emerald-600'
      };
    } else {
      return {
        icon: 'ri-file-text-line',
        iconBg: 'bg-gray-100',
        iconColor: 'text-gray-600'
      };
    }
  };

  // Show loading state while data is being fetched
  if (authLoading || licenseLoading || !licenseTypes) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading license categories...</p>
            {process.env.NODE_ENV === 'development' && (
              <div className="mt-4 text-xs text-gray-500">
                <p>Auth Loading: {authLoading ? 'Yes' : 'No'}</p>
                <p>License Loading: {licenseLoading ? 'Yes' : 'No'}</p>
                <p>License Types: {licenseTypes?.length || 0}</p>
                <p>License Type ID: {licenseTypeId}</p>
              </div>
            )}
          </div>
        </div>
      </CustomerLayout>
    );
  }

  if (!selectedLicenseType) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="text-red-600 dark:text-red-400 mb-4">
              <i className="ri-error-warning-line text-4xl"></i>
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              License Type Not Found
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              The requested license type could not be found.
            </p>
            <button
              onClick={handleBackToApplications}
              className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary-dark transition-colors"
            >
              Back to License Types
            </button>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  // Use optimized license type step configuration
  const config = getLicenseTypeStepConfig(selectedLicenseType.code || selectedLicenseType.license_type_id);

  return (
    <CustomerLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">


        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <button
              onClick={handleBackToApplications}
              className="mr-4 p-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              title="Back to License Types"
            >
              <i className="ri-arrow-left-line text-xl"></i>
            </button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {selectedLicenseType.name}
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Select a license category to begin your application
              </p>
            </div>
          </div>
          
          {/* License Type Info */}
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <div className="flex items-start">
              <i className="ri-information-line text-blue-600 dark:text-blue-400 text-lg mr-3 mt-0.5"></i>
              <div className="flex-1">
                <h3 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-1">
                  License Information
                </h3>
                <p className="text-blue-700 dark:text-blue-300 text-sm mb-2">
                  {selectedLicenseType.description || 'No description available'}
                </p>
                {config && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium text-blue-900 dark:text-blue-100">Estimated Time: </span>
                      <span className="text-blue-700 dark:text-blue-300">{config.estimatedTime}</span>
                    </div>
                    <div>
                      <span className="font-medium text-blue-900 dark:text-blue-100">Steps: </span>
                      <span className="text-blue-700 dark:text-blue-300">{config.steps.length} steps</span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Categories Grid */}
        {availableCategories.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {availableCategories.map((category) => {
              const iconData = getCategoryIcon(category.name);
              return (
                <div
                  key={category.license_category_id}
                  className="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg hover:border-primary group"
                >
                  <div className="p-6">
                    {/* Category Icon */}
                    <div className="flex items-center justify-between mb-4">
                      <div className={`w-12 h-12 ${iconData.iconBg} rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                        <i className={`${iconData.icon} text-xl ${iconData.iconColor}`}></i>
                      </div>
                      <i className="ri-arrow-right-line text-gray-400 group-hover:text-primary transition-colors duration-300"></i>
                    </div>

                    {/* Category Name */}
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2 group-hover:text-primary transition-colors duration-300">
                      {category.name}
                    </h3>

                    {/* Category Description */}
                    <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-3">
                      {category.description || 'No description available'}
                    </p>

                    {/* Fee Information */}
                    <div className="flex items-center justify-between mb-4">
                      <span className="text-sm text-gray-500 dark:text-gray-400">Application Fee:</span>
                      <span className="font-semibold text-primary">
                        {category.fee ? `MWK ${category.fee}` : 'Contact MACRA'}
                      </span>
                    </div>

                    {/* Apply Button */}
                    <button
                      onClick={() => handleCategorySelect(category)}
                      disabled={loading}
                      className="w-full bg-primary text-white py-2 px-4 rounded-lg font-medium hover:bg-primary-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                    >
                      {loading ? (
                        <>
                          <i className="ri-loader-4-line animate-spin mr-2"></i>
                          Loading...
                        </>
                      ) : (
                        <>
                          <i className="ri-file-add-line mr-2"></i>
                          Apply Now
                        </>
                      )}
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-gray-100 dark:bg-gray-800 mb-4">
              <i className="ri-file-list-line text-gray-400 dark:text-gray-500 text-xl"></i>
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              No Categories Available
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              There are currently no license categories available for this license type.
            </p>
            <button
              onClick={handleBackToApplications}
              className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
            >
              <i className="ri-arrow-left-line mr-2"></i>
              Back to License Types
            </button>
          </div>
        )}

        {/* Requirements Section */}
        {config && config.requirements.length > 0 && (
          <div className="mt-12 bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              <i className="ri-file-list-3-line mr-2"></i>
              Required Documents
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {config.requirements.map((requirement, index) => (
                <div key={index} className="flex items-start">
                  <i className="ri-checkbox-line text-primary mr-2 mt-0.5"></i>
                  <span className="text-sm text-gray-700 dark:text-gray-300">{requirement}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </CustomerLayout>
  );
};

export default LicenseCategorySelectionPage;
