{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/app/applications/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { useAuth } from '../../contexts/AuthContext';\r\nimport { useRouter } from 'next/navigation';\r\n\r\ninterface Application {\r\n  id: string;\r\n  applicant: string;\r\n  type: string;\r\n  status: string;\r\n  priority: string;\r\n  submittedDate: string;\r\n}\r\n\r\nexport default function ApplicationsPage() {\r\n  const { user } = useAuth();\r\n  const router = useRouter();\r\n  const [applications] = useState<Application[]>([]);\r\n  const [loading] = useState(true);\r\n  const isAdmin = user?.isAdmin\r\n\r\n  useEffect(() => {\r\n    // Redirect to default license type (telecommunications) if no specific type is selected\r\n    router.push('/applications/telecommunications');\r\n  }, [router]);\r\n\r\n  const getStatusBadge = (status: string) => {\r\n    const statusClasses = {\r\n      'Under Review': 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200',\r\n      'Pending Documents': 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200',\r\n      'Approved': 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200',\r\n      'Rejected': 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200',\r\n    };\r\n\r\n    return (\r\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusClasses[status as keyof typeof statusClasses] || statusClasses['Under Review']}`}>\r\n        {status}\r\n      </span>\r\n    );\r\n  };\r\n\r\n  const getPriorityBadge = (priority: string) => {\r\n    const priorityClasses = {\r\n      'High': 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200',\r\n      'Medium': 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200',\r\n      'Low': 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200',\r\n    };\r\n\r\n    return (\r\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${priorityClasses[priority as keyof typeof priorityClasses] || priorityClasses['Medium']}`}>\r\n        {priority}\r\n      </span>\r\n    );\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex items-center justify-center h-64\">\r\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600\"></div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"p-6\">\r\n      <div className=\"mb-6\">\r\n        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0\">\r\n          <div>\r\n            <h1 className=\"text-2xl font-semibold text-gray-900\">Applications</h1>\r\n            <p className=\"mt-1 text-sm text-gray-500\">\r\n              {isAdmin ? 'Manage all license applications and their status' : 'View and track your applications'}\r\n            </p>\r\n          </div>\r\n\r\n        </div>\r\n      </div>\r\n\r\n      {/* Applications Table */}\r\n      <div className=\"bg-white shadow overflow-hidden sm:rounded-md\">\r\n        <div className=\"overflow-x-auto\">\r\n          <table className=\"min-w-full divide-y divide-gray-200\">\r\n            <thead className=\"bg-gray-50\">\r\n              <tr>\r\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                  Application\r\n                </th>\r\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                  Applicant\r\n                </th>\r\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                  Type\r\n                </th>\r\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                  Status\r\n                </th>\r\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                  Priority\r\n                </th>\r\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                  Actions\r\n                </th>\r\n              </tr>\r\n            </thead>\r\n            <tbody className=\"bg-white divide-y divide-gray-200\">\r\n              {applications.map((application: Application) => (\r\n                <tr key={application.id} className=\"hover:bg-gray-50\">\r\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                    <div className=\"flex items-center\">\r\n                      <div className=\"h-10 w-10 flex-shrink-0\">\r\n                        <div className=\"h-10 w-10 rounded-full bg-red-600 flex items-center justify-center\">\r\n                          <i className=\"ri-file-text-line text-white\"></i>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"ml-4\">\r\n                        <div className=\"text-sm font-medium text-gray-900\">\r\n                          {application.id}\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                    <div className=\"text-sm text-gray-900\">{application.applicant}</div>\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                    <div className=\"text-sm text-gray-900\">{application.type}</div>\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                    {getStatusBadge(application.status)}\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                    {getPriorityBadge(application.priority)}\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                    {new Date(application.submittedDate).toLocaleDateString()}\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\r\n                    <div className=\"flex items-center justify-end space-x-2\">\r\n                      <button className=\"text-indigo-600 hover:text-indigo-900 p-1 rounded hover:bg-indigo-50\" title=\"View Application\">\r\n                        <i className=\"ri-eye-line\"></i>\r\n                      </button>\r\n                      {(isAdmin) && (\r\n                        <button type=\"button\" className=\"text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-50\" title=\"Edit Application\">\r\n                          <i className=\"ri-edit-line\"></i>\r\n                        </button>\r\n                      )}\r\n                      {isAdmin && (\r\n                        <button type=\"button\" className=\"text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50\" title=\"Delete Application\">\r\n                          <i className=\"ri-delete-bin-line\"></i>\r\n                        </button>\r\n                      )}\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n              ))}\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      </div>\r\n\r\n      {applications.length === 0 && (\r\n        <div className=\"text-center py-12\">\r\n          <i className=\"ri-file-text-line text-4xl text-gray-400 mb-4\"></i>\r\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No applications found</h3>\r\n          <p className=\"text-gray-500\">No applications are available at the moment.</p>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAee,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IACjD,MAAM,CAAC,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3B,MAAM,UAAU,MAAM;IAEtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wFAAwF;QACxF,OAAO,IAAI,CAAC;IACd,GAAG;QAAC;KAAO;IAEX,MAAM,iBAAiB,CAAC;QACtB,MAAM,gBAAgB;YACpB,gBAAgB;YAChB,qBAAqB;YACrB,YAAY;YACZ,YAAY;QACd;QAEA,qBACE,8OAAC;YAAK,WAAW,CAAC,wEAAwE,EAAE,aAAa,CAAC,OAAqC,IAAI,aAAa,CAAC,eAAe,EAAE;sBAC/K;;;;;;IAGP;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,kBAAkB;YACtB,QAAQ;YACR,UAAU;YACV,OAAO;QACT;QAEA,qBACE,8OAAC;YAAK,WAAW,CAAC,wEAAwE,EAAE,eAAe,CAAC,SAAyC,IAAI,eAAe,CAAC,SAAS,EAAE;sBACjL;;;;;;IAGP;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,8OAAC;gCAAE,WAAU;0CACV,UAAU,qDAAqD;;;;;;;;;;;;;;;;;;;;;;0BAQxE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAM,WAAU;;0CACf,8OAAC;gCAAM,WAAU;0CACf,cAAA,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,8OAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,8OAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,8OAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,8OAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,8OAAC;4CAAG,WAAU;sDAAiF;;;;;;;;;;;;;;;;;0CAKnG,8OAAC;gCAAM,WAAU;0CACd,aAAa,GAAG,CAAC,CAAC,4BACjB,8OAAC;wCAAwB,WAAU;;0DACjC,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAE,WAAU;;;;;;;;;;;;;;;;sEAGjB,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;0EACZ,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;0DAKvB,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAI,WAAU;8DAAyB,YAAY,SAAS;;;;;;;;;;;0DAE/D,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAI,WAAU;8DAAyB,YAAY,IAAI;;;;;;;;;;;0DAE1D,8OAAC;gDAAG,WAAU;0DACX,eAAe,YAAY,MAAM;;;;;;0DAEpC,8OAAC;gDAAG,WAAU;0DACX,iBAAiB,YAAY,QAAQ;;;;;;0DAExC,8OAAC;gDAAG,WAAU;0DACX,IAAI,KAAK,YAAY,aAAa,EAAE,kBAAkB;;;;;;0DAEzD,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAO,WAAU;4DAAuE,OAAM;sEAC7F,cAAA,8OAAC;gEAAE,WAAU;;;;;;;;;;;wDAEb,yBACA,8OAAC;4DAAO,MAAK;4DAAS,WAAU;4DAAoE,OAAM;sEACxG,cAAA,8OAAC;gEAAE,WAAU;;;;;;;;;;;wDAGhB,yBACC,8OAAC;4DAAO,MAAK;4DAAS,WAAU;4DAA8D,OAAM;sEAClG,cAAA,8OAAC;gEAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;uCA1Cd,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;YAsDhC,aAAa,MAAM,KAAK,mBACvB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;;;;;;kCACb,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;AAKvC", "debugId": null}}]}