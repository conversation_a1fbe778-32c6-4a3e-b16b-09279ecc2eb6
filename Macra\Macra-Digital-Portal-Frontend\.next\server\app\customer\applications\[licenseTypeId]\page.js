(()=>{var e={};e.id=6020,e.ids=[6020],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10461:(e,r,t)=>{Promise.resolve().then(t.bind(t,92074))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},15680:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var s=t(60687),i=t(43210),a=t(16189),n=t(94391),l=t(63213),o=t(98374),c=t(25011);let d=()=>{let e=(0,a.useRouter)(),r=(0,a.useParams)(),{isAuthenticated:t,loading:d}=(0,l.A)(),{licenseTypes:x,categories:m,loading:p,getCategoriesByType:u}=(0,o.r2)(),[g,h]=(0,i.useState)(null),[b,y]=(0,i.useState)([]),[f,j]=(0,i.useState)(!1),v=r.licenseTypeId;(0,i.useEffect)(()=>{if(!d&&!t)return void e.push("/customer/auth/login");if(x&&x.length>0&&v&&!p){let r=x.find(e=>e.license_type_id===v);r?(h(r),y(u(v))):p||e.push("/customer/applications")}else x&&0===x.length&&!p&&e.push("/customer/applications")},[t,d,e,x,v,p,u]);let N=r=>{j(!0),e.push(`/customer/applications/apply/applicant-info?license_category_id=${r.license_category_id}`)},k=()=>{e.push("/customer/applications")},P=e=>{let r=e.toLowerCase();if(r.includes("postal")||r.includes("mail"))return{icon:"ri-mail-line",iconBg:"bg-blue-100",iconColor:"text-blue-600"};if(r.includes("international"))return{icon:"ri-global-line",iconBg:"bg-purple-100",iconColor:"text-purple-600"};if(r.includes("domestic"))return{icon:"ri-truck-line",iconBg:"bg-green-100",iconColor:"text-green-600"};if(r.includes("district"))return{icon:"ri-map-pin-line",iconBg:"bg-orange-100",iconColor:"text-orange-600"};if(r.includes("telecom")||r.includes("spectrum"))return{icon:"ri-signal-tower-line",iconBg:"bg-indigo-100",iconColor:"text-indigo-600"};else if(r.includes("standard")||r.includes("approval"))return{icon:"ri-shield-check-line",iconBg:"bg-emerald-100",iconColor:"text-emerald-600"};else return{icon:"ri-file-text-line",iconBg:"bg-gray-100",iconColor:"text-gray-600"}};if(d||p||!x)return(0,s.jsx)(n.A,{children:(0,s.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading license categories..."}),!1]})})});if(!g)return(0,s.jsx)(n.A,{children:(0,s.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-red-600 dark:text-red-400 mb-4",children:(0,s.jsx)("i",{className:"ri-error-warning-line text-4xl"})}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"License Type Not Found"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:"The requested license type could not be found."}),(0,s.jsx)("button",{onClick:k,className:"bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary-dark transition-colors",children:"Back to License Types"})]})})});let w=(0,c.QE)(g.code||g.license_type_id);return(0,s.jsx)(n.A,{children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[(0,s.jsx)("button",{onClick:k,className:"mr-4 p-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors",title:"Back to License Types",children:(0,s.jsx)("i",{className:"ri-arrow-left-line text-xl"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:g.name}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Select a license category to begin your application"})]})]}),(0,s.jsx)("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("i",{className:"ri-information-line text-blue-600 dark:text-blue-400 text-lg mr-3 mt-0.5"}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-blue-900 dark:text-blue-100 mb-1",children:"License Information"}),(0,s.jsx)("p",{className:"text-blue-700 dark:text-blue-300 text-sm mb-2",children:g.description||"No description available"}),w&&(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium text-blue-900 dark:text-blue-100",children:"Estimated Time: "}),(0,s.jsx)("span",{className:"text-blue-700 dark:text-blue-300",children:w.estimatedTime})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium text-blue-900 dark:text-blue-100",children:"Steps: "}),(0,s.jsxs)("span",{className:"text-blue-700 dark:text-blue-300",children:[w.steps.length," steps"]})]})]})]})]})})]}),b.length>0?(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:b.map(e=>{let r=P(e.name);return(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg hover:border-primary group",children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("div",{className:`w-12 h-12 ${r.iconBg} rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300`,children:(0,s.jsx)("i",{className:`${r.icon} text-xl ${r.iconColor}`})}),(0,s.jsx)("i",{className:"ri-arrow-right-line text-gray-400 group-hover:text-primary transition-colors duration-300"})]}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2 group-hover:text-primary transition-colors duration-300",children:e.name}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-3",children:e.description||"No description available"}),(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Application Fee:"}),(0,s.jsx)("span",{className:"font-semibold text-primary",children:e.fee?`MWK ${e.fee}`:"Contact MACRA"})]}),(0,s.jsx)("button",{onClick:()=>N(e),disabled:f,className:"w-full bg-primary text-white py-2 px-4 rounded-lg font-medium hover:bg-primary-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center",children:f?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"ri-loader-4-line animate-spin mr-2"}),"Loading..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"ri-file-add-line mr-2"}),"Apply Now"]})})]})},e.license_category_id)})}):(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-gray-100 dark:bg-gray-800 mb-4",children:(0,s.jsx)("i",{className:"ri-file-list-line text-gray-400 dark:text-gray-500 text-xl"})}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"No Categories Available"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"There are currently no license categories available for this license type."}),(0,s.jsxs)("button",{onClick:k,className:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600",children:[(0,s.jsx)("i",{className:"ri-arrow-left-line mr-2"}),"Back to License Types"]})]}),w&&w.requirements.length>0&&(0,s.jsxs)("div",{className:"mt-12 bg-gray-50 dark:bg-gray-800 rounded-lg p-6",children:[(0,s.jsxs)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4",children:[(0,s.jsx)("i",{className:"ri-file-list-3-line mr-2"}),"Required Documents"]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:w.requirements.map((e,r)=>(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("i",{className:"ri-checkbox-line text-primary mr-2 mt-0.5"}),(0,s.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:e})]},r))})]})]})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},51402:(e,r,t)=>{Promise.resolve().then(t.bind(t,15680))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61133:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>m,tree:()=>c});var s=t(65239),i=t(48088),a=t(88170),n=t.n(a),l=t(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(r,o);let c={children:["",{children:["customer",{children:["applications",{children:["[licenseTypeId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,92074)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\[licenseTypeId]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,68736)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\[licenseTypeId]\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/customer/applications/[licenseTypeId]/page",pathname:"/customer/applications/[licenseTypeId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},92074:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\applications\\\\[licenseTypeId]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\[licenseTypeId]\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,7498,1658,5814,2335,6893,8374],()=>t(61133));module.exports=s})();