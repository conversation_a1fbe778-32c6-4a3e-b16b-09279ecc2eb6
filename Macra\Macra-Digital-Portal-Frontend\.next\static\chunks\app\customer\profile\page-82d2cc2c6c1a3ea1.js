(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1741],{35695:(e,r,a)=>{"use strict";var s=a(18999);a.o(s,"useParams")&&a.d(r,{useParams:function(){return s.useParams}}),a.o(s,"usePathname")&&a.d(r,{usePathname:function(){return s.usePathname}}),a.o(s,"useRouter")&&a.d(r,{useRouter:function(){return s.useRouter}}),a.o(s,"useSearchParams")&&a.d(r,{useSearchParams:function(){return s.useSearchParams}})},49449:(e,r,a)=>{Promise.resolve().then(a.bind(a,93883))},63956:(e,r,a)=>{"use strict";a.d(r,{A:()=>d});var s=a(95155);let t=(0,a(12115).forwardRef)((e,r)=>{let{label:a,error:t,helperText:d,variant:l="default",fullWidth:i=!0,className:n="",required:o,disabled:c,options:m,children:x,...g}=e,u="px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white transition-colors duration-200 ".concat(i?"w-full":""," ").concat("small"===l?"py-1.5 text-sm":"py-2"),y="".concat(u," ").concat(t?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"," ").concat(c?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""," ").concat(n);return(0,s.jsxs)("div",{className:"w-full",children:[a&&(0,s.jsxs)("label",{className:"block font-medium text-gray-700 dark:text-gray-300 mb-2 ".concat("small"===l?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"),children:[a,o&&(0,s.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,s.jsx)("select",{ref:r,className:y,disabled:c,required:o,...g,children:m?m.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value)):x}),t&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:t}),d&&!t&&(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:d})]})});t.displayName="Select";let d=t},89807:(e,r,a)=>{"use strict";a.d(r,{D:()=>i,N:()=>l});var s=a(95155),t=a(12115);let d=(0,t.createContext)();function l(e){let{children:r}=e,[a,l]=(0,t.useState)("system"),[i,n]=(0,t.useState)("light"),[o,c]=(0,t.useState)(!1);(0,t.useEffect)(()=>{c(!0)},[]),(0,t.useEffect)(()=>{if(!o)return;let e=localStorage.getItem("theme");e&&["light","dark","system"].includes(e)?l(e):l("system")},[o]),(0,t.useEffect)(()=>{if(!o)return;let e=()=>{"system"===a?n(window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"):n(a)};if(e(),"system"===a){let r=window.matchMedia("(prefers-color-scheme: dark)");return r.addEventListener("change",e),()=>r.removeEventListener("change",e)}},[a,o]),(0,t.useEffect)(()=>{o&&("dark"===i?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark"))},[i,o]);let m=e=>{l(e),o&&localStorage.setItem("theme",e)};return o?(0,s.jsx)(d.Provider,{value:{theme:a,resolvedTheme:i,setTheme:m,toggleTheme:()=>{m("light"===i?"dark":"light")}},children:r}):(0,s.jsx)(d.Provider,{value:{theme:"light",resolvedTheme:"light",setTheme:()=>{},toggleTheme:()=>{}},children:r})}function i(){let e=(0,t.useContext)(d);if(!e)throw Error("useTheme must be used within a ThemeProvider");return e}},93883:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>c});var s=a(95155),t=a(12115),d=a(66766),l=a(73209),i=a(40283),n=a(89807),o=a(63956);let c=()=>{let{user:e}=(0,i.A)(),{theme:r,setTheme:a}=(0,n.D)(),[c,m]=(0,t.useState)("profile"),[x,g]=(0,t.useState)(!1),[u,y]=(0,t.useState)({firstName:(null==e?void 0:e.first_name)||"",lastName:(null==e?void 0:e.last_name)||"",email:(null==e?void 0:e.email)||"",phone:(null==e?void 0:e.phone)||"",organizationName:"",address:"",city:"",country:"Malawi"}),[h,b]=(0,t.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[p,f]=(0,t.useState)(!1),[k,j]=(0,t.useState)({type:"",text:""}),N=e=>{let{name:r,value:a}=e.target;y(e=>({...e,[r]:a}))},v=e=>{let{name:r,value:a}=e.target;b(e=>({...e,[r]:a}))},w=async e=>{e.preventDefault(),f(!0),j({type:"",text:""});try{await new Promise(e=>setTimeout(e,1e3)),j({type:"success",text:"Profile updated successfully!"}),g(!1)}catch(e){j({type:"error",text:"Failed to update profile. Please try again."})}finally{f(!1)}},P=async e=>{if(e.preventDefault(),h.newPassword!==h.confirmPassword)return void j({type:"error",text:"New passwords do not match."});f(!0),j({type:"",text:""});try{await new Promise(e=>setTimeout(e,1e3)),j({type:"success",text:"Password changed successfully!"}),b({currentPassword:"",newPassword:"",confirmPassword:""})}catch(e){j({type:"error",text:"Failed to change password. Please try again."})}finally{f(!1)}};return(0,s.jsx)(l.A,{children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:"My Profile"}),(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Manage your account settings and preferences"})]}),(0,s.jsx)("div",{className:"flex items-center space-x-3",children:(0,s.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",children:[(0,s.jsx)("i",{className:"ri-check-line mr-1"}),"Verified Account"]})})]})}),(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow mb-6",children:(0,s.jsx)("div",{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(d.default,{className:"h-20 w-20 rounded-full object-cover ring-4 ring-white dark:ring-gray-800",src:(null==e?void 0:e.profile_image)||"https://banner2.cleanpng.com/********/sac/9128e319522f66aba9edf95180a86e7e.webp",alt:"Profile",width:80,height:80}),(0,s.jsx)("button",{className:"absolute bottom-0 right-0 bg-primary hover:bg-red-700 text-white rounded-full p-1.5 shadow-lg transition-colors",title:"Change profile picture","aria-label":"Change profile picture",children:(0,s.jsx)("i",{className:"ri-camera-line text-sm"})})]})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-gray-100",children:e?"".concat(e.last_name," ").concat(e.last_name):"Customer Name"}),(0,s.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400"}),(0,s.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:(null==e?void 0:e.email)||"<EMAIL>"}),(0,s.jsx)("div",{className:"mt-2 flex items-center space-x-4",children:(0,s.jsxs)("span",{className:"inline-flex items-center text-sm text-gray-500 dark:text-gray-400",children:[(0,s.jsx)("i",{className:"ri-calendar-line mr-1"}),"Member since ",new Date().getFullYear()-1]})})]})]})})}),(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow mb-6",children:[(0,s.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-700",children:(0,s.jsx)("nav",{className:"-mb-px flex space-x-8 px-6",children:[{id:"profile",name:"Profile Information",icon:"ri-user-line"},{id:"security",name:"Security",icon:"ri-shield-line"},{id:"preferences",name:"Preferences",icon:"ri-settings-line"}].map(e=>(0,s.jsxs)("button",{onClick:()=>m(e.id),className:"py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ".concat(c===e.id?"border-red-500 text-red-600 dark:text-red-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"),children:[(0,s.jsx)("i",{className:"".concat(e.icon," mr-2")}),e.name]},e.id))})}),(0,s.jsxs)("div",{className:"p-6",children:[k.text&&(0,s.jsx)("div",{className:"mb-6 rounded-md p-4 border-l-4 ".concat("success"===k.type?"bg-green-50 dark:bg-green-900/20 border-green-400 dark:border-green-600":"bg-red-50 dark:bg-red-900/20 border-red-400 dark:border-red-600"),children:(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("i",{className:"".concat("success"===k.type?"ri-check-line text-green-400 dark:text-green-500":"ri-error-warning-line text-red-400 dark:text-red-500"," text-lg")})}),(0,s.jsx)("div",{className:"ml-3",children:(0,s.jsx)("p",{className:"text-sm ".concat("success"===k.type?"text-green-800 dark:text-green-300":"text-red-800 dark:text-red-300"),children:k.text})})]})}),"profile"===c&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Personal Information"}),(0,s.jsxs)("button",{onClick:()=>g(!x),className:"inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors",children:[(0,s.jsx)("i",{className:"".concat(x?"ri-close-line":"ri-edit-line"," mr-2")}),x?"Cancel":"Edit Profile"]})]}),(0,s.jsxs)("form",{onSubmit:w,children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"First Name *"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"text",name:"firstName",value:u.firstName,onChange:N,disabled:!x,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500",placeholder:"Enter your first name"}),(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)("i",{className:"ri-user-line text-gray-400 dark:text-gray-500"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Last Name *"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"text",name:"lastName",value:u.lastName,onChange:N,disabled:!x,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500",placeholder:"Enter your last name"}),(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)("i",{className:"ri-user-line text-gray-400 dark:text-gray-500"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Email Address *"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"email",name:"email",value:u.email,onChange:N,disabled:!x,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500",placeholder:"Enter your email address"}),(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)("i",{className:"ri-mail-line text-gray-400 dark:text-gray-500"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Phone Number *"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"tel",name:"phone",value:u.phone,onChange:N,disabled:!x,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500",placeholder:"Enter your phone number"}),(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)("i",{className:"ri-phone-line text-gray-400 dark:text-gray-500"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Organization Name *"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"text",name:"organizationName",value:u.organizationName,onChange:N,disabled:!x,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500",placeholder:"Enter your organization name"}),(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)("i",{className:"ri-building-line text-gray-400 dark:text-gray-500"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"City"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"text",name:"city",value:u.city,onChange:N,disabled:!x,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500",placeholder:"Enter your city"}),(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)("i",{className:"ri-map-pin-line text-gray-400 dark:text-gray-500"})})]})]})]}),(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Address"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("textarea",{name:"address",rows:3,value:u.address,onChange:N,disabled:!x,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500",placeholder:"Enter your full address"}),(0,s.jsx)("div",{className:"absolute top-3 left-0 pl-3 flex items-start pointer-events-none",children:(0,s.jsx)("i",{className:"ri-home-line text-gray-400 dark:text-gray-500"})})]})]}),x&&(0,s.jsxs)("div",{className:"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700",children:[(0,s.jsx)("button",{type:"button",onClick:()=>g(!1),className:"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors",children:"Cancel"}),(0,s.jsx)("button",{type:"submit",disabled:p,className:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:p?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"ri-loader-4-line animate-spin mr-2"}),"Saving..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"ri-save-line mr-2"}),"Save Changes"]})})]})]})]}),"security"===c&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Security Settings"}),(0,s.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-6",children:[(0,s.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Change Password"}),(0,s.jsxs)("form",{onSubmit:P,className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Current Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"password",name:"currentPassword",value:h.currentPassword,onChange:v,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500",placeholder:"Enter current password"}),(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)("i",{className:"ri-lock-line text-gray-400 dark:text-gray-500"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"New Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"password",name:"newPassword",value:h.newPassword,onChange:v,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500",placeholder:"Enter new password"}),(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)("i",{className:"ri-lock-line text-gray-400 dark:text-gray-500"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Confirm New Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"password",name:"confirmPassword",value:h.confirmPassword,onChange:v,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500",placeholder:"Confirm new password"}),(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)("i",{className:"ri-lock-line text-gray-400 dark:text-gray-500"})})]})]}),(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsx)("button",{type:"submit",disabled:p,className:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:p?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"ri-loader-4-line animate-spin mr-2"}),"Updating..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"ri-shield-check-line mr-2"}),"Update Password"]})})})]})]}),(0,s.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6",children:[(0,s.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Account Security"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("i",{className:"ri-shield-check-line text-green-500 mr-3"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:"Two-Factor Authentication"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Add an extra layer of security"})]})]}),(0,s.jsx)("button",{className:"text-sm text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300",children:"Enable"})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("i",{className:"ri-smartphone-line text-blue-500 mr-3"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:"Login Notifications"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Get notified of new sign-ins"})]})]}),(0,s.jsx)("button",{className:"text-sm text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300",children:"Configure"})]})]})]})]}),"preferences"===c&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Account Preferences"}),(0,s.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-6",children:[(0,s.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Notification Preferences"}),(0,s.jsx)("div",{className:"space-y-4",children:[{id:"email_notifications",label:"Email Notifications",description:"Receive updates via email"},{id:"license_expiry",label:"License Expiry Alerts",description:"Get notified before licenses expire"},{id:"payment_reminders",label:"Payment Reminders",description:"Receive payment due notifications"},{id:"application_updates",label:"Application Updates",description:"Get updates on application status"}].map(e=>(0,s.jsxs)("div",{className:"flex items-start space-x-3 p-3 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors",children:[(0,s.jsx)("div",{className:"flex items-center h-5",children:(0,s.jsx)("input",{id:e.id,type:"checkbox",defaultChecked:!0,className:"h-4 w-4 text-primary focus:ring-primary border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700 dark:focus:ring-primary dark:ring-offset-gray-800","aria-describedby":"".concat(e.id,"-description")})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("label",{htmlFor:e.id,className:"text-sm font-medium text-gray-900 dark:text-gray-100 cursor-pointer",children:e.label}),(0,s.jsx)("p",{id:"".concat(e.id,"-description"),className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:e.description})]})]},e.id))})]}),(0,s.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-6",children:[(0,s.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Theme Settings"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Choose your preferred color scheme for the interface"}),(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-4",children:[{value:"light",label:"Light",description:"Use light theme",icon:"ri-sun-line"},{value:"dark",label:"Dark",description:"Use dark theme",icon:"ri-moon-line"},{value:"system",label:"System",description:"Follow system preference",icon:"ri-computer-line"}].map(e=>(0,s.jsxs)("button",{onClick:()=>a(e.value),className:"relative p-4 border-2 rounded-lg transition-all duration-200 ".concat(r===e.value?"border-red-500 bg-red-50 dark:bg-red-900/20":"border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500 bg-white dark:bg-gray-800"),children:[(0,s.jsxs)("div",{className:"flex flex-col items-center text-center",children:[(0,s.jsx)("div",{className:"w-10 h-10 rounded-lg flex items-center justify-center mb-3 ".concat(r===e.value?"bg-red-100 dark:bg-red-800 text-red-600 dark:text-red-400":"bg-gray-100 dark:bg-gray-600 text-gray-600 dark:text-gray-400"),children:(0,s.jsx)("i",{className:"".concat(e.icon," text-xl")})}),(0,s.jsx)("h5",{className:"text-sm font-medium ".concat(r===e.value?"text-red-900 dark:text-red-100":"text-gray-900 dark:text-gray-100"),children:e.label}),(0,s.jsx)("p",{className:"text-xs mt-1 ".concat(r===e.value?"text-red-700 dark:text-red-300":"text-gray-500 dark:text-gray-400"),children:e.description})]}),r===e.value&&(0,s.jsx)("div",{className:"absolute top-2 right-2",children:(0,s.jsx)("div",{className:"w-5 h-5 bg-red-500 rounded-full flex items-center justify-center",children:(0,s.jsx)("i",{className:"ri-check-line text-white text-xs"})})})]},e.value))})]})]}),(0,s.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-6",children:[(0,s.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Language & Region"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)(o.A,{label:"Language","aria-label":"Select language",children:[(0,s.jsx)("option",{children:"English"}),(0,s.jsx)("option",{children:"Chichewa"})]}),(0,s.jsxs)(o.A,{label:"Timezone","aria-label":"Select timezone",children:[(0,s.jsx)("option",{children:"Africa/Blantyre (CAT)"}),(0,s.jsx)("option",{children:"UTC"})]})]})]})]})]})]})]})})}}},e=>{var r=r=>e(e.s=r);e.O(0,[8122,6766,6874,283,3209,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>r(49449)),_N_E=e.O()}]);