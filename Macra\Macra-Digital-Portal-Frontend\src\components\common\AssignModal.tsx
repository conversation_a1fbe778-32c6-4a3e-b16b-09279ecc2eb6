'use client';

import React, { useState, useEffect } from 'react';
import { useToast } from '@/contexts/ToastContext';
import { taskAssignmentService } from '@/services/task-assignment';

interface Officer {
  user_id: string;
  first_name: string;
  last_name: string;
  email: string;
  department?: string;
}

interface AssignModalProps {
  isOpen: boolean;
  onClose: () => void;
  itemId: string | null;
  itemType: 'data_breach' | 'application' | 'complaint';
  itemTitle?: string;
  onAssignSuccess?: () => void;
}

const AssignModal: React.FC<AssignModalProps> = ({
  isOpen,
  onClose,
  itemId,
  itemType,
  itemTitle,
  onAssignSuccess
}) => {
  const { showSuccess, showError } = useToast();
  const [officers, setOfficers] = useState<Officer[]>([]);
  const [filteredOfficers, setFilteredOfficers] = useState<Officer[]>([]);
  const [loading, setLoading] = useState(false);
  const [assigning, setAssigning] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedOfficer, setSelectedOfficer] = useState<string>('');
  const [comment, setComment] = useState('');

  useEffect(() => {
    if (isOpen) {
      fetchOfficers();
      setSearchQuery('');
      setSelectedOfficer('');
      setComment('');
    }
  }, [isOpen]);

  useEffect(() => {
    // Filter officers based on search query
    if (searchQuery.trim() === '') {
      setFilteredOfficers(officers);
    } else {
      const filtered = officers.filter(officer =>
        `${officer.first_name} ${officer.last_name}`.toLowerCase().includes(searchQuery.toLowerCase()) ||
        officer.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        officer.department?.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredOfficers(filtered);
    }
  }, [officers, searchQuery]);

  const fetchOfficers = async () => {
    setLoading(true);
    try {
      const response = await taskAssignmentService.getOfficers();
      setOfficers(response.data || []);
    } catch (error) {
      console.error('Error fetching officers:', error);
      showError('Failed to load officers');
    } finally {
      setLoading(false);
    }
  };

  const handleAssign = async () => {
    if (!selectedOfficer || !itemId) {
      showError('Please select an officer');
      return;
    }

    setAssigning(true);
    try {
      // Create a task for the assignment
      await taskAssignmentService.assignTask(itemId, {
        assignedTo: selectedOfficer,
        comment: comment.trim() || undefined
      });

      showSuccess('Successfully assigned to officer');
      onAssignSuccess?.();
      onClose();
    } catch (error) {
      console.error('Error assigning task:', error);
      showError('Failed to assign task');
    } finally {
      setAssigning(false);
    }
  };

  const getSelectedOfficerDetails = () => {
    return officers.find(officer => officer.user_id === selectedOfficer);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div className="mt-3">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              Assign {itemType.replace('_', ' ').toUpperCase()}
            </h3>
            <button
              type="button"
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <i className="ri-close-line text-xl"></i>
            </button>
          </div>

          {/* Item Details */}
          {itemTitle && (
            <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                Item to Assign:
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">{itemTitle}</p>
            </div>
          )}

          {/* Search Officers */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Search Officers
            </label>
            <input
              type="text"
              placeholder="Search by name, email, or department..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
            />
          </div>

          {/* Officers List */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Select Officer ({filteredOfficers.length} found)
            </label>
            <div className="max-h-64 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md">
              {loading ? (
                <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                  Loading officers...
                </div>
              ) : filteredOfficers.length === 0 ? (
                <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                  No officers found
                </div>
              ) : (
                filteredOfficers.map((officer) => (
                  <div
                    key={officer.user_id}
                    className={`p-3 border-b border-gray-200 dark:border-gray-600 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 ${
                      selectedOfficer === officer.user_id ? 'bg-blue-50 dark:bg-blue-900' : ''
                    }`}
                    onClick={() => setSelectedOfficer(officer.user_id)}
                  >
                    <div className="flex items-center">
                      <input
                        type="radio"
                        name="officer"
                        value={officer.user_id}
                        checked={selectedOfficer === officer.user_id}
                        onChange={() => setSelectedOfficer(officer.user_id)}
                        className="mr-3"
                      />
                      <div className="flex-1">
                        <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {officer.first_name} {officer.last_name}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {officer.email}
                        </div>
                        {officer.department && (
                          <div className="text-xs text-gray-400 dark:text-gray-500">
                            {officer.department}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Comment */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Assignment Comment (Optional)
            </label>
            <textarea
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder="Add any notes or instructions for the assigned officer..."
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
            />
          </div>

          {/* Selected Officer Summary */}
          {selectedOfficer && (
            <div className="mb-6 p-4 bg-green-50 dark:bg-green-900 rounded-lg">
              <h4 className="text-sm font-medium text-green-900 dark:text-green-100 mb-2">
                Selected Officer:
              </h4>
              <div className="text-sm text-green-700 dark:text-green-200">
                {getSelectedOfficerDetails()?.first_name} {getSelectedOfficerDetails()?.last_name}
                <br />
                {getSelectedOfficerDetails()?.email}
                {getSelectedOfficerDetails()?.department && (
                  <>
                    <br />
                    Department: {getSelectedOfficerDetails()?.department}
                  </>
                )}
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleAssign}
              disabled={!selectedOfficer || assigning}
              className="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {assigning ? 'Assigning...' : 'Assign'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AssignModal;
