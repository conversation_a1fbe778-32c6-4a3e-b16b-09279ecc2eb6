(()=>{var e={};e.id=8731,e.ids=[8731],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10880:(e,t,r)=>{Promise.resolve().then(r.bind(r,35809))},12412:e=>{"use strict";e.exports=require("assert")},15139:(e,t,r)=>{Promise.resolve().then(r.bind(r,21750))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21750:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\audit-trail\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\audit-trail\\layout.tsx","default")},21820:e=>{"use strict";e.exports=require("os")},22145:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var a=r(60687);let s=(0,r(43210).forwardRef)(({label:e,error:t,helperText:r,variant:s="default",fullWidth:l=!0,className:o="",required:n,disabled:i,...d},u)=>{let c=`px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 ${l?"w-full":""} ${"small"===s?"py-1.5 text-sm":"py-2"}`,m=`${c} ${t?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"} ${i?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""} ${o}`,p=`block font-medium text-gray-700 dark:text-gray-300 mb-2 ${"small"===s?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"}`;return(0,a.jsxs)("div",{className:"w-full",children:[e&&(0,a.jsxs)("label",{className:p,children:[e,n&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,a.jsx)("input",{ref:u,className:m,disabled:i,required:n,...d}),t&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:t}),r&&!t&&(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:r})]})});s.displayName="TextInput";let l=s},27910:e=>{"use strict";e.exports=require("stream")},28165:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>m,tree:()=>d});var a=r(65239),s=r(48088),l=r(88170),o=r.n(l),n=r(30893),i={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>n[e]);r.d(t,i);let d={children:["",{children:["audit-trail",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,97037)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\audit-trail\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,21750)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\audit-trail\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\audit-trail\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/audit-trail/page",pathname:"/audit-trail",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35809:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>D});var a=r(60687),s=r(43210);let{Axios:l,AxiosError:o,CanceledError:n,isCancel:i,CancelToken:d,VERSION:u,all:c,Cancel:m,isAxiosError:p,spread:g,toFormData:x,AxiosHeaders:h,HttpStatusCode:b,formToJSON:y,getAdapter:f,mergeConfig:v}=r(51060).A;var w=r(12234);let j=new Map,k=3e5,A=new Map,_=new Map,P=(e,t)=>`audit_trail_${e}_${t?JSON.stringify(t):""}`,T=e=>{let t=Date.now(),r=(_.get(e)||[]).filter(e=>t-e<6e4);return r.length>=10||(r.push(t),_.set(e,r),!1)},N=e=>{let t=j.get(e);return t&&Date.now()-t.timestamp<t.ttl?t.data:(t&&j.delete(e),null)},M=(e,t,r=k)=>{j.set(e,{data:t,timestamp:Date.now(),ttl:r})};class S extends Error{constructor(e,t,r,a){super(e),this.code=t,this.status=r,this.details=a,this.name="AuditTrailError"}}let C=e=>{if(e instanceof o){let t=e.response?.status,r=e.response?.data?.message||e.message,a=e.code;if(401===t)throw new S("Authentication required","UNAUTHORIZED",t);if(403===t)throw new S("Access denied","FORBIDDEN",t);if(404===t)throw new S("Audit trail not found","NOT_FOUND",t);else if(429===t)throw new S("Too many requests","RATE_LIMITED",t);else if(t&&t>=500)throw new S("Server error occurred","SERVER_ERROR",t);else if("ERR_NETWORK"===a||"Network Error"===e.message)throw new S("Network error - please check your connection","NETWORK_ERROR");else throw new S(r||"An unexpected error occurred",a,t,e.response?.data)}throw new S(e.message||"An unexpected error occurred")};process.env.NEXT_PUBLIC_API_URL;let I={async getAuditTrails(e){let t="audit_trails_list",r=P("list",e);try{let a=N(r);if(a)return a;if(T(t))throw new S("Too many requests. Please wait before trying again.","RATE_LIMITED",429);let s=`${t}_${r}`;if(A.has(s))return await A.get(s);let l=(async()=>{let t=new URLSearchParams;e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.length>0&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.length>0&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(([e,r])=>{Array.isArray(r)?r.forEach(r=>t.append(`filter.${e}`,r)):t.set(`filter.${e}`,r)}),e.dateFrom&&t.set("dateFrom",e.dateFrom),e.dateTo&&t.set("dateTo",e.dateTo),e.userId&&t.set("userId",e.userId),e.action&&t.set("action",e.action),e.module&&t.set("module",e.module),e.status&&t.set("status",e.status),e.ipAddress&&t.set("ipAddress",e.ipAddress),e.resourceType&&t.set("resourceType",e.resourceType),e.resourceId&&t.set("resourceId",e.resourceId);let a=await w.Zl.get(`?${t.toString()}`);if(a.data&&"object"==typeof a.data){let e;return void 0!==a.data.success||a.data.data&&a.data.meta,e=a.data,M(r,e,12e4),e}throw new S("Invalid response format from server")})();A.set(s,l);try{return await l}finally{A.delete(s)}}catch(a){let e=`${t}_${r}`;A.delete(e),C(a)}},async getAuditTrail(e){let t=`audit_trail_${e}`,r=P("single",{id:e});try{let a=N(r);if(a)return a;if(T(t))throw new S("Too many requests. Please wait before trying again.","RATE_LIMITED",429);if(A.has(t))return await A.get(t);let s=(async()=>{let t=await w.Zl.get(`/${e}`);if(t.data&&"object"==typeof t.data){let e;return e=void 0!==t.data.success?t.data.data:t.data,M(r,e),e}throw new S("Invalid response format from server")})();A.set(t,s);try{return await s}finally{A.delete(t)}}catch(e){A.delete(t),C(e)}},async exportAuditTrails(e,t="csv"){try{let r=new URLSearchParams;e.search&&r.set("search",e.search),e.dateFrom&&r.set("dateFrom",e.dateFrom),e.dateTo&&r.set("dateTo",e.dateTo),e.userId&&r.set("userId",e.userId),e.action&&r.set("action",e.action),e.module&&r.set("module",e.module),e.status&&r.set("status",e.status),e.ipAddress&&r.set("ipAddress",e.ipAddress),e.resourceType&&r.set("resourceType",e.resourceType),e.resourceId&&r.set("resourceId",e.resourceId),r.set("format",t);let a=await w.Zl.get(`/export?${r.toString()}`,{responseType:"blob"});if(a.data instanceof Blob)return a.data;throw new S("Invalid export response format")}catch(e){C(e)}},async getAuditStats(e,t){try{let r=new URLSearchParams;e&&r.set("dateFrom",e),t&&r.set("dateTo",t);let a=await w.Zl.get(`/stats?${r.toString()}`);if(a.data&&"object"==typeof a.data)if(void 0!==a.data.success)return a.data.data;else return a.data;throw new S("Invalid stats response format")}catch(e){C(e)}},getActionOptions:()=>[{value:"login",label:"Login"},{value:"logout",label:"Logout"},{value:"create",label:"Create"},{value:"update",label:"Update"},{value:"delete",label:"Delete"},{value:"view",label:"View"},{value:"export",label:"Export"},{value:"import",label:"Import"}],getModuleOptions:()=>[{value:"authentication",label:"Authentication"},{value:"user_management",label:"User Management"},{value:"role_management",label:"Role Management"},{value:"permission_management",label:"Permission Management"},{value:"license_management",label:"License Management"},{value:"spectrum_management",label:"Spectrum Management"},{value:"transaction_management",label:"Transaction Management"},{value:"system_settings",label:"System Settings"}],getStatusOptions:()=>[{value:"success",label:"Success"},{value:"failure",label:"Failure"},{value:"warning",label:"Warning"}],getResourceTypeOptions:()=>[{value:"User",label:"User"},{value:"Role",label:"Role"},{value:"Permission",label:"Permission"},{value:"License",label:"License"},{value:"Spectrum",label:"Spectrum"},{value:"Transaction",label:"Transaction"},{value:"Authentication",label:"Authentication"}],formatAuditAction:e=>({login:"Login",logout:"Logout",create:"Create",update:"Update",delete:"Delete",view:"View",export:"Export",import:"Import"})[e]||e.charAt(0).toUpperCase()+e.slice(1),formatAuditModule:e=>({authentication:"Authentication",user_management:"User Management",role_management:"Role Management",permission_management:"Permission Management",license_management:"License Management",spectrum_management:"Spectrum Management",transaction_management:"Transaction Management",system_settings:"System Settings"})[e]||e.replace(/_/g," ").replace(/\b\w/g,e=>e.toUpperCase()),formatAuditStatus:e=>({success:{text:"Success",color:"green"},failure:{text:"Failed",color:"red"},warning:{text:"Warning",color:"yellow"}})[e]||{text:e.charAt(0).toUpperCase()+e.slice(1),color:"gray"},validateDateRange(e,t){if(!e&&!t)return{isValid:!0};if(e&&t){let r=new Date(e),a=new Date(t);if(r>a)return{isValid:!1,error:"Start date must be before end date"};if(a.getTime()-r.getTime()>31536e6)return{isValid:!1,error:"Date range cannot exceed 1 year"}}return{isValid:!0}},getUserDisplayName(e){if(e.user){let t=e.user.first_name||"",r=e.user.last_name||"";return`${t} ${r}`.trim()||e.user.email||"Unknown User"}return e.user_id||"System"},formatIpAddress:e=>e&&"unknown"!==e?e:"Unknown",hasChanges:e=>!!(e.old_values||e.new_values),getChangesSummary(e){let t=[];if(e.old_values&&e.new_values){let r=e.old_values,a=e.new_values;Object.keys(a).forEach(e=>{r[e]!==a[e]&&"updated_at"!==e&&t.push(`${e}: ${r[e]} → ${a[e]}`)})}return t},clearCache(){j.clear(),A.clear(),_.clear()},getCacheStats:()=>({cacheSize:j.size,pendingRequests:A.size,rateLimitEntries:_.size}),refreshData(e){if(e){let t=P("list",e);j.delete(t)}else for(let[e]of j)e.includes("audit_trail_list")&&j.delete(e)}};var R=r(70088),E=r(22145),$=r(86732);function D(){let[e,t]=(0,s.useState)(null),[r,l]=(0,s.useState)(!0),[o,n]=(0,s.useState)(null),[i,d]=(0,s.useState)({}),[u,c]=(0,s.useState)({page:1,limit:10}),m=(0,s.useCallback)(async e=>{try{l(!0),n(null),c(e);let r=await I.getAuditTrails({...e,...i});if(r&&r.data)t(r);else throw Error("Invalid response format")}catch(r){n(r instanceof Error?r.message:"Failed to load audit trail"),t({data:[],meta:{itemsPerPage:e.limit||10,totalItems:0,currentPage:e.page||1,totalPages:0,sortBy:[],searchBy:[],search:"",select:[]},links:{current:""}})}finally{l(!1)}},[i]),p=(e,t)=>{let r={...i};t&&""!==t.trim()?r[e]=t:delete r[e],d(r)},g=[{key:"created_at",label:"Date & Time",sortable:!0,render:e=>(0,a.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:e?new Date(e).toLocaleString():"N/A"})},{key:"user",label:"User",render:(e,t)=>(0,a.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:t.user?`${t.user.first_name} ${t.user.last_name}`:"System"})},{key:"action",label:"Action",sortable:!0,render:e=>(0,a.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 capitalize",children:e})},{key:"module",label:"Module",sortable:!0,render:e=>(0,a.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100 capitalize",children:e.replace(/_/g," ")})},{key:"resource_type",label:"Resource",render:e=>(0,a.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:e})},{key:"status",label:"Status",sortable:!0,render:e=>(0,a.jsx)("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full capitalize ${{success:"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200",failure:"bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200",warning:"bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200"}[e]||"bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200"}`,children:e})},{key:"ip_address",label:"IP Address",render:e=>(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400 font-mono",children:e||"N/A"})},{key:"description",label:"Description",render:e=>(0,a.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100 max-w-xs truncate",children:e||"N/A"})}];return(0,a.jsx)("div",{className:"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-4",children:(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg p-6",children:[o&&(0,a.jsx)("div",{className:"mb-6 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded",children:o}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4",children:"Filters"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsx)(E.A,{type:"date",label:"From Date",value:i.dateFrom||"",onChange:e=>p("dateFrom",e.target.value)}),(0,a.jsx)(E.A,{type:"date",label:"To Date",value:i.dateTo||"",onChange:e=>p("dateTo",e.target.value)}),(0,a.jsxs)($.A,{label:"Action Type",value:i.action||"",onChange:e=>p("action",e.target.value),children:[(0,a.jsx)("option",{value:"",children:"All Actions"}),I.getActionOptions().map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))]}),(0,a.jsxs)($.A,{label:"Module",value:i.module||"",onChange:e=>p("module",e.target.value),children:[(0,a.jsx)("option",{value:"",children:"All Modules"}),I.getModuleOptions().map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))]}),(0,a.jsx)(E.A,{type:"text",label:"IP Address",placeholder:"e.g. ***********",value:i.ipAddress||"",onChange:e=>p("ipAddress",e.target.value)}),(0,a.jsxs)($.A,{label:"Status",value:i.status||"",onChange:e=>p("status",e.target.value),children:[(0,a.jsx)("option",{value:"",children:"All Statuses"}),I.getStatusOptions().map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))]}),(0,a.jsxs)($.A,{label:"Resource Type",value:i.resourceType||"",onChange:e=>p("resourceType",e.target.value),children:[(0,a.jsx)("option",{value:"",children:"All Resources"}),I.getResourceTypeOptions().map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))]}),(0,a.jsx)(E.A,{type:"text",label:"Resource ID",placeholder:"Enter resource ID",value:i.resourceId||"",onChange:e=>p("resourceId",e.target.value)})]})]}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsx)(R.A,{columns:g,data:e,loading:r,onQueryChange:m,searchPlaceholder:"Search audit trail by description, user, or resource..."})})]})})}},47328:(e,t,r)=>{Promise.resolve().then(r.bind(r,97037))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70256:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var a=r(60687),s=r(43210),l=r(16189),o=r(63213),n=r(21891),i=r(60417);function d({children:e}){let{isAuthenticated:t,loading:r}=(0,o.A)();(0,l.useRouter)();let[d,u]=(0,s.useState)("overview"),[c,m]=(0,s.useState)(!1);return r?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"})}):t?(0,a.jsxs)("div",{className:"flex h-screen overflow-hidden",children:[(0,a.jsx)("div",{id:"mobileSidebarOverlay",className:`mobile-sidebar-overlay ${c?"show":""}`,onClick:()=>m(!1)}),(0,a.jsx)(i.default,{}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,a.jsx)(n.default,{activeTab:d,onTabChange:u,onMobileMenuToggle:()=>{m(!c)}}),e]})]}):null}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},80795:(e,t,r)=>{Promise.resolve().then(r.bind(r,70256))},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86732:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var a=r(60687);let s=(0,r(43210).forwardRef)(({label:e,error:t,helperText:r,variant:s="default",fullWidth:l=!0,className:o="",required:n,disabled:i,options:d,children:u,...c},m)=>{let p=`px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white transition-colors duration-200 ${l?"w-full":""} ${"small"===s?"py-1.5 text-sm":"py-2"}`,g=`${p} ${t?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"} ${i?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""} ${o}`,x=`block font-medium text-gray-700 dark:text-gray-300 mb-2 ${"small"===s?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"}`;return(0,a.jsxs)("div",{className:"w-full",children:[e&&(0,a.jsxs)("label",{className:x,children:[e,n&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,a.jsx)("select",{ref:m,className:g,disabled:i,required:n,...c,children:d?d.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value)):u}),t&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:t}),r&&!t&&(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:r})]})});s.displayName="Select";let l=s},94735:e=>{"use strict";e.exports=require("events")},97037:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\audit-trail\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\audit-trail\\page.tsx","default")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,7498,1658,5814,2335,6606,88],()=>r(28165));module.exports=a})();