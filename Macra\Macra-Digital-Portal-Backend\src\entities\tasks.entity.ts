import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  <PERSON>in<PERSON><PERSON>um<PERSON>,
  BeforeInsert,
} from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from './user.entity';
import { Applications } from './applications.entity';

export enum TaskType {
  APPLICATION = 'application',
  COMPLAINT = 'complaint',
  DATA_BREACH = 'data_breach',
  EVALUATION = 'evaluation',
  INSPECTION = 'inspection',
  DOCUMENT_REVIEW = 'document_review',
  COMPLIANCE_CHECK = 'compliance_check',
  FOLLOW_UP = 'follow_up',
}

export enum TaskStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  ON_HOLD = 'on_hold',
}

export enum TaskPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

@Entity('tasks')
export class Task {
  @PrimaryGeneratedColumn('uuid')
  task_id: string;

  @Column({ type: 'varchar', length: 100, unique: true })
  task_number: string;

  @Column({ type: 'varchar', length: 255 })
  title: string;

  @Column({ type: 'text' })
  description: string;

  @Column({
    type: 'enum',
    enum: TaskType,
    default: TaskType.APPLICATION,
  })
  task_type: TaskType;

  @Column({
    type: 'enum',
    enum: TaskStatus,
    default: TaskStatus.PENDING,
  })
  status: TaskStatus;

  @Column({
    type: 'enum',
    enum: TaskPriority,
    default: TaskPriority.MEDIUM,
  })
  priority: TaskPriority;

  @Column({ type: 'uuid', nullable: true })
  assigned_to: string;

  @Column({ type: 'uuid' })
  assigned_by: string;

  @Column({ type: 'timestamp', nullable: true })
  assigned_at: Date;

  @Column({ type: 'timestamp', nullable: true })
  due_date: Date;

  @Column({ type: 'timestamp', nullable: true })
  completed_at: Date;

  @Column({ type: 'uuid', nullable: true })
  application_id: string;

  @Column({ type: 'text', nullable: true })
  review_notes: string;

  @Column({ type: 'text', nullable: true })
  completion_notes: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at: Date;

  // Relations
  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'assigned_to' })
  assignee: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'assigned_by' })
  assigner: User;

  @ManyToOne(() => Applications, { nullable: true })
  @JoinColumn({ name: 'application_id' })
  application: Applications;

  @BeforeInsert()
  generateTaskNumber() {
    if (!this.task_number) {
      const timestamp = Date.now().toString(36).toUpperCase();
      const random = Math.random().toString(36).substr(2, 4).toUpperCase();
      this.task_number = `TSK-${timestamp}-${random}`;
    }
  }

  @BeforeInsert()
  generateId() {
    if (!this.task_id) {
      this.task_id = uuidv4();
    }
  }
}