'use client';

import React, { forwardRef } from 'react';

interface SelectOption {
  value: string;
  label: string;
}

interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  label?: React.ReactNode;
  error?: string;
  helperText?: string;
  variant?: 'default' | 'small';
  fullWidth?: boolean;
  options?: SelectOption[];
  children?: React.ReactNode;
}

const Select = forwardRef<HTMLSelectElement, SelectProps>(({
  label,
  error,
  helperText,
  variant = 'default',
  fullWidth = true,
  className = '',
  required,
  disabled,
  options,
  children,
  ...props
}, ref) => {
  // Base select styling with proper text visibility for all modes
  const baseSelectClass = `px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors duration-200 ${
    fullWidth ? 'w-full' : ''
  } ${variant === 'small' ? 'py-1.5 text-sm' : 'py-2'}`;

  // Error and disabled states
  const selectClass = `${baseSelectClass} ${
    error 
      ? "border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500" 
      : "border-gray-300 dark:border-gray-600"
  } ${
    disabled 
      ? "opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800" 
      : ""
  } ${className}`;

  const labelClass = `block font-medium text-gray-700 dark:text-gray-300 mb-2 ${
    variant === 'small' ? 'text-xs text-gray-600 dark:text-gray-400 mb-1' : 'text-sm'
  }`;

  return (
    <div className="w-full">
      {label && (
        <label className={labelClass}>
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <select
        ref={ref}
        className={selectClass}
        disabled={disabled}
        required={required}
        {...props}
      >
        {options ? (
          options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))
        ) : (
          children
        )}
      </select>
      
      {error && (
        <p className="mt-1 text-sm text-red-600 dark:text-red-400">
          {error}
        </p>
      )}
      
      {helperText && !error && (
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          {helperText}
        </p>
      )}
    </div>
  );
});

Select.displayName = 'Select';

export default Select;