"use strict";exports.id=6606,exports.ids=[6606],exports.modules={21891:(e,r,a)=>{a.d(r,{default:()=>m});var t=a(60687),s=a(43210),i=a(85814),l=a.n(i),n=a(16189),o=a(63213),d=a(85742),c=a(29045);let m=({activeTab:e="overview",onTabChange:r,onMobileMenuToggle:a})=>{let{isAuthenticated:i,user:m}=(0,o.A)(),[u,g]=(0,s.useState)(!1),x=(0,n.usePathname)().startsWith("/dashboard");return(0,t.jsxs)("header",{className:"bg-white dark:bg-gray-800 shadow-sm z-10",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between h-16 px-4 sm:px-6",children:[(0,t.jsx)("button",{id:"mobileMenuBtn",type:"button",onClick:a,className:"md:hidden text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none",children:(0,t.jsx)("div",{className:"w-6 h-6 flex items-center justify-center",children:(0,t.jsx)("i",{className:"ri-menu-line ri-lg"})})}),(0,t.jsx)("div",{className:"flex-1 flex items-center justify-center px-2 lg:ml-6 lg:justify-start",children:(0,t.jsxs)("div",{className:"max-w-lg w-full",children:[(0,t.jsx)("label",{htmlFor:"search",className:"sr-only",children:"Search"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)("div",{className:"w-5 h-5 flex items-center justify-center text-gray-400 dark:text-gray-500",children:(0,t.jsx)("i",{className:"ri-search-line"})})}),(0,t.jsx)("input",{id:"search",name:"search",className:"block w-full pl-10 pr-3 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-sm hover:bg-white dark:hover:bg-gray-600 transition-colors",placeholder:"Search for licenses, users, or transactions...",type:"search"})]})]})}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsxs)("button",{type:"button",className:"flex-shrink-0 p-1 mr-4 text-gray-500 dark:text-gray-400 rounded-full hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none relative",children:[(0,t.jsx)("span",{className:"sr-only",children:"View notifications"}),(0,t.jsx)("div",{className:"w-6 h-6 flex items-center justify-center",children:(0,t.jsx)("i",{className:"ri-notification-3-line ri-lg"})}),(0,t.jsx)("span",{className:"absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white"})]}),(0,t.jsxs)("div",{className:"dropdown relative",children:[(0,t.jsxs)("button",{type:"button",onClick:()=>{g(!u)},className:"flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:[(0,t.jsx)("span",{className:"sr-only",children:"Open user menu"}),m?.profile_image?(0,t.jsx)("img",{className:"h-8 w-8 rounded-full object-cover",src:m.profile_image,alt:"Profile",onError:e=>{let r=e.target;r.style.display="none",r.nextElementSibling?.classList.remove("hidden")}}):null,(0,t.jsx)("div",{className:`h-8 w-8 rounded-full bg-red-600 flex items-center justify-center text-white text-sm font-medium ${m?.profile_image?"hidden":""}`,children:m?(0,c.NQ)(m.first_name,m.last_name):"U"})]}),(0,t.jsx)("div",{className:`dropdown-content mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black dark:ring-gray-600 ring-opacity-5 ${u?"show":""}`,children:(0,t.jsxs)("div",{className:"py-1",children:[(0,t.jsx)(l(),{href:"/profile",className:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",children:"Your Profile"}),(0,t.jsx)(l(),{href:"/settings",className:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",children:"Settings"}),(0,t.jsx)("div",{className:"px-4 py-2",children:(0,t.jsx)(d.A,{variant:"text",size:"sm",className:"w-full justify-start text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",showConfirmation:!0,redirectTo:"/auth/login",children:"Sign out"})})]})})]})]})]}),x&&(0,t.jsx)("div",{className:"border-t border-gray-200 dark:border-gray-700 px-4 sm:px-6",children:(0,t.jsx)("div",{className:"py-3 flex space-x-8",children:[{id:"overview",label:"Overview"},{id:"licenses",label:"Licenses"},{id:"users",label:"Users"},{id:"transactions",label:"Transactions"},{id:"spectrum",label:"Spectrum"},{id:"compliance",label:"Compliance"}].map(a=>(0,t.jsx)("button",{type:"button",onClick:()=>{r?.(a.id),window.dispatchEvent(new CustomEvent("tabChange",{detail:{tab:a.id}}))},className:`tab-button text-sm px-1 py-2 ${e===a.id?"active":"text-gray-500"}`,children:a.label},a.id))})})]})}},29045:(e,r,a)=>{a.d(r,{NQ:()=>t}),process.env.NEXT_PUBLIC_API_URL;let t=(e,r)=>{let a=e?.charAt(0)?.toUpperCase()||"",t=r?.charAt(0)?.toUpperCase()||"";return`${a}${t}`||"U"}},60417:(e,r,a)=>{a.d(r,{default:()=>u});var t=a(60687),s=a(43210),i=a(16189),l=a(63213),n=a(85814),o=a.n(n),d=a(2677);let c=({href:e,icon:r,label:a,isActive:s=!1,onClick:i})=>{let{showLoader:l}=(0,d.M)();return(0,t.jsxs)(o(),{href:e,onClick:()=>{l({"/dashboard":"Loading Dashboard...","/applications/telecommunications":"Loading Telecommunications...","/applications/postal":"Loading Postal Services...","/applications/standards":"Loading Standards...","/applications/clf":"Loading CLF...","/procurement":"Loading Procurement...","/spectrum":"Loading Spectrum Management...","/financial":"Loading Financial Data...","/reports":"Loading Reports...","/users":"Loading User Management...","/audit-trail":"Loading Audit Trail...","/help":"Loading Help & Support..."}[e]||"Loading page..."),i&&i()},className:`
        flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200
        ${s?"bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-r-2 border-red-600 dark:border-red-400":"text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100"}
      `,children:[(0,t.jsx)("div",{className:`w-5 h-5 flex items-center justify-center mr-3 ${s?"text-red-600 dark:text-red-400":""}`,children:(0,t.jsx)("i",{className:r})}),a]})};var m=a(29045);let u=()=>{let{user:e}=(0,l.A)(),r=(0,i.usePathname)(),[a,n]=(0,s.useState)(!1);(0,s.useEffect)(()=>{n(!1)},[r]),(0,s.useEffect)(()=>{let e=e=>{let r=document.getElementById("mobile-sidebar"),t=document.getElementById("mobile-sidebar-toggle");a&&r&&!r.contains(e.target)&&t&&!t.contains(e.target)&&n(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[a]);let d=[{href:"/dashboard",icon:"ri-dashboard-line",label:"Dashboard",roles:["administrator","evaluator","customer"]},{href:"/applications/postal",icon:"ri-mail-send-line",label:"Postal",roles:["administrator","evaluator","customer"]},{href:"/applications/telecommunications",icon:"ri-broadcast-line",label:"Telecommunications",roles:["administrator","evaluator","customer"]},{href:"/applications/standards",icon:"ri-stack-line",label:"Standards",roles:["administrator","evaluator","customer"]},{href:"/applications/clf",icon:"ri-collage-line",label:"CLF",roles:["administrator","evaluator","customer"]},{href:"/task-assignment",icon:"ri-user-add-line",label:"Task Assignment",roles:["administrator","evaluator"]},{href:"/consumer-affairs",icon:"ri-shield-user-line",label:"Consumer Affairs",roles:["administrator","evaluator","customer"]},{href:"/data-breach",icon:"ri-shield-cross-line",label:"Data Breach",roles:["administrator","evaluator"]},{href:"/procurement",icon:"ri-shopping-bag-line",label:"Procurement",roles:["administrator","evaluator"]},{href:"/financial",icon:"ri-money-dollar-circle-line",label:"Accounts & Finance",roles:["administrator","evaluator"]},{href:"/reports",icon:"ri-file-chart-line",label:"Reports & Analytics",roles:["administrator","evaluator"]}].filter(r=>e?.roles?.some(e=>r.roles.includes(e))||r.roles.includes("customer")),u=[{href:"/users",icon:"ri-user-settings-line",label:"User Management",roles:["administrator"]},{href:"/settings",icon:"ri-settings-3-line",label:"Management Settings",roles:["administrator"]},{href:"/audit-trail",icon:"ri-shield-line",label:"Audit Trail",roles:["administrator","evaluator"]},{href:"/help",icon:"ri-question-line",label:"Help & Support",roles:["administrator","evaluator","customer"]}].filter(r=>e?.roles?.some(e=>r.roles.includes(e))||r.roles.includes("customer"));return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("button",{id:"mobile-sidebar-toggle",onClick:()=>{n(!a)},className:"lg:hidden fixed top-4 left-4 z-50 p-2 bg-primary text-white rounded-md shadow-lg hover:bg-red-700 transition-colors","aria-label":"Toggle mobile sidebar",children:(0,t.jsx)("i",{className:`fas ${a?"fa-times":"fa-bars"}`})}),a&&(0,t.jsx)("div",{className:"lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40",onClick:()=>n(!1)}),(0,t.jsx)("aside",{id:"mobile-sidebar",className:`
          fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out
          ${a?"translate-x-0":"-translate-x-full lg:translate-x-0"}
        `,children:(0,t.jsxs)("div",{className:"flex flex-col h-full",children:[(0,t.jsx)("div",{className:"h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700",children:(0,t.jsx)("div",{className:"flex items-center",children:(0,t.jsx)("img",{src:"/images/macra-logo.png",alt:"MACRA Logo",className:"max-h-12 w-auto"})})}),(0,t.jsxs)("nav",{className:"mt-6 px-4 side-nav",children:[(0,t.jsx)("div",{className:"space-y-1",children:d.map(e=>(0,t.jsx)(c,{href:e.href,icon:e.icon,label:e.label,isActive:r===e.href,onClick:()=>n(!1)},e.href))}),u.length>0&&(0,t.jsxs)("div",{className:"mt-8",children:[(0,t.jsx)("h3",{className:"px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Settings"}),(0,t.jsx)("div",{className:"mt-2 space-y-1",children:u.map(e=>(0,t.jsx)(c,{href:e.href,icon:e.icon,label:e.label,isActive:r===e.href,onClick:()=>n(!1)},e.href))})]})]}),e&&(0,t.jsx)("div",{className:"absolute bottom-0 w-64 p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[e.profile_image?(0,t.jsx)("img",{className:"h-10 w-10 rounded-full object-cover",src:e.profile_image,alt:"Profile",onError:e=>{let r=e.target;r.style.display="none",r.nextElementSibling?.classList.remove("hidden")}}):null,(0,t.jsx)("div",{className:`h-10 w-10 rounded-full bg-red-600 flex items-center justify-center text-white text-sm font-medium ${e.profile_image?"hidden":""}`,children:(0,m.NQ)(e.first_name,e.last_name)}),(0,t.jsxs)(o(),{href:"/profile",className:"flex-1 min-w-0",children:[(0,t.jsxs)("p",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 truncate",children:[e.first_name||"Unknown"," ",e.last_name||"User"]}),(0,t.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 truncate",children:e.roles&&e.roles.length>0?e.roles.map(e=>"string"==typeof e?e.replace(/_/g," ").replace(/\b\w/g,e=>e.toUpperCase()):"Unknown").join(", "):"User"})]})]})})]})})]})}},70440:(e,r,a)=>{a.r(r),a.d(r,{default:()=>s});var t=a(31658);let s=async e=>[{type:"image/x-icon",sizes:"256x256",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},85742:(e,r,a)=>{a.d(r,{A:()=>n});var t=a(60687),s=a(43210),i=a(16189),l=a(63213);function n({variant:e="primary",size:r="md",className:a="",showConfirmation:n=!0,redirectTo:o="/auth/login",children:d}){let[c,m]=(0,s.useState)(!1),[u,g]=(0,s.useState)(!1),{logout:x,user:h}=(0,l.A)(),f=(0,i.useRouter)(),p=(0,i.usePathname)(),b=async()=>{if(n&&!u)return void g(!0);m(!0);try{x(),await new Promise(e=>setTimeout(e,100)),p.includes("customer")?f.push("/customer/auth/login"):f.push("/auth/login")}catch(e){}finally{m(!1),g(!1)}},y=`inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed ${(()=>{switch(e){case"primary":default:return"bg-red-600 hover:bg-red-700 text-white border border-red-600";case"secondary":return"bg-white hover:bg-gray-50 text-red-600 border border-red-600";case"text":return"bg-transparent hover:bg-red-50 text-red-600 border-none";case"icon":return"bg-transparent hover:bg-red-50 text-red-600 border-none p-2"}})()} ${"icon"!==e?(()=>{switch(r){case"sm":return"px-3 py-1.5 text-sm";case"md":default:return"px-4 py-2 text-base";case"lg":return"px-6 py-3 text-lg"}})():""} ${a}`;return u?(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-sm mx-4 shadow-xl",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("svg",{className:"h-6 w-6 text-red-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,t.jsx)("div",{className:"ml-3",children:(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Confirm Logout"})})]}),(0,t.jsx)("div",{className:"mb-4",children:(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:["Are you sure you want to logout",h?.first_name?`, ${h.first_name}`:"","? You will need to login again to access your account."]})}),(0,t.jsxs)("div",{className:"flex space-x-3",children:[(0,t.jsx)("button",{onClick:b,disabled:c,className:"flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 disabled:opacity-50",children:c?"Logging out...":"Yes, Logout"}),(0,t.jsx)("button",{onClick:()=>{g(!1)},className:"flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200",children:"Cancel"})]})]})}):(0,t.jsx)("button",{onClick:b,disabled:c,className:y,title:"Logout",children:d||(0,t.jsx)(t.Fragment,{children:"icon"===e?(0,t.jsx)("svg",{className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"})}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("svg",{className:"h-4 w-4 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"})}),c?"Logging out...":"Logout"]})})})}}};