'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { getLicenseTypeStepConfig } from '@/config/licenseTypeStepConfig';

/**
 * ApplicationForm Coordinator Component
 * This component now acts as a coordinator that redirects to the new step-based routing system
 * It maintains backward compatibility while leveraging the new independent step architecture
 */

interface ApplicationFormProps {
  licenseType: string;
  licenseCategory?: string;
  licenseTypeId?: string;
  licenseCategoryId?: string;
  applicationId?: string;
  onSubmit?: (formData: any) => Promise<void>;
}

const ApplicationForm: React.FC<ApplicationFormProps> = ({
  licenseTypeId,
  licenseCategoryId,
  applicationId,
  onSubmit
}) => {
  const router = useRouter();

  // Redirect to step-based routing system
  useEffect(() => {
    // Validate required parameters
    if (!licenseTypeId || !licenseCategoryId) {
      console.error('ApplicationForm: Missing required licenseTypeId or licenseCategoryId');
      router.push('/customer/applications/new');
      return;
    }

<<<<<<< HEAD
    try {
      setIsSaving(true);

      // Step 1: Create applicant record first
      const { applicantService } = await import('@/services/applicantService');

      const applicantPayload = {
        name: applicantData.applicantName || `${applicantData.firstName || ''} ${applicantData.lastName || ''}`.trim(),
        business_registration_number: applicantData.businessRegistrationNo || `BRN-${Date.now()}`,
        tpin: applicantData.tpin || applicantData.taxNumber || `TPIN-${Date.now()}`,
        website: applicantData.website || 'https://example.com',
        email: applicantData.email,
        phone: applicantData.telephone || applicantData.phone,
        fax: applicantData.fax || undefined, // Don't send empty string for optional field
        level_of_insurance_cover: applicantData.insuranceCover || undefined, // Don't send empty string for optional field
        date_incorporation: new Date(applicantData.dateOfIncorporation || new Date().toISOString().split('T')[0]),
        place_incorporation: applicantData.placeOfIncorporation || applicantData.physicalCity || 'Malawi'
      };

      console.log('Creating applicant with data:', applicantPayload);

      let applicant;
      try {
        applicant = await applicantService.createApplicant(applicantPayload);
        console.log('Applicant created:', applicant);
        console.log('Full applicant response structure:', JSON.stringify(applicant, null, 2));
      } catch (applicantError) {
        console.error('Failed to create applicant:', applicantError);
        console.error('Applicant payload that failed:', applicantPayload);
        throw new Error(`Failed to create applicant: ${(applicantError as any)?.message || 'Unknown error'}`);
      }

      if (!applicant) {
        throw new Error('Applicant creation returned null or undefined');
      }

      // Extract applicant ID from response (handle different response formats)
      let applicantId: string;
      if (applicant.applicant_id) {
        applicantId = applicant.applicant_id;
      } else if (applicant.data?.applicant_id) {
        applicantId = applicant.data.applicant_id;
      } else if (applicant.id) {
        applicantId = applicant.id;
      } else {
        console.error('Applicant response missing ID field:', applicant);
        throw new Error('Applicant creation response missing ID field');
      }

      console.log('Extracted applicant ID:', applicantId);

      // Step 2: Create application with proper applicant_id
      const applicationPayload = {
        application_number: generateApplicationNumber(),
        applicant_id: applicantId, // Use the extracted applicant ID
        license_category_id: licenseCategoryId!,
        status: 'draft' as const, // Keep as draft until progress reaches 100%
        current_step: 1,
        progress_percentage: 0
        // Note: created_by is handled automatically by the backend controller
      };

      console.log('Creating application with data:', applicationPayload);
      console.log('Applicant ID type:', typeof applicantId, 'Value:', applicantId);
      console.log('License Category ID type:', typeof licenseCategoryId, 'Value:', licenseCategoryId);

      // Validate UUID format before sending
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(applicantId)) {
        throw new Error(`Invalid applicant_id UUID format: ${applicantId}`);
      }
      if (!uuidRegex.test(licenseCategoryId!)) {
        throw new Error(`Invalid license_category_id UUID format: ${licenseCategoryId}`);
      }

      console.log('All UUIDs validated successfully, creating application...');
      const newApplication = await applicationService.createApplication(applicationPayload);
      console.log('Application creation response:', newApplication);

      // Extract application ID from response (handle different response formats)
      let applicationIdFromResponse: string;
      if (newApplication.application_id) {
        applicationIdFromResponse = newApplication.application_id;
      } else if (newApplication.data?.application_id) {
        applicationIdFromResponse = newApplication.data.application_id;
      } else if (newApplication.id) {
        applicationIdFromResponse = newApplication.id;
      } else {
        console.error('Application response missing ID field:', newApplication);
        throw new Error('Application creation response missing ID field');
      }

      console.log('Extracted application ID:', applicationIdFromResponse);
      setApplicationId(applicationIdFromResponse);

      console.log('Application created successfully:', newApplication.application_id);
      return newApplication.application_id;
    } catch (error) {
      console.error('Error creating application:', error);
      throw error;
    } finally {
      setIsSaving(false);
    }
  }, [applicationId, licenseTypeId, licenseCategoryId, user]);

  // Generate proper application number matching backend pattern: ^[A-Z]{2,3}-[0-9]{4}-[0-9]{2,3}$
  const generateApplicationNumber = useCallback(() => {
    const now = new Date();
    const year = now.getFullYear();
    const randomNum = Math.floor(Math.random() * 999) + 1; // 1-999
    return `APP-${year}-${randomNum.toString().padStart(3, '0')}`;
    // Example: APP-2024-123
  }, []);

  // Save section data
  const saveSection = useCallback(async (sectionId: keyof ApplicationFormData, sectionData: any) => {
    try {
      // For applicant info, create applicant and application first
      if (sectionId === 'applicantInfo' && !applicationId) {
        const newApplicationId = await createApplicationFromApplicant(sectionData);
        return newApplicationId;
      }

      // For other sections, save to existing application
      if (applicationId) {
        await applicationService.saveApplicationSection(applicationId, sectionId, sectionData);
      }

      return applicationId;
    } catch (error) {
      console.error(`Error saving section ${sectionId}:`, error);
      throw error;
    }
  }, [applicationId, createApplicationFromApplicant]);

  // Get section data
  const getSectionData = useCallback((sectionId: keyof ApplicationFormData | 'reviewSubmit') => {
    if (sectionId === 'reviewSubmit') {
      return formData;
    }
    return formData[sectionId as keyof ApplicationFormData];
  }, [formData]);

  // Comprehensive validation for all form sections
  const validateFormSection = useCallback((sectionId: keyof ApplicationFormData | 'reviewSubmit', data: any) => {
    const errors: any = {};
    let isValid = true;

    // Helper function to check if field is empty
    const isEmpty = (value: any) => {
      if (value === null || value === undefined) return true;
      if (typeof value === 'string') return value.trim() === '';
      if (Array.isArray(value)) return value.length === 0;
      if (typeof value === 'object') return Object.keys(value).length === 0;
      return false;
    };

    // Helper function to add error
    const addError = (field: string, message: string) => {
      errors[field] = message;
      isValid = false;
    };

    switch (sectionId) {
      case 'applicantInfo':
        if (isEmpty(data.applicantName)) addError('applicantName', 'Applicant name is required');
        if (isEmpty(data.email)) addError('email', 'Email is required');
        if (isEmpty(data.telephone)) addError('telephone', 'Telephone is required');
        if (isEmpty(data.postalPoBox)) addError('postalPoBox', 'Postal P.O. Box is required');
        if (isEmpty(data.postalCity)) addError('postalCity', 'Postal city is required');
        if (isEmpty(data.postalCountry)) addError('postalCountry', 'Postal country is required');
        if (isEmpty(data.physicalStreet)) addError('physicalStreet', 'Physical street address is required');
        if (isEmpty(data.physicalCity)) addError('physicalCity', 'Physical city is required');
        if (isEmpty(data.physicalCountry)) addError('physicalCountry', 'Physical country is required');

        // Email format validation
        if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
          addError('email', 'Please enter a valid email address');
        }
        break;

      case 'companyProfile':
        if (isEmpty(data.businessRegistrationNo)) addError('businessRegistrationNo', 'Business registration number is required');
        if (isEmpty(data.tpin)) addError('tpin', 'TPIN is required');
        if (isEmpty(data.dateOfIncorporation)) addError('dateOfIncorporation', 'Date of incorporation is required');
        if (isEmpty(data.placeOfIncorporation)) addError('placeOfIncorporation', 'Place of incorporation is required');
        if (isEmpty(data.foreignOwnership)) addError('foreignOwnership', 'Foreign ownership information is required');
        if (isEmpty(data.shareholders)) addError('shareholders', 'At least one shareholder is required');
        if (isEmpty(data.directors)) addError('directors', 'At least one director is required');
        break;

      case 'management':
        if (isEmpty(data.organizationalStructure)) addError('organizationalStructure', 'Organizational structure is required');
        if (isEmpty(data.keyPersonnel)) addError('keyPersonnel', 'Key personnel information is required');
        if (isEmpty(data.managementTeam)) addError('managementTeam', 'Management team information is required');
        break;

      case 'professionalServices':
        // All fields in professional services are required
        if (isEmpty(data.consultants)) addError('consultants', 'Consultant information is required');
        if (isEmpty(data.serviceProviders)) addError('serviceProviders', 'Service providers information is required');
        if (isEmpty(data.technicalSupport)) addError('technicalSupport', 'Technical support information is required');
        if (isEmpty(data.maintenanceArrangements)) addError('maintenanceArrangements', 'Maintenance arrangements information is required');

        // Minimum length validation for detailed fields
        if (data.technicalSupport && data.technicalSupport.trim().length < 50) {
          addError('technicalSupport', 'Technical support description must be at least 50 characters');
        }
        if (data.maintenanceArrangements && data.maintenanceArrangements.trim().length < 50) {
          addError('maintenanceArrangements', 'Maintenance arrangements description must be at least 50 characters');
        }
        break;

      case 'businessInfo':
        if (isEmpty(data.businessDescription)) addError('businessDescription', 'Business description is required');
        if (isEmpty(data.operationalAreas)) addError('operationalAreas', 'Operational areas are required');
        if (isEmpty(data.facilities)) addError('facilities', 'Facilities information is required');
        if (isEmpty(data.equipment)) addError('equipment', 'Equipment information is required');
        break;

      case 'serviceScope':
        if (isEmpty(data.servicesOffered)) addError('servicesOffered', 'Services offered is required');
        if (isEmpty(data.geographicCoverage)) addError('geographicCoverage', 'Geographic coverage is required');
        if (isEmpty(data.targetMarket)) addError('targetMarket', 'Target market is required');
        break;

      case 'businessPlan':
        if (isEmpty(data.marketAnalysis)) addError('marketAnalysis', 'Market analysis is required');
        if (isEmpty(data.financialProjections)) addError('financialProjections', 'Financial projections are required');
        if (isEmpty(data.riskAssessment)) addError('riskAssessment', 'Risk assessment is required');
        break;

      case 'legalHistory':
        if (isEmpty(data.legalProceedings)) addError('legalProceedings', 'Legal proceedings information is required');
        if (isEmpty(data.regulatoryCompliance)) addError('regulatoryCompliance', 'Regulatory compliance information is required');
        break;
=======
    // Get license type configuration to validate
    const licenseConfig = getLicenseTypeStepConfig(licenseTypeId);
    if (!licenseConfig) {
      console.error('ApplicationForm: Invalid license type:', licenseTypeId);
      router.push('/customer/applications/new');
      return;
>>>>>>> save-application
    }

    // Determine the target route based on whether we have an application ID
    const firstStep = licenseConfig.steps[0];
    const targetApplicationId = applicationId || 'new';
    const targetUrl = `/customer/applications/apply/${targetApplicationId}?${firstStep.route}?licenseType=${licenseTypeId}&licenseCategory=${licenseCategoryId}`;

<<<<<<< HEAD
  // Get section errors
  const getSectionErrors = useCallback((sectionId: keyof ApplicationFormData | 'reviewSubmit') => {
    return errors[sectionId] || {};
  }, [errors]);

  // Check if a section is completed (no validation errors)
  const isSectionCompleted = useCallback((sectionId: keyof ApplicationFormData | 'reviewSubmit') => {
    const sectionData = getSectionData(sectionId);
    const validation = validateFormSection(sectionId, sectionData);
    return validation.isValid;
  }, [getSectionData, validateFormSection]);

  // Handle section save with application creation logic
  const handleSectionSave = useCallback(async (section: string, data: any) => {
    try {
      // Validate section data
      const validation = validateFormSection(section, data);
      if (!validation.isValid) {
        setErrors(prev => ({
          ...prev,
          [section]: validation.errors
        }));
        throw new Error('Validation failed');
      }

      // Save section data - this will create application if it's the first step
      const savedApplicationId = await saveSection(section, data);
      
      console.log(`${section} data saved successfully. Application ID: ${savedApplicationId}`);
      
      // Clear errors for this section
      setErrors(prev => ({
        ...prev,
        [section]: {}
      }));

      return savedApplicationId;
    } catch (error) {
      console.error(`Error saving ${section} data:`, error);
      throw error;
    }
  }, [saveSection, validateFormSection]);

  // Navigate to next step with validation and auto-save
  const handleNext = useCallback(async () => {
    const currentStepData = formSteps[currentStep];
    const sectionData = getSectionData(currentStepData.id);

    // Set saving state
    setIsSaving(true);

    try {
      // First validate the current step
      const validation = validateFormSection(currentStepData.id, sectionData);

      if (!validation.isValid) {
        // Set errors and prevent navigation
        setErrors(prev => ({
          ...prev,
          [currentStepData.id]: validation.errors
        }));

        // Show detailed error message
        const errorFields = Object.keys(validation.errors);
        const errorMessage = `Please complete the following required fields in the ${currentStepData.name} section:\n\n${errorFields.map(field => `• ${validation.errors[field]}`).join('\n')}`;

        console.error('Validation failed for step:', currentStepData.name);
        console.error('Errors:', validation.errors);

        alert(errorMessage);
        return;
      }

      // Clear any existing errors for this section
      setErrors(prev => ({
        ...prev,
        [currentStepData.id]: {}
      }));

      // Auto-save current step data before proceeding
      console.log(`Saving ${currentStepData.name} data...`);
      await handleSectionSave(currentStepData.id, sectionData);
      console.log(`${currentStepData.name} data saved successfully!`);

      // Move to next step only if validation and save succeeded
      if (currentStep < formSteps.length - 1) {
        setCurrentStep(currentStep + 1);
        console.log(`Successfully moved to step: ${formSteps[currentStep + 1].name}`);

        // Show success message
        // You can replace this with a toast notification if available
        console.log(`✅ ${currentStepData.name} completed and saved successfully!`);
      }
    } catch (error) {
      console.error('Error proceeding to next step:', error);

      // Show user-friendly error message with more details
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      alert(`There was an error saving your ${currentStepData.name} data: ${errorMessage}\n\nPlease check your information and try again.`);
    } finally {
      setIsSaving(false);
    }
  }, [currentStep, formSteps, getSectionData, validateFormSection, handleSectionSave]);

  // Navigate to previous step
  const handlePrevious = useCallback(() => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  }, [currentStep]);

  // Navigate to specific step with validation
  const goToStep = useCallback(async (stepIndex: number) => {
    if (stepIndex < 0 || stepIndex >= formSteps.length) return;

    // If going forward, validate all steps between current and target
    if (stepIndex > currentStep) {
      for (let i = currentStep; i < stepIndex; i++) {
        const stepData = formSteps[i];
        const sectionData = getSectionData(stepData.id);
        const validation = validateFormSection(stepData.id, sectionData);

        if (!validation.isValid) {
          // Set errors and prevent navigation
          setErrors(prev => ({
            ...prev,
            [stepData.id]: validation.errors
          }));

          alert(`Please complete all required fields in the ${stepData.name} section before proceeding.`);
          return;
        }

        // Auto-save the step data
        try {
          await handleSectionSave(stepData.id, sectionData);
        } catch (error) {
          console.error(`Error saving ${stepData.name} data:`, error);
          alert(`Error saving ${stepData.name} data. Please try again.`);
          return;
        }
      }
    }

    // Navigate to the target step
    setCurrentStep(stepIndex);
  }, [currentStep, formSteps, getSectionData, validateFormSection, handleSectionSave]);

  // Calculate current progress percentage
  const calculateProgressPercentage = useCallback(() => {
    let completedSections = 0;

    for (const step of formSteps) {
      if (step.id === 'reviewSubmit') continue; // Skip review step in progress calculation

      const sectionData = getSectionData(step.id);
      const validation = validateFormSection(step.id, sectionData);
      if (validation.isValid) {
        completedSections++;
      }
    }

    const totalSections = formSteps.length - 1; // Exclude reviewSubmit step
    return Math.round((completedSections / totalSections) * 100);
  }, [formSteps, getSectionData, validateFormSection]);

  // Handle final form submission
  const handleSubmit = useCallback(async () => {
    setIsSubmitting(true);
    try {
      // Validate all sections
      let hasErrors = false;
      const allErrors: Record<string, any> = {};

      for (const step of formSteps) {
        const sectionData = getSectionData(step.id);
        const validation = validateFormSection(step.id, sectionData);
        if (!validation.isValid) {
          allErrors[step.id] = validation.errors;
          hasErrors = true;
        }
      }

      if (hasErrors) {
        setErrors(allErrors);
        throw new Error('Please fix validation errors before submitting');
      }

      // Check if progress is 100% before allowing submission
      const currentProgress = calculateProgressPercentage();
      if (currentProgress < 100) {
        throw new Error(`Application is only ${currentProgress}% complete. Please complete all required sections before submitting.`);
      }

      // Update application status to 'submitted' when progress reaches 100%
      if (applicationId) {
        try {
          await applicationService.updateApplication(applicationId, {
            status: 'submitted',
            progress_percentage: 100,
            current_step: formSteps.length,
            submitted_at: new Date().toISOString()
          });
          console.log('Application status updated to submitted with 100% progress');
        } catch (statusError) {
          console.error('Error updating application status:', statusError);
          // Continue with submission even if status update fails
        }
      }

      // Submit the application
      await onSubmit({
        ...formData,
        licenseType,
        licenseCategory,
        applicationId: applicationId
      });
    } catch (error) {
      console.error('Error submitting application:', error);
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  }, [formSteps, getSectionData, validateFormSection, onSubmit, formData, licenseType, licenseCategory, applicationId]);

  // Render current step component
  const renderCurrentStep = useMemo(() => {
    if (currentStep >= formSteps.length) {
      return <div>Invalid step</div>;
    }

    const step = formSteps[currentStep];
    const stepId = step.id as keyof ApplicationFormData;

    // Get section data and errors
    const sectionData = stepId === 'reviewSubmit' ? formData : formData[stepId];
    const sectionErrors = errors[stepId] || {};

    // Common props for all components
    const commonProps = {
      data: sectionData,
      onChange: (data: any) => handleFormChange(stepId, data),
      errors: sectionErrors,
      disabled: isSaving
    };

    switch (stepId) {
      case 'applicantInfo':
        return <ApplicantInfo {...commonProps} data={formData.applicantInfo} onChange={(data) => handleFormChange('applicantInfo', data)} />;
      case 'companyProfile':
        return <CompanyProfile {...commonProps} data={formData.companyProfile} onChange={(data) => handleFormChange('companyProfile', data)} />;
      case 'management':
        return <Management {...commonProps} data={formData.management} onChange={(data) => handleFormChange('management', data)} />;
      case 'professionalServices':
        return <ProfessionalServices {...commonProps} data={formData.professionalServices} onChange={(data) => handleFormChange('professionalServices', data)} />;
      case 'businessInfo':
        return <BusinessInfo {...commonProps} data={formData.businessInfo} onChange={(data) => handleFormChange('businessInfo', data)} />;
      case 'serviceScope':
        return <ServiceScope {...commonProps} data={formData.serviceScope} onChange={(data) => handleFormChange('serviceScope', data)} />;
      case 'businessPlan':
        return <BusinessPlan {...commonProps} data={formData.businessPlan} onChange={(data) => handleFormChange('businessPlan', data)} />;
      case 'legalHistory':
        return <LegalHistory {...commonProps} data={formData.legalHistory} onChange={(data) => handleFormChange('legalHistory', data)} />;
      case 'reviewSubmit':
        return (
          <ReviewSubmit
            data={formData}
            onChange={() => {}}
            errors={{}}
            disabled={isSubmitting}
            onSubmit={handleSubmit}
            isSubmitting={isSubmitting}
          />
        );
      default:
        return <div>Component not found: {stepId}</div>;
    }
  }, [
    currentStep,
    formSteps,
    getSectionData,
    getSectionErrors,
    errors,
    handleFormChange,
    handleSectionSave,
    licenseTypeId,
    licenseCategoryId,
    applicationId,
    isSaving,
    formData,
    handleSubmit,
    isSubmitting
  ]);
=======
    
    router.replace(targetUrl);
  }, [licenseTypeId, licenseCategoryId, applicationId, router]);
>>>>>>> save-application

  // Show loading while redirecting
  return (
<<<<<<< HEAD
    <div className="max-w-4xl mx-auto">
      {/* Continue Application Indicator */}
      {isContinuing && (
        <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
          <div className="flex items-center">
            <i className="ri-play-circle-line text-green-600 dark:text-green-400 text-lg mr-3"></i>
            <div>
              <h3 className="text-sm font-medium text-green-900 dark:text-green-100">
                Continuing Application
              </h3>
              <p className="text-green-700 dark:text-green-300 text-sm mt-1">
                You are continuing your application from Step {currentStep + 1}. Your previous progress has been saved.
                {applicationId && (
                  <span className="ml-2">
                    Application ID: {applicationId.slice(0, 8)}...
                  </span>
                )}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Simple Step Navigation */}
      <div className="mb-8">
        <div className="flex flex-wrap gap-2 mb-4">
          {formSteps.map((step, index) => {
            const isCompleted = index < currentStep || isSectionCompleted(step.id);
            const isCurrent = index === currentStep;
            const hasErrors = Object.keys(getSectionErrors(step.id)).length > 0;

            return (
              <button
                key={step.id}
                type="button"
                onClick={() => goToStep(index)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors relative ${
                  isCurrent
                    ? 'bg-primary text-white shadow-md'
                    : isCompleted
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                    : hasErrors
                    ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                    : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                }`}
                disabled={isSaving}
              >
                {isCompleted && <i className="ri-check-line mr-1"></i>}
                {hasErrors && !isCurrent && <i className="ri-error-warning-line mr-1"></i>}
                {step.name}
                {step.required && <span className="text-red-500 ml-1">*</span>}
              </button>
            );
          })}
        </div>

        {/* Current Step Info */}
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            {formSteps[currentStep]?.name}
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Step {currentStep + 1} of {formSteps.length}
            {applicationId && (
              <span className="ml-4">
                Application ID: {applicationId.slice(0, 8)}...
              </span>
            )}
          </p>
        </div>
      </div>

      {/* Validation Summary */}
      {Object.keys(getSectionErrors(formSteps[currentStep].id)).length > 0 && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <i className="ri-error-warning-line text-red-400 text-xl"></i>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                Please complete the following required fields:
              </h3>
              <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                <ul className="list-disc list-inside space-y-1">
                  {Object.entries(getSectionErrors(formSteps[currentStep].id)).map(([field, error]) => (
                    <li key={field}>{error}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Form Content */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="p-6">
          {renderCurrentStep}
        </div>

        {/* Navigation Buttons */}
        <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
          {/* Instruction Message */}
          <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
            <div className="flex">
              <div className="flex-shrink-0">
                <i className="ri-information-line text-blue-400 text-lg"></i>
              </div>
              <div className="ml-3">
                <p className="text-sm text-blue-800 dark:text-blue-200">
                  <strong>Complete all required fields</strong> on this page, then click <strong>"Save & Continue"</strong> to automatically save your progress and move to the next section.
                </p>
              </div>
            </div>
          </div>

          <div className="flex justify-between items-center">
          <button
            type="button"
            onClick={handlePrevious}
            disabled={currentStep === 0 || isSaving}
            className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <i className="ri-arrow-left-line mr-2"></i>
            Previous
          </button>

          <div className="flex items-center space-x-4">
            {isSaving && (
              <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                <i className="ri-loader-4-line animate-spin mr-2"></i>
                Saving...
              </div>
            )}

            {currentStep < formSteps.length - 1 ? (
              <button
                type="button"
                onClick={handleNext}
                disabled={isSaving}
                className="inline-flex items-center px-6 py-3 bg-primary text-white text-sm font-medium rounded-md hover:bg-primary-dark disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-md hover:shadow-lg"
              >
                {isSaving ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Saving...
                  </>
                ) : (
                  <>
                    Save & Continue
                    <i className="ri-arrow-right-line ml-2"></i>
                  </>
                )}
              </button>
            ) : (
              <>
                {/* Progress indicator for submit step */}
                <div className="mb-4">
                  <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
                    <span>Application Progress</span>
                    <span>{calculateProgressPercentage()}%</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${calculateProgressPercentage()}%` }}
                    ></div>
                  </div>
                  {calculateProgressPercentage() < 100 && (
                    <p className="text-sm text-amber-600 dark:text-amber-400 mt-2">
                      <i className="ri-information-line mr-1"></i>
                      Please complete all required sections to submit your application.
                    </p>
                  )}
                </div>

                <button
                  type="button"
                  onClick={handleSubmit}
                  disabled={isSubmitting || calculateProgressPercentage() < 100}
                  className="inline-flex items-center px-6 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? (
                    <>
                      <i className="ri-loader-4-line animate-spin mr-2"></i>
                      Submitting...
                    </>
                  ) : calculateProgressPercentage() < 100 ? (
                    <>
                      <i className="ri-lock-line mr-2"></i>
                      Complete All Sections ({calculateProgressPercentage()}%)
                    </>
                  ) : (
                    <>
                      <i className="ri-send-plane-line mr-2"></i>
                      Submit Application
                    </>
                  )}
                </button>
              </>
            )}
          </div>
        </div>
=======
    <div className="flex items-center justify-center min-h-96">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
        <p className="text-gray-600 dark:text-gray-400">Redirecting to application form...</p>
>>>>>>> save-application
      </div>
    </div>
  );
};

export default ApplicationForm;
