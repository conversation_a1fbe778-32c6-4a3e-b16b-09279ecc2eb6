(()=>{var e={};e.id=7132,e.ids=[7132],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3889:(e,s,r)=>{Promise.resolve().then(r.bind(r,50030))},10413:(e,s,r)=>{Promise.resolve().then(r.bind(r,51778))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26385:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var t=r(65239),a=r(48088),i=r(88170),n=r.n(i),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(s,o);let d={children:["",{children:["users",{children:["edit",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,50030)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\users\\edit\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,73888)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\users\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\users\\edit\\[id]\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/users/edit/[id]/page",pathname:"/users/edit/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28973:(e,s,r)=>{Promise.resolve().then(r.bind(r,73888))},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},42700:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>d});var t=r(60687),a=r(43210),i=r(16189),n=r(85814),l=r.n(n),o=r(49048);function d(){(0,i.useRouter)();let e=(0,i.useParams)().id,[s,r]=(0,a.useState)(null),[n,d]=(0,a.useState)({email:"",password:"",confirmPassword:"",first_name:"",last_name:"",middle_name:"",phone:"",status:"active",role_ids:[]}),[c,m]=(0,a.useState)([]),[u,p]=(0,a.useState)(!1),[h,x]=(0,a.useState)(!0),[f,g]=(0,a.useState)(null),[v,y]=(0,a.useState)(null),b=async()=>{try{x(!0);let s=await o.D.getUserById(e);r(s),d({email:s.email,password:"",confirmPassword:"",first_name:s.first_name,last_name:s.last_name,middle_name:s.middle_name||"",phone:s.phone||"",status:s.status,role_ids:s.roles?.map(e=>e.role_id)||[]})}catch(e){g("Failed to load user data")}finally{x(!1)}},j=async s=>{if(s.preventDefault(),p(!0),g(null),y(null),n.password&&n.password!==n.confirmPassword){g("Passwords do not match"),p(!1);return}if(!n.email||!n.first_name||!n.last_name){g("Please fill in all required fields"),p(!1);return}try{let s={email:n.email,first_name:n.first_name,last_name:n.last_name,middle_name:n.middle_name||void 0,phone:n.phone,status:n.status,role_ids:n.role_ids.length>0?n.role_ids:void 0};n.password.trim()&&(s.password=n.password),await o.D.updateUser(e,s),y("User updated successfully!"),await b()}catch(e){g(e.response?.data?.message||"Failed to update user")}finally{p(!1)}},w=e=>{let{name:s,value:r}=e.target;d(e=>({...e,[s]:r}))},N=(e,s)=>{d(r=>({...r,role_ids:s?[...r.role_ids,e]:r.role_ids.filter(s=>s!==e)}))};return h?(0,t.jsx)("main",{className:"flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"})})})}):s?(0,t.jsx)("main",{className:"flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,t.jsxs)("div",{className:"tab-heading",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-semibold text-gray-900",children:"Edit User"}),(0,t.jsxs)("p",{className:"mt-1 text-sm text-gray-500",children:["Update user information, roles, and permissions for ",s.first_name," ",s.last_name,"."]})]}),(0,t.jsx)("div",{className:"relative",children:(0,t.jsxs)(l(),{href:"/users",className:"main-button",role:"button",children:[(0,t.jsx)("div",{className:"w-5 h-5 flex items-center justify-center mr-2",children:(0,t.jsx)("i",{className:"ri-arrow-left-line"})}),"Back to Users"]})})]}),f&&(0,t.jsx)("div",{className:"mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md",children:f}),v&&(0,t.jsx)("div",{className:"mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md",children:v}),(0,t.jsx)("div",{className:"bg-white shadow overflow-hidden sm:rounded-lg",children:(0,t.jsx)("div",{className:"px-4 py-5 sm:p-6",children:(0,t.jsxs)("form",{onSubmit:j,className:"grid gap-6 sm:grid-cols-2 lg:grid-cols-2 sm:gap-x-6",children:[(0,t.jsxs)("div",{className:"form-section",children:[(0,t.jsx)("h3",{className:"text-lg font-medium leading-6 text-gray-900",children:"Basic Information"}),(0,t.jsxs)("div",{className:"inner-form-section",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"first_name",className:"custom-form-label",children:"First Name *"}),(0,t.jsx)("input",{type:"text",name:"first_name",id:"first_name",value:n.first_name,onChange:w,className:"custom-input",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"last_name",className:"custom-form-label",children:"Last Name *"}),(0,t.jsx)("input",{type:"text",name:"last_name",id:"last_name",value:n.last_name,onChange:w,className:"custom-input",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"middle_name",className:"custom-form-label",children:"Middle Name"}),(0,t.jsx)("input",{type:"text",name:"middle_name",id:"middle_name",value:n.middle_name,onChange:w,className:"custom-input"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"custom-form-label",children:"Email Address *"}),(0,t.jsx)("input",{type:"email",name:"email",id:"email",value:n.email,onChange:w,className:"custom-input",required:!0})]}),(0,t.jsxs)("div",{className:"sm:col-span-2",children:[(0,t.jsx)("label",{htmlFor:"phone",className:"custom-form-label",children:"Phone Number"}),(0,t.jsx)("input",{type:"tel",name:"phone",id:"phone",value:n.phone,onChange:w,className:"custom-input",placeholder:"+265..."})]})]})]}),(0,t.jsxs)("div",{className:"form-section",children:[(0,t.jsx)("h3",{className:"text-lg font-medium leading-6 text-gray-900",children:"Account Information"}),(0,t.jsxs)("div",{className:"inner-form-section",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"status",className:"custom-form-label",children:"Status *"}),(0,t.jsxs)("select",{id:"status",name:"status",value:n.status,onChange:w,className:"custom-input",required:!0,children:[(0,t.jsx)("option",{value:"active",children:"Active"}),(0,t.jsx)("option",{value:"inactive",children:"Inactive"}),(0,t.jsx)("option",{value:"suspended",children:"Suspended"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password",className:"custom-form-label",children:"New Password"}),(0,t.jsx)("input",{type:"password",name:"password",id:"password",value:n.password,onChange:w,className:"custom-input"}),(0,t.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Leave blank to keep current password. Must be at least 8 characters if changing."})]}),(0,t.jsxs)("div",{className:"sm:col-span-2",children:[(0,t.jsx)("label",{htmlFor:"confirmPassword",className:"custom-form-label",children:"Confirm New Password"}),(0,t.jsx)("input",{type:"password",name:"confirmPassword",id:"confirmPassword",value:n.confirmPassword,onChange:w,className:"custom-input"})]})]})]}),(0,t.jsxs)("div",{className:"form-section border-none sm:col-span-2",children:[(0,t.jsx)("h3",{className:"text-lg font-medium leading-6 text-gray-900",children:"Role & Permissions"}),(0,t.jsxs)("div",{className:"mt-4",children:[(0,t.jsx)("label",{className:"custom-form-label mb-2",children:"User Roles"}),(0,t.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3",children:c.map(e=>(0,t.jsxs)("label",{className:"flex items-center space-x-2 bg-gray-50 rounded-md px-3 py-2 cursor-pointer hover:bg-red-50 transition",children:[(0,t.jsx)("input",{type:"checkbox",checked:n.role_ids.includes(e.role_id),onChange:s=>N(e.role_id,s.target.checked),className:"form-checkbox h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary"}),(0,t.jsx)("span",{className:"text-sm text-gray-700 capitalize",children:e.name.replace(/_/g," ")})]},e.role_id))})]})]}),(0,t.jsxs)("div",{className:"sm:col-span-2 flex justify-end space-x-3 pt-6 border-t border-gray-200",children:[(0,t.jsx)(l(),{href:"/users",className:"secondary-main-button",children:"Cancel"}),(0,t.jsx)("button",{type:"submit",disabled:u,className:"main-button disabled:opacity-50 disabled:cursor-not-allowed",children:u?"Updating...":"Update User"})]})]})})})]})}):(0,t.jsx)("main",{className:"flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h1",{className:"text-2xl font-semibold text-gray-900",children:"User Not Found"}),(0,t.jsx)("p",{className:"mt-2 text-gray-600",children:"The user you're looking for doesn't exist."}),(0,t.jsx)(l(),{href:"/users",className:"mt-4 main-button inline-flex",children:"Back to Users"})]})})})}r(85177)},49048:(e,s,r)=>{"use strict";r.d(s,{D:()=>i});var t=r(12234),a=r(51278);let i={async getUsers(e={}){let s=new URLSearchParams;e.page&&s.set("page",e.page.toString()),e.limit&&s.set("limit",e.limit.toString()),e.search&&s.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>s.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>s.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(([e,r])=>{Array.isArray(r)?r.forEach(r=>s.append(`filter.${e}`,r)):s.set(`filter.${e}`,r)});let r=await t.Gf.get(`?${s.toString()}`);return(0,a.zp)(r)},async getUser(e){let s=await t.Gf.get(`/${e}`);return(0,a.zp)(s)},async getUserById(e){return this.getUser(e)},async getProfile(){let e=await t.Gf.get("/profile");return(0,a.zp)(e)},async createUser(e){let s=await t.Gf.post("",e);return(0,a.zp)(s)},async updateUser(e,s){let r=await t.Gf.put(`/${e}`,s);return(0,a.zp)(r)},async updateProfile(e){let s=await t.Gf.put("/profile",e);return(0,a.zp)(s)},async changePassword(e){let s=await t.Gf.put("/profile/password",e);return(0,a.zp)(s)},async uploadAvatar(e){let s=new FormData;s.append("avatar",e);try{let e=await t.Gf.post("/profile/avatar",s,{headers:{"Content-Type":"multipart/form-data"}});return(0,a.zp)(e)}catch(e){throw e}},async removeAvatar(){let e=await t.Gf.delete("/profile/avatar");return(0,a.zp)(e)},async deleteUser(e){await t.Gf.delete(`/${e}`)}}},50030:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\users\\\\edit\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\users\\edit\\[id]\\page.tsx","default")},51778:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>d});var t=r(60687),a=r(43210),i=r(16189),n=r(63213),l=r(21891),o=r(60417);function d({children:e}){let{isAuthenticated:s,loading:r}=(0,n.A)();(0,i.useRouter)();let[d,c]=(0,a.useState)("overview"),[m,u]=(0,a.useState)(!1);return r?(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"})}):s?(0,t.jsxs)("div",{className:"flex h-screen overflow-hidden",children:[(0,t.jsx)("div",{id:"mobileSidebarOverlay",className:`mobile-sidebar-overlay ${m?"show":""}`,onClick:()=>u(!1)}),(0,t.jsx)(o.default,{}),(0,t.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,t.jsx)(l.default,{activeTab:d,onTabChange:c,onMobileMenuToggle:()=>{u(!m)}}),e]})]}):null}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73888:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\users\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\users\\layout.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85177:(e,s,r)=>{"use strict";r.d(s,{O:()=>i});var t=r(12234),a=r(51278);process.env.NEXT_PUBLIC_API_URL;let i={async getRoles(e={}){let s=new URLSearchParams;e.page&&s.set("page",e.page.toString()),e.limit&&s.set("limit",e.limit.toString()),e.search&&s.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>s.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>s.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(([e,r])=>{Array.isArray(r)?r.forEach(r=>s.append(`filter.${e}`,r)):s.set(`filter.${e}`,r)});let r=await t.rV.get(`?${s.toString()}`);return(0,a.zp)(r)},async getRole(e){let s=await t.rV.get(`/${e}`);return(0,a.zp)(s)},async getRoleWithPermissions(e){let s=await t.rV.get(`/${e}?include=permissions`);return(0,a.zp)(s)},async createRole(e){let s=await t.rV.post("",e);return(0,a.zp)(s)},async updateRole(e,s){let r=await t.rV.patch(`/${e}`,s);return(0,a.zp)(r)},async deleteRole(e){await t.rV.delete(`/${e}`)},async assignPermissions(e,s){let r=await t.rV.post(`/${e}/permissions`,{permission_ids:s});return(0,a.zp)(r)},async removePermissions(e,s){let r=await t.rV.delete(`/${e}/permissions`,{data:{permission_ids:s}});return(0,a.zp)(r)},async getPermissions(){let e=await t.uE.get("/permissions");return(0,a.zp)(e)}}},94161:(e,s,r)=>{Promise.resolve().then(r.bind(r,42700))},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4447,7498,1658,5814,2335,6606],()=>r(26385));module.exports=t})();