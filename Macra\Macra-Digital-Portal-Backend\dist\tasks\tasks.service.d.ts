import { Repository } from 'typeorm';
import { Task } from '../entities/tasks.entity';
import { CreateTaskDto } from '../dto/tasks/create-task.dto';
import { UpdateTaskDto } from '../dto/tasks/update-task.dto';
import { AssignTaskDto } from '../dto/tasks/assign-task.dto';
import { PaginateQuery, Paginated } from 'nestjs-paginate';
export declare class TasksService {
    private readonly tasksRepository;
    constructor(tasksRepository: Repository<Task>);
    private readonly paginateConfig;
    create(createTaskDto: CreateTaskDto, creatorId: string): Promise<Task>;
    findAll(query: PaginateQuery): Promise<Paginated<Task>>;
    findUnassigned(query: PaginateQuery): Promise<Paginated<Task>>;
    findAssigned(query: PaginateQuery): Promise<Paginated<Task>>;
    findAssignedToUser(userId: string, query: PaginateQuery): Promise<Paginated<Task>>;
    findOne(id: string): Promise<Task>;
    update(id: string, updateTaskDto: UpdateTaskDto): Promise<Task>;
    assign(id: string, assignTaskDto: AssignTaskDto, assignerId: string): Promise<Task>;
    reassign(id: string, assignTaskDto: AssignTaskDto, assignerId: string): Promise<Task>;
    remove(id: string): Promise<void>;
    getTaskStats(): Promise<{
        total: number;
        unassigned: number;
        assigned: number;
        completed: number;
        overdue: number;
    }>;
}
