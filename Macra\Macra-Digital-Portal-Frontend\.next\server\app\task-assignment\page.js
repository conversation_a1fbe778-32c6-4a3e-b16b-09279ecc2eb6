(()=>{var e={};e.id=5104,e.ids=[5104],e.modules={3165:(e,t,r)=>{Promise.resolve().then(r.bind(r,36802))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6790:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x});var a=r(60687),s=r(43210),i=r(63213),n=r(16189),d=r(71773),l=r(12234);let o={getUnassignedTasks:async e=>{let t=new URLSearchParams;e?.page&&t.append("page",e.page.toString()),e?.limit&&t.append("limit",e.limit.toString()),e?.search&&t.append("search",e.search),e?.task_type&&t.append("task_type",e.task_type);let r=t.toString(),a=`/tasks/unassigned${r?`?${r}`:""}`;return(await l.uE.get(a)).data},getAssignedTasks:async e=>{let t=new URLSearchParams;e?.page&&t.append("page",e.page.toString()),e?.limit&&t.append("limit",e.limit.toString()),e?.search&&t.append("search",e.search),e?.task_type&&t.append("task_type",e.task_type);let r=t.toString(),a=`/tasks/assigned${r?`?${r}`:""}`;return(await l.uE.get(a)).data},assignTask:async(e,t)=>(await l.uE.put(`/tasks/${e}/assign`,t)).data};var c=r(87911);function x(){let{isAuthenticated:e}=(0,i.A)(),{showSuccess:t,showError:r}=(0,c.d)();(0,n.useRouter)();let[l,x]=(0,s.useState)([]),[g,p]=(0,s.useState)([]),[u,m]=(0,s.useState)(!0),[h,y]=(0,s.useState)(""),[b,f]=(0,s.useState)(null),[k,v]=(0,s.useState)(""),[j,w]=(0,s.useState)(!1),[N,_]=(0,s.useState)("unassigned"),[P,C]=(0,s.useState)(""),[S,A]=(0,s.useState)(""),T=(0,s.useCallback)(async()=>{try{m(!0),y("");let e={limit:50,search:S||void 0,task_type:P||void 0},t="unassigned"===N?await o.getUnassignedTasks(e):await o.getAssignedTasks(e);x(t.data||[])}catch(e){y("Failed to load tasks")}finally{m(!1)}},[N,S,P]),M=async(e,a)=>{try{w(!0),await o.assignTask(e,{assignedTo:a}),await T(),f(null),v(""),t("Task assigned successfully!"),y("")}catch(t){let e=t instanceof Error?t.message:"Unknown error";r(`Failed to assign task: ${e}`),y("Failed to assign task")}finally{w(!1)}};if(u)return(0,a.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,a.jsx)(d.A,{message:"Loading Task Assignment..."})});let D=e=>{switch(e){case"application":return"ri-file-text-line";case"complaint":return"ri-feedback-line";case"data_breach":return"ri-shield-line";case"evaluation":return"ri-survey-line";case"inspection":return"ri-search-line";default:return"ri-task-line"}},q=e=>{switch(e){case"application":return"bg-blue-100 text-blue-800";case"complaint":return"bg-yellow-100 text-yellow-800";case"data_breach":return"bg-red-100 text-red-800";case"evaluation":return"bg-green-100 text-green-800";case"inspection":return"bg-purple-100 text-purple-800";default:return"bg-gray-100 text-gray-800"}},F=e=>{switch(e){case"urgent":return"bg-red-100 text-red-800";case"high":return"bg-orange-100 text-orange-800";case"medium":return"bg-yellow-100 text-yellow-800";case"low":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}};return(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2",children:"Task Assignment"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Assign various types of tasks to officers for processing and evaluation"})]}),h&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:(0,a.jsx)("p",{children:h})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg p-4 mb-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Search Tasks"}),(0,a.jsx)("input",{type:"text",value:S,onChange:e=>A(e.target.value),placeholder:"Search by task ID, title, or description...",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Task Type"}),(0,a.jsxs)("select",{value:P,onChange:e=>C(e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",children:[(0,a.jsx)("option",{value:"",children:"All Types"}),(0,a.jsx)("option",{value:"application",children:"Applications"}),(0,a.jsx)("option",{value:"complaint",children:"Complaints"}),(0,a.jsx)("option",{value:"data_breach",children:"Data Breach Reports"}),(0,a.jsx)("option",{value:"evaluation",children:"Evaluations"}),(0,a.jsx)("option",{value:"inspection",children:"Inspections"})]})]}),(0,a.jsx)("div",{className:"flex items-end",children:(0,a.jsx)("button",{type:"button",onClick:()=>{A(""),C("")},className:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600",children:"Clear Filters"})})]})}),(0,a.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-700 mb-6",children:(0,a.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,a.jsxs)("button",{type:"button",onClick:()=>_("unassigned"),className:`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center ${"unassigned"===N?"border-primary text-primary":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"}`,children:[(0,a.jsx)("i",{className:"ri-task-line mr-2"}),"Unassigned Tasks",(0,a.jsx)("span",{className:"ml-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 py-0.5 px-2 rounded-full text-xs",children:l.length})]}),(0,a.jsxs)("button",{type:"button",onClick:()=>_("assigned"),className:`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center ${"assigned"===N?"border-primary text-primary":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"}`,children:[(0,a.jsx)("i",{className:"ri-user-check-line mr-2"}),"Assigned Tasks",(0,a.jsx)("span",{className:"ml-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 py-0.5 px-2 rounded-full text-xs",children:l.length})]})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[(0,a.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Task ID"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Description"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Type"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Priority"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"assigned"===N?"Assigned To":"Created"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"assigned"===N?"Assigned By":"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Review"})]})}),(0,a.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:0===l.length?(0,a.jsx)("tr",{children:(0,a.jsx)("td",{colSpan:7,className:"px-6 py-12 text-center text-gray-500 dark:text-gray-400",children:(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)("i",{className:"ri-task-line text-4xl mb-2"}),(0,a.jsxs)("p",{children:["No ",N," tasks found"]}),(S||P)&&(0,a.jsx)("p",{className:"text-sm mt-1",children:"Try adjusting your filters"})]})})}):l.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"h-10 w-10 flex-shrink-0",children:(0,a.jsx)("div",{className:"h-10 w-10 rounded-full bg-primary flex items-center justify-center",children:(0,a.jsx)("i",{className:`${D(e.task_type)} text-white`})})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:e.task_number}),(0,a.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:["ID: ",e.task_id.slice(0,8),"..."]})]})]})}),(0,a.jsxs)("td",{className:"px-6 py-4",children:[(0,a.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100 font-medium",children:e.title}),(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400 mt-1",children:e.description.length>100?`${e.description.substring(0,100)}...`:e.description})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${q(e.task_type)}`,children:e.task_type.replace("_"," ").toUpperCase()})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:e.priority&&(0,a.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${F(e.priority)}`,children:e.priority.toUpperCase()})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:"assigned"===N&&e.assigned_to?(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"text-gray-900 dark:text-gray-100",children:[e.assigned_to.first_name," ",e.assigned_to.last_name]}),(0,a.jsx)("div",{className:"text-xs",children:e.assigned_to.email})]}):(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-gray-900 dark:text-gray-100",children:new Date(e.created_at).toLocaleDateString()}),(0,a.jsx)("div",{className:"text-xs",children:new Date(e.created_at).toLocaleTimeString()})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:"assigned"===N&&e.assigned_by?(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsxs)("div",{className:"text-gray-900 dark:text-gray-100",children:[e.assigned_by.first_name," ",e.assigned_by.last_name]}),(0,a.jsx)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:e.assigned_at?new Date(e.assigned_at).toLocaleDateString():""})]}):(0,a.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${"pending"===e.status?"bg-yellow-100 text-yellow-800":"in_progress"===e.status?"bg-blue-100 text-blue-800":"completed"===e.status?"bg-green-100 text-green-800":"cancelled"===e.status?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}`,children:e.status.replace("_"," ").toUpperCase()})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex items-center justify-end space-x-2",children:[(0,a.jsx)("button",{className:"text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 p-1 rounded hover:bg-indigo-50 dark:hover:bg-indigo-900",title:"View Task Details",children:(0,a.jsx)("i",{className:"ri-eye-line"})}),"unassigned"===N&&(0,a.jsx)("button",{onClick:()=>f(e.task_id),className:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 p-1 rounded hover:bg-green-50 dark:hover:bg-green-900",title:"Assign Task",children:(0,a.jsx)("i",{className:"ri-user-add-line"})}),"assigned"===N&&(0,a.jsx)("button",{onClick:()=>f(e.task_id),className:"text-orange-600 hover:text-orange-900 dark:text-orange-400 dark:hover:text-orange-300 p-1 rounded hover:bg-orange-50 dark:hover:bg-orange-900",title:"Reassign Task",children:(0,a.jsx)("i",{className:"ri-user-settings-line"})})]})})]},e.task_id))})]})})}),b&&(0,a.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,a.jsx)("div",{className:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800",children:(0,a.jsxs)("div",{className:"mt-3",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Assign Task to Officer"}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Select Officer"}),(0,a.jsxs)("select",{value:k,onChange:e=>v(e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",children:[(0,a.jsx)("option",{value:"",children:"Select an officer..."}),g.map(e=>(0,a.jsxs)("option",{value:e.user_id,children:[e.first_name," ",e.last_name," - ",e.email]},e.user_id))]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,a.jsx)("button",{type:"button",onClick:()=>{f(null),v("")},className:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:"Cancel"}),(0,a.jsx)("button",{type:"button",onClick:()=>M(b,k),disabled:!k||j,className:"px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed",children:j?"Assigning...":"Assign"})]})]})})})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12377:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>g,tree:()=>o});var a=r(65239),s=r(48088),i=r(88170),n=r.n(i),d=r(30893),l={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>d[e]);r.d(t,l);let o={children:["",{children:["task-assignment",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,36802)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\task-assignment\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,69309)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\task-assignment\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\task-assignment\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},g=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/task-assignment/page",pathname:"/task-assignment",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24597:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\components\\Header.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30781:(e,t,r)=>{Promise.resolve().then(r.bind(r,6790))},33873:e=>{"use strict";e.exports=require("path")},36802:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\task-assignment\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\task-assignment\\page.tsx","default")},51336:(e,t,r)=>{Promise.resolve().then(r.bind(r,21891)),Promise.resolve().then(r.bind(r,60417))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64488:(e,t,r)=>{Promise.resolve().then(r.bind(r,24597)),Promise.resolve().then(r.bind(r,78624))},69309:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>n});var a=r(37413),s=r(78624),i=r(24597);let n={title:"Task Assignment - MACRA Digital Portal",description:"Assign various types of tasks to officers for processing and evaluation"};function d({children:e}){return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,a.jsxs)("div",{className:"flex h-screen",children:[(0,a.jsx)(s.default,{}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,a.jsx)(i.default,{}),(0,a.jsx)("main",{className:"flex-1 overflow-y-auto",children:e})]})]})})}},74075:e=>{"use strict";e.exports=require("zlib")},78624:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\Sidebar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\components\\Sidebar.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,7498,1658,5814,2335,6606],()=>r(12377));module.exports=a})();