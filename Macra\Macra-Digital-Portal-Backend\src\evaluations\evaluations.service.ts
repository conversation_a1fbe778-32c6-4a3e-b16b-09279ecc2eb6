import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { paginate, Paginated, PaginateQuery, PaginateConfig } from 'nestjs-paginate';
import { Evaluations } from '../entities/evaluations.entity';
import { EvaluationCriteria } from '../entities/evaluation-criteria.entity';
import { Applications } from '../entities/applications.entity';
import { CreateEvaluationDto } from '../dto/evaluations/create-evaluation.dto';
import { UpdateEvaluationDto } from '../dto/evaluations/update-evaluation.dto';

@Injectable()
export class EvaluationsService {
  constructor(
    @InjectRepository(Evaluations)
    private evaluationsRepository: Repository<Evaluations>,
    @InjectRepository(EvaluationCriteria)
    private evaluationCriteriaRepository: Repository<EvaluationCriteria>,
    @InjectRepository(Applications)
    private applicationsRepository: Repository<Applications>,
  ) {}

  private readonly paginateConfig: PaginateConfig<Evaluations> = {
    sortableColumns: ['created_at', 'updated_at', 'total_score', 'status'],
    searchableColumns: ['evaluators_notes', 'recommendation'],
    defaultSortBy: [['created_at', 'DESC']],
    defaultLimit: 10,
    maxLimit: 100,
    relations: ['application', 'evaluator', 'application.applicant', 'application.license_category'],
  };

  async create(createEvaluationDto: CreateEvaluationDto, createdBy: string): Promise<Evaluations> {
    // Check if application exists
    const application = await this.applicationsRepository.findOne({
      where: { application_id: createEvaluationDto.application_id },
    });

    if (!application) {
      throw new NotFoundException('Application not found');
    }

    // Check if evaluation already exists for this application
    const existingEvaluation = await this.evaluationsRepository.findOne({
      where: { application_id: createEvaluationDto.application_id },
    });

    if (existingEvaluation) {
      throw new ConflictException('Evaluation already exists for this application');
    }

    // Create evaluation
    const evaluation = this.evaluationsRepository.create({
      ...createEvaluationDto,
      created_by: createdBy,
    });

    const savedEvaluation = await this.evaluationsRepository.save(evaluation);

    // Create evaluation criteria if provided
    if (createEvaluationDto.criteria && createEvaluationDto.criteria.length > 0) {
      const criteriaEntities = createEvaluationDto.criteria.map(criteria => 
        this.evaluationCriteriaRepository.create({
          ...criteria,
          evaluation_id: savedEvaluation.evaluation_id,
          created_by: createdBy,
        })
      );

      await this.evaluationCriteriaRepository.save(criteriaEntities);
    }

    return this.findOne(savedEvaluation.evaluation_id);
  }

  async findAll(query: PaginateQuery): Promise<Paginated<Evaluations>> {
    return paginate(query, this.evaluationsRepository, this.paginateConfig);
  }

  async findOne(id: string): Promise<Evaluations> {
    const evaluation = await this.evaluationsRepository.findOne({
      where: { evaluation_id: id },
      relations: [
        'application',
        'application.applicant',
        'application.license_category',
        'evaluator',
        'creator',
        'updater',
      ],
    });

    if (!evaluation) {
      throw new NotFoundException(`Evaluation with ID ${id} not found`);
    }

    return evaluation;
  }

  async findByApplication(applicationId: string): Promise<Evaluations | null> {
    return this.evaluationsRepository.findOne({
      where: { application_id: applicationId },
      relations: [
        'application',
        'application.applicant',
        'application.license_category',
        'evaluator',
        'creator',
        'updater',
      ],
    });
  }

  async findCriteria(evaluationId: string): Promise<EvaluationCriteria[]> {
    return this.evaluationCriteriaRepository.find({
      where: { evaluation_id: evaluationId },
      order: { category: 'ASC', subcategory: 'ASC' },
    });
  }

  async update(id: string, updateEvaluationDto: UpdateEvaluationDto, updatedBy: string): Promise<Evaluations> {
    const evaluation = await this.findOne(id);

    // Update evaluation
    Object.assign(evaluation, updateEvaluationDto, { 
      updated_by: updatedBy,
      ...(updateEvaluationDto.status === 'completed' ? { completed_at: new Date() } : {})
    });

    await this.evaluationsRepository.save(evaluation);

    // Update criteria if provided
    if (updateEvaluationDto.criteria && updateEvaluationDto.criteria.length > 0) {
      // Delete existing criteria
      await this.evaluationCriteriaRepository.delete({ evaluation_id: id });

      // Create new criteria
      const criteriaEntities = updateEvaluationDto.criteria.map(criteria => 
        this.evaluationCriteriaRepository.create({
          ...criteria,
          evaluation_id: id,
          created_by: updatedBy,
        })
      );

      await this.evaluationCriteriaRepository.save(criteriaEntities);
    }

    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    const evaluation = await this.findOne(id);
    await this.evaluationsRepository.softDelete(id);
  }

  async getEvaluationStats(): Promise<{
    total: number;
    draft: number;
    completed: number;
    approved: number;
    rejected: number;
    averageScore: number;
  }> {
    const [total, draft, completed, approved, rejected] = await Promise.all([
      this.evaluationsRepository.count(),
      this.evaluationsRepository.count({ where: { status: 'draft' } }),
      this.evaluationsRepository.count({ where: { status: 'completed' } }),
      this.evaluationsRepository.count({ where: { recommendation: 'approve' } }),
      this.evaluationsRepository.count({ where: { recommendation: 'reject' } }),
    ]);

    const avgResult = await this.evaluationsRepository
      .createQueryBuilder('evaluation')
      .select('AVG(evaluation.total_score)', 'average')
      .getRawOne();

    return {
      total,
      draft,
      completed,
      approved,
      rejected,
      averageScore: parseFloat(avgResult.average) || 0,
    };
  }
}
