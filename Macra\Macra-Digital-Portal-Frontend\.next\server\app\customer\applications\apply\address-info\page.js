(()=>{var e={};e.id=6250,e.ids=[6250],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},25778:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\applications\\\\apply\\\\address-info\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\address-info\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},55642:(e,r,s)=>{Promise.resolve().then(s.bind(s,25778))},62309:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>l});var t=s(65239),a=s(48088),i=s(88170),n=s.n(i),d=s(30893),o={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>d[e]);s.d(r,o);let l={children:["",{children:["customer",{children:["applications",{children:["apply",{children:["address-info",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,25778)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\address-info\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,68736)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\address-info\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/customer/applications/apply/address-info/page",pathname:"/customer/applications/apply/address-info",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73570:(e,r,s)=>{Promise.resolve().then(s.bind(s,86464))},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86464:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>g});var t=s(60687),a=s(43210),i=s(16189),n=s(94391),d=s(13128),o=s(63213),l=s(76377),c=s(99798),p=s(25890),u=s(78637),x=s(36212);s(37590);let m={createAddress:async e=>await x.dr.createAddress(e),editAddress:async e=>await x.dr.editAddress(e)};var h=s(90678);function g(){let e=(0,i.useSearchParams)(),{isAuthenticated:r,loading:s}=(0,o.A)(),x=e.get("license_category_id"),g=e.get("application_id"),[_,v]=(0,a.useState)(!0),[y,f]=(0,a.useState)(!1),[b,j]=(0,a.useState)(null),[P,w]=(0,a.useState)(!1),[A,q]=(0,a.useState)({}),[k,C]=(0,a.useState)(null),{handleNext:N,handlePrevious:S,nextStep:M}=(0,p.f)({currentStepRoute:"address-info",licenseCategoryId:x,applicationId:g}),[E,B]=(0,a.useState)({address_line_1:"",address_line_2:"",address_line_3:"",city:"",postal_code:"",country:"Malawi"}),[D,F]=(0,a.useState)(null),[G,I]=(0,a.useState)(null),T=(e,r)=>{B(s=>({...s,[e]:r})),w(!0),k&&C(null),A[e]&&q(r=>{let s={...r};return delete s[e],s})},R=e=>r=>{T(e,r.target.value)},z=async()=>{if(!g)return j("Application ID is required"),!1;f(!0);try{let e,r=(0,h.oQ)(E,"address");if(!r.isValid)return q(r.errors||{}),!1;let s=await u.k.getApplication(g);if(!s.applicant_id)throw Error("No applicant found for this application");let t={address_type:"business",entity_type:"applicant",entity_id:s.applicant_id,address_line_1:String(E.address_line_1||""),address_line_2:E.address_line_2?String(E.address_line_2):void 0,address_line_3:E.address_line_3?String(E.address_line_3):void 0,postal_code:String(E.postal_code||"00000"),country:String(E.country||"Malawi"),city:String(E.city||"")};if(G){let r={address_id:G.address_id,...t};(e=await m.editAddress(r)).address_id}else(e=await m.createAddress(t)).address_id,I(e);try{await u.k.updateApplication(g,{current_step:3,progress_percentage:36})}catch(e){}return w(!1),C("Address information saved successfully!"),q({}),setTimeout(()=>{C(null)},5e3),!0}catch(r){let e="Failed to save address information. Please try again.";return r.response?.data?.message?e=r.response.data.message:r.message&&(e=r.message),q({save:e}),!1}finally{f(!1)}},L=async()=>{await N(z)};return s||_?(0,t.jsx)(n.A,{children:(0,t.jsx)("div",{className:"flex justify-center items-center min-h-[400px]",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"})})}):b?(0,t.jsx)(n.A,{children:(0,t.jsx)("div",{className:"max-w-4xl mx-auto p-6",children:(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,t.jsx)("p",{className:"text-red-700",children:b})})})}):(0,t.jsx)(n.A,{children:(0,t.jsxs)(d.A,{onNext:L,onPrevious:()=>{S()},onSave:z,showNextButton:!0,showPreviousButton:!0,showSaveButton:!0,nextButtonText:M?`Continue to ${M.name}`:"Continue",previousButtonText:"Back to Previous Step",saveButtonText:G?"Update Address Information":"Save Address Information",nextButtonDisabled:!1,isSaving:y,children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:g?"Edit Address Information":"Address Information"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:g?"Update your address information below.":"Provide your physical and postal address details."}),g&&!D&&(0,t.jsx)("div",{className:"mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg",children:(0,t.jsx)("p",{className:"text-sm text-green-700 dark:text-green-300",children:G?"✅ Editing existing application. Your saved address information has been loaded.":"\uD83D\uDCDD Editing existing application. No address information found - you can add it below."})}),D&&(0,t.jsx)("div",{className:"mt-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg",children:(0,t.jsxs)("p",{className:"text-sm text-yellow-700 dark:text-yellow-300",children:["⚠️ ",D]})})]}),(0,t.jsx)(c.bc,{successMessage:k,errorMessage:A.save,validationErrors:Object.fromEntries(Object.entries(A).filter(([e])=>"save"!==e))}),(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h3",{className:"text-md font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Business Address"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsx)("div",{className:"md:col-span-2",children:(0,t.jsx)(l.ks,{label:"Address Line 1",value:E.address_line_1,onChange:R("address_line_1"),error:A.address_line_1,required:!0,placeholder:"Enter street address"})}),(0,t.jsx)("div",{className:"md:col-span-2",children:(0,t.jsx)(l.ks,{label:"Address Line 2",value:E.address_line_2,onChange:R("address_line_2"),error:A.address_line_2,placeholder:"Enter additional address information (optional)"})}),(0,t.jsx)("div",{className:"md:col-span-2",children:(0,t.jsx)(l.ks,{label:"Address Line 3",value:E.address_line_3,onChange:R("address_line_3"),error:A.address_line_3,placeholder:"Enter additional address information (optional)"})}),(0,t.jsx)(l.ks,{label:"City",value:E.city,onChange:R("city"),error:A.city,required:!0,placeholder:"Enter city"}),(0,t.jsx)(l.ks,{label:"Postal Code",value:E.postal_code,onChange:R("postal_code"),error:A.postal_code,placeholder:"Enter postal code"}),(0,t.jsx)(l.ks,{label:"Country",value:E.country,onChange:R("country"),error:A.country,required:!0,placeholder:"Enter country"})]})]})]})})}},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4447,7498,1658,5814,7563,6893,3128,1887,4682],()=>s(62309));module.exports=t})();