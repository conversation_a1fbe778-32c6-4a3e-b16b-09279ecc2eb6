(()=>{var e={};e.id=6250,e.ids=[6250],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},25778:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\applications\\\\apply\\\\address-info\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\address-info\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},55642:(e,r,t)=>{Promise.resolve().then(t.bind(t,25778))},62309:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>l});var a=t(65239),s=t(48088),i=t(88170),n=t.n(i),o=t(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(r,d);let l={children:["",{children:["customer",{children:["applications",{children:["apply",{children:["address-info",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,25778)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\address-info\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,68736)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\address-info\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/customer/applications/apply/address-info/page",pathname:"/customer/applications/apply/address-info",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73570:(e,r,t)=>{Promise.resolve().then(t.bind(t,86464))},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86464:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>h});var a=t(60687),s=t(43210),i=t(16189),n=t(94391),o=t(13128),d=t(63213),l=t(76377),c=t(99798),p=t(25890),u=t(78637),m=t(36212);t(37590);let _={createAddress:async e=>await m.dr.createAddress(e),editAddress:async e=>await m.dr.editAddress(e)};var y=t(90678);function h(){let e=(0,i.useSearchParams)(),{isAuthenticated:r,loading:t}=(0,d.A)(),m=e.get("license_category_id"),h=e.get("application_id"),[g,x]=(0,s.useState)(!0),[f,v]=(0,s.useState)(!1),[b,P]=(0,s.useState)(null),[j,k]=(0,s.useState)(!1),[q,w]=(0,s.useState)({}),[A,C]=(0,s.useState)(null),{handleNext:E,handlePrevious:N,nextStep:S}=(0,p.f)({currentStepRoute:"address-info",licenseCategoryId:m,applicationId:h}),[M,$]=(0,s.useState)({address_line_1:"",address_line_2:"",address_line_3:"",city:"",postal_code:"",country:"Malawi"}),[D,B]=(0,s.useState)(null),[I,F]=(0,s.useState)(null),G=(e,r)=>{$(t=>({...t,[e]:r})),k(!0),A&&C(null),q[e]&&w(r=>{let t={...r};return delete t[e],t})},Y=e=>r=>{G(e,r.target.value)},T=async()=>{if(!h)return P("Application ID is required"),!1;v(!0);try{let e,r=(0,y.oQ)(M,"address");if(!r.isValid)return w(r.errors||{}),!1;let t=await u.k.getApplication(h);if(!t.applicant_id)throw Error("No applicant found for this application");let a={address_type:"business",entity_type:"applicant",entity_id:t.applicant_id,address_line_1:String(M.address_line_1||""),address_line_2:M.address_line_2?String(M.address_line_2):void 0,address_line_3:M.address_line_3?String(M.address_line_3):void 0,postal_code:String(M.postal_code||"00000"),country:String(M.country||"Malawi"),city:String(M.city||"")};if(I){let r={address_id:I.address_id,...a};(e=await _.editAddress(r)).address_id}else(e=await _.createAddress(a)).address_id,F(e);try{await u.k.updateApplication(h,{current_step:3,progress_percentage:36})}catch(e){}return k(!1),C("Address information saved successfully!"),w({}),setTimeout(()=>{C(null)},5e3),!0}catch(r){let e="Failed to save address information. Please try again.";return r.response?.data?.message?e=r.response.data.message:r.message&&(e=r.message),w({save:e}),!1}finally{v(!1)}},O=async()=>{await E(T)};return t||g?(0,a.jsx)(n.A,{children:(0,a.jsx)("div",{className:"flex justify-center items-center min-h-[400px]",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"})})}):b?(0,a.jsx)(n.A,{children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto p-6",children:(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,a.jsx)("p",{className:"text-red-700",children:b})})})}):(0,a.jsx)(n.A,{children:(0,a.jsxs)(o.A,{onNext:O,onPrevious:()=>{N()},onSave:T,showNextButton:!0,showPreviousButton:!0,showSaveButton:!0,nextButtonText:S?`Continue to ${S.name}`:"Continue",previousButtonText:"Back to Previous Step",saveButtonText:I?"Update Address Information":"Save Address Information",nextButtonDisabled:!1,isSaving:f,children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:h?"Edit Address Information":"Address Information"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:h?"Update your address information below.":"Provide your physical and postal address details."}),h&&!D&&(0,a.jsx)("div",{className:"mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg",children:(0,a.jsx)("p",{className:"text-sm text-green-700 dark:text-green-300",children:I?"✅ Editing existing application. Your saved address information has been loaded.":"\uD83D\uDCDD Editing existing application. No address information found - you can add it below."})}),D&&(0,a.jsx)("div",{className:"mt-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-yellow-700 dark:text-yellow-300",children:["⚠️ ",D]})})]}),(0,a.jsx)(c.bc,{successMessage:A,errorMessage:q.save,validationErrors:Object.fromEntries(Object.entries(q).filter(([e])=>"save"!==e))}),(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h3",{className:"text-md font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Business Address"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsx)("div",{className:"md:col-span-2",children:(0,a.jsx)(l.ks,{label:"Address Line 1",value:M.address_line_1,onChange:Y("address_line_1"),error:q.address_line_1,required:!0,placeholder:"Enter street address"})}),(0,a.jsx)("div",{className:"md:col-span-2",children:(0,a.jsx)(l.ks,{label:"Address Line 2",value:M.address_line_2,onChange:Y("address_line_2"),error:q.address_line_2,placeholder:"Enter additional address information (optional)"})}),(0,a.jsx)("div",{className:"md:col-span-2",children:(0,a.jsx)(l.ks,{label:"Address Line 3",value:M.address_line_3,onChange:Y("address_line_3"),error:q.address_line_3,placeholder:"Enter additional address information (optional)"})}),(0,a.jsx)(l.ks,{label:"City",value:M.city,onChange:Y("city"),error:q.city,required:!0,placeholder:"Enter city"}),(0,a.jsx)(l.ks,{label:"Postal Code",value:M.postal_code,onChange:Y("postal_code"),error:q.postal_code,placeholder:"Enter postal code"}),(0,a.jsx)(l.ks,{label:"Country",value:M.country,onChange:Y("country"),error:q.country,required:!0,placeholder:"Enter country"})]})]})]})})}},90678:(e,r,t)=>{"use strict";t.d(r,{oQ:()=>s});let a={email:/^[^\s@]+@[^\s@]+\.[^\s@]+$/,phone:/^(\+265|0)[0-9]{8,9}$/,percentage:/^(100|[1-9]?[0-9])$/};a.email,a.phone,a.percentage;let s=(e,r)=>{let t={};switch(r){case"applicantInfo":["name","business_registration_number","tpin","website","email","phone","date_incorporation","place_incorporation"].forEach(r=>{e[r]&&("string"!=typeof e[r]||""!==e[r].trim())||(t[r]=`${r.replace(/_/g," ")} is required`)}),e.email&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.email)&&(t.email="Please enter a valid email address"),e.phone&&!/^[+]?[\d\s\-()]+$/.test(e.phone)&&(t.phone="Please enter a valid phone number"),e.fax&&""!==e.fax.trim()&&!/^[+]?[\d\s\-()]+$/.test(e.fax)&&(t.fax="Please enter a valid fax number"),e.date_incorporation&&!/^\d{4}-\d{2}-\d{2}$/.test(e.date_incorporation)&&(t.date_incorporation="Please enter a valid date (YYYY-MM-DD)");break;case"companyProfile":["company_name","business_registration_number","tax_number","company_type","incorporation_date","incorporation_place","company_email","company_phone","company_address","company_city","company_district","number_of_employees","annual_revenue","business_description"].forEach(r=>{e[r]&&("string"!=typeof e[r]||""!==e[r].trim())||(t[r]=`${r.replace(/_/g," ")} is required`)}),e.company_email&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.company_email)&&(t.company_email="Please enter a valid email address");break;case"businessInfo":["business_model","operational_structure","target_market","competitive_advantage","facilities_description","equipment_description","operational_areas","service_delivery_model","quality_assurance","customer_support"].forEach(r=>{e[r]&&("string"!=typeof e[r]||""!==e[r].trim())||(t[r]=`${r.replace(/_/g," ")} is required`)});break;case"serviceScope":["services_offered","geographic_coverage","service_categories","target_customers","service_capacity"].forEach(r=>{e[r]&&("string"!=typeof e[r]||""!==e[r].trim())||(t[r]=`${r.replace(/_/g," ")} is required`)});break;case"businessPlan":["executive_summary","market_analysis","financial_projections","revenue_model","investment_requirements","implementation_timeline","risk_analysis","success_metrics"].forEach(r=>{e[r]&&("string"!=typeof e[r]||""!==e[r].trim())||(t[r]=`${r.replace(/_/g," ")} is required`)});break;case"legalHistory":e.compliance_record&&""!==e.compliance_record.trim()||(t.compliance_record="Compliance record is required"),e.declaration_accepted||(t.declaration_accepted="You must accept the declaration to proceed"),e.criminal_history&&(!e.criminal_details||""===e.criminal_details.trim())&&(t.criminal_details="Please provide details of your criminal history"),e.bankruptcy_history&&(!e.bankruptcy_details||""===e.bankruptcy_details.trim())&&(t.bankruptcy_details="Please provide details of your bankruptcy history"),e.regulatory_actions&&(!e.regulatory_details||""===e.regulatory_details.trim())&&(t.regulatory_details="Please provide details of regulatory actions"),e.litigation_history&&(!e.litigation_details||""===e.litigation_details.trim())&&(t.litigation_details="Please provide details of litigation history");break;case"address":["address_line_1","city","country"].forEach(r=>{e[r]&&("string"!=typeof e[r]||""!==e[r].trim())||(t[r]=`${r.replace(/_/g," ")} is required`)});break;case"contactInfo":["primary_contact_first_name","primary_contact_last_name","primary_contact_designation","primary_contact_email","primary_contact_phone"].forEach(r=>{e[r]&&("string"!=typeof e[r]||""!==e[r].trim())||(t[r]=`${r.replace(/_/g," ")} is required`)}),e.primary_contact_email&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.primary_contact_email)&&(t.primary_contact_email="Please enter a valid email address"),e.secondary_contact_email&&""!==e.secondary_contact_email.trim()&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.secondary_contact_email)&&(t.secondary_contact_email="Please enter a valid email address"),e.primary_contact_phone&&!/^[+]?[\d\s\-()]+$/.test(e.primary_contact_phone)&&(t.primary_contact_phone="Please enter a valid phone number"),e.secondary_contact_phone&&""!==e.secondary_contact_phone.trim()&&!/^[+]?[\d\s\-()]+$/.test(e.secondary_contact_phone)&&(t.secondary_contact_phone="Please enter a valid phone number")}return{isValid:0===Object.keys(t).length,errors:t}}},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,7498,1658,5814,7563,6893,140,1887],()=>t(62309));module.exports=a})();