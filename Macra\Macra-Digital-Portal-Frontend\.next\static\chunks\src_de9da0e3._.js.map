{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/LogoutButton.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { useRouter, usePathname } from 'next/navigation';\r\nimport { useAuth } from '../contexts/AuthContext';\r\n\r\ninterface LogoutButtonProps {\r\n  variant?: 'primary' | 'secondary' | 'text' | 'icon';\r\n  size?: 'sm' | 'md' | 'lg';\r\n  className?: string;\r\n  showConfirmation?: boolean;\r\n  redirectTo?: string;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nexport default function LogoutButton({\r\n  variant = 'primary',\r\n  size = 'md',\r\n  className = '',\r\n  showConfirmation = true,\r\n  redirectTo = '/auth/login',\r\n  children,\r\n}: LogoutButtonProps) {\r\n  const [isLoggingOut, setIsLoggingOut] = useState(false);\r\n  const [showConfirm, setShowConfirm] = useState(false);\r\n  const { logout, user } = useAuth();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  const handleLogout = async () => {\r\n    if (showConfirmation && !showConfirm) {\r\n      setShowConfirm(true);\r\n      return;\r\n    }\r\n\r\n    setIsLoggingOut(true);\r\n    \r\n    try {\r\n      // Call logout from context\r\n      logout();\r\n      \r\n      // Small delay to ensure cleanup is complete\r\n      await new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n      if(pathname.includes('customer')){\r\n        // Redirect to specified page\r\n        router.push('/customer/auth/login');\r\n      } else {\r\n        router.push('/auth/login');\r\n      }\r\n      \r\n\r\n      \r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n    } finally {\r\n      setIsLoggingOut(false);\r\n      setShowConfirm(false);\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    setShowConfirm(false);\r\n  };\r\n\r\n  // Base styles for different variants\r\n  const getVariantStyles = () => {\r\n    switch (variant) {\r\n      case 'primary':\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n      case 'secondary':\r\n        return 'bg-white hover:bg-gray-50 text-red-600 border border-red-600';\r\n      case 'text':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none';\r\n      case 'icon':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none p-2';\r\n      default:\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n    }\r\n  };\r\n\r\n  // Size styles\r\n  const getSizeStyles = () => {\r\n    switch (size) {\r\n      case 'sm':\r\n        return 'px-3 py-1.5 text-sm';\r\n      case 'md':\r\n        return 'px-4 py-2 text-base';\r\n      case 'lg':\r\n        return 'px-6 py-3 text-lg';\r\n      default:\r\n        return 'px-4 py-2 text-base';\r\n    }\r\n  };\r\n\r\n  const baseStyles = 'inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\r\n  \r\n  const buttonStyles = `${baseStyles} ${getVariantStyles()} ${variant !== 'icon' ? getSizeStyles() : ''} ${className}`;\r\n\r\n  // Confirmation dialog\r\n  if (showConfirm) {\r\n    return (\r\n      <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n        <div className=\"bg-white rounded-lg p-6 max-w-sm mx-4 shadow-xl\">\r\n          <div className=\"flex items-center mb-4\">\r\n            <div className=\"flex-shrink-0\">\r\n              <svg className=\"h-6 w-6 text-red-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z\" />\r\n              </svg>\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <h3 className=\"text-lg font-medium text-gray-900\">Confirm Logout</h3>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"mb-4\">\r\n            <p className=\"text-sm text-gray-500\">\r\n              Are you sure you want to logout{user?.first_name ? `, ${user.first_name}` : ''}? You will need to login again to access your account.\r\n            </p>\r\n          </div>\r\n          \r\n          <div className=\"flex space-x-3\">\r\n            <button\r\n              onClick={handleLogout}\r\n              disabled={isLoggingOut}\r\n              className=\"flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 disabled:opacity-50\"\r\n            >\r\n              {isLoggingOut ? 'Logging out...' : 'Yes, Logout'}\r\n            </button>\r\n            <button\r\n              onClick={handleCancel}\r\n              className=\"flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200\"\r\n            >\r\n              Cancel\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <button\r\n      onClick={handleLogout}\r\n      disabled={isLoggingOut}\r\n      className={buttonStyles}\r\n      title=\"Logout\"\r\n    >\r\n      {children || (\r\n        <>\r\n          {variant === 'icon' ? (\r\n            <svg className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n            </svg>\r\n          ) : (\r\n            <>\r\n              <svg className=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n              </svg>\r\n              {isLoggingOut ? 'Logging out...' : 'Logout'}\r\n            </>\r\n          )}\r\n        </>\r\n      )}\r\n    </button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAee,SAAS,aAAa,EACnC,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,mBAAmB,IAAI,EACvB,aAAa,aAAa,EAC1B,QAAQ,EACU;;IAClB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe;QACnB,IAAI,oBAAoB,CAAC,aAAa;YACpC,eAAe;YACf;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,2BAA2B;YAC3B;YAEA,4CAA4C;YAC5C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,IAAG,SAAS,QAAQ,CAAC,aAAY;gBAC/B,6BAA6B;gBAC7B,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QAIF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,gBAAgB;YAChB,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,eAAe;IACjB;IAEA,qCAAqC;IACrC,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,cAAc;IACd,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa;IAEnB,MAAM,eAAe,GAAG,WAAW,CAAC,EAAE,mBAAmB,CAAC,EAAE,YAAY,SAAS,kBAAkB,GAAG,CAAC,EAAE,WAAW;IAEpH,sBAAsB;IACtB,IAAI,aAAa;QACf,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAAuB,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CAC3E,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;;;;;;;;;;;;kCAItD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;gCAAwB;gCACH,MAAM,aAAa,CAAC,EAAE,EAAE,KAAK,UAAU,EAAE,GAAG;gCAAG;;;;;;;;;;;;kCAInF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,eAAe,mBAAmB;;;;;;0CAErC,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,6LAAC;QACC,SAAS;QACT,UAAU;QACV,WAAW;QACX,OAAM;kBAEL,0BACC;sBACG,YAAY,uBACX,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,SAAQ;gBAAY,QAAO;0BAC9D,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;qCAGvE;;kCACE,6LAAC;wBAAI,WAAU;wBAAe,MAAK;wBAAO,SAAQ;wBAAY,QAAO;kCACnE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAEtE,eAAe,mBAAmB;;;;;;;;;AAOjD;GAvJwB;;QAUG,kIAAA,CAAA,UAAO;QACjB,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAZN", "debugId": null}}, {"offset": {"line": 277, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/customer/CustomerLayout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { usePathname, useRouter } from 'next/navigation';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useLoading } from '@/contexts/LoadingContext';\r\nimport LogoutButton from '../LogoutButton';\r\n\r\ninterface BreadcrumbItem {\r\n  label: string;\r\n  href?: string;\r\n}\r\n\r\ninterface CustomerLayoutProps {\r\n  children: React.ReactNode;\r\n  breadcrumbs?: BreadcrumbItem[];\r\n}\r\n\r\nconst CustomerLayout: React.FC<CustomerLayoutProps> = ({ children, breadcrumbs }) => {\r\n  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);\r\n  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);\r\n  const pathname = usePathname();\r\n  const router = useRouter();\r\n  const { user, logout } = useAuth();\r\n  const { showLoader } = useLoading();\r\n\r\n  // Memoize navigation items to prevent unnecessary re-renders\r\n  const navigationItems = useMemo(() => [\r\n    {\r\n      name: 'Dashboard',\r\n      href: '/customer',\r\n      icon: 'ri-dashboard-line',\r\n      current: pathname === '/customer'\r\n    },\r\n    {\r\n      name: 'My Licenses',\r\n      href: '/customer/my-licenses',\r\n      icon: 'ri-key-line',\r\n      current: pathname === '/customer/my-licenses'\r\n    },\r\n    {\r\n      name: 'New Applications',\r\n      href: '/customer/applications',\r\n      icon: 'ri-file-list-3-line',\r\n      current: pathname === '/customer/applications'\r\n    },\r\n    {\r\n      name: 'Payments',\r\n      href: '/customer/payments',\r\n      icon: 'ri-bank-card-line',\r\n      current: pathname === '/customer/payments'\r\n    },\r\n    {\r\n      name: 'Documents',\r\n      href: '/customer/documents',\r\n      icon: 'ri-file-text-line',\r\n      current: pathname === '/customer/documents'\r\n    },\r\n    {\r\n      name: 'Procurement',\r\n      href: '/customer/procurement',\r\n      icon: 'ri-auction-line',\r\n      current: pathname === '/customer/procurement'\r\n    },\r\n    {\r\n      name: 'Request Resource',\r\n      href: '/customer/resources',\r\n      icon: 'ri-hand-heart-line',\r\n      current: pathname === '/customer/resources'\r\n    }\r\n  ], [pathname]);\r\n\r\n  const supportItems = useMemo(() => [\r\n    {\r\n      name: 'Data Protection',\r\n      href: '/customer/data-protection',\r\n      icon: 'ri-shield-keyhole-line'\r\n    },\r\n    {\r\n      name: 'Help Center',\r\n      href: '/customer/help',\r\n      icon: 'ri-question-line'\r\n    }\r\n  ], []);\r\n\r\n  // Prefetch customer pages on mount for faster navigation\r\n  useEffect(() => {\r\n    const prefetchPages = () => {\r\n      const customerPages = [\r\n        '/customer',\r\n        '/customer/applications',\r\n        '/customer/applications/standards',\r\n        '/customer/payments',\r\n        '/customer/my-licenses',\r\n        '/customer/procurement',\r\n        '/customer/profile',\r\n        '/customer/data-protection',\r\n        '/customer/resources',\r\n        '/customer/help'\r\n      ];\r\n\r\n      customerPages.forEach(page => {\r\n        router.prefetch(page);\r\n      });\r\n    };\r\n\r\n    // Delay prefetching to not interfere with initial page load\r\n    const timer = setTimeout(prefetchPages, 1000);\r\n    return () => clearTimeout(timer);\r\n  }, [router]);\r\n\r\n  const toggleMobileSidebar = useCallback(() => {\r\n    setIsMobileSidebarOpen(!isMobileSidebarOpen);\r\n  }, [isMobileSidebarOpen]);\r\n\r\n  const handleNavClick = useCallback((href: string, name: string) => {\r\n    const pageMessages: { [key: string]: string } = {\r\n      '/customer': 'Loading Dashboard...',\r\n      '/customer/my-licenses': 'Loading My Licenses...',\r\n      '/customer/applications': 'Loading Applications...',\r\n      '/customer/applications/apply/': 'Loading Standards License Options...',\r\n      '/customer/payments': 'Loading Payments...',\r\n      '/customer/documents': 'Loading Documents...',\r\n      '/customer/procurement': 'Loading Procurement...',\r\n      '/customer/resources': 'Loading Resources...',\r\n      '/customer/data-protection': 'Loading Data Protection...',\r\n      '/customer/help': 'Loading Help Center...',\r\n      '/customer/profile': 'Loading Profile...',\r\n      '/customer/settings': 'Loading Settings...'\r\n    };\r\n\r\n    const message = pageMessages[href] || `Loading ${name}...`;\r\n    showLoader(message);\r\n    setIsMobileSidebarOpen(false);\r\n  }, [showLoader]);\r\n\r\n  const handleNavHover = useCallback((href: string) => {\r\n    // Prefetch on hover for instant navigation\r\n    router.prefetch(href);\r\n  }, [router]);\r\n\r\n  const toggleUserDropdown = useCallback(() => {\r\n    setIsUserDropdownOpen(!isUserDropdownOpen);\r\n  }, [isUserDropdownOpen]);\r\n\r\n  return (\r\n    <div className=\"flex h-screen overflow-hidden\">\r\n      {/* Mobile sidebar overlay */}\r\n      {isMobileSidebarOpen && (\r\n        <div \r\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden\"\r\n          onClick={() => setIsMobileSidebarOpen(false)}\r\n        />\r\n      )}\r\n\r\n      {/* Sidebar */}\r\n      <aside className={`\r\n        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out gpu-accelerated\r\n        ${isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}\r\n      `}>\r\n        <div className=\"flex flex-col h-full\">\r\n          {/* Logo/Header */}\r\n          <div className=\"h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700\">\r\n            <div className=\"flex items-center\">\r\n              <Image \r\n                src=\"/images/macra-logo.png\" \r\n                alt=\"MACRA Logo\" \r\n                className=\"max-h-12 w-auto\" \r\n                width={120}\r\n                height={48}\r\n                priority\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Navigation */}\r\n          <nav className=\"mt-6 px-4 side-nav\">\r\n            {/* Main Navigation */}\r\n            <div className=\"space-y-1\">\r\n              {navigationItems.map((item) => (\r\n                <Link\r\n                  key={item.name}\r\n                  href={item.href}\r\n                  onClick={() => handleNavClick(item.href, item.name)}\r\n                  onMouseEnter={() => handleNavHover(item.href)}\r\n                  className={`\r\n                    flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated\r\n                    ${item.current\r\n                      ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-r-2 border-red-600 dark:border-red-400'\r\n                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100'\r\n                    }\r\n                  `}\r\n                >\r\n                  <div className={`w-5 h-5 flex items-center justify-center mr-3 ${item.current ? 'text-red-600 dark:text-red-400' : ''}`}>\r\n                    <i className={item.icon}></i>\r\n                  </div>\r\n                  {item.name}\r\n                </Link>\r\n              ))}\r\n            </div>\r\n\r\n            {/* Support Section */}\r\n            <div className=\"mt-8\">\r\n              <h3 className=\"px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                Support\r\n              </h3>\r\n              <div className=\"mt-2 space-y-1\">\r\n                {supportItems.map((item) => (\r\n                  <Link\r\n                    key={item.name}\r\n                    href={item.href}\r\n                    onClick={() => handleNavClick(item.href, item.name)}\r\n                    onMouseEnter={() => handleNavHover(item.href)}\r\n                    className=\"flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100\"\r\n                  >\r\n                    <div className=\"w-5 h-5 flex items-center justify-center mr-3\">\r\n                      <i className={item.icon}></i>\r\n                    </div>\r\n                    {item.name}\r\n                  </Link>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </nav>\r\n\r\n          {/* User Info */}\r\n          <div className=\"absolute bottom-0 w-64 p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800\">\r\n            <div className=\"flex items-center space-x-3\">\r\n              <Image\r\n                className=\"h-10 w-10 rounded-full object-cover\"\r\n                src={user?.profile_image || \"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp\"}\r\n                alt=\"Profile\"\r\n                width={40}\r\n                height={40}\r\n              />\r\n              <Link \r\n                href='/customer/profile' \r\n                className=\"flex-1 min-w-0\"\r\n                onClick={() => handleNavClick('/customer/profile', 'Profile')}\r\n              >\r\n                <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100 truncate\">\r\n                  {user ? `${user.first_name} ${user.last_name}` : 'Customer'}\r\n                </p>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 truncate\">\r\n                  {/* {user?.organizationName || 'Organization'} */}\r\n                </p>\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </aside>\r\n\r\n      {/* Main content */}\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        {/* Top header */}\r\n        <header className=\"bg-white dark:bg-gray-800 shadow-sm z-10\">\r\n          <div className=\"flex items-center justify-between h-16 px-4 sm:px-6\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={toggleMobileSidebar}\r\n              className=\"md:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none\"\r\n              aria-label=\"Open mobile menu\"\r\n            >\r\n              <div className=\"w-6 h-6 flex items-center justify-center\">\r\n                <i className=\"ri-menu-line ri-lg\"></i>\r\n              </div>\r\n            </button>\r\n            \r\n            <div className=\"flex-1\">\r\n              {breadcrumbs && breadcrumbs.length > 0 && (\r\n                <nav className=\"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400\">\r\n                  {breadcrumbs.map((item, index) => (\r\n                    <React.Fragment key={index}>\r\n                      {index > 0 && <i className=\"ri-arrow-right-s-line\"></i>}\r\n                      {item.href ? (\r\n                        <Link href={item.href} className=\"hover:text-primary\">\r\n                          {item.label}\r\n                        </Link>\r\n                      ) : (\r\n                        <span className=\"text-gray-900 dark:text-gray-100\">{item.label}</span>\r\n                      )}\r\n                    </React.Fragment>\r\n                  ))}\r\n                </nav>\r\n              )}\r\n            </div>\r\n            \r\n            <div className=\"flex items-center\">\r\n              <button\r\n                type=\"button\"\r\n                className=\"flex-shrink-0 p-1 mr-4 text-gray-500 dark:text-gray-400 rounded-full hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none relative\"\r\n              >\r\n                <span className=\"sr-only\">View notifications</span>\r\n                <div className=\"w-6 h-6 flex items-center justify-center\">\r\n                  <i className=\"ri-notification-3-line ri-lg\"></i>\r\n                </div>\r\n                <span className=\"absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white dark:ring-gray-800\"></span>\r\n              </button>\r\n              \r\n              <div className=\"relative\">\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={toggleUserDropdown}\r\n                  className=\"flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n                >\r\n                  <span className=\"sr-only\">Open user menu</span>\r\n                  <Image\r\n                    className=\"h-8 w-8 rounded-full\"\r\n                    src={user?.profile_image || \"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp\"}\r\n                    alt=\"Profile\"\r\n                    width={32}\r\n                    height={32}\r\n                  />\r\n                </button>\r\n                \r\n                {isUserDropdownOpen && (\r\n                  <div className=\"absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 dark:ring-gray-700 z-50\">\r\n                    <div className=\"py-1\">\r\n                      <Link\r\n                        href=\"/customer/profile\"\r\n                        className=\"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        onClick={() => {\r\n                          handleNavClick('/customer/profile', 'Profile');\r\n                          setIsUserDropdownOpen(false);\r\n                        }}\r\n                      >\r\n                        Your Profile\r\n                      </Link>\r\n          \r\n                      <LogoutButton\r\n                        variant=\"text\"\r\n                        size=\"sm\"\r\n                        className=\"w-full justify-start text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        showConfirmation={true}\r\n                      >\r\n                        Sign out\r\n                      </LogoutButton>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </header>\r\n\r\n        {/* Main content area */}\r\n        <main className=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900\">\r\n          <div className=\"py-6\">\r\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n              {children}\r\n            </div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CustomerLayout;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAoBA,MAAM,iBAAgD,CAAC,EAAE,QAAQ,EAAE,WAAW,EAAE;;IAC9E,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,aAAU,AAAD;IAEhC,6DAA6D;IAC7D,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;mDAAE,IAAM;gBACpC;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;aACD;kDAAE;QAAC;KAAS;IAEb,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;gDAAE,IAAM;gBACjC;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;gBACR;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;gBACR;aACD;+CAAE,EAAE;IAEL,yDAAyD;IACzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;0DAAgB;oBACpB,MAAM,gBAAgB;wBACpB;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;qBACD;oBAED,cAAc,OAAO;kEAAC,CAAA;4BACpB,OAAO,QAAQ,CAAC;wBAClB;;gBACF;;YAEA,4DAA4D;YAC5D,MAAM,QAAQ,WAAW,eAAe;YACxC;4CAAO,IAAM,aAAa;;QAC5B;mCAAG;QAAC;KAAO;IAEX,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE;YACtC,uBAAuB,CAAC;QAC1B;0DAAG;QAAC;KAAoB;IAExB,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC,MAAc;YAChD,MAAM,eAA0C;gBAC9C,aAAa;gBACb,yBAAyB;gBACzB,0BAA0B;gBAC1B,iCAAiC;gBACjC,sBAAsB;gBACtB,uBAAuB;gBACvB,yBAAyB;gBACzB,uBAAuB;gBACvB,6BAA6B;gBAC7B,kBAAkB;gBAClB,qBAAqB;gBACrB,sBAAsB;YACxB;YAEA,MAAM,UAAU,YAAY,CAAC,KAAK,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,CAAC;YAC1D,WAAW;YACX,uBAAuB;QACzB;qDAAG;QAAC;KAAW;IAEf,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC;YAClC,2CAA2C;YAC3C,OAAO,QAAQ,CAAC;QAClB;qDAAG;QAAC;KAAO;IAEX,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE;YACrC,sBAAsB,CAAC;QACzB;yDAAG;QAAC;KAAmB;IAEvB,qBACE,6LAAC;QAAI,WAAU;;YAEZ,qCACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,uBAAuB;;;;;;0BAK1C,6LAAC;gBAAM,WAAW,CAAC;;QAEjB,EAAE,sBAAsB,kBAAkB,qCAAqC;MACjF,CAAC;0BACC,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,WAAU;oCACV,OAAO;oCACP,QAAQ;oCACR,QAAQ;;;;;;;;;;;;;;;;sCAMd,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC,+JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;4CAClD,cAAc,IAAM,eAAe,KAAK,IAAI;4CAC5C,WAAW,CAAC;;oBAEV,EAAE,KAAK,OAAO,GACV,8GACA,wHACH;kBACH,CAAC;;8DAED,6LAAC;oDAAI,WAAW,CAAC,8CAA8C,EAAE,KAAK,OAAO,GAAG,mCAAmC,IAAI;8DACrH,cAAA,6LAAC;wDAAE,WAAW,KAAK,IAAI;;;;;;;;;;;gDAExB,KAAK,IAAI;;2CAfL,KAAK,IAAI;;;;;;;;;;8CAqBpB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAuF;;;;;;sDAGrG,6LAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,6LAAC,+JAAA,CAAA,UAAI;oDAEH,MAAM,KAAK,IAAI;oDACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;oDAClD,cAAc,IAAM,eAAe,KAAK,IAAI;oDAC5C,WAAU;;sEAEV,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAE,WAAW,KAAK,IAAI;;;;;;;;;;;wDAExB,KAAK,IAAI;;mDATL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;sCAiBxB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,gIAAA,CAAA,UAAK;wCACJ,WAAU;wCACV,KAAK,MAAM,iBAAiB;wCAC5B,KAAI;wCACJ,OAAO;wCACP,QAAQ;;;;;;kDAEV,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,eAAe,qBAAqB;;0DAEnD,6LAAC;gDAAE,WAAU;0DACV,OAAO,GAAG,KAAK,UAAU,CAAC,CAAC,EAAE,KAAK,SAAS,EAAE,GAAG;;;;;;0DAEnD,6LAAC;gDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUvB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAO,WAAU;kCAChB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,cAAW;8CAEX,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;;;;;;;;;;;;;;;;8CAIjB,6LAAC;oCAAI,WAAU;8CACZ,eAAe,YAAY,MAAM,GAAG,mBACnC,6LAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;;oDACZ,QAAQ,mBAAK,6LAAC;wDAAE,WAAU;;;;;;oDAC1B,KAAK,IAAI,iBACR,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAM,KAAK,IAAI;wDAAE,WAAU;kEAC9B,KAAK,KAAK;;;;;6EAGb,6LAAC;wDAAK,WAAU;kEAAoC,KAAK,KAAK;;;;;;;+CAP7C;;;;;;;;;;;;;;;8CAe7B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,WAAU;;8DAEV,6LAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAE,WAAU;;;;;;;;;;;8DAEf,6LAAC;oDAAK,WAAU;;;;;;;;;;;;sDAGlB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;;sEAEV,6LAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,6LAAC,gIAAA,CAAA,UAAK;4DACJ,WAAU;4DACV,KAAK,MAAM,iBAAiB;4DAC5B,KAAI;4DACJ,OAAO;4DACP,QAAQ;;;;;;;;;;;;gDAIX,oCACC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,+JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;gEACV,SAAS;oEACP,eAAe,qBAAqB;oEACpC,sBAAsB;gEACxB;0EACD;;;;;;0EAID,6LAAC,qIAAA,CAAA,UAAY;gEACX,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,kBAAkB;0EACnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAYf,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GAlVM;;QAGa,qIAAA,CAAA,cAAW;QACb,qIAAA,CAAA,YAAS;QACC,kIAAA,CAAA,UAAO;QACT,qIAAA,CAAA,aAAU;;;KAN7B;uCAoVS", "debugId": null}}, {"offset": {"line": 924, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/forms/TextInput.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { forwardRef } from 'react';\r\n\r\ninterface TextInputProps extends React.InputHTMLAttributes<HTMLInputElement> {\r\n  label?: React.ReactNode;\r\n  error?: string;\r\n  helperText?: string;\r\n  variant?: 'default' | 'small';\r\n  fullWidth?: boolean;\r\n}\r\n\r\nconst TextInput = forwardRef<HTMLInputElement, TextInputProps>(({\r\n  label,\r\n  error,\r\n  helperText,\r\n  variant = 'default',\r\n  fullWidth = true,\r\n  className = '',\r\n  required,\r\n  disabled,\r\n  ...props\r\n}, ref) => {\r\n  // Base input styling with proper text visibility for all modes\r\n  const baseInputClass = `px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 ${\r\n    fullWidth ? 'w-full' : ''\r\n  } ${variant === 'small' ? 'py-1.5 text-sm' : 'py-2'}`;\r\n\r\n  // Error and disabled states\r\n  const inputClass = `${baseInputClass} ${\r\n    error \r\n      ? \"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500\" \r\n      : \"border-gray-300 dark:border-gray-600\"\r\n  } ${\r\n    disabled \r\n      ? \"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800\" \r\n      : \"\"\r\n  } ${className}`;\r\n\r\n  const labelClass = `block font-medium text-gray-700 dark:text-gray-300 mb-2 ${\r\n    variant === 'small' ? 'text-xs text-gray-600 dark:text-gray-400 mb-1' : 'text-sm'\r\n  }`;\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      {label && (\r\n        <label className={labelClass}>\r\n          {label}\r\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\r\n        </label>\r\n      )}\r\n      \r\n      <input\r\n        ref={ref}\r\n        className={inputClass}\r\n        disabled={disabled}\r\n        required={required}\r\n        {...props}\r\n      />\r\n      \r\n      {error && (\r\n        <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\r\n          {error}\r\n        </p>\r\n      )}\r\n      \r\n      {helperText && !error && (\r\n        <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n          {helperText}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n});\r\n\r\nTextInput.displayName = 'TextInput';\r\n\r\nexport default TextInput;"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAYA,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OAAoC,CAAC,EAC9D,KAAK,EACL,KAAK,EACL,UAAU,EACV,UAAU,SAAS,EACnB,YAAY,IAAI,EAChB,YAAY,EAAE,EACd,QAAQ,EACR,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,+DAA+D;IAC/D,MAAM,iBAAiB,CAAC,kPAAkP,EACxQ,YAAY,WAAW,GACxB,CAAC,EAAE,YAAY,UAAU,mBAAmB,QAAQ;IAErD,4BAA4B;IAC5B,MAAM,aAAa,GAAG,eAAe,CAAC,EACpC,QACI,+EACA,uCACL,CAAC,EACA,WACI,8DACA,GACL,CAAC,EAAE,WAAW;IAEf,MAAM,aAAa,CAAC,wDAAwD,EAC1E,YAAY,UAAU,kDAAkD,WACxE;IAEF,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBAAM,WAAW;;oBACf;oBACA,0BAAY,6LAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,6LAAC;gBACC,KAAK;gBACL,WAAW;gBACX,UAAU;gBACV,UAAU;gBACT,GAAG,KAAK;;;;;;YAGV,uBACC,6LAAC;gBAAE,WAAU;0BACV;;;;;;YAIJ,cAAc,CAAC,uBACd,6LAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;;AAEA,UAAU,WAAW,GAAG;uCAET", "debugId": null}}, {"offset": {"line": 1008, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/forms/TextArea.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { forwardRef } from 'react';\r\n\r\ninterface TextAreaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {\r\n  label?: React.ReactNode;\r\n  error?: string;\r\n  helperText?: string;\r\n  variant?: 'default' | 'small';\r\n  fullWidth?: boolean;\r\n}\r\n\r\nconst TextArea = forwardRef<HTMLTextAreaElement, TextAreaProps>(({\r\n  label,\r\n  error,\r\n  helperText,\r\n  variant = 'default',\r\n  fullWidth = true,\r\n  className = '',\r\n  required,\r\n  disabled,\r\n  rows = 3,\r\n  ...props\r\n}, ref) => {\r\n  // Base textarea styling with proper text visibility for all modes\r\n  const baseTextAreaClass = `px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 resize-y ${\r\n    fullWidth ? 'w-full' : ''\r\n  } ${variant === 'small' ? 'py-1.5 text-sm' : 'py-2'}`;\r\n\r\n  // Error and disabled states\r\n  const textAreaClass = `${baseTextAreaClass} ${\r\n    error \r\n      ? \"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500\" \r\n      : \"border-gray-300 dark:border-gray-600\"\r\n  } ${\r\n    disabled \r\n      ? \"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800\" \r\n      : \"\"\r\n  } ${className}`;\r\n\r\n  const labelClass = `block font-medium text-gray-700 dark:text-gray-300 mb-2 ${\r\n    variant === 'small' ? 'text-xs text-gray-600 dark:text-gray-400 mb-1' : 'text-sm'\r\n  }`;\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      {label && (\r\n        <label className={labelClass}>\r\n          {label}\r\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\r\n        </label>\r\n      )}\r\n      \r\n      <textarea\r\n        ref={ref}\r\n        className={textAreaClass}\r\n        disabled={disabled}\r\n        required={required}\r\n        rows={rows}\r\n        {...props}\r\n      />\r\n      \r\n      {error && (\r\n        <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\r\n          {error}\r\n        </p>\r\n      )}\r\n      \r\n      {helperText && !error && (\r\n        <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n          {helperText}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n});\r\n\r\nTextArea.displayName = 'TextArea';\r\n\r\nexport default TextArea;"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAYA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OAAsC,CAAC,EAC/D,KAAK,EACL,KAAK,EACL,UAAU,EACV,UAAU,SAAS,EACnB,YAAY,IAAI,EAChB,YAAY,EAAE,EACd,QAAQ,EACR,QAAQ,EACR,OAAO,CAAC,EACR,GAAG,OACJ,EAAE;IACD,kEAAkE;IAClE,MAAM,oBAAoB,CAAC,2PAA2P,EACpR,YAAY,WAAW,GACxB,CAAC,EAAE,YAAY,UAAU,mBAAmB,QAAQ;IAErD,4BAA4B;IAC5B,MAAM,gBAAgB,GAAG,kBAAkB,CAAC,EAC1C,QACI,+EACA,uCACL,CAAC,EACA,WACI,8DACA,GACL,CAAC,EAAE,WAAW;IAEf,MAAM,aAAa,CAAC,wDAAwD,EAC1E,YAAY,UAAU,kDAAkD,WACxE;IAEF,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBAAM,WAAW;;oBACf;oBACA,0BAAY,6LAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,6LAAC;gBACC,KAAK;gBACL,WAAW;gBACX,UAAU;gBACV,UAAU;gBACV,MAAM;gBACL,GAAG,KAAK;;;;;;YAGV,uBACC,6LAAC;gBAAE,WAAU;0BACV;;;;;;YAIJ,cAAc,CAAC,uBACd,6LAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;;AAEA,SAAS,WAAW,GAAG;uCAER", "debugId": null}}, {"offset": {"line": 1093, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/forms/Select.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { forwardRef } from 'react';\r\n\r\ninterface SelectOption {\r\n  value: string;\r\n  label: string;\r\n}\r\n\r\ninterface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {\r\n  label?: React.ReactNode;\r\n  error?: string;\r\n  helperText?: string;\r\n  variant?: 'default' | 'small';\r\n  fullWidth?: boolean;\r\n  options?: SelectOption[];\r\n  children?: React.ReactNode;\r\n}\r\n\r\nconst Select = forwardRef<HTMLSelectElement, SelectProps>(({\r\n  label,\r\n  error,\r\n  helperText,\r\n  variant = 'default',\r\n  fullWidth = true,\r\n  className = '',\r\n  required,\r\n  disabled,\r\n  options,\r\n  children,\r\n  ...props\r\n}, ref) => {\r\n  // Base select styling with proper text visibility for all modes\r\n  const baseSelectClass = `px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors duration-200 ${\r\n    fullWidth ? 'w-full' : ''\r\n  } ${variant === 'small' ? 'py-1.5 text-sm' : 'py-2'}`;\r\n\r\n  // Error and disabled states\r\n  const selectClass = `${baseSelectClass} ${\r\n    error \r\n      ? \"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500\" \r\n      : \"border-gray-300 dark:border-gray-600\"\r\n  } ${\r\n    disabled \r\n      ? \"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800\" \r\n      : \"\"\r\n  } ${className}`;\r\n\r\n  const labelClass = `block font-medium text-gray-700 dark:text-gray-300 mb-2 ${\r\n    variant === 'small' ? 'text-xs text-gray-600 dark:text-gray-400 mb-1' : 'text-sm'\r\n  }`;\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      {label && (\r\n        <label className={labelClass}>\r\n          {label}\r\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\r\n        </label>\r\n      )}\r\n      \r\n      <select\r\n        ref={ref}\r\n        className={selectClass}\r\n        disabled={disabled}\r\n        required={required}\r\n        {...props}\r\n      >\r\n        {options ? (\r\n          options.map((option) => (\r\n            <option key={option.value} value={option.value}>\r\n              {option.label}\r\n            </option>\r\n          ))\r\n        ) : (\r\n          children\r\n        )}\r\n      </select>\r\n      \r\n      {error && (\r\n        <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\r\n          {error}\r\n        </p>\r\n      )}\r\n      \r\n      {helperText && !error && (\r\n        <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n          {helperText}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n});\r\n\r\nSelect.displayName = 'Select';\r\n\r\nexport default Select;"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAmBA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OAAkC,CAAC,EACzD,KAAK,EACL,KAAK,EACL,UAAU,EACV,UAAU,SAAS,EACnB,YAAY,IAAI,EAChB,YAAY,EAAE,EACd,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,gEAAgE;IAChE,MAAM,kBAAkB,CAAC,mMAAmM,EAC1N,YAAY,WAAW,GACxB,CAAC,EAAE,YAAY,UAAU,mBAAmB,QAAQ;IAErD,4BAA4B;IAC5B,MAAM,cAAc,GAAG,gBAAgB,CAAC,EACtC,QACI,+EACA,uCACL,CAAC,EACA,WACI,8DACA,GACL,CAAC,EAAE,WAAW;IAEf,MAAM,aAAa,CAAC,wDAAwD,EAC1E,YAAY,UAAU,kDAAkD,WACxE;IAEF,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBAAM,WAAW;;oBACf;oBACA,0BAAY,6LAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,6LAAC;gBACC,KAAK;gBACL,WAAW;gBACX,UAAU;gBACV,UAAU;gBACT,GAAG,KAAK;0BAER,UACC,QAAQ,GAAG,CAAC,CAAC,uBACX,6LAAC;wBAA0B,OAAO,OAAO,KAAK;kCAC3C,OAAO,KAAK;uBADF,OAAO,KAAK;;;;gCAK3B;;;;;;YAIH,uBACC,6LAAC;gBAAE,WAAU;0BACV;;;;;;YAIJ,cAAc,CAAC,uBACd,6LAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;;AAEA,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 1185, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/services/consumer-affairs/consumerAffairsService.ts"], "sourcesContent": ["import { apiClient } from '@/lib/apiClient';\r\nimport { processApiResponse } from '@/lib/authUtils';\r\n\r\n// Types following backend entity structure\r\nexport interface ConsumerAffairsComplaint {\r\n  complaint_id: string;\r\n  complaint_number: string;\r\n  complainant_id: string;\r\n  title: string;\r\n  description: string;\r\n  category: ComplaintCategory;\r\n  status: ComplaintStatus;\r\n  priority: ComplaintPriority;\r\n  assigned_to?: string;\r\n  resolution?: string;\r\n  internal_notes?: string;\r\n  resolved_at?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  deleted_at?: string;\r\n  created_by?: string;\r\n  updated_by?: string;\r\n\r\n  // Related data\r\n  complainant?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n\r\n  assignee?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n\r\n  attachments?: ConsumerAffairsComplaintAttachment[];\r\n  status_history?: ConsumerAffairsComplaintStatusHistory[];\r\n}\r\n\r\nexport interface ConsumerAffairsComplaintAttachment {\r\n  attachment_id: string;\r\n  complaint_id: string;\r\n  file_name: string;\r\n  file_type: string;\r\n  file_size: number;\r\n  file_path: string;\r\n  uploaded_at: string;\r\n  uploaded_by: string;\r\n}\r\n\r\nexport interface ConsumerAffairsComplaintStatusHistory {\r\n  history_id: string;\r\n  complaint_id: string;\r\n  status: ComplaintStatus;\r\n  comment?: string;\r\n  created_at: string;\r\n  created_by: string;\r\n}\r\n\r\n// Enums matching backend\r\nexport enum ComplaintCategory {\r\n  BILLING_CHARGES = 'Billing & Charges',\r\n  SERVICE_QUALITY = 'Service Quality',\r\n  NETWORK_ISSUES = 'Network Issues',\r\n  CUSTOMER_SERVICE = 'Customer Service',\r\n  CONTRACT_DISPUTES = 'Contract Disputes',\r\n  ACCESSIBILITY = 'Accessibility',\r\n  FRAUD_SCAMS = 'Fraud & Scams',\r\n  OTHER = 'Other',\r\n}\r\n\r\nexport enum ComplaintStatus {\r\n  SUBMITTED = 'submitted',\r\n  UNDER_REVIEW = 'under_review',\r\n  INVESTIGATING = 'investigating',\r\n  RESOLVED = 'resolved',\r\n  CLOSED = 'closed',\r\n}\r\n\r\nexport enum ComplaintPriority {\r\n  LOW = 'low',\r\n  MEDIUM = 'medium',\r\n  HIGH = 'high',\r\n  URGENT = 'urgent',\r\n}\r\n\r\nexport interface CreateConsumerAffairsComplaintData {\r\n  title: string;\r\n  description: string;\r\n  category: ComplaintCategory;\r\n  priority?: ComplaintPriority;\r\n  attachments?: File[];\r\n}\r\n\r\nexport interface UpdateConsumerAffairsComplaintData {\r\n  title?: string;\r\n  description?: string;\r\n  category?: ComplaintCategory;\r\n  status?: ComplaintStatus;\r\n  priority?: ComplaintPriority;\r\n  assigned_to?: string;\r\n  resolution?: string;\r\n  internal_notes?: string;\r\n  resolved_at?: string;\r\n}\r\n\r\n// Pagination interfaces following user service pattern\r\nexport interface PaginatedResponse<T> {\r\n  data: T[];\r\n  meta: {\r\n    itemsPerPage: number;\r\n    totalItems: number;\r\n    currentPage: number;\r\n    totalPages: number;\r\n    sortBy: string[][];\r\n    searchBy: string[];\r\n    search: string;\r\n    filter: Record<string, string | string[]>;\r\n  };\r\n  links: {\r\n    first: string;\r\n    previous: string;\r\n    current: string;\r\n    next: string;\r\n    last: string;\r\n  };\r\n}\r\n\r\nexport interface PaginateQuery {\r\n  page?: number;\r\n  limit?: number;\r\n  sortBy?: string[];\r\n  searchBy?: string[];\r\n  search?: string;\r\n  filter?: Record<string, string | string[]>;\r\n}\r\n\r\nexport type ConsumerAffairsComplaintsResponse = PaginatedResponse<ConsumerAffairsComplaint>;\r\n\r\nexport const consumerAffairsService = {\r\n\r\n  // Create new complaint\r\n  async createComplaint(data: CreateConsumerAffairsComplaintData): Promise<ConsumerAffairsComplaint> {\r\n    try {\r\n      console.log('🔄 Creating consumer affairs complaint:', {\r\n        title: data.title,\r\n        category: data.category,\r\n        hasAttachments: data.attachments && data.attachments.length > 0\r\n      });\r\n\r\n      const formData = new FormData();\r\n      formData.append('title', data.title);\r\n      formData.append('description', data.description);\r\n      formData.append('category', data.category);\r\n\r\n      if (data.priority) {\r\n        formData.append('priority', data.priority);\r\n      }\r\n\r\n      // Add attachments if provided\r\n      if (data.attachments && data.attachments.length > 0) {\r\n        data.attachments.forEach((file) => {\r\n          formData.append('attachments', file);\r\n        });\r\n      }\r\n\r\n      const response = await apiClient.post('/consumer-affairs-complaints', formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data',\r\n        },\r\n      });\r\n\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get all complaints with pagination\r\n  async getComplaints(query: PaginateQuery = {}): Promise<ConsumerAffairsComplaintsResponse> {\r\n    const params = new URLSearchParams();\r\n\r\n    if (query.page) params.set('page', query.page.toString());\r\n    if (query.limit) params.set('limit', query.limit.toString());\r\n    if (query.search) params.set('search', query.search);\r\n    if (query.sortBy) {\r\n      query.sortBy.forEach(sort => params.append('sortBy', sort));\r\n    }\r\n    if (query.searchBy) {\r\n      query.searchBy.forEach(search => params.append('searchBy', search));\r\n    }\r\n    if (query.filter) {\r\n      Object.entries(query.filter).forEach(([key, value]) => {\r\n        if (Array.isArray(value)) {\r\n          value.forEach(v => params.append(`filter.${key}`, v));\r\n        } else {\r\n          params.set(`filter.${key}`, value);\r\n        }\r\n      });\r\n    }\r\n\r\n    const response = await apiClient.get(`/consumer-affairs-complaints?${params.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get complaint by ID\r\n  async getComplaint(id: string): Promise<ConsumerAffairsComplaint> {\r\n    const response = await apiClient.get(`/consumer-affairs-complaints/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get complaint by ID (alias for consistency)\r\n  async getComplaintById(id: string): Promise<ConsumerAffairsComplaint> {\r\n    return this.getComplaint(id);\r\n  },\r\n\r\n  // Update complaint\r\n  async updateComplaint(id: string, data: UpdateConsumerAffairsComplaintData): Promise<ConsumerAffairsComplaint> {\r\n    const response = await apiClient.put(`/consumer-affairs-complaints/${id}`, data);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Delete complaint\r\n  async deleteComplaint(id: string): Promise<void> {\r\n    await apiClient.delete(`/consumer-affairs-complaints/${id}`);\r\n  },\r\n\r\n  // Update complaint status (for staff)\r\n  async updateComplaintStatus(id: string, status: ComplaintStatus, comment?: string): Promise<ConsumerAffairsComplaint> {\r\n    const response = await apiClient.put(`/consumer-affairs-complaints/${id}/status`, {\r\n      status,\r\n      comment\r\n    });\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Add attachment to complaint\r\n  async addAttachment(id: string, file: File): Promise<ConsumerAffairsComplaintAttachment> {\r\n    const formData = new FormData();\r\n    formData.append('files', file);\r\n\r\n    const response = await apiClient.post(`/consumer-affairs-complaints/${id}/attachments`, formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Remove attachment from complaint\r\n  async removeAttachment(complaintId: string, attachmentId: string): Promise<void> {\r\n    await apiClient.delete(`/consumer-affairs-complaints/${complaintId}/attachments/${attachmentId}`);\r\n  },\r\n\r\n\r\n\r\n  // Helper methods\r\n  getStatusColor(status: string): string {\r\n    switch (status?.toLowerCase()) {\r\n      case 'submitted': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';\r\n      case 'under_review': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\r\n      case 'investigating': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\r\n      case 'resolved': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\r\n      case 'closed': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\r\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\r\n    }\r\n  },\r\n\r\n  getPriorityColor(priority: string): string {\r\n    switch (priority?.toLowerCase()) {\r\n      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\r\n      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\r\n      case 'high': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\r\n      case 'urgent': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';\r\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\r\n    }\r\n  },\r\n\r\n  getStatusOptions(): Array<{ value: string; label: string }> {\r\n    return [\r\n      { value: 'submitted', label: 'Submitted' },\r\n      { value: 'under_review', label: 'Under Review' },\r\n      { value: 'investigating', label: 'Investigating' },\r\n      { value: 'resolved', label: 'Resolved' },\r\n      { value: 'closed', label: 'Closed' }\r\n    ];\r\n  },\r\n\r\n  getCategoryOptions(): Array<{ value: string; label: string }> {\r\n    return [\r\n      { value: 'Billing & Charges', label: 'Billing & Charges' },\r\n      { value: 'Service Quality', label: 'Service Quality' },\r\n      { value: 'Network Issues', label: 'Network Issues' },\r\n      { value: 'Customer Service', label: 'Customer Service' },\r\n      { value: 'Contract Disputes', label: 'Contract Disputes' },\r\n      { value: 'Accessibility', label: 'Accessibility' },\r\n      { value: 'Fraud & Scams', label: 'Fraud & Scams' },\r\n      { value: 'Other', label: 'Other' }\r\n    ];\r\n  },\r\n\r\n  getPriorityOptions(): Array<{ value: string; label: string }> {\r\n    return [\r\n      { value: 'low', label: 'Low' },\r\n      { value: 'medium', label: 'Medium' },\r\n      { value: 'high', label: 'High' },\r\n      { value: 'urgent', label: 'Urgent' }\r\n    ];\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AA8DO,IAAA,AAAK,2CAAA;;;;;;;;;WAAA;;AAWL,IAAA,AAAK,yCAAA;;;;;;WAAA;;AAQL,IAAA,AAAK,2CAAA;;;;;WAAA;;AA4DL,MAAM,yBAAyB;IAEpC,uBAAuB;IACvB,MAAM,iBAAgB,IAAwC;QAC5D,IAAI;YACF,QAAQ,GAAG,CAAC,2CAA2C;gBACrD,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;gBACvB,gBAAgB,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG;YAChE;YAEA,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,SAAS,KAAK,KAAK;YACnC,SAAS,MAAM,CAAC,eAAe,KAAK,WAAW;YAC/C,SAAS,MAAM,CAAC,YAAY,KAAK,QAAQ;YAEzC,IAAI,KAAK,QAAQ,EAAE;gBACjB,SAAS,MAAM,CAAC,YAAY,KAAK,QAAQ;YAC3C;YAEA,8BAA8B;YAC9B,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG,GAAG;gBACnD,KAAK,WAAW,CAAC,OAAO,CAAC,CAAC;oBACxB,SAAS,MAAM,CAAC,eAAe;gBACjC;YACF;YAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,gCAAgC,UAAU;gBAC9E,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,qCAAqC;IACrC,MAAM,eAAc,QAAuB,CAAC,CAAC;QAC3C,MAAM,SAAS,IAAI;QAEnB,IAAI,MAAM,IAAI,EAAE,OAAO,GAAG,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ;QACtD,IAAI,MAAM,KAAK,EAAE,OAAO,GAAG,CAAC,SAAS,MAAM,KAAK,CAAC,QAAQ;QACzD,IAAI,MAAM,MAAM,EAAE,OAAO,GAAG,CAAC,UAAU,MAAM,MAAM;QACnD,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,MAAM,CAAC,OAAO,CAAC,CAAA,OAAQ,OAAO,MAAM,CAAC,UAAU;QACvD;QACA,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAA,SAAU,OAAO,MAAM,CAAC,YAAY;QAC7D;QACA,IAAI,MAAM,MAAM,EAAE;YAChB,OAAO,OAAO,CAAC,MAAM,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAChD,IAAI,MAAM,OAAO,CAAC,QAAQ;oBACxB,MAAM,OAAO,CAAC,CAAA,IAAK,OAAO,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBACpD,OAAO;oBACL,OAAO,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBAC9B;YACF;QACF;QAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,OAAO,QAAQ,IAAI;QACxF,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,sBAAsB;IACtB,MAAM,cAAa,EAAU;QAC3B,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,IAAI;QACzE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8CAA8C;IAC9C,MAAM,kBAAiB,EAAU;QAC/B,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B;IAEA,mBAAmB;IACnB,MAAM,iBAAgB,EAAU,EAAE,IAAwC;QACxE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,IAAI,EAAE;QAC3E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,mBAAmB;IACnB,MAAM,iBAAgB,EAAU;QAC9B,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,6BAA6B,EAAE,IAAI;IAC7D;IAEA,sCAAsC;IACtC,MAAM,uBAAsB,EAAU,EAAE,MAAuB,EAAE,OAAgB;QAC/E,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,GAAG,OAAO,CAAC,EAAE;YAChF;YACA;QACF;QACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8BAA8B;IAC9B,MAAM,eAAc,EAAU,EAAE,IAAU;QACxC,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS;QAEzB,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,CAAC,6BAA6B,EAAE,GAAG,YAAY,CAAC,EAAE,UAAU;YAChG,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,mCAAmC;IACnC,MAAM,kBAAiB,WAAmB,EAAE,YAAoB;QAC9D,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,6BAA6B,EAAE,YAAY,aAAa,EAAE,cAAc;IAClG;IAIA,iBAAiB;IACjB,gBAAe,MAAc;QAC3B,OAAQ,QAAQ;YACd,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAiB,OAAO;YAC7B,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,kBAAiB,QAAgB;QAC/B,OAAQ,UAAU;YAChB,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA;QACE,OAAO;YACL;gBAAE,OAAO;gBAAa,OAAO;YAAY;YACzC;gBAAE,OAAO;gBAAgB,OAAO;YAAe;YAC/C;gBAAE,OAAO;gBAAiB,OAAO;YAAgB;YACjD;gBAAE,OAAO;gBAAY,OAAO;YAAW;YACvC;gBAAE,OAAO;gBAAU,OAAO;YAAS;SACpC;IACH;IAEA;QACE,OAAO;YACL;gBAAE,OAAO;gBAAqB,OAAO;YAAoB;YACzD;gBAAE,OAAO;gBAAmB,OAAO;YAAkB;YACrD;gBAAE,OAAO;gBAAkB,OAAO;YAAiB;YACnD;gBAAE,OAAO;gBAAoB,OAAO;YAAmB;YACvD;gBAAE,OAAO;gBAAqB,OAAO;YAAoB;YACzD;gBAAE,OAAO;gBAAiB,OAAO;YAAgB;YACjD;gBAAE,OAAO;gBAAiB,OAAO;YAAgB;YACjD;gBAAE,OAAO;gBAAS,OAAO;YAAQ;SAClC;IACH;IAEA;QACE,OAAO;YACL;gBAAE,OAAO;gBAAO,OAAO;YAAM;YAC7B;gBAAE,OAAO;gBAAU,OAAO;YAAS;YACnC;gBAAE,OAAO;gBAAQ,OAAO;YAAO;YAC/B;gBAAE,OAAO;gBAAU,OAAO;YAAS;SACpC;IACH;AACF", "debugId": null}}, {"offset": {"line": 1439, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/services/consumer-affairs/index.ts"], "sourcesContent": ["// Consumer Affairs Services\nexport * from './consumerAffairsService';\nexport { consumerAffairsService } from './consumerAffairsService';\n"], "names": [], "mappings": "AAAA,4BAA4B;;AAC5B", "debugId": null}}, {"offset": {"line": 1462, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/customer/ConsumerAffairsModal.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport TextInput from '@/components/forms/TextInput';\nimport TextArea from '@/components/forms/TextArea';\nimport Select from '@/components/forms/Select';\nimport { consumerAffairsService, CreateConsumerAffairsComplaintData } from '@/services/consumer-affairs';\nimport { useToast } from '@/contexts/ToastContext';\n\ninterface ConsumerAffairsModalProps {\n  onClose: () => void;\n  onSubmit: (data: any) => void;\n}\n\nconst complaintCategories = [\n  'Billing & Charges',\n  'Service Quality',\n  'Network Issues',\n  'Customer Service',\n  'Contract Disputes',\n  'Accessibility',\n  'Fraud & Scams',\n  'Other'\n];\n\nconst ConsumerAffairsModal: React.FC<ConsumerAffairsModalProps> = ({ onClose, onSubmit }) => {\n  const { showSuccess, showError } = useToast();\n  const [formData, setFormData] = useState({\n    title: '',\n    description: '',\n    category: ''\n  });\n  const [formErrors, setFormErrors] = useState<{[key: string]: string}>({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [attachments, setAttachments] = useState<File[]>([]);\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    \n    // Clear error when user starts typing\n    if (formErrors[name]) {\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n\n  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    if (e.target.files) {\n      const newFiles = Array.from(e.target.files);\n      setAttachments(prev => [...prev, ...newFiles]);\n    }\n  };\n\n  const removeAttachment = (index: number) => {\n    setAttachments(prev => prev.filter((_, i) => i !== index));\n  };\n\n  const validateForm = () => {\n    const errors: {[key: string]: string} = {};\n\n    if (!formData.title.trim()) {\n      errors.title = 'Title is required';\n    }\n\n    if (!formData.description.trim()) {\n      errors.description = 'Description is required';\n    } else if (formData.description.trim().length < 20) {\n      errors.description = 'Description must be at least 20 characters';\n    }\n\n    if (!formData.category) {\n      errors.category = 'Category is required';\n    }\n\n    setFormErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!validateForm()) {\n      return;\n    }\n\n    setIsSubmitting(true);\n    try {\n      const complaintData: CreateConsumerAffairsComplaintData = {\n        title: formData.title,\n        description: formData.description,\n        category: formData.category,\n        attachments: attachments\n      };\n\n      const response = await consumerAffairsService.createComplaint(complaintData);\n\n      if (response.success) {\n        // Show success message\n        showSuccess(\n          `Your complaint has been submitted successfully! Reference ID: ${response.data.complaint_id || 'N/A'}`,\n          6000\n        );\n\n        // Also show a browser alert as backup (temporary for debugging)\n        alert(`✅ Success! Your complaint has been submitted successfully!\\n\\nReference ID: ${response.data.complaint_id || 'N/A'}`);\n\n        // Also show a browser alert as backup (temporary for debugging)\n        alert(`✅ Success! Your complaint has been submitted successfully!\\n\\nReference ID: ${response.data.complaint_id || 'N/A'}`);\n\n        onSubmit(response.data);\n\n        // Reset form\n        setFormData({\n          title: '',\n          description: '',\n          category: ''\n        });\n        setAttachments([]);\n      } else {\n        throw new Error(response.message || 'Failed to submit complaint');\n      }\n\n    } catch (error) {\n      console.error('Error submitting complaint:', error);\n      const errorMessage = error instanceof Error ? error.message : 'Failed to submit complaint. Please try again.';\n      showError(errorMessage);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const formatFileSize = (bytes: number) => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto\">\n        <div className=\"flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\n            Lodge Consumer Affairs Complaint\n          </h3>\n          <button\n            type=\"button\"\n            onClick={onClose}\n            aria-label=\"Close modal\"\n            className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n          >\n            <i className=\"ri-close-line text-xl\"></i>\n          </button>\n        </div>\n\n        <div className=\"p-6\">\n          <div className=\"mb-6\">\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n              Submit your complaint about telecommunications services, billing issues, or other consumer concerns.\n              Our team will investigate and work to resolve your issue.\n            </p>\n          </div>\n\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            {/* Title */}\n            <TextInput\n              label=\"Complaint Title *\"\n              id=\"complaint-title\"\n              name=\"title\"\n              value={formData.title}\n              onChange={handleInputChange}\n              placeholder=\"Brief summary of your complaint\"\n              error={formErrors.title}\n              required\n            />\n\n            {/* Category */}\n            <Select\n              label=\"Category\"\n              name=\"category\"\n              value={formData.category}\n              onChange={handleInputChange}\n              error={formErrors.category}\n              required\n            >\n              <option value=\"\">Select a category</option>\n              {complaintCategories.map(category => (\n                <option key={category} value={category}>{category}</option>\n              ))}\n            </Select>\n\n            {/* Description */}\n            <div>\n              <TextArea\n                label=\"Detailed Description *\"\n                id=\"complaint-description\"\n                name=\"description\"\n                value={formData.description}\n                onChange={handleInputChange}\n                rows={6}\n                placeholder=\"Please provide a detailed description of your complaint, including dates, times, and any relevant information...\"\n                error={formErrors.description}\n                helperText=\"Minimum 20 characters required\"\n                required\n              />\n            </div>\n\n            {/* File Attachments */}\n            <div>\n              <label htmlFor=\"complaint-attachments\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                Supporting Documents (Optional)\n              </label>\n              <input\n                id=\"complaint-attachments\"\n                type=\"file\"\n                multiple\n                accept=\".pdf,.doc,.docx,.jpg,.jpeg,.png\"\n                onChange={handleFileChange}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n              />\n              <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                Accepted formats: PDF, DOC, DOCX, JPG, PNG (Max 5MB per file)\n              </p>\n\n              {/* Show selected files */}\n              {attachments.length > 0 && (\n                <div className=\"mt-3 space-y-2\">\n                  {attachments.map((file, index) => (\n                    <div key={index} className=\"flex items-center justify-between bg-gray-50 dark:bg-gray-700 p-2 rounded\">\n                      <div className=\"flex items-center\">\n                        <i className=\"ri-file-line text-gray-400 mr-2\"></i>\n                        <span className=\"text-sm text-gray-700 dark:text-gray-300\">{file.name}</span>\n                        <span className=\"text-xs text-gray-500 dark:text-gray-400 ml-2\">\n                          ({formatFileSize(file.size)})\n                        </span>\n                      </div>\n                      <button\n                        type=\"button\"\n                        onClick={() => removeAttachment(index)}\n                        className=\"text-red-500 hover:text-red-700\"\n                        aria-label={`Remove ${file.name}`}\n                      >\n                        <i className=\"ri-close-line\"></i>\n                      </button>\n                    </div>\n                  ))}\n                </div>\n              )}\n            </div>\n\n            {/* Submit Buttons */}\n            <div className=\"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700\">\n              <button\n                type=\"button\"\n                onClick={onClose}\n                className=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\n              >\n                Cancel\n              </button>\n              <button\n                type=\"submit\"\n                disabled={isSubmitting}\n                className=\"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-300\"\n              >\n                {isSubmitting ? (\n                  <>\n                    <i className=\"ri-loader-4-line animate-spin mr-2\"></i>\n                    Submitting...\n                  </>\n                ) : (\n                  <>\n                    <i className=\"ri-send-plane-line mr-2\"></i>\n                    Submit Complaint\n                  </>\n                )}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ConsumerAffairsModal;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;;;AAPA;;;;;;;AAcA,MAAM,sBAAsB;IAC1B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,uBAA4D,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE;;IACtF,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAC1C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,aAAa;QACb,UAAU;IACZ;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IACvE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAEzD,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;QAED,sCAAsC;QACtC,IAAI,UAAU,CAAC,KAAK,EAAE;YACpB,cAAc,CAAA,OAAQ,CAAC;oBACrB,GAAG,IAAI;oBACP,CAAC,KAAK,EAAE;gBACV,CAAC;QACH;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE;YAClB,MAAM,WAAW,MAAM,IAAI,CAAC,EAAE,MAAM,CAAC,KAAK;YAC1C,eAAe,CAAA,OAAQ;uBAAI;uBAAS;iBAAS;QAC/C;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,eAAe,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;IACrD;IAEA,MAAM,eAAe;QACnB,MAAM,SAAkC,CAAC;QAEzC,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,OAAO,KAAK,GAAG;QACjB;QAEA,IAAI,CAAC,SAAS,WAAW,CAAC,IAAI,IAAI;YAChC,OAAO,WAAW,GAAG;QACvB,OAAO,IAAI,SAAS,WAAW,CAAC,IAAI,GAAG,MAAM,GAAG,IAAI;YAClD,OAAO,WAAW,GAAG;QACvB;QAEA,IAAI,CAAC,SAAS,QAAQ,EAAE;YACtB,OAAO,QAAQ,GAAG;QACpB;QAEA,cAAc;QACd,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;IACxC;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,gBAAgB;QAChB,IAAI;YACF,MAAM,gBAAoD;gBACxD,OAAO,SAAS,KAAK;gBACrB,aAAa,SAAS,WAAW;gBACjC,UAAU,SAAS,QAAQ;gBAC3B,aAAa;YACf;YAEA,MAAM,WAAW,MAAM,mKAAA,CAAA,yBAAsB,CAAC,eAAe,CAAC;YAE9D,IAAI,SAAS,OAAO,EAAE;gBACpB,uBAAuB;gBACvB,YACE,CAAC,8DAA8D,EAAE,SAAS,IAAI,CAAC,YAAY,IAAI,OAAO,EACtG;gBAGF,gEAAgE;gBAChE,MAAM,CAAC,4EAA4E,EAAE,SAAS,IAAI,CAAC,YAAY,IAAI,OAAO;gBAE1H,gEAAgE;gBAChE,MAAM,CAAC,4EAA4E,EAAE,SAAS,IAAI,CAAC,YAAY,IAAI,OAAO;gBAE1H,SAAS,SAAS,IAAI;gBAEtB,aAAa;gBACb,YAAY;oBACV,OAAO;oBACP,aAAa;oBACb,UAAU;gBACZ;gBACA,eAAe,EAAE;YACnB,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,OAAO,IAAI;YACtC;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,UAAU;QACZ,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAyD;;;;;;sCAGvE,6LAAC;4BACC,MAAK;4BACL,SAAS;4BACT,cAAW;4BACX,WAAU;sCAEV,cAAA,6LAAC;gCAAE,WAAU;;;;;;;;;;;;;;;;;8BAIjB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAA2C;;;;;;;;;;;sCAM1D,6LAAC;4BAAK,UAAU;4BAAc,WAAU;;8CAEtC,6LAAC,2IAAA,CAAA,UAAS;oCACR,OAAM;oCACN,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,KAAK;oCACrB,UAAU;oCACV,aAAY;oCACZ,OAAO,WAAW,KAAK;oCACvB,QAAQ;;;;;;8CAIV,6LAAC,wIAAA,CAAA,UAAM;oCACL,OAAM;oCACN,MAAK;oCACL,OAAO,SAAS,QAAQ;oCACxB,UAAU;oCACV,OAAO,WAAW,QAAQ;oCAC1B,QAAQ;;sDAER,6LAAC;4CAAO,OAAM;sDAAG;;;;;;wCAChB,oBAAoB,GAAG,CAAC,CAAA,yBACvB,6LAAC;gDAAsB,OAAO;0DAAW;+CAA5B;;;;;;;;;;;8CAKjB,6LAAC;8CACC,cAAA,6LAAC,0IAAA,CAAA,UAAQ;wCACP,OAAM;wCACN,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,WAAW;wCAC3B,UAAU;wCACV,MAAM;wCACN,aAAY;wCACZ,OAAO,WAAW,WAAW;wCAC7B,YAAW;wCACX,QAAQ;;;;;;;;;;;8CAKZ,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAwB,WAAU;sDAAkE;;;;;;sDAGnH,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,QAAQ;4CACR,QAAO;4CACP,UAAU;4CACV,WAAU;;;;;;sDAEZ,6LAAC;4CAAE,WAAU;sDAAgD;;;;;;wCAK5D,YAAY,MAAM,GAAG,mBACpB,6LAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC;oDAAgB,WAAU;;sEACzB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;;;;;;8EACb,6LAAC;oEAAK,WAAU;8EAA4C,KAAK,IAAI;;;;;;8EACrE,6LAAC;oEAAK,WAAU;;wEAAgD;wEAC5D,eAAe,KAAK,IAAI;wEAAE;;;;;;;;;;;;;sEAGhC,6LAAC;4DACC,MAAK;4DACL,SAAS,IAAM,iBAAiB;4DAChC,WAAU;4DACV,cAAY,CAAC,OAAO,EAAE,KAAK,IAAI,EAAE;sEAEjC,cAAA,6LAAC;gEAAE,WAAU;;;;;;;;;;;;mDAdP;;;;;;;;;;;;;;;;8CAuBlB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CACC,MAAK;4CACL,UAAU;4CACV,WAAU;sDAET,6BACC;;kEACE,6LAAC;wDAAE,WAAU;;;;;;oDAAyC;;6EAIxD;;kEACE,6LAAC;wDAAE,WAAU;;;;;;oDAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW/D;GAxQM;;QAC+B,mIAAA,CAAA,WAAQ;;;KADvC;uCA0QS", "debugId": null}}, {"offset": {"line": 1923, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/services/data-breach/dataBreachService.ts"], "sourcesContent": ["import { apiClient } from '@/lib/apiClient';\r\nimport { processApiResponse } from '@/lib/authUtils';\r\n\r\n// Types following backend entity structure\r\nexport interface DataBreachReport {\r\n  report_id: string;\r\n  report_number: string;\r\n  reporter_id: string;\r\n  title: string;\r\n  description: string;\r\n  category: DataBreachCategory;\r\n  severity: DataBreachSeverity;\r\n  status: DataBreachStatus;\r\n  priority: DataBreachPriority;\r\n  incident_date: string;\r\n  organization_involved: string;\r\n  affected_data_types?: string;\r\n  contact_attempts?: string;\r\n  assigned_to?: string;\r\n  resolution?: string;\r\n  internal_notes?: string;\r\n  resolved_at?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  deleted_at?: string;\r\n  created_by?: string;\r\n  updated_by?: string;\r\n\r\n  // Related data\r\n  reporter?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n\r\n  assignee?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n\r\n  attachments?: DataBreachReportAttachment[];\r\n  status_history?: DataBreachReportStatusHistory[];\r\n}\r\n\r\nexport interface DataBreachReportAttachment {\r\n  attachment_id: string;\r\n  report_id: string;\r\n  file_name: string;\r\n  file_type: string;\r\n  file_size: number;\r\n  file_path: string;\r\n  uploaded_at: string;\r\n  uploaded_by: string;\r\n}\r\n\r\nexport interface DataBreachReportStatusHistory {\r\n  history_id: string;\r\n  report_id: string;\r\n  status: DataBreachStatus;\r\n  comment?: string;\r\n  created_at: string;\r\n  created_by: string;\r\n}\r\n\r\n// Enums matching backend\r\nexport enum DataBreachCategory {\r\n  PERSONAL_DATA = 'Personal Data',\r\n  FINANCIAL_DATA = 'Financial Data',\r\n  HEALTH_DATA = 'Health Data',\r\n  TECHNICAL_DATA = 'Technical Data',\r\n  COMMUNICATION_DATA = 'Communication Data',\r\n  OTHER = 'Other',\r\n}\r\n\r\nexport enum DataBreachSeverity {\r\n  LOW = 'low',\r\n  MEDIUM = 'medium',\r\n  HIGH = 'high',\r\n  CRITICAL = 'critical',\r\n}\r\n\r\nexport enum DataBreachStatus {\r\n  SUBMITTED = 'submitted',\r\n  UNDER_REVIEW = 'under_review',\r\n  INVESTIGATING = 'investigating',\r\n  RESOLVED = 'resolved',\r\n  CLOSED = 'closed',\r\n}\r\n\r\nexport enum DataBreachPriority {\r\n  LOW = 'low',\r\n  MEDIUM = 'medium',\r\n  HIGH = 'high',\r\n  URGENT = 'urgent',\r\n}\r\n\r\nexport interface CreateDataBreachReportData {\r\n  title: string;\r\n  description: string;\r\n  category: DataBreachCategory;\r\n  severity: DataBreachSeverity;\r\n  priority?: DataBreachPriority;\r\n  incident_date: string;\r\n  organization_involved: string;\r\n  affected_data_types?: string;\r\n  contact_attempts?: string;\r\n  attachments?: File[];\r\n}\r\n\r\nexport interface UpdateDataBreachReportData {\r\n  title?: string;\r\n  description?: string;\r\n  category?: DataBreachCategory;\r\n  severity?: DataBreachSeverity;\r\n  status?: DataBreachStatus;\r\n  priority?: DataBreachPriority;\r\n  incident_date?: string;\r\n  organization_involved?: string;\r\n  affected_data_types?: string;\r\n  contact_attempts?: string;\r\n  assigned_to?: string;\r\n  resolution?: string;\r\n  internal_notes?: string;\r\n  resolved_at?: string;\r\n}\r\n\r\n// Pagination interfaces following user service pattern\r\nexport interface PaginatedResponse<T> {\r\n  data: T[];\r\n  meta: {\r\n    itemsPerPage: number;\r\n    totalItems: number;\r\n    currentPage: number;\r\n    totalPages: number;\r\n    sortBy: string[][];\r\n    searchBy: string[];\r\n    search: string;\r\n    filter: Record<string, string | string[]>;\r\n  };\r\n  links: {\r\n    first: string;\r\n    previous: string;\r\n    current: string;\r\n    next: string;\r\n    last: string;\r\n  };\r\n}\r\n\r\nexport interface PaginateQuery {\r\n  page?: number;\r\n  limit?: number;\r\n  sortBy?: string[];\r\n  searchBy?: string[];\r\n  search?: string;\r\n  filter?: Record<string, string | string[]>;\r\n}\r\n\r\nexport type DataBreachReportsResponse = PaginatedResponse<DataBreachReport>;\r\n\r\nexport const dataBreachService = {\r\n  // Create new report\r\n  async createReport(data: CreateDataBreachReportData): Promise<DataBreachReport> {\r\n    try {\r\n      console.log('🔄 Creating data breach report:', {\r\n        title: data.title,\r\n        category: data.category,\r\n        severity: data.severity,\r\n        hasAttachments: data.attachments && data.attachments.length > 0\r\n      });\r\n\r\n      const formData = new FormData();\r\n      formData.append('title', data.title);\r\n      formData.append('description', data.description);\r\n      formData.append('category', data.category);\r\n      formData.append('severity', data.severity);\r\n      formData.append('incident_date', data.incident_date);\r\n      formData.append('organization_involved', data.organization_involved);\r\n\r\n      if (data.priority) {\r\n        formData.append('priority', data.priority);\r\n      }\r\n\r\n      if (data.affected_data_types) {\r\n        formData.append('affected_data_types', data.affected_data_types);\r\n      }\r\n\r\n      if (data.contact_attempts) {\r\n        formData.append('contact_attempts', data.contact_attempts);\r\n      }\r\n\r\n      // Add attachments if provided\r\n      if (data.attachments && data.attachments.length > 0) {\r\n        data.attachments.forEach((file) => {\r\n          formData.append('attachments', file);\r\n        });\r\n      }\r\n\r\n      const response = await apiClient.post('/data-breach-reports', formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data',\r\n        },\r\n      });\r\n\r\n      console.log('✅ Data breach report created successfully:', response.data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('❌ Error creating data breach report:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get all reports with pagination\r\n  async getReports(query: PaginateQuery = {}): Promise<DataBreachReportsResponse> {\r\n    const params = new URLSearchParams();\r\n\r\n    if (query.page) params.set('page', query.page.toString());\r\n    if (query.limit) params.set('limit', query.limit.toString());\r\n    if (query.search) params.set('search', query.search);\r\n    if (query.sortBy) {\r\n      query.sortBy.forEach(sort => params.append('sortBy', sort));\r\n    }\r\n    if (query.searchBy) {\r\n      query.searchBy.forEach(search => params.append('searchBy', search));\r\n    }\r\n    if (query.filter) {\r\n      Object.entries(query.filter).forEach(([key, value]) => {\r\n        if (Array.isArray(value)) {\r\n          value.forEach(v => params.append(`filter.${key}`, v));\r\n        } else {\r\n          params.set(`filter.${key}`, value);\r\n        }\r\n      });\r\n    }\r\n\r\n    const response = await apiClient.get(`/data-breach-reports?${params.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get report by ID\r\n  async getReport(id: string): Promise<DataBreachReport> {\r\n    const response = await apiClient.get(`/data-breach-reports/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get report by ID (alias for consistency)\r\n  async getReportById(id: string): Promise<DataBreachReport> {\r\n    return this.getReport(id);\r\n  },\r\n\r\n  // Update report\r\n  async updateReport(id: string, data: UpdateDataBreachReportData): Promise<DataBreachReport> {\r\n    const response = await apiClient.put(`/data-breach-reports/${id}`, data);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Delete report\r\n  async deleteReport(id: string): Promise<void> {\r\n    await apiClient.delete(`/data-breach-reports/${id}`);\r\n  },\r\n\r\n  // Update report status (for staff)\r\n  async updateReportStatus(id: string, status: DataBreachStatus, comment?: string): Promise<DataBreachReport> {\r\n    const response = await apiClient.put(`/data-breach-reports/${id}/status`, {\r\n      status,\r\n      comment\r\n    });\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Add attachment to report\r\n  async addAttachment(id: string, file: File): Promise<DataBreachReportAttachment> {\r\n    const formData = new FormData();\r\n    formData.append('files', file);\r\n\r\n    const response = await apiClient.post(`/data-breach-reports/${id}/attachments`, formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Remove attachment from report\r\n  async removeAttachment(reportId: string, attachmentId: string): Promise<void> {\r\n    await apiClient.delete(`/data-breach-reports/${reportId}/attachments/${attachmentId}`);\r\n  },\r\n\r\n  // Helper methods\r\n  getStatusColor(status: string): string {\r\n    switch (status?.toLowerCase()) {\r\n      case 'submitted': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';\r\n      case 'under_review': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\r\n      case 'investigating': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\r\n      case 'resolved': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\r\n      case 'closed': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\r\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\r\n    }\r\n  },\r\n\r\n  getSeverityColor(severity: string): string {\r\n    switch (severity?.toLowerCase()) {\r\n      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\r\n      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\r\n      case 'high': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\r\n      case 'critical': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';\r\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\r\n    }\r\n  },\r\n\r\n  getStatusOptions(): Array<{ value: string; label: string }> {\r\n    return [\r\n      { value: 'submitted', label: 'Submitted' },\r\n      { value: 'under_review', label: 'Under Review' },\r\n      { value: 'investigating', label: 'Investigating' },\r\n      { value: 'resolved', label: 'Resolved' },\r\n      { value: 'closed', label: 'Closed' }\r\n    ];\r\n  },\r\n\r\n  getCategoryOptions(): Array<{ value: string; label: string }> {\r\n    return [\r\n      { value: 'Personal Data', label: 'Personal Data' },\r\n      { value: 'Financial Data', label: 'Financial Data' },\r\n      { value: 'Health Data', label: 'Health Data' },\r\n      { value: 'Technical Data', label: 'Technical Data' },\r\n      { value: 'Communication Data', label: 'Communication Data' },\r\n      { value: 'Other', label: 'Other' }\r\n    ];\r\n  },\r\n\r\n  getSeverityOptions(): Array<{ value: string; label: string }> {\r\n    return [\r\n      { value: 'low', label: 'Low' },\r\n      { value: 'medium', label: 'Medium' },\r\n      { value: 'high', label: 'High' },\r\n      { value: 'critical', label: 'Critical' }\r\n    ];\r\n  },\r\n\r\n  getPriorityOptions(): Array<{ value: string; label: string }> {\r\n    return [\r\n      { value: 'low', label: 'Low' },\r\n      { value: 'medium', label: 'Medium' },\r\n      { value: 'high', label: 'High' },\r\n      { value: 'urgent', label: 'Urgent' }\r\n    ];\r\n  },\r\n};\r\n\r\n\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAmEO,IAAA,AAAK,4CAAA;;;;;;;WAAA;;AASL,IAAA,AAAK,4CAAA;;;;;WAAA;;AAOL,IAAA,AAAK,0CAAA;;;;;;WAAA;;AAQL,IAAA,AAAK,4CAAA;;;;;WAAA;;AAsEL,MAAM,oBAAoB;IAC/B,oBAAoB;IACpB,MAAM,cAAa,IAAgC;QACjD,IAAI;YACF,QAAQ,GAAG,CAAC,mCAAmC;gBAC7C,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;gBACvB,UAAU,KAAK,QAAQ;gBACvB,gBAAgB,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG;YAChE;YAEA,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,SAAS,KAAK,KAAK;YACnC,SAAS,MAAM,CAAC,eAAe,KAAK,WAAW;YAC/C,SAAS,MAAM,CAAC,YAAY,KAAK,QAAQ;YACzC,SAAS,MAAM,CAAC,YAAY,KAAK,QAAQ;YACzC,SAAS,MAAM,CAAC,iBAAiB,KAAK,aAAa;YACnD,SAAS,MAAM,CAAC,yBAAyB,KAAK,qBAAqB;YAEnE,IAAI,KAAK,QAAQ,EAAE;gBACjB,SAAS,MAAM,CAAC,YAAY,KAAK,QAAQ;YAC3C;YAEA,IAAI,KAAK,mBAAmB,EAAE;gBAC5B,SAAS,MAAM,CAAC,uBAAuB,KAAK,mBAAmB;YACjE;YAEA,IAAI,KAAK,gBAAgB,EAAE;gBACzB,SAAS,MAAM,CAAC,oBAAoB,KAAK,gBAAgB;YAC3D;YAEA,8BAA8B;YAC9B,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG,GAAG;gBACnD,KAAK,WAAW,CAAC,OAAO,CAAC,CAAC;oBACxB,SAAS,MAAM,CAAC,eAAe;gBACjC;YACF;YAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,wBAAwB,UAAU;gBACtE,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,QAAQ,GAAG,CAAC,8CAA8C,SAAS,IAAI;YACvE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM;QACR;IACF;IAEA,kCAAkC;IAClC,MAAM,YAAW,QAAuB,CAAC,CAAC;QACxC,MAAM,SAAS,IAAI;QAEnB,IAAI,MAAM,IAAI,EAAE,OAAO,GAAG,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ;QACtD,IAAI,MAAM,KAAK,EAAE,OAAO,GAAG,CAAC,SAAS,MAAM,KAAK,CAAC,QAAQ;QACzD,IAAI,MAAM,MAAM,EAAE,OAAO,GAAG,CAAC,UAAU,MAAM,MAAM;QACnD,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,MAAM,CAAC,OAAO,CAAC,CAAA,OAAQ,OAAO,MAAM,CAAC,UAAU;QACvD;QACA,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAA,SAAU,OAAO,MAAM,CAAC,YAAY;QAC7D;QACA,IAAI,MAAM,MAAM,EAAE;YAChB,OAAO,OAAO,CAAC,MAAM,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAChD,IAAI,MAAM,OAAO,CAAC,QAAQ;oBACxB,MAAM,OAAO,CAAC,CAAA,IAAK,OAAO,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBACpD,OAAO;oBACL,OAAO,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBAC9B;YACF;QACF;QAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,OAAO,QAAQ,IAAI;QAChF,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,mBAAmB;IACnB,MAAM,WAAU,EAAU;QACxB,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,IAAI;QACjE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,2CAA2C;IAC3C,MAAM,eAAc,EAAU;QAC5B,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB;IAEA,gBAAgB;IAChB,MAAM,cAAa,EAAU,EAAE,IAAgC;QAC7D,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,IAAI,EAAE;QACnE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gBAAgB;IAChB,MAAM,cAAa,EAAU;QAC3B,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,qBAAqB,EAAE,IAAI;IACrD;IAEA,mCAAmC;IACnC,MAAM,oBAAmB,EAAU,EAAE,MAAwB,EAAE,OAAgB;QAC7E,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,GAAG,OAAO,CAAC,EAAE;YACxE;YACA;QACF;QACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,2BAA2B;IAC3B,MAAM,eAAc,EAAU,EAAE,IAAU;QACxC,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS;QAEzB,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,CAAC,qBAAqB,EAAE,GAAG,YAAY,CAAC,EAAE,UAAU;YACxF,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gCAAgC;IAChC,MAAM,kBAAiB,QAAgB,EAAE,YAAoB;QAC3D,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,qBAAqB,EAAE,SAAS,aAAa,EAAE,cAAc;IACvF;IAEA,iBAAiB;IACjB,gBAAe,MAAc;QAC3B,OAAQ,QAAQ;YACd,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAiB,OAAO;YAC7B,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,kBAAiB,QAAgB;QAC/B,OAAQ,UAAU;YAChB,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA;QACE,OAAO;YACL;gBAAE,OAAO;gBAAa,OAAO;YAAY;YACzC;gBAAE,OAAO;gBAAgB,OAAO;YAAe;YAC/C;gBAAE,OAAO;gBAAiB,OAAO;YAAgB;YACjD;gBAAE,OAAO;gBAAY,OAAO;YAAW;YACvC;gBAAE,OAAO;gBAAU,OAAO;YAAS;SACpC;IACH;IAEA;QACE,OAAO;YACL;gBAAE,OAAO;gBAAiB,OAAO;YAAgB;YACjD;gBAAE,OAAO;gBAAkB,OAAO;YAAiB;YACnD;gBAAE,OAAO;gBAAe,OAAO;YAAc;YAC7C;gBAAE,OAAO;gBAAkB,OAAO;YAAiB;YACnD;gBAAE,OAAO;gBAAsB,OAAO;YAAqB;YAC3D;gBAAE,OAAO;gBAAS,OAAO;YAAQ;SAClC;IACH;IAEA;QACE,OAAO;YACL;gBAAE,OAAO;gBAAO,OAAO;YAAM;YAC7B;gBAAE,OAAO;gBAAU,OAAO;YAAS;YACnC;gBAAE,OAAO;gBAAQ,OAAO;YAAO;YAC/B;gBAAE,OAAO;gBAAY,OAAO;YAAW;SACxC;IACH;IAEA;QACE,OAAO;YACL;gBAAE,OAAO;gBAAO,OAAO;YAAM;YAC7B;gBAAE,OAAO;gBAAU,OAAO;YAAS;YACnC;gBAAE,OAAO;gBAAQ,OAAO;YAAO;YAC/B;gBAAE,OAAO;gBAAU,OAAO;YAAS;SACpC;IACH;AACF", "debugId": null}}, {"offset": {"line": 2207, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/services/data-breach/index.ts"], "sourcesContent": ["export * from './dataBreachService';\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 2228, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/customer/DataBreachModal.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport TextInput from '@/components/forms/TextInput';\nimport TextArea from '@/components/forms/TextArea';\nimport Select from '@/components/forms/Select';\nimport { dataBreachService, CreateDataBreachReportData } from '@/services/data-breach';\nimport { useToast } from '@/contexts/ToastContext';\n\ninterface DataBreachModalProps {\n  onClose: () => void;\n  onSubmit: (data: any) => void;\n}\n\nconst breachCategories = [\n  'Unauthorized Data Access',\n  'Data Misuse or Sharing',\n  'Privacy Violations',\n  'Identity Theft',\n  'Phishing Attempts',\n  'Data Loss or Theft',\n  'Consent Violations',\n  'Other'\n];\n\nconst severityLevels = [\n  { value: 'low', label: 'Low - Minor privacy concern' },\n  { value: 'medium', label: 'Medium - Moderate data exposure' },\n  { value: 'high', label: 'High - Significant data breach' },\n  { value: 'critical', label: 'Critical - Severe security incident' }\n];\n\nconst DataBreachModal: React.FC<DataBreachModalProps> = ({ onClose, onSubmit }) => {\n  const { showSuccess, showError } = useToast();\n  const [formData, setFormData] = useState({\n    title: '',\n    description: '',\n    category: '',\n    severity: '',\n    incidentDate: '',\n    affectedData: '',\n    organization: '',\n    contactAttempts: ''\n  });\n  const [formErrors, setFormErrors] = useState<{[key: string]: string}>({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [attachments, setAttachments] = useState<File[]>([]);\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    \n    // Clear error when user starts typing\n    if (formErrors[name]) {\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n\n  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    if (e.target.files) {\n      const newFiles = Array.from(e.target.files);\n      setAttachments(prev => [...prev, ...newFiles]);\n    }\n  };\n\n  const removeAttachment = (index: number) => {\n    setAttachments(prev => prev.filter((_, i) => i !== index));\n  };\n\n  const validateForm = () => {\n    const errors: {[key: string]: string} = {};\n\n    if (!formData.title.trim()) {\n      errors.title = 'Title is required';\n    }\n\n    if (!formData.description.trim()) {\n      errors.description = 'Description is required';\n    } else if (formData.description.trim().length < 20) {\n      errors.description = 'Description must be at least 20 characters';\n    }\n\n    if (!formData.category) {\n      errors.category = 'Category is required';\n    }\n\n    if (!formData.severity) {\n      errors.severity = 'Severity level is required';\n    }\n\n    if (!formData.incidentDate) {\n      errors.incidentDate = 'Incident date is required';\n    }\n\n    if (!formData.organization.trim()) {\n      errors.organization = 'Organization involved is required';\n    }\n\n    setFormErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!validateForm()) {\n      return;\n    }\n\n    setIsSubmitting(true);\n    try {\n      const reportData: CreateDataBreachReportData = {\n        title: formData.title,\n        description: formData.description,\n        category: formData.category,\n        severity: formData.severity,\n        incident_date: formData.incidentDate,\n        organization_involved: formData.organization,\n        affected_data_types: formData.affectedData,\n        contact_attempts: formData.contactAttempts,\n        attachments: attachments\n      };\n\n      const response = await dataBreachService.createReport(reportData);\n\n      // Show success message\n      showSuccess(\n        `Your data breach report has been submitted successfully! Reference ID: ${response.report_id || 'N/A'}`,\n        6000\n      );\n\n      // Also show a browser alert as backup (temporary for debugging)\n      alert(`✅ Success! Your data breach report has been submitted successfully!\\n\\nReference ID: ${response.report_id || 'N/A'}`);\n\n      onSubmit(response);\n\n      // Reset form\n      setFormData({\n        title: '',\n        description: '',\n        category: '',\n        severity: '',\n        incidentDate: '',\n        affectedData: '',\n        organization: '',\n        contactAttempts: ''\n      });\n      setAttachments([]);\n\n    } catch (error) {\n      console.error('Error submitting data breach report:', error);\n      const errorMessage = error instanceof Error ? error.message : 'Failed to submit data breach report. Please try again.';\n      showError(errorMessage);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const formatFileSize = (bytes: number) => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto\">\n        <div className=\"flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\n            Report Data Breach\n          </h3>\n          <button\n            type=\"button\"\n            onClick={onClose}\n            aria-label=\"Close modal\"\n            className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n          >\n            <i className=\"ri-close-line text-xl\"></i>\n          </button>\n        </div>\n\n        <div className=\"p-6\">\n          <div className=\"mb-6\">\n            <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\">\n              <div className=\"flex\">\n                <i className=\"ri-shield-keyhole-line text-red-600 text-lg mr-3 mt-0.5\"></i>\n                <div>\n                  <h4 className=\"text-sm font-medium text-red-800 dark:text-red-300 mb-1\">\n                    Data Breach Reporting\n                  </h4>\n                  <p className=\"text-sm text-red-700 dark:text-red-400\">\n                    Report unauthorized access, misuse, or breach of your personal data. This information will be treated confidentially and investigated promptly.\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            {/* Title */}\n            <TextInput\n              label=\"Incident Title *\"\n              id=\"breach-title\"\n              name=\"title\"\n              value={formData.title}\n              onChange={handleInputChange}\n              placeholder=\"Brief summary of the data breach incident\"\n              error={formErrors.title}\n              required\n            />\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {/* Category */}\n              <Select\n                label=\"Breach Category *\"\n                name=\"category\"\n                value={formData.category}\n                onChange={handleInputChange}\n                error={formErrors.category}\n                required\n              >\n                <option value=\"\">Select a category</option>\n                {breachCategories.map(category => (\n                  <option key={category} value={category}>{category}</option>\n                ))}\n              </Select>\n\n              {/* Severity */}\n              <Select\n                label=\"Severity Level *\"\n                name=\"severity\"\n                value={formData.severity}\n                onChange={handleInputChange}\n                error={formErrors.severity}\n                required\n              >\n                <option value=\"\">Select severity level</option>\n                {severityLevels.map(level => (\n                  <option key={level.value} value={level.value}>{level.label}</option>\n                ))}\n              </Select>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {/* Incident Date */}\n              <TextInput\n                label=\"Incident Date *\"\n                id=\"incident-date\"\n                name=\"incidentDate\"\n                type=\"date\"\n                value={formData.incidentDate}\n                onChange={handleInputChange}\n                error={formErrors.incidentDate}\n                required\n              />\n\n              {/* Organization */}\n              <TextInput\n                label=\"Organization Involved *\"\n                id=\"organization\"\n                name=\"organization\"\n                value={formData.organization}\n                onChange={handleInputChange}\n                placeholder=\"Name of the organization responsible\"\n                error={formErrors.organization}\n                required\n              />\n            </div>\n\n            {/* Affected Data */}\n            <TextArea\n              label=\"Affected Data Types\"\n              id=\"affected-data\"\n              name=\"affectedData\"\n              value={formData.affectedData}\n              onChange={handleInputChange}\n              rows={3}\n              placeholder=\"Describe what type of personal data was affected (e.g., names, phone numbers, addresses, financial information)\"\n              error={formErrors.affectedData}\n            />\n\n            {/* Description */}\n            <TextArea\n              label=\"Detailed Description *\"\n              id=\"breach-description\"\n              name=\"description\"\n              value={formData.description}\n              onChange={handleInputChange}\n              rows={6}\n              placeholder=\"Please provide a detailed description of the incident, including how you discovered it, what happened, and any impact on you...\"\n              error={formErrors.description}\n              helperText=\"Minimum 20 characters required\"\n              required\n            />\n\n            {/* Contact Attempts */}\n            <TextArea\n              label=\"Previous Contact Attempts\"\n              id=\"contact-attempts\"\n              name=\"contactAttempts\"\n              value={formData.contactAttempts}\n              onChange={handleInputChange}\n              rows={3}\n              placeholder=\"Describe any attempts you made to contact the organization about this incident\"\n              error={formErrors.contactAttempts}\n            />\n\n            {/* File Attachments */}\n            <div>\n              <label htmlFor=\"breach-attachments\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                Supporting Evidence (Optional)\n              </label>\n              <input\n                id=\"breach-attachments\"\n                type=\"file\"\n                multiple\n                accept=\".pdf,.doc,.docx,.jpg,.jpeg,.png\"\n                onChange={handleFileChange}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n              />\n              <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                Screenshots, emails, documents, or other evidence (Max 5MB per file)\n              </p>\n\n              {/* Show selected files */}\n              {attachments.length > 0 && (\n                <div className=\"mt-3 space-y-2\">\n                  {attachments.map((file, index) => (\n                    <div key={index} className=\"flex items-center justify-between bg-gray-50 dark:bg-gray-700 p-2 rounded\">\n                      <div className=\"flex items-center\">\n                        <i className=\"ri-file-line text-gray-400 mr-2\"></i>\n                        <span className=\"text-sm text-gray-700 dark:text-gray-300\">{file.name}</span>\n                        <span className=\"text-xs text-gray-500 dark:text-gray-400 ml-2\">\n                          ({formatFileSize(file.size)})\n                        </span>\n                      </div>\n                      <button\n                        type=\"button\"\n                        onClick={() => removeAttachment(index)}\n                        className=\"text-red-500 hover:text-red-700\"\n                        aria-label={`Remove ${file.name}`}\n                      >\n                        <i className=\"ri-close-line\"></i>\n                      </button>\n                    </div>\n                  ))}\n                </div>\n              )}\n            </div>\n\n            {/* Submit Buttons */}\n            <div className=\"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700\">\n              <button\n                type=\"button\"\n                onClick={onClose}\n                className=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\n              >\n                Cancel\n              </button>\n              <button\n                type=\"submit\"\n                disabled={isSubmitting}\n                className=\"px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-300\"\n              >\n                {isSubmitting ? (\n                  <>\n                    <i className=\"ri-loader-4-line animate-spin mr-2\"></i>\n                    Submitting...\n                  </>\n                ) : (\n                  <>\n                    <i className=\"ri-shield-keyhole-line mr-2\"></i>\n                    Submit Report\n                  </>\n                )}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default DataBreachModal;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;;;AAPA;;;;;;;AAcA,MAAM,mBAAmB;IACvB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,iBAAiB;IACrB;QAAE,OAAO;QAAO,OAAO;IAA8B;IACrD;QAAE,OAAO;QAAU,OAAO;IAAkC;IAC5D;QAAE,OAAO;QAAQ,OAAO;IAAiC;IACzD;QAAE,OAAO;QAAY,OAAO;IAAsC;CACnE;AAED,MAAM,kBAAkD,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE;;IAC5E,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAC1C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,aAAa;QACb,UAAU;QACV,UAAU;QACV,cAAc;QACd,cAAc;QACd,cAAc;QACd,iBAAiB;IACnB;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IACvE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAEzD,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;QAED,sCAAsC;QACtC,IAAI,UAAU,CAAC,KAAK,EAAE;YACpB,cAAc,CAAA,OAAQ,CAAC;oBACrB,GAAG,IAAI;oBACP,CAAC,KAAK,EAAE;gBACV,CAAC;QACH;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE;YAClB,MAAM,WAAW,MAAM,IAAI,CAAC,EAAE,MAAM,CAAC,KAAK;YAC1C,eAAe,CAAA,OAAQ;uBAAI;uBAAS;iBAAS;QAC/C;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,eAAe,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;IACrD;IAEA,MAAM,eAAe;QACnB,MAAM,SAAkC,CAAC;QAEzC,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,OAAO,KAAK,GAAG;QACjB;QAEA,IAAI,CAAC,SAAS,WAAW,CAAC,IAAI,IAAI;YAChC,OAAO,WAAW,GAAG;QACvB,OAAO,IAAI,SAAS,WAAW,CAAC,IAAI,GAAG,MAAM,GAAG,IAAI;YAClD,OAAO,WAAW,GAAG;QACvB;QAEA,IAAI,CAAC,SAAS,QAAQ,EAAE;YACtB,OAAO,QAAQ,GAAG;QACpB;QAEA,IAAI,CAAC,SAAS,QAAQ,EAAE;YACtB,OAAO,QAAQ,GAAG;QACpB;QAEA,IAAI,CAAC,SAAS,YAAY,EAAE;YAC1B,OAAO,YAAY,GAAG;QACxB;QAEA,IAAI,CAAC,SAAS,YAAY,CAAC,IAAI,IAAI;YACjC,OAAO,YAAY,GAAG;QACxB;QAEA,cAAc;QACd,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;IACxC;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,gBAAgB;QAChB,IAAI;YACF,MAAM,aAAyC;gBAC7C,OAAO,SAAS,KAAK;gBACrB,aAAa,SAAS,WAAW;gBACjC,UAAU,SAAS,QAAQ;gBAC3B,UAAU,SAAS,QAAQ;gBAC3B,eAAe,SAAS,YAAY;gBACpC,uBAAuB,SAAS,YAAY;gBAC5C,qBAAqB,SAAS,YAAY;gBAC1C,kBAAkB,SAAS,eAAe;gBAC1C,aAAa;YACf;YAEA,MAAM,WAAW,MAAM,yJAAA,CAAA,oBAAiB,CAAC,YAAY,CAAC;YAEtD,uBAAuB;YACvB,YACE,CAAC,uEAAuE,EAAE,SAAS,SAAS,IAAI,OAAO,EACvG;YAGF,gEAAgE;YAChE,MAAM,CAAC,qFAAqF,EAAE,SAAS,SAAS,IAAI,OAAO;YAE3H,SAAS;YAET,aAAa;YACb,YAAY;gBACV,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,UAAU;gBACV,cAAc;gBACd,cAAc;gBACd,cAAc;gBACd,iBAAiB;YACnB;YACA,eAAe,EAAE;QAEnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,UAAU;QACZ,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAyD;;;;;;sCAGvE,6LAAC;4BACC,MAAK;4BACL,SAAS;4BACT,cAAW;4BACX,WAAU;sCAEV,cAAA,6LAAC;gCAAE,WAAU;;;;;;;;;;;;;;;;;8BAIjB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;;;;;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA0D;;;;;;8DAGxE,6LAAC;oDAAE,WAAU;8DAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ9D,6LAAC;4BAAK,UAAU;4BAAc,WAAU;;8CAEtC,6LAAC,2IAAA,CAAA,UAAS;oCACR,OAAM;oCACN,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,KAAK;oCACrB,UAAU;oCACV,aAAY;oCACZ,OAAO,WAAW,KAAK;oCACvB,QAAQ;;;;;;8CAGV,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC,wIAAA,CAAA,UAAM;4CACL,OAAM;4CACN,MAAK;4CACL,OAAO,SAAS,QAAQ;4CACxB,UAAU;4CACV,OAAO,WAAW,QAAQ;4CAC1B,QAAQ;;8DAER,6LAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,iBAAiB,GAAG,CAAC,CAAA,yBACpB,6LAAC;wDAAsB,OAAO;kEAAW;uDAA5B;;;;;;;;;;;sDAKjB,6LAAC,wIAAA,CAAA,UAAM;4CACL,OAAM;4CACN,MAAK;4CACL,OAAO,SAAS,QAAQ;4CACxB,UAAU;4CACV,OAAO,WAAW,QAAQ;4CAC1B,QAAQ;;8DAER,6LAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,eAAe,GAAG,CAAC,CAAA,sBAClB,6LAAC;wDAAyB,OAAO,MAAM,KAAK;kEAAG,MAAM,KAAK;uDAA7C,MAAM,KAAK;;;;;;;;;;;;;;;;;8CAK9B,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC,2IAAA,CAAA,UAAS;4CACR,OAAM;4CACN,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,YAAY;4CAC5B,UAAU;4CACV,OAAO,WAAW,YAAY;4CAC9B,QAAQ;;;;;;sDAIV,6LAAC,2IAAA,CAAA,UAAS;4CACR,OAAM;4CACN,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,YAAY;4CAC5B,UAAU;4CACV,aAAY;4CACZ,OAAO,WAAW,YAAY;4CAC9B,QAAQ;;;;;;;;;;;;8CAKZ,6LAAC,0IAAA,CAAA,UAAQ;oCACP,OAAM;oCACN,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,YAAY;oCAC5B,UAAU;oCACV,MAAM;oCACN,aAAY;oCACZ,OAAO,WAAW,YAAY;;;;;;8CAIhC,6LAAC,0IAAA,CAAA,UAAQ;oCACP,OAAM;oCACN,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,WAAW;oCAC3B,UAAU;oCACV,MAAM;oCACN,aAAY;oCACZ,OAAO,WAAW,WAAW;oCAC7B,YAAW;oCACX,QAAQ;;;;;;8CAIV,6LAAC,0IAAA,CAAA,UAAQ;oCACP,OAAM;oCACN,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,eAAe;oCAC/B,UAAU;oCACV,MAAM;oCACN,aAAY;oCACZ,OAAO,WAAW,eAAe;;;;;;8CAInC,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAqB,WAAU;sDAAkE;;;;;;sDAGhH,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,QAAQ;4CACR,QAAO;4CACP,UAAU;4CACV,WAAU;;;;;;sDAEZ,6LAAC;4CAAE,WAAU;sDAAgD;;;;;;wCAK5D,YAAY,MAAM,GAAG,mBACpB,6LAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC;oDAAgB,WAAU;;sEACzB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;;;;;;8EACb,6LAAC;oEAAK,WAAU;8EAA4C,KAAK,IAAI;;;;;;8EACrE,6LAAC;oEAAK,WAAU;;wEAAgD;wEAC5D,eAAe,KAAK,IAAI;wEAAE;;;;;;;;;;;;;sEAGhC,6LAAC;4DACC,MAAK;4DACL,SAAS,IAAM,iBAAiB;4DAChC,WAAU;4DACV,cAAY,CAAC,OAAO,EAAE,KAAK,IAAI,EAAE;sEAEjC,cAAA,6LAAC;gEAAE,WAAU;;;;;;;;;;;;mDAdP;;;;;;;;;;;;;;;;8CAuBlB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CACC,MAAK;4CACL,UAAU;4CACV,WAAU;sDAET,6BACC;;kEACE,6LAAC;wDAAE,WAAU;;;;;;oDAAyC;;6EAIxD;;kEACE,6LAAC;wDAAE,WAAU;;;;;;oDAAkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnE;GAtWM;;QAC+B,mIAAA,CAAA,WAAQ;;;KADvC;uCAwWS", "debugId": null}}, {"offset": {"line": 2862, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/app/customer/data-protection/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport CustomerLayout from '@/components/customer/CustomerLayout';\nimport Loader from '@/components/Loader';\nimport { useAuth } from '@/contexts/AuthContext';\nimport ConsumerAffairsModal from '@/components/customer/ConsumerAffairsModal';\nimport DataBreachModal from '@/components/customer/DataBreachModal';\nimport ComplaintStatusBar, { COMPLAINT_STAGES, getStageIndexFromStatus } from '@/components/customer/ComplaintStatusBar';\nimport { consumerAffairsService, ConsumerAffairsComplaint } from '@/services/consumer-affairs';\nimport { dataBreachService, DataBreachReport } from '@/services/data-breach';\n\ninterface CombinedComplaint {\n  id: string;\n  title: string;\n  description: string;\n  category: string;\n  type: 'consumer_affairs' | 'data_breach';\n  priority: 'low' | 'medium' | 'high' | 'urgent';\n  status: 'submitted' | 'under_review' | 'investigating' | 'resolved' | 'closed';\n  submittedAt: string;\n  updatedAt: string;\n  assignedTo?: string;\n  resolution?: string;\n  number?: string;\n}\n\n// Sample data - replace with actual API calls\nconst sampleComplaints: CombinedComplaint[] = [\n  {\n    id: 'COMP-2024-001',\n    title: 'Unfair Billing Practices by Service Provider',\n    description: 'I have been charged excessive fees for services that were not properly delivered.',\n    category: 'Billing & Charges',\n    type: 'consumer_affairs',\n    priority: 'high',\n    status: 'investigating',\n    submittedAt: '2024-01-15T10:30:00Z',\n    updatedAt: '2024-01-18T14:20:00Z',\n    assignedTo: 'John Mwale - Consumer Affairs Officer'\n  },\n  {\n    id: 'BREACH-2024-001',\n    title: 'Unauthorized Access to Personal Data',\n    description: 'My personal information was accessed without my consent by a telecommunications company.',\n    category: 'Data Privacy',\n    type: 'data_breach',\n    priority: 'urgent',\n    status: 'under_review',\n    submittedAt: '2024-01-20T14:15:00Z',\n    updatedAt: '2024-01-21T09:30:00Z',\n    assignedTo: 'Mary Banda - Data Protection Officer'\n  }\n];\n\nconst DataProtectionPage = () => {\n  const { isAuthenticated, loading: authLoading } = useAuth();\n  const router = useRouter();\n\n  const [complaints, setComplaints] = useState<CombinedComplaint[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [activeTab, setActiveTab] = useState<'overview' | 'track'>('overview');\n  const [showConsumerAffairsModal, setShowConsumerAffairsModal] = useState(false);\n  const [showDataBreachModal, setShowDataBreachModal] = useState(false);\n\n  // Redirect to customer login if not authenticated\n  useEffect(() => {\n    if (!authLoading && !isAuthenticated) {\n      router.push('/customer/auth/login');\n    }\n  }, [isAuthenticated, authLoading, router]);\n\n  // Fetch data function\n  const fetchData = async () => {\n    if (!isAuthenticated) {\n      console.log('❌ User not authenticated, skipping data fetch');\n      return;\n    }\n\n    console.log('✅ User authenticated, fetching data...');\n\n    try {\n      setIsLoading(true);\n      setError('');\n\n      // Fetch both consumer affairs complaints and data breach reports\n      const [consumerAffairsResponse, dataBreachResponse] = await Promise.all([\n        consumerAffairsService.getComplaints({ limit: 100 }),\n        dataBreachService.getReports({ limit: 100 })\n      ]);\n\n        console.log('🔍 Consumer Affairs Response:', consumerAffairsResponse);\n        console.log('🔍 Consumer Affairs Response.data type:', typeof consumerAffairsResponse.data);\n        console.log('🔍 Consumer Affairs Response.data:', consumerAffairsResponse.data);\n        console.log('🔍 Data Breach Response:', dataBreachResponse);\n        console.log('🔍 Data Breach Response.data type:', typeof dataBreachResponse.data);\n        console.log('🔍 Data Breach Response.data:', dataBreachResponse.data);\n\n        // Ensure data is an array (services return data directly)\n        const consumerAffairsData = Array.isArray(consumerAffairsResponse.data)\n          ? consumerAffairsResponse.data\n          : [];\n        const dataBreachData = Array.isArray(dataBreachResponse.data)\n          ? dataBreachResponse.data\n          : [];\n\n        console.log('🔍 Consumer Affairs Data Array:', consumerAffairsData);\n        console.log('🔍 Data Breach Data Array:', dataBreachData);\n\n        // Combine and transform the data\n        const combinedComplaints: CombinedComplaint[] = [\n          ...consumerAffairsData.map((complaint: ConsumerAffairsComplaint) => ({\n            id: complaint.complaint_id,\n            title: complaint.title,\n            description: complaint.description,\n            category: complaint.category,\n            type: 'consumer_affairs' as const,\n            priority: complaint.priority,\n            status: complaint.status,\n            submittedAt: complaint.created_at,\n            updatedAt: complaint.updated_at,\n            assignedTo: complaint.assignee?.first_name && complaint.assignee?.last_name\n              ? `${complaint.assignee.first_name} ${complaint.assignee.last_name}`\n              : undefined,\n            resolution: complaint.resolution,\n            number: complaint.complaint_number\n          })),\n          ...dataBreachData.map((report: DataBreachReport) => ({\n            id: report.report_id,\n            title: report.title,\n            description: report.description,\n            category: report.category,\n            type: 'data_breach' as const,\n            priority: report.priority,\n            status: report.status,\n            submittedAt: report.created_at,\n            updatedAt: report.updated_at,\n            assignedTo: report.assignee?.first_name && report.assignee?.last_name\n              ? `${report.assignee.first_name} ${report.assignee.last_name}`\n              : undefined,\n            resolution: report.resolution,\n            number: report.report_number\n          }))\n        ];\n\n        // Sort by creation date (newest first)\n        combinedComplaints.sort((a, b) => new Date(b.submittedAt).getTime() - new Date(a.submittedAt).getTime());\n\n        setComplaints(combinedComplaints);\n\n      } catch (err: any) {\n        console.error('Error fetching complaints:', err);\n        console.error('Error details:', {\n          message: err.message,\n          response: err.response?.data,\n          status: err.response?.status\n        });\n\n        if (err.response?.status === 401) {\n          setError('Authentication required. Please log in again.');\n        } else if (err.response?.status === 404) {\n          setError('API endpoints not found. Please check if the backend is running.');\n        } else {\n          setError(`Failed to load complaints: ${err.message || 'Unknown error'}`);\n        }\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n  // Fetch data on mount and when authentication changes\n  useEffect(() => {\n    fetchData();\n  }, [isAuthenticated]);\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'submitted': return 'bg-blue-100 text-blue-800';\n      case 'under_review': return 'bg-yellow-100 text-yellow-800';\n      case 'investigating': return 'bg-orange-100 text-orange-800';\n      case 'resolved': return 'bg-green-100 text-green-800';\n      case 'closed': return 'bg-gray-100 text-gray-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority) {\n      case 'low': return 'bg-gray-100 text-gray-800';\n      case 'medium': return 'bg-blue-100 text-blue-800';\n      case 'high': return 'bg-orange-100 text-orange-800';\n      case 'urgent': return 'bg-red-100 text-red-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  const consumerAffairsComplaints = complaints.filter(c => c.type === 'consumer_affairs');\n  const dataBreachComplaints = complaints.filter(c => c.type === 'data_breach');\n\n  if (authLoading || isLoading) {\n    return (\n      <CustomerLayout>\n        <div className=\"flex items-center justify-center min-h-96\">\n          <Loader message=\"Loading Data Protection...\" />\n        </div>\n      </CustomerLayout>\n    );\n  }\n\n  if (error) {\n    return (\n      <CustomerLayout>\n        <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6\">\n          <p>{error}</p>\n          <button\n            type=\"button\"\n            onClick={() => window.location.reload()}\n            className=\"mt-2 text-sm underline hover:no-underline\"\n          >\n            Try again\n          </button>\n        </div>\n      </CustomerLayout>\n    );\n  }\n\n  return (\n    <CustomerLayout>\n      <div className=\"max-w-7xl mx-auto\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <h1 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2\">\n            Data Protection\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            Submit and track consumer affairs complaints and data breach reports\n          </p>\n        </div>\n\n        {/* Tabs */}\n        <div className=\"border-b border-gray-200 dark:border-gray-700 mb-6\">\n          <nav className=\"-mb-px flex space-x-8\">\n            {[\n              { key: 'overview', label: 'Overview', icon: 'ri-dashboard-line' },\n              { key: 'track', label: 'Track Complaints', icon: 'ri-search-eye-line', count: complaints.length }\n            ].map((tab) => (\n              <button\n                type=\"button\"\n                key={tab.key}\n                onClick={() => setActiveTab(tab.key as 'overview' | 'track')}\n                className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center ${\n                  activeTab === tab.key\n                    ? 'border-primary text-primary'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\n                }`}\n              >\n                <i className={`${tab.icon} mr-2`}></i>\n                {tab.label}\n                {tab.count !== undefined && (\n                  <span className=\"ml-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 py-0.5 px-2 rounded-full text-xs\">\n                    {tab.count}\n                  </span>\n                )}\n              </button>\n            ))}\n          </nav>\n        </div>\n\n        {/* Overview Tab */}\n        {activeTab === 'overview' && (\n          <div className=\"space-y-8\">\n            {/* Statistics Cards */}\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n              <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <i className=\"ri-shield-user-line text-2xl text-blue-600\"></i>\n                  </div>\n                  <div className=\"ml-4\">\n                    <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Consumer Affairs</p>\n                    <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">{consumerAffairsComplaints.length}</p>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <i className=\"ri-shield-keyhole-line text-2xl text-red-600\"></i>\n                  </div>\n                  <div className=\"ml-4\">\n                    <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Data Breaches</p>\n                    <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">{dataBreachComplaints.length}</p>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <i className=\"ri-file-list-3-line text-2xl text-green-600\"></i>\n                  </div>\n                  <div className=\"ml-4\">\n                    <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Total Complaints</p>\n                    <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">{complaints.length}</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Action Cards */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {/* Consumer Affairs Card */}\n              <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n                <div className=\"flex items-center mb-4\">\n                  <div className=\"flex-shrink-0\">\n                    <i className=\"ri-shield-user-line text-3xl text-blue-600\"></i>\n                  </div>\n                  <div className=\"ml-4\">\n                    <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">Consumer Affairs</h3>\n                    <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n                      Report issues with telecommunications services, billing, or customer service\n                    </p>\n                  </div>\n                </div>\n                <div className=\"space-y-3\">\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    <p>• Billing disputes and overcharges</p>\n                    <p>• Service quality issues</p>\n                    <p>• Network connectivity problems</p>\n                    <p>• Customer service complaints</p>\n                  </div>\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowConsumerAffairsModal(true)}\n                    className=\"w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-300\"\n                  >\n                    <i className=\"ri-file-add-line mr-2\"></i>\n                    Lodge Consumer Affairs Complaint\n                  </button>\n                </div>\n              </div>\n\n              {/* Data Breach Card */}\n              <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n                <div className=\"flex items-center mb-4\">\n                  <div className=\"flex-shrink-0\">\n                    <i className=\"ri-shield-keyhole-line text-3xl text-red-600\"></i>\n                  </div>\n                  <div className=\"ml-4\">\n                    <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">Data Breach Report</h3>\n                    <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n                      Report unauthorized access, misuse, or breach of your personal data\n                    </p>\n                  </div>\n                </div>\n                <div className=\"space-y-3\">\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    <p>• Unauthorized data access</p>\n                    <p>• Data misuse or sharing</p>\n                    <p>• Privacy violations</p>\n                    <p>• Identity theft concerns</p>\n                  </div>\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowDataBreachModal(true)}\n                    className=\"w-full bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors duration-300\"\n                  >\n                    <i className=\"ri-shield-keyhole-line mr-2\"></i>\n                    Report Data Breach\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Track Complaints Tab */}\n        {activeTab === 'track' && (\n          <div>\n            {complaints.length === 0 ? (\n              <div className=\"text-center py-12\">\n                <i className=\"ri-file-search-line text-4xl text-gray-400 mb-4\"></i>\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\">No complaints found</h3>\n                <p className=\"text-gray-500 dark:text-gray-400 mb-4\">\n                  You haven&apos;t submitted any complaints yet.\n                </p>\n                <button\n                  type=\"button\"\n                  onClick={() => setActiveTab('overview')}\n                  className=\"px-4 py-2 bg-primary text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors duration-300\"\n                >\n                  Submit Your First Complaint\n                </button>\n              </div>\n            ) : (\n              <div className=\"space-y-6\">\n                {complaints.map((complaint) => (\n                  <div key={complaint.id} className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n                    <div className=\"flex justify-between items-start mb-4\">\n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center mb-2\">\n                          <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mr-3\">\n                            {complaint.title}\n                          </h3>\n                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(complaint.status)}`}>\n                            {complaint.status.replace('_', ' ').toUpperCase()}\n                          </span>\n                          <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(complaint.priority)}`}>\n                            {complaint.priority.toUpperCase()}\n                          </span>\n                          <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${\n                            complaint.type === 'consumer_affairs' ? 'bg-blue-100 text-blue-800' : 'bg-red-100 text-red-800'\n                          }`}>\n                            {complaint.type === 'consumer_affairs' ? 'CONSUMER AFFAIRS' : 'DATA BREACH'}\n                          </span>\n                        </div>\n                        <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-2\">\n                          ID: {complaint.id} | Category: {complaint.category}\n                        </p>\n                        <p className=\"text-sm text-gray-700 dark:text-gray-300 mb-3\">\n                          {complaint.description.length > 150 \n                            ? `${complaint.description.substring(0, 150)}...` \n                            : complaint.description}\n                        </p>\n                        <div className=\"flex items-center text-xs text-gray-500 dark:text-gray-400\">\n                          <span>Submitted: {formatDate(complaint.submittedAt)}</span>\n                          <span className=\"mx-2\">•</span>\n                          <span>Updated: {formatDate(complaint.updatedAt)}</span>\n                          {complaint.assignedTo && (\n                            <>\n                              <span className=\"mx-2\">•</span>\n                              <span>Assigned to: {complaint.assignedTo}</span>\n                            </>\n                          )}\n                        </div>\n                      </div>\n                      <button\n                        type=\"button\"\n                        className=\"ml-4 px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600\"\n                      >\n                        View Details\n                      </button>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Modals */}\n        {showConsumerAffairsModal && (\n          <ConsumerAffairsModal\n            onClose={() => setShowConsumerAffairsModal(false)}\n            onSubmit={(data) => {\n              console.log('Consumer Affairs complaint submitted:', data);\n              setShowConsumerAffairsModal(false);\n              // Refresh complaints list without full page reload\n              fetchData();\n            }}\n          />\n        )}\n\n        {showDataBreachModal && (\n          <DataBreachModal\n            onClose={() => setShowDataBreachModal(false)}\n            onSubmit={(data) => {\n              console.log('Data breach report submitted:', data);\n              setShowDataBreachModal(false);\n              // Refresh complaints list without full page reload\n              fetchData();\n            }}\n          />\n        )}\n      </div>\n    </CustomerLayout>\n  );\n};\n\nexport default DataProtectionPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AACA;AAAA;;;AAXA;;;;;;;;;;AA4BA,8CAA8C;AAC9C,MAAM,mBAAwC;IAC5C;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;QACN,UAAU;QACV,QAAQ;QACR,aAAa;QACb,WAAW;QACX,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;QACN,UAAU;QACV,QAAQ;QACR,aAAa;QACb,WAAW;QACX,YAAY;IACd;CACD;AAED,MAAM,qBAAqB;;IACzB,MAAM,EAAE,eAAe,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACxD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IACpE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IACjE,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,kDAAkD;IAClD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,CAAC,eAAe,CAAC,iBAAiB;gBACpC,OAAO,IAAI,CAAC;YACd;QACF;uCAAG;QAAC;QAAiB;QAAa;KAAO;IAEzC,sBAAsB;IACtB,MAAM,YAAY;QAChB,IAAI,CAAC,iBAAiB;YACpB,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,QAAQ,GAAG,CAAC;QAEZ,IAAI;YACF,aAAa;YACb,SAAS;YAET,iEAAiE;YACjE,MAAM,CAAC,yBAAyB,mBAAmB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACtE,mKAAA,CAAA,yBAAsB,CAAC,aAAa,CAAC;oBAAE,OAAO;gBAAI;gBAClD,yJAAA,CAAA,oBAAiB,CAAC,UAAU,CAAC;oBAAE,OAAO;gBAAI;aAC3C;YAEC,QAAQ,GAAG,CAAC,iCAAiC;YAC7C,QAAQ,GAAG,CAAC,2CAA2C,OAAO,wBAAwB,IAAI;YAC1F,QAAQ,GAAG,CAAC,sCAAsC,wBAAwB,IAAI;YAC9E,QAAQ,GAAG,CAAC,4BAA4B;YACxC,QAAQ,GAAG,CAAC,sCAAsC,OAAO,mBAAmB,IAAI;YAChF,QAAQ,GAAG,CAAC,iCAAiC,mBAAmB,IAAI;YAEpE,0DAA0D;YAC1D,MAAM,sBAAsB,MAAM,OAAO,CAAC,wBAAwB,IAAI,IAClE,wBAAwB,IAAI,GAC5B,EAAE;YACN,MAAM,iBAAiB,MAAM,OAAO,CAAC,mBAAmB,IAAI,IACxD,mBAAmB,IAAI,GACvB,EAAE;YAEN,QAAQ,GAAG,CAAC,mCAAmC;YAC/C,QAAQ,GAAG,CAAC,8BAA8B;YAE1C,iCAAiC;YACjC,MAAM,qBAA0C;mBAC3C,oBAAoB,GAAG,CAAC,CAAC,YAAwC,CAAC;wBACnE,IAAI,UAAU,YAAY;wBAC1B,OAAO,UAAU,KAAK;wBACtB,aAAa,UAAU,WAAW;wBAClC,UAAU,UAAU,QAAQ;wBAC5B,MAAM;wBACN,UAAU,UAAU,QAAQ;wBAC5B,QAAQ,UAAU,MAAM;wBACxB,aAAa,UAAU,UAAU;wBACjC,WAAW,UAAU,UAAU;wBAC/B,YAAY,UAAU,QAAQ,EAAE,cAAc,UAAU,QAAQ,EAAE,YAC9D,GAAG,UAAU,QAAQ,CAAC,UAAU,CAAC,CAAC,EAAE,UAAU,QAAQ,CAAC,SAAS,EAAE,GAClE;wBACJ,YAAY,UAAU,UAAU;wBAChC,QAAQ,UAAU,gBAAgB;oBACpC,CAAC;mBACE,eAAe,GAAG,CAAC,CAAC,SAA6B,CAAC;wBACnD,IAAI,OAAO,SAAS;wBACpB,OAAO,OAAO,KAAK;wBACnB,aAAa,OAAO,WAAW;wBAC/B,UAAU,OAAO,QAAQ;wBACzB,MAAM;wBACN,UAAU,OAAO,QAAQ;wBACzB,QAAQ,OAAO,MAAM;wBACrB,aAAa,OAAO,UAAU;wBAC9B,WAAW,OAAO,UAAU;wBAC5B,YAAY,OAAO,QAAQ,EAAE,cAAc,OAAO,QAAQ,EAAE,YACxD,GAAG,OAAO,QAAQ,CAAC,UAAU,CAAC,CAAC,EAAE,OAAO,QAAQ,CAAC,SAAS,EAAE,GAC5D;wBACJ,YAAY,OAAO,UAAU;wBAC7B,QAAQ,OAAO,aAAa;oBAC9B,CAAC;aACF;YAED,uCAAuC;YACvC,mBAAmB,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO;YAErG,cAAc;QAEhB,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,QAAQ,KAAK,CAAC,kBAAkB;gBAC9B,SAAS,IAAI,OAAO;gBACpB,UAAU,IAAI,QAAQ,EAAE;gBACxB,QAAQ,IAAI,QAAQ,EAAE;YACxB;YAEA,IAAI,IAAI,QAAQ,EAAE,WAAW,KAAK;gBAChC,SAAS;YACX,OAAO,IAAI,IAAI,QAAQ,EAAE,WAAW,KAAK;gBACvC,SAAS;YACX,OAAO;gBACL,SAAS,CAAC,2BAA2B,EAAE,IAAI,OAAO,IAAI,iBAAiB;YACzE;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEF,sDAAsD;IACtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR;QACF;uCAAG;QAAC;KAAgB;IAEpB,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAiB,OAAO;YAC7B,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,4BAA4B,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;IACpE,MAAM,uBAAuB,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;IAE/D,IAAI,eAAe,WAAW;QAC5B,qBACE,6LAAC,mJAAA,CAAA,UAAc;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,+HAAA,CAAA,UAAM;oBAAC,SAAQ;;;;;;;;;;;;;;;;IAIxB;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC,mJAAA,CAAA,UAAc;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;kCAAG;;;;;;kCACJ,6LAAC;wBACC,MAAK;wBACL,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;wBACrC,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,6LAAC,mJAAA,CAAA,UAAc;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA2D;;;;;;sCAGzE,6LAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;8BAMlD,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ;4BACC;gCAAE,KAAK;gCAAY,OAAO;gCAAY,MAAM;4BAAoB;4BAChE;gCAAE,KAAK;gCAAS,OAAO;gCAAoB,MAAM;gCAAsB,OAAO,WAAW,MAAM;4BAAC;yBACjG,CAAC,GAAG,CAAC,CAAC,oBACL,6LAAC;gCACC,MAAK;gCAEL,SAAS,IAAM,aAAa,IAAI,GAAG;gCACnC,WAAW,CAAC,6EAA6E,EACvF,cAAc,IAAI,GAAG,GACjB,gCACA,0HACJ;;kDAEF,6LAAC;wCAAE,WAAW,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC;;;;;;oCAC/B,IAAI,KAAK;oCACT,IAAI,KAAK,KAAK,2BACb,6LAAC;wCAAK,WAAU;kDACb,IAAI,KAAK;;;;;;;+BAZT,IAAI,GAAG;;;;;;;;;;;;;;;gBAqBnB,cAAc,4BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA2D,0BAA0B,MAAM;;;;;;;;;;;;;;;;;;;;;;;8CAK9G,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA2D,qBAAqB,MAAM;;;;;;;;;;;;;;;;;;;;;;;8CAKzG,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA2D,WAAW,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOjG,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAE,WAAU;;;;;;;;;;;8DAEf,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAuD;;;;;;sEACrE,6LAAC;4DAAE,WAAU;sEAA2C;;;;;;;;;;;;;;;;;;sDAK5D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAE;;;;;;sEACH,6LAAC;sEAAE;;;;;;sEACH,6LAAC;sEAAE;;;;;;sEACH,6LAAC;sEAAE;;;;;;;;;;;;8DAEL,6LAAC;oDACC,MAAK;oDACL,SAAS,IAAM,4BAA4B;oDAC3C,WAAU;;sEAEV,6LAAC;4DAAE,WAAU;;;;;;wDAA4B;;;;;;;;;;;;;;;;;;;8CAO/C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAE,WAAU;;;;;;;;;;;8DAEf,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAuD;;;;;;sEACrE,6LAAC;4DAAE,WAAU;sEAA2C;;;;;;;;;;;;;;;;;;sDAK5D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAE;;;;;;sEACH,6LAAC;sEAAE;;;;;;sEACH,6LAAC;sEAAE;;;;;;sEACH,6LAAC;sEAAE;;;;;;;;;;;;8DAEL,6LAAC;oDACC,MAAK;oDACL,SAAS,IAAM,uBAAuB;oDACtC,WAAU;;sEAEV,6LAAC;4DAAE,WAAU;;;;;;wDAAkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAU1D,cAAc,yBACb,6LAAC;8BACE,WAAW,MAAM,KAAK,kBACrB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;;;;;0CACb,6LAAC;gCAAG,WAAU;0CAA4D;;;;;;0CAC1E,6LAAC;gCAAE,WAAU;0CAAwC;;;;;;0CAGrD,6LAAC;gCACC,MAAK;gCACL,SAAS,IAAM,aAAa;gCAC5B,WAAU;0CACX;;;;;;;;;;;6CAKH,6LAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,0BACf,6LAAC;gCAAuB,WAAU;0CAChC,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEACX,UAAU,KAAK;;;;;;sEAElB,6LAAC;4DAAK,WAAW,CAAC,2CAA2C,EAAE,eAAe,UAAU,MAAM,GAAG;sEAC9F,UAAU,MAAM,CAAC,OAAO,CAAC,KAAK,KAAK,WAAW;;;;;;sEAEjD,6LAAC;4DAAK,WAAW,CAAC,gDAAgD,EAAE,iBAAiB,UAAU,QAAQ,GAAG;sEACvG,UAAU,QAAQ,CAAC,WAAW;;;;;;sEAEjC,6LAAC;4DAAK,WAAW,CAAC,gDAAgD,EAChE,UAAU,IAAI,KAAK,qBAAqB,8BAA8B,2BACtE;sEACC,UAAU,IAAI,KAAK,qBAAqB,qBAAqB;;;;;;;;;;;;8DAGlE,6LAAC;oDAAE,WAAU;;wDAAgD;wDACtD,UAAU,EAAE;wDAAC;wDAAc,UAAU,QAAQ;;;;;;;8DAEpD,6LAAC;oDAAE,WAAU;8DACV,UAAU,WAAW,CAAC,MAAM,GAAG,MAC5B,GAAG,UAAU,WAAW,CAAC,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC,GAC/C,UAAU,WAAW;;;;;;8DAE3B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;gEAAK;gEAAY,WAAW,UAAU,WAAW;;;;;;;sEAClD,6LAAC;4DAAK,WAAU;sEAAO;;;;;;sEACvB,6LAAC;;gEAAK;gEAAU,WAAW,UAAU,SAAS;;;;;;;wDAC7C,UAAU,UAAU,kBACnB;;8EACE,6LAAC;oEAAK,WAAU;8EAAO;;;;;;8EACvB,6LAAC;;wEAAK;wEAAc,UAAU,UAAU;;;;;;;;;;;;;;;;;;;;;sDAKhD,6LAAC;4CACC,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;+BA1CK,UAAU,EAAE;;;;;;;;;;;;;;;gBAsD/B,0CACC,6LAAC,yJAAA,CAAA,UAAoB;oBACnB,SAAS,IAAM,4BAA4B;oBAC3C,UAAU,CAAC;wBACT,QAAQ,GAAG,CAAC,yCAAyC;wBACrD,4BAA4B;wBAC5B,mDAAmD;wBACnD;oBACF;;;;;;gBAIH,qCACC,6LAAC,oJAAA,CAAA,UAAe;oBACd,SAAS,IAAM,uBAAuB;oBACtC,UAAU,CAAC;wBACT,QAAQ,GAAG,CAAC,iCAAiC;wBAC7C,uBAAuB;wBACvB,mDAAmD;wBACnD;oBACF;;;;;;;;;;;;;;;;;AAMZ;GAlbM;;QAC8C,kIAAA,CAAA,UAAO;QAC1C,qIAAA,CAAA,YAAS;;;KAFpB;uCAobS", "debugId": null}}]}