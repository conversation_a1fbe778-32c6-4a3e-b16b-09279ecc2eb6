"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateStakeholderDto = void 0;
const class_validator_1 = require("class-validator");
const stakeholders_entity_1 = require("../../entities/stakeholders.entity");
class CreateStakeholderDto {
    applicant_id;
    first_name;
    last_name;
    middle_name;
    contact_id;
    nationality;
    position;
    profile;
    cv_document_id;
}
exports.CreateStakeholderDto = CreateStakeholderDto;
__decorate([
    (0, class_validator_1.IsUUID)('4', { message: 'Applicant ID not valid!' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Applicant ID is required' }),
    __metadata("design:type", String)
], CreateStakeholderDto.prototype, "applicant_id", void 0);
__decorate([
    (0, class_validator_1.IsString)({ message: 'First name contains invalid characters!' }),
    (0, class_validator_1.Max)(100, { message: 'First name must not exceed 100 characters' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'First name is required' }),
    __metadata("design:type", String)
], CreateStakeholderDto.prototype, "first_name", void 0);
__decorate([
    (0, class_validator_1.IsString)({ message: 'Last name contains invalid characters!' }),
    (0, class_validator_1.Max)(100, { message: 'Last name must not exceed 100 characters' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Last name is required' }),
    __metadata("design:type", String)
], CreateStakeholderDto.prototype, "last_name", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Middle name contains invalid characters!' }),
    (0, class_validator_1.Length)(1, 100),
    __metadata("design:type", String)
], CreateStakeholderDto.prototype, "middle_name", void 0);
__decorate([
    (0, class_validator_1.IsUUID)('4', { message: 'Contact ID is not a valid UUID!' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Contact ID is required' }),
    __metadata("design:type", String)
], CreateStakeholderDto.prototype, "contact_id", void 0);
__decorate([
    (0, class_validator_1.IsString)({ message: 'Nationality contains invalid characters!' }),
    (0, class_validator_1.Max)(50, { message: 'Nationality must not exceed 50 characters' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Nationality is required' }),
    __metadata("design:type", String)
], CreateStakeholderDto.prototype, "nationality", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(stakeholders_entity_1.StakeholderPosition, { message: 'Invalid stakeholder position. Must be one of CEO, Shareholder, Auditor, or Lawyer.' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Stakeholder position is required' }),
    __metadata("design:type", String)
], CreateStakeholderDto.prototype, "position", void 0);
__decorate([
    (0, class_validator_1.IsString)({ message: 'Profile contains invalid characters!' }),
    (0, class_validator_1.Max)(300, { message: 'Profile must not exceed 300 characters' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Profile is required' }),
    __metadata("design:type", String)
], CreateStakeholderDto.prototype, "profile", void 0);
__decorate([
    (0, class_validator_1.IsUUID)('4', { message: 'CV Document ID not valid!' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'CV Document ID is required' }),
    __metadata("design:type", String)
], CreateStakeholderDto.prototype, "cv_document_id", void 0);
//# sourceMappingURL=create-stakeholder.dto.js.map