(()=>{var e={};e.id=6636,e.ids=[6636],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3601:(e,r,s)=>{Promise.resolve().then(s.bind(s,33499))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13329:(e,r,s)=>{Promise.resolve().then(s.bind(s,75758))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20377:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var a=s(65239),t=s(48088),l=s(88170),i=s.n(l),d=s(30893),n={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);s.d(r,n);let c={children:["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,75758)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\profile\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,34521)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\profile\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\profile\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33499:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>u});var a=s(60687),t=s(43210),l=s(63213),i=s(49048);function d({user:e,onUpdate:r}){let[s,l]=(0,t.useState)({email:e.email,first_name:e.first_name,last_name:e.last_name,middle_name:e.middle_name||"",phone:e.phone}),[d,n]=(0,t.useState)(!1),[c,o]=(0,t.useState)(null),[x,m]=(0,t.useState)(null),g=e=>{let{name:r,value:s}=e.target;l(e=>({...e,[r]:s}))},u=async e=>{e.preventDefault(),n(!0),o(null),m(null);try{let e=await i.D.updateProfile({email:s.email,first_name:s.first_name,last_name:s.last_name,middle_name:s.middle_name||void 0,phone:s.phone});r(e),m("Profile updated successfully!")}catch(e){o(e.response?.data?.message||"Failed to update profile")}finally{n(!1)}};return(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"h-10 w-10 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center",children:(0,a.jsx)("i",{className:"ri-user-settings-line text-red-600 dark:text-red-400 text-xl"})})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-gray-100",children:"Personal Information"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Update your personal details and contact information."})]})]})}),c&&(0,a.jsx)("div",{className:"mb-6 rounded-md bg-red-50 dark:bg-red-900/20 p-4 border-l-4 border-red-400 dark:border-red-600",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-error-warning-line text-red-400 dark:text-red-500 text-lg"})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-red-800 dark:text-red-300",children:c})})]})}),x&&(0,a.jsx)("div",{className:"mb-6 rounded-md bg-green-50 dark:bg-green-900/20 p-4 border-l-4 border-green-400 dark:border-green-600",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-check-line text-green-400 dark:text-green-500 text-lg"})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-green-800 dark:text-green-300",children:x})})]})}),(0,a.jsxs)("form",{onSubmit:u,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-800 rounded-lg p-4",children:[(0,a.jsxs)("h4",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 flex items-center",children:[(0,a.jsx)("i",{className:"ri-user-line text-gray-500 dark:text-gray-400 mr-2"}),"Personal Details"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"first_name",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"First Name *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:"text",name:"first_name",id:"first_name",required:!0,value:s.first_name,onChange:g,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500",placeholder:"Enter your first name"}),(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)("i",{className:"ri-user-line text-gray-400 dark:text-gray-500"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"last_name",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Last Name *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:"text",name:"last_name",id:"last_name",required:!0,value:s.last_name,onChange:g,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500",placeholder:"Enter your last name"}),(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)("i",{className:"ri-user-line text-gray-400 dark:text-gray-500"})})]})]})]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsx)("label",{htmlFor:"middle_name",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Middle Name"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:"text",name:"middle_name",id:"middle_name",value:s.middle_name,onChange:g,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500",placeholder:"Enter your middle name (optional)"}),(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)("i",{className:"ri-user-line text-gray-400 dark:text-gray-500"})})]})]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-800 rounded-lg p-4",children:[(0,a.jsxs)("h4",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 flex items-center",children:[(0,a.jsx)("i",{className:"ri-contacts-line text-gray-500 dark:text-gray-400 mr-2"}),"Contact Information"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Email Address *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:"email",name:"email",id:"email",required:!0,value:s.email,onChange:g,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500",placeholder:"Enter your email address"}),(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)("i",{className:"ri-mail-line text-gray-400 dark:text-gray-500"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Phone Number *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:"tel",name:"phone",id:"phone",required:!0,value:s.phone,onChange:g,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500",placeholder:"Enter your phone number"}),(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)("i",{className:"ri-phone-line text-gray-400 dark:text-gray-500"})})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4",children:[(0,a.jsxs)("h4",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 flex items-center",children:[(0,a.jsx)("i",{className:"ri-information-line text-gray-500 dark:text-gray-400 mr-2"}),"Account Information"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 border border-blue-200 dark:border-blue-800",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-fingerprint-line text-blue-500 dark:text-blue-400 text-xl"})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"User ID"}),(0,a.jsx)("p",{className:"text-sm text-gray-900 dark:text-gray-100 font-mono",children:e.user_id})]})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 border border-blue-200 dark:border-blue-800",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-shield-user-line text-blue-500 dark:text-blue-400 text-xl"})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Role"}),(0,a.jsx)("p",{className:"text-sm text-gray-900 dark:text-gray-100",children:e.roles?.length||"No Role"})]})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 border border-blue-200 dark:border-blue-800",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:`${"active"===e.status?"ri-check-line text-green-500 dark:text-green-400":"ri-close-line text-red-500 dark:text-red-400"} text-xl`})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Account Status"}),(0,a.jsx)("p",{className:"text-sm text-gray-900 dark:text-gray-100",children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})]})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 border border-blue-200 dark:border-blue-800",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-calendar-line text-blue-500 dark:text-blue-400 text-xl"})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Member Since"}),(0,a.jsx)("p",{className:"text-sm text-gray-900 dark:text-gray-100",children:new Date(e.created_at).toLocaleDateString()})]})]})})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700",children:[(0,a.jsxs)("button",{type:"button",onClick:()=>{l({email:e.email,first_name:e.first_name,last_name:e.last_name,middle_name:e.middle_name||"",phone:e.phone}),o(null),m(null)},className:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-colors duration-200",children:[(0,a.jsx)("i",{className:"ri-refresh-line mr-2"}),"Reset"]}),(0,a.jsx)("button",{type:"submit",disabled:d,className:"inline-flex items-center px-6 py-2 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200",children:d?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Updating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"ri-save-line mr-2"}),"Update Profile"]})})]})]})]})}function n({userId:e}){let[r,s]=(0,t.useState)({current_password:"",new_password:"",confirm_password:""}),[l,d]=(0,t.useState)(!1),[n,c]=(0,t.useState)(null),[o,x]=(0,t.useState)(null),[m,g]=(0,t.useState)({current:!1,new:!1,confirm:!1}),u=e=>{let{name:r,value:a}=e.target;s(e=>({...e,[r]:a}))},h=e=>{g(r=>({...r,[e]:!r[e]}))},p=(e=>{let r=e.length>=8,s=/[A-Z]/.test(e),a=/[a-z]/.test(e),t=/[\d\W]/.test(e);return{minLength:r,hasUpperCase:s,hasLowerCase:a,hasNumberOrSpecial:t,isValid:r&&s&&a&&t}})(r.new_password),f=async e=>{if(e.preventDefault(),d(!0),c(null),x(null),r.new_password!==r.confirm_password){c("New password and confirmation do not match"),d(!1);return}if(!p.isValid){c("New password does not meet the requirements"),d(!1);return}try{await i.D.changePassword({current_password:r.current_password,new_password:r.new_password,confirm_password:r.confirm_password}),x("Password changed successfully!"),s({current_password:"",new_password:"",confirm_password:""})}catch(e){c(e.response?.data?.message||"Failed to change password")}finally{d(!1)}};return(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"h-10 w-10 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center",children:(0,a.jsx)("i",{className:"ri-shield-keyhole-line text-red-600 dark:text-red-400 text-xl"})})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-gray-100",children:"Change Password"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Update your password to keep your account secure."})]})]})}),n&&(0,a.jsx)("div",{className:"mb-6 rounded-md bg-red-50 dark:bg-red-900/20 p-4 border-l-4 border-red-400 dark:border-red-600",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-error-warning-line text-red-400 dark:text-red-500 text-lg"})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-red-800 dark:text-red-300",children:n})})]})}),o&&(0,a.jsx)("div",{className:"mb-6 rounded-md bg-green-50 dark:bg-green-900/20 p-4 border-l-4 border-green-400 dark:border-green-600",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-check-line text-green-400 dark:text-green-500 text-lg"})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-green-800 dark:text-green-300",children:o})})]})}),(0,a.jsx)("div",{className:"mb-6 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-information-line text-blue-400 dark:text-blue-500 text-lg"})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-blue-800 dark:text-blue-200",children:"Security Requirements"}),(0,a.jsx)("div",{className:"mt-2 text-sm text-blue-700 dark:text-blue-300",children:(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,a.jsx)("li",{children:"Password must be at least 8 characters long"}),(0,a.jsx)("li",{children:"Include at least one uppercase and lowercase letter"}),(0,a.jsx)("li",{children:"Include at least one number or special character"}),(0,a.jsx)("li",{children:"You'll need to enter your current password to confirm changes"})]})})]})]})}),(0,a.jsxs)("form",{onSubmit:f,className:"space-y-6",children:[(0,a.jsx)("div",{className:"bg-gray-50 dark:bg-gray-800 rounded-lg p-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"current_password",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Current Password *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:m.current?"text":"password",name:"current_password",id:"current_password",required:!0,value:r.current_password,onChange:u,className:"block w-full pl-10 pr-10 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500",placeholder:"Enter your current password"}),(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)("i",{className:"ri-lock-line text-gray-400 dark:text-gray-500"})}),(0,a.jsx)("button",{type:"button",onClick:()=>h("current"),className:"absolute inset-y-0 right-0 pr-3 flex items-center hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200",children:(0,a.jsx)("i",{className:`${m.current?"ri-eye-off-line":"ri-eye-line"} text-gray-400 dark:text-gray-500`})})]})]})}),(0,a.jsxs)("div",{className:"m-2",children:[(0,a.jsx)("label",{htmlFor:"new_password",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"New Password *"}),(0,a.jsxs)("div",{className:"mt-1 relative",children:[(0,a.jsx)("input",{type:m.new?"text":"password",name:"new_password",id:"new_password",required:!0,value:r.new_password,onChange:u,className:"block w-full pl-10 pr-10 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500"}),(0,a.jsx)("button",{type:"button",onClick:()=>h("new"),className:"absolute inset-y-0 right-0 pr-3 flex items-center hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200",children:(0,a.jsx)("i",{className:`${m.new?"ri-eye-off-line":"ri-eye-line"} text-gray-400 dark:text-gray-500`})})]}),r.new_password&&(0,a.jsxs)("div",{className:"mt-2 space-y-1",children:[(0,a.jsxs)("div",{className:`flex items-center text-xs ${p.minLength?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400"}`,children:[(0,a.jsx)("i",{className:`${p.minLength?"ri-check-line":"ri-close-line"} mr-1`}),"At least 8 characters"]}),(0,a.jsxs)("div",{className:`flex items-center text-xs ${p.hasUpperCase?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400"}`,children:[(0,a.jsx)("i",{className:`${p.hasUpperCase?"ri-check-line":"ri-close-line"} mr-1`}),"One uppercase letter"]}),(0,a.jsxs)("div",{className:`flex items-center text-xs ${p.hasLowerCase?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400"}`,children:[(0,a.jsx)("i",{className:`${p.hasLowerCase?"ri-check-line":"ri-close-line"} mr-1`}),"One lowercase letter"]}),(0,a.jsxs)("div",{className:`flex items-center text-xs ${p.hasNumberOrSpecial?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400"}`,children:[(0,a.jsx)("i",{className:`${p.hasNumberOrSpecial?"ri-check-line":"ri-close-line"} mr-1`}),"One number or special character"]})]})]}),(0,a.jsxs)("div",{className:"m-2",children:[(0,a.jsx)("label",{htmlFor:"confirm_password",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Confirm New Password *"}),(0,a.jsxs)("div",{className:" relative",children:[(0,a.jsx)("input",{type:m.confirm?"text":"password",name:"confirm_password",id:"confirm_password",required:!0,value:r.confirm_password,onChange:u,className:"block w-full pl-10 pr-10 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500"}),(0,a.jsx)("button",{type:"button",onClick:()=>h("confirm"),className:"absolute inset-y-0 right-0 pr-3 flex items-center hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200",children:(0,a.jsx)("i",{className:`${m.confirm?"ri-eye-off-line":"ri-eye-line"} text-gray-400 dark:text-gray-500`})})]}),r.confirm_password&&(0,a.jsxs)("div",{className:`mt-1 flex items-center text-xs ${r.new_password===r.confirm_password?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400"}`,children:[(0,a.jsx)("i",{className:`${r.new_password===r.confirm_password?"ri-check-line":"ri-close-line"} mr-1`}),"Passwords match"]})]}),(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsx)("button",{type:"submit",disabled:l||!p.isValid||r.new_password!==r.confirm_password,className:"inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 dark:focus:ring-offset-gray-800",children:l?"Changing...":"Change Password"})})]})]})}function c({user:e,onUpdate:r}){let[s,l]=(0,t.useState)(!1),[d,n]=(0,t.useState)(null),[c,o]=(0,t.useState)(null),[x,m]=(0,t.useState)(null),g=(0,t.useRef)(null),u=async()=>{let e=g.current?.files?.[0];if(!e)return void n("Please select a file first.");l(!0),n(null),o(null);try{let s=await i.D.uploadAvatar(e);r(s),o("Profile picture updated successfully!"),m(null),g.current&&(g.current.value="")}catch(e){n(e.response?.data?.message||"Failed to upload profile picture")}finally{l(!1)}},h=async()=>{if(e.profile_image){l(!0),n(null),o(null);try{let e=await i.D.removeAvatar();r(e),o("Profile picture removed successfully!")}catch(e){n(e.response?.data?.message||"Failed to remove profile picture")}finally{l(!1)}}},p=()=>{g.current?.click()};return(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"h-10 w-10 bg-red-100 rounded-lg flex items-center justify-center",children:(0,a.jsx)("i",{className:"ri-image-line text-red-600 text-xl"})})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Profile Picture"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Upload a profile picture to personalize your account."})]})]})}),d&&(0,a.jsx)("div",{className:"mb-6 rounded-md bg-red-50 p-4 border-l-4 border-red-400",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-error-warning-line text-red-400 text-lg"})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-red-800",children:d})})]})}),c&&(0,a.jsx)("div",{className:"mb-6 rounded-md bg-green-50 p-4 border-l-4 border-green-400",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-check-line text-green-400 text-lg"})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-green-800",children:c})})]})}),(0,a.jsx)("div",{className:"bg-gray-50 dark:bg-gray-800 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-start lg:space-x-8 space-y-6 lg:space-y-0",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"relative inline-block",children:[(0,a.jsx)("div",{className:"h-40 w-40 rounded-full overflow-hidden bg-gray-100 dark:bg-gray-700 border-4 border-white dark:border-gray-600 shadow-lg",children:x?(0,a.jsx)("img",{src:x,alt:"Preview",className:"h-full w-full object-cover"}):e.profile_image?(0,a.jsx)("img",{src:e.profile_image,alt:`${e.first_name} ${e.last_name}`,className:"h-full w-full object-cover"}):(0,a.jsx)("div",{className:"h-full w-full flex items-center justify-center bg-gradient-to-br from-red-500 to-red-600",children:(0,a.jsxs)("span",{className:"text-4xl font-bold text-white",children:[e.first_name?.charAt(0),e.last_name?.charAt(0)]})})}),x&&(0,a.jsx)("div",{className:"absolute -top-2 -right-2",children:(0,a.jsx)("div",{className:"bg-blue-500 dark:bg-blue-600 text-white rounded-full p-1",children:(0,a.jsx)("i",{className:"ri-eye-line text-sm"})})})]}),(0,a.jsx)("p",{className:"mt-3 text-sm text-gray-500 dark:text-gray-400",children:x?"Preview":"Current Picture"})]})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("input",{ref:g,type:"file",accept:"image/jpeg,image/png,image/gif,image/webp",onChange:e=>{let r=e.target.files?.[0];if(!r)return;if(!["image/jpeg","image/png","image/gif","image/webp"].includes(r.type))return void n("Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.");if(r.size>0xa00000)return void n("File size too large. Maximum size is 10MB.");let s=new FileReader;s.onload=e=>{m(e.target?.result)},s.readAsDataURL(r),n(null)},className:"hidden"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{onClick:p,className:"border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center hover:border-red-400 dark:hover:border-red-500 transition-colors duration-200 cursor-pointer bg-white dark:bg-gray-800",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"mx-auto h-12 w-12 text-gray-400 dark:text-gray-500",children:(0,a.jsx)("i",{className:"ri-upload-cloud-line text-4xl"})}),(0,a.jsxs)("div",{className:"text-sm text-gray-600 dark:text-gray-300",children:[(0,a.jsx)("span",{className:"font-medium text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300",children:"Click to upload"})," ","or drag and drop"]}),(0,a.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"PNG, JPG, GIF, WebP up to 5MB"})]})}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("button",{type:"button",onClick:p,disabled:s,className:"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 transition-colors duration-200",children:[(0,a.jsx)("i",{className:"ri-folder-open-line mr-2"}),"Browse Files"]}),x&&(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)("button",{type:"button",onClick:u,disabled:s,className:"flex-1 inline-flex justify-center items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 transition-colors duration-200",children:s?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Uploading..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"ri-upload-line mr-2"}),"Upload"]})}),(0,a.jsxs)("button",{type:"button",onClick:()=>{m(null),g.current&&(g.current.value="")},className:"flex-1 inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200",children:[(0,a.jsx)("i",{className:"ri-close-line mr-2"}),"Cancel"]})]}),e.profile_image&&!x&&(0,a.jsxs)("button",{type:"button",onClick:h,disabled:s,className:"w-full inline-flex justify-center items-center px-4 py-2 border border-red-300 dark:border-red-600 shadow-sm text-sm font-medium rounded-lg text-red-700 dark:text-red-400 bg-white dark:bg-gray-800 hover:bg-red-50 dark:hover:bg-red-900/20 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 transition-colors duration-200",children:[(0,a.jsx)("i",{className:"ri-delete-bin-line mr-2"}),s?"Removing...":"Remove Picture"]})]}),(0,a.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800",children:[(0,a.jsxs)("h4",{className:"text-sm font-medium text-blue-800 dark:text-blue-300 mb-2 flex items-center",children:[(0,a.jsx)("i",{className:"ri-information-line mr-2"}),"File Requirements"]}),(0,a.jsxs)("ul",{className:"text-sm text-blue-700 dark:text-blue-300 space-y-1",children:[(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"ri-check-line mr-2 text-blue-500 dark:text-blue-400"}),"Supported formats: JPEG, PNG, GIF, WebP"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"ri-check-line mr-2 text-blue-500 dark:text-blue-400"}),"Maximum file size: 10MB"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"ri-check-line mr-2 text-blue-500 dark:text-blue-400"}),"Recommended size: 400x400 pixels"]})]})]})]})]})]})})]})}var o=s(39659);function x(){let{theme:e,setTheme:r}=(0,o.D)(),s=[{value:"light",label:"Light",description:"Use light theme",icon:"ri-sun-line"},{value:"dark",label:"Dark",description:"Use dark theme",icon:"ri-moon-line"},{value:"system",label:"System",description:"Follow system preference",icon:"ri-computer-line"}];return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Display Preferences"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Customize how the interface appears to you."})]}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg",children:(0,a.jsx)("div",{className:"px-4 py-5 sm:p-6",children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:"Theme"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Choose your preferred color scheme"})]}),(0,a.jsx)("div",{className:"ml-4",children:(0,a.jsx)("select",{value:e,onChange:e=>r(e.target.value),className:"block w-40 pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm rounded-md",children:s.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))})})]})}),(0,a.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-4",children:s.map(s=>(0,a.jsxs)("button",{onClick:()=>r(s.value),className:`relative p-4 border-2 rounded-lg transition-all duration-200 ${e===s.value?"border-red-500 bg-red-50 dark:bg-red-900/20":"border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"}`,children:[(0,a.jsxs)("div",{className:"flex flex-col items-center text-center",children:[(0,a.jsx)("div",{className:`w-8 h-8 rounded-lg flex items-center justify-center mb-2 ${e===s.value?"bg-red-100 dark:bg-red-800 text-red-600 dark:text-red-400":"bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400"}`,children:(0,a.jsx)("i",{className:`${s.icon} text-lg`})}),(0,a.jsx)("h5",{className:`text-sm font-medium ${e===s.value?"text-red-900 dark:text-red-100":"text-gray-900 dark:text-gray-100"}`,children:s.label}),(0,a.jsx)("p",{className:`text-xs mt-1 ${e===s.value?"text-red-700 dark:text-red-300":"text-gray-500 dark:text-gray-400"}`,children:s.description})]}),e===s.value&&(0,a.jsx)("div",{className:"absolute top-2 right-2",children:(0,a.jsx)("div",{className:"w-4 h-4 bg-red-500 rounded-full flex items-center justify-center",children:(0,a.jsx)("i",{className:"ri-check-line text-white text-xs"})})})]},s.value))}),(0,a.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-4",children:[(0,a.jsx)("h5",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-3",children:"Preview"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-md",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-red-500 rounded-full flex items-center justify-center",children:(0,a.jsx)("i",{className:"ri-user-line text-white text-sm"})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:"Sample User"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"<EMAIL>"})]})]}),(0,a.jsx)("button",{className:"px-3 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700 transition-colors",children:"Action"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,a.jsxs)("div",{className:"p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-md",children:[(0,a.jsx)("p",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Card Title"}),(0,a.jsx)("p",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:"123"})]}),(0,a.jsxs)("div",{className:"p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-md",children:[(0,a.jsx)("p",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Another Card"}),(0,a.jsx)("p",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:"456"})]})]})]})]})]})})})]})}var m=s(29045);let g=e=>{let r=e.roles?.map(e=>e.name)||[];return{user_id:e.user_id,email:e.email,first_name:e.first_name,last_name:e.last_name,middle_name:e.middle_name,phone:e.phone,status:e.status,profile_image:e.profile_image,roles:r,isAdmin:r.includes("administrator")||e.isAdmin||!1}};function u(){let{updateUser:e}=(0,l.A)(),[r,s]=(0,t.useState)(null),[i,o]=(0,t.useState)(!0),[u,h]=(0,t.useState)(null),[p,f]=(0,t.useState)("profile"),b=async r=>{s(r),e&&e(g(r))};return i?(0,a.jsx)("div",{className:"flex-1 overflow-y-auto bg-gray-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-16 w-16 border-b-2 border-red-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading profile..."})]})}):u?(0,a.jsx)("div",{className:"flex-1 overflow-y-auto bg-gray-50 p-6",children:(0,a.jsx)("div",{className:"max-w-md mx-auto",children:(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("i",{className:"ri-error-warning-line text-red-400 mr-2"}),(0,a.jsx)("span",{children:u})]})})})}):(0,a.jsxs)("div",{className:"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900",children:[(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",children:(0,a.jsx)("div",{className:"px-4 sm:px-6 lg:px-8",children:(0,a.jsx)("div",{className:"py-6",children:(0,a.jsx)("div",{className:"md:flex md:items-center md:justify-between",children:(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold leading-7 text-gray-900 dark:text-gray-100 sm:text-3xl sm:truncate",children:"Profile Settings"}),(0,a.jsx)("div",{className:"mt-1 flex flex-col sm:flex-row sm:flex-wrap sm:mt-0 sm:space-x-6",children:(0,a.jsxs)("div",{className:"mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400",children:[(0,a.jsx)("i",{className:"ri-user-line mr-1.5 h-5 w-5 text-gray-400"}),"Manage your account settings and preferences"]})})]})})})})}),(0,a.jsxs)("div",{className:"flex-1 px-4 sm:px-6 lg:px-8 py-4",children:[(0,a.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg mb-4",children:[(0,a.jsx)("div",{className:"bg-gradient-to-r from-red-500 to-red-600 px-6 py-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"h-20 w-20 rounded-full border-4 border-white overflow-hidden bg-white",children:r?.profile_image?(0,a.jsx)("img",{className:"h-full w-full object-cover",src:r.profile_image,alt:`${r.first_name} ${r.last_name}`}):(0,a.jsx)("div",{className:"h-full w-full bg-red-600 flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-2xl font-bold text-white",children:(0,m.NQ)(r?.first_name,r?.last_name)})})})}),(0,a.jsxs)("div",{className:"ml-6",children:[(0,a.jsxs)("h2",{className:"text-2xl font-bold text-white",children:[r?.first_name," ",r?.last_name]}),(0,a.jsx)("p",{className:"text-red-100",children:r?.email}),(0,a.jsxs)("div",{className:"mt-2 flex items-center",children:[(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-white bg-opacity-20 text-white",children:(0,a.jsx)("i",{className:"ri-shield-user-line mr-1"})}),(0,a.jsx)("span",{className:"ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:(0,a.jsx)("i",{className:"ri-check-line mr-1"})})]})]})]})}),(0,a.jsx)("div",{className:"bg-gray-50 dark:bg-gray-700 px-6 py-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-calendar-line text-gray-400 text-lg"})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Member Since"}),(0,a.jsx)("p",{className:"text-sm text-gray-900 dark:text-gray-100",children:new Date(r?.created_at||"").toLocaleDateString()})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-time-line text-gray-400 text-lg"})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Last Login"}),(0,a.jsx)("p",{className:"text-sm text-gray-900 dark:text-gray-100",children:r?.last_login?new Date(r.last_login).toLocaleDateString():"Never"})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-phone-line text-gray-400 text-lg"})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Phone"}),(0,a.jsx)("p",{className:"text-sm text-gray-900 dark:text-gray-100",children:r?.phone})]})]})]})})]}),(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg",children:[(0,a.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-700",children:(0,a.jsx)("nav",{className:"-mb-px flex space-x-8 px-6","aria-label":"Tabs",children:[{id:"profile",label:"Profile Information",icon:"ri-user-line"},{id:"security",label:"Security",icon:"ri-shield-line"},{id:"avatar",label:"Profile Picture",icon:"ri-image-line"},{id:"preferences",label:"Display Preferences",icon:"ri-palette-line"}].map(e=>(0,a.jsx)("button",{onClick:()=>f(e.id),className:`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${p===e.id?"border-red-500 text-red-600 dark:text-red-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"}`,"aria-current":p===e.id?"page":void 0,children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("i",{className:`${e.icon} text-lg`}),(0,a.jsx)("span",{children:e.label})]})},e.id))})}),(0,a.jsxs)("div",{className:"p-4 lg:p-6",children:["profile"===p&&r&&(0,a.jsx)("div",{className:"max-w-4xl",children:(0,a.jsx)(d,{user:r,onUpdate:b})}),"security"===p&&r&&(0,a.jsx)("div",{className:"max-w-2xl",children:(0,a.jsx)(n,{userId:r.user_id})}),"avatar"===p&&r&&(0,a.jsx)("div",{className:"max-w-2xl",children:(0,a.jsx)(c,{user:r,onUpdate:b})}),"preferences"===p&&(0,a.jsx)("div",{className:"max-w-4xl",children:(0,a.jsx)(x,{})})]})]})]}),(0,a.jsx)("div",{className:"h-8"})]})]})}},33873:e=>{"use strict";e.exports=require("path")},34521:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\profile\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\profile\\layout.tsx","default")},44034:(e,r,s)=>{Promise.resolve().then(s.bind(s,34521))},47675:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>c});var a=s(60687),t=s(43210),l=s(16189),i=s(63213),d=s(21891),n=s(60417);function c({children:e}){let{isAuthenticated:r,loading:s}=(0,i.A)();(0,l.useRouter)();let[c,o]=(0,t.useState)("overview"),[x,m]=(0,t.useState)(!1);return s?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"})}):r?(0,a.jsxs)("div",{className:"flex h-screen overflow-hidden",children:[(0,a.jsx)("div",{id:"mobileSidebarOverlay",className:`mobile-sidebar-overlay ${x?"show":""}`,onClick:()=>m(!1)}),(0,a.jsx)(n.default,{}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,a.jsx)(d.default,{activeTab:c,onTabChange:o,onMobileMenuToggle:()=>{m(!x)}}),e]})]}):null}},49048:(e,r,s)=>{"use strict";s.d(r,{D:()=>l});var a=s(12234),t=s(51278);let l={async getUsers(e={}){let r=new URLSearchParams;e.page&&r.set("page",e.page.toString()),e.limit&&r.set("limit",e.limit.toString()),e.search&&r.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>r.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>r.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(([e,s])=>{Array.isArray(s)?s.forEach(s=>r.append(`filter.${e}`,s)):r.set(`filter.${e}`,s)});let s=await a.Gf.get(`?${r.toString()}`);return(0,t.zp)(s)},async getUser(e){let r=await a.Gf.get(`/${e}`);return(0,t.zp)(r)},async getUserById(e){return this.getUser(e)},async getProfile(){let e=await a.Gf.get("/profile");return(0,t.zp)(e)},async createUser(e){let r=await a.Gf.post("",e);return(0,t.zp)(r)},async updateUser(e,r){let s=await a.Gf.put(`/${e}`,r);return(0,t.zp)(s)},async updateProfile(e){let r=await a.Gf.put("/profile",e);return(0,t.zp)(r)},async changePassword(e){let r=await a.Gf.put("/profile/password",e);return(0,t.zp)(r)},async uploadAvatar(e){let r=new FormData;r.append("avatar",e);try{let e=await a.Gf.post("/profile/avatar",r,{headers:{"Content-Type":"multipart/form-data"}});return(0,t.zp)(e)}catch(e){throw e}},async removeAvatar(){let e=await a.Gf.delete("/profile/avatar");return(0,t.zp)(e)},async deleteUser(e){await a.Gf.delete(`/${e}`)}}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},75758:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\profile\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\profile\\page.tsx","default")},78538:(e,r,s)=>{Promise.resolve().then(s.bind(s,47675))},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),a=r.X(0,[4447,7498,1658,5814,2335,6606],()=>s(20377));module.exports=a})();