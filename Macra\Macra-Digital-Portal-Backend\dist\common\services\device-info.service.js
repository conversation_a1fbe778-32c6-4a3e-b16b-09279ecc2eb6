"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var DeviceInfoService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeviceInfoService = void 0;
const common_1 = require("@nestjs/common");
const axios_1 = __importDefault(require("axios"));
let DeviceInfoService = DeviceInfoService_1 = class DeviceInfoService {
    logger = new common_1.Logger(DeviceInfoService_1.name);
    async getDeviceInfo(req) {
        try {
            const ipAddress = req.headers['x-forwarded-for'] || req.socket.remoteAddress;
            const userAgent = req.headers['user-agent'];
            const ip = ipAddress ? ipAddress.toString().split(',').pop()?.trim() : '';
            let country = 'Unknown';
            let city = 'Unknown';
            if (ip && ip !== '127.0.0.1' && ip !== 'localhost' && !ip.startsWith('192.168.')) {
                try {
                    const response = await axios_1.default.get(`http://ip-api.com/json/${ip}`, {
                        timeout: 5000,
                    });
                    if (response.data && response.data.status === 'success') {
                        country = response.data.country || 'Unknown';
                        city = response.data.city || 'Unknown';
                    }
                }
                catch (geoError) {
                    this.logger.warn(`Failed to get geolocation for IP ${ip}:`, geoError.message);
                }
            }
            return {
                ip: ip || 'Unknown',
                country,
                city,
                userAgent: userAgent || 'Unknown',
            };
        }
        catch (error) {
            this.logger.error('Failed to extract device info:', error);
            return {
                ip: 'Unknown',
                country: 'Unknown',
                city: 'Unknown',
                userAgent: 'Unknown',
            };
        }
    }
    extractIpAddress(req) {
        const forwarded = req.headers['x-forwarded-for'];
        const remoteAddress = req.socket.remoteAddress;
        if (forwarded) {
            return forwarded.toString().split(',')[0].trim();
        }
        return remoteAddress || 'Unknown';
    }
    isLocalIp(ip) {
        if (!ip || ip === 'Unknown')
            return true;
        const localPatterns = [
            /^127\./,
            /^192\.168\./,
            /^10\./,
            /^172\.(1[6-9]|2\d|3[01])\./,
            /^::1$/,
            /^fe80:/,
        ];
        return localPatterns.some(pattern => pattern.test(ip));
    }
};
exports.DeviceInfoService = DeviceInfoService;
exports.DeviceInfoService = DeviceInfoService = DeviceInfoService_1 = __decorate([
    (0, common_1.Injectable)()
], DeviceInfoService);
//# sourceMappingURL=device-info.service.js.map