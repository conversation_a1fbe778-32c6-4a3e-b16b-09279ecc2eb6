(()=>{var e={};e.id=1741,e.ids=[1741],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6739:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>c});var t=a(60687),s=a(43210),d=a(30474),i=a(94391),l=a(63213),n=a(39659),o=a(86732);let c=()=>{let{user:e}=(0,l.A)(),{theme:r,setTheme:a}=(0,n.D)(),[c,x]=(0,s.useState)("profile"),[m,g]=(0,s.useState)(!1),[u,y]=(0,s.useState)({firstName:e?.first_name||"",lastName:e?.last_name||"",email:e?.email||"",phone:e?.phone||"",organizationName:"",address:"",city:"",country:"Malawi"}),[p,b]=(0,s.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[h,f]=(0,s.useState)(!1),[j,k]=(0,s.useState)({type:"",text:""}),v=e=>{let{name:r,value:a}=e.target;y(e=>({...e,[r]:a}))},N=e=>{let{name:r,value:a}=e.target;b(e=>({...e,[r]:a}))},w=async e=>{e.preventDefault(),f(!0),k({type:"",text:""});try{await new Promise(e=>setTimeout(e,1e3)),k({type:"success",text:"Profile updated successfully!"}),g(!1)}catch{k({type:"error",text:"Failed to update profile. Please try again."})}finally{f(!1)}},P=async e=>{if(e.preventDefault(),p.newPassword!==p.confirmPassword)return void k({type:"error",text:"New passwords do not match."});f(!0),k({type:"",text:""});try{await new Promise(e=>setTimeout(e,1e3)),k({type:"success",text:"Password changed successfully!"}),b({currentPassword:"",newPassword:"",confirmPassword:""})}catch{k({type:"error",text:"Failed to change password. Please try again."})}finally{f(!1)}};return(0,t.jsx)(i.A,{children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:"My Profile"}),(0,t.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Manage your account settings and preferences"})]}),(0,t.jsx)("div",{className:"flex items-center space-x-3",children:(0,t.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",children:[(0,t.jsx)("i",{className:"ri-check-line mr-1"}),"Verified Account"]})})]})}),(0,t.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow mb-6",children:(0,t.jsx)("div",{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(d.default,{className:"h-20 w-20 rounded-full object-cover ring-4 ring-white dark:ring-gray-800",src:e?.profile_image||"https://banner2.cleanpng.com/********/sac/9128e319522f66aba9edf95180a86e7e.webp",alt:"Profile",width:80,height:80}),(0,t.jsx)("button",{className:"absolute bottom-0 right-0 bg-primary hover:bg-red-700 text-white rounded-full p-1.5 shadow-lg transition-colors",title:"Change profile picture","aria-label":"Change profile picture",children:(0,t.jsx)("i",{className:"ri-camera-line text-sm"})})]})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-gray-100",children:e?`${e.last_name} ${e.last_name}`:"Customer Name"}),(0,t.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400"}),(0,t.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:e?.email||"<EMAIL>"}),(0,t.jsx)("div",{className:"mt-2 flex items-center space-x-4",children:(0,t.jsxs)("span",{className:"inline-flex items-center text-sm text-gray-500 dark:text-gray-400",children:[(0,t.jsx)("i",{className:"ri-calendar-line mr-1"}),"Member since ",new Date().getFullYear()-1]})})]})]})})}),(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow mb-6",children:[(0,t.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-700",children:(0,t.jsx)("nav",{className:"-mb-px flex space-x-8 px-6",children:[{id:"profile",name:"Profile Information",icon:"ri-user-line"},{id:"security",name:"Security",icon:"ri-shield-line"},{id:"preferences",name:"Preferences",icon:"ri-settings-line"}].map(e=>(0,t.jsxs)("button",{onClick:()=>x(e.id),className:`py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${c===e.id?"border-red-500 text-red-600 dark:text-red-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"}`,children:[(0,t.jsx)("i",{className:`${e.icon} mr-2`}),e.name]},e.id))})}),(0,t.jsxs)("div",{className:"p-6",children:[j.text&&(0,t.jsx)("div",{className:`mb-6 rounded-md p-4 border-l-4 ${"success"===j.type?"bg-green-50 dark:bg-green-900/20 border-green-400 dark:border-green-600":"bg-red-50 dark:bg-red-900/20 border-red-400 dark:border-red-600"}`,children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("i",{className:`${"success"===j.type?"ri-check-line text-green-400 dark:text-green-500":"ri-error-warning-line text-red-400 dark:text-red-500"} text-lg`})}),(0,t.jsx)("div",{className:"ml-3",children:(0,t.jsx)("p",{className:`text-sm ${"success"===j.type?"text-green-800 dark:text-green-300":"text-red-800 dark:text-red-300"}`,children:j.text})})]})}),"profile"===c&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Personal Information"}),(0,t.jsxs)("button",{onClick:()=>g(!m),className:"inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors",children:[(0,t.jsx)("i",{className:`${m?"ri-close-line":"ri-edit-line"} mr-2`}),m?"Cancel":"Edit Profile"]})]}),(0,t.jsxs)("form",{onSubmit:w,children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"First Name *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:"text",name:"firstName",value:u.firstName,onChange:v,disabled:!m,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500",placeholder:"Enter your first name"}),(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)("i",{className:"ri-user-line text-gray-400 dark:text-gray-500"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Last Name *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:"text",name:"lastName",value:u.lastName,onChange:v,disabled:!m,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500",placeholder:"Enter your last name"}),(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)("i",{className:"ri-user-line text-gray-400 dark:text-gray-500"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Email Address *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:"email",name:"email",value:u.email,onChange:v,disabled:!m,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500",placeholder:"Enter your email address"}),(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)("i",{className:"ri-mail-line text-gray-400 dark:text-gray-500"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Phone Number *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:"tel",name:"phone",value:u.phone,onChange:v,disabled:!m,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500",placeholder:"Enter your phone number"}),(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)("i",{className:"ri-phone-line text-gray-400 dark:text-gray-500"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Organization Name *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:"text",name:"organizationName",value:u.organizationName,onChange:v,disabled:!m,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500",placeholder:"Enter your organization name"}),(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)("i",{className:"ri-building-line text-gray-400 dark:text-gray-500"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"City"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:"text",name:"city",value:u.city,onChange:v,disabled:!m,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500",placeholder:"Enter your city"}),(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)("i",{className:"ri-map-pin-line text-gray-400 dark:text-gray-500"})})]})]})]}),(0,t.jsxs)("div",{className:"mt-6",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Address"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("textarea",{name:"address",rows:3,value:u.address,onChange:v,disabled:!m,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500",placeholder:"Enter your full address"}),(0,t.jsx)("div",{className:"absolute top-3 left-0 pl-3 flex items-start pointer-events-none",children:(0,t.jsx)("i",{className:"ri-home-line text-gray-400 dark:text-gray-500"})})]})]}),m&&(0,t.jsxs)("div",{className:"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700",children:[(0,t.jsx)("button",{type:"button",onClick:()=>g(!1),className:"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors",children:"Cancel"}),(0,t.jsx)("button",{type:"submit",disabled:h,className:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:h?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("i",{className:"ri-loader-4-line animate-spin mr-2"}),"Saving..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("i",{className:"ri-save-line mr-2"}),"Save Changes"]})})]})]})]}),"security"===c&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Security Settings"}),(0,t.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-6",children:[(0,t.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Change Password"}),(0,t.jsxs)("form",{onSubmit:P,className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Current Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:"password",name:"currentPassword",value:p.currentPassword,onChange:N,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500",placeholder:"Enter current password"}),(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)("i",{className:"ri-lock-line text-gray-400 dark:text-gray-500"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"New Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:"password",name:"newPassword",value:p.newPassword,onChange:N,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500",placeholder:"Enter new password"}),(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)("i",{className:"ri-lock-line text-gray-400 dark:text-gray-500"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Confirm New Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:"password",name:"confirmPassword",value:p.confirmPassword,onChange:N,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500",placeholder:"Confirm new password"}),(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)("i",{className:"ri-lock-line text-gray-400 dark:text-gray-500"})})]})]}),(0,t.jsx)("div",{className:"flex justify-end",children:(0,t.jsx)("button",{type:"submit",disabled:h,className:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:h?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("i",{className:"ri-loader-4-line animate-spin mr-2"}),"Updating..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("i",{className:"ri-shield-check-line mr-2"}),"Update Password"]})})})]})]}),(0,t.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6",children:[(0,t.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Account Security"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("i",{className:"ri-shield-check-line text-green-500 mr-3"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:"Two-Factor Authentication"}),(0,t.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Add an extra layer of security"})]})]}),(0,t.jsx)("button",{className:"text-sm text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300",children:"Enable"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("i",{className:"ri-smartphone-line text-blue-500 mr-3"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:"Login Notifications"}),(0,t.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Get notified of new sign-ins"})]})]}),(0,t.jsx)("button",{className:"text-sm text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300",children:"Configure"})]})]})]})]}),"preferences"===c&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Account Preferences"}),(0,t.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-6",children:[(0,t.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Notification Preferences"}),(0,t.jsx)("div",{className:"space-y-4",children:[{id:"email_notifications",label:"Email Notifications",description:"Receive updates via email"},{id:"license_expiry",label:"License Expiry Alerts",description:"Get notified before licenses expire"},{id:"payment_reminders",label:"Payment Reminders",description:"Receive payment due notifications"},{id:"application_updates",label:"Application Updates",description:"Get updates on application status"}].map(e=>(0,t.jsxs)("div",{className:"flex items-start space-x-3 p-3 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors",children:[(0,t.jsx)("div",{className:"flex items-center h-5",children:(0,t.jsx)("input",{id:e.id,type:"checkbox",defaultChecked:!0,className:"h-4 w-4 text-primary focus:ring-primary border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700 dark:focus:ring-primary dark:ring-offset-gray-800","aria-describedby":`${e.id}-description`})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("label",{htmlFor:e.id,className:"text-sm font-medium text-gray-900 dark:text-gray-100 cursor-pointer",children:e.label}),(0,t.jsx)("p",{id:`${e.id}-description`,className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:e.description})]})]},e.id))})]}),(0,t.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-6",children:[(0,t.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Theme Settings"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Choose your preferred color scheme for the interface"}),(0,t.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-4",children:[{value:"light",label:"Light",description:"Use light theme",icon:"ri-sun-line"},{value:"dark",label:"Dark",description:"Use dark theme",icon:"ri-moon-line"},{value:"system",label:"System",description:"Follow system preference",icon:"ri-computer-line"}].map(e=>(0,t.jsxs)("button",{onClick:()=>a(e.value),className:`relative p-4 border-2 rounded-lg transition-all duration-200 ${r===e.value?"border-red-500 bg-red-50 dark:bg-red-900/20":"border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500 bg-white dark:bg-gray-800"}`,children:[(0,t.jsxs)("div",{className:"flex flex-col items-center text-center",children:[(0,t.jsx)("div",{className:`w-10 h-10 rounded-lg flex items-center justify-center mb-3 ${r===e.value?"bg-red-100 dark:bg-red-800 text-red-600 dark:text-red-400":"bg-gray-100 dark:bg-gray-600 text-gray-600 dark:text-gray-400"}`,children:(0,t.jsx)("i",{className:`${e.icon} text-xl`})}),(0,t.jsx)("h5",{className:`text-sm font-medium ${r===e.value?"text-red-900 dark:text-red-100":"text-gray-900 dark:text-gray-100"}`,children:e.label}),(0,t.jsx)("p",{className:`text-xs mt-1 ${r===e.value?"text-red-700 dark:text-red-300":"text-gray-500 dark:text-gray-400"}`,children:e.description})]}),r===e.value&&(0,t.jsx)("div",{className:"absolute top-2 right-2",children:(0,t.jsx)("div",{className:"w-5 h-5 bg-red-500 rounded-full flex items-center justify-center",children:(0,t.jsx)("i",{className:"ri-check-line text-white text-xs"})})})]},e.value))})]})]}),(0,t.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-6",children:[(0,t.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Language & Region"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)(o.A,{label:"Language","aria-label":"Select language",children:[(0,t.jsx)("option",{children:"English"}),(0,t.jsx)("option",{children:"Chichewa"})]}),(0,t.jsxs)(o.A,{label:"Timezone","aria-label":"Select timezone",children:[(0,t.jsx)("option",{children:"Africa/Blantyre (CAT)"}),(0,t.jsx)("option",{children:"UTC"})]})]})]})]})]})]})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},12463:(e,r,a)=>{Promise.resolve().then(a.bind(a,6739))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24087:(e,r,a)=>{Promise.resolve().then(a.bind(a,24337))},24337:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>t});let t=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\profile\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\profile\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},51369:(e,r,a)=>{"use strict";a.r(r),a.d(r,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>o});var t=a(65239),s=a(48088),d=a(88170),i=a.n(d),l=a(30893),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);a.d(r,n);let o={children:["",{children:["customer",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,24337)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\profile\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,68736)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\profile\\page.tsx"],x={require:a,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/customer/profile/page",pathname:"/customer/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86732:(e,r,a)=>{"use strict";a.d(r,{A:()=>d});var t=a(60687);let s=(0,a(43210).forwardRef)(({label:e,error:r,helperText:a,variant:s="default",fullWidth:d=!0,className:i="",required:l,disabled:n,options:o,children:c,...x},m)=>{let g=`px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white transition-colors duration-200 ${d?"w-full":""} ${"small"===s?"py-1.5 text-sm":"py-2"}`,u=`${g} ${r?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"} ${n?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""} ${i}`,y=`block font-medium text-gray-700 dark:text-gray-300 mb-2 ${"small"===s?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"}`;return(0,t.jsxs)("div",{className:"w-full",children:[e&&(0,t.jsxs)("label",{className:y,children:[e,l&&(0,t.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,t.jsx)("select",{ref:m,className:u,disabled:n,required:l,...x,children:o?o.map(e=>(0,t.jsx)("option",{value:e.value,children:e.label},e.value)):c}),r&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:r}),a&&!r&&(0,t.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:a})]})});s.displayName="Select";let d=s},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var a=e=>r(r.s=e),t=r.X(0,[4447,7498,1658,5814,2335,6893],()=>a(51369));module.exports=t})();