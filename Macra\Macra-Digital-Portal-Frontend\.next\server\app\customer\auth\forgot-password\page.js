(()=>{var e={};e.id=218,e.ids=[218],e.modules={542:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l});var s=t(60687),a=t(43210),o=t(85814),i=t.n(o),n=t(30474),d=t(63443);function l(){let[e,r]=(0,a.useState)(""),[t,o]=(0,a.useState)(!1),[l,c]=(0,a.useState)(""),[u,m]=(0,a.useState)(""),[p,x]=(0,a.useState)(!1),[g,h]=(0,a.useState)(!1),f=async r=>{r.preventDefault(),o(!0),m(""),c("");try{let r=await d.y.forgotPassword({email:e});c(r.message),x(!0)}catch(e){m(e.response?.data?.message||"Failed to send reset email. Please try again.")}finally{o(!1)}};return(0,s.jsxs)("div",{className:"min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900",children:[(0,s.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsx)(n.default,{src:"/images/macra-logo.png",alt:"MACRA Logo",width:50,height:50,className:"h-16 w-auto"})}),(0,s.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-gray-100",children:p&&g?"Success!":"Forgot your password?"}),(0,s.jsx)("p",{className:"mt-2 text-center text-sm text-gray-600 dark:text-gray-400",children:p&&g?"A password reset link has been sent":"Enter your email address and we'll send you a reset code."})]}),(0,s.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",id:"submitForm",children:(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[u&&(0,s.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-md",children:u}),l&&g&&!p&&(0,s.jsx)("div",{className:"mb-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-600 dark:text-green-400 px-4 py-3 rounded-md",children:l}),!p&&g?(0,s.jsxs)("form",{className:"space-y-6",onSubmit:f,children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Email address"}),(0,s.jsx)("div",{className:"mt-1",children:(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e,onChange:e=>r(e.target.value),className:"appearance-none block w-full px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors"})})]}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",disabled:t,className:"w-full flex justify-center py-3 px-4 border-2 border-transparent rounded-md shadow-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-all disabled:opacity-50 disabled:cursor-not-allowed",children:t?"Sending...":"Send reset link"})})]}):(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center mt-4",children:[(0,s.jsx)("div",{className:"w-16 h-16 mb-4 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center animate-bounce shadow-md",children:(0,s.jsx)("svg",{className:"w-8 h-8 text-green-600 dark:text-green-300",fill:"none",stroke:"currentColor",strokeWidth:"3",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5 13l4 4L19 7"})})}),(0,s.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400 text-center",children:["If your account exists, you will receive an email with a reset link. ",(0,s.jsx)("br",{}),(0,s.jsx)(i(),{href:"/auth/forgot-password",className:"text-sm text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300",children:"Didn't receive link?"})]})]}),(!p||!g)&&(0,s.jsx)("div",{className:"mt-6 text-center",children:(0,s.jsx)(i(),{href:"/customer/auth/login",className:"font-medium text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300",children:"Back to sign in"})})]})})]})}},1720:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\auth\\\\forgot-password\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\auth\\forgot-password\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35677:(e,r,t)=>{Promise.resolve().then(t.bind(t,542))},40413:(e,r,t)=>{Promise.resolve().then(t.bind(t,1720))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68736:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o,metadata:()=>a});var s=t(37413);let a={title:"Customer Dashboard - Digital Portal",description:"Customer portal for managing licenses and applications"};function o({children:e}){return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50",children:e})}},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"256x256",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{},99801:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>l});var s=t(65239),a=t(48088),o=t(88170),i=t.n(o),n=t(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(r,d);let l={children:["",{children:["customer",{children:["auth",{children:["forgot-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1720)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\auth\\forgot-password\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,68736)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\auth\\forgot-password\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/customer/auth/forgot-password/page",pathname:"/customer/auth/forgot-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,7498,1658,5814,2335],()=>t(99801));module.exports=s})();