(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4837],{17363:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var a=r(95155),i=r(12115),s=r(35695),l=r(73209),n=r(84588),o=r(40283),d=r(30159),c=r(97500),m=r(19278);let u=()=>{let e=(0,s.useRouter)(),t=(0,s.useSearchParams)(),{isAuthenticated:r,loading:u}=(0,o.A)(),p=t.get("license_category_id"),x=t.get("application_id"),[h,b]=(0,i.useState)(!0),[g,f]=(0,i.useState)(!1),[y,w]=(0,i.useState)(null),[j,k]=(0,i.useState)(null),[v,N]=(0,i.useState)(!1),[A,S]=(0,i.useState)(null);(0,i.useEffect)(()=>{(async()=>{if(x&&r&&!u){await d.k.getApplication(x);try{if(b(!0),w(null),k(null),!x||""===x.trim())throw Error("Application ID is missing from the URL");if(!/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(x))throw Error("Invalid application ID format");if(p)try{let e=await c.TG.getLicenseCategory(p);e&&e.license_type&&S(e.license_type)}catch(e){}try{if(!x)throw Error("Application ID is null or undefined");if(!await d.k.getApplication(x))throw Error("Application not found")}catch(r){var e,t,a,i,s,l;if((null==(e=r.response)?void 0:e.status)===404)throw Error("Application not found. The application may have been deleted or the ID is incorrect.");if((null==(t=r.response)?void 0:t.status)===403)throw Error("You do not have permission to access this application.");if((null==(a=r.response)?void 0:a.status)===401)throw Error("Authentication required. Please log in again.");else if((null==(i=r.response)?void 0:i.status)===500)throw Error("Server error occurred while loading the application. Please try again later.");else if(null==(l=r.response)||null==(s=l.data)?void 0:s.message)throw Error("Failed to load application: ".concat(r.response.data.message));else throw Error("Failed to load application data: ".concat(r.message||"Unknown error"))}}catch(e){w(e.message||"Failed to load application data")}finally{b(!1)}}})()},[x,r,u]);let _=async()=>{if(!x)return w("Application ID is required"),!1;if(!v)return w("You must confirm the declaration before submitting"),!1;f(!0);try{return await d.k.updateApplication(x,{status:m.r.SUBMITTED,submitted_at:new Date().toISOString(),progress_percentage:100,current_step:8}),e.push("/customer/applications/submitted?application_id=".concat(x)),!0}catch(e){return w("Failed to submit application. Please try again."),!1}finally{f(!1)}},E=async()=>{await _()};return u||h?(0,a.jsx)(l.A,{children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading application for submission..."})]})})}):y?(0,a.jsx)(l.A,{children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto p-6",children:(0,a.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"ri-error-warning-line text-red-600 dark:text-red-400 text-2xl mr-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-red-800 dark:text-red-200",children:"Error Loading Application"}),(0,a.jsx)("p",{className:"text-red-700 dark:text-red-300 mt-1",children:y}),x&&(0,a.jsxs)("div",{className:"mt-2 p-2 bg-red-100 dark:bg-red-900/30 rounded text-xs text-red-600 dark:text-red-400",children:["Application ID: ",x]}),(0,a.jsxs)("div",{className:"mt-4 space-x-2",children:[(0,a.jsxs)("button",{onClick:()=>e.back(),className:"inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40",children:[(0,a.jsx)("i",{className:"ri-arrow-left-line mr-2"}),"Go Back"]}),(0,a.jsxs)("button",{onClick:()=>window.location.reload(),className:"inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40",children:[(0,a.jsx)("i",{className:"ri-refresh-line mr-2"}),"Retry"]})]})]})]})})})}):(0,a.jsx)(l.A,{children:(0,a.jsxs)(n.A,{onNext:E,onPrevious:()=>{e.push("/customer/applications/apply/documents?license_category_id=".concat(p,"&application_id=").concat(x))},onSave:async()=>!0,showNextButton:!0,showPreviousButton:!0,showSaveButton:!1,nextButtonText:g?"Submitting...":"Submit Application",previousButtonText:"Back to Documents",nextButtonDisabled:!v||g,isSaving:g,children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Submit Application"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:"Review your application details and submit for review by MACRA."}),x&&(0,a.jsx)("div",{className:"mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:[(0,a.jsx)("i",{className:"ri-file-text-line mr-1"}),"Application ID: ",x]})}),j&&(0,a.jsx)("div",{className:"mt-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-yellow-700 dark:text-yellow-300",children:["⚠️ ",j]})})]}),(0,a.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg mb-6",children:[(0,a.jsxs)("h4",{className:"text-md font-medium text-blue-900 dark:text-blue-100 mb-2",children:[(0,a.jsx)("i",{className:"ri-information-line mr-2"}),"Important Information"]}),(0,a.jsxs)("ul",{className:"text-sm text-blue-800 dark:text-blue-200 space-y-1",children:[(0,a.jsx)("li",{children:"• Your application will be reviewed by MACRA within 30 business days"}),(0,a.jsx)("li",{children:"• You will receive email notifications about the status of your application"}),(0,a.jsx)("li",{children:"• Additional documentation may be requested during the review process"}),(0,a.jsx)("li",{children:"• Application fees are non-refundable"}),(0,a.jsx)("li",{children:"• You can track your application status in your dashboard"})]})]}),(0,a.jsx)("div",{className:"bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg mb-6",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("i",{className:"ri-alert-line text-yellow-600 dark:text-yellow-400 mr-2 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-yellow-800 dark:text-yellow-200",children:"Before Submitting"}),(0,a.jsx)("p",{className:"text-sm text-yellow-700 dark:text-yellow-300 mt-1",children:"Please ensure all information is accurate and complete. Once submitted, you will not be able to modify your application without contacting MACRA support."})]})]})}),(0,a.jsxs)("div",{className:"border-t border-gray-200 dark:border-gray-700 pt-6",children:[(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("input",{type:"checkbox",id:"confirmation",checked:v,onChange:e=>N(e.target.checked),className:"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded mt-1"}),(0,a.jsxs)("label",{htmlFor:"confirmation",className:"ml-2 block text-sm text-gray-900 dark:text-gray-100",children:["I confirm that all information provided in this application is true, accurate, and complete to the best of my knowledge. I understand that providing false or misleading information may result in the rejection of my application or revocation of any license granted. I agree to the terms and conditions of the licensing process.",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]})]}),!v&&(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-500 dark:text-gray-400",children:"You must accept this declaration to submit your application."})]})]})})}},72689:(e,t,r)=>{Promise.resolve().then(r.bind(r,17363))}},e=>{var t=t=>e(e.s=t);e.O(0,[8122,6766,6874,283,3209,4588,6383,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(72689)),_N_E=e.O()}]);