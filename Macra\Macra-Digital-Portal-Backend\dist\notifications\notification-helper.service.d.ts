import { NotificationsService } from './notifications.service';
export declare class NotificationHelperService {
    private readonly notificationsService;
    constructor(notificationsService: NotificationsService);
    notifyApplicationStatus(applicationId: string, applicantId: string, applicantEmail: string, applicantPhone: string, applicationNumber: string, status: string, createdBy: string): Promise<void>;
    notifyTaskAssignment(taskId: string, assigneeId: string, assigneeEmail: string, assigneePhone: string, taskTitle: string, taskDescription: string, createdBy: string): Promise<void>;
    notifyLicenseExpiry(licenseId: string, customerId: string, customerEmail: string, customerPhone: string, licenseNumber: string, expiryDate: Date, daysUntilExpiry: number, createdBy: string): Promise<void>;
    private generateApplicationStatusEmailHtml;
    private generateTaskAssignmentEmailHtml;
    private generateLicenseExpiryEmailHtml;
}
