(()=>{var e={};e.id=4214,e.ids=[4214],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7538:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i,metadata:()=>s});var r=a(37413);let s={title:"License Applications - MACRA Digital Portal",description:"Manage license applications for various service types"};function i({children:e}){return(0,r.jsx)(r.Fragment,{children:e})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19961:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>c});var r=a(60687),s=a(43210),i=a(16189),n=a(63213),l=a(21891),o=a(60417);function c({children:e}){let{isAuthenticated:t,loading:a}=(0,n.A)();(0,i.useRouter)();let[c,d]=(0,s.useState)("overview"),[p,u]=(0,s.useState)(!1);return a?(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"})}):t?(0,r.jsxs)("div",{className:"flex h-screen overflow-hidden",children:[(0,r.jsx)("div",{id:"mobileSidebarOverlay",className:`mobile-sidebar-overlay ${p?"show":""}`,onClick:()=>u(!1)}),(0,r.jsx)(o.default,{}),(0,r.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,r.jsx)(l.default,{activeTab:c,onTabChange:d,onMobileMenuToggle:()=>{u(!p)}}),e]})]}):null}},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31373:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>c});var r=a(65239),s=a(48088),i=a(88170),n=a.n(i),l=a(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);a.d(t,o);let c={children:["",{children:["applications",{children:["[license-type]",{children:["view",{children:["[application-id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,80192)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\applications\\[license-type]\\view\\[application-id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,7538)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\applications\\[license-type]\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,68031)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\applications\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\applications\\[license-type]\\view\\[application-id]\\page.tsx"],p={require:a,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/applications/[license-type]/view/[application-id]/page",pathname:"/applications/[license-type]/view/[application-id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},33873:e=>{"use strict";e.exports=require("path")},45618:(e,t,a)=>{Promise.resolve().then(a.bind(a,98175))},51366:(e,t,a)=>{Promise.resolve().then(a.bind(a,19961))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59614:(e,t,a)=>{Promise.resolve().then(a.bind(a,68031))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68031:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\applications\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\applications\\layout.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},78637:(e,t,a)=>{"use strict";a.d(t,{k:()=>i});var r=a(51278),s=a(12234);let i={async getApplications(e){let t=new URLSearchParams;e?.page&&t.append("page",e.page.toString()),e?.limit&&t.append("limit",e.limit.toString()),e?.search&&t.append("search",e.search),e?.sortBy&&t.append("sortBy",e.sortBy),e?.sortOrder&&t.append("sortOrder",e.sortOrder),e?.filters?.licenseTypeId&&t.append("filter.license_category.license_type_id",e.filters.licenseTypeId),e?.filters?.licenseCategoryId&&t.append("filter.license_category_id",e.filters.licenseCategoryId),e?.filters?.status&&t.append("filter.status",e.filters.status);let a=await s.uE.get(`/applications?${t.toString()}`);return(0,r.zp)(a)},async getApplicationsByLicenseType(e,t){let a=new URLSearchParams;t?.page&&a.append("page",t.page.toString()),t?.limit&&a.append("limit",t.limit.toString()),t?.search&&a.append("search",t.search),t?.status&&a.append("filter.status",t.status),a.append("filter.license_category.license_type_id",e);let i=await s.uE.get(`/applications?${a.toString()}`);return(0,r.zp)(i)},async getApplication(e){let t=await s.uE.get(`/applications/${e}`);return(0,r.zp)(t)},async getApplicationsByApplicant(e){let t=await s.uE.get(`/applications/by-applicant/${e}`);return(0,r.zp)(t)},async getApplicationsByStatus(e){let t=await s.uE.get(`/applications/by-status/${e}`);return(0,r.zp)(t)},async updateApplicationStatus(e,t){let a=await s.uE.put(`/applications/${e}/status?status=${t}`);return(0,r.zp)(a)},async updateApplicationProgress(e,t,a){let i=await s.uE.put(`/applications/${e}/progress?currentStep=${t}&progressPercentage=${a}`);return(0,r.zp)(i)},async getApplicationStats(){let e=await s.uE.get("/applications/stats");return(0,r.zp)(e)},async createApplication(e){try{let t=await s.uE.post("/applications",e);return(0,r.zp)(t)}catch(e){throw e}},async updateApplication(e,t){try{let a=await s.uE.put(`/applications/${e}`,t,{timeout:3e4});return(0,r.zp)(a)}catch(e){if("ECONNABORTED"===e.code)throw Error("Request timeout - please try again");if(e.response?.status===400){let t=e.response?.data?.message||"Invalid application data";throw Error(`Bad Request: ${t}`)}if(e.response?.status===429)throw Error("Too many requests - please wait a moment and try again");throw e}},async deleteApplication(e){let t=await s.uE.delete(`/applications/${e}`);return(0,r.zp)(t)},async createApplicationWithApplicant(e){try{let t=new Date,a=t.toISOString().slice(0,10).replace(/-/g,""),r=t.toTimeString().slice(0,8).replace(/:/g,""),s=Math.random().toString(36).substr(2,3).toUpperCase(),i=`APP-${a}-${r}-${s}`;if(!/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e.user_id))throw Error(`Invalid user_id format: ${e.user_id}. Expected UUID format.`);return await this.createApplication({application_number:i,applicant_id:e.user_id,license_category_id:e.license_category_id,current_step:1,progress_percentage:0})}catch(e){throw e}},async saveApplicationSection(e,t,a){try{let a=1,r=["applicantInfo","companyProfile","businessInfo","serviceScope","businessPlan","legalHistory","reviewSubmit"].indexOf(t);a=r>=0?r+1:1;let s=Math.min(Math.round(a/6*100),100);await this.updateApplication(e,{progress_percentage:s,current_step:a})}catch(e){throw e}},async getApplicationSection(e,t){try{let a=await s.uE.get(`/applications/${e}/sections/${t}`);return(0,r.zp)(a)}catch(e){throw e}},async submitApplication(e){try{let t=await s.uE.put(`/applications/${e}`,{status:"submitted",submitted_at:new Date().toISOString(),progress_percentage:100,current_step:7});return(0,r.zp)(t)}catch(e){throw e}},async getUserApplications(){try{let e=await s.uE.get("/applications/user-applications"),t=(0,r.zp)(e),a=[];return t?.data?a=Array.isArray(t.data)?t.data:[]:Array.isArray(t)?a=t:t&&(a=[t]),a}catch(e){throw e}},async saveAsDraft(e,t){try{let a=await s.uE.put(`/applications/${e}`,{form_data:t,status:"draft"});return(0,r.zp)(a)}catch(e){throw e}},async validateApplication(e){try{let e={},t=[];for(let a of["applicantInfo","companyProfile","businessInfo","legalHistory"])e[a]&&0!==Object.keys(e[a]).length||t.push(`${a} section is incomplete`);return{isValid:0===t.length,errors:t}}catch(e){throw e}}}},79551:e=>{"use strict";e.exports=require("url")},80192:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\applications\\\\[license-type]\\\\view\\\\[application-id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\applications\\[license-type]\\view\\[application-id]\\page.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},82570:(e,t,a)=>{Promise.resolve().then(a.bind(a,80192))},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{},98175:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>o});var r=a(60687),s=a(16189),i=a(43210),n=a(78637);function l({applicationId:e,departmentType:t,onBack:a}){let[s,l]=(0,i.useState)(null),[o,c]=(0,i.useState)({}),[d,p]=(0,i.useState)(!1),[u,g]=(0,i.useState)(null),[x,m]=(0,i.useState)("applicant"),y=async()=>{p(!0),g(null);try{let t=await n.k.getApplication(e);l(t),c({})}catch(e){g("Failed to load application details")}finally{p(!1)}},h=(e,t)=>{let a=o[t]||{};return a&&0!==Object.keys(a).length?(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow",children:[(0,r.jsx)("h4",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4",children:e}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:Object.entries(a).map(([e,t])=>(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("label",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 capitalize",children:[e.replace(/([A-Z])/g," $1").trim(),":"]}),(0,r.jsx)("div",{className:"text-gray-900 dark:text-gray-100",children:Array.isArray(t)?(0,r.jsx)("ul",{className:"list-disc list-inside space-y-1",children:t.map((e,t)=>(0,r.jsx)("li",{className:"text-sm",children:"object"==typeof e?JSON.stringify(e,null,2):String(e)},t))}):"object"==typeof t&&null!==t?(0,r.jsx)("pre",{className:"text-sm bg-gray-100 dark:bg-gray-700 p-2 rounded overflow-auto",children:JSON.stringify(t,null,2)}):(0,r.jsx)("span",{className:"text-sm",children:String(t)||"N/A"})})]},e))})]}):(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow",children:[(0,r.jsx)("h4",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4",children:e}),(0,r.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"No data available for this section."})]})};return d?(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading application details..."})]})})}):u?(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("i",{className:"ri-error-warning-line text-4xl text-red-500 mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Error Loading Application"}),(0,r.jsx)("p",{className:"text-gray-500 dark:text-gray-400 mb-4",children:u}),(0,r.jsxs)("div",{className:"space-x-4",children:[(0,r.jsx)("button",{type:"button",onClick:y,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Try Again"}),(0,r.jsx)("button",{type:"button",onClick:a,className:"px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700",children:"Go Back"})]})]})})}):s?(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between py-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("button",{type:"button",onClick:a,className:"flex items-center text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100",children:[(0,r.jsx)("i",{className:"ri-arrow-left-line mr-2"}),"Back to ",{postal:"Postal Services",telecommunications:"Telecommunications",standards:"Standards Compliance",clf:"CLF (Converged Licensing Framework)"}[t]||t]}),(0,r.jsx)("div",{className:"h-6 border-l border-gray-300 dark:border-gray-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:"Application Details"}),(0,r.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mt-1",children:[s.application_number," • ",(e=>{if(!e)return(0,r.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300",children:"Unknown"});let t=e.replace("_"," ").replace(/\b\w/g,e=>e.toUpperCase());return(0,r.jsx)("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${{draft:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300",submitted:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",under_review:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",evaluation:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",approved:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",rejected:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",withdrawn:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"}[e]||"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"}`,children:t})})(s?.status)]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Progress"}),(0,r.jsxs)("p",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:[s?.progress_percentage||0,"%"]})]}),(0,r.jsx)("div",{className:"w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${s?.progress_percentage||0}%`}})})]})]})})}),(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsx)("nav",{className:"flex space-x-8","aria-label":"Tabs",children:[{id:"applicant",label:"Applicant Details",icon:"ri-user-line"},{id:"company",label:"Company Profile",icon:"ri-building-line"},{id:"management",label:"Management",icon:"ri-team-line"},{id:"professional",label:"Professional Services",icon:"ri-service-line"},{id:"business",label:"Business Info",icon:"ri-briefcase-line"},{id:"service",label:"Service Scope",icon:"ri-global-line"},{id:"plan",label:"Business Plan",icon:"ri-file-chart-line"},{id:"legal",label:"Legal History",icon:"ri-scales-line"},{id:"documents",label:"Documents",icon:"ri-folder-line"},{id:"comments",label:"Comments",icon:"ri-chat-3-line"},{id:"assignment",label:"Assignment",icon:"ri-user-settings-line"}].map(e=>(0,r.jsxs)("button",{type:"button",onClick:()=>m(e.id),className:`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${x===e.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:border-gray-300"}`,children:[(0,r.jsx)("i",{className:`${e.icon} mr-2`}),e.label]},e.id))})})}),(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(()=>{switch(x){case"applicant":default:return h("Applicant Details","applicantInfo");case"company":return h("Company Profile","companyProfile");case"management":return h("Management","management");case"professional":return h("Professional Services","professionalServices");case"business":return h("Business Information","businessInfo");case"service":return h("Service Scope","serviceScope");case"plan":return h("Business Plan","businessPlan");case"legal":return h("Legal History","legalHistory");case"documents":return(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow",children:[(0,r.jsx)("h4",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Documents"}),s?.documents&&s.documents.length>0?(0,r.jsx)("div",{className:"space-y-3",children:s.documents.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"ri-file-line text-2xl text-gray-400 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-gray-900 dark:text-gray-100",children:e.name||`Document ${t+1}`}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:e.type||"Unknown type"})]})]}),(0,r.jsxs)("button",{type:"button",className:"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium",children:[(0,r.jsx)("i",{className:"ri-download-line mr-1"}),"Download"]})]},t))}):(0,r.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"No documents uploaded"})]});case"comments":return(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow",children:[(0,r.jsx)("h4",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Comments & Reviews"}),(0,r.jsx)("p",{className:"text-gray-500 dark:text-gray-400 mb-4",children:"Add comments for each section before assigning to an officer."}),(0,r.jsx)("div",{className:"space-y-4",children:(0,r.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-4",children:[(0,r.jsx)("h5",{className:"font-medium text-gray-900 dark:text-gray-100 mb-2",children:"General Comments"}),(0,r.jsx)("textarea",{className:"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100",rows:4,placeholder:"Add your comments about this application..."}),(0,r.jsx)("button",{type:"button",className:"mt-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Save Comment"})]})})]});case"assignment":return(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow",children:[(0,r.jsx)("h4",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Officer Assignment"}),(0,r.jsx)("p",{className:"text-gray-500 dark:text-gray-400 mb-6",children:"Assign this application to an officer for review and processing."}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Select Officer"}),(0,r.jsxs)("select",{className:"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100",children:[(0,r.jsx)("option",{value:"",children:"Choose an officer..."}),(0,r.jsx)("option",{value:"officer1",children:"John Doe - Senior Officer"}),(0,r.jsx)("option",{value:"officer2",children:"Jane Smith - Review Officer"}),(0,r.jsx)("option",{value:"officer3",children:"Mike Johnson - Technical Officer"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Assignment Notes"}),(0,r.jsx)("textarea",{className:"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100",rows:4,placeholder:"Add any specific instructions or notes for the assigned officer..."})]}),(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsx)("button",{type:"button",className:"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Assign Officer"}),(0,r.jsx)("button",{type:"button",className:"px-6 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700",children:"Save as Draft"})]})]})]})}})()})]}):(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("i",{className:"ri-file-line text-4xl text-gray-400 mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"No Application Data"}),(0,r.jsx)("p",{className:"text-gray-500 dark:text-gray-400 mb-4",children:"Application details could not be found."}),(0,r.jsx)("button",{type:"button",onClick:a,className:"px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700",children:"Go Back"})]})})})}function o(){let e=(0,s.useParams)(),t=(0,s.useRouter)(),a=e["license-type"],i=e["application-id"];return(0,r.jsx)(l,{applicationId:i,departmentType:a,onBack:()=>{t.push(`/applications/${a}`)}})}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[4447,7498,1658,5814,2335,6606],()=>a(31373));module.exports=r})();