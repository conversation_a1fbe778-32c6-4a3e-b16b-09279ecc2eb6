'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useRouter } from 'next/navigation';
import Loader from '../../components/Loader';
import { taskAssignmentService, GenericTask } from '../../services/task-assignment';
import { useToast } from '../../contexts/ToastContext';

interface User {
  user_id: string;
  first_name: string;
  last_name: string;
  email: string;
  department?: string;
}

export default function TaskAssignmentPage() {
  const { isAuthenticated } = useAuth();
  const { showSuccess, showError } = useToast();
  const router = useRouter();
  const [tasks, setTasks] = useState<GenericTask[]>([]);
  const [officers, setOfficers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedTask, setSelectedTask] = useState<string | null>(null);
  const [selectedOfficer, setSelectedOfficer] = useState<string>('');
  const [isAssigning, setIsAssigning] = useState(false);
  const [activeTab, setActiveTab] = useState<'unassigned' | 'assigned'>('unassigned');
  const [taskTypeFilter, setTaskTypeFilter] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState<string>('');

  const fetchTasks = useCallback(async () => {
    try {
      setLoading(true);
      setError('');

      const params = {
        limit: 50,
        search: searchQuery || undefined,
        task_type: taskTypeFilter || undefined
      };

      const response = activeTab === 'unassigned'
        ? await taskAssignmentService.getUnassignedTasks(params)
        : await taskAssignmentService.getAssignedTasks(params);

      setTasks(response.data || []);
    } catch (err) {
      console.error('Error fetching tasks:', err);
      setError('Failed to load tasks');
    } finally {
      setLoading(false);
    }
  }, [activeTab, searchQuery, taskTypeFilter]);

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login');
      return;
    }

    fetchTasks();
    fetchOfficers();
  }, [isAuthenticated, router, fetchTasks]);

  const fetchOfficers = async () => {
    try {
      const response = await taskAssignmentService.getOfficers();
      setOfficers(response.data || []);
    } catch (err) {
      console.error('Error fetching officers:', err);
    }
  };

  const handleAssignTask = async (taskId: string, officerId: string) => {
    try {
      setIsAssigning(true);

      await taskAssignmentService.assignTask(taskId, { assignedTo: officerId });

      // Refresh tasks list
      await fetchTasks();
      setSelectedTask(null);
      setSelectedOfficer('');

      // Show success message
      showSuccess('Task assigned successfully!');
      setError('');
    } catch (err) {
      console.error('Error assigning task:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      showError(`Failed to assign task: ${errorMessage}`);
      setError('Failed to assign task');
    } finally {
      setIsAssigning(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'draft': return 'bg-gray-100 text-gray-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'submitted': return 'bg-blue-100 text-blue-800';
      case 'under_review': return 'bg-orange-100 text-orange-800';
      case 'evaluation': return 'bg-purple-100 text-purple-800';
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <Loader message="Loading Task Assignment..." />
      </div>
    );
  }

  const getTaskTypeIcon = (taskType: string) => {
    switch (taskType) {
      case 'application': return 'ri-file-text-line';
      case 'complaint': return 'ri-feedback-line';
      case 'data_breach': return 'ri-shield-line';
      case 'evaluation': return 'ri-survey-line';
      case 'inspection': return 'ri-search-line';
      default: return 'ri-task-line';
    }
  };

  const getTaskTypeColor = (taskType: string) => {
    switch (taskType) {
      case 'application': return 'bg-blue-100 text-blue-800';
      case 'complaint': return 'bg-yellow-100 text-yellow-800';
      case 'data_breach': return 'bg-red-100 text-red-800';
      case 'evaluation': return 'bg-green-100 text-green-800';
      case 'inspection': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
          Task Assignment
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Assign various types of tasks to officers for processing and evaluation
        </p>
      </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
            <p>{error}</p>
          </div>
        )}

        {/* Filters */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-4 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Search Tasks
              </label>
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search by task ID, title, or description..."
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Task Type
              </label>
              <select
                value={taskTypeFilter}
                onChange={(e) => setTaskTypeFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
              >
                <option value="">All Types</option>
                <option value="application">Applications</option>
                <option value="complaint">Complaints</option>
                <option value="data_breach">Data Breach Reports</option>
                <option value="evaluation">Evaluations</option>
                <option value="inspection">Inspections</option>
              </select>
            </div>
            <div className="flex items-end">
              <button
                type="button"
                onClick={() => {
                  setSearchQuery('');
                  setTaskTypeFilter('');
                }}
                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600"
              >
                Clear Filters
              </button>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 dark:border-gray-700 mb-6">
          <nav className="-mb-px flex space-x-8">
            <button
              type="button"
              onClick={() => setActiveTab('unassigned')}
              className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center ${
                activeTab === 'unassigned'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <i className="ri-task-line mr-2"></i>
              Unassigned Tasks
              <span className="ml-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 py-0.5 px-2 rounded-full text-xs">
                {tasks.length}
              </span>
            </button>
            <button
              type="button"
              onClick={() => setActiveTab('assigned')}
              className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center ${
                activeTab === 'assigned'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <i className="ri-user-check-line mr-2"></i>
              Assigned Tasks
              <span className="ml-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 py-0.5 px-2 rounded-full text-xs">
                {tasks.length}
              </span>
            </button>
          </nav>
        </div>

        {/* Tasks Table */}
        <div className="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Task ID
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Description
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Priority
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {activeTab === 'assigned' ? 'Assigned To' : 'Created'}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {activeTab === 'assigned' ? 'Assigned By' : 'Status'}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Review
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {tasks.length === 0 ? (
                  <tr>
                    <td colSpan={7} className="px-6 py-12 text-center text-gray-500 dark:text-gray-400">
                      <div className="flex flex-col items-center">
                        <i className="ri-task-line text-4xl mb-2"></i>
                        <p>No {activeTab} tasks found</p>
                        {(searchQuery || taskTypeFilter) && (
                          <p className="text-sm mt-1">Try adjusting your filters</p>
                        )}
                      </div>
                    </td>
                  </tr>
                ) : (
                  tasks.map((task) => (
                    <tr key={task.task_id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                      {/* Task ID */}
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="h-10 w-10 flex-shrink-0">
                            <div className="h-10 w-10 rounded-full bg-primary flex items-center justify-center">
                              <i className={`${getTaskTypeIcon(task.task_type)} text-white`}></i>
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {task.task_number}
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              ID: {task.task_id.slice(0, 8)}...
                            </div>
                          </div>
                        </div>
                      </td>

                      {/* Description */}
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-900 dark:text-gray-100 font-medium">
                          {task.title}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                          {task.description.length > 100
                            ? `${task.description.substring(0, 100)}...`
                            : task.description}
                        </div>
                      </td>

                      {/* Type */}
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getTaskTypeColor(task.task_type)}`}>
                          {task.task_type.replace('_', ' ').toUpperCase()}
                        </span>
                      </td>

                      {/* Priority */}
                      <td className="px-6 py-4 whitespace-nowrap">
                        {task.priority && (
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(task.priority)}`}>
                            {task.priority.toUpperCase()}
                          </span>
                        )}
                      </td>

                      {/* Assigned To / Created */}
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {activeTab === 'assigned' && task.assigned_to ? (
                          <div>
                            <div className="text-gray-900 dark:text-gray-100">
                              {task.assigned_to.first_name} {task.assigned_to.last_name}
                            </div>
                            <div className="text-xs">
                              {task.assigned_to.email}
                            </div>
                          </div>
                        ) : (
                          <div>
                            <div className="text-gray-900 dark:text-gray-100">
                              {new Date(task.created_at).toLocaleDateString()}
                            </div>
                            <div className="text-xs">
                              {new Date(task.created_at).toLocaleTimeString()}
                            </div>
                          </div>
                        )}
                      </td>

                      {/* Assigned By / Status */}
                      <td className="px-6 py-4 whitespace-nowrap">
                        {activeTab === 'assigned' && task.assigned_by ? (
                          <div className="text-sm">
                            <div className="text-gray-900 dark:text-gray-100">
                              {task.assigned_by.first_name} {task.assigned_by.last_name}
                            </div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              {task.assigned_at ? new Date(task.assigned_at).toLocaleDateString() : ''}
                            </div>
                          </div>
                        ) : (
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            task.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                            task.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                            task.status === 'completed' ? 'bg-green-100 text-green-800' :
                            task.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {task.status.replace('_', ' ').toUpperCase()}
                          </span>
                        )}
                      </td>

                      {/* Review (Actions) */}
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          <button
                            className="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 p-1 rounded hover:bg-indigo-50 dark:hover:bg-indigo-900"
                            title="View Task Details"
                          >
                            <i className="ri-eye-line"></i>
                          </button>
                          {activeTab === 'unassigned' && (
                            <button
                              onClick={() => setSelectedTask(task.task_id)}
                              className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 p-1 rounded hover:bg-green-50 dark:hover:bg-green-900"
                              title="Assign Task"
                            >
                              <i className="ri-user-add-line"></i>
                            </button>
                          )}
                          {activeTab === 'assigned' && (
                            <button
                              onClick={() => setSelectedTask(task.task_id)}
                              className="text-orange-600 hover:text-orange-900 dark:text-orange-400 dark:hover:text-orange-300 p-1 rounded hover:bg-orange-50 dark:hover:bg-orange-900"
                              title="Reassign Task"
                            >
                              <i className="ri-user-settings-line"></i>
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Assignment Modal */}
        {selectedTask && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
              <div className="mt-3">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                  Assign Task to Officer
                </h3>
                
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Select Officer
                  </label>
                  <select
                    value={selectedOfficer}
                    onChange={(e) => setSelectedOfficer(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
                  >
                    <option value="">Select an officer...</option>
                    {officers.map((officer) => (
                      <option key={officer.user_id} value={officer.user_id}>
                        {officer.first_name} {officer.last_name} - {officer.email}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => {
                      setSelectedTask(null);
                      setSelectedOfficer('');
                    }}
                    className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    onClick={() => handleAssignTask(selectedTask, selectedOfficer)}
                    disabled={!selectedOfficer || isAssigning}
                    className="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isAssigning ? 'Assigning...' : 'Assign'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
    </div>
  );
}