'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useRouter } from 'next/navigation';
import Loader from '../../components/Loader';
import { taskAssignmentService } from '../../services/task-assignment';

interface Application {
  application_id: string;
  application_number: string;
  status: string;
  created_at: string;
  updated_at: string;
  applicant: {
    applicant_id: string;
    company_name: string;
    first_name: string;
    last_name: string;
  };
  license_category: {
    license_category_id: string;
    name: string;
    license_type: {
      license_type_id: string;
      name: string;
    };
  };
  assignee?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  assigned_at?: string;
}

interface User {
  user_id: string;
  first_name: string;
  last_name: string;
  email: string;
  department?: string;
}

export default function TaskAssignmentPage() {
  const { user, isAuthenticated } = useAuth();
  const router = useRouter();
  const [applications, setApplications] = useState<Application[]>([]);
  const [officers, setOfficers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedApplication, setSelectedApplication] = useState<string | null>(null);
  const [selectedOfficer, setSelectedOfficer] = useState<string>('');
  const [isAssigning, setIsAssigning] = useState(false);
  const [activeTab, setActiveTab] = useState<'unassigned' | 'assigned'>('unassigned');

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login');
      return;
    }

    fetchApplications();
    fetchOfficers();
  }, [isAuthenticated, router, activeTab]);

  const fetchApplications = async () => {
    try {
      setLoading(true);
      
      const response = activeTab === 'unassigned' 
        ? await taskAssignmentService.getUnassignedApplications({ limit: 50 })
        : await taskAssignmentService.getAllApplications({ limit: 50 });
      
      setApplications(response.data || []);
    } catch (err) {
      console.error('Error fetching applications:', err);
      setError('Failed to load applications');
    } finally {
      setLoading(false);
    }
  };

  const fetchOfficers = async () => {
    try {
      const response = await taskAssignmentService.getOfficers();
      setOfficers(response.data || []);
    } catch (err) {
      console.error('Error fetching officers:', err);
    }
  };

  const handleAssignApplication = async (applicationId: string, officerId: string) => {
    try {
      setIsAssigning(true);
      
      await taskAssignmentService.assignApplication(applicationId, { assignedTo: officerId });
      
      // Refresh applications list
      await fetchApplications();
      setSelectedApplication(null);
      setSelectedOfficer('');
      
      // Show success message
      setError('');
      alert('Application assigned successfully!');
    } catch (err) {
      console.error('Error assigning application:', err);
      setError('Failed to assign application');
    } finally {
      setIsAssigning(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'draft': return 'bg-gray-100 text-gray-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'submitted': return 'bg-blue-100 text-blue-800';
      case 'under_review': return 'bg-orange-100 text-orange-800';
      case 'evaluation': return 'bg-purple-100 text-purple-800';
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <Loader message="Loading Task Assignment..." />
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
          Task Assignment
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Assign license applications to officers for processing and evaluation
        </p>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
          <p>{error}</p>
        </div>
      )}

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700 mb-6">
        <nav className="-mb-px flex space-x-8">
          <button
            type="button"
            onClick={() => setActiveTab('unassigned')}
            className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center ${
              activeTab === 'unassigned'
                ? 'border-primary text-primary'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <i className="ri-file-list-line mr-2"></i>
            Unassigned Applications
            <span className="ml-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 py-0.5 px-2 rounded-full text-xs">
              {applications.length}
            </span>
          </button>
          <button
            type="button"
            onClick={() => setActiveTab('assigned')}
            className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center ${
              activeTab === 'assigned'
                ? 'border-primary text-primary'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <i className="ri-user-settings-line mr-2"></i>
            Assigned Applications
          </button>
        </nav>
      </div>

      {/* Applications Table */}
      <div className="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Application
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Applicant
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  License Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {activeTab === 'assigned' ? 'Assigned To' : 'Submitted'}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {applications.map((application) => (
                <tr key={application.application_id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="h-10 w-10 flex-shrink-0">
                        <div className="h-10 w-10 rounded-full bg-primary flex items-center justify-center">
                          <i className="ri-file-text-line text-white"></i>
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {application.application_number}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          ID: {application.application_id.slice(0, 8)}...
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-gray-100">
                      {application.applicant.company_name || 
                       `${application.applicant.first_name} ${application.applicant.last_name}`}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-gray-100">
                      {application.license_category.license_type.name}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {application.license_category.name}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(application.status)}`}>
                      {application.status.replace('_', ' ').toUpperCase()}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {activeTab === 'assigned' && application.assignee ? (
                      <div>
                        <div className="text-sm text-gray-900 dark:text-gray-100">
                          {application.assignee.first_name} {application.assignee.last_name}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {application.assigned_at ? formatDate(application.assigned_at) : 'Recently assigned'}
                        </div>
                      </div>
                    ) : (
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {formatDate(application.created_at)}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end space-x-2">
                      <button 
                        className="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 p-1 rounded hover:bg-indigo-50 dark:hover:bg-indigo-900" 
                        title="View Application"
                      >
                        <i className="ri-eye-line"></i>
                      </button>
                      {activeTab === 'unassigned' && (
                        <button
                          onClick={() => setSelectedApplication(application.application_id)}
                          className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 p-1 rounded hover:bg-green-50 dark:hover:bg-green-900"
                          title="Assign Application"
                        >
                          <i className="ri-user-add-line"></i>
                        </button>
                      )}
                      {activeTab === 'assigned' && (
                        <button
                          onClick={() => setSelectedApplication(application.application_id)}
                          className="text-orange-600 hover:text-orange-900 dark:text-orange-400 dark:hover:text-orange-300 p-1 rounded hover:bg-orange-50 dark:hover:bg-orange-900"
                          title="Reassign Application"
                        >
                          <i className="ri-user-settings-line"></i>
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Empty State */}
      {applications.length === 0 && (
        <div className="text-center py-12">
          <i className="ri-file-list-line text-4xl text-gray-400 mb-4"></i>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
            No {activeTab} applications found
          </h3>
          <p className="text-gray-500 dark:text-gray-400">
            {activeTab === 'unassigned' 
              ? 'All applications have been assigned to officers.' 
              : 'No applications have been assigned yet.'}
          </p>
        </div>
      )}

      {/* Assignment Modal */}
      {selectedApplication && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                Assign Application to Officer
              </h3>
              
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Select Officer
                </label>
                <select
                  value={selectedOfficer}
                  onChange={(e) => setSelectedOfficer(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
                >
                  <option value="">Select an officer...</option>
                  {officers.map((officer) => (
                    <option key={officer.user_id} value={officer.user_id}>
                      {officer.first_name} {officer.last_name} - {officer.email}
                    </option>
                  ))}
                </select>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => {
                    setSelectedApplication(null);
                    setSelectedOfficer('');
                  }}
                  className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                >
                  Cancel
                </button>
                <button
                  onClick={() => handleAssignApplication(selectedApplication, selectedOfficer)}
                  disabled={!selectedOfficer || isAssigning}
                  className="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isAssigning ? 'Assigning...' : 'Assign'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}