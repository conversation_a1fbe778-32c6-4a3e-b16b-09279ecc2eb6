import axios, { AxiosInstance, AxiosError } from 'axios';
import { getAuthToken } from './auth';
import { forceLogout } from './authUtils';
import { apiRateLimiter, withExponentialBackoff } from '../utils/rateLimiter';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

/**
 * Create an authenticated axios instance with proper error handling
 * This instance automatically handles 401 errors and performs auto-logout
 */
export const createApiClient = (baseURL: string = API_BASE_URL): AxiosInstance => {
  const instance = axios.create({
    baseURL,
    timeout: 120000, // 120 second timeout for better reliability
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Request interceptor to add auth token and rate limiting
  instance.interceptors.request.use(
    async (config) => {
      // // Apply rate limiting based on endpoint
      // const endpoint = config.url || '';
      // const rateLimitKey = endpoint.includes('license-types') || endpoint.includes('license-categories')
      //   ? 'license-data'
      //   : 'general';

      // // Check rate limit before making request
      // await apiRateLimiter.checkRateLimit(rateLimitKey);

      const token = getAuthToken();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }

      // Log request for debugging (only in development)
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Staff API] Making ${config.method?.toUpperCase()} request to: ${config.baseURL}${config.url}`);
      }
      return config;
    },
    (error) => {
      console.error('Request interceptor error:', error);
      return Promise.reject(error);
    }
  );

  // Response interceptor for error handling and auto-logout
  instance.interceptors.response.use(
    (response) => {
      return response;
    },
    async (error: AxiosError) => {
      const originalRequest = error.config as AxiosError['config'] & {
        _retry?: boolean;
        _retryCount?: number;
      };

      // Handle 429 Rate Limiting (same as customer API client)
      if (error.response?.status === 429) {
        // Ensure originalRequest exists and has the required properties
        if (originalRequest && !originalRequest._retry) {
          originalRequest._retry = true;

          // Get retry delay from headers or use exponential backoff
          const retryAfter = error.response.headers['retry-after'];
          const delay = retryAfter ? parseInt(retryAfter) * 1000 : Math.min(1000 * Math.pow(2, originalRequest._retryCount || 0), 10000);

          originalRequest._retryCount = (originalRequest._retryCount || 0) + 1;

          // Don't retry more than 3 times
          if (originalRequest._retryCount <= 10) {
            if (process.env.NODE_ENV === 'development') {
              console.warn(`[Staff API] Rate limited. Retrying after ${delay}ms (attempt ${originalRequest._retryCount})`);
            }

            await new Promise(resolve => setTimeout(resolve, delay));
            return instance(originalRequest);
          } else {
            // Exhausted retries
            if (process.env.NODE_ENV === 'development') {
              console.error('[Staff API] Rate limiting exhausted retries:', {
                url: error.config?.url,
                method: error.config?.method,
                retryCount: originalRequest._retryCount
              });
            }
          }
        } else {
          // Already retrying or no original request
          if (process.env.NODE_ENV === 'development') {
            console.warn('[Staff API] Rate limited but already retrying or no original request');
          }
        }
      }

      // Only log detailed errors in development (for non-rate-limiting errors or exhausted retries)
      if (process.env.NODE_ENV === 'development' && error.response?.status !== 429) {
        console.error('API Error Details:', {
          url: error.config?.url,
          method: error.config?.method,
          status: error.response?.status,
          statusText: error.response?.statusText,
          message: error.message,
          code: error.code,
          data: error.response?.data
        });
      }

      // Handle authentication errors - auto logout on 401
      if (error.response?.status === 401) {
        console.warn('Authentication failed - token invalid or expired. Forcing logout...');
        forceLogout();
        return Promise.reject(new Error('Authentication failed. Please log in again.'));
      }

      // Handle authorization errors
      if (error.response?.status === 403) {
        console.warn('Access denied - insufficient permissions');
        // You can add a toast notification here
      }

      // Handle validation/conflict errors (duplicate registration, etc.)
      if (error.response?.status === 409 || error.response?.status === 422) {
        const errorData = error.response?.data as { message?: string };
        console.warn('Validation/Conflict error:', errorData?.message || error.message);
        // Let the calling service handle the specific error message
      }

      // Handle network errors
      if (error.code === 'ERR_NETWORK' || error.message === 'Network Error') {
        console.error('Network error - backend may not be accessible');
        // You can add a toast notification here
      }

      // Handle timeout errors
      if (error.code === 'ECONNABORTED') {
        console.error('Request timeout - server took too long to respond');
        // You can add a toast notification here
      }

      return Promise.reject(error);
    }
  );

  return instance;
};

/**
 * Default API client instance
 */
export const apiClient = createApiClient();

/**
 * Auth-specific API client
 */
export const authApiClient = createApiClient(`${API_BASE_URL}/auth`);

/**
 * Users API client
 */
export const usersApiClient = createApiClient(`${API_BASE_URL}/users`);

/**
 * Roles API client
 */
export const rolesApiClient = createApiClient(`${API_BASE_URL}/roles`);

/**
 * Audit Trail API client
 */
export const auditApiClient = createApiClient(`${API_BASE_URL}/audit-trail`);
