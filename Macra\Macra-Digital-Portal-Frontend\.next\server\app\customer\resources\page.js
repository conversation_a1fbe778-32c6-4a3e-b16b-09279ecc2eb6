(()=>{var e={};e.id=6737,e.ids=[6737],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},22145:(e,r,t)=>{"use strict";t.d(r,{A:()=>i});var s=t(60687);let a=(0,t(43210).forwardRef)(({label:e,error:r,helperText:t,variant:a="default",fullWidth:i=!0,className:l="",required:n,disabled:d,...o},c)=>{let x=`px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 ${i?"w-full":""} ${"small"===a?"py-1.5 text-sm":"py-2"}`,m=`${x} ${r?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"} ${d?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""} ${l}`,u=`block font-medium text-gray-700 dark:text-gray-300 mb-2 ${"small"===a?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"}`;return(0,s.jsxs)("div",{className:"w-full",children:[e&&(0,s.jsxs)("label",{className:u,children:[e,n&&(0,s.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,s.jsx)("input",{ref:c,className:m,disabled:d,required:n,...o}),r&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:r}),t&&!r&&(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:t})]})});a.displayName="TextInput";let i=a},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},33919:(e,r,t)=>{Promise.resolve().then(t.bind(t,49241))},49241:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\resources\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\resources\\page.tsx","default")},53207:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var s=t(60687),a=t(43210),i=t(85814),l=t.n(i),n=t(16189),d=t(94391),o=t(63213),c=t(22145),x=t(62978),m=t(86732);let u=()=>{let{isAuthenticated:e,loading:r}=(0,o.A)(),t=(0,n.useRouter)(),[i,u]=(0,a.useState)(!1),[g,p]=(0,a.useState)({requestType:"",subject:"",description:"",relatedLicense:"",contactMethod:[]}),[h,y]=(0,a.useState)([]),[b,f]=(0,a.useState)(0),[j,v]=(0,a.useState)(!1),[N,k]=(0,a.useState)(""),[w,q]=(0,a.useState)(!1);if((0,a.useEffect)(()=>{u(!0)},[]),(0,a.useEffect)(()=>{!i||r||e||t.push("/customer/auth/login")},[i,r,e,t]),!i||r)return(0,s.jsx)(d.A,{children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,s.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading..."})]})})})});if(!e)return null;let P=e=>{let{name:r,value:t}=e.target;p(e=>({...e,[r]:t})),"description"===r&&f(t.length)},M=e=>{let{value:r,checked:t}=e.target;p(e=>({...e,contactMethod:t?[...e.contactMethod,r]:e.contactMethod.filter(e=>e!==r)}))},C=e=>{y(r=>r.filter(r=>r.name!==e))},S=async e=>{e.preventDefault(),q(!0);try{let e=Math.floor(1e3*Math.random())+1,r=`#REQ-2025-${String(e).padStart(3,"0")}`;k(r),v(!0)}catch(e){alert("Failed to submit request. Please try again.")}finally{q(!1)}};return(0,s.jsx)(d.A,{children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mb-4",children:[(0,s.jsx)(l(),{href:"/customer",className:"hover:text-primary",children:"Dashboard"}),(0,s.jsx)("i",{className:"ri-arrow-right-s-line"}),(0,s.jsx)("span",{className:"text-gray-900 dark:text-gray-100",children:"Request Resource"})]}),(0,s.jsx)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-gray-100",children:"Request Resource"}),(0,s.jsx)("p",{className:"mt-2 text-gray-600 dark:text-gray-400",children:"Submit requests for additional resources, support, or services from MACRA."})]})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,s.jsx)("div",{className:"lg:col-span-2",children:(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6",children:(0,s.jsxs)("form",{onSubmit:S,className:"space-y-6",children:[(0,s.jsxs)(m.A,{label:(0,s.jsxs)("span",{children:[(0,s.jsx)("i",{className:"ri-folder-line mr-2 text-primary"}),"Request Type"]}),id:"requestType",name:"requestType",required:!0,value:g.requestType,onChange:P,children:[(0,s.jsx)("option",{value:"",children:"Select request type"}),(0,s.jsx)("option",{value:"technical-support",children:"Technical Support"}),(0,s.jsx)("option",{value:"license-modification",children:"License Modification"}),(0,s.jsx)("option",{value:"spectrum-allocation",children:"Spectrum Allocation"}),(0,s.jsx)("option",{value:"compliance-guidance",children:"Compliance Guidance"}),(0,s.jsx)("option",{value:"documentation",children:"Documentation Request"}),(0,s.jsx)("option",{value:"training",children:"Training & Capacity Building"}),(0,s.jsx)("option",{value:"consultation",children:"Regulatory Consultation"}),(0,s.jsx)("option",{value:"other",children:"Other"})]}),(0,s.jsx)(c.A,{label:(0,s.jsxs)("span",{children:[(0,s.jsx)("i",{className:"ri-text mr-2 text-primary"}),"Subject"]}),id:"subject",name:"subject",required:!0,value:g.subject,onChange:P,placeholder:"Brief description of your request"}),(0,s.jsxs)("div",{children:[(0,s.jsx)(x.A,{label:(0,s.jsxs)("span",{children:[(0,s.jsx)("i",{className:"ri-file-text-line mr-2 text-primary"}),"Detailed Description"]}),id:"description",name:"description",rows:6,required:!0,value:g.description,onChange:P,placeholder:"Provide detailed information about your request, including any specific requirements or context...",maxLength:1e3}),(0,s.jsxs)("div",{className:"mt-2 text-sm text-gray-500 dark:text-gray-400",children:[(0,s.jsx)("span",{className:b>1e3?"text-red-500":"",children:b}),"/1000 characters"]})]}),(0,s.jsxs)(m.A,{label:(0,s.jsxs)("span",{children:[(0,s.jsx)("i",{className:"ri-award-line mr-2 text-primary"}),"Related License (Optional)"]}),id:"relatedLicense",name:"relatedLicense",value:g.relatedLicense,onChange:P,children:[(0,s.jsx)("option",{value:"",children:"Select related license"}),(0,s.jsx)("option",{value:"NSL-2025-001",children:"NSL-2025-001 - Internet Service Provider License"}),(0,s.jsx)("option",{value:"RBL-2025-002",children:"RBL-2025-002 - Radio Broadcasting License"}),(0,s.jsx)("option",{value:"TVL-2025-003",children:"TVL-2025-003 - Television Broadcasting License"}),(0,s.jsx)("option",{value:"MNL-2023-001",children:"MNL-2023-001 - Mobile Network License"}),(0,s.jsx)("option",{value:"SCL-2025-004",children:"SCL-2025-004 - Satellite Communications License"}),(0,s.jsx)("option",{value:"PSL-2025-005",children:"PSL-2025-005 - Postal Services License"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"fileInput",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[(0,s.jsx)("i",{className:"ri-attachment-line mr-2 text-primary"}),"Attachments (Optional)"]}),(0,s.jsxs)("div",{className:"border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center hover:border-primary transition-colors",children:[(0,s.jsx)("input",{type:"file",id:"fileInput",multiple:!0,accept:".pdf,.doc,.docx,.jpg,.jpeg,.png",onChange:e=>{Array.from(e.target.files||[]).forEach(e=>{if(e.size<=0xa00000){let r={name:e.name,size:e.size,file:e};y(e=>[...e,r])}else alert(`File ${e.name} is too large. Maximum size is 10MB.`)}),e.target.value=""},"aria-label":"Upload attachment files",className:"hidden"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("i",{className:"ri-upload-cloud-line text-4xl text-gray-400"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("button",{type:"button",onClick:()=>document.getElementById("fileInput")?.click(),className:"text-primary hover:text-primary font-medium",children:"Click to upload files"}),(0,s.jsx)("span",{className:"text-gray-500 dark:text-gray-400",children:" or drag and drop"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"PDF, DOC, DOCX, JPG, PNG up to 10MB each"})]})]}),h.length>0&&(0,s.jsx)("div",{className:"mt-3 space-y-2",children:h.map((e,r)=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("i",{className:"ri-file-line mr-3 text-primary"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:e.name}),(0,s.jsxs)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:[(e.size/1024/1024).toFixed(2)," MB"]})]})]}),(0,s.jsx)("button",{type:"button",onClick:()=>C(e.name),className:"text-red-500 hover:text-red-700","aria-label":`Remove ${e.name}`,title:`Remove ${e.name}`,children:(0,s.jsx)("i",{className:"ri-close-line"})})]},r))})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:[(0,s.jsx)("i",{className:"ri-phone-line mr-2 text-primary"}),"Preferred Contact Method"]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3",children:[(0,s.jsxs)("label",{className:"flex items-center p-3 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,s.jsx)("input",{type:"checkbox",name:"contactMethod",value:"email",checked:g.contactMethod.includes("email"),onChange:M,className:"rounded border-gray-300 text-primary focus:ring-primary"}),(0,s.jsx)("span",{className:"ml-3 text-sm text-gray-900 dark:text-gray-100",children:"Email"})]}),(0,s.jsxs)("label",{className:"flex items-center p-3 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,s.jsx)("input",{type:"checkbox",name:"contactMethod",value:"phone",checked:g.contactMethod.includes("phone"),onChange:M,className:"rounded border-gray-300 text-primary focus:ring-primary"}),(0,s.jsx)("span",{className:"ml-3 text-sm text-gray-900 dark:text-gray-100",children:"Phone"})]})]})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 pt-6",children:[(0,s.jsxs)("button",{type:"submit",disabled:w,className:"flex-1 bg-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,s.jsx)("i",{className:"ri-send-plane-line mr-2"}),w?"Submitting...":"Submit Request"]}),(0,s.jsxs)("button",{type:"button",onClick:()=>{alert("Draft saved successfully! You can continue editing later.")},className:"flex-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-lg font-medium hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all",children:[(0,s.jsx)("i",{className:"ri-save-line mr-2"}),"Save as Draft"]})]})]})})}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6",children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center",children:[(0,s.jsx)("i",{className:"ri-lightning-line mr-2 text-primary"}),"Quick Actions"]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)(l(),{href:"/customer/help",className:"flex items-center p-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors",children:[(0,s.jsx)("i",{className:"ri-question-line mr-3 text-primary"}),"View FAQ"]}),(0,s.jsxs)(l(),{href:"/customer/documents",className:"flex items-center p-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors",children:[(0,s.jsx)("i",{className:"ri-book-line mr-3 text-primary"}),"Resource Library"]}),(0,s.jsxs)(l(),{href:"/customer/help",className:"flex items-center p-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors",children:[(0,s.jsx)("i",{className:"ri-customer-service-line mr-3 text-primary"}),"Help Center"]}),(0,s.jsxs)(l(),{href:"/customer/resources/history",className:"flex items-center p-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors",children:[(0,s.jsx)("i",{className:"ri-time-line mr-3 text-primary"}),"Request History"]})]})]}),(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6",children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center",children:[(0,s.jsx)("i",{className:"ri-time-line mr-2 text-primary"}),"Expected Response Times"]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full mr-3"}),(0,s.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:"Low Priority"})]}),(0,s.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"5-7 days"})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-3 h-3 bg-yellow-500 rounded-full mr-3"}),(0,s.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:"Medium Priority"})]}),(0,s.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"2-3 days"})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 bg-red-50 dark:bg-red-900/20 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-3 h-3 bg-red-500 rounded-full mr-3"}),(0,s.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:"High Priority"})]}),(0,s.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"24 hours"})]})]})]})]})]}),j&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-xl p-8 max-w-md mx-4",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)("i",{className:"ri-check-line text-2xl text-green-600"})}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2",children:"Request Submitted Successfully"}),(0,s.jsxs)("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:["Your request has been submitted and assigned ticket number ",(0,s.jsx)("strong",{children:N}),". You will receive updates via email."]}),(0,s.jsx)("button",{onClick:()=>{v(!1),p({requestType:"",subject:"",description:"",relatedLicense:"",contactMethod:[]}),y([]),f(0)},className:"w-full bg-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-opacity-90 transition-all",children:"Continue"})]})})})]})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62978:(e,r,t)=>{"use strict";t.d(r,{A:()=>i});var s=t(60687);let a=(0,t(43210).forwardRef)(({label:e,error:r,helperText:t,variant:a="default",fullWidth:i=!0,className:l="",required:n,disabled:d,rows:o=3,...c},x)=>{let m=`px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 resize-y ${i?"w-full":""} ${"small"===a?"py-1.5 text-sm":"py-2"}`,u=`${m} ${r?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"} ${d?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""} ${l}`,g=`block font-medium text-gray-700 dark:text-gray-300 mb-2 ${"small"===a?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"}`;return(0,s.jsxs)("div",{className:"w-full",children:[e&&(0,s.jsxs)("label",{className:g,children:[e,n&&(0,s.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,s.jsx)("textarea",{ref:x,className:u,disabled:d,required:n,rows:o,...c}),r&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:r}),t&&!r&&(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:t})]})});a.displayName="TextArea";let i=a},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70871:(e,r,t)=>{Promise.resolve().then(t.bind(t,53207))},71905:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>o});var s=t(65239),a=t(48088),i=t(88170),l=t.n(i),n=t(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(r,d);let o={children:["",{children:["customer",{children:["resources",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,49241)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\resources\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,68736)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\resources\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/customer/resources/page",pathname:"/customer/resources",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86732:(e,r,t)=>{"use strict";t.d(r,{A:()=>i});var s=t(60687);let a=(0,t(43210).forwardRef)(({label:e,error:r,helperText:t,variant:a="default",fullWidth:i=!0,className:l="",required:n,disabled:d,options:o,children:c,...x},m)=>{let u=`px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors duration-200 ${i?"w-full":""} ${"small"===a?"py-1.5 text-sm":"py-2"}`,g=`${u} ${r?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"} ${d?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""} ${l}`,p=`block font-medium text-gray-700 dark:text-gray-300 mb-2 ${"small"===a?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"}`;return(0,s.jsxs)("div",{className:"w-full",children:[e&&(0,s.jsxs)("label",{className:p,children:[e,n&&(0,s.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,s.jsx)("select",{ref:m,className:g,disabled:d,required:n,...x,children:o?o.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value)):c}),r&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:r}),t&&!r&&(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:t})]})});a.displayName="Select";let i=a},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,7498,1658,5814,7563,6893],()=>t(71905));module.exports=s})();