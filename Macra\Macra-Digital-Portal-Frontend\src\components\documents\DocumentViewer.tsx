'use client';

import React, { useState, useEffect, useCallback } from 'react';
import DataTable from '@/components/common/DataTable';
import DocumentPreviewModal from '@/components/documents/DocumentPreviewModal';
import { documentService } from '@/services/documentService';
import { PaginateQuery, PaginatedResponse } from '@/services/userService';

// Document interface based on the backend entity
export interface Document {
  document_id: string;
  document_type: string;
  file_name: string;
  entity_type: string;
  entity_id: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  is_required: boolean;
  created_at: string;
  updated_at: string;
  created_by: string;
  updated_by?: string;
  creator?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
}

interface TableColumn {
  key: string;
  label: string;
  sortable?: boolean;
  render?: (value: unknown, item: Document) => React.ReactElement;
}

interface DocumentViewerProps {
  title?: string;
  subtitle?: string;
  searchPlaceholder?: string;
  showEntityInfo?: boolean;
  showRequiredColumn?: boolean;
  showCreatorInfo?: boolean;
  showActions?: boolean;
  customActions?: (document: Document) => React.ReactElement;
  filterParams?: Record<string, string | number | string[] | undefined>;
  className?: string;
}

const DocumentViewer: React.FC<DocumentViewerProps> = ({
  title = "Documents",
  subtitle = "View and manage documents",
  searchPlaceholder = "Search documents by name, type, or entity...",
  showEntityInfo = true,
  showRequiredColumn = true,
  showCreatorInfo = false,
  showActions = true,
  customActions,
  filterParams = {},
  className = "",
}) => {
  const [documentsData, setDocumentsData] = useState<PaginatedResponse<Document> | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState<boolean>(false);

  // Load documents function
  const loadDocuments = useCallback(async (query: PaginateQuery) => {
    setLoading(true);
    setError(null);

    try {
      console.log('Loading documents with query:', query);

      // Merge filter params with query
      const response = await documentService.getDocuments({
        page: query.page,
        limit: query.limit,
        search: query.search,
        sortBy: query.sortBy?.join(','),
        ...filterParams,
      });

      console.log('Documents loaded successfully:', response);
      setDocumentsData(response);
    } catch (err: unknown) {
      console.error('Error loading documents:', err);
      if (err instanceof Error) {
        setError(err.message || 'Failed to load documents');
      } else {
        setError('Failed to load documents');
      }
    } finally {
      setLoading(false);
    }
  }, [filterParams]);

  // Load documents on component mount
  useEffect(() => {
    loadDocuments({
      page: 1,
      limit: 10,
      search: '',
      sortBy: ['created_at:DESC'],
    });
  }, [loadDocuments]);

  // Format file size helper
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Format date helper
  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Get document type badge
  const getDocumentTypeBadge = (type: string | null | undefined): React.ReactElement => {
    const typeConfig: Record<string, { color: string; label: string }> = {
      'certificate_incorporation': { color: 'bg-blue-100 text-blue-800', label: 'Certificate' },
      'memorandum_association': { color: 'bg-green-100 text-green-800', label: 'Memorandum' },
      'shareholding_structure': { color: 'bg-purple-100 text-purple-800', label: 'Shareholding' },
      'business_plan': { color: 'bg-orange-100 text-orange-800', label: 'Business Plan' },
      'financial_statements': { color: 'bg-red-100 text-red-800', label: 'Financial' },
      'cv': { color: 'bg-yellow-100 text-yellow-800', label: 'CV' },
      'other': { color: 'bg-gray-100 text-gray-800', label: 'Other' },
    };

    const config = typeConfig[type?.toLowerCase() || 'other'] || typeConfig['other'];

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    );
  };

  // Download document handler
  const handleDownload = async (doc: Document) => {
    try {
      console.log('Downloading document:', doc.document_id);
      const blob = await documentService.downloadDocument(doc.document_id);

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = doc.file_name;
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (err: unknown) {
      console.error('Error downloading document:', err);
      setError('Failed to download document');
    }
  };

  // View document details handler
  const handleViewDocument = (doc: Document) => {
    setSelectedDocument(doc);
    setIsPreviewModalOpen(true);
  };

  // Close preview modal
  const handleClosePreview = () => {
    setIsPreviewModalOpen(false);
    setSelectedDocument(null);
  };

  // Build table columns dynamically based on props
  const buildColumns = () => {
    const columns: TableColumn[] = [
      {
        key: 'file_name',
        label: 'Document Name',
        sortable: true,
        render: (value: unknown, item: Document) => (
          <div className="flex items-center">
            <div className="flex-shrink-0 h-10 w-10">
              <div className="h-10 w-10 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                <i className="ri-file-text-line text-gray-500 dark:text-gray-400"></i>
              </div>
            </div>
            <div className="ml-4">
              <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                {value as string}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {item.mime_type}
              </div>
            </div>
          </div>
        ),
      },
      {
        key: 'document_type',
        label: 'Type',
        sortable: true,
        render: (value: unknown) => getDocumentTypeBadge(value as string),
      },
    ];

    // Add entity info column if enabled
    if (showEntityInfo) {
      columns.push({
        key: 'entity_type',
        label: 'Related To',
        sortable: true,
        render: (value: unknown, item: Document) => (
          <div>
            <div className="text-sm text-gray-900 dark:text-gray-100 capitalize">
              {value as string}
            </div>
            {item.entity_id && (
              <div className="text-xs text-gray-500 dark:text-gray-400">
                ID: {item.entity_id.substring(0, 8)}...
              </div>
            )}
          </div>
        ),
      });
    }

    // Add file size column
    columns.push({
      key: 'file_size',
      label: 'Size',
      sortable: true,
      render: (value: unknown) => (
        <span className="text-sm text-gray-900 dark:text-gray-100">
          {formatFileSize(value as number)}
        </span>
      ),
    });

    // Add required column if enabled
    if (showRequiredColumn) {
      columns.push({
        key: 'is_required',
        label: 'Required',
        sortable: true,
        render: (value: unknown) => (
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            value as boolean
              ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
              : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
          }`}>
            {value as boolean ? 'Required' : 'Optional'}
          </span>
        ),
      });
    }

    // Add creator info column if enabled
    if (showCreatorInfo) {
      columns.push({
        key: 'creator',
        label: 'Uploaded By',
        sortable: false,
        render: (value: unknown, item: Document) => (
          <div>
            {item.creator ? (
              <div className="text-sm text-gray-900 dark:text-gray-100">
                {item.creator.first_name} {item.creator.last_name}
              </div>
            ) : (
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Unknown
              </div>
            )}
          </div>
        ),
      });
    }

    // Add upload date column
    columns.push({
      key: 'created_at',
      label: 'Uploaded',
      sortable: true,
      render: (value: unknown) => (
        <span className="text-sm text-gray-900 dark:text-gray-100">
          {formatDate(value as string)}
        </span>
      ),
    });

    // Add actions column if enabled
    if (showActions) {
      columns.push({
        key: 'actions',
        label: 'Actions',
        render: (_: unknown, item: Document) => (
          <div className="flex items-center space-x-2">
            {customActions ? (
              customActions(item)
            ) : (
              <>
                <button
                  onClick={() => handleDownload(item)}
                  className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                  title="Download"
                >
                  <i className="ri-download-line"></i>
                </button>
                <button
                  onClick={() => handleViewDocument(item)}
                  className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                  title="View Details"
                >
                  <i className="ri-eye-line"></i>
                </button>
              </>
            )}
          </div>
        ),
      });
    }

    return columns;
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {title}
              </h1>
              <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                {subtitle}
              </p>
            </div>
            <div className="flex items-center space-x-3">
              {documentsData?.meta && (
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {documentsData.meta.totalItems} document{documentsData.meta.totalItems !== 1 ? 's' : ''}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <i className="ri-error-warning-line text-red-400"></i>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                Error loading documents
              </h3>
              <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                {error}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Documents Table */}
      <DataTable
        columns={buildColumns()}
        data={documentsData}
        loading={loading}
        onQueryChange={loadDocuments}
        searchPlaceholder={searchPlaceholder}
      />

      {/* Document Preview Modal */}
      <DocumentPreviewModal
        document={selectedDocument}
        isOpen={isPreviewModalOpen}
        onClose={handleClosePreview}
        onDownload={handleDownload}
      />
    </div>
  );
};

export default DocumentViewer;