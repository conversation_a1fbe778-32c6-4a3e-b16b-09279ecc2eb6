(()=>{var e={};e.id=537,e.ids=[537],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},43489:(e,r,t)=>{Promise.resolve().then(t.bind(t,88467))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56641:(e,r,t)=>{Promise.resolve().then(t.bind(t,92644))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85469:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>l});var s=t(65239),a=t(48088),i=t(88170),o=t.n(i),n=t(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(r,c);let l={children:["",{children:["customer",{children:["applications",{children:["apply",{children:["service-scope",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,88467)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\service-scope\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,68736)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\service-scope\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/customer/applications/apply/service-scope/page",pathname:"/customer/applications/apply/service-scope",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},88467:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\applications\\\\apply\\\\service-scope\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\service-scope\\page.tsx","default")},92644:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>x});var s=t(60687),a=t(43210),i=t(16189),o=t(94391),n=t(13128),c=t(63213),l=t(76377),d=t(78637);let p={async createScopeOfService(e){try{return{scope_of_service_id:`mock-${Date.now()}`,...e,created_at:new Date().toISOString(),updated_at:new Date().toISOString()}}catch(e){throw e}},async getScopeOfService(e){try{throw Error("Scope of service not found")}catch(e){throw e}},async getScopeOfServiceByApplication(e){try{return null}catch(e){return null}},async updateScopeOfService(e){try{return{scope_of_service_id:e.scope_of_service_id,application_id:"",nature_of_service:e.nature_of_service||"",premises:e.premises||"",transport_type:e.transport_type||"",customer_assistance:e.customer_assistance||"",updated_at:new Date().toISOString()}}catch(e){throw e}},async createOrUpdateScopeOfService(e,r){try{let t=await this.getScopeOfServiceByApplication(e);if(t)return await this.updateScopeOfService({scope_of_service_id:t.scope_of_service_id,...r});return await this.createScopeOfService({application_id:e,...r})}catch(e){throw e}}};var u=t(85787),m=t(34130);let x=()=>{let e=(0,i.useRouter)(),r=(0,i.useSearchParams)(),{isAuthenticated:t,loading:x}=(0,c.A)(),v=r.get("license_category_id"),h=r.get("application_id"),[f,g]=(0,a.useState)(!0),[y,_]=(0,a.useState)(!1),[b,j]=(0,a.useState)(null),[w,S]=(0,a.useState)(!1),[k,P]=(0,a.useState)({}),[N,q]=(0,a.useState)(null),[C,D]=(0,a.useState)(null),{saveFormData:O}=(0,u.u)({applicationId:h,stepName:"service-scope",autoLoad:!0}),[A,M]=(0,a.useState)({nature_of_service:"",premises:"",transport_type:"",customer_assistance:""}),B=(e,r)=>{M(t=>({...t,[e]:r})),S(!0),k[e]&&P(r=>{let t={...r};return delete t[e],t})};(0,a.useEffect)(()=>{(async()=>{if(h&&t&&!x)try{if(g(!0),j(null),q(null),v)try{let e=await m.TG.getLicenseCategory(v);e&&e.license_type&&D(e.license_type)}catch(e){}await d.k.getApplication(h);try{let e=await p.getScopeOfServiceByApplication(h);e&&M({nature_of_service:e.nature_of_service||"",premises:e.premises||"",transport_type:e.transport_type||"",customer_assistance:e.customer_assistance||""})}catch(e){q("Could not load existing scope of service data. You can still fill out the form.")}}catch(e){j("Failed to load application data")}finally{g(!1)}})()},[h,t,x]);let E=async()=>{if(!h)return P({save:"Application ID is required"}),!1;_(!0);try{let e={};if(A.nature_of_service.trim()||(e.nature_of_service="Nature of service is required"),A.premises.trim()||(e.premises="Premises information is required"),A.transport_type.trim()||(e.transport_type="Transport type is required"),A.customer_assistance.trim()||(e.customer_assistance="Customer assistance information is required"),Object.keys(e).length>0)return P(e),_(!1),!1;let r={nature_of_service:A.nature_of_service,premises:A.premises,transport_type:A.transport_type,customer_assistance:A.customer_assistance},t=[p.createOrUpdateScopeOfService(h,r),d.k.updateApplication(h,{current_step:6,progress_percentage:86})];try{await Promise.all(t)}catch(e){throw Error("Failed to save service scope information")}return S(!1),P({}),!0}catch(e){return P({save:"Failed to save service scope information. Please try again."}),!1}finally{_(!1)}},F=async()=>{await E()&&e.push(`/customer/applications/apply/documents?license_category_id=${v}&application_id=${h}`)};return x||f?(0,s.jsx)(o.A,{children:(0,s.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading service scope form..."})]})})}):b?(0,s.jsx)(o.A,{children:(0,s.jsx)("div",{className:"max-w-4xl mx-auto p-6",children:(0,s.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("i",{className:"ri-error-warning-line text-red-600 dark:text-red-400 text-2xl mr-4"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-red-800 dark:text-red-200",children:"Error Loading Form"}),(0,s.jsx)("p",{className:"text-red-700 dark:text-red-300 mt-1",children:b}),(0,s.jsxs)("button",{onClick:()=>e.back(),className:"mt-4 inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40",children:[(0,s.jsx)("i",{className:"ri-arrow-left-line mr-2"}),"Go Back"]})]})]})})})}):(0,s.jsx)(o.A,{children:(0,s.jsxs)(n.A,{onNext:F,onPrevious:()=>{e.push(`/customer/applications/apply/management?license_category_id=${v}&application_id=${h}`)},onSave:E,showNextButton:!0,showPreviousButton:!0,showSaveButton:!0,nextButtonText:"Continue to Documents",previousButtonText:"Back to Management Information",saveButtonText:"Save Service Scope Information",nextButtonDisabled:!1,isSaving:y,children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:h?"Edit Service Scope Information":"Service Scope Information"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:h?"Update your service scope and operational details below.":"Provide details about the scope of services you plan to offer."}),h&&!N&&!f&&(0,s.jsx)("div",{className:"mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg",children:(0,s.jsx)("p",{className:"text-sm text-green-700 dark:text-green-300",children:"✅ Editing existing application. Your saved service scope information has been loaded."})}),N&&(0,s.jsx)("div",{className:"mt-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg",children:(0,s.jsxs)("p",{className:"text-sm text-yellow-700 dark:text-yellow-300",children:["⚠️ ",N]})})]}),Object.keys(k).length>0&&(0,s.jsxs)("div",{className:"mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg",children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-red-800 dark:text-red-200 mb-2",children:"Please fix the following errors:"}),(0,s.jsx)("ul",{className:"text-sm text-red-700 dark:text-red-300 list-disc list-inside",children:Object.entries(k).map(([e,r])=>(0,s.jsx)("li",{children:r},e))})]}),(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6",children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"border-b border-gray-200 dark:border-gray-700 pb-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Service Scope Details"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:"Define the scope of services you plan to offer and your operational details."})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(l.fs,{label:"Nature of Service",value:A.nature_of_service,onChange:e=>B("nature_of_service",e.target.value),error:k.nature_of_service,rows:4,placeholder:"Describe the nature and type of services you plan to offer...",required:!0}),(0,s.jsx)(l.fs,{label:"Premises",value:A.premises,onChange:e=>B("premises",e.target.value),error:k.premises,rows:3,placeholder:"Describe your business premises and facilities...",required:!0}),(0,s.jsx)(l.fs,{label:"Transport Type",value:A.transport_type,onChange:e=>B("transport_type",e.target.value),error:k.transport_type,rows:3,placeholder:"Describe the types of transport and logistics you will use...",required:!0}),(0,s.jsx)(l.fs,{label:"Customer Assistance",value:A.customer_assistance,onChange:e=>B("customer_assistance",e.target.value),error:k.customer_assistance,rows:3,placeholder:"Describe how you will provide customer assistance and support...",required:!0})]})]})})]})})}},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,7498,1658,5814,7563,6893,6212,140,7033,2324],()=>t(85469));module.exports=s})();