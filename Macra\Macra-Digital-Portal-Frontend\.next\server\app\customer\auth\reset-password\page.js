(()=>{var e={};e.id=2644,e.ids=[2644],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},25195:(e,r,t)=>{Promise.resolve().then(t.bind(t,48010))},27910:e=>{"use strict";e.exports=require("stream")},28308:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\auth\\\\reset-password\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\auth\\reset-password\\page.tsx","default")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34548:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var s=t(43210);let a=s.forwardRef(function({title:e,titleId:r,...t},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},t),e?s.createElement("title",{id:r},e):null,s.createElement("path",{fillRule:"evenodd",d:"M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm13.36-1.814a.75.75 0 1 0-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 0 0-1.06 1.06l2.25 2.25a.75.75 0 0 0 1.14-.094l3.75-5.25Z",clipRule:"evenodd"}))})},38325:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>l});var s=t(65239),a=t(48088),o=t(88170),n=t.n(o),i=t(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(r,d);let l={children:["",{children:["customer",{children:["auth",{children:["reset-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,28308)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\auth\\reset-password\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,68736)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\auth\\reset-password\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/customer/auth/reset-password/page",pathname:"/customer/auth/reset-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},48010:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p});var s=t(60687),a=t(43210),o=t(16189),n=t(30474),i=t(85814),d=t.n(i),l=t(53862),c=t(55192),u=t(34548),m=t(63443);function p(){(0,o.useSearchParams)();let[e,r]=(0,a.useState)(""),[t,i]=(0,a.useState)(""),[p,x]=(0,a.useState)(""),[g,h]=(0,a.useState)(""),[f,w]=(0,a.useState)(""),[b,y]=(0,a.useState)(""),[v,j]=(0,a.useState)(""),[P,k]=(0,a.useState)(!0),[N,q]=(0,a.useState)(!0),C=(0,o.useRouter)(),M=e=>/.{8,}/.test(e)?/[A-Z]/.test(e)?/[a-z]/.test(e)?/\d/.test(e)?/[@$!%*?&#^()_+\-=\[\]{};':"\\|,.<>\/?]/.test(e)?"":"Password must contain at least one special character.":"Password must contain at least one number.":"Password must contain at least one lowercase letter.":"Password must contain at least one uppercase letter.":"Password must be at least 8 characters.",A=async r=>{if(r.preventDefault(),y(""),j(""),g!==f)return void y("Passwords do not match.");let s=M(g);if(s)return void y(s);if(!e||!t||!p){y("Missing required reset data. Redirecting..."),setTimeout(()=>C.push("/auth/forgot-password"),2e3);return}try{await m.y.resetPassword({user_id:e,code:t,unique:p,new_password:g}),j("Password reset successful. Redirecting to login..."),sessionStorage.clear(),setTimeout(()=>C.push("/customer/auth/login"),2e3)}catch(t){let e,r="string"==typeof(e="object"==typeof t&&null!==t?t?.response?.data?.message||t?.message||"Failed to reset password.":"string"==typeof t?t:"Failed to reset password.")?e:Array.isArray(e)?e.join(", "):"An error occurred.";y(r),r.toLowerCase().includes("password")&&(h(""),w(""))}};if(P)return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,s.jsx)(l.A,{className:"w-16 h-16 animate-spin text-gray-500 dark:text-gray-300"})});let R=b.toLowerCase().includes("password");return b&&!R?(0,s.jsx)("div",{className:"min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900",children:(0,s.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md text-center",children:[(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsx)(n.default,{src:"/images/macra-logo.png",alt:"MACRA Logo",width:50,height:50,className:"h-16 w-auto place-content-center"})}),(0,s.jsx)("h2",{className:"mt-6 text-2xl font-bold text-red-600 dark:text-red-400",children:b||"Invalid Link!"})," ",(0,s.jsx)("br",{}),(0,s.jsx)("p",{className:"mt-2 text-sm p-4 text-gray-600 dark:text-gray-400",children:"Please request a new reset email to continue."}),(0,s.jsx)("div",{className:"mt-4",children:(0,s.jsx)(d(),{href:"/customer/auth/forgot-password",className:"text-red-600 dark:text-red-400 underline text-sm",children:"Request Reset"})})]})}):(0,s.jsxs)("div",{className:"min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900",children:[(0,s.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsx)(n.default,{src:"/images/macra-logo.png",alt:"MACRA Logo",width:50,height:50,className:"h-16 w-auto"})}),(0,s.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-gray-100",children:"Set New Password"})]}),(0,s.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",id:"submitForm",children:(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[b&&R&&(0,s.jsxs)("div",{className:"mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-md flex items-center",children:[(0,s.jsx)(c.A,{className:"w-5 h-5 mr-2"}),b]}),v?(0,s.jsxs)("div",{className:"mb-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-600 dark:text-green-400 px-4 py-3 rounded-md flex items-center",children:[(0,s.jsx)(u.A,{className:"w-5 h-5 mr-2"}),v]}):(0,s.jsxs)("form",{className:"space-y-6",onSubmit:A,children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"newPassword",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"New Password"}),(0,s.jsx)("input",{id:"newPassword",name:"newPassword",type:"password",required:!0,value:g,onChange:e=>h(e.target.value),className:"mt-1 block w-full px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Confirm Password"}),(0,s.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:"password",required:!0,value:f,onChange:e=>w(e.target.value),className:"mt-1 block w-full px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors"})]}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",className:"w-full flex justify-center py-3 px-4 border-2 border-transparent rounded-md shadow-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-all disabled:opacity-50 disabled:cursor-not-allowed",disabled:N||P,children:"Reset Password"})})]})]})})]})}},53862:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var s=t(43210);let a=s.forwardRef(function({title:e,titleId:r,...t},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},t),e?s.createElement("title",{id:r},e):null,s.createElement("path",{fillRule:"evenodd",d:"M4.755 10.059a7.5 7.5 0 0 1 12.548-3.364l1.903 1.903h-3.183a.75.75 0 1 0 0 1.5h4.992a.75.75 0 0 0 .75-.75V4.356a.75.75 0 0 0-1.5 0v3.18l-1.9-1.9A9 9 0 0 0 3.306 9.67a.75.75 0 1 0 1.45.388Zm15.408 3.352a.75.75 0 0 0-.919.53 7.5 7.5 0 0 1-12.548 3.364l-1.902-1.903h3.183a.75.75 0 0 0 0-1.5H2.984a.75.75 0 0 0-.75.75v4.992a.75.75 0 0 0 1.5 0v-3.18l1.9 1.9a9 9 0 0 0 15.059-*********** 0 0 0-.53-.918Z",clipRule:"evenodd"}))})},55192:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var s=t(43210);let a=s.forwardRef(function({title:e,titleId:r,...t},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},t),e?s.createElement("title",{id:r},e):null,s.createElement("path",{fillRule:"evenodd",d:"M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm-1.72 6.97a.75.75 0 1 0-1.06 1.06L10.94 12l-1.72 1.72a.75.75 0 1 0 1.06 1.06L12 13.06l1.72 1.72a.75.75 0 1 0 1.06-1.06L13.06 12l1.72-1.72a.75.75 0 1 0-1.06-1.06L12 10.94l-1.72-1.72Z",clipRule:"evenodd"}))})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68736:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o,metadata:()=>a});var s=t(37413);let a={title:"Customer Dashboard - Digital Portal",description:"Customer portal for managing licenses and applications"};function o({children:e}){return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50",children:e})}},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"256x256",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},88243:(e,r,t)=>{Promise.resolve().then(t.bind(t,28308))},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,7498,1658,5814,2335],()=>t(38325));module.exports=s})();