(()=>{var e={};e.id=4409,e.ids=[4409],e.modules={1611:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\applications\\\\apply\\\\documents\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\documents\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11097:(e,t,r)=>{Promise.resolve().then(r.bind(r,1611))},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20085:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var a=r(60687),s=r(43210),i=r(16189),n=r(94391),c=r(13128),o=r(63213),l=r(24325),d=r(78637),u=r(64826),m=r(45125);let p=()=>{let e=(0,i.useRouter)(),t=(0,i.useSearchParams)(),{isAuthenticated:r,loading:p}=(0,o.A)(),x=t.get("license_category_id"),g=t.get("application_id"),[y,h]=(0,s.useState)(!0),[f,_]=(0,s.useState)(!1),[b,j]=(0,s.useState)(null),[v,w]=(0,s.useState)(null),[k,N]=(0,s.useState)({}),[D,C]=(0,s.useState)([]),[q,P]=(0,s.useState)([]),[A,L]=(0,s.useState)({}),[S,$]=(0,s.useState)({});(0,s.useEffect)(()=>{(async()=>{if(g&&x&&r&&!p)try{h(!0),j(null),w(null);let e=[];try{let t=await m._.getLicenseCategoryDocumentsByCategory(x);e=Array.isArray(t)?t:[],C(e)}catch(t){if(t.response?.status===404)e=[],C([]),w("No required documents configured for this license category.");else{let t=[{license_category_document_id:"default_1",license_category_id:x,name:"Certificate of Incorporation",is_required:!0},{license_category_document_id:"default_2",license_category_id:x,name:"Business Plan",is_required:!0},{license_category_document_id:"default_3",license_category_id:x,name:"Financial Statements",is_required:!0},{license_category_document_id:"default_4",license_category_id:x,name:"Tax Clearance Certificate",is_required:!1}];e=t,C(t),w("Using default document requirements. Backend service not available.")}}try{let t=await u.D.getDocumentsByApplication(g),r=Array.isArray(t)?t:[];if(P(r),e.length>0){let t=e.map(e=>e.name.toLowerCase().replace(/\s+/g,"_")),a=r.map(e=>e.document_type);t.filter(e=>!a.includes(e)),a.filter(e=>!t.includes(e))}r.length}catch(e){P([]),e.response?.status===401?w("Authentication required. Please log in to view documents."):e.response?.status===404||w("Could not load existing documents from server. You can still upload new documents.")}}catch(e){j("Failed to load documents data")}finally{h(!1)}})()},[g,x,r,p]);let E=(e,t)=>{L(r=>{let a={...r};return t?a[e]=t:delete a[e],a}),k[e]&&N(t=>{let r={...t};return delete r[e],r})},B=async(e,t)=>{try{$(t=>({...t,[e]:0}));let r=Array.isArray(D)?D.find(t=>t.name.toLowerCase().replace(/\s+/g,"_")===e):void 0,a={document_type:r?u.D.mapDocumentNameToType(r.name):e,entity_type:"application",entity_id:g,is_required:r?.is_required||!1};try{let r=await u.D.uploadDocument(t,a);$(t=>({...t,[e]:100})),P(e=>[...e,r.document])}catch(s){$(t=>({...t,[e]:100}));let r={document_id:`mock_${Date.now()}_${e}`,document_type:e,file_name:t.name,entity_type:"application",entity_id:g,file_path:`mock_path/${t.name}`,file_size:t.size,mime_type:t.type,is_required:a.is_required,created_at:new Date().toISOString()};P(e=>[...e,r])}return L(t=>{let r={...t};return delete r[e],r}),!0}catch(t){return N(t=>({...t,[e]:"Failed to process document. Please try again."})),$(t=>{let r={...t};return delete r[e],r}),!1}},z=async()=>{if(!g)return N({save:"Application ID is required"}),!1;_(!0);try{let e={},t=Array.isArray(D)?D.filter(e=>e.is_required).map(e=>e.name.toLowerCase().replace(/\s+/g,"_")):[],r=q.map(e=>e.document_type),a=Object.keys(A);for(let s of t)if(!r.includes(s)&&!a.includes(s)){let t=Array.isArray(D)&&D.find(e=>e.name.toLowerCase().replace(/\s+/g,"_")===s)?.name||s;e[s]=`${t} is required`}if(Object.keys(e).length>0)return N(e),_(!1),!1;if(Object.keys(A).length>0){let e=Object.entries(A).map(([e,t])=>B(e,t));if(!(await Promise.all(e)).every(e=>e))throw Error("Some documents failed to upload")}try{await d.k.updateApplication(g,{current_step:7,progress_percentage:86})}catch(e){}return N({}),!0}catch(e){return N({save:"Failed to process documents. Please try again."}),!1}finally{_(!1)}},F=async()=>{await z()&&e.push(`/customer/applications/apply/submit?license_category_id=${x}&application_id=${g}`)},U=async e=>{try{if(e.startsWith("mock_"))P(t=>t.filter(t=>t.document_id!==e));else try{await u.D.deleteDocument(e),P(t=>t.filter(t=>t.document_id!==e))}catch(t){P(t=>t.filter(t=>t.document_id!==e))}}catch(e){N(e=>({...e,remove:"Failed to remove document. Please try again."}))}},M=(()=>{let e=new Map;return Array.isArray(D)&&D.forEach(t=>{let r=t.name.toLowerCase().replace(/\s+/g,"_");e.set(r,{type:"required",requiredDoc:t,uploadedDoc:null,docType:r,isRequired:t.is_required,isUploaded:!1})}),q.forEach(t=>{let r=t.document_type,a=e.get(r);a?e.set(r,{...a,uploadedDoc:t,isUploaded:!0}):e.set(r,{type:"uploaded",requiredDoc:null,uploadedDoc:t,docType:r,isRequired:!1,isUploaded:!0})}),Array.from(e.values())})(),R=async e=>{try{let t=await u.D.previewDocument(e.document_id),r=URL.createObjectURL(t);window.open(r,"_blank")}catch(e){alert("Failed to preview document")}},O=async e=>{try{let t=await u.D.downloadDocument(e.document_id),r=URL.createObjectURL(t),a=window.document.createElement("a");a.href=r,a.download=e.file_name,window.document.body.appendChild(a),a.click(),a.remove(),URL.revokeObjectURL(r)}catch(e){alert("Failed to download document")}};return p||y?(0,a.jsx)(n.A,{children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading required documents..."})]})})}):b?(0,a.jsx)(n.A,{children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto p-6",children:(0,a.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"ri-error-warning-line text-red-600 dark:text-red-400 text-2xl mr-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-red-800 dark:text-red-200",children:"Error Loading Documents"}),(0,a.jsx)("p",{className:"text-red-700 dark:text-red-300 mt-1",children:b}),(0,a.jsxs)("button",{onClick:()=>e.back(),className:"mt-4 inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40",children:[(0,a.jsx)("i",{className:"ri-arrow-left-line mr-2"}),"Go Back"]})]})]})})})}):(0,a.jsx)(n.A,{children:(0,a.jsxs)(c.A,{onNext:F,onPrevious:()=>{e.push(`/customer/applications/apply/legal-history?license_category_id=${x}&application_id=${g}`)},onSave:z,showNextButton:!0,showPreviousButton:!0,showSaveButton:!0,nextButtonText:"Continue to Submit",previousButtonText:"Back to Legal History",saveButtonText:"Upload Documents",nextButtonDisabled:!1,isSaving:f,children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Required Documents"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:"Upload all required documents for your license application."}),g&&(0,a.jsx)("div",{className:"mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:[(0,a.jsx)("i",{className:"ri-file-upload-line mr-1"}),"Application ID: ",g]})}),v&&(0,a.jsx)("div",{className:"mt-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-yellow-700 dark:text-yellow-300",children:["⚠️ ",v]})})]}),Object.keys(k).length>0&&(0,a.jsxs)("div",{className:"mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-red-800 dark:text-red-200 mb-2",children:"Please fix the following errors:"}),(0,a.jsx)("ul",{className:"text-sm text-red-700 dark:text-red-300 list-disc list-inside",children:Object.entries(k).map(([e,t])=>(0,a.jsx)("li",{children:t},e))})]}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6",children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"border-b border-gray-200 dark:border-gray-700 pb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Document Upload"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:"Please upload all required documents. Accepted formats: PDF, DOC, DOCX, JPG, PNG (Max 10MB each)"})]}),(0,a.jsxs)("div",{children:[Array.isArray(D)&&D.length>0?(0,a.jsx)("div",{className:"space-y-6",children:M.map(e=>{let{docType:t,requiredDoc:r,uploadedDoc:s,isRequired:i,isUploaded:n}=e,c=void 0!==S[t];return(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:[r.name,r.is_required&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),n&&s&&(0,a.jsxs)("p",{className:"text-xs text-green-600 dark:text-green-400 mt-1",children:["✅ Uploaded: ",s.file_name]})]}),n&&s&&(0,a.jsxs)("button",{onClick:()=>U(s.document_id),className:"text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 text-sm",children:[(0,a.jsx)("i",{className:"ri-delete-bin-line mr-1"}),"Remove"]})]}),!n&&(0,a.jsx)(l.A,{id:`document-${t}`,label:"",accept:".pdf,.doc,.docx,.jpg,.jpeg,.png",maxSize:10,required:r.is_required,value:A[t]||null,onChange:e=>E(t,e),description:`Upload ${r.name.toLowerCase()}`}),c&&(0,a.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-primary h-2 rounded-full transition-all duration-300",style:{width:`${S[t]}%`}})}),k[t]&&(0,a.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:k[t]})]},r.license_category_document_id)})}):(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("i",{className:"ri-attachment-line text-4xl text-gray-400 dark:text-gray-500 mb-4"}),(0,a.jsx)("h4",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"No Required Documents"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"No specific documents are required for this license category. You can upload any supporting documents or attachments below."}),(0,a.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,a.jsx)(l.A,{id:"general-attachment",label:"Upload Supporting Documents",accept:".pdf,.doc,.docx,.jpg,.jpeg,.png,.txt",onChange:e=>{e&&L(t=>({...t,general_attachment:e}))},className:"mb-4"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Supported formats: PDF, DOC, DOCX, JPG, PNG, TXT (Max 10MB)"})]})]}),q.length>0&&(0,a.jsxs)("div",{className:"mt-8",children:[(0,a.jsxs)("h4",{className:"text-md font-medium text-gray-900 dark:text-gray-100 mb-4",children:["Uploaded Documents (",q.length,")"]}),(0,a.jsx)("div",{className:"space-y-3",children:q.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("i",{className:"ri-file-line text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:e.file_name}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:[e.document_type," • Uploaded ",e.created_at?new Date(e.created_at).toLocaleDateString():"Unknown date"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[u.D.isPreviewable(e.file_name)&&(0,a.jsxs)("button",{onClick:()=>R(e),className:"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 text-sm",children:[(0,a.jsx)("i",{className:"ri-eye-line mr-1"}),"Preview"]}),(0,a.jsxs)("button",{onClick:()=>O(e),className:"text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200 text-sm",children:[(0,a.jsx)("i",{className:"ri-download-line mr-1"}),"Download"]}),(0,a.jsxs)("button",{onClick:()=>U(e.document_id),className:"text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 text-sm",children:[(0,a.jsx)("i",{className:"ri-delete-bin-line mr-1"}),"Remove"]})]})]},e.document_id))})]})]})]})}),Array.isArray(D)&&D.length>0&&(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Upload Summary"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"Total Required:"}),(0,a.jsx)("span",{className:"ml-2 font-medium text-gray-900 dark:text-gray-100",children:D.filter(e=>e.is_required).length})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"Uploaded:"}),(0,a.jsx)("span",{className:"ml-2 font-medium text-green-600 dark:text-green-400",children:q.length})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"Pending:"}),(0,a.jsx)("span",{className:"ml-2 font-medium text-orange-600 dark:text-orange-400",children:Object.keys(A).length})]})]})]})]})})}},21820:e=>{"use strict";e.exports=require("os")},24249:(e,t,r)=>{Promise.resolve().then(r.bind(r,20085))},24325:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(60687),s=r(43210);let i=({id:e,label:t,accept:r=".pdf",maxSize:i=10,required:n=!1,value:c,onChange:o,description:l,className:d=""})=>{let u=(0,s.useRef)(null);return(0,a.jsxs)("div",{className:d,children:[(0,a.jsxs)("label",{htmlFor:e,className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[t," ",n&&(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsxs)("div",{onClick:()=>{u.current?.click()},className:"border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center cursor-pointer hover:border-primary hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors",children:[(0,a.jsx)("input",{ref:u,type:"file",accept:r,onChange:e=>{let t=e.target.files?.[0]||null;if(t){if(t.size>1024*i*1024)return void alert(`File size must be less than ${i}MB`);if(r&&!r.split(",").some(e=>t.name.toLowerCase().endsWith(e.trim().replace("*",""))))return void alert(`File type must be: ${r}`)}o(t)},className:"hidden",id:e,required:n}),c?(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"flex items-center justify-center",children:(0,a.jsx)("i",{className:"ri-file-text-line text-3xl text-green-500"})}),(0,a.jsx)("p",{className:"text-sm font-medium text-green-600 dark:text-green-400",children:c.name}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:[(c.size/1024/1024).toFixed(2)," MB"]}),(0,a.jsxs)("button",{type:"button",onClick:e=>{e.stopPropagation(),o(null),u.current&&(u.current.value="")},className:"inline-flex items-center px-3 py-1 bg-red-100 text-red-700 hover:bg-red-200 rounded-md text-xs font-medium transition-colors",children:[(0,a.jsx)("i",{className:"ri-delete-bin-line mr-1"}),"Remove"]})]}):(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"flex items-center justify-center",children:(0,a.jsx)("i",{className:"ri-upload-cloud-2-line text-3xl text-gray-400"})}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Click to upload ",t.toLowerCase()]}),l&&(0,a.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-500",children:l}),(0,a.jsxs)("div",{className:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors",children:[(0,a.jsx)("i",{className:"ri-folder-upload-line mr-2"}),"Choose File"]})]})]}),l&&!c&&(0,a.jsx)("p",{className:"mt-2 text-xs text-gray-500 dark:text-gray-400",children:l})]})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},45125:(e,t,r)=>{"use strict";r.d(t,{_:()=>i});var a=r(51278),s=r(72901);let i={async getLicenseCategoryDocuments(e={}){let t=new URLSearchParams;e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(([e,r])=>{Array.isArray(r)?r.forEach(r=>t.append(`filter.${e}`,r)):t.set(`filter.${e}`,r)});let r=await s.uE.get(`/license-category-documents?${t.toString()}`);return(0,a.zp)(r)},async getLicenseCategoryDocument(e){let t=await s.uE.get(`/license-category-documents/${e}`);return(0,a.zp)(t)},async getLicenseCategoryDocumentsByCategory(e){let t=await s.uE.get(`/license-category-documents/by-license-category/${e}`);return(0,a.zp)(t).data},async createLicenseCategoryDocument(e){let t=await s.uE.post("/license-category-documents",e);return(0,a.zp)(t)},async updateLicenseCategoryDocument(e,t){let r=await s.uE.patch(`/license-category-documents/${e}`,t);return(0,a.zp)(r)},async deleteLicenseCategoryDocument(e){let t=await s.uE.delete(`/license-category-documents/${e}`);return(0,a.zp)(t)},async getAllLicenseCategoryDocuments(){let e=await this.getLicenseCategoryDocuments({limit:1e3});return(0,a.zp)(e)}}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64826:(e,t,r)=>{"use strict";r.d(t,{D:()=>i});var a=r(51278),s=r(72901);let i={async getDocuments(e){try{let t=new URLSearchParams;e?.page&&t.append("page",e.page.toString()),e?.limit&&t.append("limit",e.limit.toString()),e?.search&&t.append("search",e.search),e?.sortBy&&t.append("sortBy",e.sortBy);let r=await s.uE.get(`/documents?${t.toString()}`);return(0,a.zp)(r)}catch(e){throw e}},async getDocumentsByEntity(e,t){try{let r=await s.uE.get(`/documents/entity/${e}/${t}`);return(0,a.zp)(r)}catch(e){throw e}},async getDocumentsByApplication(e){try{let t=await s.uE.get(`/documents/by-application/${e}`);return(0,a.zp)(t)}catch(e){throw e}},async getRequiredDocumentsForLicenseCategory(e){try{let t=await s.uE.get(`/license-category-documents/category/${e}`);return(0,a.zp)(t)}catch(e){throw e}},async uploadDocument(e,t){try{let r={document_type:t.document_type,file_name:e.name,entity_type:t.entity_type,entity_id:t.entity_id,file_path:`uploads/${t.entity_type}/${t.entity_id}/${e.name}`,file_size:e.size,mime_type:e.type,is_required:t.is_required||!1},i=await s.uE.post("/documents",r);return{document:(0,a.zp)(i),message:"Document record created successfully"}}catch(e){throw e}},async createDocument(e){try{let t=await s.uE.post("/documents",e);return(0,a.zp)(t)}catch(e){throw e}},async updateDocument(e,t){try{let r=await s.uE.put(`/documents/${e}`,t);return(0,a.zp)(r)}catch(e){throw e}},async deleteDocument(e){try{await s.uE.delete(`/documents/${e}`)}catch(e){throw e}},async getDocument(e){try{let t=await s.uE.get(`/documents/${e}`);return(0,a.zp)(t)}catch(e){throw e}},async downloadDocument(e){try{return(await s.uE.get(`/documents/${e}/download`,{responseType:"blob"})).data}catch(e){throw e}},async previewDocument(e){try{return(await s.uE.get(`/documents/${e}/preview`,{responseType:"blob"})).data}catch(e){throw e}},isPreviewable:e=>["application/pdf","image/jpeg","image/jpg","image/png","image/gif","image/webp","text/plain","text/html","text/css","text/javascript","application/json"].includes(e.toLowerCase()),async checkRequiredDocuments(e,t){try{let r=await this.getRequiredDocumentsForLicenseCategory(t),a=await this.getDocumentsByApplication(e),s=a.map(e=>e.document_type),i=r.filter(e=>e.is_required&&!s.includes(e.name.toLowerCase().replace(/\s+/g,"_")));return{allUploaded:0===i.length,missing:i,uploaded:a}}catch(e){throw e}},getDocumentTypes:()=>["certificate_incorporation","memorandum_association","shareholding_structure","business_plan","financial_statements","technical_proposal","coverage_plan","network_diagram","equipment_specifications","insurance_certificate","tax_clearance","audited_accounts","bank_statement","cv_document","other"],formatDocumentType:e=>e.split("_").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "),mapDocumentNameToType(e){let t={"Certificate of Incorporation":"certificate_incorporation","Memorandum of Association":"memorandum_association","Shareholding Structure":"shareholding_structure","Business Plan":"business_plan","Financial Statements":"financial_statements","Technical Proposal":"technical_proposal","Coverage Plan":"coverage_plan","Network Diagram":"network_diagram","Equipment Specifications":"equipment_specifications","Insurance Certificate":"insurance_certificate","Tax Clearance Certificate":"tax_clearance","Tax Clearance":"tax_clearance","Audited Accounts":"audited_accounts","Bank Statement":"bank_statement","CV Document":"cv_document",Other:"other"};if(t[e])return t[e];let r=e.toLowerCase();for(let[e,a]of Object.entries(t))if(e.toLowerCase()===r)return a;return e.toLowerCase().replace(/\s+/g,"_")},validateFile:(e,t=10,r=[])=>e.size>1024*t*1024?{isValid:!1,error:`File size must be less than ${t}MB`}:r.length>0&&!r.includes(e.type)?{isValid:!1,error:`File type not allowed. Allowed types: ${r.join(", ")}`}:{isValid:!0}}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},87753:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>l});var a=r(65239),s=r(48088),i=r(88170),n=r.n(i),c=r(30893),o={};for(let e in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>c[e]);r.d(t,o);let l={children:["",{children:["customer",{children:["applications",{children:["apply",{children:["documents",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1611)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\documents\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,68736)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\documents\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/customer/applications/apply/documents/page",pathname:"/customer/applications/apply/documents",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,7498,1658,5814,7563,6893,6212,140],()=>r(87753));module.exports=a})();