(()=>{var e={};e.id=4409,e.ids=[4409],e.modules={1611:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\applications\\\\apply\\\\documents\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\documents\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11097:(e,t,a)=>{Promise.resolve().then(a.bind(a,1611))},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20085:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>g});var r=a(60687),s=a(43210),i=a(16189),n=a(94391),c=a(13128),o=a(63213),l=a(24325),d=a(78637),u=a(64826),p=a(45125),m=a(25890);let g=()=>{let e=(0,i.useSearchParams)(),{isAuthenticated:t,loading:a}=(0,o.A)(),g=e.get("license_category_id"),y=e.get("application_id"),[x,h]=(0,s.useState)(!0),[f,_]=(0,s.useState)(!1),[w,b]=(0,s.useState)(null),[v,j]=(0,s.useState)(null),[k,N]=(0,s.useState)({}),[D,C]=(0,s.useState)([]),[P,A]=(0,s.useState)([]),[S,q]=(0,s.useState)({}),[E,$]=(0,s.useState)({}),{handleNext:z,handlePrevious:B,nextStep:L}=(0,m.f)({currentStepRoute:"documents",licenseCategoryId:g,applicationId:y});(0,s.useEffect)(()=>{(async()=>{if(y&&g&&t&&!a)try{h(!0),b(null),j(null);let e=[];try{let t=await p._.getLicenseCategoryDocumentsByCategory(g);e=Array.isArray(t)?t:[],C(e)}catch(t){if(t.response?.status===404)e=[],C([]),j("No required documents configured for this license category.");else{let t=[{license_category_document_id:"default_1",license_category_id:g,name:"Certificate of Incorporation",is_required:!0},{license_category_document_id:"default_2",license_category_id:g,name:"Business Plan",is_required:!0},{license_category_document_id:"default_3",license_category_id:g,name:"Financial Statements",is_required:!0},{license_category_document_id:"default_4",license_category_id:g,name:"Tax Clearance Certificate",is_required:!1}];e=t,C(t),j("Using default document requirements. Backend service not available.")}}try{let t=await u.D.getDocumentsByApplication(y),a=Array.isArray(t)?t:[];if(A(a),e.length>0){let t=e.map(e=>e.name.toLowerCase().replace(/\s+/g,"_")),r=a.map(e=>e.document_type);t.filter(e=>!r.includes(e)),r.filter(e=>!t.includes(e))}a.length}catch(e){A([]),e.response?.status===401?j("Authentication required. Please log in to view documents."):e.response?.status===404||j("Could not load existing documents from server. You can still upload new documents.")}}catch(e){b("Failed to load documents data")}finally{h(!1)}})()},[y,g,t,a]);let M=(e,t)=>{q(a=>{let r={...a};return t?r[e]=t:delete r[e],r}),k[e]&&N(t=>{let a={...t};return delete a[e],a})},R=async(e,t)=>{try{$(t=>({...t,[e]:0}));let a=Array.isArray(D)?D.find(t=>t.name.toLowerCase().replace(/\s+/g,"_")===e):void 0,r={document_type:a?u.D.mapDocumentNameToType(a.name):e,entity_type:"application",entity_id:y,is_required:a?.is_required||!1};try{let a=await u.D.uploadDocument(t,r);$(t=>({...t,[e]:100})),A(e=>[...e,a.document])}catch(s){$(t=>({...t,[e]:100}));let a={document_id:`mock_${Date.now()}_${e}`,document_type:e,file_name:t.name,entity_type:"application",entity_id:y,file_path:`mock_path/${t.name}`,file_size:t.size,mime_type:t.type,is_required:r.is_required,created_at:new Date().toISOString()};A(e=>[...e,a])}return q(t=>{let a={...t};return delete a[e],a}),!0}catch(t){return N(t=>({...t,[e]:"Failed to process document. Please try again."})),$(t=>{let a={...t};return delete a[e],a}),!1}},U=async()=>{if(!y)return N({save:"Application ID is required"}),!1;_(!0);try{if(N({}),Object.keys(S).length>0){let e=Object.entries(S).map(([e,t])=>R(e,t));if(!(await Promise.all(e)).every(e=>e))throw Error("Some documents failed to upload")}try{await d.k.updateApplication(y,{current_step:7,progress_percentage:86})}catch(e){}return N({}),!0}catch(e){return N({save:"Failed to process documents. Please try again."}),!1}finally{_(!1)}},F=async()=>{await z(U)},T=async e=>{try{if(e.startsWith("mock_"))A(t=>t.filter(t=>t.document_id!==e));else try{await u.D.deleteDocument(e),A(t=>t.filter(t=>t.document_id!==e))}catch(t){A(t=>t.filter(t=>t.document_id!==e))}}catch(e){N(e=>({...e,remove:"Failed to remove document. Please try again."}))}},O=(()=>{let e=new Map;return Array.isArray(D)&&D.forEach(t=>{let a=t.name.toLowerCase().replace(/\s+/g,"_");e.set(a,{type:"required",requiredDoc:t,uploadedDoc:null,docType:a,isRequired:t.is_required,isUploaded:!1})}),P.forEach(t=>{let a=t.document_type,r=e.get(a);r?e.set(a,{...r,uploadedDoc:t,isUploaded:!0}):e.set(a,{type:"uploaded",requiredDoc:null,uploadedDoc:t,docType:a,isRequired:!1,isUploaded:!0})}),Array.from(e.values())})(),I=async e=>{try{let t=await u.D.previewDocument(e.document_id),a=URL.createObjectURL(t);window.open(a,"_blank")}catch(e){alert("Failed to preview document")}},G=async e=>{try{let t=await u.D.downloadDocument(e.document_id),a=URL.createObjectURL(t),r=window.document.createElement("a");r.href=a,r.download=e.file_name,window.document.body.appendChild(r),r.click(),r.remove(),URL.revokeObjectURL(a)}catch(e){alert("Failed to download document")}};return a||x?(0,r.jsx)(n.A,{children:(0,r.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading required documents..."})]})})}):w?(0,r.jsx)(n.A,{children:(0,r.jsx)("div",{className:"max-w-4xl mx-auto p-6",children:(0,r.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"ri-error-warning-line text-red-600 dark:text-red-400 text-2xl mr-4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-red-800 dark:text-red-200",children:"Error Loading Documents"}),(0,r.jsx)("p",{className:"text-red-700 dark:text-red-300 mt-1",children:w}),(0,r.jsxs)("button",{onClick:()=>B(),className:"mt-4 inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40",children:[(0,r.jsx)("i",{className:"ri-arrow-left-line mr-2"}),"Go Back"]})]})]})})})}):(0,r.jsx)(n.A,{children:(0,r.jsxs)(c.A,{onNext:F,onPrevious:()=>{B()},onSave:U,showNextButton:!0,showPreviousButton:!0,showSaveButton:!0,nextButtonText:L?`Continue to ${L.name}`:"Continue",previousButtonText:"Back to Previous Step",saveButtonText:"Save & Continue",nextButtonDisabled:!1,isSaving:f,children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Required Documents"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:"Upload documents for your license application. You can upload documents gradually and return to this page later."}),y&&(0,r.jsx)("div",{className:"mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:[(0,r.jsx)("i",{className:"ri-file-upload-line mr-1"}),"Application ID: ",y]})}),v&&(0,r.jsx)("div",{className:"mt-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-yellow-700 dark:text-yellow-300",children:["⚠️ ",v]})})]}),Object.keys(k).length>0&&(0,r.jsxs)("div",{className:"mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-red-800 dark:text-red-200 mb-2",children:"Please fix the following errors:"}),(0,r.jsx)("ul",{className:"text-sm text-red-700 dark:text-red-300 list-disc list-inside",children:Object.entries(k).map(([e,t])=>(0,r.jsx)("li",{children:t},e))})]}),(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6",children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"border-b border-gray-200 dark:border-gray-700 pb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Document Upload"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:"Upload your documents as they become available. Accepted formats: PDF, DOC, DOCX, JPG, PNG (Max 10MB each)"})]}),(0,r.jsxs)("div",{children:[Array.isArray(D)&&D.length>0?(0,r.jsx)("div",{className:"space-y-4",children:O.map(e=>{let{docType:t,requiredDoc:a,uploadedDoc:s,isRequired:i,isUploaded:n}=e,c=void 0!==E[t];return(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:[a.name,a.is_required&&(0,r.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),n&&s&&(0,r.jsxs)("p",{className:"text-xs text-green-600 dark:text-green-400 mt-1",children:["✅ Uploaded: ",s.file_name]})]}),n&&s&&(0,r.jsxs)("button",{onClick:()=>T(s.document_id),className:"text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 text-sm",children:[(0,r.jsx)("i",{className:"ri-delete-bin-line mr-1"}),"Remove"]})]}),!n&&(0,r.jsx)(l.A,{id:`document-${t}`,label:"",accept:".pdf,.doc,.docx,.jpg,.jpeg,.png",required:!1,maxSize:10,value:S[t]||null,onChange:e=>M(t,e),description:`Upload ${a.name.toLowerCase()}`}),c&&(0,r.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-primary h-2 rounded-full transition-all duration-300",style:{width:`${E[t]}%`}})}),k[t]&&(0,r.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:k[t]})]},a.license_category_document_id)})}):(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("i",{className:"ri-attachment-line text-4xl text-gray-400 dark:text-gray-500 mb-4"}),(0,r.jsx)("h4",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"No Required Documents"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"No specific documents are required for this license category. You can upload any supporting documents or attachments below."}),(0,r.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,r.jsx)(l.A,{id:"general-attachment",label:"Upload Supporting Documents",accept:".pdf,.doc,.docx,.jpg,.jpeg,.png,.txt",onChange:e=>{e&&q(t=>({...t,general_attachment:e}))},className:"mb-4"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Supported formats: PDF, DOC, DOCX, JPG, PNG, TXT (Max 10MB)"})]})]}),P.length>0&&(0,r.jsxs)("div",{className:"mt-8",children:[(0,r.jsxs)("h4",{className:"text-md font-medium text-gray-900 dark:text-gray-100 mb-4",children:["Uploaded Documents (",P.length,")"]}),(0,r.jsx)("div",{className:"space-y-3",children:P.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("i",{className:"ri-file-line text-gray-400"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:e.file_name}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:[e.document_type," • Uploaded ",e.created_at?new Date(e.created_at).toLocaleDateString():"Unknown date"]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[u.D.isPreviewable(e.file_name)&&(0,r.jsxs)("button",{onClick:()=>I(e),className:"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 text-sm",children:[(0,r.jsx)("i",{className:"ri-eye-line mr-1"}),"Preview"]}),(0,r.jsxs)("button",{onClick:()=>G(e),className:"text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200 text-sm",children:[(0,r.jsx)("i",{className:"ri-download-line mr-1"}),"Download"]}),(0,r.jsxs)("button",{onClick:()=>T(e.document_id),className:"text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 text-sm",children:[(0,r.jsx)("i",{className:"ri-delete-bin-line mr-1"}),"Remove"]})]})]},e.document_id))})]})]})]})}),Array.isArray(D)&&D.length>0&&(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Upload Summary"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"Total Required:"}),(0,r.jsx)("span",{className:"ml-2 font-medium text-gray-900 dark:text-gray-100",children:D.filter(e=>e.is_required).length})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"Uploaded:"}),(0,r.jsx)("span",{className:"ml-2 font-medium text-green-600 dark:text-green-400",children:P.length})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"Pending:"}),(0,r.jsx)("span",{className:"ml-2 font-medium text-orange-600 dark:text-orange-400",children:Object.keys(S).length})]})]})]})]})})}},21820:e=>{"use strict";e.exports=require("os")},24249:(e,t,a)=>{Promise.resolve().then(a.bind(a,20085))},24325:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var r=a(60687),s=a(43210);let i=({id:e,label:t,accept:a=".pdf",maxSize:i=10,required:n=!1,value:c,onChange:o,description:l,className:d=""})=>{let u=(0,s.useRef)(null);return(0,r.jsxs)("div",{className:d,children:[(0,r.jsxs)("label",{htmlFor:e,className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[t," ",n&&(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsxs)("div",{onClick:()=>{u.current?.click()},className:"border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-3 text-center cursor-pointer hover:border-primary hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors",children:[(0,r.jsx)("input",{ref:u,type:"file",accept:a,onChange:e=>{let t=e.target.files?.[0]||null;if(t){if(t.size>1024*i*1024)return void alert(`File size must be less than ${i}MB`);if(a&&!a.split(",").some(e=>t.name.toLowerCase().endsWith(e.trim().replace("*",""))))return void alert(`File type must be: ${a}`)}o(t)},className:"hidden",id:e,required:n}),c?(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("div",{className:"flex items-center justify-center",children:(0,r.jsx)("i",{className:"ri-file-text-line text-2xl text-green-500"})}),(0,r.jsx)("p",{className:"text-sm font-medium text-green-600 dark:text-green-400",children:c.name}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:[(c.size/1024/1024).toFixed(2)," MB"]}),(0,r.jsxs)("button",{type:"button",onClick:e=>{e.stopPropagation(),o(null),u.current&&(u.current.value="")},className:"inline-flex items-center px-3 py-1 bg-red-100 text-red-700 hover:bg-red-200 rounded-md text-xs font-medium transition-colors",children:[(0,r.jsx)("i",{className:"ri-delete-bin-line mr-1"}),"Remove"]})]}):(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("div",{className:"flex items-center justify-center",children:(0,r.jsx)("i",{className:"ri-upload-cloud-2-line text-2xl text-gray-400"})}),(0,r.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Click to upload ",t.toLowerCase()]}),l&&(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-500",children:l}),(0,r.jsxs)("div",{className:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors",children:[(0,r.jsx)("i",{className:"ri-folder-upload-line mr-2"}),"Choose File"]})]})]}),l&&!c&&(0,r.jsx)("p",{className:"mt-2 text-xs text-gray-500 dark:text-gray-400",children:l})]})}},25890:(e,t,a)=>{"use strict";a.d(t,{f:()=>c});var r=a(43210),s=a(16189),i=a(25011),n=a(36212);let c=({currentStepRoute:e,licenseCategoryId:t,applicationId:a})=>{let c=(0,s.useRouter)(),[o,l]=(0,r.useState)(!0),[d,u]=(0,r.useState)(null),[p,m]=(0,r.useState)(null),[g,y]=(0,r.useState)([]),x=(0,r.useMemo)(()=>new n.ef,[]),h=(0,r.useCallback)(async()=>{if(!t){u("License category ID is required"),l(!1);return}try{l(!0),u(null);let e=await x.getLicenseCategory(t);if(!e?.license_type_id)throw Error("License category does not have a license type ID");await new Promise(e=>setTimeout(e,500));let a=await x.getLicenseType(e.license_type_id);if(!a)throw Error("License type not found");let r=a.code||a.license_type_id;m(r);let s=[];s=(0,i.nF)(r)?(0,i.PY)(r):(0,i.QE)(r).steps,y(s)}catch(e){u(e.message||"Failed to load navigation configuration"),y((0,i.QE)("default").steps),m("default")}finally{l(!1)}},[t,x]);(0,r.useEffect)(()=>{h()},[h]);let f=(0,r.useMemo)(()=>g.findIndex(t=>t.route===e),[g,e]),_=(0,r.useMemo)(()=>g[f]||null,[g,f]),w=(0,r.useMemo)(()=>f>=0&&f<g.length-1?g[f+1]:null,[g,f]),b=(0,r.useMemo)(()=>f>0?g[f-1]:null,[g,f]),v=g.length,j=0===f,k=f===g.length-1,N=!k&&null!==w,D=!j&&null!==b,C=(0,r.useCallback)(e=>{let r=new URLSearchParams;return r.set("license_category_id",t||""),a&&r.set("application_id",a),`/customer/applications/apply/${e}?${r.toString()}`},[t,a]),P=(0,r.useCallback)(e=>{let t=C(e);c.push(t)},[C,c]);return{handleNext:(0,r.useCallback)(async e=>{if(N&&w){if(e)try{if(!await e())return}catch(e){e.message?.includes("timeout")||e.message?.includes("Bad Request")||e.message?.includes("Too many requests");return}P(w.route)}},[N,w,P]),handlePrevious:(0,r.useCallback)(()=>{D&&b&&P(b.route)},[D,b,P]),navigateToStep:P,currentStep:_,nextStep:w,previousStep:b,currentStepIndex:f,totalSteps:v,loading:o,error:d,licenseTypeCode:p,isFirstStep:j,isLastStep:k,canNavigateNext:N,canNavigatePrevious:D}}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},45125:(e,t,a)=>{"use strict";a.d(t,{_:()=>i});var r=a(51278),s=a(12234);let i={async getLicenseCategoryDocuments(e={}){let t=new URLSearchParams;e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(([e,a])=>{Array.isArray(a)?a.forEach(a=>t.append(`filter.${e}`,a)):t.set(`filter.${e}`,a)});let a=await s.uE.get(`/license-category-documents?${t.toString()}`);return(0,r.zp)(a)},async getLicenseCategoryDocument(e){let t=await s.uE.get(`/license-category-documents/${e}`);return(0,r.zp)(t)},async getLicenseCategoryDocumentsByCategory(e){let t=await s.uE.get(`/license-category-documents/by-license-category/${e}`);return(0,r.zp)(t).data},async createLicenseCategoryDocument(e){let t=await s.uE.post("/license-category-documents",e);return(0,r.zp)(t)},async updateLicenseCategoryDocument(e,t){let a=await s.uE.patch(`/license-category-documents/${e}`,t);return(0,r.zp)(a)},async deleteLicenseCategoryDocument(e){let t=await s.uE.delete(`/license-category-documents/${e}`);return(0,r.zp)(t)},async getAllLicenseCategoryDocuments(){let e=await this.getLicenseCategoryDocuments({limit:1e3});return(0,r.zp)(e)}}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64826:(e,t,a)=>{"use strict";a.d(t,{D:()=>i});var r=a(51278),s=a(12234);let i={async getDocuments(e){try{let t=new URLSearchParams;e?.page&&t.append("page",e.page.toString()),e?.limit&&t.append("limit",e.limit.toString()),e?.search&&t.append("search",e.search),e?.sortBy&&t.append("sortBy",e.sortBy);let a=await s.uE.get(`/documents?${t.toString()}`);return(0,r.zp)(a)}catch(e){throw e}},async getDocumentsByEntity(e,t){try{let a=await s.uE.get(`/documents/entity/${e}/${t}`);return(0,r.zp)(a)}catch(e){throw e}},async getDocumentsByApplication(e){try{let t=await s.uE.get(`/documents/by-application/${e}`);return(0,r.zp)(t)}catch(e){throw e}},async getRequiredDocumentsForLicenseCategory(e){try{let t=await s.uE.get(`/license-category-documents/category/${e}`);return(0,r.zp)(t)}catch(e){throw e}},async uploadDocument(e,t){try{let a={document_type:t.document_type,file_name:e.name,entity_type:t.entity_type,entity_id:t.entity_id,file_path:`uploads/${t.entity_type}/${t.entity_id}/${e.name}`,file_size:e.size,mime_type:e.type,is_required:t.is_required||!1},i=await s.uE.post("/documents",a);return{document:(0,r.zp)(i),message:"Document record created successfully"}}catch(e){throw e}},async createDocument(e){try{let t=await s.uE.post("/documents",e);return(0,r.zp)(t)}catch(e){throw e}},async updateDocument(e,t){try{let a=await s.uE.put(`/documents/${e}`,t);return(0,r.zp)(a)}catch(e){throw e}},async deleteDocument(e){try{await s.uE.delete(`/documents/${e}`)}catch(e){throw e}},async getDocument(e){try{let t=await s.uE.get(`/documents/${e}`);return(0,r.zp)(t)}catch(e){throw e}},async downloadDocument(e){try{return(await s.uE.get(`/documents/${e}/download`,{responseType:"blob"})).data}catch(e){throw e}},async previewDocument(e){try{return(await s.uE.get(`/documents/${e}/preview`,{responseType:"blob"})).data}catch(e){throw e}},isPreviewable:e=>["application/pdf","image/jpeg","image/jpg","image/png","image/gif","image/webp","text/plain","text/html","text/css","text/javascript","application/json"].includes(e.toLowerCase()),async checkRequiredDocuments(e,t){try{let a=await this.getRequiredDocumentsForLicenseCategory(t),r=await this.getDocumentsByApplication(e),s=r.map(e=>e.document_type),i=a.filter(e=>e.is_required&&!s.includes(e.name.toLowerCase().replace(/\s+/g,"_")));return{allUploaded:0===i.length,missing:i,uploaded:r}}catch(e){throw e}},getDocumentTypes:()=>["certificate_incorporation","memorandum_association","shareholding_structure","business_plan","financial_statements","technical_proposal","coverage_plan","network_diagram","equipment_specifications","insurance_certificate","tax_clearance","audited_accounts","bank_statement","cv_document","other"],formatDocumentType:e=>e.split("_").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "),mapDocumentNameToType(e){let t={"Certificate of Incorporation":"certificate_incorporation","Memorandum of Association":"memorandum_association","Shareholding Structure":"shareholding_structure","Business Plan":"business_plan","Financial Statements":"financial_statements","Technical Proposal":"technical_proposal","Coverage Plan":"coverage_plan","Network Diagram":"network_diagram","Equipment Specifications":"equipment_specifications","Insurance Certificate":"insurance_certificate","Tax Clearance Certificate":"tax_clearance","Tax Clearance":"tax_clearance","Audited Accounts":"audited_accounts","Bank Statement":"bank_statement","CV Document":"cv_document",Other:"other"};if(t[e])return t[e];let a=e.toLowerCase();for(let[e,r]of Object.entries(t))if(e.toLowerCase()===a)return r;return e.toLowerCase().replace(/\s+/g,"_")},validateFile:(e,t=10,a=[])=>e.size>1024*t*1024?{isValid:!1,error:`File size must be less than ${t}MB`}:a.length>0&&!a.includes(e.type)?{isValid:!1,error:`File type not allowed. Allowed types: ${a.join(", ")}`}:{isValid:!0}}},74075:e=>{"use strict";e.exports=require("zlib")},78637:(e,t,a)=>{"use strict";a.d(t,{k:()=>i});var r=a(51278),s=a(12234);let i={async getApplications(e){let t=new URLSearchParams;e?.page&&t.append("page",e.page.toString()),e?.limit&&t.append("limit",e.limit.toString()),e?.search&&t.append("search",e.search),e?.sortBy&&t.append("sortBy",e.sortBy),e?.sortOrder&&t.append("sortOrder",e.sortOrder),e?.filters?.licenseTypeId&&t.append("filter.license_category.license_type_id",e.filters.licenseTypeId),e?.filters?.licenseCategoryId&&t.append("filter.license_category_id",e.filters.licenseCategoryId),e?.filters?.status&&t.append("filter.status",e.filters.status);let a=await s.uE.get(`/applications?${t.toString()}`);return(0,r.zp)(a)},async getApplicationsByLicenseType(e,t){let a=new URLSearchParams;t?.page&&a.append("page",t.page.toString()),t?.limit&&a.append("limit",t.limit.toString()),t?.search&&a.append("search",t.search),t?.status&&a.append("filter.status",t.status),a.append("filter.license_category.license_type_id",e);let i=await s.uE.get(`/applications?${a.toString()}`);return(0,r.zp)(i)},async getApplication(e){let t=await s.uE.get(`/applications/${e}`);return(0,r.zp)(t)},async getApplicationsByApplicant(e){let t=await s.uE.get(`/applications/by-applicant/${e}`);return(0,r.zp)(t)},async getApplicationsByStatus(e){let t=await s.uE.get(`/applications/by-status/${e}`);return(0,r.zp)(t)},async updateApplicationStatus(e,t){let a=await s.uE.put(`/applications/${e}/status?status=${t}`);return(0,r.zp)(a)},async updateApplicationProgress(e,t,a){let i=await s.uE.put(`/applications/${e}/progress?currentStep=${t}&progressPercentage=${a}`);return(0,r.zp)(i)},async getApplicationStats(){let e=await s.uE.get("/applications/stats");return(0,r.zp)(e)},async createApplication(e){try{let t=await s.uE.post("/applications",e);return(0,r.zp)(t)}catch(e){throw e}},async updateApplication(e,t){try{let a=await s.uE.put(`/applications/${e}`,t,{timeout:3e4});return(0,r.zp)(a)}catch(e){if("ECONNABORTED"===e.code)throw Error("Request timeout - please try again");if(e.response?.status===400){let t=e.response?.data?.message||"Invalid application data";throw Error(`Bad Request: ${t}`)}if(e.response?.status===429)throw Error("Too many requests - please wait a moment and try again");throw e}},async deleteApplication(e){let t=await s.uE.delete(`/applications/${e}`);return(0,r.zp)(t)},async createApplicationWithApplicant(e){try{let t=new Date,a=t.toISOString().slice(0,10).replace(/-/g,""),r=t.toTimeString().slice(0,8).replace(/:/g,""),s=Math.random().toString(36).substr(2,3).toUpperCase(),i=`APP-${a}-${r}-${s}`;if(!/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e.user_id))throw Error(`Invalid user_id format: ${e.user_id}. Expected UUID format.`);return await this.createApplication({application_number:i,applicant_id:e.user_id,license_category_id:e.license_category_id,current_step:1,progress_percentage:0})}catch(e){throw e}},async saveApplicationSection(e,t,a){try{let a=1,r=["applicantInfo","companyProfile","businessInfo","serviceScope","businessPlan","legalHistory","reviewSubmit"].indexOf(t);a=r>=0?r+1:1;let s=Math.min(Math.round(a/6*100),100);await this.updateApplication(e,{progress_percentage:s,current_step:a})}catch(e){throw e}},async getApplicationSection(e,t){try{let a=await s.uE.get(`/applications/${e}/sections/${t}`);return(0,r.zp)(a)}catch(e){throw e}},async submitApplication(e){try{let t=await s.uE.put(`/applications/${e}`,{status:"submitted",submitted_at:new Date().toISOString(),progress_percentage:100,current_step:7});return(0,r.zp)(t)}catch(e){throw e}},async getUserApplications(){try{let e=await s.uE.get("/applications/user-applications"),t=(0,r.zp)(e),a=[];return t?.data?a=Array.isArray(t.data)?t.data:[]:Array.isArray(t)?a=t:t&&(a=[t]),a}catch(e){throw e}},async saveAsDraft(e,t){try{let a=await s.uE.put(`/applications/${e}`,{form_data:t,status:"draft"});return(0,r.zp)(a)}catch(e){throw e}},async validateApplication(e){try{let e={},t=[];for(let a of["applicantInfo","companyProfile","businessInfo","legalHistory"])e[a]&&0!==Object.keys(e[a]).length||t.push(`${a} section is incomplete`);return{isValid:0===t.length,errors:t}}catch(e){throw e}}}},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},87753:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>l});var r=a(65239),s=a(48088),i=a(88170),n=a.n(i),c=a(30893),o={};for(let e in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>c[e]);a.d(t,o);let l={children:["",{children:["customer",{children:["applications",{children:["apply",{children:["documents",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,1611)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\documents\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,68736)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\documents\\page.tsx"],u={require:a,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/customer/applications/apply/documents/page",pathname:"/customer/applications/apply/documents",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[4447,7498,1658,5814,7563,6893,3128],()=>a(87753));module.exports=r})();