"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApplicationStatusTrackingModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const applications_entity_1 = require("../entities/applications.entity");
const application_status_history_entity_1 = require("../entities/application-status-history.entity");
const user_entity_1 = require("../entities/user.entity");
const application_status_tracking_controller_1 = require("../controllers/application-status-tracking.controller");
const application_status_tracking_service_1 = require("../services/application-status-tracking.service");
let ApplicationStatusTrackingModule = class ApplicationStatusTrackingModule {
};
exports.ApplicationStatusTrackingModule = ApplicationStatusTrackingModule;
exports.ApplicationStatusTrackingModule = ApplicationStatusTrackingModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                applications_entity_1.Applications,
                application_status_history_entity_1.ApplicationStatusHistory,
                user_entity_1.User
            ])
        ],
        controllers: [application_status_tracking_controller_1.ApplicationStatusTrackingController],
        providers: [application_status_tracking_service_1.ApplicationStatusTrackingService],
        exports: [application_status_tracking_service_1.ApplicationStatusTrackingService]
    })
], ApplicationStatusTrackingModule);
//# sourceMappingURL=application-status-tracking.module.js.map