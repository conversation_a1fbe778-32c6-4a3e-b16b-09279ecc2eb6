(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5009],{42165:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>E});var a=r(95155),s=r(12115),n=r(76312),i=r(69733),l=r(52956),d=r(10012);let o={async createDepartment(e){try{let t=await l.uE.post("/department",e);return(0,d.zp)(t)}catch(e){throw e}},async getDepartments(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{let t=new URLSearchParams;e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(e=>{let[r,a]=e;Array.isArray(a)?a.forEach(e=>t.append("filter.".concat(r),e)):t.set("filter.".concat(r),a)});let r=await l.uE.get("/department?".concat(t.toString()));return(0,d.zp)(r)}catch(e){throw e}},async getAllDepartments(){try{return(await this.getDepartments()).data}catch(e){throw e}},async getDepartment(e){let t=await l.uE.get("/department/".concat(e));return(0,d.zp)(t)},async getDepartmentById(e){return this.getDepartment(e)},async updateDepartment(e,t){let r=await l.uE.put("/department/".concat(e),t);return(0,d.zp)(r)},async deleteDepartment(e){await l.uE.delete("/department/".concat(e))},formatDepartmentCode:e=>e.toUpperCase(),formatDepartmentName:e=>e.charAt(0).toUpperCase()+e.slice(1),validateDepartmentCode:e=>/^[A-Z]{1,5}$/.test(e),validateEmail:e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)};var c=r(84744),m=r(61967),g=r(63956);let x=function(e){let{roles:t,formData:r,handleRoleToggle:n}=e,[i,l]=(0,s.useState)(!1),d=(0,s.useRef)(null),o=e=>e.length<=3?e.toUpperCase():e.charAt(0).toUpperCase()+e.slice(1).toLowerCase();(0,s.useEffect)(()=>{function e(e){d.current&&!d.current.contains(e.target)&&l(!1)}return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let c=r.role_ids.length;return(0,a.jsxs)("div",{className:"w-full",ref:d,children:[(0,a.jsx)("label",{className:"block font-medium text-gray-700 dark:text-gray-300 mb-2 text-sm",children:"Roles"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("button",{type:"button",onClick:()=>l(!i),className:"\n            px-3 py-2 border rounded-md shadow-sm transition-colors duration-200 text-left cursor-pointer\n            bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\n            focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary\n            w-full\n            ".concat(i?"border-red-500 ring-2 ring-red-500":"border-gray-300 dark:border-gray-600","\n          "),"aria-haspopup":"listbox","aria-expanded":i,"aria-labelledby":"roles-label",children:[(0,a.jsx)("span",{className:"block truncate",children:0===c?"Select roles...":"".concat(c," role").concat(c>1?"s":""," selected")}),(0,a.jsx)("span",{className:"absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-gray-400 transition-transform duration-200 ".concat(i?"rotate-180":""),xmlns:"http://www.w3.org/2000/svg",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 111.06 1.06l-4.24 4.24a.75.75 0 01-1.06 0L5.21 8.27a.75.75 0 01.02-1.06z",clipRule:"evenodd"})})})]}),i&&(0,a.jsx)("ul",{className:"absolute z-[9999] bottom-full mb-1 w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-40 py-1 text-sm ring-1 ring-black ring-opacity-5 overflow-auto",role:"listbox","aria-labelledby":"roles-label",children:t.length>0?t.map(e=>{let t=r.role_ids.includes(e.role_id);return(0,a.jsxs)("li",{className:"cursor-pointer select-none relative py-2 pl-10 pr-4 hover:bg-red-50 dark:hover:bg-red-700",onClick:()=>n(e.role_id),role:"option","aria-selected":t,children:[(0,a.jsx)("span",{className:"block truncate ".concat(t?"font-semibold text-red-600 dark:text-red-300":"font-normal text-gray-900 dark:text-gray-100"),children:o(e.name)}),t&&(0,a.jsx)("span",{className:"absolute inset-y-0 left-0 flex items-center pl-3 text-red-600 dark:text-red-300",children:(0,a.jsx)("svg",{className:"h-5 w-5",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M16.704 5.293a1 1 0 00-1.414-1.414L7 12.172l-2.293-2.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l8-8z",clipRule:"evenodd"})})})]},e.role_id)}):(0,a.jsx)("li",{className:"text-gray-500 dark:text-gray-400 px-4 py-2",children:"Loading roles..."})})]})]})},u=function(e){let{departments:t,selectedDepartmentId:r,onSelect:n}=e,[i,l]=(0,s.useState)(!1),d=(0,s.useRef)(null);(0,s.useEffect)(()=>{function e(e){d.current&&!d.current.contains(e.target)&&l(!1)}return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let o=t.find(e=>e.department_id===r);return(0,a.jsxs)("div",{className:"w-full",ref:d,children:[(0,a.jsx)("label",{className:"block font-medium text-gray-700 dark:text-gray-300 mb-2 text-sm",children:"Department"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("button",{type:"button",onClick:()=>l(!i),className:"w-full px-3 py-2 border rounded-md shadow-sm transition-colors duration-200 text-left\n            bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\n            focus:outline-none focus:ring-2 focus:ring-primary\n            ".concat(i?"border-red-500 ring-2 ring-red-500":"border-gray-300 dark:border-gray-600","\n          "),"aria-haspopup":"listbox","aria-expanded":i,children:[(0,a.jsx)("span",{className:"block truncate",children:o?o.name:"Select department..."}),(0,a.jsx)("span",{className:"absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-gray-400 transition-transform duration-200 ".concat(i?"rotate-180":""),xmlns:"http://www.w3.org/2000/svg",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 111.06 1.06l-4.24 4.24a.75.75 0 01-1.06 0L5.21 8.27a.75.75 0 01.02-1.06z",clipRule:"evenodd"})})})]}),i&&(0,a.jsx)("ul",{className:"absolute z-50 bottom-full mb-1 w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 py-1 text-sm ring-1 ring-black ring-opacity-5 overflow-auto",role:"listbox",children:t.length>0?t.map(e=>(0,a.jsxs)("li",{onClick:()=>{n(e.department_id),l(!1)},className:"cursor-pointer select-none relative py-2 pl-10 pr-4 hover:bg-red-50 dark:hover:bg-red-700 ".concat(r===e.department_id?"font-semibold text-red-600 dark:text-red-300":"text-gray-900 dark:text-gray-100"),role:"option","aria-selected":r===e.department_id,children:[e.name,r===e.department_id&&(0,a.jsx)("span",{className:"absolute inset-y-0 left-0 flex items-center pl-3 text-red-600 dark:text-red-300",children:(0,a.jsx)("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M16.704 5.293a1 1 0 00-1.414-1.414L7 12.172l-2.293-2.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l8-8z",clipRule:"evenodd"})})})]},e.department_id)):(0,a.jsx)("li",{className:"text-gray-500 dark:text-gray-400 px-4 py-2",children:"No departments found"})})]})]})},p=function(e){let{organizations:t,selectedOrganizationId:r,onSelect:n,onNavigateToOrganizations:i}=e,[l,d]=(0,s.useState)(!1),[o,c]=(0,s.useState)(""),m=(0,s.useRef)(null),g=(0,s.useRef)(null);(0,s.useEffect)(()=>{function e(e){m.current&&!m.current.contains(e.target)&&d(!1)}return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]),(0,s.useEffect)(()=>{function e(e){l&&"Escape"===e.key&&d(!1)}return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[l]);let x=t.find(e=>e.organization_id===r),u=[...t.filter(e=>""===o||e.name.toLowerCase().includes(o.toLowerCase())||e.registration_number.toLowerCase().includes(o.toLowerCase())||e.tpin&&e.tpin.toLowerCase().includes(o.toLowerCase())),{organization_id:"other",name:"Other (Create New)",registration_number:"",tpin:"",website:"",email:"",phone:"",postal_address:"",physical_address:"",date_incorporation:"",place_incorporation:"",profile_description:"",created_at:"",updated_at:""}];return(0,a.jsxs)("div",{className:"w-full",ref:m,children:[(0,a.jsx)("label",{className:"block font-medium text-gray-700 dark:text-gray-300 mb-2 text-sm",children:"Organization"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{ref:g,type:"text",value:o,onChange:e=>{c(e.target.value),d(!0)},onFocus:()=>d(!0),placeholder:"Search organizations...",className:"w-full px-3 py-2 pr-10 border rounded-md shadow-sm\n            bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\n            focus:outline-none focus:ring-2 focus:ring-primary\n            ".concat(l?"border-red-500 ring-2 ring-red-500":"border-gray-300 dark:border-gray-600","\n          "),"aria-haspopup":"listbox","aria-expanded":l}),(0,a.jsx)("button",{type:"button",onClick:()=>d(!l),className:"absolute inset-y-0 right-0 flex items-center pr-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:(0,a.jsx)("svg",{className:"h-5 w-5 transition-transform duration-200 ".concat(l?"rotate-180":""),xmlns:"http://www.w3.org/2000/svg",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 111.06 1.06l-4.24 4.24a.75.75 0 01-1.06 0L5.21 8.27a.75.75 0 01.02-1.06z",clipRule:"evenodd"})})}),x&&(0,a.jsx)("div",{className:"mt-2 p-2 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("span",{className:"text-sm font-medium text-green-800 dark:text-green-200",children:["Selected: ",x.name]}),"other"!==x.organization_id&&(0,a.jsxs)("div",{className:"text-xs text-green-600 dark:text-green-400",children:[x.registration_number," ",x.tpin&&"• ".concat(x.tpin)]})]}),(0,a.jsx)("button",{type:"button",onClick:()=>n(""),className:"text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200",title:"Clear selection",children:(0,a.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})]})}),l&&(0,a.jsx)("ul",{className:"absolute z-50 bottom-full mb-1 w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 py-1 text-sm ring-1 ring-black ring-opacity-5 overflow-auto",role:"listbox",children:u.length>1?u.map(e=>(0,a.jsxs)("li",{onClick:()=>{"other"===e.organization_id&&i?i():n(e.organization_id),d(!1)},className:"cursor-pointer select-none relative py-2 pl-10 pr-4 hover:bg-red-50 dark:hover:bg-red-700 ".concat(r===e.organization_id?"font-semibold text-red-600 dark:text-red-300":"text-gray-900 dark:text-gray-100"," ").concat("other"===e.organization_id?"border-t border-gray-200 dark:border-gray-600 mt-1 pt-3":""),role:"option","aria-selected":r===e.organization_id,children:[(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"font-medium",children:e.name}),"other"!==e.organization_id&&(0,a.jsxs)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:[e.registration_number," ",e.tpin&&"• ".concat(e.tpin)]})]}),r===e.organization_id&&(0,a.jsx)("span",{className:"absolute inset-y-0 left-0 flex items-center pl-3 text-red-600 dark:text-red-300",children:(0,a.jsx)("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M16.704 5.293a1 1 0 00-1.414-1.414L7 12.172l-2.293-2.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l8-8z",clipRule:"evenodd"})})})]},e.organization_id)):(0,a.jsxs)("li",{className:"text-gray-500 dark:text-gray-400 px-4 py-2",children:["No organizations found",(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)("button",{onClick:()=>{i?i():n("other"),d(!1)},className:"text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 underline",children:"Create New Organization"})})]})})]})]})};function h(e){let{isOpen:t,onClose:r,onSave:n,user:i,roles:l=[],departments:d=[],organizations:o=[],onNavigateToOrganizations:h}=e,[b,f]=(0,s.useState)({email:"",password:"",first_name:"",last_name:"",middle_name:"",phone:"",department_id:"",organization_id:"",status:"active",role_ids:[]}),[y,v]=(0,s.useState)(!1),[j,k]=(0,s.useState)(null),[w,N]=(0,s.useState)(!1),[_,C]=(0,s.useState)(!1),[z,S]=(0,s.useState)(!1);(0,s.useEffect)(()=>{if(i){var e;let t=!!i.department_id;f({email:i.email,password:"",first_name:i.first_name,last_name:i.last_name,middle_name:i.middle_name||"",phone:i.phone,department_id:i.department_id||"",organization_id:i.organization_id||"",status:i.status,role_ids:(null==(e=i.roles)?void 0:e.map(e=>e.role_id))||[]}),N(t),C(!0)}else f({email:"",password:"",first_name:"",last_name:"",middle_name:"",phone:"",department_id:"",organization_id:"",status:"active",role_ids:[]}),N(!1),C(!1);k(null),S(!1)},[i,t]),(0,s.useEffect)(()=>{z&&C(!0)},[w,z]),(0,s.useEffect)(()=>{z&&(w?f(e=>({...e,organization_id:""})):f(e=>({...e,department_id:""})))},[w,z]);let D=()=>b.email&&b.email.trim()?b.first_name&&b.first_name.trim()?b.last_name&&b.last_name.trim()?b.phone&&b.phone.trim()?i||b.password&&b.password.trim()?!w||b.department_id&&b.department_id.trim()?0==b.role_ids.length?"Staff user can not be created without roles":b.role_ids.some(e=>{let t=l.find(t=>t.role_id===e);if(!t)return!1;let r=t.name.toLowerCase();return"administrator"===r||"admin"===r||"super_admin"===r})&&!b.email.endsWith("@macra.mw")?"Only MACRA staff can be assigned administrator roles":b.role_ids.some(e=>{let t=l.find(t=>t.role_id===e);return!!t&&"evaluator"===t.name.toLowerCase()})&&!b.email.endsWith("@macra.mw")?"Only MACRA staff can be assigned evaluator roles":w||b.organization_id&&b.organization_id.trim()?null:"Please select an organization for non-MACRA staff members":"Please select a department for MACRA staff members":"Password is required for new users":"Phone number is required":"Last name is required":"First name is required":"Email is required",E=async e=>{e.preventDefault(),v(!0),k(null);let t=D();if(t){k(t),v(!1);return}try{if(i){let e={email:b.email,first_name:b.first_name,last_name:b.last_name,middle_name:b.middle_name||void 0,phone:b.phone,status:b.status,role_ids:b.role_ids.length>0?b.role_ids:void 0,department_id:w&&b.department_id||void 0,organization_id:!w&&b.organization_id||void 0};b.password.trim()&&(e.password=b.password),await c.D.updateUser(i.user_id,e)}else{let e={email:b.email,password:b.password,first_name:b.first_name,last_name:b.last_name,middle_name:b.middle_name||void 0,phone:b.phone,status:b.status,role_ids:b.role_ids.length>0?b.role_ids:void 0,department_id:w&&b.department_id||void 0,organization_id:!w&&b.organization_id||void 0};await c.D.createUser(e)}n()}catch(e){k(e instanceof Error&&"response"in e&&"object"==typeof e.response&&null!==e.response&&"data"in e.response&&"object"==typeof e.response.data&&null!==e.response.data&&"message"in e.response.data&&"string"==typeof e.response.data.message?e.response.data.message:"Failed to save user")}finally{v(!1)}},P=e=>{let{name:t,value:r}=e.target;f(e=>({...e,[t]:r}))};return t?(0,a.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,a.jsxs)("div",{className:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-gray-500 dark:bg-gray-900 bg-opacity-75 dark:bg-opacity-75 transition-opacity",onClick:r}),(0,a.jsx)("div",{className:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-visible shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:(0,a.jsxs)("form",{onSubmit:E,children:[(0,a.jsx)("div",{className:"bg-white rounded-lg dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:(0,a.jsx)("div",{className:"sm:flex sm:items-start",children:(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4",children:i?"Edit User":"Create New User"}),j&&(0,a.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded",children:j}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(m.A,{type:"email",name:"email",label:"Email",required:!0,value:b.email,onChange:P,placeholder:"Enter email address"}),(0,a.jsx)(m.A,{type:"password",name:"password",label:"Password ".concat(i?"":"*"),required:!i,value:b.password,onChange:P,placeholder:i?"Leave blank to keep current password":"Enter password"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(m.A,{type:"text",name:"first_name",label:"First Name",required:!0,value:b.first_name,onChange:P,placeholder:"Enter first name"}),(0,a.jsx)(m.A,{type:"text",name:"last_name",label:"Last Name",required:!0,value:b.last_name,onChange:P,placeholder:"Enter last name"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(m.A,{type:"text",name:"middle_name",label:"Middle Name",value:b.middle_name,onChange:P,placeholder:"Enter middle name (optional)"}),(0,a.jsx)(m.A,{type:"tel",name:"phone",label:"Phone",required:!0,value:b.phone,onChange:P,placeholder:"Enter phone number"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)(g.A,{name:"status",label:"Status",value:b.status,onChange:P,children:[(0,a.jsx)("option",{value:"active",children:"Active"}),(0,a.jsx)("option",{value:"inactive",children:"Inactive"}),(0,a.jsx)("option",{value:"suspended",children:"Suspended"})]}),(0,a.jsxs)(g.A,{name:"isStaff",label:"MACRA Staff Member?",value:w?"true":"false",onChange:e=>{N("true"===e.target.value),S(!0)},children:[(0,a.jsx)("option",{value:"true",children:"Yes"}),(0,a.jsx)("option",{value:"false",children:"No"})]}),_&&(0,a.jsxs)(a.Fragment,{children:[w?(0,a.jsx)(u,{selectedDepartmentId:b.department_id,onSelect:e=>{f(t=>({...t,department_id:e}))},departments:d}):(0,a.jsx)(p,{selectedOrganizationId:b.organization_id,onSelect:e=>{f(t=>({...t,organization_id:e}))},organizations:o,onNavigateToOrganizations:h}),(0,a.jsx)(x,{roles:l,formData:b,handleRoleToggle:e=>{let t=String(e);f(e=>({...e,role_ids:e.role_ids.includes(t)?e.role_ids.filter(e=>e!==t):[...e.role_ids,t]}))}})]})]})]})]})})}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,a.jsx)("button",{type:"submit",disabled:y,className:"main-button inline-flex items-center justify-center w-full sm:w-auto sm:ml-3 disabled:opacity-50",children:y?"Saving...":i?"Update User":"Create User"}),(0,a.jsx)("button",{type:"button",onClick:r,className:"secondary-main-button inline-flex items-center justify-center mt-3 w-full sm:mt-0 sm:ml-3 sm:w-auto",children:"Cancel"})]})]})})]})}):null}var b=r(40165);let f=e=>{let{isOpen:t,onClose:r,onSave:n,department:i}=e,[l,d]=(0,s.useState)({code:"",name:"",description:"",email:""}),[c,m]=(0,s.useState)({}),[g,x]=(0,s.useState)(!1),u=!!i;(0,s.useEffect)(()=>{t&&(i?d({code:i.code,name:i.name,description:i.description,email:i.email}):d({code:"",name:"",description:"",email:""}),m({}))},[t,i]);let p=()=>{let e={};return l.code.trim()?l.code.length>5?e.code="Department code must be 5 characters or less":/^[A-Z0-9]+$/.test(l.code.toUpperCase())||(e.code="Department code must contain only letters and numbers"):e.code="Department code is required",l.name.trim()?l.name.length>100&&(e.name="Department name must be 100 characters or less"):e.name="Department name is required",l.description.trim()||(e.description="Description is required"),l.email.trim()?o.validateEmail(l.email)||(e.email="Please enter a valid email address"):e.email="Email is required",m(e),0===Object.keys(e).length},h=async e=>{if(e.preventDefault(),p())try{let e;if(x(!0),u&&i){let t={code:l.code.toUpperCase(),name:l.name,description:l.description,email:l.email};e=await o.updateDepartment(i.department_id,t)}else{let t={...l,code:l.code.toUpperCase()};e=await o.createDepartment(t)}n(e),r()}catch(e){var t,a;(null==(a=e.response)||null==(t=a.data)?void 0:t.message)?e.response.data.message.includes("code")?m({code:"Department code already exists"}):e.response.data.message.includes("name")?m({name:"Department name already exists"}):e.response.data.message.includes("email")?m({email:"Email already exists"}):m({general:e.response.data.message}):m({general:"Failed to save department. Please try again."})}finally{x(!1)}},b=(e,t)=>{d(r=>({...r,[e]:t})),c[e]&&m(t=>({...t,[e]:""}))};return t?(0,a.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,a.jsxs)("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:r}),(0,a.jsx)("div",{className:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:(0,a.jsxs)("form",{onSubmit:h,children:[(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900 dark:text-gray-100",children:u?"Edit Department":"Add New Department"}),(0,a.jsx)("button",{type:"button",onClick:r,className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:(0,a.jsx)("i",{className:"ri-close-line text-xl"})})]}),c.general&&(0,a.jsx)("div",{className:"mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md",children:(0,a.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:c.general})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"code",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Department Code *"}),(0,a.jsx)("input",{type:"text",id:"code",value:l.code,onChange:e=>b("code",e.target.value.toUpperCase()),className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 font-mono ".concat(c.code?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"," bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"),placeholder:"e.g., HR, IT, FIN",maxLength:5,disabled:g}),c.code&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:c.code}),(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:"Maximum 5 characters, letters and numbers only"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Department Name *"}),(0,a.jsx)("input",{type:"text",id:"name",value:l.name,onChange:e=>b("name",e.target.value),className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ".concat(c.name?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"," bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"),placeholder:"e.g., Human Resources",maxLength:100,disabled:g}),c.name&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:c.name})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Description *"}),(0,a.jsx)("textarea",{id:"description",value:l.description,onChange:e=>b("description",e.target.value),rows:3,className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ".concat(c.description?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"," bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"),placeholder:"Brief description of the department's role and responsibilities",disabled:g}),c.description&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:c.description})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Department Email *"}),(0,a.jsx)("input",{type:"email",id:"email",value:l.email,onChange:e=>b("email",e.target.value),className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ".concat(c.email?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"," bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"),placeholder:"<EMAIL>",disabled:g}),c.email&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:c.email})]})]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,a.jsx)("button",{type:"submit",disabled:g,className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:g?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"ri-loader-4-line animate-spin mr-2"}),u?"Updating...":"Creating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"ri-save-line mr-2"}),u?"Update Department":"Create Department"]})}),(0,a.jsx)("button",{type:"button",onClick:r,disabled:g,className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"Cancel"})]})]})})]})}):null},y={async createOrganization(e){try{let t=await l.uE.post("/organization",e);return(0,d.zp)(t)}catch(e){throw e}},async getOrganizations(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{let t=new URLSearchParams;e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(e=>{let[r,a]=e;Array.isArray(a)?a.forEach(e=>t.append("filter.".concat(r),e)):t.set("filter.".concat(r),a)});let r=await l.uE.get("/organization?".concat(t.toString()));return(0,d.zp)(r)}catch(e){throw e}},async getAllOrganizations(){try{return(await this.getOrganizations()).data}catch(e){throw e}},async getOrganization(e){let t=await l.uE.get("/organization/".concat(e));return(0,d.zp)(t)},async getOrganizationById(e){return this.getOrganization(e)},async updateOrganization(e,t){let r=await l.uE.put("/organization/".concat(e),t);return(0,d.zp)(r)},async deleteOrganization(e){await l.uE.delete("/organization/".concat(e))},formatOrganizationName:e=>e.charAt(0).toUpperCase()+e.slice(1),formatRegistrationNumber:e=>e.toUpperCase().trim(),validateRegistrationNumber:e=>/^[A-Z0-9\-]+$/.test(e)&&e.length>=3,validateEmail:e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),validatePhone:e=>/^[\d\s\-\+\(\)]{5,20}$/.test(e),validateWebsite(e){try{return new URL(e),!0}catch(e){return!1}},formatWebsite:e=>e.startsWith("http://")||e.startsWith("https://")?e:"https://".concat(e),formatIncorporationDate:e=>new Date(e).toLocaleDateString(),validateIncorporationDate(e){let t=new Date(e);return t<=new Date&&t.getFullYear()>1800},validateTpin:e=>/^[A-Z0-9\-]+$/.test(e)&&e.length>=8,formatTpin:e=>e.toUpperCase().trim()},v=e=>{let{isOpen:t,onClose:r,onSave:n,organization:i}=e,l={name:"",registration_number:"",tpin:"",website:"",email:"",phone:"",fax:"",postal_address:"",physical_address:"",date_incorporation:"",place_incorporation:"",profile_description:""},[d,o]=(0,s.useState)(l),[c,m]=(0,s.useState)({}),[g,x]=(0,s.useState)(!1),u=!!i;(0,s.useEffect)(()=>{t&&(i?o({name:i.name||"",registration_number:i.registration_number||"",tpin:i.tpin||"",website:i.website||"",email:i.email||"",phone:i.phone||"",fax:i.fax||"",postal_address:i.postal_address||"",physical_address:i.physical_address||"",date_incorporation:i.date_incorporation?i.date_incorporation.split("T")[0]:"",place_incorporation:i.place_incorporation||"",profile_description:i.profile_description||""}):o(l),m({}))},[t,i]);let p=()=>{let e={};return d?(d.name&&d.name.trim()?d.name.length>200&&(e.name="Organization name must be 200 characters or less"):e.name="Organization name is required",d.registration_number&&d.registration_number.trim()?y.validateRegistrationNumber(d.registration_number)||(e.registration_number="Registration number must be at least 3 characters and contain only letters, numbers, and hyphens"):e.registration_number="Registration number is required",d.tpin&&d.tpin.trim()?y.validateTpin(d.tpin)||(e.tpin="TPIN must be at least 8 characters and contain only letters, numbers, and hyphens"):e.tpin="TPIN is required",d.website&&d.website.trim()?y.validateWebsite(y.formatWebsite(d.website))||(e.website="Please enter a valid website URL"):e.website="Website is required",d.email&&d.email.trim()?y.validateEmail(d.email)||(e.email="Please enter a valid email address"):e.email="Email is required",d.phone&&d.phone.trim()?y.validatePhone(d.phone)||(e.phone="Please enter a valid phone number (5-20 characters)"):e.phone="Phone number is required",d.fax&&d.fax.trim()&&!y.validatePhone(d.fax)&&(e.fax="Please enter a valid fax number"),d.postal_address&&d.postal_address.trim()||(e.postal_address="Postal address is required"),d.physical_address&&d.physical_address.trim()||(e.physical_address="Physical address is required"),d.date_incorporation&&d.date_incorporation.trim()?y.validateIncorporationDate(d.date_incorporation)||(e.date_incorporation="Please enter a valid date of incorporation (not in the future)"):e.date_incorporation="Date of incorporation is required",d.place_incorporation&&d.place_incorporation.trim()||(e.place_incorporation="Place of incorporation is required"),d.profile_description&&d.profile_description.trim()?d.profile_description.length>1e3&&(e.profile_description="Profile description must be 1000 characters or less"):e.profile_description="Profile description is required",m(e),0===Object.keys(e).length):(e.general="Form data is not properly initialized",m(e),!1)},h=async e=>{if(e.preventDefault(),p())try{let e;if(x(!0),u&&i){let t={name:d.name,registration_number:y.formatRegistrationNumber(d.registration_number||""),tpin:y.formatTpin(d.tpin||""),website:y.formatWebsite(d.website||""),email:d.email,phone:d.phone,fax:d.fax||void 0,postal_address:d.postal_address,physical_address:d.physical_address,date_incorporation:d.date_incorporation,place_incorporation:d.place_incorporation,profile_description:d.profile_description};e=await y.updateOrganization(i.organization_id,t)}else{let t={...d,registration_number:y.formatRegistrationNumber(d.registration_number||""),tpin:y.formatTpin(d.tpin||""),website:y.formatWebsite(d.website||""),fax:d.fax||void 0};e=await y.createOrganization(t)}n(e),r()}catch(e){var t,a;(null==(a=e.response)||null==(t=a.data)?void 0:t.message)?e.response.data.message.includes("registration_number")?m({registration_number:"Registration number already exists"}):e.response.data.message.includes("tpin")?m({tpin:"TPIN already exists"}):e.response.data.message.includes("name")?m({name:"Organization name already exists"}):e.response.data.message.includes("email")?m({email:"Email already exists"}):m({general:e.response.data.message}):m({general:"Failed to save organization. Please try again."})}finally{x(!1)}},b=(e,t)=>{o(r=>({...r,[e]:t})),c[e]&&m(t=>({...t,[e]:""}))};return t?(0,a.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,a.jsxs)("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:r}),(0,a.jsx)("div",{className:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full",children:(0,a.jsxs)("form",{onSubmit:h,children:[(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900 dark:text-gray-100",children:u?"Edit Organization":"Add New Organization"}),(0,a.jsx)("button",{type:"button",onClick:r,className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:(0,a.jsx)("i",{className:"ri-close-line text-xl"})})]}),c.general&&(0,a.jsx)("div",{className:"mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md",children:(0,a.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:c.general})}),(0,a.jsxs)("div",{className:"space-y-4 max-h-96 overflow-y-auto",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Organization Name *"}),(0,a.jsx)("input",{type:"text",id:"name",value:d.name,onChange:e=>b("name",e.target.value),className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ".concat(c.name?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"," bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"),placeholder:"e.g., ABC Company Limited",maxLength:200,disabled:g}),c.name&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:c.name})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"registration_number",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Registration Number *"}),(0,a.jsx)("input",{type:"text",id:"registration_number",value:d.registration_number,onChange:e=>b("registration_number",e.target.value.toUpperCase()),className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 font-mono ".concat(c.registration_number?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"," bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"),placeholder:"e.g., BN123456",disabled:g}),c.registration_number&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:c.registration_number}),(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:"Business registration number from registrar"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"tpin",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"TPIN *"}),(0,a.jsx)("input",{type:"text",id:"tpin",value:d.tpin,onChange:e=>b("tpin",e.target.value.toUpperCase()),className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 font-mono ".concat(c.tpin?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"," bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"),placeholder:"e.g., 12345678-90",disabled:g}),c.tpin&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:c.tpin}),(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:"Tax Payer Identification Number"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"website",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Website *"}),(0,a.jsx)("input",{type:"url",id:"website",value:d.website,onChange:e=>b("website",e.target.value),className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ".concat(c.website?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"," bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"),placeholder:"e.g., www.company.com",disabled:g}),c.website&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:c.website})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Email Address *"}),(0,a.jsx)("input",{type:"email",id:"email",value:d.email,onChange:e=>b("email",e.target.value),className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ".concat(c.email?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"," bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"),placeholder:"<EMAIL>",disabled:g}),c.email&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:c.email})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Phone Number *"}),(0,a.jsx)("input",{type:"tel",id:"phone",value:d.phone,onChange:e=>b("phone",e.target.value),className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ".concat(c.phone?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"," bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"),placeholder:"+265 1 234 567",disabled:g}),c.phone&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:c.phone})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"fax",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Fax Number"}),(0,a.jsx)("input",{type:"tel",id:"fax",value:d.fax,onChange:e=>b("fax",e.target.value),className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ".concat(c.fax?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"," bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"),placeholder:"+265 1 234 568 (optional)",disabled:g}),c.fax&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:c.fax})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"postal_address",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Postal Address *"}),(0,a.jsx)("textarea",{id:"postal_address",value:d.postal_address,onChange:e=>b("postal_address",e.target.value),rows:2,className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ".concat(c.postal_address?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"," bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"),placeholder:"P.O. Box 123, City, Country",disabled:g}),c.postal_address&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:c.postal_address})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"physical_address",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Physical Address *"}),(0,a.jsx)("textarea",{id:"physical_address",value:d.physical_address,onChange:e=>b("physical_address",e.target.value),rows:2,className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ".concat(c.physical_address?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"," bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"),placeholder:"Street address, building, city",disabled:g}),c.physical_address&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:c.physical_address})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"date_incorporation",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Date of Incorporation *"}),(0,a.jsx)("input",{type:"date",id:"date_incorporation",value:d.date_incorporation,onChange:e=>b("date_incorporation",e.target.value),className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ".concat(c.date_incorporation?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"," bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"),disabled:g}),c.date_incorporation&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:c.date_incorporation})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"place_incorporation",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Place of Incorporation *"}),(0,a.jsx)("input",{type:"text",id:"place_incorporation",value:d.place_incorporation,onChange:e=>b("place_incorporation",e.target.value),className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ".concat(c.place_incorporation?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"," bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"),placeholder:"e.g., Lilongwe, Malawi",disabled:g}),c.place_incorporation&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:c.place_incorporation})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"profile_description",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Profile Description *"}),(0,a.jsx)("textarea",{id:"profile_description",value:d.profile_description,onChange:e=>b("profile_description",e.target.value),rows:4,className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ".concat(c.profile_description?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"," bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"),placeholder:"Brief description of the organization's business activities and profile",maxLength:1e3,disabled:g}),c.profile_description&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:c.profile_description}),(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:"Maximum 1000 characters"})]})]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,a.jsx)("button",{type:"submit",disabled:g,className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:g?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"ri-loader-4-line animate-spin mr-2"}),u?"Updating...":"Creating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"ri-save-line mr-2"}),u?"Update Organization":"Create Organization"]})}),(0,a.jsx)("button",{type:"button",onClick:r,disabled:g,className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"Cancel"})]})]})})]})}):null},j=e=>{let{tabs:t,activeTab:r,onTabChange:s}=e;return(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-700",children:(0,a.jsx)("nav",{className:"-mb-px flex space-x-8 px-6","aria-label":"Tabs",children:t.map(e=>(0,a.jsx)("button",{onClick:()=>s(e.id),className:"whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ".concat(r===e.id?"border-red-500 text-red-600 dark:text-red-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"),"aria-current":r===e.id?"page":void 0,children:e.label},e.id))})}),(0,a.jsx)("div",{className:"mt-6",children:t.map(e=>(0,a.jsx)("div",{className:"tab-content ".concat(r===e.id?"":"hidden"),children:e.content},e.id))})]})};var k=r(41987);function w(e){let{isOpen:t,onClose:r,onConfirm:s,title:n,message:i,confirmText:l="Confirm",cancelText:d="Cancel",confirmVariant:o="danger",loading:c=!1,icon:m}=e;return t?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 flex items-center justify-center z-50 p-4",children:(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 transform transition-all",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-start mb-4",children:[m||(()=>{switch(o){case"danger":return(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"h-10 w-10 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center",children:(0,a.jsx)("i",{className:"ri-delete-bin-line text-red-600 dark:text-red-400 text-xl"})})});case"warning":return(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"h-10 w-10 bg-yellow-100 dark:bg-yellow-900/20 rounded-full flex items-center justify-center",children:(0,a.jsx)("i",{className:"ri-alert-line text-yellow-600 dark:text-yellow-400 text-xl"})})});default:return(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"h-10 w-10 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center",children:(0,a.jsx)("i",{className:"ri-information-line text-blue-600 dark:text-blue-400 text-xl"})})})}})(),(0,a.jsxs)("div",{className:"ml-4 flex-1",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2",children:n}),(0,a.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"string"==typeof i?(0,a.jsx)("p",{children:i}):i})]})]}),(0,a.jsxs)("div",{className:"flex space-x-3 mt-6",children:[(0,a.jsx)("button",{onClick:s,disabled:c,className:"flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed ".concat((()=>{switch(o){case"danger":default:return"bg-red-600 hover:bg-red-700 text-white focus:ring-red-500";case"primary":return"bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500";case"warning":return"bg-yellow-600 hover:bg-yellow-700 text-white focus:ring-yellow-500"}})()),children:c?(0,a.jsxs)("div",{className:"flex items-center justify-center",children:[(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Processing..."]}):l}),(0,a.jsx)("button",{onClick:r,disabled:c,className:"flex-1 px-4 py-2 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-offset-gray-800 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:d})]})]})})}):null}let N=(0,s.forwardRef)((e,t)=>{let{label:r,error:s,helperText:n,required:i=!1,options:l=[],placeholder:d="Select an option...",className:o="",containerClassName:c="",onChange:m,id:g,value:x,...u}=e,p=g||"select-".concat(Math.random().toString(36).substr(2,9)),h="\n    w-full px-3 py-2 border rounded-md shadow-sm \n    focus:outline-none focus:ring-2 focus:ring-offset-2 \n    disabled:opacity-50 disabled:cursor-not-allowed\n    dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600\n    transition-colors duration-200\n    appearance-none bg-white\n    bg-no-repeat bg-right bg-[length:16px_16px]\n    pr-10\n  ",b=s?"".concat(h," border-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-600"):"".concat(h," border-gray-300 focus:border-primary focus:ring-primary dark:focus:border-primary dark:focus:ring-primary");return(0,a.jsxs)("div",{className:"space-y-1 ".concat(c),children:[r&&(0,a.jsxs)("label",{htmlFor:p,className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:[r,i&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("select",{ref:t,id:p,value:x||"",onChange:e=>{m&&m(e.target.value)},className:"".concat(b," ").concat(o),...u,children:[d&&(0,a.jsx)("option",{value:"",disabled:!0,children:d}),l.map(e=>(0,a.jsx)("option",{value:e.value,disabled:e.disabled,children:e.label},e.value))]}),(0,a.jsx)("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none",children:(0,a.jsx)("i",{className:"ri-arrow-down-s-line text-gray-400 dark:text-gray-500"})})]}),s&&(0,a.jsxs)("p",{className:"text-sm text-red-600 dark:text-red-400 flex items-center",children:[(0,a.jsx)("i",{className:"ri-error-warning-line mr-1"}),s]}),n&&!s&&(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:n})]})});N.displayName="Select";let _=e=>{let{onEditUser:t,onCreateUser:r}=e,[n,l]=(0,s.useState)(null),[d,m]=(0,s.useState)(!0),[g,x]=(0,s.useState)(null),[u,p]=(0,s.useState)(!1),[h,b]=(0,s.useState)(null),[f,v]=(0,s.useState)(!1),[j,_]=(0,s.useState)({}),[C,z]=(0,s.useState)([]),[S,D]=(0,s.useState)([]),[E,P]=(0,s.useState)([]),[A,R]=(0,s.useState)({page:1,limit:10}),L=(0,s.useCallback)(async e=>{try{m(!0),x(null),R(e);let t=await c.D.getUsers(e);l(t)}catch(r){let t="Failed to load users";if(r&&"object"==typeof r)if("code"in r&&"ERR_NETWORK"===r.code)t="Unable to connect to the server. Please check if the backend is running or contact your administrator.";else if("message"in r&&"Network Error"===r.message)t="Unable to connect to the server. Please check if the backend is running or contact your administrator.";else if("response"in r&&r.response&&"object"==typeof r.response){if("status"in r.response){let e=r.response.status;401===e?t="Authentication required. Please log in again.":403===e?t="You do not have permission to view users.":500===e?t="Server error. Please try again later.":"data"in r.response&&r.response.data&&"object"==typeof r.response.data&&"message"in r.response.data&&"string"==typeof r.response.data.message&&(t=r.response.data.message)}}else"message"in r&&"string"==typeof r.message&&(t=r.message);x(t),l({data:[],meta:{itemsPerPage:e.limit||10,totalItems:0,currentPage:e.page||1,totalPages:0,sortBy:[],searchBy:[],search:"",select:[]},links:{current:""}})}finally{m(!1)}},[]);(0,s.useEffect)(()=>{L({page:1,limit:10}),O(),U(),B()},[L]),(0,s.useEffect)(()=>{let e={};Object.entries(j).forEach(t=>{let[r,a]=t;void 0!==a&&""!==a.trim()&&(e[r]=a)}),L({page:1,limit:A.limit||10,filter:Object.keys(e).length>0?e:void 0})},[j,L,A.limit]);let O=async()=>{try{let e=(await o.getDepartments({page:1,limit:100})).data;z(e)}catch(e){z([])}},U=async()=>{try{let e=await i.O.getRoles({page:1,limit:100});D(e.data)}catch(e){}},B=async()=>{try{let e=await y.getOrganizations({page:1,limit:100});P(e.data)}catch(e){}},F=(e,t)=>{let r={...j};t&&""!==t.trim()?r[e]=t:delete r[e],_(r)},T=e=>{b(e),p(!0)},M=async()=>{if(h){v(!0);try{await c.D.deleteUser(h.user_id),n&&L({page:n.meta.currentPage,limit:n.meta.itemsPerPage}),p(!1),b(null)}catch(e){x("Failed to delete user")}finally{v(!1)}}},q=[{key:"user",label:"User",sortable:!0,render:(e,t)=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0 h-10 w-10",children:t.profile_image?(0,a.jsx)("img",{className:"h-10 w-10 rounded-full",src:t.profile_image,alt:"".concat(t.first_name," ").concat(t.last_name)}):(0,a.jsx)("div",{className:"h-10 w-10 rounded-full bg-red-600 dark:bg-red-700 flex items-center justify-center",children:(0,a.jsxs)("span",{className:"text-sm font-medium text-white",children:[t.first_name.charAt(0),t.last_name.charAt(0)]})})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsxs)("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:[t.first_name," ",t.last_name]}),(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:t.email})]})]})},{key:"department",label:"Department",render:(e,t)=>(0,a.jsx)("span",{className:"text-sm text-gray-900 dark:text-gray-100",children:(()=>{var e;if(null==(e=t.department)?void 0:e.name)return t.department.name;if(t.department_id&&C.length>0){let e=C.find(e=>e.department_id===t.department_id);return e?e.name:"Department Not Found"}return t.department_id&&0===C.length?"Loading...":"No Department"})()})},{key:"roles",label:"Roles",render:(e,t)=>(0,a.jsx)("span",{className:"text-sm text-gray-900 dark:text-gray-100",children:t.roles&&t.roles.length>0?t.roles.map(e=>null==e?void 0:e.name).join(", "):"No Roles"})},{key:"status",label:"Status",sortable:!0,render:e=>(0,a.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full ".concat("active"===e?"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200":"suspended"===e?"bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200":"bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200"),children:e.charAt(0).toUpperCase()+e.slice(1)})},{key:"last_login",label:"Last Login",sortable:!0,render:e=>(0,a.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:e?new Date(e).toLocaleString():"Never"})},{key:"actions",label:"Actions",render:(e,r)=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>t(r),className:"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 p-1 rounded hover:bg-indigo-50 dark:hover:bg-indigo-900",title:"Edit user",children:(0,a.jsx)("i",{className:"ri-edit-line"})}),(0,a.jsx)("button",{onClick:()=>T(r),className:"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 p-1 rounded hover:bg-red-50 dark:hover:bg-red-900",title:"Delete user",children:(0,a.jsx)("i",{className:"ri-delete-bin-line"})})]})}];return(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:"Users"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Manage users and their access permissions."})]}),(0,a.jsx)("div",{children:(0,a.jsx)("div",{className:"flex space-x-2 place-content-start",children:(0,a.jsx)("div",{className:"relative",children:(0,a.jsxs)("button",{type:"button",onClick:r,className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-900",children:[(0,a.jsx)("div",{className:"w-5 h-5 flex items-center justify-center mr-2",children:(0,a.jsx)("i",{className:"ri-user-add-line"})}),"Add User"]})})})})]})}),g&&(0,a.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded",children:g}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4",children:"Filters"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[C.length>0&&(0,a.jsx)(N,{label:"Department",value:j.department_id||"",onChange:e=>F("department_id",e),options:[{value:"",label:"All Departments"},...C.map(e=>({value:e.department_id,label:e.name}))]}),E.length>0&&(0,a.jsx)(N,{label:"Organization",value:j.organization_id||"",onChange:e=>F("organization_id",e),options:[{value:"",label:"All Organizations"},...E.map(e=>({value:e.organization_id,label:e.name}))]}),(0,a.jsx)(N,{label:"Role",value:j.role||"",onChange:e=>F("role",e),options:[{value:"",label:"All Roles"},...S.map(e=>({value:e.role_id,label:e.name}))]}),(0,a.jsx)(N,{label:"Status",value:j.status||"",onChange:e=>F("status",e),options:[{value:"",label:"All Statuses"},{value:"active",label:"Active"},{value:"inactive",label:"Inactive"},{value:"suspended",label:"Suspended"}]})]})]}),(0,a.jsx)(k.A,{columns:q,data:n,loading:d,onQueryChange:L,searchPlaceholder:"Search users by name or email..."}),(0,a.jsx)(w,{isOpen:u,onClose:()=>{p(!1),b(null)},onConfirm:M,title:"Delete User",message:h?(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"mb-2",children:["Are you sure you want to delete ",(0,a.jsxs)("strong",{children:[h.first_name," ",h.last_name]}),"?"]}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"This action cannot be undone. All data associated with this user will be permanently removed."})]}):"Are you sure you want to delete this user?",confirmText:"Yes, Delete User",cancelText:"Cancel",confirmVariant:"danger",loading:f})]})},C=e=>{let{onEditRole:t,onCreateRole:r}=e,[n,l]=(0,s.useState)(null),[d,o]=(0,s.useState)(!0),[c,m]=(0,s.useState)(null),[g,x]=(0,s.useState)(!1),[u,p]=(0,s.useState)(null),[h,b]=(0,s.useState)(!1);(0,s.useEffect)(()=>{f({page:1,limit:10})},[]);let f=async e=>{try{o(!0);let t=await i.O.getRoles(e);l(t)}catch(t){l({data:[],meta:{itemsPerPage:e.limit||10,totalItems:0,currentPage:e.page||1,totalPages:0,sortBy:[],searchBy:[],search:"",select:[]},links:{current:""}})}finally{o(!1)}},y=e=>{p(e),x(!0)},v=async()=>{if(u){b(!0);try{await i.O.deleteRole(u.role_id),n&&f({page:n.meta.currentPage,limit:n.meta.itemsPerPage}),x(!1),p(null)}catch(e){m("Failed to delete role")}finally{b(!1)}}},j=[{key:"name",label:"Role Name",sortable:!0,render:e=>(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 capitalize",children:e})},{key:"description",label:"Description",render:e=>(0,a.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:e||"No description"})},{key:"created_at",label:"Created Date",sortable:!0,render:e=>(0,a.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:e?new Date(e).toLocaleDateString():"N/A"})},{key:"actions",label:"Actions",render:(e,r)=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>t(r),className:"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 p-1 rounded hover:bg-indigo-50 dark:hover:bg-indigo-900",title:"Edit role",children:(0,a.jsx)("i",{className:"ri-edit-line"})}),(0,a.jsx)("button",{onClick:()=>y(r),className:"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 p-1 rounded hover:bg-red-50 dark:hover:bg-red-900",title:"Delete role",children:(0,a.jsx)("i",{className:"ri-delete-bin-line"})})]})}];return(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:"Roles"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Manage user roles and their permissions."})]}),(0,a.jsx)("div",{className:"flex space-x-3 place-content-start",children:(0,a.jsx)("div",{className:"relative",children:(0,a.jsxs)("button",{onClick:r,className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-900",children:[(0,a.jsx)("div",{className:"w-5 h-5 flex items-center justify-center mr-2",children:(0,a.jsx)("i",{className:"ri-add-line"})}),"Add Role"]})})})]})}),c&&(0,a.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded",children:c}),(0,a.jsx)(k.A,{columns:j,data:n,loading:d,onQueryChange:f,searchPlaceholder:"Search roles by name or description..."}),(0,a.jsx)(w,{isOpen:g,onClose:()=>{x(!1),p(null)},onConfirm:v,title:"Delete Role",message:u?(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"mb-2",children:["Are you sure you want to delete the role ",(0,a.jsx)("strong",{children:u.name}),"?"]}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"This action cannot be undone. Users with this role will lose their associated permissions."})]}):"Are you sure you want to delete this role?",confirmText:"Yes, Delete Role",cancelText:"Cancel",confirmVariant:"danger",loading:h})]})},z=e=>{let{onEditPermission:t,onCreatePermission:r}=e,[i,l]=(0,s.useState)(null),[d,o]=(0,s.useState)(!0),[c,m]=(0,s.useState)(null);(0,s.useEffect)(()=>{g({page:1,limit:10})},[]);let g=async e=>{try{o(!0);let t=await n.p.getPermissions(e);l(t)}catch(t){l({data:[],meta:{itemsPerPage:e.limit||10,totalItems:0,currentPage:e.page||1,totalPages:0,sortBy:[],searchBy:[],search:"",select:[]},links:{current:""}})}finally{o(!1)}},x=[{key:"name",label:"Permission Name",sortable:!0,render:e=>(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:e.replace(/[_:]/g," ").replace(/\b\w/g,e=>e.toUpperCase())})},{key:"description",label:"Description",render:e=>(0,a.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:e||"No description"})},{key:"category",label:"Category",sortable:!0,render:e=>(0,a.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200",children:e})},{key:"roles",label:"Assigned Roles",render:(e,t)=>(0,a.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:t.roles&&t.roles.length>0?t.roles.map(e=>e.name).join(", "):"None"})},{key:"created_at",label:"Created Date",sortable:!0,render:e=>(0,a.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:e?new Date(e).toLocaleDateString():"N/A"})}];return(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:"Permissions"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Manage system permissions and their assignments."})]}),r&&(0,a.jsx)("div",{className:"flex space-x-3 place-content-start",children:(0,a.jsx)("div",{className:"relative",children:(0,a.jsxs)("button",{onClick:r,className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-900",children:[(0,a.jsx)("div",{className:"w-5 h-5 flex items-center justify-center mr-2",children:(0,a.jsx)("i",{className:"ri-add-line"})}),"Add Permission"]})})})]})}),c&&(0,a.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded",children:c}),(0,a.jsx)(k.A,{columns:x,data:i,loading:d,onQueryChange:g,searchPlaceholder:"Search permissions by name, description, or category..."})]})},S=e=>{let{onEditDepartment:t,onCreateDepartment:r}=e,[n,i]=(0,s.useState)([]),[l,d]=(0,s.useState)(!0),[c,m]=(0,s.useState)(null),[g,x]=(0,s.useState)(!1),[u,p]=(0,s.useState)(null),[h,b]=(0,s.useState)(!1);(0,s.useEffect)(()=>{f()},[]);let f=async()=>{try{d(!0),m(null);let e=await o.getAllDepartments();Array.isArray(e)?i(e):(i([]),m("Invalid data format received from server"))}catch(e){m("Failed to load departments"),i([])}finally{d(!1)}},y=e=>{p(e),x(!0)},v=async()=>{if(u)try{b(!0),await o.deleteDepartment(u.department_id),i(e=>e.filter(e=>e.department_id!==u.department_id)),x(!1),p(null)}catch(e){m("Failed to delete department")}finally{b(!1)}},j=Array.isArray(n)?n:[],N={data:j,meta:{itemsPerPage:j.length,totalItems:j.length,currentPage:1,totalPages:1,sortBy:[["created_at","DESC"]],searchBy:["name","code","email"],search:"",select:[],filter:{}},links:{first:"",previous:"",current:"",next:"",last:""}},_=[{key:"code",label:"Code",render:e=>(0,a.jsx)("span",{className:"font-mono text-sm font-medium text-gray-900 dark:text-gray-100",children:e})},{key:"name",label:"Name",render:e=>(0,a.jsx)("span",{className:"font-medium text-gray-900 dark:text-gray-100",children:e})},{key:"description",label:"Description",render:e=>(0,a.jsx)("span",{className:"text-gray-600 dark:text-gray-400 max-w-xs truncate",children:e})},{key:"email",label:"Email",render:e=>(0,a.jsx)("span",{className:"text-blue-600 dark:text-blue-400",children:e})},{key:"created_at",label:"Created",render:e=>(0,a.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:new Date(e).toLocaleDateString()})},{key:"actions",label:"Actions",render:(e,r)=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>t(r),className:"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 p-1 rounded hover:bg-indigo-50 dark:hover:bg-indigo-900",title:"Edit department",children:(0,a.jsx)("i",{className:"ri-edit-line"})}),(0,a.jsx)("button",{onClick:()=>y(r),className:"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 p-1 rounded hover:bg-red-50 dark:hover:bg-red-900",title:"Delete department",children:(0,a.jsx)("i",{className:"ri-delete-bin-line"})})]})}];return c?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"text-red-600 dark:text-red-400 mb-4",children:(0,a.jsx)("i",{className:"ri-error-warning-line text-4xl"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Error Loading Departments"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:c}),(0,a.jsxs)("button",{onClick:f,className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:[(0,a.jsx)("i",{className:"ri-refresh-line mr-2"}),"Try Again"]})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-gray-100",children:"Departments"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Manage organizational departments and their information."})]}),(0,a.jsx)("div",{children:(0,a.jsxs)("button",{onClick:r,className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:[(0,a.jsx)("i",{className:"ri-add-line mr-2"}),"Add Department"]})})]}),(0,a.jsx)(k.A,{columns:_,data:N,loading:l,onQueryChange:()=>{},searchPlaceholder:"Search departments by name, code, or email..."}),(0,a.jsx)(w,{isOpen:g,onClose:()=>{x(!1),p(null)},onConfirm:v,title:"Delete Department",message:u?'Are you sure you want to delete the department "'.concat(u.name,'" (').concat(u.code,")? This action cannot be undone."):"",confirmText:"Delete",cancelText:"Cancel",loading:h,confirmVariant:"danger"})]})},D=e=>{let{onEditOrganization:t,onCreateOrganization:r}=e,[n,i]=(0,s.useState)(null),[l,d]=(0,s.useState)(!0),[o,c]=(0,s.useState)(null),[m,g]=(0,s.useState)(!1),[x,u]=(0,s.useState)(null),[p,h]=(0,s.useState)(!1),[b,f]=(0,s.useState)({page:1,limit:10}),v=(0,s.useCallback)(async e=>{try{d(!0),c(null),f(e);let t=await y.getOrganizations(e);i(t)}catch(r){let t="Failed to load organizations";if(r&&"object"==typeof r)if("code"in r&&"ERR_NETWORK"===r.code)t="Unable to connect to the server. Please check if the backend is running or contact your administrator.";else if("message"in r&&"Network Error"===r.message)t="Unable to connect to the server. Please check if the backend is running or contact your administrator.";else if("response"in r&&r.response&&"object"==typeof r.response){if("status"in r.response){let e=r.response.status;401===e?t="Authentication required. Please log in again.":403===e?t="You do not have permission to view organizations.":500===e?t="Server error. Please try again later.":"data"in r.response&&r.response.data&&"object"==typeof r.response.data&&"message"in r.response.data&&"string"==typeof r.response.data.message&&(t=r.response.data.message)}}else"message"in r&&"string"==typeof r.message&&(t=r.message);c(t),i({data:[],meta:{itemsPerPage:e.limit||10,totalItems:0,currentPage:e.page||1,totalPages:0,sortBy:[],searchBy:[],search:"",select:[],filter:{}},links:{first:"",previous:"",current:"",next:"",last:""}})}finally{d(!1)}},[]);(0,s.useEffect)(()=>{v({page:1,limit:10})},[v]);let j=e=>{u(e),g(!0)},N=async()=>{if(x){h(!0);try{await y.deleteOrganization(x.organization_id),n&&v({page:n.meta.currentPage,limit:n.meta.itemsPerPage}),g(!1),u(null)}catch(e){c("Failed to delete organization")}finally{h(!1)}}},_=[{key:"organization",label:"Organization",sortable:!0,render:(e,t)=>(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("div",{className:"font-medium text-gray-900 dark:text-gray-100",children:t.name}),(0,a.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Reg: ",t.registration_number]})]})},{key:"contact",label:"Contact",render:(e,t)=>(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("div",{className:"text-sm text-blue-600 dark:text-blue-400",children:t.email}),(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:t.phone})]})},{key:"tpin",label:"TPIN",render:e=>(0,a.jsx)("span",{className:"font-mono text-sm font-medium text-gray-900 dark:text-gray-100",children:e})},{key:"website",label:"Website",render:e=>(0,a.jsx)("span",{className:"text-sm text-blue-600 dark:text-blue-400 hover:underline",children:e?(0,a.jsx)("a",{href:e,target:"_blank",rel:"noopener noreferrer",children:e.replace(/^https?:\/\//,"")}):(0,a.jsx)("span",{className:"text-gray-400",children:"-"})})},{key:"date_incorporation",label:"Incorporated",sortable:!0,render:e=>(0,a.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:new Date(e).toLocaleDateString()})},{key:"created_at",label:"Created",sortable:!0,render:e=>(0,a.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:new Date(e).toLocaleDateString()})},{key:"actions",label:"Actions",render:(e,r)=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>t(r),className:"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 p-1 rounded hover:bg-indigo-50 dark:hover:bg-indigo-900",title:"Edit organization",children:(0,a.jsx)("i",{className:"ri-edit-line"})}),(0,a.jsx)("button",{onClick:()=>j(r),className:"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 p-1 rounded hover:bg-red-50 dark:hover:bg-red-900",title:"Delete organization",children:(0,a.jsx)("i",{className:"ri-delete-bin-line"})})]})}];return o?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"text-red-600 dark:text-red-400 mb-4",children:(0,a.jsx)("i",{className:"ri-error-warning-line text-4xl"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Error Loading Organizations"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:o}),(0,a.jsxs)("button",{onClick:()=>v({page:1,limit:10}),className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:[(0,a.jsx)("i",{className:"ri-refresh-line mr-2"}),"Try Again"]})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-gray-100",children:"Organizations"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Manage organizations and their information."})]}),(0,a.jsx)("div",{children:(0,a.jsxs)("button",{onClick:r,className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:[(0,a.jsx)("i",{className:"ri-add-line mr-2"}),"Add Organization"]})})]}),(0,a.jsx)(k.A,{columns:_,data:n,loading:l,onQueryChange:v,searchPlaceholder:"Search organizations by name, registration number, or email..."}),(0,a.jsx)(w,{isOpen:m,onClose:()=>{g(!1),u(null)},onConfirm:N,title:"Delete Organization",message:x?(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"mb-2",children:["Are you sure you want to delete ",(0,a.jsx)("strong",{children:x.name}),"?"]}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"This action cannot be undone. All data associated with this organization will be permanently removed."})]}):"Are you sure you want to delete this organization?",confirmText:"Yes, Delete Organization",cancelText:"Cancel",confirmVariant:"danger",loading:p})]})};function E(){let[e,t]=(0,s.useState)([]),[r,l]=(0,s.useState)([]),[d,c]=(0,s.useState)([]),[m,g]=(0,s.useState)([]),[x,u]=(0,s.useState)(!1),[p,k]=(0,s.useState)(!1),[w,N]=(0,s.useState)(!1),[E,P]=(0,s.useState)(!1),[A,R]=(0,s.useState)(null),[L,O]=(0,s.useState)(null),[U,B]=(0,s.useState)(null),[F,T]=(0,s.useState)(null),[M,q]=(0,s.useState)("users"),[I,W]=(0,s.useState)(""),[G,Y]=(0,s.useState)("");(0,s.useEffect)(()=>{$(),Z(),Q(),V()},[]);let $=async()=>{try{let e=await n.p.getAllPermissions();t(e)}catch(e){}},Q=async()=>{try{let e=await o.getAllDepartments();l(e)}catch(e){}},V=async()=>{try{let e=await y.getAllOrganizations();c(e)}catch(e){}},Z=async()=>{try{let e=await i.O.getRoles({page:1,limit:100});g(e.data)}catch(e){}},H=async e=>{try{let t=await i.O.getRoleWithPermissions(e.role_id);O(t),k(!0)}catch(t){O(e),k(!0)}},K=()=>{u(!1),R(null)},J=()=>{k(!1),O(null)},X=()=>{N(!1),B(null)},ee=()=>{P(!1),T(null)},et=[{id:"users",label:"Users",content:(0,a.jsx)(_,{onEditUser:e=>{R(e),u(!0)},onCreateUser:()=>{R(null),u(!0)}})},{id:"roles",label:"Roles",content:(0,a.jsx)(C,{onEditRole:H,onCreateRole:()=>{O(null),k(!0)}})},{id:"permissions",label:"Permissions",content:(0,a.jsx)(z,{})},{id:"departments",label:"Departments",content:(0,a.jsx)(S,{onEditDepartment:e=>{B(e),N(!0)},onCreateDepartment:()=>{B(null),N(!0)}})},{id:"organizations",label:"Organizations",content:(0,a.jsx)(D,{onEditOrganization:e=>{T(e),P(!0)},onCreateOrganization:()=>{T(null),P(!0)}})}];return(0,a.jsxs)("main",{className:"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-4 sm:p-6",children:[(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[I&&(0,a.jsx)("div",{className:"mb-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 relative animate-in slide-in-from-top-2 duration-300",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"h-5 w-5 text-green-400 dark:text-green-500",children:(0,a.jsx)("svg",{fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})})}),(0,a.jsxs)("div",{className:"ml-3 flex-1",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-green-800 dark:text-green-200 mb-1",children:"Success"}),(0,a.jsx)("p",{className:"text-sm text-green-700 dark:text-green-300",children:I})]}),(0,a.jsx)("div",{className:"ml-4 flex-shrink-0",children:(0,a.jsx)("button",{type:"button",onClick:()=>W(""),className:"inline-flex text-green-400 hover:text-green-600 dark:text-green-500 dark:hover:text-green-300 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 rounded-md p-1 transition-colors duration-200","aria-label":"Dismiss success message",children:(0,a.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})})]})}),G&&(0,a.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 relative animate-in slide-in-from-top-2 duration-300",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"h-5 w-5 text-red-400 dark:text-red-500",children:(0,a.jsx)("svg",{fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})})}),(0,a.jsxs)("div",{className:"ml-3 flex-1",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-red-800 dark:text-red-200 mb-1",children:"Error"}),(0,a.jsx)("p",{className:"text-sm text-red-700 dark:text-red-300",children:G})]}),(0,a.jsx)("div",{className:"ml-4 flex-shrink-0",children:(0,a.jsx)("button",{type:"button",onClick:()=>Y(""),className:"inline-flex text-red-400 hover:text-red-600 dark:text-red-500 dark:hover:text-red-300 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 rounded-md p-1 transition-colors duration-200","aria-label":"Dismiss error message",children:(0,a.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})})]})}),(0,a.jsx)(j,{tabs:et,activeTab:M,onTabChange:e=>{q(e)}})]}),(0,a.jsx)(h,{isOpen:x,onClose:K,onSave:()=>{K()},user:A,roles:m,departments:r,organizations:d,onNavigateToOrganizations:()=>{q("organizations"),x&&(u(!1),R(null))}}),(0,a.jsx)(b.A,{isOpen:p,onClose:J,onSave:function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];J(),W('Role "'.concat(e,'" has been ').concat(t?"updated":"created"," successfully!")),Y(""),setTimeout(()=>{W("")},5e3)},role:L,permissions:e}),(0,a.jsx)(f,{isOpen:w,onClose:X,onSave:e=>{X(),W('Department "'.concat(e.name,'" has been ').concat(U?"updated":"created"," successfully!")),Y(""),setTimeout(()=>{W("")},5e3)},department:U}),(0,a.jsx)(v,{isOpen:E,onClose:ee,onSave:e=>{ee(),W('Organization "'.concat(e.name,'" has been ').concat(F?"updated":"created"," successfully!")),Y(""),setTimeout(()=>{W("")},5e3)},organization:F})]})}},63956:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var a=r(95155);let s=(0,r(12115).forwardRef)((e,t)=>{let{label:r,error:s,helperText:n,variant:i="default",fullWidth:l=!0,className:d="",required:o,disabled:c,options:m,children:g,...x}=e,u="px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white transition-colors duration-200 ".concat(l?"w-full":""," ").concat("small"===i?"py-1.5 text-sm":"py-2"),p="".concat(u," ").concat(s?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"," ").concat(c?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""," ").concat(d);return(0,a.jsxs)("div",{className:"w-full",children:[r&&(0,a.jsxs)("label",{className:"block font-medium text-gray-700 dark:text-gray-300 mb-2 ".concat("small"===i?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"),children:[r,o&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,a.jsx)("select",{ref:t,className:p,disabled:c,required:o,...x,children:m?m.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value)):g}),s&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:s}),n&&!s&&(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:n})]})});s.displayName="Select";let n=s},81614:(e,t,r)=>{Promise.resolve().then(r.bind(r,42165))},84744:(e,t,r)=>{"use strict";r.d(t,{D:()=>n});var a=r(52956),s=r(10012);let n={async getUsers(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(e=>{let[r,a]=e;Array.isArray(a)?a.forEach(e=>t.append("filter.".concat(r),e)):t.set("filter.".concat(r),a)});let r=await a.Gf.get("?".concat(t.toString()));return(0,s.zp)(r)},async getUser(e){let t=await a.Gf.get("/".concat(e));return(0,s.zp)(t)},async getUserById(e){return this.getUser(e)},async getProfile(){let e=await a.Gf.get("/profile");return(0,s.zp)(e)},async createUser(e){let t=await a.Gf.post("",e);return(0,s.zp)(t)},async updateUser(e,t){let r=await a.Gf.put("/".concat(e),t);return(0,s.zp)(r)},async updateProfile(e){let t=await a.Gf.put("/profile",e);return(0,s.zp)(t)},async changePassword(e){let t=await a.Gf.put("/profile/password",e);return(0,s.zp)(t)},async uploadAvatar(e){let t=new FormData;t.append("avatar",e);try{let e=await a.Gf.post("/profile/avatar",t,{headers:{"Content-Type":"multipart/form-data"}});return(0,s.zp)(e)}catch(e){throw e}},async removeAvatar(){let e=await a.Gf.delete("/profile/avatar");return(0,s.zp)(e)},async deleteUser(e){await a.Gf.delete("/".concat(e))}}}},e=>{var t=t=>e(e.s=t);e.O(0,[8122,1987,7724,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(81614)),_N_E=e.O()}]);