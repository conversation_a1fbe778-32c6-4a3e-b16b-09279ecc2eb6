import { apiClient } from '../lib/apiClient';

// Generic Task interface for different types of tasks
export interface GenericTask {
  task_id: string;
  task_type: 'application' | 'complaint' | 'data_breach' | 'evaluation' | 'inspection';
  task_number: string;
  title: string;
  description: string;
  status: string;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  created_at: string;
  updated_at: string;
  assigned_to?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  assigned_by?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  assigned_at?: string;
  due_date?: string;
  metadata?: {
    [key: string]: unknown;
  };
}

// Legacy interface for backward compatibility
export interface TaskAssignmentApplication {
  application_id: string;
  application_number: string;
  status: string;
  created_at: string;
  updated_at: string;
  applicant: {
    applicant_id: string;
    company_name: string;
    first_name: string;
    last_name: string;
  };
  license_category: {
    license_category_id: string;
    name: string;
    license_type: {
      license_type_id: string;
      name: string;
    };
  };
  assignee?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  assigned_at?: string;
}

export interface TaskAssignmentOfficer {
  user_id: string;
  first_name: string;
  last_name: string;
  email: string;
  department?: string;
}

export interface AssignApplicationRequest {
  assignedTo: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  meta: {
    itemCount: number;
    totalItems: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
  links: {
    first: string;
    previous: string;
    next: string;
    last: string;
  };
}

export const taskAssignmentService = {
  // Generic task management methods
  getUnassignedTasks: async (params?: {
    page?: number;
    limit?: number;
    search?: string;
    task_type?: string;
  }) => {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.search) searchParams.append('search', params.search);
    if (params?.task_type) searchParams.append('task_type', params.task_type);

    const queryString = searchParams.toString();
    const url = `/tasks/unassigned${queryString ? `?${queryString}` : ''}`;

    const response = await apiClient.get(url);
    return response.data;
  },

  getAssignedTasks: async (params?: {
    page?: number;
    limit?: number;
    search?: string;
    task_type?: string;
  }) => {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.search) searchParams.append('search', params.search);
    if (params?.task_type) searchParams.append('task_type', params.task_type);

    const queryString = searchParams.toString();
    const url = `/tasks/assigned${queryString ? `?${queryString}` : ''}`;

    const response = await apiClient.get(url);
    return response.data;
  },

  assignTask: async (taskId: string, assignData: { assignedTo: string; comment?: string }) => {
    const response = await apiClient.put(`/tasks/${taskId}/assign`, assignData);
    return response.data;
  },

  getTaskById: async (taskId: string) => {
    const response = await apiClient.get(`/tasks/${taskId}`);
    return response.data;
  },

  // Legacy application-specific methods (for backward compatibility)
  getUnassignedApplications: async (params?: { page?: number; limit?: number; search?: string }) => {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.search) searchParams.append('search', params.search);

    const queryString = searchParams.toString();
    const url = `/applications/unassigned${queryString ? `?${queryString}` : ''}`;
    
    const response = await apiClient.get(url);
    return response.data;
  },

  // Get all applications (including assigned)
  getAllApplications: async (params?: { page?: number; limit?: number; search?: string }) => {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.search) searchParams.append('search', params.search);

    const queryString = searchParams.toString();
    const url = `/applications${queryString ? `?${queryString}` : ''}`;
    
    const response = await apiClient.get(url);
    return response.data;
  },

  // Get applications assigned to current user
  getMyAssignedApplications: async (params?: { page?: number; limit?: number; search?: string }) => {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.search) searchParams.append('search', params.search);

    const queryString = searchParams.toString();
    const url = `/applications/assigned/me${queryString ? `?${queryString}` : ''}`;
    
    const response = await apiClient.get(url);
    return response.data;
  },

  // Get officers for assignment
  getOfficers: async () => {
    try {
      const response = await apiClient.get('/users');
      return response.data;
    } catch (error) {
      console.error('Error fetching officers:', error);
      return { data: [] };
    }
  },

  // Assign application to officer
  assignApplication: async (applicationId: string, assignData: AssignApplicationRequest) => {
    const response = await apiClient.put(`/applications/${applicationId}/assign`, assignData);
    return response.data;
  },

  // Get application details
  getApplication: async (applicationId: string) => {
    const response = await apiClient.get(`/applications/${applicationId}`);
    return response.data;
  },
};