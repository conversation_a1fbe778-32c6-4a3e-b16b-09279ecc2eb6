'use client';

import { useState, useEffect } from 'react';
import { Evaluation, EvaluationCriteria } from '@/services/evaluationService';

interface Application {
  application_id: string;
  application_number: string;
  applicant?: any;
  license_category?: any;
  status: string;
  submitted_at?: string;
}

interface Document {
  document_id: string;
  document_type: string;
  file_name: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  is_required: boolean;
  created_at: string;
}

interface ApplicationEvaluationViewProps {
  application: Application;
  evaluation: Evaluation;
  documents: Document[];
  onSave: (data: {
    criteria: EvaluationCriteria[];
    evaluators_notes: string;
    shareholding_compliance?: boolean;
  }) => void;
  onSubmit: (data: {
    criteria: EvaluationCriteria[];
    evaluators_notes: string;
    shareholding_compliance?: boolean;
  }) => void;
  onBack: () => void;
  saving: boolean;
  licenseType: string;
}

export default function ApplicationEvaluationView({
  application,
  evaluation,
  documents,
  onSave,
  onSubmit,
  onBack,
  saving,
  licenseType,
}: ApplicationEvaluationViewProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const [criteria, setCriteria] = useState<EvaluationCriteria[]>(evaluation.criteria || []);
  const [evaluatorNotes, setEvaluatorNotes] = useState(evaluation.evaluators_notes || '');
  const [shareholdingCompliance, setShareholdingCompliance] = useState<boolean | undefined>(
    evaluation.shareholding_compliance
  );

  const [totalScore, setTotalScore] = useState(0);
  const [recommendation, setRecommendation] = useState<'approve' | 'reject'>('approve');

  useEffect(() => {
    // Calculate total score whenever criteria changes
    if (criteria.length > 0) {
      const weightedSum = criteria.reduce((sum, criterion) => {
        return sum + (criterion.score * criterion.weight);
      }, 0);
      
      const totalWeight = criteria.reduce((sum, criterion) => sum + criterion.weight, 0);
      const calculatedScore = totalWeight > 0 ? (weightedSum / totalWeight) : 0;
      
      setTotalScore(calculatedScore);
      setRecommendation(calculatedScore >= 70 ? 'approve' : 'reject');
    }
  }, [criteria]);

  const handleCriteriaChange = (index: number, field: keyof EvaluationCriteria, value: number) => {
    const updatedCriteria = [...criteria];
    updatedCriteria[index] = { ...updatedCriteria[index], [field]: value };
    setCriteria(updatedCriteria);
  };

  const handleSave = () => {
    onSave({
      criteria,
      evaluators_notes: evaluatorNotes,
      shareholding_compliance: shareholdingCompliance,
    });
  };

  const handleSubmit = () => {
    if (window.confirm('Are you sure you want to submit this evaluation? This action cannot be undone.')) {
      onSubmit({
        criteria,
        evaluators_notes: evaluatorNotes,
        shareholding_compliance: shareholdingCompliance,
      });
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getDocumentIcon = (mimeType: string): string => {
    if (mimeType.includes('pdf')) return 'ri-file-pdf-line';
    if (mimeType.includes('word')) return 'ri-file-word-line';
    if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return 'ri-file-excel-line';
    if (mimeType.includes('image')) return 'ri-image-line';
    return 'ri-file-line';
  };

  const tabs = [
    { id: 'overview', label: 'Application Overview', icon: 'ri-file-list-line' },
    { id: 'documents', label: 'Documents', icon: 'ri-folder-line' },
    { id: 'evaluation', label: 'Evaluation', icon: 'ri-star-line' },
  ];

  const isCompleted = evaluation.status === 'completed';

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center space-x-4">
              <button
                type="button"
                onClick={onBack}
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              >
                <i className="ri-arrow-left-line mr-2"></i>
                Back
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  Evaluate Application
                </h1>
                <p className="text-sm text-gray-600">
                  {application.application_number} • {application.applicant?.name}
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                isCompleted 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-yellow-100 text-yellow-800'
              }`}>
                {isCompleted ? 'Completed' : 'Draft'}
              </div>
              
              <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                recommendation === 'approve' 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                Score: {totalScore.toFixed(1)}% • {recommendation.toUpperCase()}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                type="button"
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeTab === tab.id
                    ? 'border-primary text-primary'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <i className={tab.icon}></i>
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'overview' && (
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Application Details</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700">Application Number</label>
                <p className="mt-1 text-sm text-gray-900">{application.application_number}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Applicant Name</label>
                <p className="mt-1 text-sm text-gray-900">{application.applicant?.name || 'N/A'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">License Category</label>
                <p className="mt-1 text-sm text-gray-900">{application.license_category?.name || 'N/A'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Status</label>
                <p className="mt-1 text-sm text-gray-900">{application.status}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Submitted Date</label>
                <p className="mt-1 text-sm text-gray-900">
                  {application.submitted_at 
                    ? new Date(application.submitted_at).toLocaleDateString()
                    : 'Not submitted'
                  }
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Business Registration</label>
                <p className="mt-1 text-sm text-gray-900">
                  {application.applicant?.business_registration_number || 'N/A'}
                </p>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'documents' && (
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Application Documents</h3>
            {documents.length > 0 ? (
              <div className="space-y-3">
                {documents.map((doc) => (
                  <div key={doc.document_id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <i className={`${getDocumentIcon(doc.mime_type)} text-2xl text-gray-400`}></i>
                      <div>
                        <p className="text-sm font-medium text-gray-900">{doc.file_name}</p>
                        <p className="text-xs text-gray-500">
                          {doc.document_type} • {formatFileSize(doc.file_size)} • 
                          {new Date(doc.created_at).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {doc.is_required && (
                        <span className="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded">
                          Required
                        </span>
                      )}
                      <button
                        type="button"
                        className="text-primary hover:text-primary-dark text-sm font-medium"
                        onClick={() => window.open(`/api/documents/${doc.document_id}/download`, '_blank')}
                      >
                        View
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <i className="ri-folder-open-line text-4xl text-gray-400 mb-4"></i>
                <p className="text-gray-500">No documents uploaded for this application.</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'evaluation' && (
          <div className="space-y-6">
            {/* Evaluation Criteria */}
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Evaluation Criteria</h3>
              <div className="space-y-4">
                {criteria.map((criterion, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-sm font-medium text-gray-900 capitalize">
                        {criterion.category.replace(/_/g, ' ')} - {criterion.subcategory.replace(/_/g, ' ')}
                      </h4>
                      <span className="text-xs text-gray-500">
                        Weight: {(criterion.weight * 100).toFixed(0)}%
                      </span>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="flex-1">
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Score (0-100)
                        </label>
                        <input
                          type="number"
                          min="0"
                          max="100"
                          value={criterion.score}
                          onChange={(e) => handleCriteriaChange(index, 'score', parseFloat(e.target.value) || 0)}
                          disabled={isCompleted}
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm disabled:bg-gray-100"
                        />
                      </div>
                      {criterion.max_marks && (
                        <div className="flex-1">
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            Awarded Marks (Max: {criterion.max_marks})
                          </label>
                          <input
                            type="number"
                            min="0"
                            max={criterion.max_marks}
                            value={criterion.awarded_marks || 0}
                            onChange={(e) => handleCriteriaChange(index, 'awarded_marks', parseFloat(e.target.value) || 0)}
                            disabled={isCompleted}
                            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm disabled:bg-gray-100"
                          />
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Evaluation Summary */}
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Evaluation Summary</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <p className="text-2xl font-bold text-gray-900">{totalScore.toFixed(1)}%</p>
                  <p className="text-sm text-gray-600">Total Score</p>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <p className="text-2xl font-bold text-gray-900">70%</p>
                  <p className="text-sm text-gray-600">Pass Threshold</p>
                </div>
                <div className={`text-center p-4 rounded-lg ${
                  recommendation === 'approve' ? 'bg-green-50' : 'bg-red-50'
                }`}>
                  <p className={`text-2xl font-bold ${
                    recommendation === 'approve' ? 'text-green-900' : 'text-red-900'
                  }`}>
                    {recommendation.toUpperCase()}
                  </p>
                  <p className="text-sm text-gray-600">Recommendation</p>
                </div>
              </div>

              {/* Shareholding Compliance */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Shareholding Compliance
                </label>
                <div className="flex items-center space-x-4">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="shareholding"
                      checked={shareholdingCompliance === true}
                      onChange={() => setShareholdingCompliance(true)}
                      disabled={isCompleted}
                      className="focus:ring-primary h-4 w-4 text-primary border-gray-300"
                    />
                    <span className="ml-2 text-sm text-gray-700">Compliant</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="shareholding"
                      checked={shareholdingCompliance === false}
                      onChange={() => setShareholdingCompliance(false)}
                      disabled={isCompleted}
                      className="focus:ring-primary h-4 w-4 text-primary border-gray-300"
                    />
                    <span className="ml-2 text-sm text-gray-700">Non-compliant</span>
                  </label>
                </div>
              </div>

              {/* Evaluator Notes */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Evaluator Notes
                </label>
                <textarea
                  rows={4}
                  value={evaluatorNotes}
                  onChange={(e) => setEvaluatorNotes(e.target.value)}
                  disabled={isCompleted}
                  placeholder="Enter your evaluation notes and comments..."
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm disabled:bg-gray-100"
                />
              </div>

              {/* Action Buttons */}
              {!isCompleted && (
                <div className="flex items-center justify-end space-x-3">
                  <button
                    type="button"
                    onClick={handleSave}
                    disabled={saving}
                    className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50"
                  >
                    {saving ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
                        Saving...
                      </>
                    ) : (
                      <>
                        <i className="ri-save-line mr-2"></i>
                        Save Draft
                      </>
                    )}
                  </button>
                  <button
                    type="button"
                    onClick={handleSubmit}
                    disabled={saving}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50"
                  >
                    {saving ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Submitting...
                      </>
                    ) : (
                      <>
                        <i className="ri-check-line mr-2"></i>
                        Submit Evaluation
                      </>
                    )}
                  </button>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
