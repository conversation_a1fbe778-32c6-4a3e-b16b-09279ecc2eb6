(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{16679:()=>{},30347:()=>{},31297:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,63924,23)),Promise.resolve().then(r.bind(r,13568)),Promise.resolve().then(r.t.bind(r,30347,23)),Promise.resolve().then(r.bind(r,58818)),Promise.resolve().then(r.bind(r,70933)),Promise.resolve().then(r.t.bind(r,58974,23))},36093:(e,t,r)=>{"use strict";r.d(t,{M:()=>l,o:()=>d});var s=r(95155),a=r(12115),i=r(35695),n=r(94469);let o=(0,a.createContext)(void 0),l=()=>{let e=(0,a.useContext)(o);if(!e)throw Error("useLoading must be used within a LoadingProvider");return e},d=e=>{let{children:t}=e,[r,l]=(0,a.useState)(!1),[d,c]=(0,a.useState)("Loading..."),u=(0,i.usePathname)();return(0,a.useEffect)(()=>{l(!0),c("Loading page..."),setTimeout(()=>{l(!1)},300)},[u]),(0,s.jsxs)(o.Provider,{value:{isLoading:r,setLoading:e=>{l(e)},showLoader:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Loading...";c(e),l(!0)},hideLoader:()=>{l(!1)}},children:[t,r&&(0,s.jsx)("div",{className:"fixed inset-0 bg-white dark:bg-gray-900 bg-opacity-80 dark:bg-opacity-80 backdrop-blur-sm z-50 flex items-center justify-center",children:(0,s.jsx)(n.A,{message:d})})]})}},58818:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var s=r(95155),a=r(40283),i=r(36093),n=r(89807),o=r(66910),l=r(94469),d=r(12115);function c(e){let{children:t,fallback:r=null}=e,[a,i]=(0,d.useState)(!1);return((0,d.useEffect)(()=>{i(!0)},[]),a)?(0,s.jsx)(s.Fragment,{children:t}):(0,s.jsx)(s.Fragment,{children:r})}function u(e){let{children:t}=e;return(0,s.jsx)(c,{fallback:(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center",children:(0,s.jsx)(l.A,{message:"Initializing application..."})}),children:(0,s.jsx)(n.N,{children:(0,s.jsx)(i.o,{children:(0,s.jsx)(o.t,{children:(0,s.jsx)(a.O,{children:t})})})})})}},58974:()=>{},63924:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_549949",variable:"__variable_549949"}},66910:(e,t,r)=>{"use strict";r.d(t,{t:()=>o,d:()=>l});var s=r(95155),a=r(12115);let i=e=>{let{message:t,type:r,duration:i=5e3,onClose:n}=e,[o,l]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{let e=setTimeout(()=>{l(!1),setTimeout(n,300)},i);return()=>clearTimeout(e)},[i,n]),(0,s.jsx)("div",{className:"relative max-w-sm w-full border rounded-lg p-4 shadow-lg transition-all duration-300 ".concat(o?"opacity-100 translate-y-0":"opacity-0 -translate-y-2"," ").concat((()=>{switch(r){case"success":return"bg-green-50 border-green-200 text-green-800 dark:bg-green-900/20 dark:border-green-800 dark:text-green-300";case"error":return"bg-red-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-300";case"warning":return"bg-yellow-50 border-yellow-200 text-yellow-800 dark:bg-yellow-900/20 dark:border-yellow-800 dark:text-yellow-300";case"info":return"bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-300";default:return"bg-gray-50 border-gray-200 text-gray-800 dark:bg-gray-900/20 dark:border-gray-800 dark:text-gray-300"}})()),children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("i",{className:"".concat((()=>{switch(r){case"success":return"ri-check-circle-line";case"error":return"ri-error-warning-line";case"warning":return"ri-alert-line";default:return"ri-information-line"}})()," text-lg")})}),(0,s.jsx)("div",{className:"ml-3 flex-1",children:(0,s.jsx)("p",{className:"text-sm font-medium",children:t})}),(0,s.jsx)("div",{className:"ml-4 flex-shrink-0",children:(0,s.jsxs)("button",{type:"button",onClick:()=>{l(!1),setTimeout(n,300)},className:"inline-flex rounded-md p-1.5 hover:bg-black/5 dark:hover:bg-white/5 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-current",children:[(0,s.jsx)("span",{className:"sr-only",children:"Dismiss"}),(0,s.jsx)("i",{className:"ri-close-line text-sm"})]})})]})})};r(16679);let n=(0,a.createContext)(void 0),o=e=>{let{children:t}=e,[r,o]=(0,a.useState)([]),l=(e,t,r)=>{let s={id:Math.random().toString(36).substr(2,9),message:e,type:t,duration:r};o(e=>[...e,s])},d=e=>{o(t=>t.filter(t=>t.id!==e))};return(0,s.jsxs)(n.Provider,{value:{showToast:l,showSuccess:(e,t)=>{l(e,"success",t)},showError:(e,t)=>{l(e,"error",t)},showWarning:(e,t)=>{l(e,"warning",t)},showInfo:(e,t)=>{l(e,"info",t)}},children:[t,(0,s.jsx)("div",{className:"toast-container",children:r.map((e,t)=>(0,s.jsx)("div",{className:"toast-wrapper","data-index":t,"data-toast-index":t,children:(0,s.jsx)(i,{message:e.message,type:e.type,duration:e.duration,onClose:()=>d(e.id)})},e.id))})]})},l=()=>{let e=(0,a.useContext)(n);if(!e)throw Error("useToast must be used within a ToastProvider");return e}},70933:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var s=r(95155),a=r(12115);let i=e=>{let{children:t}=e;return(0,a.useEffect)(()=>{let e=()=>{let e=document.querySelectorAll("img[data-src]"),t=new IntersectionObserver(e=>{e.forEach(e=>{if(e.isIntersecting){let r=e.target;r.src=r.dataset.src||"",r.classList.remove("lazy"),t.unobserve(r)}})});e.forEach(e=>t.observe(e))},t=()=>{document.querySelectorAll(".nav-item, .dashboard-card, .btn-primary").forEach(e=>{e.style.willChange="transform, background-color"}),document.documentElement.style.scrollBehavior="smooth"},r=setTimeout(()=>{e(),t()},100);return()=>{clearTimeout(r),document.querySelectorAll(".nav-item, .dashboard-card, .btn-primary").forEach(e=>{e.style.willChange="auto"})}},[]),(0,s.jsx)(s.Fragment,{children:t})}},89807:(e,t,r)=>{"use strict";r.d(t,{D:()=>o,N:()=>n});var s=r(95155),a=r(12115);let i=(0,a.createContext)();function n(e){let{children:t}=e,[r,n]=(0,a.useState)("system"),[o,l]=(0,a.useState)("light"),[d,c]=(0,a.useState)(!1);(0,a.useEffect)(()=>{c(!0)},[]),(0,a.useEffect)(()=>{if(!d)return;let e=localStorage.getItem("theme");e&&["light","dark","system"].includes(e)?n(e):n("system")},[d]),(0,a.useEffect)(()=>{if(!d)return;let e=()=>{"system"===r?l(window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"):l(r)};if(e(),"system"===r){let t=window.matchMedia("(prefers-color-scheme: dark)");return t.addEventListener("change",e),()=>t.removeEventListener("change",e)}},[r,d]),(0,a.useEffect)(()=>{d&&("dark"===o?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark"))},[o,d]);let u=e=>{n(e),d&&localStorage.setItem("theme",e)};return d?(0,s.jsx)(i.Provider,{value:{theme:r,resolvedTheme:o,setTheme:u,toggleTheme:()=>{u("light"===o?"dark":"light")}},children:t}):(0,s.jsx)(i.Provider,{value:{theme:"light",resolvedTheme:"light",setTheme:()=>{},toggleTheme:()=>{}},children:t})}function o(){let e=(0,a.useContext)(i);if(!e)throw Error("useTheme must be used within a ThemeProvider");return e}},94469:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(95155),a=r(66766);let i=e=>{let{message:t="Loading..."}=e;return(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("div",{className:"relative w-20 h-20 mx-auto",children:[(0,s.jsxs)("svg",{className:"absolute inset-0 animate-spin",viewBox:"0 0 50 50",fill:"none",children:[(0,s.jsx)("defs",{children:(0,s.jsxs)("linearGradient",{id:"fadeGradient",x1:"0%",y1:"0%",x2:"100%",y2:"0%",children:[(0,s.jsx)("stop",{offset:"0%",stopColor:"#dc2626",stopOpacity:"0"}),(0,s.jsx)("stop",{offset:"20%",stopColor:"#dc2626",stopOpacity:"1"}),(0,s.jsx)("stop",{offset:"80%",stopColor:"#dc2626",stopOpacity:"1"}),(0,s.jsx)("stop",{offset:"100%",stopColor:"#dc2626",stopOpacity:"0"})]})}),(0,s.jsx)("circle",{cx:"25",cy:"25",r:"20",stroke:"rgba(255, 255, 255, 0.0)",strokeWidth:"2"}),(0,s.jsx)("circle",{cx:"25",cy:"25",r:"20",stroke:"url(#fadeGradient)",strokeWidth:"1",strokeDasharray:"70",strokeDashoffset:"10",fill:"none"})]}),(0,s.jsx)(a.default,{src:"/images/macra-logo.png",alt:"MACRA Logo",width:40,height:40,className:"object-contain absolute inset-0 h-10 w-10 m-auto animate-fadeLoop"})]}),(0,s.jsx)("p",{className:"mt-4 text-gray-600",children:t})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[8513,7690,6462,3255,8122,6766,8006,283,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(31297)),_N_E=e.O()}]);