{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/services/data-breach/dataBreachService.ts"], "sourcesContent": ["import { apiClient } from '@/lib/apiClient';\r\nimport { processApiResponse } from '@/lib/authUtils';\r\n\r\n// Types following backend entity structure\r\nexport interface DataBreachReport {\r\n  report_id: string;\r\n  report_number: string;\r\n  reporter_id: string;\r\n  title: string;\r\n  description: string;\r\n  category: DataBreachCategory;\r\n  severity: DataBreachSeverity;\r\n  status: DataBreachStatus;\r\n  priority: DataBreachPriority;\r\n  incident_date: string;\r\n  organization_involved: string;\r\n  affected_data_types?: string;\r\n  contact_attempts?: string;\r\n  assigned_to?: string;\r\n  resolution?: string;\r\n  internal_notes?: string;\r\n  resolved_at?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  deleted_at?: string;\r\n  created_by?: string;\r\n  updated_by?: string;\r\n\r\n  // Related data\r\n  reporter?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n\r\n  assignee?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n\r\n  attachments?: DataBreachReportAttachment[];\r\n  status_history?: DataBreachReportStatusHistory[];\r\n}\r\n\r\nexport interface DataBreachReportAttachment {\r\n  attachment_id: string;\r\n  report_id: string;\r\n  file_name: string;\r\n  file_type: string;\r\n  file_size: number;\r\n  file_path: string;\r\n  uploaded_at: string;\r\n  uploaded_by: string;\r\n}\r\n\r\nexport interface DataBreachReportStatusHistory {\r\n  history_id: string;\r\n  report_id: string;\r\n  status: DataBreachStatus;\r\n  comment?: string;\r\n  created_at: string;\r\n  created_by: string;\r\n}\r\n\r\n// Enums matching backend\r\nexport enum DataBreachCategory {\r\n  PERSONAL_DATA = 'Personal Data',\r\n  FINANCIAL_DATA = 'Financial Data',\r\n  HEALTH_DATA = 'Health Data',\r\n  TECHNICAL_DATA = 'Technical Data',\r\n  COMMUNICATION_DATA = 'Communication Data',\r\n  OTHER = 'Other',\r\n}\r\n\r\nexport enum DataBreachSeverity {\r\n  LOW = 'low',\r\n  MEDIUM = 'medium',\r\n  HIGH = 'high',\r\n  CRITICAL = 'critical',\r\n}\r\n\r\nexport enum DataBreachStatus {\r\n  SUBMITTED = 'submitted',\r\n  UNDER_REVIEW = 'under_review',\r\n  INVESTIGATING = 'investigating',\r\n  RESOLVED = 'resolved',\r\n  CLOSED = 'closed',\r\n}\r\n\r\nexport enum DataBreachPriority {\r\n  LOW = 'low',\r\n  MEDIUM = 'medium',\r\n  HIGH = 'high',\r\n  URGENT = 'urgent',\r\n}\r\n\r\nexport interface CreateDataBreachReportData {\r\n  title: string;\r\n  description: string;\r\n  category: DataBreachCategory;\r\n  severity: DataBreachSeverity;\r\n  priority?: DataBreachPriority;\r\n  incident_date: string;\r\n  organization_involved: string;\r\n  affected_data_types?: string;\r\n  contact_attempts?: string;\r\n  attachments?: File[];\r\n}\r\n\r\nexport interface UpdateDataBreachReportData {\r\n  title?: string;\r\n  description?: string;\r\n  category?: DataBreachCategory;\r\n  severity?: DataBreachSeverity;\r\n  status?: DataBreachStatus;\r\n  priority?: DataBreachPriority;\r\n  incident_date?: string;\r\n  organization_involved?: string;\r\n  affected_data_types?: string;\r\n  contact_attempts?: string;\r\n  assigned_to?: string;\r\n  resolution?: string;\r\n  internal_notes?: string;\r\n  resolved_at?: string;\r\n}\r\n\r\n// Pagination interfaces following user service pattern\r\nexport interface PaginatedResponse<T> {\r\n  data: T[];\r\n  meta: {\r\n    itemsPerPage: number;\r\n    totalItems: number;\r\n    currentPage: number;\r\n    totalPages: number;\r\n    sortBy: string[][];\r\n    searchBy: string[];\r\n    search: string;\r\n    filter: Record<string, string | string[]>;\r\n  };\r\n  links: {\r\n    first: string;\r\n    previous: string;\r\n    current: string;\r\n    next: string;\r\n    last: string;\r\n  };\r\n}\r\n\r\nexport interface PaginateQuery {\r\n  page?: number;\r\n  limit?: number;\r\n  sortBy?: string[];\r\n  searchBy?: string[];\r\n  search?: string;\r\n  filter?: Record<string, string | string[]>;\r\n}\r\n\r\nexport type DataBreachReportsResponse = PaginatedResponse<DataBreachReport>;\r\n\r\nexport const dataBreachService = {\r\n  // Create new report\r\n  async createReport(data: CreateDataBreachReportData): Promise<DataBreachReport> {\r\n    try {\r\n      console.log('🔄 Creating data breach report:', {\r\n        title: data.title,\r\n        category: data.category,\r\n        severity: data.severity,\r\n        hasAttachments: data.attachments && data.attachments.length > 0\r\n      });\r\n\r\n      const formData = new FormData();\r\n      formData.append('title', data.title);\r\n      formData.append('description', data.description);\r\n      formData.append('category', data.category);\r\n      formData.append('severity', data.severity);\r\n      formData.append('incident_date', data.incident_date);\r\n      formData.append('organization_involved', data.organization_involved);\r\n\r\n      if (data.priority) {\r\n        formData.append('priority', data.priority);\r\n      }\r\n\r\n      if (data.affected_data_types) {\r\n        formData.append('affected_data_types', data.affected_data_types);\r\n      }\r\n\r\n      if (data.contact_attempts) {\r\n        formData.append('contact_attempts', data.contact_attempts);\r\n      }\r\n\r\n      // Add attachments if provided\r\n      if (data.attachments && data.attachments.length > 0) {\r\n        data.attachments.forEach((file) => {\r\n          formData.append('attachments', file);\r\n        });\r\n      }\r\n\r\n      const response = await apiClient.post('/data-breach-reports', formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data',\r\n        },\r\n      });\r\n\r\n      console.log('✅ Data breach report created successfully:', response.data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('❌ Error creating data breach report:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get report by ID\r\n  async getReportById(reportId: string): Promise<DataBreachReport> {\r\n    try {\r\n      console.log('🔄 Fetching data breach report by ID:', reportId);\r\n\r\n      const response = await apiClient.get(`/data-breach-reports/${reportId}`);\r\n\r\n      console.log('✅ Data breach report fetched successfully:', response.data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('❌ Error fetching data breach report:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Update report status\r\n  async updateStatus(reportId: string, status: string, comment?: string): Promise<DataBreachReport> {\r\n    try {\r\n      console.log('🔄 Updating data breach report status:', { reportId, status, comment });\r\n\r\n      const response = await apiClient.put(`/data-breach-reports/${reportId}/status`, {\r\n        status,\r\n        comment\r\n      });\r\n\r\n      console.log('✅ Data breach report status updated successfully:', response.data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('❌ Error updating data breach report status:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Assign report to officer\r\n  async assignReport(reportId: string, assignedTo: string): Promise<DataBreachReport> {\r\n    try {\r\n      console.log('🔄 Assigning data breach report:', { reportId, assignedTo });\r\n\r\n      const response = await apiClient.put(`/data-breach-reports/${reportId}/assign`, {\r\n        assigned_to: assignedTo\r\n      });\r\n\r\n      console.log('✅ Data breach report assigned successfully:', response.data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('❌ Error assigning data breach report:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get all reports with pagination\r\n  async getReports(query: PaginateQuery = {}): Promise<DataBreachReportsResponse> {\r\n    const params = new URLSearchParams();\r\n\r\n    if (query.page) params.set('page', query.page.toString());\r\n    if (query.limit) params.set('limit', query.limit.toString());\r\n    if (query.search) params.set('search', query.search);\r\n    if (query.sortBy) {\r\n      query.sortBy.forEach(sort => params.append('sortBy', sort));\r\n    }\r\n    if (query.searchBy) {\r\n      query.searchBy.forEach(search => params.append('searchBy', search));\r\n    }\r\n    if (query.filter) {\r\n      Object.entries(query.filter).forEach(([key, value]) => {\r\n        if (Array.isArray(value)) {\r\n          value.forEach(v => params.append(`filter.${key}`, v));\r\n        } else {\r\n          params.set(`filter.${key}`, value);\r\n        }\r\n      });\r\n    }\r\n\r\n    const response = await apiClient.get(`/data-breach-reports?${params.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get report by ID\r\n  async getReport(id: string): Promise<DataBreachReport> {\r\n    const response = await apiClient.get(`/data-breach-reports/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get report by ID (alias for consistency)\r\n  async getReportById(id: string): Promise<DataBreachReport> {\r\n    return this.getReport(id);\r\n  },\r\n\r\n  // Update report\r\n  async updateReport(id: string, data: UpdateDataBreachReportData): Promise<DataBreachReport> {\r\n    const response = await apiClient.put(`/data-breach-reports/${id}`, data);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Delete report\r\n  async deleteReport(id: string): Promise<void> {\r\n    await apiClient.delete(`/data-breach-reports/${id}`);\r\n  },\r\n\r\n  // Update report status (for staff)\r\n  async updateReportStatus(id: string, status: DataBreachStatus, comment?: string): Promise<DataBreachReport> {\r\n    const response = await apiClient.put(`/data-breach-reports/${id}/status`, {\r\n      status,\r\n      comment\r\n    });\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Add attachment to report\r\n  async addAttachment(id: string, file: File): Promise<DataBreachReportAttachment> {\r\n    const formData = new FormData();\r\n    formData.append('files', file);\r\n\r\n    const response = await apiClient.post(`/data-breach-reports/${id}/attachments`, formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Remove attachment from report\r\n  async removeAttachment(reportId: string, attachmentId: string): Promise<void> {\r\n    await apiClient.delete(`/data-breach-reports/${reportId}/attachments/${attachmentId}`);\r\n  },\r\n\r\n  // Helper methods\r\n  getStatusColor(status: string): string {\r\n    switch (status?.toLowerCase()) {\r\n      case 'submitted': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';\r\n      case 'under_review': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\r\n      case 'investigating': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\r\n      case 'resolved': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\r\n      case 'closed': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\r\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\r\n    }\r\n  },\r\n\r\n  getSeverityColor(severity: string): string {\r\n    switch (severity?.toLowerCase()) {\r\n      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\r\n      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\r\n      case 'high': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\r\n      case 'critical': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';\r\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\r\n    }\r\n  },\r\n\r\n  getStatusOptions(): Array<{ value: string; label: string }> {\r\n    return [\r\n      { value: 'submitted', label: 'Submitted' },\r\n      { value: 'under_review', label: 'Under Review' },\r\n      { value: 'investigating', label: 'Investigating' },\r\n      { value: 'resolved', label: 'Resolved' },\r\n      { value: 'closed', label: 'Closed' }\r\n    ];\r\n  },\r\n\r\n  getCategoryOptions(): Array<{ value: string; label: string }> {\r\n    return [\r\n      { value: 'Personal Data', label: 'Personal Data' },\r\n      { value: 'Financial Data', label: 'Financial Data' },\r\n      { value: 'Health Data', label: 'Health Data' },\r\n      { value: 'Technical Data', label: 'Technical Data' },\r\n      { value: 'Communication Data', label: 'Communication Data' },\r\n      { value: 'Other', label: 'Other' }\r\n    ];\r\n  },\r\n\r\n  getSeverityOptions(): Array<{ value: string; label: string }> {\r\n    return [\r\n      { value: 'low', label: 'Low' },\r\n      { value: 'medium', label: 'Medium' },\r\n      { value: 'high', label: 'High' },\r\n      { value: 'critical', label: 'Critical' }\r\n    ];\r\n  },\r\n\r\n  getPriorityOptions(): Array<{ value: string; label: string }> {\r\n    return [\r\n      { value: 'low', label: 'Low' },\r\n      { value: 'medium', label: 'Medium' },\r\n      { value: 'high', label: 'High' },\r\n      { value: 'urgent', label: 'Urgent' }\r\n    ];\r\n  },\r\n};\r\n\r\n\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAmEO,IAAA,AAAK,4CAAA;;;;;;;WAAA;;AASL,IAAA,AAAK,4CAAA;;;;;WAAA;;AAOL,IAAA,AAAK,0CAAA;;;;;;WAAA;;AAQL,IAAA,AAAK,4CAAA;;;;;WAAA;;AAsEL,MAAM,oBAAoB;IAC/B,oBAAoB;IACpB,MAAM,cAAa,IAAgC;QACjD,IAAI;YACF,QAAQ,GAAG,CAAC,mCAAmC;gBAC7C,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;gBACvB,UAAU,KAAK,QAAQ;gBACvB,gBAAgB,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG;YAChE;YAEA,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,SAAS,KAAK,KAAK;YACnC,SAAS,MAAM,CAAC,eAAe,KAAK,WAAW;YAC/C,SAAS,MAAM,CAAC,YAAY,KAAK,QAAQ;YACzC,SAAS,MAAM,CAAC,YAAY,KAAK,QAAQ;YACzC,SAAS,MAAM,CAAC,iBAAiB,KAAK,aAAa;YACnD,SAAS,MAAM,CAAC,yBAAyB,KAAK,qBAAqB;YAEnE,IAAI,KAAK,QAAQ,EAAE;gBACjB,SAAS,MAAM,CAAC,YAAY,KAAK,QAAQ;YAC3C;YAEA,IAAI,KAAK,mBAAmB,EAAE;gBAC5B,SAAS,MAAM,CAAC,uBAAuB,KAAK,mBAAmB;YACjE;YAEA,IAAI,KAAK,gBAAgB,EAAE;gBACzB,SAAS,MAAM,CAAC,oBAAoB,KAAK,gBAAgB;YAC3D;YAEA,8BAA8B;YAC9B,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG,GAAG;gBACnD,KAAK,WAAW,CAAC,OAAO,CAAC,CAAC;oBACxB,SAAS,MAAM,CAAC,eAAe;gBACjC;YACF;YAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,wBAAwB,UAAU;gBACtE,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,QAAQ,GAAG,CAAC,8CAA8C,SAAS,IAAI;YACvE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM;QACR;IACF;IAEA,mBAAmB;IACnB,MAAM,eAAc,QAAgB;QAClC,IAAI;YACF,QAAQ,GAAG,CAAC,yCAAyC;YAErD,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,UAAU;YAEvE,QAAQ,GAAG,CAAC,8CAA8C,SAAS,IAAI;YACvE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM;QACR;IACF;IAEA,uBAAuB;IACvB,MAAM,cAAa,QAAgB,EAAE,MAAc,EAAE,OAAgB;QACnE,IAAI;YACF,QAAQ,GAAG,CAAC,0CAA0C;gBAAE;gBAAU;gBAAQ;YAAQ;YAElF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,SAAS,OAAO,CAAC,EAAE;gBAC9E;gBACA;YACF;YAEA,QAAQ,GAAG,CAAC,qDAAqD,SAAS,IAAI;YAC9E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+CAA+C;YAC7D,MAAM;QACR;IACF;IAEA,2BAA2B;IAC3B,MAAM,cAAa,QAAgB,EAAE,UAAkB;QACrD,IAAI;YACF,QAAQ,GAAG,CAAC,oCAAoC;gBAAE;gBAAU;YAAW;YAEvE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,SAAS,OAAO,CAAC,EAAE;gBAC9E,aAAa;YACf;YAEA,QAAQ,GAAG,CAAC,+CAA+C,SAAS,IAAI;YACxE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,MAAM;QACR;IACF;IAEA,kCAAkC;IAClC,MAAM,YAAW,QAAuB,CAAC,CAAC;QACxC,MAAM,SAAS,IAAI;QAEnB,IAAI,MAAM,IAAI,EAAE,OAAO,GAAG,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ;QACtD,IAAI,MAAM,KAAK,EAAE,OAAO,GAAG,CAAC,SAAS,MAAM,KAAK,CAAC,QAAQ;QACzD,IAAI,MAAM,MAAM,EAAE,OAAO,GAAG,CAAC,UAAU,MAAM,MAAM;QACnD,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,MAAM,CAAC,OAAO,CAAC,CAAA,OAAQ,OAAO,MAAM,CAAC,UAAU;QACvD;QACA,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAA,SAAU,OAAO,MAAM,CAAC,YAAY;QAC7D;QACA,IAAI,MAAM,MAAM,EAAE;YAChB,OAAO,OAAO,CAAC,MAAM,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAChD,IAAI,MAAM,OAAO,CAAC,QAAQ;oBACxB,MAAM,OAAO,CAAC,CAAA,IAAK,OAAO,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBACpD,OAAO;oBACL,OAAO,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBAC9B;YACF;QACF;QAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,OAAO,QAAQ,IAAI;QAChF,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,mBAAmB;IACnB,MAAM,WAAU,EAAU;QACxB,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,IAAI;QACjE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,2CAA2C;IAC3C,MAAM,eAAc,EAAU;QAC5B,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB;IAEA,gBAAgB;IAChB,MAAM,cAAa,EAAU,EAAE,IAAgC;QAC7D,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,IAAI,EAAE;QACnE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gBAAgB;IAChB,MAAM,cAAa,EAAU;QAC3B,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,qBAAqB,EAAE,IAAI;IACrD;IAEA,mCAAmC;IACnC,MAAM,oBAAmB,EAAU,EAAE,MAAwB,EAAE,OAAgB;QAC7E,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,GAAG,OAAO,CAAC,EAAE;YACxE;YACA;QACF;QACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,2BAA2B;IAC3B,MAAM,eAAc,EAAU,EAAE,IAAU;QACxC,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS;QAEzB,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,CAAC,qBAAqB,EAAE,GAAG,YAAY,CAAC,EAAE,UAAU;YACxF,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gCAAgC;IAChC,MAAM,kBAAiB,QAAgB,EAAE,YAAoB;QAC3D,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,qBAAqB,EAAE,SAAS,aAAa,EAAE,cAAc;IACvF;IAEA,iBAAiB;IACjB,gBAAe,MAAc;QAC3B,OAAQ,QAAQ;YACd,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAiB,OAAO;YAC7B,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,kBAAiB,QAAgB;QAC/B,OAAQ,UAAU;YAChB,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA;QACE,OAAO;YACL;gBAAE,OAAO;gBAAa,OAAO;YAAY;YACzC;gBAAE,OAAO;gBAAgB,OAAO;YAAe;YAC/C;gBAAE,OAAO;gBAAiB,OAAO;YAAgB;YACjD;gBAAE,OAAO;gBAAY,OAAO;YAAW;YACvC;gBAAE,OAAO;gBAAU,OAAO;YAAS;SACpC;IACH;IAEA;QACE,OAAO;YACL;gBAAE,OAAO;gBAAiB,OAAO;YAAgB;YACjD;gBAAE,OAAO;gBAAkB,OAAO;YAAiB;YACnD;gBAAE,OAAO;gBAAe,OAAO;YAAc;YAC7C;gBAAE,OAAO;gBAAkB,OAAO;YAAiB;YACnD;gBAAE,OAAO;gBAAsB,OAAO;YAAqB;YAC3D;gBAAE,OAAO;gBAAS,OAAO;YAAQ;SAClC;IACH;IAEA;QACE,OAAO;YACL;gBAAE,OAAO;gBAAO,OAAO;YAAM;YAC7B;gBAAE,OAAO;gBAAU,OAAO;YAAS;YACnC;gBAAE,OAAO;gBAAQ,OAAO;YAAO;YAC/B;gBAAE,OAAO;gBAAY,OAAO;YAAW;SACxC;IACH;IAEA;QACE,OAAO;YACL;gBAAE,OAAO;gBAAO,OAAO;YAAM;YAC7B;gBAAE,OAAO;gBAAU,OAAO;YAAS;YACnC;gBAAE,OAAO;gBAAQ,OAAO;YAAO;YAC/B;gBAAE,OAAO;gBAAU,OAAO;YAAS;SACpC;IACH;AACF", "debugId": null}}, {"offset": {"line": 339, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/services/data-breach/index.ts"], "sourcesContent": ["export * from './dataBreachService';\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 360, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/customer/ComplaintStatusBar.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\ninterface ComplaintStage {\n  id: string;\n  name: string;\n  description?: string;\n  icon?: string;\n}\n\ninterface ComplaintStatusBarProps {\n  currentStage: number; // 0-based index\n  stages: ComplaintStage[];\n  status?: 'submitted' | 'under_review' | 'investigating' | 'resolved' | 'closed';\n  progressPercentage?: number;\n  showPercentage?: boolean;\n  showStageNames?: boolean;\n  size?: 'sm' | 'md' | 'lg';\n  variant?: 'horizontal' | 'vertical';\n  className?: string;\n}\n\nconst ComplaintStatusBar: React.FC<ComplaintStatusBarProps> = ({\n  currentStage,\n  stages,\n  status = 'submitted',\n  progressPercentage,\n  showPercentage = true,\n  showStageNames = true,\n  size = 'md',\n  variant = 'horizontal',\n  className = ''\n}) => {\n  // Calculate progress percentage if not provided\n  const calculatedProgress = progressPercentage ?? Math.round(((currentStage + 1) / stages.length) * 100);\n  \n  // Get status color\n  const getStatusColor = () => {\n    switch (status) {\n      case 'submitted': return 'bg-blue-500';\n      case 'under_review': return 'bg-yellow-500';\n      case 'investigating': return 'bg-orange-500';\n      case 'resolved': return 'bg-green-500';\n      case 'closed': return 'bg-gray-500';\n      default: return 'bg-gray-400';\n    }\n  };\n\n  // Get status text color\n  const getStatusTextColor = () => {\n    switch (status) {\n      case 'submitted': return 'text-blue-600';\n      case 'under_review': return 'text-yellow-600';\n      case 'investigating': return 'text-orange-600';\n      case 'resolved': return 'text-green-600';\n      case 'closed': return 'text-gray-600';\n      default: return 'text-gray-600';\n    }\n  };\n\n  // Size configurations\n  const sizeClasses = {\n    sm: {\n      stage: 'w-6 h-6 text-xs',\n      bar: 'h-1',\n      text: 'text-xs'\n    },\n    md: {\n      stage: 'w-8 h-8 text-sm',\n      bar: 'h-2',\n      text: 'text-sm'\n    },\n    lg: {\n      stage: 'w-10 h-10 text-base',\n      bar: 'h-3',\n      text: 'text-base'\n    }\n  };\n\n  // Vertical variant\n  if (variant === 'vertical') {\n    return (\n      <div className={`space-y-4 ${className}`}>\n        {stages.map((stage, index) => {\n          const isCompleted = index < currentStage;\n          const isCurrent = index === currentStage;\n\n          return (\n            <div key={stage.id} className=\"flex items-center space-x-3\">\n              {/* Stage Circle */}\n              <div className={`\n                ${sizeClasses[size].stage} rounded-full flex items-center justify-center font-medium\n                ${isCompleted ? `${getStatusColor()} text-white` : \n                  isCurrent ? `border-2 border-current ${getStatusTextColor()} bg-white` :\n                  'bg-gray-200 text-gray-400'}\n              `}>\n                {isCompleted ? (\n                  <i className=\"ri-check-line\"></i>\n                ) : (\n                  <span>{index + 1}</span>\n                )}\n              </div>\n\n              {/* Stage Info */}\n              <div className=\"flex-1\">\n                <div className={`font-medium ${isCurrent ? getStatusTextColor() : isCompleted ? 'text-gray-900' : 'text-gray-400'}`}>\n                  {stage.name}\n                </div>\n                {stage.description && (\n                  <div className={`${sizeClasses[size].text} ${isCurrent ? 'text-gray-600' : 'text-gray-400'}`}>\n                    {stage.description}\n                  </div>\n                )}\n              </div>\n\n              {/* Progress Indicator */}\n              {isCurrent && showPercentage && (\n                <div className={`${sizeClasses[size].text} font-medium ${getStatusTextColor()}`}>\n                  {calculatedProgress}%\n                </div>\n              )}\n            </div>\n          );\n        })}\n      </div>\n    );\n  }\n\n  // Horizontal variant\n  return (\n    <div className={`w-full ${className}`}>\n      {/* Progress Bar */}\n      <div className=\"relative\">\n        <div className={`w-full ${sizeClasses[size].bar} bg-gray-200 rounded-full overflow-hidden`}>\n          <div \n            className={`${sizeClasses[size].bar} ${getStatusColor()} transition-all duration-500 ease-out rounded-full`}\n            style={{ width: `${calculatedProgress}%` }}\n          />\n        </div>\n\n        {/* Stage Markers */}\n        <div className=\"absolute top-0 left-0 w-full flex justify-between items-center\" style={{ transform: 'translateY(-50%)' }}>\n          {stages.map((stage, index) => {\n            const isCompleted = index < currentStage;\n            const isCurrent = index === currentStage;\n            const position = (index / (stages.length - 1)) * 100;\n\n            return (\n              <div \n                key={stage.id}\n                className=\"flex flex-col items-center\"\n                style={{ position: 'absolute', left: `${position}%`, transform: 'translateX(-50%)' }}\n              >\n                {/* Stage Circle */}\n                <div className={`\n                  ${sizeClasses[size].stage} rounded-full flex items-center justify-center font-medium border-2 bg-white\n                  ${isCompleted ? `${getStatusColor().replace('bg-', 'border-')} ${getStatusColor()} text-white` : \n                    isCurrent ? `border-current ${getStatusTextColor()}` :\n                    'border-gray-300 text-gray-400'}\n                `}>\n                  {isCompleted ? (\n                    <i className=\"ri-check-line\"></i>\n                  ) : stage.icon ? (\n                    <i className={stage.icon}></i>\n                  ) : (\n                    <span>{index + 1}</span>\n                  )}\n                </div>\n\n                {/* Stage Name */}\n                {showStageNames && (\n                  <div className={`mt-2 ${sizeClasses[size].text} font-medium text-center max-w-20 ${\n                    isCurrent ? getStatusTextColor() : \n                    isCompleted ? 'text-gray-900' : \n                    'text-gray-400'\n                  }`}>\n                    {stage.name}\n                  </div>\n                )}\n              </div>\n            );\n          })}\n        </div>\n      </div>\n\n      {/* Progress Percentage */}\n      {showPercentage && (\n        <div className=\"flex justify-between items-center mt-8\">\n          <div className={`${sizeClasses[size].text} ${getStatusTextColor()} font-medium`}>\n            Progress: {calculatedProgress}%\n          </div>\n          <div className={`${sizeClasses[size].text} text-gray-500 capitalize`}>\n            Status: {status.replace('_', ' ')}\n          </div>\n        </div>\n      )}\n\n      {/* Current Stage Description */}\n      {showStageNames && stages[currentStage]?.description && (\n        <div className=\"mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg\">\n          <div className={`${sizeClasses[size].text} text-gray-600 dark:text-gray-400`}>\n            <strong>Current Stage:</strong> {stages[currentStage].description}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ComplaintStatusBar;\n\n// Predefined stage configurations for different complaint types\nexport const COMPLAINT_STAGES = {\n  CONSUMER_AFFAIRS: [\n    { id: 'submitted', name: 'Submitted', description: 'Complaint has been received and logged', icon: 'ri-file-text-line' },\n    { id: 'under_review', name: 'Under Review', description: 'Initial review and assessment in progress', icon: 'ri-search-line' },\n    { id: 'investigating', name: 'Investigating', description: 'Detailed investigation and fact-finding', icon: 'ri-spy-line' },\n    { id: 'resolved', name: 'Resolved', description: 'Issue has been resolved and action taken', icon: 'ri-check-double-line' }\n  ],\n  DATA_BREACH: [\n    { id: 'submitted', name: 'Reported', description: 'Data breach report has been received', icon: 'ri-shield-line' },\n    { id: 'under_review', name: 'Assessment', description: 'Assessing severity and impact', icon: 'ri-search-line' },\n    { id: 'investigating', name: 'Investigation', description: 'Investigating the breach and gathering evidence', icon: 'ri-spy-line' },\n    { id: 'resolved', name: 'Resolved', description: 'Breach contained and remediation completed', icon: 'ri-shield-check-line' }\n  ]\n};\n\n// Helper function to get current stage index from status\nexport const getStageIndexFromStatus = (status: string): number => {\n  const statusMap: { [key: string]: number } = {\n    'submitted': 0,\n    'under_review': 1,\n    'investigating': 2,\n    'resolved': 3,\n    'closed': 3\n  };\n  return statusMap[status] || 0;\n};\n"], "names": [], "mappings": ";;;;;;AAAA;;AAuBA,MAAM,qBAAwD,CAAC,EAC7D,YAAY,EACZ,MAAM,EACN,SAAS,WAAW,EACpB,kBAAkB,EAClB,iBAAiB,IAAI,EACrB,iBAAiB,IAAI,EACrB,OAAO,IAAI,EACX,UAAU,YAAY,EACtB,YAAY,EAAE,EACf;IACC,gDAAgD;IAChD,MAAM,qBAAqB,sBAAsB,KAAK,KAAK,CAAC,AAAC,CAAC,eAAe,CAAC,IAAI,OAAO,MAAM,GAAI;IAEnG,mBAAmB;IACnB,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAiB,OAAO;YAC7B,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,wBAAwB;IACxB,MAAM,qBAAqB;QACzB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAiB,OAAO;YAC7B,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,sBAAsB;IACtB,MAAM,cAAc;QAClB,IAAI;YACF,OAAO;YACP,KAAK;YACL,MAAM;QACR;QACA,IAAI;YACF,OAAO;YACP,KAAK;YACL,MAAM;QACR;QACA,IAAI;YACF,OAAO;YACP,KAAK;YACL,MAAM;QACR;IACF;IAEA,mBAAmB;IACnB,IAAI,YAAY,YAAY;QAC1B,qBACE,6LAAC;YAAI,WAAW,CAAC,UAAU,EAAE,WAAW;sBACrC,OAAO,GAAG,CAAC,CAAC,OAAO;gBAClB,MAAM,cAAc,QAAQ;gBAC5B,MAAM,YAAY,UAAU;gBAE5B,qBACE,6LAAC;oBAAmB,WAAU;;sCAE5B,6LAAC;4BAAI,WAAW,CAAC;gBACf,EAAE,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC;gBAC1B,EAAE,cAAc,GAAG,iBAAiB,WAAW,CAAC,GAC9C,YAAY,CAAC,wBAAwB,EAAE,qBAAqB,SAAS,CAAC,GACtE,4BAA4B;cAChC,CAAC;sCACE,4BACC,6LAAC;gCAAE,WAAU;;;;;qDAEb,6LAAC;0CAAM,QAAQ;;;;;;;;;;;sCAKnB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAW,CAAC,YAAY,EAAE,YAAY,uBAAuB,cAAc,kBAAkB,iBAAiB;8CAChH,MAAM,IAAI;;;;;;gCAEZ,MAAM,WAAW,kBAChB,6LAAC;oCAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,YAAY,kBAAkB,iBAAiB;8CACzF,MAAM,WAAW;;;;;;;;;;;;wBAMvB,aAAa,gCACZ,6LAAC;4BAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,EAAE,sBAAsB;;gCAC5E;gCAAmB;;;;;;;;mBA9BhB,MAAM,EAAE;;;;;YAmCtB;;;;;;IAGN;IAEA,qBAAqB;IACrB,qBACE,6LAAC;QAAI,WAAW,CAAC,OAAO,EAAE,WAAW;;0BAEnC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAW,CAAC,OAAO,EAAE,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,yCAAyC,CAAC;kCACxF,cAAA,6LAAC;4BACC,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,iBAAiB,kDAAkD,CAAC;4BAC3G,OAAO;gCAAE,OAAO,GAAG,mBAAmB,CAAC,CAAC;4BAAC;;;;;;;;;;;kCAK7C,6LAAC;wBAAI,WAAU;wBAAiE,OAAO;4BAAE,WAAW;wBAAmB;kCACpH,OAAO,GAAG,CAAC,CAAC,OAAO;4BAClB,MAAM,cAAc,QAAQ;4BAC5B,MAAM,YAAY,UAAU;4BAC5B,MAAM,WAAW,AAAC,QAAQ,CAAC,OAAO,MAAM,GAAG,CAAC,IAAK;4BAEjD,qBACE,6LAAC;gCAEC,WAAU;gCACV,OAAO;oCAAE,UAAU;oCAAY,MAAM,GAAG,SAAS,CAAC,CAAC;oCAAE,WAAW;gCAAmB;;kDAGnF,6LAAC;wCAAI,WAAW,CAAC;kBACf,EAAE,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC;kBAC1B,EAAE,cAAc,GAAG,iBAAiB,OAAO,CAAC,OAAO,WAAW,CAAC,EAAE,iBAAiB,WAAW,CAAC,GAC5F,YAAY,CAAC,eAAe,EAAE,sBAAsB,GACpD,gCAAgC;gBACpC,CAAC;kDACE,4BACC,6LAAC;4CAAE,WAAU;;;;;mDACX,MAAM,IAAI,iBACZ,6LAAC;4CAAE,WAAW,MAAM,IAAI;;;;;iEAExB,6LAAC;sDAAM,QAAQ;;;;;;;;;;;oCAKlB,gCACC,6LAAC;wCAAI,WAAW,CAAC,KAAK,EAAE,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,kCAAkC,EAC/E,YAAY,uBACZ,cAAc,kBACd,iBACA;kDACC,MAAM,IAAI;;;;;;;+BA3BV,MAAM,EAAE;;;;;wBAgCnB;;;;;;;;;;;;YAKH,gCACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,qBAAqB,YAAY,CAAC;;4BAAE;4BACpE;4BAAmB;;;;;;;kCAEhC,6LAAC;wBAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,yBAAyB,CAAC;;4BAAE;4BAC3D,OAAO,OAAO,CAAC,KAAK;;;;;;;;;;;;;YAMlC,kBAAkB,MAAM,CAAC,aAAa,EAAE,6BACvC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,iCAAiC,CAAC;;sCAC1E,6LAAC;sCAAO;;;;;;wBAAuB;wBAAE,MAAM,CAAC,aAAa,CAAC,WAAW;;;;;;;;;;;;;;;;;;AAM7E;KAzLM;uCA2LS;AAGR,MAAM,mBAAmB;IAC9B,kBAAkB;QAChB;YAAE,IAAI;YAAa,MAAM;YAAa,aAAa;YAA0C,MAAM;QAAoB;QACvH;YAAE,IAAI;YAAgB,MAAM;YAAgB,aAAa;YAA6C,MAAM;QAAiB;QAC7H;YAAE,IAAI;YAAiB,MAAM;YAAiB,aAAa;YAA2C,MAAM;QAAc;QAC1H;YAAE,IAAI;YAAY,MAAM;YAAY,aAAa;YAA4C,MAAM;QAAuB;KAC3H;IACD,aAAa;QACX;YAAE,IAAI;YAAa,MAAM;YAAY,aAAa;YAAwC,MAAM;QAAiB;QACjH;YAAE,IAAI;YAAgB,MAAM;YAAc,aAAa;YAAiC,MAAM;QAAiB;QAC/G;YAAE,IAAI;YAAiB,MAAM;YAAiB,aAAa;YAAmD,MAAM;QAAc;QAClI;YAAE,IAAI;YAAY,MAAM;YAAY,aAAa;YAA8C,MAAM;QAAuB;KAC7H;AACH;AAGO,MAAM,0BAA0B,CAAC;IACtC,MAAM,YAAuC;QAC3C,aAAa;QACb,gBAAgB;QAChB,iBAAiB;QACjB,YAAY;QACZ,UAAU;IACZ;IACA,OAAO,SAAS,CAAC,OAAO,IAAI;AAC9B", "debugId": null}}, {"offset": {"line": 742, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/data-breach/DataBreachViewModal.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { dataBreachService, DataBreachReport } from '@/services/data-breach';\nimport { useToast } from '@/contexts/ToastContext';\nimport ComplaintStatusBar, { COMPLAINT_STAGES, getStageIndexFromStatus } from '@/components/customer/ComplaintStatusBar';\n\ninterface DataBreachViewModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  reportId: string | null;\n  onUpdate?: () => void;\n}\n\nconst DataBreachViewModal: React.FC<DataBreachViewModalProps> = ({\n  isOpen,\n  onClose,\n  reportId,\n  onUpdate\n}) => {\n  const { showSuccess, showError } = useToast();\n  const [report, setReport] = useState<DataBreachReport | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [activeTab, setActiveTab] = useState('overview');\n  const [isAssigning, setIsAssigning] = useState(false);\n  const [assignedOfficer, setAssignedOfficer] = useState('');\n\n  useEffect(() => {\n    if (isOpen && reportId) {\n      fetchReportDetails();\n    }\n  }, [isOpen, reportId]);\n\n  const fetchReportDetails = async () => {\n    if (!reportId) return;\n    \n    setLoading(true);\n    setError(null);\n    \n    try {\n      const response = await dataBreachService.getReportById(reportId);\n      setReport(response);\n      setAssignedOfficer(response.assigned_to || '');\n    } catch (err: unknown) {\n      console.error('Error fetching report details:', err);\n      const errorMessage = err instanceof Error ? err.message : 'Unknown error';\n      setError(`Failed to load report details: ${errorMessage}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleAssignOfficer = async () => {\n    if (!reportId || !assignedOfficer.trim()) {\n      showError('Please enter an officer name');\n      return;\n    }\n\n    setIsAssigning(true);\n    try {\n      await dataBreachService.assignReport(reportId, assignedOfficer);\n      showSuccess('Officer assigned successfully');\n      \n      // Update local state\n      if (report) {\n        setReport({ ...report, assigned_to: assignedOfficer });\n      }\n      \n      if (onUpdate) onUpdate();\n    } catch (err: unknown) {\n      console.error('Error assigning officer:', err);\n      const errorMessage = err instanceof Error ? err.message : 'Unknown error';\n      showError(`Failed to assign officer: ${errorMessage}`);\n    } finally {\n      setIsAssigning(false);\n    }\n  };\n\n  const handleCloseReport = async () => {\n    if (!reportId) return;\n\n    try {\n      await dataBreachService.updateStatus(reportId, 'closed');\n      showSuccess('Report closed successfully');\n      \n      // Update local state\n      if (report) {\n        setReport({ ...report, status: 'closed' });\n      }\n      \n      if (onUpdate) onUpdate();\n    } catch (err: unknown) {\n      console.error('Error closing report:', err);\n      const errorMessage = err instanceof Error ? err.message : 'Unknown error';\n      showError(`Failed to close report: ${errorMessage}`);\n    }\n  };\n\n  const handleClose = () => {\n    setReport(null);\n    setError(null);\n    setActiveTab('overview');\n    setAssignedOfficer('');\n    onClose();\n  };\n\n  if (!isOpen) return null;\n\n  const tabs = [\n    { id: 'overview', label: 'Overview', icon: 'ri-file-text-line' },\n    { id: 'details', label: 'Report Details', icon: 'ri-information-line' },\n    { id: 'actions', label: 'Actions', icon: 'ri-settings-line' }\n  ];\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'submitted': return 'bg-blue-100 text-blue-800';\n      case 'under_review': return 'bg-yellow-100 text-yellow-800';\n      case 'investigating': return 'bg-orange-100 text-orange-800';\n      case 'resolved': return 'bg-green-100 text-green-800';\n      case 'closed': return 'bg-gray-100 text-gray-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getSeverityColor = (severity: string) => {\n    switch (severity) {\n      case 'low': return 'bg-green-100 text-green-800';\n      case 'medium': return 'bg-yellow-100 text-yellow-800';\n      case 'high': return 'bg-orange-100 text-orange-800';\n      case 'critical': return 'bg-red-100 text-red-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const renderOverviewTab = () => (\n    <div className=\"space-y-6\">\n      {/* Status Progress */}\n      <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n        <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-4\">Investigation Progress</h4>\n        <ComplaintStatusBar\n          currentStage={getStageIndexFromStatus(report?.status || 'submitted')}\n          stages={COMPLAINT_STAGES.DATA_BREACH}\n          status={report?.status as 'submitted' | 'under_review' | 'investigating' | 'resolved' | 'closed'}\n          size=\"sm\"\n          variant=\"horizontal\"\n          showPercentage={true}\n          showStageNames={true}\n        />\n      </div>\n\n      {/* Basic Information */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n          <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-3\">Report Information</h4>\n          <div className=\"space-y-2 text-sm\">\n            <div><span className=\"font-medium\">Report ID:</span> {report?.report_id}</div>\n            <div><span className=\"font-medium\">Report Number:</span> {report?.report_number}</div>\n            <div><span className=\"font-medium\">Status:</span> \n              <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(report?.status || '')}`}>\n                {report?.status?.replace('_', ' ').toUpperCase()}\n              </span>\n            </div>\n            <div><span className=\"font-medium\">Severity:</span> \n              <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(report?.severity || '')}`}>\n                {report?.severity?.toUpperCase()}\n              </span>\n            </div>\n            <div><span className=\"font-medium\">Category:</span> {report?.category}</div>\n            <div><span className=\"font-medium\">Incident Date:</span> {report?.incident_date ? new Date(report.incident_date).toLocaleDateString() : 'N/A'}</div>\n          </div>\n        </div>\n\n        <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n          <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-3\">Reporter Information</h4>\n          <div className=\"space-y-2 text-sm\">\n            <div><span className=\"font-medium\">Name:</span> {report?.reporter ? `${report.reporter.first_name} ${report.reporter.last_name}` : 'N/A'}</div>\n            <div><span className=\"font-medium\">Email:</span> {report?.reporter?.email || 'N/A'}</div>\n            <div><span className=\"font-medium\">Reported:</span> {report?.created_at ? new Date(report.created_at).toLocaleDateString() : 'N/A'}</div>\n            <div><span className=\"font-medium\">Last Updated:</span> {report?.updated_at ? new Date(report.updated_at).toLocaleDateString() : 'N/A'}</div>\n            <div><span className=\"font-medium\">Assigned To:</span> {report?.assigned_to || 'Unassigned'}</div>\n          </div>\n        </div>\n      </div>\n\n      {/* Organization and Impact */}\n      <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n        <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-3\">Incident Details</h4>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\">\n          <div><span className=\"font-medium\">Organization Involved:</span> {report?.organization_involved || 'N/A'}</div>\n          <div><span className=\"font-medium\">Affected Data Types:</span> {report?.affected_data_types || 'N/A'}</div>\n        </div>\n        {report?.contact_attempts && (\n          <div className=\"mt-3\">\n            <span className=\"font-medium\">Contact Attempts:</span>\n            <p className=\"mt-1 text-gray-600 dark:text-gray-400\">{report.contact_attempts}</p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n\n  const renderDetailsTab = () => (\n    <div className=\"space-y-6\">\n      <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n        <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-3\">Incident Title</h4>\n        <p className=\"text-gray-700 dark:text-gray-300\">{report?.title}</p>\n      </div>\n\n      <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n        <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-3\">Detailed Description</h4>\n        <p className=\"text-gray-700 dark:text-gray-300 whitespace-pre-wrap\">{report?.description}</p>\n      </div>\n\n      {report?.resolution && (\n        <div className=\"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 p-4 rounded-lg\">\n          <h4 className=\"text-sm font-medium text-green-800 dark:text-green-300 mb-3\">Resolution</h4>\n          <p className=\"text-green-700 dark:text-green-400 whitespace-pre-wrap\">{report.resolution}</p>\n        </div>\n      )}\n\n      {report?.internal_notes && (\n        <div className=\"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 p-4 rounded-lg\">\n          <h4 className=\"text-sm font-medium text-yellow-800 dark:text-yellow-300 mb-3\">Internal Notes</h4>\n          <p className=\"text-yellow-700 dark:text-yellow-400 whitespace-pre-wrap\">{report.internal_notes}</p>\n        </div>\n      )}\n    </div>\n  );\n\n  const renderActionsTab = () => (\n    <div className=\"space-y-6\">\n      {/* Assign Officer */}\n      <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n        <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-3\">Assign Officer</h4>\n        <div className=\"flex gap-3\">\n          <input\n            type=\"text\"\n            value={assignedOfficer}\n            onChange={(e) => setAssignedOfficer(e.target.value)}\n            placeholder=\"Enter officer name\"\n            className=\"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm dark:bg-gray-800 dark:text-gray-100\"\n          />\n          <button\n            type=\"button\"\n            onClick={handleAssignOfficer}\n            disabled={isAssigning || !assignedOfficer.trim()}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {isAssigning ? 'Assigning...' : 'Assign'}\n          </button>\n        </div>\n      </div>\n\n      {/* Close Report */}\n      <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n        <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-3\">Close Report</h4>\n        <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-3\">\n          Mark this report as closed. This action cannot be undone.\n        </p>\n        <button\n          type=\"button\"\n          onClick={handleCloseReport}\n          disabled={report?.status === 'closed'}\n          className=\"px-4 py-2 bg-red-600 text-white rounded-md text-sm hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n        >\n          {report?.status === 'closed' ? 'Already Closed' : 'Close Report'}\n        </button>\n      </div>\n    </div>\n  );\n\n  const renderTabContent = () => {\n    switch (activeTab) {\n      case 'overview':\n        return renderOverviewTab();\n      case 'details':\n        return renderDetailsTab();\n      case 'actions':\n        return renderActionsTab();\n      default:\n        return renderOverviewTab();\n    }\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg w-full max-w-6xl max-h-[90vh] overflow-hidden flex flex-col\">\n        {/* Header */}\n        <div className=\"flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700\">\n          <div>\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\n              Data Breach Report Details\n            </h3>\n            <p className=\"text-sm text-gray-500 dark:text-gray-400 mt-1\">\n              {report?.report_number || 'Loading...'}\n            </p>\n          </div>\n          <button\n            type=\"button\"\n            onClick={handleClose}\n            aria-label=\"Close modal\"\n            className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n          >\n            <i className=\"ri-close-line text-xl\"></i>\n          </button>\n        </div>\n\n        {/* Tabs */}\n        <div className=\"border-b border-gray-200 dark:border-gray-700\">\n          <nav className=\"flex space-x-8 px-6\" aria-label=\"Tabs\">\n            {tabs.map((tab) => (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id)}\n                className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${\n                  activeTab === tab.id\n                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:border-gray-300'\n                }`}\n              >\n                <i className={`${tab.icon} mr-2`}></i>\n                {tab.label}\n              </button>\n            ))}\n          </nav>\n        </div>\n\n        {/* Content */}\n        <div className=\"flex-1 overflow-y-auto p-6\">\n          {loading ? (\n            <div className=\"flex items-center justify-center py-12\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n              <span className=\"ml-3 text-gray-600 dark:text-gray-400\">Loading report details...</span>\n            </div>\n          ) : error ? (\n            <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\">\n              <div className=\"flex\">\n                <i className=\"ri-error-warning-line text-red-400 mr-2\"></i>\n                <p className=\"text-red-700 dark:text-red-200\">{error}</p>\n              </div>\n            </div>\n          ) : report ? (\n            renderTabContent()\n          ) : (\n            <div className=\"text-center py-12\">\n              <p className=\"text-gray-500 dark:text-gray-400\">No report data available</p>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default DataBreachViewModal;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;;;AALA;;;;;AAcA,MAAM,sBAA0D,CAAC,EAC/D,MAAM,EACN,OAAO,EACP,QAAQ,EACR,QAAQ,EACT;;IACC,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAC1C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B;IAC9D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,UAAU,UAAU;gBACtB;YACF;QACF;wCAAG;QAAC;QAAQ;KAAS;IAErB,MAAM,qBAAqB;QACzB,IAAI,CAAC,UAAU;QAEf,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,yJAAA,CAAA,oBAAiB,CAAC,aAAa,CAAC;YACvD,UAAU;YACV,mBAAmB,SAAS,WAAW,IAAI;QAC7C,EAAE,OAAO,KAAc;YACrB,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS,CAAC,+BAA+B,EAAE,cAAc;QAC3D,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,YAAY,CAAC,gBAAgB,IAAI,IAAI;YACxC,UAAU;YACV;QACF;QAEA,eAAe;QACf,IAAI;YACF,MAAM,yJAAA,CAAA,oBAAiB,CAAC,YAAY,CAAC,UAAU;YAC/C,YAAY;YAEZ,qBAAqB;YACrB,IAAI,QAAQ;gBACV,UAAU;oBAAE,GAAG,MAAM;oBAAE,aAAa;gBAAgB;YACtD;YAEA,IAAI,UAAU;QAChB,EAAE,OAAO,KAAc;YACrB,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,UAAU,CAAC,0BAA0B,EAAE,cAAc;QACvD,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,MAAM,yJAAA,CAAA,oBAAiB,CAAC,YAAY,CAAC,UAAU;YAC/C,YAAY;YAEZ,qBAAqB;YACrB,IAAI,QAAQ;gBACV,UAAU;oBAAE,GAAG,MAAM;oBAAE,QAAQ;gBAAS;YAC1C;YAEA,IAAI,UAAU;QAChB,EAAE,OAAO,KAAc;YACrB,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,UAAU,CAAC,wBAAwB,EAAE,cAAc;QACrD;IACF;IAEA,MAAM,cAAc;QAClB,UAAU;QACV,SAAS;QACT,aAAa;QACb,mBAAmB;QACnB;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,OAAO;QACX;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM;QAAoB;QAC/D;YAAE,IAAI;YAAW,OAAO;YAAkB,MAAM;QAAsB;QACtE;YAAE,IAAI;YAAW,OAAO;YAAW,MAAM;QAAmB;KAC7D;IAED,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAiB,OAAO;YAC7B,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,oBAAoB,kBACxB,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA4D;;;;;;sCAC1E,6LAAC,uJAAA,CAAA,UAAkB;4BACjB,cAAc,CAAA,GAAA,uJAAA,CAAA,0BAAuB,AAAD,EAAE,QAAQ,UAAU;4BACxD,QAAQ,uJAAA,CAAA,mBAAgB,CAAC,WAAW;4BACpC,QAAQ,QAAQ;4BAChB,MAAK;4BACL,SAAQ;4BACR,gBAAgB;4BAChB,gBAAgB;;;;;;;;;;;;8BAKpB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA4D;;;;;;8CAC1E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DAAI,6LAAC;oDAAK,WAAU;8DAAc;;;;;;gDAAiB;gDAAE,QAAQ;;;;;;;sDAC9D,6LAAC;;8DAAI,6LAAC;oDAAK,WAAU;8DAAc;;;;;;gDAAqB;gDAAE,QAAQ;;;;;;;sDAClE,6LAAC;;8DAAI,6LAAC;oDAAK,WAAU;8DAAc;;;;;;8DACjC,6LAAC;oDAAK,WAAW,CAAC,gDAAgD,EAAE,eAAe,QAAQ,UAAU,KAAK;8DACvG,QAAQ,QAAQ,QAAQ,KAAK,KAAK;;;;;;;;;;;;sDAGvC,6LAAC;;8DAAI,6LAAC;oDAAK,WAAU;8DAAc;;;;;;8DACjC,6LAAC;oDAAK,WAAW,CAAC,gDAAgD,EAAE,iBAAiB,QAAQ,YAAY,KAAK;8DAC3G,QAAQ,UAAU;;;;;;;;;;;;sDAGvB,6LAAC;;8DAAI,6LAAC;oDAAK,WAAU;8DAAc;;;;;;gDAAgB;gDAAE,QAAQ;;;;;;;sDAC7D,6LAAC;;8DAAI,6LAAC;oDAAK,WAAU;8DAAc;;;;;;gDAAqB;gDAAE,QAAQ,gBAAgB,IAAI,KAAK,OAAO,aAAa,EAAE,kBAAkB,KAAK;;;;;;;;;;;;;;;;;;;sCAI5I,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA4D;;;;;;8CAC1E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DAAI,6LAAC;oDAAK,WAAU;8DAAc;;;;;;gDAAY;gDAAE,QAAQ,WAAW,GAAG,OAAO,QAAQ,CAAC,UAAU,CAAC,CAAC,EAAE,OAAO,QAAQ,CAAC,SAAS,EAAE,GAAG;;;;;;;sDACnI,6LAAC;;8DAAI,6LAAC;oDAAK,WAAU;8DAAc;;;;;;gDAAa;gDAAE,QAAQ,UAAU,SAAS;;;;;;;sDAC7E,6LAAC;;8DAAI,6LAAC;oDAAK,WAAU;8DAAc;;;;;;gDAAgB;gDAAE,QAAQ,aAAa,IAAI,KAAK,OAAO,UAAU,EAAE,kBAAkB,KAAK;;;;;;;sDAC7H,6LAAC;;8DAAI,6LAAC;oDAAK,WAAU;8DAAc;;;;;;gDAAoB;gDAAE,QAAQ,aAAa,IAAI,KAAK,OAAO,UAAU,EAAE,kBAAkB,KAAK;;;;;;;sDACjI,6LAAC;;8DAAI,6LAAC;oDAAK,WAAU;8DAAc;;;;;;gDAAmB;gDAAE,QAAQ,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;8BAMrF,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA4D;;;;;;sCAC1E,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDAAI,6LAAC;4CAAK,WAAU;sDAAc;;;;;;wCAA6B;wCAAE,QAAQ,yBAAyB;;;;;;;8CACnG,6LAAC;;sDAAI,6LAAC;4CAAK,WAAU;sDAAc;;;;;;wCAA2B;wCAAE,QAAQ,uBAAuB;;;;;;;;;;;;;wBAEhG,QAAQ,kCACP,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAc;;;;;;8CAC9B,6LAAC;oCAAE,WAAU;8CAAyC,OAAO,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;IAOvF,MAAM,mBAAmB,kBACvB,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA4D;;;;;;sCAC1E,6LAAC;4BAAE,WAAU;sCAAoC,QAAQ;;;;;;;;;;;;8BAG3D,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA4D;;;;;;sCAC1E,6LAAC;4BAAE,WAAU;sCAAwD,QAAQ;;;;;;;;;;;;gBAG9E,QAAQ,4BACP,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA8D;;;;;;sCAC5E,6LAAC;4BAAE,WAAU;sCAA0D,OAAO,UAAU;;;;;;;;;;;;gBAI3F,QAAQ,gCACP,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAgE;;;;;;sCAC9E,6LAAC;4BAAE,WAAU;sCAA4D,OAAO,cAAc;;;;;;;;;;;;;;;;;;IAMtG,MAAM,mBAAmB,kBACvB,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA4D;;;;;;sCAC1E,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;oCAClD,aAAY;oCACZ,WAAU;;;;;;8CAEZ,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,UAAU,eAAe,CAAC,gBAAgB,IAAI;oCAC9C,WAAU;8CAET,cAAc,iBAAiB;;;;;;;;;;;;;;;;;;8BAMtC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA4D;;;;;;sCAC1E,6LAAC;4BAAE,WAAU;sCAAgD;;;;;;sCAG7D,6LAAC;4BACC,MAAK;4BACL,SAAS;4BACT,UAAU,QAAQ,WAAW;4BAC7B,WAAU;sCAET,QAAQ,WAAW,WAAW,mBAAmB;;;;;;;;;;;;;;;;;;IAM1D,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAyD;;;;;;8CAGvE,6LAAC;oCAAE,WAAU;8CACV,QAAQ,iBAAiB;;;;;;;;;;;;sCAG9B,6LAAC;4BACC,MAAK;4BACL,SAAS;4BACT,cAAW;4BACX,WAAU;sCAEV,cAAA,6LAAC;gCAAE,WAAU;;;;;;;;;;;;;;;;;8BAKjB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;wBAAsB,cAAW;kCAC7C,KAAK,GAAG,CAAC,CAAC,oBACT,6LAAC;gCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;gCAClC,WAAW,CAAC,2DAA2D,EACrE,cAAc,IAAI,EAAE,GAChB,qDACA,0HACJ;;kDAEF,6LAAC;wCAAE,WAAW,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC;;;;;;oCAC/B,IAAI,KAAK;;+BATL,IAAI,EAAE;;;;;;;;;;;;;;;8BAgBnB,6LAAC;oBAAI,WAAU;8BACZ,wBACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAK,WAAU;0CAAwC;;;;;;;;;;;+BAExD,sBACF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;;;;;;8CACb,6LAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;;;;;+BAGjD,SACF,mCAEA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO9D;GApVM;;QAM+B,mIAAA,CAAA,WAAQ;;;KANvC;uCAsVS", "debugId": null}}, {"offset": {"line": 1696, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/app/data-breach/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { dataBreachService, DataBreachReport } from '@/services/data-breach';\nimport Loader from '@/components/Loader';\nimport DataBreachViewModal from '@/components/data-breach/DataBreachViewModal';\nimport AssignModal from '@/components/common/AssignModal';\n\nconst DataBreachPage: React.FC = () => {\n  const { isAuthenticated } = useAuth();\n  const [reports, setReports] = useState<DataBreachReport[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string>('');\n  const [selectedReportId, setSelectedReportId] = useState<string | null>(null);\n  const [showViewModal, setShowViewModal] = useState(false);\n  const [showAssignModal, setShowAssignModal] = useState(false);\n  const [filter, setFilter] = useState({\n    status: '',\n    category: '',\n    severity: '',\n    search: ''\n  });\n\n  const fetchReports = async () => {\n    if (!isAuthenticated) return;\n\n    try {\n      setLoading(true);\n      console.log('🔄 Fetching all data breach reports for staff...');\n\n      // For staff, fetch all reports (not filtered by user)\n      const response = await dataBreachService.getReports({\n        limit: 100,\n        ...filter\n      });\n\n      console.log('✅ Data breach reports fetched:', response);\n\n      if (response.success && Array.isArray(response.data)) {\n        setReports(response.data);\n      } else {\n        setReports([]);\n      }\n    } catch (err: unknown) {\n      console.error('❌ Error fetching reports:', err);\n      const errorMessage = err instanceof Error ? err.message : 'Unknown error';\n      setError(`Failed to load reports: ${errorMessage}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch reports\n  useEffect(() => {\n    fetchReports();\n  }, [isAuthenticated, filter]);\n\n  const handleStatusUpdate = async (reportId: string, newStatus: string) => {\n    try {\n      // TODO: Implement status update API call\n      console.log('Updating report status:', reportId, newStatus);\n      \n      // Update local state\n      setReports(prev => \n        prev.map(report => \n          report.report_id === reportId \n            ? { ...report, status: newStatus as DataBreachReport['status'] }\n            : report\n        )\n      );\n    } catch (error) {\n      console.error('Error updating status:', error);\n    }\n  };\n\n  const handleViewReport = (reportId: string) => {\n    setSelectedReportId(reportId);\n    setShowViewModal(true);\n  };\n\n  const handleAssignReport = (reportId: string) => {\n    setSelectedReportId(reportId);\n    setShowAssignModal(true);\n  };\n\n  const handleCloseModal = () => {\n    setShowViewModal(false);\n    setSelectedReportId(null);\n  };\n\n  const handleCloseAssignModal = () => {\n    setShowAssignModal(false);\n    setSelectedReportId(null);\n  };\n\n  const handleModalUpdate = () => {\n    // Refresh the reports list when modal updates data\n    fetchReports();\n  };\n\n  const handleAssignSuccess = () => {\n    // Refresh the reports list when assignment is successful\n    fetchReports();\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status?.toLowerCase()) {\n      case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\n      case 'under_review': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';\n      case 'investigating': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\n      case 'resolved': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\n      case 'closed': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n    }\n  };\n\n  const getSeverityColor = (severity: string) => {\n    switch (severity?.toLowerCase()) {\n      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\n      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\n      case 'high': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\n      case 'critical': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"p-6 min-h-full bg-gray-50 dark:bg-gray-900\">\n        <Loader message=\"Loading data breach reports...\" />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"p-6 min-h-full bg-gray-50 dark:bg-gray-900\">\n      {/* Header */}\n      <div className=\"mb-6\">\n        <h1 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">\n          Data Breach Management\n        </h1>\n        <p className=\"text-gray-600 dark:text-gray-400 mt-2\">\n          Manage and investigate data breach reports and privacy violations\n        </p>\n      </div>\n\n      {/* Statistics Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <i className=\"ri-shield-cross-line text-2xl text-red-600\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Total Reports</p>\n              <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">{reports.length}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <i className=\"ri-time-line text-2xl text-yellow-600\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Pending</p>\n              <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">\n                {reports.filter(r => r.status === 'pending').length}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <i className=\"ri-search-line text-2xl text-blue-600\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Investigating</p>\n              <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">\n                {reports.filter(r => r.status === 'investigating').length}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <i className=\"ri-alert-line text-2xl text-red-600\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Critical</p>\n              <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">\n                {reports.filter(r => r.severity === 'critical').length}\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Filters */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Search\n            </label>\n            <input\n              type=\"text\"\n              placeholder=\"Search reports...\"\n              value={filter.search}\n              onChange={(e) => setFilter(prev => ({ ...prev, search: e.target.value }))}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-gray-100\"\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Status\n            </label>\n            <select\n              value={filter.status}\n              onChange={(e) => setFilter(prev => ({ ...prev, status: e.target.value }))}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-gray-100\"\n              aria-label=\"Filter by status\"\n              title=\"Filter reports by status\"\n            >\n              <option value=\"\">All Statuses</option>\n              <option value=\"pending\">Pending</option>\n              <option value=\"under_review\">Under Review</option>\n              <option value=\"investigating\">Investigating</option>\n              <option value=\"resolved\">Resolved</option>\n              <option value=\"closed\">Closed</option>\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Category\n            </label>\n            <select\n              value={filter.category}\n              onChange={(e) => setFilter(prev => ({ ...prev, category: e.target.value }))}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-gray-100\"\n              aria-label=\"Filter by category\"\n              title=\"Filter reports by category\"\n            >\n              <option value=\"\">All Categories</option>\n              <option value=\"Unauthorized Data Access\">Unauthorized Data Access</option>\n              <option value=\"Data Misuse or Sharing\">Data Misuse or Sharing</option>\n              <option value=\"Privacy Violations\">Privacy Violations</option>\n              <option value=\"Identity Theft\">Identity Theft</option>\n              <option value=\"Other\">Other</option>\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Severity\n            </label>\n            <select\n              value={filter.severity}\n              onChange={(e) => setFilter(prev => ({ ...prev, severity: e.target.value }))}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-gray-100\"\n              aria-label=\"Filter by severity\"\n              title=\"Filter reports by severity\"\n            >\n              <option value=\"\">All Severities</option>\n              <option value=\"low\">Low</option>\n              <option value=\"medium\">Medium</option>\n              <option value=\"high\">High</option>\n              <option value=\"critical\">Critical</option>\n            </select>\n          </div>\n        </div>\n      </div>\n\n      {/* Error Message */}\n      {error && (\n        <div className=\"bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4 mb-6\">\n          <div className=\"flex\">\n            <i className=\"ri-error-warning-line text-red-400 mr-2\"></i>\n            <p className=\"text-red-700 dark:text-red-200\">{error}</p>\n          </div>\n        </div>\n      )}\n\n      {/* Reports Table */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden\">\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n            Data Breach Reports ({reports.length})\n          </h3>\n        </div>\n\n        {reports.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <i className=\"ri-shield-cross-line text-4xl text-gray-400 mb-4\"></i>\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\">No reports found</h3>\n            <p className=\"text-gray-500 dark:text-gray-400\">\n              No data breach reports have been submitted yet.\n            </p>\n          </div>\n        ) : (\n          <div className=\"overflow-x-auto\">\n            <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n              <thead className=\"bg-gray-50 dark:bg-gray-900\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    Report\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    Category\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    Severity\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    Status\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    Incident Date\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    Actions\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n                {reports.map((report) => (\n                  <tr key={report.report_id} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div>\n                        <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\n                          {report.title}\n                        </div>\n                        <div className=\"text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs\">\n                          {report.description}\n                        </div>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200\">\n                        {report.category}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getSeverityColor(report.severity)}`}>\n                        {report.severity?.toUpperCase()}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(report.status)}`}>\n                        {report.status?.replace('_', ' ').toUpperCase()}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\n                      {report.incident_date ? new Date(report.incident_date).toLocaleDateString() : 'N/A'}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                      <div className=\"flex items-center space-x-2\">\n                        <button\n                          type=\"button\"\n                          onClick={() => handleViewReport(report.report_id)}\n                          className=\"inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\"\n                          title=\"View report details\"\n                        >\n                          <i className=\"ri-eye-line mr-1\"></i>\n                          View\n                        </button>\n                        <button\n                          type=\"button\"\n                          onClick={() => handleAssignReport(report.report_id)}\n                          className=\"inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\"\n                          title=\"Assign report to officer\"\n                        >\n                          <i className=\"ri-user-add-line mr-1\"></i>\n                          Assign\n                        </button>\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        )}\n      </div>\n\n      {/* View Modal */}\n      <DataBreachViewModal\n        isOpen={showViewModal}\n        onClose={handleCloseModal}\n        reportId={selectedReportId}\n        onUpdate={handleModalUpdate}\n      />\n    </div>\n  );\n};\n\nexport default DataBreachPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;;;AANA;;;;;;AASA,MAAM,iBAA2B;;IAC/B,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAClC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACxE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACnC,QAAQ;QACR,UAAU;QACV,UAAU;QACV,QAAQ;IACV;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,iBAAiB;QAEtB,IAAI;YACF,WAAW;YACX,QAAQ,GAAG,CAAC;YAEZ,sDAAsD;YACtD,MAAM,WAAW,MAAM,yJAAA,CAAA,oBAAiB,CAAC,UAAU,CAAC;gBAClD,OAAO;gBACP,GAAG,MAAM;YACX;YAEA,QAAQ,GAAG,CAAC,kCAAkC;YAE9C,IAAI,SAAS,OAAO,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBACpD,WAAW,SAAS,IAAI;YAC1B,OAAO;gBACL,WAAW,EAAE;YACf;QACF,EAAE,OAAO,KAAc;YACrB,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS,CAAC,wBAAwB,EAAE,cAAc;QACpD,SAAU;YACR,WAAW;QACb;IACF;IAEA,gBAAgB;IAChB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR;QACF;mCAAG;QAAC;QAAiB;KAAO;IAE5B,MAAM,qBAAqB,OAAO,UAAkB;QAClD,IAAI;YACF,yCAAyC;YACzC,QAAQ,GAAG,CAAC,2BAA2B,UAAU;YAEjD,qBAAqB;YACrB,WAAW,CAAA,OACT,KAAK,GAAG,CAAC,CAAA,SACP,OAAO,SAAS,KAAK,WACjB;wBAAE,GAAG,MAAM;wBAAE,QAAQ;oBAAwC,IAC7D;QAGV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,oBAAoB;QACpB,iBAAiB;IACnB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,oBAAoB;QACpB,mBAAmB;IACrB;IAEA,MAAM,mBAAmB;QACvB,iBAAiB;QACjB,oBAAoB;IACtB;IAEA,MAAM,yBAAyB;QAC7B,mBAAmB;QACnB,oBAAoB;IACtB;IAEA,MAAM,oBAAoB;QACxB,mDAAmD;QACnD;IACF;IAEA,MAAM,sBAAsB;QAC1B,yDAAyD;QACzD;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ,QAAQ;YACd,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAiB,OAAO;YAC7B,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ,UAAU;YAChB,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,+HAAA,CAAA,UAAM;gBAAC,SAAQ;;;;;;;;;;;IAGtB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsD;;;;;;kCAGpE,6LAAC;wBAAE,WAAU;kCAAwC;;;;;;;;;;;;0BAMvD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;;;;;;;;;;;8CAEf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAuD;;;;;;sDACpE,6LAAC;4CAAE,WAAU;sDAA2D,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAK5F,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;;;;;;;;;;;8CAEf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAuD;;;;;;sDACpE,6LAAC;4CAAE,WAAU;sDACV,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAM3D,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;;;;;;;;;;;8CAEf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAuD;;;;;;sDACpE,6LAAC;4CAAE,WAAU;sDACV,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,iBAAiB,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAMjE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;;;;;;;;;;;8CAEf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAuD;;;;;;sDACpE,6LAAC;4CAAE,WAAU;sDACV,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,YAAY,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,6LAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO,OAAO,MAAM;oCACpB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCACvE,WAAU;;;;;;;;;;;;sCAId,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,6LAAC;oCACC,OAAO,OAAO,MAAM;oCACpB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCACvE,WAAU;oCACV,cAAW;oCACX,OAAM;;sDAEN,6LAAC;4CAAO,OAAM;sDAAG;;;;;;sDACjB,6LAAC;4CAAO,OAAM;sDAAU;;;;;;sDACxB,6LAAC;4CAAO,OAAM;sDAAe;;;;;;sDAC7B,6LAAC;4CAAO,OAAM;sDAAgB;;;;;;sDAC9B,6LAAC;4CAAO,OAAM;sDAAW;;;;;;sDACzB,6LAAC;4CAAO,OAAM;sDAAS;;;;;;;;;;;;;;;;;;sCAI3B,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,6LAAC;oCACC,OAAO,OAAO,QAAQ;oCACtB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCACzE,WAAU;oCACV,cAAW;oCACX,OAAM;;sDAEN,6LAAC;4CAAO,OAAM;sDAAG;;;;;;sDACjB,6LAAC;4CAAO,OAAM;sDAA2B;;;;;;sDACzC,6LAAC;4CAAO,OAAM;sDAAyB;;;;;;sDACvC,6LAAC;4CAAO,OAAM;sDAAqB;;;;;;sDACnC,6LAAC;4CAAO,OAAM;sDAAiB;;;;;;sDAC/B,6LAAC;4CAAO,OAAM;sDAAQ;;;;;;;;;;;;;;;;;;sCAI1B,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,6LAAC;oCACC,OAAO,OAAO,QAAQ;oCACtB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCACzE,WAAU;oCACV,cAAW;oCACX,OAAM;;sDAEN,6LAAC;4CAAO,OAAM;sDAAG;;;;;;sDACjB,6LAAC;4CAAO,OAAM;sDAAM;;;;;;sDACpB,6LAAC;4CAAO,OAAM;sDAAS;;;;;;sDACvB,6LAAC;4CAAO,OAAM;sDAAO;;;;;;sDACrB,6LAAC;4CAAO,OAAM;sDAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOhC,uBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;;;;;;sCACb,6LAAC;4BAAE,WAAU;sCAAkC;;;;;;;;;;;;;;;;;0BAMrD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;;gCAAuD;gCAC7C,QAAQ,MAAM;gCAAC;;;;;;;;;;;;oBAIxC,QAAQ,MAAM,KAAK,kBAClB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;;;;;0CACb,6LAAC;gCAAG,WAAU;0CAA4D;;;;;;0CAC1E,6LAAC;gCAAE,WAAU;0CAAmC;;;;;;;;;;;6CAKlD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAM,WAAU;;8CACf,6LAAC;oCAAM,WAAU;8CACf,cAAA,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;;;;;;;;;;;;8CAKtH,6LAAC;oCAAM,WAAU;8CACd,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;4CAA0B,WAAU;;8DACnC,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;0EACZ,OAAO,KAAK;;;;;;0EAEf,6LAAC;gEAAI,WAAU;0EACZ,OAAO,WAAW;;;;;;;;;;;;;;;;;8DAIzB,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAK,WAAU;kEACb,OAAO,QAAQ;;;;;;;;;;;8DAGpB,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAK,WAAW,CAAC,yDAAyD,EAAE,iBAAiB,OAAO,QAAQ,GAAG;kEAC7G,OAAO,QAAQ,EAAE;;;;;;;;;;;8DAGtB,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAK,WAAW,CAAC,yDAAyD,EAAE,eAAe,OAAO,MAAM,GAAG;kEACzG,OAAO,MAAM,EAAE,QAAQ,KAAK,KAAK;;;;;;;;;;;8DAGtC,6LAAC;oDAAG,WAAU;8DACX,OAAO,aAAa,GAAG,IAAI,KAAK,OAAO,aAAa,EAAE,kBAAkB,KAAK;;;;;;8DAEhF,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,MAAK;gEACL,SAAS,IAAM,iBAAiB,OAAO,SAAS;gEAChD,WAAU;gEACV,OAAM;;kFAEN,6LAAC;wEAAE,WAAU;;;;;;oEAAuB;;;;;;;0EAGtC,6LAAC;gEACC,MAAK;gEACL,SAAS,IAAM,mBAAmB,OAAO,SAAS;gEAClD,WAAU;gEACV,OAAM;;kFAEN,6LAAC;wEAAE,WAAU;;;;;;oEAA4B;;;;;;;;;;;;;;;;;;;2CA9CxC,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;0BA4DrC,6LAAC,8JAAA,CAAA,UAAmB;gBAClB,QAAQ;gBACR,SAAS;gBACT,UAAU;gBACV,UAAU;;;;;;;;;;;;AAIlB;GAzYM;;QACwB,kIAAA,CAAA,UAAO;;;KAD/B;uCA2YS", "debugId": null}}]}