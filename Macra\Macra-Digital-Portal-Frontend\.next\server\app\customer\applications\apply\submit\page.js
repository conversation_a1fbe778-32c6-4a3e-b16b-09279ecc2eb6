(()=>{var e={};e.id=4837,e.ids=[4837],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},15885:(e,t,r)=>{"use strict";r.d(t,{U_:()=>n,_l:()=>i,qI:()=>s});class a{set(e,t,r=this.defaultTTL){let a=Date.now();this.cache.set(e,{data:t,timestamp:a,expiresAt:a+r})}get(e){let t=this.cache.get(e);return t?Date.now()>t.expiresAt?(this.cache.delete(e),null):t.data:null}has(e){return null!==this.get(e)}delete(e){return this.cache.delete(e)}clear(){this.cache.clear()}getStats(){return{size:this.cache.size,keys:Array.from(this.cache.keys())}}cleanup(){let e=Date.now(),t=0;for(let[t,r]of this.cache.entries())e>r.expiresAt&&this.cache.delete(t)}async getOrSet(e,t,r=this.defaultTTL){let a=this.get(e);if(null!==a)return a;let s=await t();return this.set(e,s,r),s}invalidatePattern(e){let t=new RegExp(e),r=0;for(let e of this.cache.keys())t.test(e)&&this.cache.delete(e)}constructor(){this.cache=new Map,this.defaultTTL=3e5}}let s=new a,i={LICENSE_TYPES:"license-types",LICENSE_CATEGORIES:"license-categories",LICENSE_CATEGORIES_BY_TYPE:e=>`license-categories-type-${e}`,USER_APPLICATIONS:"user-applications",APPLICATION:e=>`application-${e}`},n={SHORT:12e4,MEDIUM:3e5,LONG:9e5,VERY_LONG:36e5};setInterval(()=>{s.cleanup()},3e5)},18980:(e,t,r)=>{"use strict";r.d(t,{r:()=>a});var a=function(e){return e.DRAFT="draft",e.SUBMITTED="submitted",e.UNDER_REVIEW="under_review",e.EVALUATION="evaluation",e.APPROVED="approved",e.REJECTED="rejected",e.WITHDRAWN="withdrawn",e}({})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32871:(e,t,r)=>{Promise.resolve().then(r.bind(r,78479))},33873:e=>{"use strict";e.exports=require("path")},34130:(e,t,r)=>{"use strict";r.d(t,{TG:()=>o});var a=r(12234),s=r(15885);let i=e=>e.toLowerCase().replace(/[^a-z0-9\s]/g,"").replace(/\s+/g,"-").replace(/^-+|-+$/g,"").substring(0,50),n=e=>e.map(e=>({...e,code:i(e.name),children:e.children?n(e.children):void 0})),o={async getLicenseCategories(e={}){let t=new URLSearchParams;return e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(([e,r])=>{Array.isArray(r)?r.forEach(r=>t.append(`filter.${e}`,r)):t.set(`filter.${e}`,r)}),(await a.uE.get(`/license-categories?${t.toString()}`)).data},async getLicenseCategory(e){try{return(await a.uE.get(`/license-categories/${e}`,{timeout:3e4})).data}catch(e){if("ECONNABORTED"===e.code)throw Error("Request timeout - please try again");throw e}},async getLicenseCategoriesByType(e){try{return(await a.uE.get(`/license-categories/by-license-type/${e}`,{timeout:3e4})).data}catch(e){if("ECONNABORTED"===e.code)throw Error("Request timeout - please try again");if(e.response?.status===429)throw Error("Too many requests - please wait a moment and try again");throw e}},createLicenseCategory:async e=>(await a.uE.post("/license-categories",e)).data,updateLicenseCategory:async(e,t)=>(await a.uE.put(`/license-categories/${e}`,t)).data,deleteLicenseCategory:async e=>(await a.uE.delete(`/license-categories/${e}`)).data,async getAllLicenseCategories(){return s.qI.getOrSet(s._l.LICENSE_CATEGORIES,async()=>n((await this.getLicenseCategories({limit:100})).data),s.U_.LONG)},getCategoryTree:async e=>s.qI.getOrSet(`category-tree-${e}`,async()=>n((await a.uE.get(`/license-categories/license-type/${e}/tree`)).data),s.U_.MEDIUM),getRootCategories:async e=>s.qI.getOrSet(`root-categories-${e}`,async()=>(await a.uE.get(`/license-categories/license-type/${e}/root`)).data,s.U_.MEDIUM),async getCategoriesForParentSelection(e,t){try{try{let r=await a.uE.get(`/license-categories/license-type/${e}/for-parent-selection`,{params:t?{excludeId:t}:{}});if(r.data&&Array.isArray(r.data.data))return r.data.data;return[]}catch(s){let r=await a.uE.get(`/license-categories/by-license-type/${e}`);if(!(r.data&&Array.isArray(r.data)))return[];{let e=r.data;return t&&(e=e.filter(e=>e.license_category_id!==t)),e}}}catch(e){return[]}},getPotentialParents:async(e,t)=>(await a.uE.get(`/license-categories/license-type/${e}/potential-parents`,{params:t?{excludeId:t}:{}})).data}},49743:(e,t,r)=>{Promise.resolve().then(r.bind(r,70857))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63097:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var a=r(65239),s=r(48088),i=r(88170),n=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["customer",{children:["applications",{children:["apply",{children:["submit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,70857)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\submit\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,68736)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\submit\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/customer/applications/apply/submit/page",pathname:"/customer/applications/apply/submit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},70857:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\applications\\\\apply\\\\submit\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\submit\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},78479:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var a=r(60687),s=r(43210),i=r(16189),n=r(94391),o=r(13128),l=r(63213),c=r(78637),d=r(34130),u=r(18980);let p=()=>{let e=(0,i.useRouter)(),t=(0,i.useSearchParams)(),{isAuthenticated:r,loading:p}=(0,l.A)(),m=t.get("license_category_id"),h=t.get("application_id"),[x,g]=(0,s.useState)(!0),[y,b]=(0,s.useState)(!1),[f,w]=(0,s.useState)(null),[v,j]=(0,s.useState)(null),[E,N]=(0,s.useState)(!1),[A,k]=(0,s.useState)(null);(0,s.useEffect)(()=>{(async()=>{if(h&&r&&!p)try{if(g(!0),w(null),j(null),m)try{let e=await d.TG.getLicenseCategory(m);e&&e.license_type&&k(e.license_type)}catch(e){}await c.k.getApplication(h)}catch(e){w("Failed to load application data")}finally{g(!1)}})()},[h,r,p]);let P=async()=>{if(!h)return w("Application ID is required"),!1;if(!E)return w("You must confirm the declaration before submitting"),!1;b(!0);try{return await c.k.updateApplication(h,{status:u.r.SUBMITTED,submitted_at:new Date().toISOString(),progress_percentage:100,current_step:8}),e.push(`/customer/applications/submitted?application_id=${h}`),!0}catch(e){return w("Failed to submit application. Please try again."),!1}finally{b(!1)}},_=async()=>{await P()};return p||x?(0,a.jsx)(n.A,{children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading application for submission..."})]})})}):f?(0,a.jsx)(n.A,{children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto p-6",children:(0,a.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"ri-error-warning-line text-red-600 dark:text-red-400 text-2xl mr-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-red-800 dark:text-red-200",children:"Error Loading Application"}),(0,a.jsx)("p",{className:"text-red-700 dark:text-red-300 mt-1",children:f}),(0,a.jsxs)("button",{onClick:()=>e.back(),className:"mt-4 inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40",children:[(0,a.jsx)("i",{className:"ri-arrow-left-line mr-2"}),"Go Back"]})]})]})})})}):(0,a.jsx)(n.A,{children:(0,a.jsxs)(o.A,{onNext:_,onPrevious:()=>{e.push(`/customer/applications/apply/documents?license_category_id=${m}&application_id=${h}`)},onSave:async()=>!0,showNextButton:!0,showPreviousButton:!0,showSaveButton:!1,nextButtonText:y?"Submitting...":"Submit Application",previousButtonText:"Back to Documents",nextButtonDisabled:!E||y,isSaving:y,children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Submit Application"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:"Review your application details and submit for review by MACRA."}),h&&(0,a.jsx)("div",{className:"mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:[(0,a.jsx)("i",{className:"ri-file-text-line mr-1"}),"Application ID: ",h]})}),v&&(0,a.jsx)("div",{className:"mt-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-yellow-700 dark:text-yellow-300",children:["⚠️ ",v]})})]}),(0,a.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg mb-6",children:[(0,a.jsxs)("h4",{className:"text-md font-medium text-blue-900 dark:text-blue-100 mb-2",children:[(0,a.jsx)("i",{className:"ri-information-line mr-2"}),"Important Information"]}),(0,a.jsxs)("ul",{className:"text-sm text-blue-800 dark:text-blue-200 space-y-1",children:[(0,a.jsx)("li",{children:"• Your application will be reviewed by MACRA within 30 business days"}),(0,a.jsx)("li",{children:"• You will receive email notifications about the status of your application"}),(0,a.jsx)("li",{children:"• Additional documentation may be requested during the review process"}),(0,a.jsx)("li",{children:"• Application fees are non-refundable"}),(0,a.jsx)("li",{children:"• You can track your application status in your dashboard"})]})]}),(0,a.jsx)("div",{className:"bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg mb-6",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("i",{className:"ri-alert-line text-yellow-600 dark:text-yellow-400 mr-2 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-yellow-800 dark:text-yellow-200",children:"Before Submitting"}),(0,a.jsx)("p",{className:"text-sm text-yellow-700 dark:text-yellow-300 mt-1",children:"Please ensure all information is accurate and complete. Once submitted, you will not be able to modify your application without contacting MACRA support."})]})]})}),(0,a.jsxs)("div",{className:"border-t border-gray-200 dark:border-gray-700 pt-6",children:[(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("input",{type:"checkbox",id:"confirmation",checked:E,onChange:e=>N(e.target.checked),className:"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded mt-1"}),(0,a.jsxs)("label",{htmlFor:"confirmation",className:"ml-2 block text-sm text-gray-900 dark:text-gray-100",children:["I confirm that all information provided in this application is true, accurate, and complete to the best of my knowledge. I understand that providing false or misleading information may result in the rejection of my application or revocation of any license granted. I agree to the terms and conditions of the licensing process.",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]})]}),!E&&(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-500 dark:text-gray-400",children:"You must accept this declaration to submit your application."})]})]})})}},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,7498,1658,5814,7563,6893,140],()=>r(63097));module.exports=a})();