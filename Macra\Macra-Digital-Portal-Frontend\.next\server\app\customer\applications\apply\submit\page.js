(()=>{var e={};e.id=4837,e.ids=[4837],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},15885:(e,t,a)=>{"use strict";a.d(t,{U_:()=>n,_l:()=>i,qI:()=>s});class r{set(e,t,a=this.defaultTTL){let r=Date.now();this.cache.set(e,{data:t,timestamp:r,expiresAt:r+a})}get(e){let t=this.cache.get(e);return t?Date.now()>t.expiresAt?(this.cache.delete(e),null):t.data:null}has(e){return null!==this.get(e)}delete(e){return this.cache.delete(e)}clear(){this.cache.clear()}getStats(){return{size:this.cache.size,keys:Array.from(this.cache.keys())}}cleanup(){let e=Date.now(),t=0;for(let[t,a]of this.cache.entries())e>a.expiresAt&&this.cache.delete(t)}async getOrSet(e,t,a=this.defaultTTL){let r=this.get(e);if(null!==r)return r;let s=await t();return this.set(e,s,a),s}invalidatePattern(e){let t=new RegExp(e),a=0;for(let e of this.cache.keys())t.test(e)&&this.cache.delete(e)}constructor(){this.cache=new Map,this.defaultTTL=3e5}}let s=new r,i={LICENSE_TYPES:"license-types",LICENSE_CATEGORIES:"license-categories",LICENSE_CATEGORIES_BY_TYPE:e=>`license-categories-type-${e}`,USER_APPLICATIONS:"user-applications",APPLICATION:e=>`application-${e}`},n={SHORT:12e4,MEDIUM:3e5,LONG:9e5,VERY_LONG:36e5};setInterval(()=>{s.cleanup()},3e5)},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32871:(e,t,a)=>{Promise.resolve().then(a.bind(a,78479))},33873:e=>{"use strict";e.exports=require("path")},34130:(e,t,a)=>{"use strict";a.d(t,{TG:()=>l});var r=a(72901),s=a(15885);let i=e=>e.toLowerCase().replace(/[^a-z0-9\s]/g,"").replace(/\s+/g,"-").replace(/^-+|-+$/g,"").substring(0,50),n=e=>e.map(e=>({...e,code:i(e.name),children:e.children?n(e.children):void 0})),l={async getLicenseCategories(e={}){let t=new URLSearchParams;return e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(([e,a])=>{Array.isArray(a)?a.forEach(a=>t.append(`filter.${e}`,a)):t.set(`filter.${e}`,a)}),(await r.uE.get(`/license-categories?${t.toString()}`)).data},getLicenseCategory:async e=>(await r.uE.get(`/license-categories/${e}`)).data,getLicenseCategoriesByType:async e=>(await r.uE.get(`/license-categories/by-license-type/${e}`)).data,createLicenseCategory:async e=>(await r.uE.post("/license-categories",e)).data,updateLicenseCategory:async(e,t)=>(await r.uE.put(`/license-categories/${e}`,t)).data,deleteLicenseCategory:async e=>(await r.uE.delete(`/license-categories/${e}`)).data,async getAllLicenseCategories(){return s.qI.getOrSet(s._l.LICENSE_CATEGORIES,async()=>n((await this.getLicenseCategories({limit:100})).data),s.U_.LONG)},getCategoryTree:async e=>s.qI.getOrSet(`category-tree-${e}`,async()=>n((await r.uE.get(`/license-categories/license-type/${e}/tree`)).data),s.U_.MEDIUM),getRootCategories:async e=>s.qI.getOrSet(`root-categories-${e}`,async()=>(await r.uE.get(`/license-categories/license-type/${e}/root`)).data,s.U_.MEDIUM),async getCategoriesForParentSelection(e,t){try{try{let a=await r.uE.get(`/license-categories/license-type/${e}/for-parent-selection`,{params:t?{excludeId:t}:{}});if(a.data&&Array.isArray(a.data.data))return a.data.data;return[]}catch(s){let a=await r.uE.get(`/license-categories/by-license-type/${e}`);if(!(a.data&&Array.isArray(a.data)))return[];{let e=a.data;return t&&(e=e.filter(e=>e.license_category_id!==t)),e}}}catch(e){return[]}},getPotentialParents:async(e,t)=>(await r.uE.get(`/license-categories/license-type/${e}/potential-parents`,{params:t?{excludeId:t}:{}})).data}},49743:(e,t,a)=>{Promise.resolve().then(a.bind(a,70857))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63097:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>m,tree:()=>o});var r=a(65239),s=a(48088),i=a(88170),n=a.n(i),l=a(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);a.d(t,c);let o={children:["",{children:["customer",{children:["applications",{children:["apply",{children:["submit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,70857)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\submit\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,68736)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\submit\\page.tsx"],p={require:a,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/customer/applications/apply/submit/page",pathname:"/customer/applications/apply/submit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},70857:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\applications\\\\apply\\\\submit\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\submit\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},78479:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>p});var r=a(60687),s=a(43210),i=a(16189),n=a(94391),l=a(13128),c=a(63213),o=a(78637),d=a(34130);let p=()=>{let e=(0,i.useRouter)(),t=(0,i.useSearchParams)(),{isAuthenticated:a,loading:p}=(0,c.A)(),m=t.get("license_category_id"),u=t.get("application_id"),[g,x]=(0,s.useState)(!0),[h,y]=(0,s.useState)(!1),[b,f]=(0,s.useState)(null),[j,v]=(0,s.useState)(null),[N,k]=(0,s.useState)(!1),[w,S]=(0,s.useState)(null),[A,C]=(0,s.useState)(null);(0,s.useEffect)(()=>{(async()=>{if(u&&a&&!p)try{if(x(!0),f(null),v(null),m)try{let e=await d.TG.getLicenseCategory(m);e&&e.license_type&&S(e.license_type)}catch(e){}let e=await o.k.getApplication(u);try{let[t,a,r,s,i,n]=await Promise.all([applicationFormDataService.getFormSection(u,"applicant-info").catch(()=>null),applicationFormDataService.getFormSection(u,"address-info").catch(()=>null),applicationFormDataService.getFormSection(u,"contact-info").catch(()=>null),applicationFormDataService.getFormSection(u,"management").catch(()=>null),applicationFormDataService.getFormSection(u,"service-scope").catch(()=>null),applicationFormDataService.getFormSection(u,"legal-history").catch(()=>null)]);C({applicantInfo:t,addressInfo:a,contactInfo:r,management:s,serviceScope:i,legalHistory:n,application:e})}catch(e){v("Could not load complete application summary. You can still submit your application.")}}catch(e){f("Failed to load application data")}finally{x(!1)}})()},[u,a,p]);let P=async()=>{if(!u)return f("Application ID is required"),!1;if(!N)return f("You must confirm the declaration before submitting"),!1;y(!0);try{return await o.k.updateApplication(u,{status:"submitted",submitted_at:new Date().toISOString(),progress_percentage:100,current_step:8}),e.push(`/customer/applications/submitted?application_id=${u}`),!0}catch(e){return f("Failed to submit application. Please try again."),!1}finally{y(!1)}},E=async()=>{await P()};return p||g?(0,r.jsx)(n.A,{children:(0,r.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading application for submission..."})]})})}):b?(0,r.jsx)(n.A,{children:(0,r.jsx)("div",{className:"max-w-4xl mx-auto p-6",children:(0,r.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"ri-error-warning-line text-red-600 dark:text-red-400 text-2xl mr-4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-red-800 dark:text-red-200",children:"Error Loading Application"}),(0,r.jsx)("p",{className:"text-red-700 dark:text-red-300 mt-1",children:b}),(0,r.jsxs)("button",{onClick:()=>e.back(),className:"mt-4 inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40",children:[(0,r.jsx)("i",{className:"ri-arrow-left-line mr-2"}),"Go Back"]})]})]})})})}):(0,r.jsx)(n.A,{children:(0,r.jsxs)(l.A,{onNext:E,onPrevious:()=>{e.push(`/customer/applications/apply/documents?license_category_id=${m}&application_id=${u}`)},onSave:async()=>!0,showNextButton:!0,showPreviousButton:!0,showSaveButton:!1,nextButtonText:h?"Submitting...":"Submit Application",previousButtonText:"Back to Documents",nextButtonDisabled:!N||h,isSaving:h,children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Submit Application"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:"Review your application details and submit for review by MACRA."}),u&&(0,r.jsx)("div",{className:"mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:[(0,r.jsx)("i",{className:"ri-file-text-line mr-1"}),"Application ID: ",u]})}),j&&(0,r.jsx)("div",{className:"mt-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-yellow-700 dark:text-yellow-300",children:["⚠️ ",j]})})]}),(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6",children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"border-b border-gray-200 dark:border-gray-700 pb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Application Summary"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:"Your application is ready for submission to MACRA for review."})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg",children:[(0,r.jsxs)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",children:[(0,r.jsx)("i",{className:"ri-user-line mr-2"}),"Applicant Information"]}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:A?.applicantInfo?"✅ Completed":"❌ Missing"})]}),(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg",children:[(0,r.jsxs)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",children:[(0,r.jsx)("i",{className:"ri-map-pin-line mr-2"}),"Address Information"]}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:A?.addressInfo?"✅ Completed":"❌ Missing"})]}),(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg",children:[(0,r.jsxs)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",children:[(0,r.jsx)("i",{className:"ri-phone-line mr-2"}),"Contact Information"]}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:A?.contactInfo?"✅ Completed":"❌ Missing"})]}),(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg",children:[(0,r.jsxs)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",children:[(0,r.jsx)("i",{className:"ri-team-line mr-2"}),"Management Information"]}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:A?.management?"✅ Completed":"❌ Missing"})]}),(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg",children:[(0,r.jsxs)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",children:[(0,r.jsx)("i",{className:"ri-service-line mr-2"}),"Service Scope"]}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:A?.serviceScope?"✅ Completed":"❌ Missing"})]}),(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg",children:[(0,r.jsxs)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",children:[(0,r.jsx)("i",{className:"ri-shield-check-line mr-2"}),"Legal History"]}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:A?.legalHistory?"✅ Completed":"❌ Missing"})]})]})]})}),(0,r.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg mb-6",children:[(0,r.jsxs)("h4",{className:"text-md font-medium text-blue-900 dark:text-blue-100 mb-2",children:[(0,r.jsx)("i",{className:"ri-information-line mr-2"}),"Important Information"]}),(0,r.jsxs)("ul",{className:"text-sm text-blue-800 dark:text-blue-200 space-y-1",children:[(0,r.jsx)("li",{children:"• Your application will be reviewed by MACRA within 30 business days"}),(0,r.jsx)("li",{children:"• You will receive email notifications about the status of your application"}),(0,r.jsx)("li",{children:"• Additional documentation may be requested during the review process"}),(0,r.jsx)("li",{children:"• Application fees are non-refundable"}),(0,r.jsx)("li",{children:"• You can track your application status in your dashboard"})]})]}),(0,r.jsx)("div",{className:"bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg mb-6",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("i",{className:"ri-alert-line text-yellow-600 dark:text-yellow-400 mr-2 mt-0.5"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-yellow-800 dark:text-yellow-200",children:"Before Submitting"}),(0,r.jsx)("p",{className:"text-sm text-yellow-700 dark:text-yellow-300 mt-1",children:"Please ensure all information is accurate and complete. Once submitted, you will not be able to modify your application without contacting MACRA support."})]})]})}),(0,r.jsxs)("div",{className:"border-t border-gray-200 dark:border-gray-700 pt-6",children:[(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("input",{type:"checkbox",id:"confirmation",checked:N,onChange:e=>k(e.target.checked),className:"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded mt-1"}),(0,r.jsxs)("label",{htmlFor:"confirmation",className:"ml-2 block text-sm text-gray-900 dark:text-gray-100",children:["I confirm that all information provided in this application is true, accurate, and complete to the best of my knowledge. I understand that providing false or misleading information may result in the rejection of my application or revocation of any license granted. I agree to the terms and conditions of the licensing process.",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]})]}),!N&&(0,r.jsx)("p",{className:"mt-2 text-sm text-gray-500 dark:text-gray-400",children:"You must accept this declaration to submit your application."})]})]})})}},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[4447,7498,1658,5814,7563,6893,6212,140],()=>a(63097));module.exports=r})();