import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';
import Cookies from 'js-cookie';
import { processApiResponse } from './authUtils';

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

// Create axios instance for customer portal (same as staff portal)
const customerApiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 120000, // Increased timeout to match main API client (120 seconds)
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Create auth-specific client (same as staff portal)
const customerAuthApiClient: AxiosInstance = axios.create({
  baseURL: `${API_BASE_URL}/auth`,
  timeout: 120000, // Increased timeout to match main API client
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Add debug logging to auth client (only in development)
customerAuthApiClient.interceptors.request.use(
  (config) => {
    if (process.env.NODE_ENV === 'development') {
      console.log('Customer Auth API Request:', {
        url: `${config.baseURL}${config.url}`,
        method: config.method,
        headers: config.headers,
        data: config.data
      });
    }
    return config;
  },
  (error) => {
    console.error('Customer Auth API Request Error:', error);
    return Promise.reject(error);
  }
);

customerAuthApiClient.interceptors.response.use(
  (response) => {
    if (process.env.NODE_ENV === 'development') {
      console.log('Customer Auth API Response Success:', {
        status: response.status,
        statusText: response.statusText,
        url: response.config.url
      });
    }
    return response;
  },
  (error) => {
    if (process.env.NODE_ENV === 'development') {
      console.error('Customer Auth API Interceptor Error:', {
        message: error?.message || 'Unknown error',
        code: error?.code || 'NO_CODE',
        status: error?.response?.status || 'NO_STATUS',
        statusText: error?.response?.statusText || 'NO_STATUS_TEXT',
        url: error?.config?.url || 'NO_URL',
        method: error?.config?.method || 'NO_METHOD',
        baseURL: error?.config?.baseURL || 'NO_BASE_URL',
        isAxiosError: error?.isAxiosError || false,
        responseData: error?.response?.data || 'NO_RESPONSE_DATA',
        requestData: error?.config?.data || 'NO_REQUEST_DATA',
        headers: error?.config?.headers || 'NO_HEADERS'
      });
    }

    // Don't handle 401 here, let the login method handle it
    return Promise.reject(error);
  }
);

// Request interceptor to add auth token
customerApiClient.interceptors.request.use(
  (config) => {
    const token = Cookies.get('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling with retry logic
customerApiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  async (error: AxiosError) => {
    const originalRequest = error.config as AxiosError['config'] & {
      _retry?: boolean;
      _retryCount?: number;
    };

    // Handle 429 Rate Limiting
    if (error.response?.status === 429) {
      if (!originalRequest._retry) {
        originalRequest._retry = true;
        
        // Get retry delay from headers or use exponential backoff
        const retryAfter = error.response.headers['retry-after'];
        const delay = retryAfter ? parseInt(retryAfter) * 1000 : Math.min(1000 * Math.pow(2, originalRequest._retryCount || 0), 10000);
        
        originalRequest._retryCount = (originalRequest._retryCount || 0) + 1;
        
        // Don't retry more than 3 times
        if (originalRequest._retryCount <= 3) {
          console.warn(`Rate limited. Retrying after ${delay}ms (attempt ${originalRequest._retryCount})`);
          
          await new Promise(resolve => setTimeout(resolve, delay));
          return customerApiClient(originalRequest);
        }
      }
    }

    // Handle 401 Unauthorized
    if (error.response?.status === 401) {
      // Clear auth token and redirect to login
      Cookies.remove('auth_token');
      Cookies.remove('auth_user');
      window.location.href = '/auth/login';
    }
    
    return Promise.reject(error);
  }
);

// API Service Class
export class CustomerApiService {
  public api: AxiosInstance;
  private pendingRequests: Map<string, Promise<unknown>> = new Map();

  constructor() {
    this.api = customerApiClient;
  }

  // Request deduplication helper
  private async deduplicateRequest<T>(key: string, requestFn: () => Promise<T>): Promise<T> {
    if (this.pendingRequests.has(key)) {
      return this.pendingRequests.get(key) as Promise<T>;
    }

    const promise = requestFn().finally(() => {
      this.pendingRequests.delete(key);
    });

    this.pendingRequests.set(key, promise);
    return promise;
  }

  // Set auth token
  setAuthToken(token: string) {
    this.api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }

  // Remove auth token
  removeAuthToken() {
    delete this.api.defaults.headers.common['Authorization'];
  }

  async logout() {
    const response = await customerAuthApiClient.post('/logout');
    return processApiResponse(response);
  }

  async refreshToken() {
    const response = await customerAuthApiClient.post('/refresh');
    return processApiResponse(response);
  }

  // 2FA endpoints
  async generateTwoFactorCode(userId: string, action: string) {
    const response = await customerAuthApiClient.post('/generate-2fa', { user_id: userId, action });
    return processApiResponse(response);
  }

  async verify2FA(data: { user_id: string; code: string; unique: string }) {
    const response = await customerAuthApiClient.post('/verify-2fa', data);

    // Handle response structure consistently with login
    if (processApiResponse(response)?.data) {
      const authData = processApiResponse(response).data;
      
      // Map backend field names to frontend expected format
      const mappedAuthData = {
        access_token: authData.access_token,
        user: {
          id: authData.user.user_id,
          firstName: authData.user.first_name,
          lastName: authData.user.last_name,
          email: authData.user.email,
          roles: authData.user.roles || [],
          isAdmin: (authData.user.roles || []).includes('administrator'),
          profileImage: authData.user.profile_image,
          createdAt: authData.user.created_at || new Date().toISOString(),
          lastLogin: authData.user.last_login,
          organizationName: authData.user.organization_name,
          two_factor_enabled: authData.user.two_factor_enabled
        }
      };
      
      return mappedAuthData;
    }

    return processApiResponse(response);
  }

  async setupTwoFactorAuth(data: { access_token: string; user_id: string }) {
    const response = await customerAuthApiClient.post('/setup-2fa', data);
    return processApiResponse(response);
  }

  // User profile endpoints
  async getProfile() {
    return this.deduplicateRequest('getProfile', async () => {
      const response = await this.api.get('/users/profile');
      return processApiResponse(response);
    });
  }

  async updateProfile(profileData: ProfileUpdateData) {
    const response = await this.api.put('/users/profile', profileData);
    return processApiResponse(response);
  }

  // Addressing endpoints
  async getAddresses() {
    const response = await this.api.get('/address/all');
    return processApiResponse(response);
  }

  async createAddress(addressData: CreateAddressData) {
    const response = await this.api.post('/address/create', addressData);
    return processApiResponse(response);
  }

  async getAddress(id: string) {
    const response = await this.api.get(`/address/${id}`);
    return processApiResponse(response);
  }

  async editAddress(addressData: EditAddressData) {
    const { address_id, ...updateData } = addressData;
    if (!address_id) {
      throw new Error('Address ID is required for updating');
    }
    const response = await this.api.put(`/address/${address_id}`, updateData);
    return processApiResponse(response);
  }

  async getAddressesByEntity(entityType: string, entityId: string) {
    const response = await this.api.get(`/address/all?entity_type=${encodeURIComponent(entityType)}&entity_id=${encodeURIComponent(entityId)}`);
    return processApiResponse(response);
  }

  async deleteAddress(id: string) {
    const response = await this.api.delete(`/address/soft/${id}`);
    return processApiResponse(response);
  }

  async searchPostcodes(searchParams: SearchPostcodes) {
    const response = await this.api.post('/postal-codes/search', searchParams);
    return processApiResponse(response);
  }

  // License endpoints
  async getLicenses(params?: { status?: string; page?: number; limit?: number }) {
    const response = await this.api.get('/licenses', { params });
    return processApiResponse(response);
  }

  async getLicense(id: string) {
    const response = await this.api.get(`/licenses/${id}`);
    return processApiResponse(response);
  }

  async createLicenseApplication(applicationData: LicenseApplicationData) {
    const response = await this.api.post('/license-applications', applicationData);
    return processApiResponse(response);
  }

  // License Types endpoints
  async getLicenseTypes(params?: { page?: number; limit?: number }) {
    const response = await this.api.get('/license-types', { params });
    return processApiResponse(response);
  }

  async getLicenseType(id: string) {
    const response = await this.api.get(`/license-types/${id}`);
    return processApiResponse(response);
  }

  // License Categories endpoints
  async getLicenseCategories(params?: { page?: number; limit?: number }) {
    const response = await this.api.get('/license-categories', { params });
    return processApiResponse(response);
  }

  async getLicenseCategoriesByType(licenseTypeId: string) {
    const response = await this.api.get(`/license-categories/by-license-type/${licenseTypeId}`);
    return processApiResponse(response);
  }

  async getLicenseCategoryTree(licenseTypeId: string) {
    const response = await this.api.get(`/license-categories/license-type/${licenseTypeId}/tree`);
    return processApiResponse(response);
  }

  async getLicenseCategory(id: string) {
    const response = await this.api.get(`/license-categories/${id}`);
    return processApiResponse(response);
  }

  // Application endpoints
  async getApplications(params?: { status?: string; page?: number; limit?: number }) {
    const response = await this.api.get('/applications', { params });
    return processApiResponse(response);
  }

  async getApplication(id: string) {
    const response = await this.api.get(`/applications/${id}`);
    return processApiResponse(response);
  }

  async createApplication(applicationData: any) {
    const response = await this.api.post('/applications', applicationData);
    return processApiResponse(response);
  }

  async updateApplication(id: string, applicationData: Partial<LicenseApplicationData>) {
    const response = await this.api.put(`/applications/${id}`, applicationData);
    return processApiResponse(response);
  }

  // Payment endpoints
  async getPayments(params?: { status?: string; page?: number; limit?: number }) {
    const response = await this.api.get('/payments', { params });
    return processApiResponse(response);
  }

  async getPayment(id: string) {
    const response = await this.api.get(`/payments/${id}`);
    return processApiResponse(response);
  }

  async createPayment(paymentData: PaymentCreateData) {
    const response = await this.api.post('/payments', paymentData);
    return processApiResponse(response);
  }

  // Document endpoints
  async getDocuments(params?: { type?: string; page?: number; limit?: number }) {
    const response = await this.api.get('/documents', { params });
    return processApiResponse(response);
  }

  async uploadDocument(formData: FormData) {
    const response = await this.api.post('/documents/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return processApiResponse(response);
  }

  async downloadDocument(id: string) {
    const response = await this.api.get(`/documents/${id}/download`, {
      responseType: 'blob',
    });
    return processApiResponse(response);
  }

  // Dashboard statistics
  async getDashboardStats() {
    const response = await this.api.get('/dashboard/stats');
    return processApiResponse(response);
  }

  // Notifications
  async getNotifications(params?: { read?: boolean; page?: number; limit?: number }) {
    const response = await this.api.get('/notifications', { params });
    return processApiResponse(response);
  }

  async markNotificationAsRead(id: string) {
    const response = await this.api.patch(`/notifications/${id}/read`);
    return processApiResponse(response);
  }

  // Procurement endpoints
  async getTenders(params?: { status?: string; category?: string; page?: number; limit?: number }) {
    const response = await this.api.get('/procurement/tenders', { params });
    return processApiResponse(response);
  }

  async getTender(id: string) {
    const response = await this.api.get(`/procurement/tenders/${id}`);
    return processApiResponse(processApiResponse(response));
  }

  async payForTenderAccess(tenderId: string, paymentData: TenderPaymentData) {
    const response = await this.api.post(`/procurement/tenders/${tenderId}/pay-access`, paymentData);
    return processApiResponse(processApiResponse(response));
  }

  async downloadTenderDocument(documentId: string) {
    const response = await this.api.get(`/procurement/documents/${documentId}/download`, {
      responseType: 'blob',
    });
    return processApiResponse(processApiResponse(response));
  }

  async getMyBids(params?: { status?: string; page?: number; limit?: number }) {
    const response = await this.api.get('/procurement/my-bids', { params });
    return processApiResponse(processApiResponse(response));
  }

  async getBid(id: string) {
    const response = await this.api.get(`/procurement/bids/${id}`);
    return processApiResponse(processApiResponse(response));
  }

  async submitBid(formData: FormData) {
    const response = await this.api.post('/procurement/bids', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return processApiResponse(processApiResponse(response));
  }

  async updateBid(id: string, formData: FormData) {
    const response = await this.api.put(`/procurement/bids/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return processApiResponse(processApiResponse(response));
  }

  async getProcurementPayments(params?: { status?: string; page?: number; limit?: number }) {
    const response = await this.api.get('/procurement/payments', { params });
    return processApiResponse(processApiResponse(response));
  }

  async getProcurementPayment(id: string) {
    const response = await this.api.get(`/procurement/payments/${id}`);
    return processApiResponse(processApiResponse(response));
  }

  // Consumer Affairs endpoints
  async getComplaints(params?: { status?: string; category?: string; page?: number; limit?: number }) {
    const response = await this.api.get('/consumer-affairs/complaints', { params });
    return processApiResponse(processApiResponse(response));
  }

  async getComplaint(id: string) {
    const response = await this.api.get(`/consumer-affairs/complaints/${id}`);
    return processApiResponse(processApiResponse(response));
  }

  async submitComplaint(complaintData: ComplaintData) {
    const formData = new FormData();
    formData.append('title', complaintData.title);
    formData.append('description', complaintData.description);
    formData.append('category', complaintData.category);

    if (complaintData.attachments) {
      complaintData.attachments.forEach((file, index) => {
        formData.append(`attachments[${index}]`, file);
      });
    }

    const response = await this.api.post('/consumer-affairs/complaints', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return processApiResponse(processApiResponse(response));
  }

  async updateComplaint(id: string, updates: Partial<ComplaintData>) {
    const response = await this.api.put(`/consumer-affairs/complaints/${id}`, updates);
    return processApiResponse(processApiResponse(response));
  }

  async downloadComplaintAttachment(complaintId: string, attachmentId: string) {
    const response = await this.api.get(`/consumer-affairs/complaints/${complaintId}/attachments/${attachmentId}/download`, {
      responseType: 'blob',
    });
    return processApiResponse(processApiResponse(response));
  }
}

// Export singleton instance
export const customerApi = new CustomerApiService();

// Export axios instance for direct use if needed
export { customerApiClient };

// Export types
export interface ApiResponse<T = unknown> {
  success: boolean;
  data: T;
  message?: string;
  errors?: string[] | { [key: string]: string[] } | string;
}

export interface PaginatedResponse<T = unknown> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface License {
  id: string;
  licenseNumber: string;
  type: string;
  status: 'active' | 'expired' | 'suspended' | 'pending';
  issueDate: string;
  expirationDate: string;
  organizationName: string;
  description?: string;
}

export interface Application {
  id: string;
  applicationNumber: string;
  type: string;
  status: 'draft' | 'submitted' | 'under_review' | 'approved' | 'rejected';
  submittedDate: string;
  lastUpdated: string;
  organizationName: string;
  description?: string;
}

export interface Payment {
  id: string;
  invoiceNumber: string;
  amount: number;
  currency: string;
  status: 'pending' | 'paid' | 'overdue' | 'cancelled';
  dueDate: string;
  paidDate?: string;
  issueDate: string;
  description: string;
  paymentType: string;
  clientName: string;
  clientEmail: string;
  paymentMethod?: string;
  notes?: string;
  relatedLicense?: string;
  relatedApplication?: string;
}

export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  organizationName?: string;
  roles: string[];
  isAdmin: boolean;
  profileImage?: string;
  createdAt: string;
  lastLogin?: string;
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  two_factor_enabled?: boolean;
}

export interface ProfileUpdateData {
  firstName?: string;
  lastName?: string;
  email?: string;
  organizationName?: string;
  profileImage?: string;
}

export interface CreateAddressData {
  address_type: string;
  entity_type?: string;
  entity_id?:string;
  address_line_1: string;
  address_line_2?: string;
  postal_code: string;
  country: string;
  city: string;
}

export interface EditAddressData {
  address_id: string;
  address_type?: string;
  address_line_1?: string;
  address_line_2?: string;
  postal_code?: string;
  country?: string;
  city?: string;
}

export interface SearchPostcodes {
  region?: string;
  district?: string;
  location?: string;
  postal_code?: string;
}

export interface PostalCodeLookupResult {
  postal_code_id: string;
  region: string;
  district: string;
  location: string;
  postal_code: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string | null;
}


export interface LicenseApplicationData {
  type: string;
  organizationName: string;
  description?: string;
  contactEmail?: string;
  contactPhone?: string;
  businessAddress?: string;
  businessType?: string;
  requestedStartDate?: string;
  additionalDocuments?: string[];
  notes?: string;
}

export interface PaymentCreateData {
  amount: number;
  currency: string;
  dueDate: string;
  issueDate: string;
  description: string;
  paymentType: string;
  clientName: string;
  clientEmail: string;
  paymentMethod?: string;
  notes?: string;
  relatedLicense?: string;
  relatedApplication?: string;
}

export interface TenderPaymentData {
  amount: number;
  currency: string;
  paymentMethod: string;
  description?: string;
}

export interface ComplaintData {
  title: string;
  description: string;
  category: string;
  attachments?: File[];
}
