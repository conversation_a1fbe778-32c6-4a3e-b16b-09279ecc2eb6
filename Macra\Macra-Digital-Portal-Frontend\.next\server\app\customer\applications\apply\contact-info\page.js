(()=>{var e={};e.id=8734,e.ids=[8734],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24990:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\applications\\\\apply\\\\contact-info\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\contact-info\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},46332:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>f});var r=a(60687),n=a(43210),i=a(16189),o=a(94391),s=a(13128),c=a(63213),l=a(76377),p=a(99798),d=a(25890),_=a(78637);a(55457);var m=a(36212),u=a(51278);let y=new m.ef,h={async createContactPerson(e){let t=await y.api.post("/contact-persons",e);return(0,u.zp)(t)},async getContactPerson(e){let t=await y.api.get(`/contact-persons/${e}`);return(0,u.zp)(t)},async updateContactPerson(e){let{contact_id:t,...a}=e,r=await y.api.put(`/contact-persons/${t}`,a);return(0,u.zp)(r)},async deleteContactPerson(e){await y.api.delete(`/contact-persons/${e}`)},async getContactPersonsByApplication(e){let t=await y.api.get(`/contact-persons/application/${e}`);return(0,u.zp)(t)},async getContactPersonsByApplicationGrouped(e){let t=await y.api.get(`/contact-persons/application/${e}/grouped`);return(0,u.zp)(t)},async getContactPersonsByApplicant(e){let t=await y.api.get(`/contact-persons/application/${e}`);return(0,u.zp)(t)},async getContactPersonsByApplicantGrouped(e){let t=await y.api.get(`/contact-persons/application/${e}/grouped`);return(0,u.zp)(t)},async setPrimaryContact(e,t){let a=await y.api.put(`/contact-persons/${t}/set-primary`,{application_id:e});return(0,u.zp)(a)},async searchContactPersons(e){let t=await y.api.get(`/contact-persons/search?q=${encodeURIComponent(e)}`);return(0,u.zp)(t)},async createPrimaryContact(e,t){return this.createContactPerson({...t,application_id:e,is_primary:!0})},async createSecondaryContact(e,t){return this.createContactPerson({...t,application_id:e,is_primary:!1})}};var g=a(90678);function f(){let e=(0,i.useSearchParams)(),{isAuthenticated:t,loading:a}=(0,c.A)(),m=e.get("license_category_id"),u=e.get("application_id"),[y,f]=(0,n.useState)(!0),[x,b]=(0,n.useState)(!1),[v,P]=(0,n.useState)(null),[w,j]=(0,n.useState)(!1),[C,k]=(0,n.useState)({}),[q,E]=(0,n.useState)(null),[$,N]=(0,n.useState)(null),{handleNext:A,handlePrevious:M,nextStep:z}=(0,d.f)({currentStepRoute:"contact-info",licenseCategoryId:m,applicationId:u}),[I,S]=(0,n.useState)({primary_contact_first_name:"",primary_contact_last_name:"",primary_contact_middle_name:"",primary_contact_designation:"",primary_contact_email:"",primary_contact_phone:"",secondary_contact_first_name:"",secondary_contact_last_name:"",secondary_contact_middle_name:"",secondary_contact_designation:"",secondary_contact_email:"",secondary_contact_phone:"",website:"",social_media_facebook:"",social_media_twitter:"",social_media_linkedin:""}),D=(e,t)=>{let a="string"==typeof t?t:t.target.value;S(t=>({...t,[e]:a})),j(!0),$&&N(null),C[e]&&k(t=>{let a={...t};return delete a[e],a})},B=async()=>{if(!u)return P("Application ID is required"),!1;b(!0);try{let e=(0,g.oQ)(I,"contactInfo");if(!e.isValid)return k(e.errors||{}),!1;let t={};I.primary_contact_designation&&(I.primary_contact_designation.length<5||I.primary_contact_designation.length>50)&&(t.primary_contact_designation="Designation must be between 5 and 50 characters"),I.secondary_contact_designation&&(I.secondary_contact_designation.length<5||I.secondary_contact_designation.length>50)&&(t.secondary_contact_designation="Designation must be between 5 and 50 characters");let a=/^[+]?[\d\s\-()]+$/;if(I.primary_contact_phone&&!a.test(I.primary_contact_phone)&&(t.primary_contact_phone="Invalid phone number format"),I.secondary_contact_phone&&!a.test(I.secondary_contact_phone)&&(t.secondary_contact_phone="Invalid phone number format"),Object.keys(t).length>0)return k(t),!1;if(!(await _.k.getApplication(u)).applicant_id)throw Error("No applicant found for this application");let r=[];try{r=(await h.getContactPersonsByApplication(u)).data}catch(e){r=[]}if(I.primary_contact_first_name&&I.primary_contact_last_name){let e={application_id:u,first_name:I.primary_contact_first_name,last_name:I.primary_contact_last_name,middle_name:I.primary_contact_middle_name||void 0,designation:I.primary_contact_designation,email:I.primary_contact_email,phone:I.primary_contact_phone,is_primary:!0},t=r.find(e=>e.is_primary);t?await h.updateContactPerson({contact_id:t.contact_id,...e}):await h.createContactPerson(e)}if(I.secondary_contact_first_name&&I.secondary_contact_last_name){let e={application_id:u,first_name:I.secondary_contact_first_name,last_name:I.secondary_contact_last_name,middle_name:I.secondary_contact_middle_name||void 0,designation:I.secondary_contact_designation,email:I.secondary_contact_email,phone:I.secondary_contact_phone,is_primary:!1},t=r.find(e=>!e.is_primary&&"Emergency Contact"!==e.designation);t?await h.updateContactPerson({contact_id:t.contact_id,...e}):await h.createContactPerson(e)}try{await _.k.updateApplication(u,{current_step:4,progress_percentage:57})}catch(e){}return j(!1),N("Contact information saved successfully!"),k({}),setTimeout(()=>{N(null)},5e3),!0}catch(e){if(e.response?.data?.message)if(Array.isArray(e.response.data.message)){let t={};e.response.data.message.forEach(e=>{e.includes("designation")?(t.primary_contact_designation="Designation must be between 5 and 50 characters",t.secondary_contact_designation="Designation must be between 5 and 50 characters"):e.includes("phone")?(t.primary_contact_phone="Invalid phone number format",t.secondary_contact_phone="Invalid phone number format"):e.includes("email")&&(t.primary_contact_email="Invalid email format",t.secondary_contact_email="Invalid email format")}),k(t)}else k({save:e.response.data.message});else k({save:"Failed to save contact information. Please try again."});return!1}finally{b(!1)}},T=async()=>{await A(B)};return a||y?(0,r.jsx)(o.A,{children:(0,r.jsx)("div",{className:"flex justify-center items-center min-h-[400px]",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"})})}):v?(0,r.jsx)(o.A,{children:(0,r.jsx)("div",{className:"max-w-4xl mx-auto p-6",children:(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,r.jsx)("p",{className:"text-red-700",children:v})})})}):(0,r.jsx)(o.A,{children:(0,r.jsxs)(s.A,{onNext:T,onPrevious:()=>{M()},onSave:B,showNextButton:!0,showPreviousButton:!0,showSaveButton:!0,nextButtonText:z?`Continue to ${z.name}`:"Continue",previousButtonText:"Back to Previous Step",saveButtonText:"Save Contact Information",nextButtonDisabled:!1,isSaving:x,children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:u?"Edit Contact Information":"Contact Information"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:u?"Update your contact information below.":"Provide contact details for your organization."}),u&&!q&&(0,r.jsx)("div",{className:"mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg",children:(0,r.jsx)("p",{className:"text-sm text-green-700 dark:text-green-300",children:"✅ Editing existing application. Basic contact information has been loaded from your applicant details."})}),q&&(0,r.jsx)("div",{className:"mt-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-yellow-700 dark:text-yellow-300",children:["⚠️ ",q]})})]}),(0,r.jsx)(p.bc,{successMessage:$,errorMessage:C.save,validationErrors:Object.fromEntries(Object.entries(C).filter(([e])=>"save"!==e))}),(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h3",{className:"text-md font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Primary Contact Person"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsx)(l.ks,{label:"First Name",value:I.primary_contact_first_name,onChange:e=>D("primary_contact_first_name",e),error:C.primary_contact_first_name,required:!0,placeholder:"Enter first name"}),(0,r.jsx)(l.ks,{label:"Last Name",value:I.primary_contact_last_name,onChange:e=>D("primary_contact_last_name",e),error:C.primary_contact_last_name,required:!0,placeholder:"Enter last name"}),(0,r.jsx)(l.ks,{label:"Middle Name",value:I.primary_contact_middle_name,onChange:e=>D("primary_contact_middle_name",e),error:C.primary_contact_middle_name,placeholder:"Enter middle name (optional)"}),(0,r.jsx)(l.ks,{label:"Designation/Job Title",value:I.primary_contact_designation,onChange:e=>D("primary_contact_designation",e),error:C.primary_contact_designation,required:!0,placeholder:"Enter job title (5-50 characters)",helperText:"Must be between 5 and 50 characters"}),(0,r.jsx)(l.ks,{label:"Email Address",type:"email",value:I.primary_contact_email,onChange:e=>D("primary_contact_email",e),error:C.primary_contact_email,required:!0,placeholder:"Enter email address"}),(0,r.jsx)(l.ks,{label:"Phone Number",value:I.primary_contact_phone,onChange:e=>D("primary_contact_phone",e),error:C.primary_contact_phone,required:!0,placeholder:"Enter phone number (+265123456789)",helperText:"International format with 10-20 digits"})]})]}),(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h3",{className:"text-md font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Secondary Contact Person (Optional)"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsx)(l.ks,{label:"First Name",value:I.secondary_contact_first_name,onChange:e=>D("secondary_contact_first_name",e),error:C.secondary_contact_first_name,placeholder:"Enter first name"}),(0,r.jsx)(l.ks,{label:"Last Name",value:I.secondary_contact_last_name,onChange:e=>D("secondary_contact_last_name",e),error:C.secondary_contact_last_name,placeholder:"Enter last name"}),(0,r.jsx)(l.ks,{label:"Middle Name",value:I.secondary_contact_middle_name,onChange:e=>D("secondary_contact_middle_name",e),error:C.secondary_contact_middle_name,placeholder:"Enter middle name (optional)"}),(0,r.jsx)(l.ks,{label:"Designation/Job Title",value:I.secondary_contact_designation,onChange:e=>D("secondary_contact_designation",e),error:C.secondary_contact_designation,placeholder:"Enter job title (5-50 characters)",helperText:"Must be between 5 and 50 characters"}),(0,r.jsx)(l.ks,{label:"Email Address",type:"email",value:I.secondary_contact_email,onChange:e=>D("secondary_contact_email",e),error:C.secondary_contact_email,placeholder:"Enter email address"}),(0,r.jsx)(l.ks,{label:"Phone Number",value:I.secondary_contact_phone,onChange:e=>D("secondary_contact_phone",e),error:C.secondary_contact_phone,placeholder:"Enter phone number (+265123456789)",helperText:"International format with 10-20 digits"})]})]})]})})}},55457:(e,t,a)=>{"use strict";a.d(t,{W:()=>i});var r=a(51278),n=a(12234);let i={async createApplicant(e){try{let t=await n.uE.post("/applicants",e);return(0,r.zp)(t)}catch(e){throw e}},async getApplicant(e){try{let t=await n.uE.get(`/applicants/${e}`);return(0,r.zp)(t)}catch(e){throw e}},async updateApplicant(e,t){try{let a=await n.uE.put(`/applicants/${e}`,t);return(0,r.zp)(a)}catch(e){throw e}},async getApplicantsByUser(){try{let e=await n.uE.get("/applicants/by-user");return(0,r.zp)(e)}catch(e){throw e}},async deleteApplicant(e){try{let t=await n.uE.delete(`/applicants/${e}`);return(0,r.zp)(t)}catch(e){throw e}}}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},76206:(e,t,a)=>{Promise.resolve().then(a.bind(a,46332))},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},90678:(e,t,a)=>{"use strict";a.d(t,{oQ:()=>n});let r={email:/^[^\s@]+@[^\s@]+\.[^\s@]+$/,phone:/^(\+265|0)[0-9]{8,9}$/,percentage:/^(100|[1-9]?[0-9])$/};r.email,r.phone,r.percentage;let n=(e,t)=>{let a={};switch(t){case"applicantInfo":["name","business_registration_number","tpin","website","email","phone","date_incorporation","place_incorporation"].forEach(t=>{e[t]&&("string"!=typeof e[t]||""!==e[t].trim())||(a[t]=`${t.replace(/_/g," ")} is required`)}),e.email&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.email)&&(a.email="Please enter a valid email address"),e.phone&&!/^[+]?[\d\s\-()]+$/.test(e.phone)&&(a.phone="Please enter a valid phone number"),e.fax&&""!==e.fax.trim()&&!/^[+]?[\d\s\-()]+$/.test(e.fax)&&(a.fax="Please enter a valid fax number"),e.date_incorporation&&!/^\d{4}-\d{2}-\d{2}$/.test(e.date_incorporation)&&(a.date_incorporation="Please enter a valid date (YYYY-MM-DD)");break;case"companyProfile":["company_name","business_registration_number","tax_number","company_type","incorporation_date","incorporation_place","company_email","company_phone","company_address","company_city","company_district","number_of_employees","annual_revenue","business_description"].forEach(t=>{e[t]&&("string"!=typeof e[t]||""!==e[t].trim())||(a[t]=`${t.replace(/_/g," ")} is required`)}),e.company_email&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.company_email)&&(a.company_email="Please enter a valid email address");break;case"businessInfo":["business_model","operational_structure","target_market","competitive_advantage","facilities_description","equipment_description","operational_areas","service_delivery_model","quality_assurance","customer_support"].forEach(t=>{e[t]&&("string"!=typeof e[t]||""!==e[t].trim())||(a[t]=`${t.replace(/_/g," ")} is required`)});break;case"serviceScope":["services_offered","geographic_coverage","service_categories","target_customers","service_capacity"].forEach(t=>{e[t]&&("string"!=typeof e[t]||""!==e[t].trim())||(a[t]=`${t.replace(/_/g," ")} is required`)});break;case"businessPlan":["executive_summary","market_analysis","financial_projections","revenue_model","investment_requirements","implementation_timeline","risk_analysis","success_metrics"].forEach(t=>{e[t]&&("string"!=typeof e[t]||""!==e[t].trim())||(a[t]=`${t.replace(/_/g," ")} is required`)});break;case"legalHistory":e.compliance_record&&""!==e.compliance_record.trim()||(a.compliance_record="Compliance record is required"),e.declaration_accepted||(a.declaration_accepted="You must accept the declaration to proceed"),e.criminal_history&&(!e.criminal_details||""===e.criminal_details.trim())&&(a.criminal_details="Please provide details of your criminal history"),e.bankruptcy_history&&(!e.bankruptcy_details||""===e.bankruptcy_details.trim())&&(a.bankruptcy_details="Please provide details of your bankruptcy history"),e.regulatory_actions&&(!e.regulatory_details||""===e.regulatory_details.trim())&&(a.regulatory_details="Please provide details of regulatory actions"),e.litigation_history&&(!e.litigation_details||""===e.litigation_details.trim())&&(a.litigation_details="Please provide details of litigation history");break;case"address":["address_line_1","city","country"].forEach(t=>{e[t]&&("string"!=typeof e[t]||""!==e[t].trim())||(a[t]=`${t.replace(/_/g," ")} is required`)});break;case"contactInfo":["primary_contact_first_name","primary_contact_last_name","primary_contact_designation","primary_contact_email","primary_contact_phone"].forEach(t=>{e[t]&&("string"!=typeof e[t]||""!==e[t].trim())||(a[t]=`${t.replace(/_/g," ")} is required`)}),e.primary_contact_email&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.primary_contact_email)&&(a.primary_contact_email="Please enter a valid email address"),e.secondary_contact_email&&""!==e.secondary_contact_email.trim()&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.secondary_contact_email)&&(a.secondary_contact_email="Please enter a valid email address"),e.primary_contact_phone&&!/^[+]?[\d\s\-()]+$/.test(e.primary_contact_phone)&&(a.primary_contact_phone="Please enter a valid phone number"),e.secondary_contact_phone&&""!==e.secondary_contact_phone.trim()&&!/^[+]?[\d\s\-()]+$/.test(e.secondary_contact_phone)&&(a.secondary_contact_phone="Please enter a valid phone number")}return{isValid:0===Object.keys(a).length,errors:a}}},93145:(e,t,a)=>{Promise.resolve().then(a.bind(a,24990))},94735:e=>{"use strict";e.exports=require("events")},96525:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>o.a,__next_app__:()=>d,pages:()=>p,routeModule:()=>_,tree:()=>l});var r=a(65239),n=a(48088),i=a(88170),o=a.n(i),s=a(30893),c={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>s[e]);a.d(t,c);let l={children:["",{children:["customer",{children:["applications",{children:["apply",{children:["contact-info",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,24990)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\contact-info\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,68736)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,p=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\contact-info\\page.tsx"],d={require:a,loadChunk:()=>Promise.resolve()},_=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/customer/applications/apply/contact-info/page",pathname:"/customer/applications/apply/contact-info",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})}};var t=require("../../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[4447,7498,1658,5814,7563,6893,140,1887],()=>a(96525));module.exports=r})();