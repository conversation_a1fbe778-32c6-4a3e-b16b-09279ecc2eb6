'use client';

import { useParams, useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { applicationService } from '@/services/applicationService';
import { evaluationService, Evaluation, EvaluationCriteria } from '@/services/evaluationService';
import { documentService as documentsService } from '@/services/documentService';
import ApplicationEvaluationView from '@/components/evaluation/ApplicationEvaluationView';

interface Application {
  application_id: string;
  application_number: string;
  applicant_id: string;
  license_category_id: string;
  status: string;
  current_step: number;
  progress_percentage: number;
  submitted_at?: string;
  created_at: string;
  updated_at: string;
  applicant?: any;
  license_category?: any;
}

interface Document {
  document_id: string;
  document_type: string;
  file_name: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  is_required: boolean;
  created_at: string;
}

export default function EvaluateApplicationPage() {
  const params = useParams();
  const router = useRouter();
  const { user, isAuthenticated, loading: authLoading } = useAuth();
  
  const licenseType = params['license-type'] as string;
  const applicationId = params['application-id'] as string;

  const [application, setApplication] = useState<Application | null>(null);
  const [evaluation, setEvaluation] = useState<Evaluation | null>(null);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);

  // Check if user is staff
  const isStaff = user?.roles?.some((role: string) => 
    ['admin', 'administrator', 'staff', 'moderator', 'manager'].includes(role.toLowerCase())
  ) || false;

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/auth/login');
      return;
    }

    if (!authLoading && !isStaff) {
      setError('Access denied. Staff privileges required.');
      return;
    }

    if (isAuthenticated && isStaff && applicationId) {
      loadData();
    }
  }, [isAuthenticated, authLoading, isStaff, applicationId]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load application details
      const appData = await applicationService.getApplication(applicationId);
      setApplication(appData);

      // Load existing evaluation or create template
      let evalData = await evaluationService.getEvaluationByApplication(applicationId);
      
      if (!evalData) {
        // Create evaluation template based on license type
        const template = evaluationService.getEvaluationTemplate(licenseType);
        evalData = {
          evaluation_id: '',
          application_id: applicationId,
          evaluator_id: user?.user_id || '',
          evaluation_type: licenseType,
          status: 'draft' as const,
          total_score: 0,
          recommendation: 'approve' as const,
          evaluators_notes: '',
          shareholding_compliance: undefined,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          criteria: template,
        };
      }
      
      setEvaluation(evalData);

      // Load application documents
      try {
        const docsData = await documentsService.getDocumentsByEntity('application', applicationId);
        setDocuments(docsData);
      } catch (docError) {
        console.warn('Could not load documents:', docError);
        setDocuments([]);
      }

    } catch (err: any) {
      console.error('Error loading evaluation data:', err);
      setError(err.message || 'Failed to load evaluation data');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveEvaluation = async (evaluationData: {
    criteria: EvaluationCriteria[];
    evaluators_notes: string;
    shareholding_compliance?: boolean;
  }) => {
    if (!evaluation || !user) return;

    try {
      setSaving(true);
      
      const totalScore = evaluationService.calculateTotalScore(evaluationData.criteria);
      const recommendation = totalScore >= 70 ? 'approve' : 'reject';

      const updateData = {
        total_score: totalScore,
        recommendation,
        evaluators_notes: evaluationData.evaluators_notes,
        shareholding_compliance: evaluationData.shareholding_compliance,
        criteria: evaluationData.criteria,
      };

      let updatedEvaluation;
      
      if (evaluation.evaluation_id) {
        // Update existing evaluation
        updatedEvaluation = await evaluationService.updateEvaluation(evaluation.evaluation_id, updateData);
      } else {
        // Create new evaluation
        updatedEvaluation = await evaluationService.createEvaluation({
          application_id: applicationId,
          evaluator_id: user.user_id,
          evaluation_type: licenseType,
          ...updateData,
        });
      }

      setEvaluation(updatedEvaluation);
      
      // Show success message
      alert('Evaluation saved successfully!');
      
    } catch (err: any) {
      console.error('Error saving evaluation:', err);
      alert('Failed to save evaluation: ' + (err.message || 'Unknown error'));
    } finally {
      setSaving(false);
    }
  };

  const handleSubmitEvaluation = async (evaluationData: {
    criteria: EvaluationCriteria[];
    evaluators_notes: string;
    shareholding_compliance?: boolean;
  }) => {
    if (!evaluation || !user) return;

    try {
      setSaving(true);
      
      const totalScore = evaluationService.calculateTotalScore(evaluationData.criteria);
      const recommendation = totalScore >= 70 ? 'approve' : 'reject';

      const submitData = {
        total_score: totalScore,
        recommendation,
        evaluators_notes: evaluationData.evaluators_notes,
        shareholding_compliance: evaluationData.shareholding_compliance,
        criteria: evaluationData.criteria,
      };

      let submittedEvaluation;
      
      if (evaluation.evaluation_id) {
        // Submit existing evaluation
        submittedEvaluation = await evaluationService.submitEvaluation(evaluation.evaluation_id, submitData);
      } else {
        // Create and submit new evaluation
        const newEvaluation = await evaluationService.createEvaluation({
          application_id: applicationId,
          evaluator_id: user.user_id,
          evaluation_type: licenseType,
          ...submitData,
        });
        
        submittedEvaluation = await evaluationService.submitEvaluation(newEvaluation.evaluation_id, {
          total_score: totalScore,
          recommendation,
          evaluators_notes: evaluationData.evaluators_notes,
          criteria: evaluationData.criteria,
        });
      }

      setEvaluation(submittedEvaluation);
      
      // Show success message and redirect
      alert(`Evaluation submitted successfully! Recommendation: ${recommendation.toUpperCase()}`);
      router.push(`/applications/${licenseType}`);
      
    } catch (err: any) {
      console.error('Error submitting evaluation:', err);
      alert('Failed to submit evaluation: ' + (err.message || 'Unknown error'));
    } finally {
      setSaving(false);
    }
  };

  const handleBack = () => {
    router.push(`/applications/${licenseType}`);
  };

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600">Loading evaluation...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="mb-4">
            <i className="ri-error-warning-line text-4xl text-red-500"></i>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error</h3>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            type="button"
            onClick={handleBack}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            <i className="ri-arrow-left-line mr-2"></i>
            Back to Applications
          </button>
        </div>
      </div>
    );
  }

  if (!application || !evaluation) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Application or evaluation data not found.</p>
          <button
            type="button"
            onClick={handleBack}
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            <i className="ri-arrow-left-line mr-2"></i>
            Back to Applications
          </button>
        </div>
      </div>
    );
  }

  return (
    <ApplicationEvaluationView
      application={application}
      evaluation={evaluation}
      documents={documents}
      onSave={handleSaveEvaluation}
      onSubmit={handleSubmitEvaluation}
      onBack={handleBack}
      saving={saving}
      licenseType={licenseType}
    />
  );
}
