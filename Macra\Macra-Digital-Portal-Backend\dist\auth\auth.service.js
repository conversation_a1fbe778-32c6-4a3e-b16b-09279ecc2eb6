"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AuthService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const users_service_1 = require("../users/users.service");
const two_factor_dto_1 = require("../dto/auth/two-factor.dto");
const speakeasy = __importStar(require("speakeasy"));
const qrcode = __importStar(require("qrcode"));
const bcrypt = __importStar(require("bcryptjs"));
const auth_constants_1 = require("../common/constants/auth.constants");
const email_service_1 = require("../common/services/email.service");
const device_info_service_1 = require("../common/services/device-info.service");
const error_handler_util_1 = require("../common/utils/error-handler.util");
const auth_types_1 = require("../common/types/auth.types");
let AuthService = AuthService_1 = class AuthService {
    usersService;
    jwtService;
    emailService;
    deviceInfoService;
    logger = new common_1.Logger(AuthService_1.name);
    constructor(usersService, jwtService, emailService, deviceInfoService) {
        this.usersService = usersService;
        this.jwtService = jwtService;
        this.emailService = emailService;
        this.deviceInfoService = deviceInfoService;
    }
    async validateUser(email, password) {
        this.logger.log(`Validating user: ${email}`);
        const user = await this.usersService.findByEmail(email);
        this.logger.log(`User found: ${user ? 'YES' : 'NO'}`);
        if (user) {
            const isPasswordValid = await this.usersService.validatePassword(password, user.password);
            this.logger.log(`Password valid: ${isPasswordValid ? 'YES' : 'NO'}`);
            if (isPasswordValid) {
                return user;
            }
        }
        return null;
    }
    async login(loginDto, req) {
        const { email } = loginDto;
        if (!(0, auth_types_1.isValidEmail)(email)) {
            throw new common_1.BadRequestException('Invalid email format');
        }
        const context = error_handler_util_1.ErrorHandler.createActionContext(email, 'login');
        try {
            error_handler_util_1.ErrorHandler.logInfo(this.logger, 'Login attempt started', context);
            const user = await this.validateUser(email, loginDto.password);
            if (!user) {
                error_handler_util_1.ErrorHandler.logWarning(this.logger, 'Invalid credentials provided', context);
                throw new common_1.UnauthorizedException(auth_constants_1.AuthMessages.INVALID_CREDENTIALS);
            }
            if (user.status !== 'active') {
                error_handler_util_1.ErrorHandler.logWarning(this.logger, 'Inactive account login attempt', error_handler_util_1.ErrorHandler.createUserContext(user, 'login', { status: user.status }));
                throw new common_1.UnauthorizedException(auth_constants_1.AuthMessages.ACCOUNT_INACTIVE);
            }
            await this.usersService.updateLastLogin(user.user_id);
            error_handler_util_1.ErrorHandler.logInfo(this.logger, 'Last login updated successfully', error_handler_util_1.ErrorHandler.createUserContext(user, 'login'));
            const payload = this.createJwtPayload(user);
            const requires2FA = auth_constants_1.AuthUtils.requires2FA(user.two_factor_enabled, user.last_login);
            const accessToken = requires2FA ? '' : this.jwtService.sign(payload);
            const response = this.createAuthResponse(user, accessToken, requires2FA);
            if (requires2FA) {
                error_handler_util_1.ErrorHandler.logInfo(this.logger, '2FA required, sending OTP', error_handler_util_1.ErrorHandler.createUserContext(user, 'login-2fa'));
                await this.generateTwoFactorCode(user.user_id, auth_constants_1.TwoFactorAction.LOGIN);
            }
            else {
                error_handler_util_1.ErrorHandler.logInfo(this.logger, 'Login successful without 2FA', error_handler_util_1.ErrorHandler.createUserContext(user, 'login'));
            }
            return response;
        }
        catch (error) {
            if (!(error instanceof common_1.UnauthorizedException)) {
                error_handler_util_1.ErrorHandler.handleError(this.logger, error, 'Login process failed', context);
            }
            throw error;
        }
    }
    async register(registerDto) {
        try {
            const user = await this.usersService.create(registerDto);
            const payload = this.createJwtPayload(user);
            const accessToken = this.jwtService.sign(payload);
            return this.createAuthResponse(user, accessToken);
        }
        catch (error) {
            throw new common_1.BadRequestException(error.message);
        }
    }
    async validateJwtPayload(payload) {
        return this.usersService.findByEmail(payload.email);
    }
    createJwtPayload(user) {
        return {
            email: user.email,
            sub: user.user_id,
            roles: user.roles?.map(role => role.name) || [],
        };
    }
    createAuthResponse(user, accessToken, requiresTwoFactor = false) {
        return {
            access_token: accessToken,
            user: {
                user_id: user.user_id,
                email: user.email,
                first_name: user.first_name,
                last_name: user.last_name,
                two_factor_enabled: user.two_factor_enabled,
                roles: user.roles?.map(role => role.name) || [],
                isStaff: auth_constants_1.AuthUtils.isStaffUser(user.roles),
            },
            requiresTwoFactor,
        };
    }
    async forgotPassword(forgotPasswordDto) {
        const user = await this.usersService.findByEmail(forgotPasswordDto.email);
        if (!user) {
            return { message: 'If the email exists, a password reset link has been sent.' };
        }
        await this.generateTwoFactorCode(user.user_id, auth_constants_1.TwoFactorAction.RESET);
        return { message: 'If the email exists, a password reset link has been sent.' };
    }
    async resetPassword(resetPasswordDto) {
        const user = await this.usersService.findById(resetPasswordDto.user_id);
        if (!user) {
            throw new common_1.BadRequestException(auth_constants_1.AuthMessages.INVALID_RESET_CODE);
        }
        if (await this.usersService.validatePassword(resetPasswordDto.new_password, user.password)) {
            throw new common_1.BadRequestException(auth_constants_1.AuthMessages.PASSWORD_SAME_AS_CURRENT);
        }
        await this.usersService.updatePassword(user.user_id, resetPasswordDto.new_password);
        await this.usersService.clearTempTwoFactorCode(user.user_id);
        await this.usersService.clearTwoFactorCode(user.user_id);
        await this.emailService.sendPasswordResetEmail(user.email, {
            userName: user.first_name,
            loginUrl: `${process.env.FRONTEND_URL}${auth_constants_1.AuthConstants.URL_PATTERNS.LOGIN_PATH}`
        });
        return { message: auth_constants_1.AuthUtils.formatMessage(auth_constants_1.AuthMessages.PASSWORD_RESET_SUCCESS, { email: user.email }) };
    }
    async setupTwoFactorAuth(requestTwoFactorDto) {
        const userId = requestTwoFactorDto.user_id;
        const user = await this.usersService.findById(userId);
        if (!user) {
            this.logger.warn(`First time 2FA setup failed: User ID ${userId} not found!`);
            throw new common_1.BadRequestException(auth_constants_1.AuthMessages.USER_NOT_FOUND);
        }
        if (user.two_factor_enabled) {
            return {
                otpAuthUrl: '',
                qrCodeDataUrl: '',
                secret: '',
                message: auth_constants_1.AuthUtils.formatMessage(auth_constants_1.AuthMessages.TWO_FACTOR_ALREADY_ENABLED, { email: user.email }),
            };
        }
        const generateCode = await this.generateTwoFactorCode(requestTwoFactorDto.user_id, auth_constants_1.TwoFactorAction.VERIFY);
        const otpAuthUrl = generateCode.otpAuthUrl;
        const secret = generateCode.secret;
        const qrCodeDataUrl = await qrcode.toDataURL(otpAuthUrl);
        return {
            otpAuthUrl,
            qrCodeDataUrl,
            secret: secret,
            message: auth_constants_1.AuthUtils.formatMessage(auth_constants_1.AuthMessages.TWO_FACTOR_SETUP_SUCCESS, { email: user.email })
        };
    }
    async generateTwoFactorCode(userId, action) {
        if (!(0, auth_types_1.isValidUserId)(userId)) {
            throw new common_1.BadRequestException('Invalid user ID format');
        }
        const user = await this.usersService.findById(userId);
        if (!user) {
            this.logger.warn(`2FA attempt failed: User ID ${userId} not found`);
            throw new common_1.BadRequestException(auth_constants_1.AuthMessages.USER_NOT_FOUND);
        }
        const secret = speakeasy.generateSecret({
            name: auth_constants_1.AuthConstants.TWO_FACTOR.ISSUER_NAME,
            length: auth_constants_1.AuthConstants.TWO_FACTOR.SECRET_LENGTH
        });
        if (!secret.otpauth_url) {
            throw new common_1.InternalServerErrorException('Failed to generate OTP URL');
        }
        const token = speakeasy.totp({ secret: secret.base32, encoding: 'base32' });
        const hashedToken = await bcrypt.hash(token, auth_constants_1.AuthConstants.TWO_FACTOR.CODE_HASH_ROUNDS);
        const expiresAt = auth_constants_1.AuthUtils.createExpiryDate();
        const emailTemplate = action === auth_constants_1.TwoFactorAction.RESET ? auth_constants_1.EmailTemplates.RESET : auth_constants_1.EmailTemplates.TWO_FACTOR;
        const verifyUrl = email_service_1.EmailService.createVerificationUrl(userId, secret.base32, token, action, user.roles);
        try {
            if (action === auth_constants_1.TwoFactorAction.LOGIN) {
                this.logger.log(`Storing 2FA code for login: ${user.email}`);
                await this.usersService.setTwoFactorCode(userId, hashedToken, expiresAt);
            }
            else {
                this.logger.log(`Storing temp 2FA code for setup/reset: ${user.email}`);
                await this.usersService.setTwoFactorCodeTempReset(userId, secret.base32, hashedToken, expiresAt);
            }
            await this.emailService.send2FAEmail(user.email, emailTemplate, auth_constants_1.EmailSubjects.VERIFY_OTP, {
                name: user.first_name,
                message: auth_constants_1.TwoFactorMessages[action],
                verifyUrl,
            });
            this.logger.log(`2FA email sent to ${user.email} for action: ${action}`);
            return {
                message: auth_constants_1.AuthMessages.TWO_FACTOR_CODE_SENT,
                otpAuthUrl: secret.otpauth_url,
                hashedToken,
                secret: secret.base32,
            };
        }
        catch (error) {
            this.logger.error(`2FA generation failed for ${user.email}`, error);
            throw new common_1.InternalServerErrorException('Failed to generate or send 2FA code');
        }
    }
    async verifyTwoFactorCode(twoFactorDto, req) {
        const user = await this.usersService.findById(twoFactorDto.user_id);
        if (!user) {
            throw new common_1.BadRequestException(auth_constants_1.AuthMessages.USER_NOT_FOUND);
        }
        if (!user.two_factor_code || !user.two_factor_temp || user.two_factor_temp !== twoFactorDto.unique) {
            this.logger.warn(`Invalid unique code for user ${user.email}`);
            throw new common_1.BadRequestException(auth_constants_1.AuthMessages.INVALID_VERIFICATION_LINK);
        }
        if (!user.two_factor_next_verification || user.two_factor_next_verification < new Date()) {
            if (user.two_factor_next_verification) {
                this.logger.warn(`Expired code. Expired on: `, user.two_factor_next_verification);
            }
            throw new common_1.BadRequestException(auth_constants_1.AuthMessages.EXPIRED_VERIFICATION_LINK);
        }
        const compareToken = await bcrypt.compare(twoFactorDto.code, user.two_factor_code);
        if (!compareToken) {
            throw new common_1.BadRequestException(auth_constants_1.AuthMessages.INVALID_VERIFICATION_CODE);
        }
        const isLoginOTP = user.two_factor_enabled && user.email_verified_at;
        try {
            const expiresAt = new Date(Date.now() + 5 * 60 * 1000);
            await this.usersService.clearTwoFactorCode(user.user_id);
            if (isLoginOTP) {
                await this.usersService.setTwoFactorCode(user.user_id, twoFactorDto.code, expiresAt);
            }
            else {
                await this.usersService.enableTwoFactor(user.user_id, expiresAt);
                await this.usersService.verifyEmail(user.user_id);
                await this.usersService.setTwoFactorCode(user.user_id, twoFactorDto.code, expiresAt);
            }
        }
        catch (error) {
            this.logger.error('Failed to process two factor code', error);
        }
        const payload = this.createJwtPayload(user);
        const { ip, country, city, userAgent } = await this.deviceInfoService.getDeviceInfo(req);
        const subject = isLoginOTP ? auth_constants_1.EmailSubjects.LOGIN_DETAILS : auth_constants_1.EmailSubjects.TWO_FACTOR_SETUP;
        const message = isLoginOTP
            ? auth_constants_1.AuthMessages.LOGIN_NOTIFICATION_MESSAGE
            : auth_constants_1.AuthMessages.TWO_FACTOR_ENABLED_MESSAGE;
        await this.emailService.sendLoginAlertEmail(user.email, subject, {
            userName: `${user.first_name.trim()} ${user.last_name.trim()}`,
            loginUrl: `${process.env.FRONTEND_URL}${auth_constants_1.AuthConstants.URL_PATTERNS.LOGIN_PATH}`,
            ip: ip || 'Unknown',
            country: country || 'Unknown',
            city: city || 'Unknown',
            userAgent: userAgent || 'Unknown',
            message: message
        });
        const accessToken = this.jwtService.sign(payload);
        const response = this.createAuthResponse(user, accessToken);
        return {
            ...response,
            user: {
                ...response.user,
                two_factor_enabled: true,
            },
            message: isLoginOTP ? auth_constants_1.AuthMessages.OTP_VERIFIED : auth_constants_1.AuthUtils.formatMessage(auth_constants_1.AuthMessages.TWO_FACTOR_ENABLED, { email: user.email })
        };
    }
};
exports.AuthService = AuthService;
__decorate([
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [two_factor_dto_1.RequestTwoFactorDto]),
    __metadata("design:returntype", Promise)
], AuthService.prototype, "setupTwoFactorAuth", null);
exports.AuthService = AuthService = AuthService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [users_service_1.UsersService,
        jwt_1.JwtService,
        email_service_1.EmailService,
        device_info_service_1.DeviceInfoService])
], AuthService);
//# sourceMappingURL=auth.service.js.map