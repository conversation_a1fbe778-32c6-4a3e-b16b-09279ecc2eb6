"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TasksController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/jwt-auth.guard");
const roles_guard_1 = require("../auth/roles.guard");
const roles_decorator_1 = require("../auth/roles.decorator");
const tasks_service_1 = require("./tasks.service");
const create_task_dto_1 = require("../dto/tasks/create-task.dto");
const update_task_dto_1 = require("../dto/tasks/update-task.dto");
const assign_task_dto_1 = require("../dto/tasks/assign-task.dto");
const nestjs_paginate_1 = require("nestjs-paginate");
const audit_interceptor_1 = require("../common/interceptors/audit.interceptor");
const audit_trail_entity_1 = require("../entities/audit-trail.entity");
let TasksController = class TasksController {
    tasksService;
    constructor(tasksService) {
        this.tasksService = tasksService;
    }
    create(createTaskDto, req) {
        return this.tasksService.create(createTaskDto, req.user.sub);
    }
    findAll(query) {
        return this.tasksService.findAll(query);
    }
    findUnassigned(query) {
        return this.tasksService.findUnassigned(query);
    }
    findAssigned(query) {
        return this.tasksService.findAssigned(query);
    }
    findMyTasks(query, req) {
        return this.tasksService.findAssignedToUser(req.user.sub, query);
    }
    getStats() {
        return this.tasksService.getTaskStats();
    }
    findOne(id) {
        return this.tasksService.findOne(id);
    }
    update(id, updateTaskDto) {
        return this.tasksService.update(id, updateTaskDto);
    }
    assign(id, assignTaskDto, req) {
        return this.tasksService.assign(id, assignTaskDto, req.user.sub);
    }
    reassign(id, assignTaskDto, req) {
        return this.tasksService.reassign(id, assignTaskDto, req.user.sub);
    }
    remove(id) {
        return this.tasksService.remove(id);
    }
};
exports.TasksController = TasksController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)('administrator', 'evaluator'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new task' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Task created successfully' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.CREATE,
        module: audit_trail_entity_1.AuditModule.TASK_MANAGEMENT,
        resourceType: 'Task',
        description: 'Create new task',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_a = typeof create_task_dto_1.CreateTaskDto !== "undefined" && create_task_dto_1.CreateTaskDto) === "function" ? _a : Object, Object]),
    __metadata("design:returntype", void 0)
], TasksController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)('administrator', 'evaluator'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all tasks with pagination' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Tasks retrieved successfully' }),
    __param(0, (0, nestjs_paginate_1.Paginate)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], TasksController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('unassigned'),
    (0, roles_decorator_1.Roles)('administrator', 'evaluator'),
    (0, swagger_1.ApiOperation)({ summary: 'Get unassigned tasks' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Unassigned tasks retrieved successfully' }),
    __param(0, (0, nestjs_paginate_1.Paginate)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], TasksController.prototype, "findUnassigned", null);
__decorate([
    (0, common_1.Get)('assigned'),
    (0, roles_decorator_1.Roles)('administrator', 'evaluator'),
    (0, swagger_1.ApiOperation)({ summary: 'Get assigned tasks' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Assigned tasks retrieved successfully' }),
    __param(0, (0, nestjs_paginate_1.Paginate)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], TasksController.prototype, "findAssigned", null);
__decorate([
    (0, common_1.Get)('assigned/me'),
    (0, roles_decorator_1.Roles)('administrator', 'evaluator'),
    (0, swagger_1.ApiOperation)({ summary: 'Get tasks assigned to current user' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'User tasks retrieved successfully' }),
    __param(0, (0, nestjs_paginate_1.Paginate)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], TasksController.prototype, "findMyTasks", null);
__decorate([
    (0, common_1.Get)('stats'),
    (0, roles_decorator_1.Roles)('administrator', 'evaluator'),
    (0, swagger_1.ApiOperation)({ summary: 'Get task statistics' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Task statistics retrieved successfully' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], TasksController.prototype, "getStats", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, roles_decorator_1.Roles)('administrator', 'evaluator'),
    (0, swagger_1.ApiOperation)({ summary: 'Get task by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Task retrieved successfully' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], TasksController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, roles_decorator_1.Roles)('administrator', 'evaluator'),
    (0, swagger_1.ApiOperation)({ summary: 'Update task' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Task updated successfully' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.TASK_MANAGEMENT,
        resourceType: 'Task',
        description: 'Update task',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_task_dto_1.UpdateTaskDto]),
    __metadata("design:returntype", void 0)
], TasksController.prototype, "update", null);
__decorate([
    (0, common_1.Put)(':id/assign'),
    (0, roles_decorator_1.Roles)('administrator', 'evaluator'),
    (0, swagger_1.ApiOperation)({ summary: 'Assign task to user' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Task assigned successfully' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.TASK_MANAGEMENT,
        resourceType: 'Task',
        description: 'Assign task to user',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, assign_task_dto_1.AssignTaskDto, Object]),
    __metadata("design:returntype", void 0)
], TasksController.prototype, "assign", null);
__decorate([
    (0, common_1.Put)(':id/reassign'),
    (0, roles_decorator_1.Roles)('administrator', 'evaluator'),
    (0, swagger_1.ApiOperation)({ summary: 'Reassign task to another user' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Task reassigned successfully' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.TASK_MANAGEMENT,
        resourceType: 'Task',
        description: 'Reassign task to another user',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, assign_task_dto_1.AssignTaskDto, Object]),
    __metadata("design:returntype", void 0)
], TasksController.prototype, "reassign", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)('administrator'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete task' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Task deleted successfully' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.DELETE,
        module: audit_trail_entity_1.AuditModule.TASK_MANAGEMENT,
        resourceType: 'Task',
        description: 'Delete task',
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], TasksController.prototype, "remove", null);
exports.TasksController = TasksController = __decorate([
    (0, swagger_1.ApiTags)('Tasks'),
    (0, common_1.Controller)('tasks'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [tasks_service_1.TasksService])
], TasksController);
//# sourceMappingURL=tasks.controller.js.map