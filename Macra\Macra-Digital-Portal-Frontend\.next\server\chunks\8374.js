"use strict";exports.id=8374,exports.ids=[8374],exports.modules={5618:(e,t,a)=>{a.d(t,{v:()=>n});var i=a(12234),s=a(15885);let n={async getLicenseTypes(e={}){let t=new URLSearchParams;return e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(([e,a])=>{Array.isArray(a)?a.forEach(a=>t.append(`filter.${e}`,a)):t.set(`filter.${e}`,a)}),(await i.uE.get(`/license-types?${t.toString()}`)).data},getLicenseType:async e=>(await i.uE.get(`/license-types/${e}`)).data,createLicenseType:async e=>(await i.uE.post("/license-types",e)).data,updateLicenseType:async(e,t)=>(await i.uE.put(`/license-types/${e}`,t)).data,deleteLicenseType:async e=>(await i.uE.delete(`/license-types/${e}`)).data,async getAllLicenseTypes(){return s.qI.getOrSet(s._l.LICENSE_TYPES,async()=>(await this.getLicenseTypes({limit:100})).data,s.U_.LONG)}}},15885:(e,t,a)=>{a.d(t,{U_:()=>r,_l:()=>n,qI:()=>s});class i{set(e,t,a=this.defaultTTL){let i=Date.now();this.cache.set(e,{data:t,timestamp:i,expiresAt:i+a})}get(e){let t=this.cache.get(e);return t?Date.now()>t.expiresAt?(this.cache.delete(e),null):t.data:null}has(e){return null!==this.get(e)}delete(e){return this.cache.delete(e)}clear(){this.cache.clear()}getStats(){return{size:this.cache.size,keys:Array.from(this.cache.keys())}}cleanup(){let e=Date.now(),t=0;for(let[t,a]of this.cache.entries())e>a.expiresAt&&this.cache.delete(t)}async getOrSet(e,t,a=this.defaultTTL){let i=this.get(e);if(null!==i)return i;let s=await t();return this.set(e,s,a),s}invalidatePattern(e){let t=new RegExp(e),a=0;for(let e of this.cache.keys())t.test(e)&&this.cache.delete(e)}constructor(){this.cache=new Map,this.defaultTTL=3e5}}let s=new i,n={LICENSE_TYPES:"license-types",LICENSE_CATEGORIES:"license-categories",LICENSE_CATEGORIES_BY_TYPE:e=>`license-categories-type-${e}`,USER_APPLICATIONS:"user-applications",APPLICATION:e=>`application-${e}`},r={SHORT:12e4,MEDIUM:3e5,LONG:9e5,VERY_LONG:36e5};setInterval(()=>{s.cleanup()},3e5)},25011:(e,t,a)=>{a.d(t,{B5:()=>f,PY:()=>m,QE:()=>c,WC:()=>T,Yk:()=>y,kR:()=>h,lW:()=>g,nF:()=>u,zH:()=>p});let i={applicantInfo:{id:"applicant-info",name:"Applicant Information",component:"ApplicantInfo",route:"applicant-info",required:!0,description:"Personal or company information of the applicant",estimatedTime:"5"},addressInfo:{id:"address-info",name:"Address Information",component:"AddressInfo",route:"address-info",required:!0,description:"Physical and postal address details",estimatedTime:"3"},contactInfo:{id:"contact-info",name:"Contact Information",component:"ContactInfo",route:"contact-info",required:!0,description:"Contact details and communication preferences",estimatedTime:"5"},management:{id:"management",name:"Management Structure",component:"Management",route:"management",required:!1,description:"Management team and organizational structure",estimatedTime:"8"},professionalServices:{id:"professional-services",name:"Professional Services",component:"ProfessionalServices",route:"professional-services",required:!1,description:"External consultants and service providers",estimatedTime:"6"},serviceScope:{id:"service-scope",name:"Service Scope",component:"ServiceScope",route:"service-scope",required:!0,description:"Services offered and geographic coverage",estimatedTime:"8"},legalHistory:{id:"legal-history",name:"Legal History",component:"LegalHistory",route:"legal-history",required:!0,description:"Legal compliance and regulatory history",estimatedTime:"5"},documents:{id:"documents",name:"Required Documents",component:"Documents",route:"documents",required:!0,description:"Upload required documents for license application",estimatedTime:"10"},submit:{id:"submit",name:"Submit Application",component:"Submit",route:"submit",required:!0,description:"Review and submit your application",estimatedTime:"5"}},s={telecommunications:{licenseTypeId:"telecommunications",name:"Telecommunications License",description:"License for telecommunications service providers including ISPs, mobile operators, and fixed-line services",steps:[i.applicantInfo,i.addressInfo,i.contactInfo,i.management,i.serviceScope,i.legalHistory,i.documents,i.submit],estimatedTotalTime:"97 minutes",requirements:["Business registration certificate","Tax compliance certificate","Technical specifications","Financial statements","Management CVs","Network coverage plans"]},postal_services:{licenseTypeId:"postal_services",name:"Postal Services License",description:"License for postal and courier service providers",steps:[i.applicantInfo,i.addressInfo,i.contactInfo,i.legalHistory,i.documents,i.submit],estimatedTotalTime:"65 minutes",requirements:["Business registration certificate","Fleet inventory","Service coverage map","Insurance certificates","Premises documentation"]},standards_compliance:{licenseTypeId:"standards_compliance",name:"Standards Compliance License",description:"License for standards compliance and certification services",steps:[i.applicantInfo,i.addressInfo,i.contactInfo,i.management,i.professionalServices,i.serviceScope,i.legalHistory,i.documents,i.submit],estimatedTotalTime:"82 minutes",requirements:["Accreditation certificates","Technical competency proof","Quality management system","Laboratory facilities documentation","Staff qualifications"]},broadcasting:{licenseTypeId:"broadcasting",name:"Broadcasting License",description:"License for radio and television broadcasting services",steps:[i.applicantInfo,i.addressInfo,i.contactInfo,i.management,i.serviceScope,i.professionalServices,i.legalHistory,i.documents,i.submit],estimatedTotalTime:"86 minutes",requirements:["Broadcasting equipment specifications","Content programming plan","Studio facility documentation","Transmission coverage maps","Local content compliance plan"]},spectrum_management:{licenseTypeId:"spectrum_management",name:"Spectrum Management License",description:"License for radio frequency spectrum management and allocation",steps:[i.applicantInfo,i.management,i.serviceScope,i.professionalServices,i.legalHistory,i.documents,i.submit],estimatedTotalTime:"89 minutes",requirements:["Spectrum usage plan","Technical interference analysis","Equipment type approval","Frequency coordination agreements","Monitoring capabilities documentation"]},clf:{licenseTypeId:"clf",name:"CLF License",description:"Consumer Lending and Finance license",steps:[i.applicantInfo,i.addressInfo,i.contactInfo,i.management,i.professionalServices,i.legalHistory,i.documents,i.submit],estimatedTotalTime:"51 minutes",requirements:["Financial institution license","Capital adequacy documentation","Risk management framework","Consumer protection policies","Anti-money laundering procedures"]}},n={telecommunications:"telecommunications","postal services":"postal_services",postal_services:"postal_services","standards compliance":"standards_compliance",standards_compliance:"standards_compliance",broadcasting:"broadcasting","spectrum management":"spectrum_management",spectrum_management:"spectrum_management",clf:"clf","consumer lending and finance":"clf"},r={licenseTypeId:"default",name:"Standard License Application",description:"Standard license application process with all required steps",steps:[i.applicantInfo,i.addressInfo,i.contactInfo,i.management,i.professionalServices,i.serviceScope,i.legalHistory,i.documents,i.submit],estimatedTotalTime:"120 minutes",requirements:["Business registration certificate","Tax compliance certificate","Financial statements","Management CVs","Professional qualifications","Service documentation"]},c=e=>{if(!e||"string"!=typeof e)return r;let t=s[e];if(t)return t;let a=e.toLowerCase().replace(/[^a-z0-9]/g,"_");if(t=s[a])return t;let i=n[a];if(i)return s[i];if(o(e)){let t=d(e);if(t){let e=s[t];if(e)return e}}let c=Object.keys(s).find(t=>e.toLowerCase().includes(t)||t.includes(e.toLowerCase()));return c?s[c]:r},o=e=>/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e),l={},p=e=>{l=e},d=e=>l[e]||null,m=e=>{if(["telecommunications","postal_services","standards_compliance","broadcasting","spectrum_management"].includes(e)){let t=s[e];if(t)return t.steps}return r.steps},u=e=>["telecommunications","postal_services","standards_compliance","broadcasting","spectrum_management"].includes(e),g=(e,t)=>{let a=c(e);return a&&a.steps.find(e=>e.route===t)||null},f=(e,t)=>c(e).steps.findIndex(e=>e.route===t),y=e=>c(e).steps.length,h=(e,t)=>{let a=c(e),i=f(e,t);return -1===i||i>=a.steps.length-1?null:a.steps[i+1]},T=(e,t)=>{let a=c(e),i=f(e,t);return i<=0?null:a.steps[i-1]}},34130:(e,t,a)=>{a.d(t,{TG:()=>c});var i=a(12234),s=a(15885);let n=e=>e.toLowerCase().replace(/[^a-z0-9\s]/g,"").replace(/\s+/g,"-").replace(/^-+|-+$/g,"").substring(0,50),r=e=>e.map(e=>({...e,code:n(e.name),children:e.children?r(e.children):void 0})),c={async getLicenseCategories(e={}){let t=new URLSearchParams;return e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(([e,a])=>{Array.isArray(a)?a.forEach(a=>t.append(`filter.${e}`,a)):t.set(`filter.${e}`,a)}),(await i.uE.get(`/license-categories?${t.toString()}`)).data},async getLicenseCategory(e){try{return(await i.uE.get(`/license-categories/${e}`,{timeout:3e4})).data}catch(e){if("ECONNABORTED"===e.code)throw Error("Request timeout - please try again");throw e}},async getLicenseCategoriesByType(e){try{return(await i.uE.get(`/license-categories/by-license-type/${e}`,{timeout:3e4})).data}catch(e){if("ECONNABORTED"===e.code)throw Error("Request timeout - please try again");if(e.response?.status===429)throw Error("Too many requests - please wait a moment and try again");throw e}},createLicenseCategory:async e=>(await i.uE.post("/license-categories",e)).data,updateLicenseCategory:async(e,t)=>(await i.uE.put(`/license-categories/${e}`,t)).data,deleteLicenseCategory:async e=>(await i.uE.delete(`/license-categories/${e}`)).data,async getAllLicenseCategories(){return s.qI.getOrSet(s._l.LICENSE_CATEGORIES,async()=>r((await this.getLicenseCategories({limit:100})).data),s.U_.LONG)},getCategoryTree:async e=>s.qI.getOrSet(`category-tree-${e}`,async()=>r((await i.uE.get(`/license-categories/license-type/${e}/tree`)).data),s.U_.MEDIUM),getRootCategories:async e=>s.qI.getOrSet(`root-categories-${e}`,async()=>(await i.uE.get(`/license-categories/license-type/${e}/root`)).data,s.U_.MEDIUM),async getCategoriesForParentSelection(e,t){try{try{let a=await i.uE.get(`/license-categories/license-type/${e}/for-parent-selection`,{params:t?{excludeId:t}:{}});if(a.data&&Array.isArray(a.data.data))return a.data.data;return[]}catch(s){let a=await i.uE.get(`/license-categories/by-license-type/${e}`);if(!(a.data&&Array.isArray(a.data)))return[];{let e=a.data;return t&&(e=e.filter(e=>e.license_category_id!==t)),e}}}catch(e){return[]}},getPotentialParents:async(e,t)=>(await i.uE.get(`/license-categories/license-type/${e}/potential-parents`,{params:t?{excludeId:t}:{}})).data}},98374:(e,t,a)=>{a.d(t,{UX:()=>c,r2:()=>l,tj:()=>o});var i=a(43210),s=a(5618),n=a(34130),r=a(25011);let c=()=>{let[e,t]=(0,i.useState)([]),[a,n]=(0,i.useState)(!0),[c,o]=(0,i.useState)(null),l=async()=>{n(!0),o(null);try{let e=await s.v.getAllLicenseTypes();t(e);let a={};e.forEach(e=>{e.code&&(a[e.license_type_id]=e.code)}),(0,r.zH)(a)}catch(a){let e="Failed to fetch license types";if(a.response?.status===429){e="Too many requests. Please wait a moment and try again.";try{let e=await s.v.getAllLicenseTypes();if(e&&e.length>0){t(e),o(null);return}}catch(e){}}else e=a.response?.data?.message||e;o(e)}finally{n(!1)}};return(0,i.useEffect)(()=>{l()},[]),{licenseTypes:e,loading:a,error:c,refetch:l}},o=()=>{let[e,t]=(0,i.useState)([]),[a,s]=(0,i.useState)(!0),[r,c]=(0,i.useState)(null),o=async()=>{s(!0),c(null);try{let e=await n.TG.getAllLicenseCategories();t(e)}catch(a){let e="Failed to fetch license categories";if(a.response?.status===429){e="Too many requests. Please wait a moment and try again.";try{let e=await n.TG.getAllLicenseCategories();if(e&&e.length>0){t(e),c(null);return}}catch(e){}}else e=a.response?.data?.message||e;c(e)}finally{s(!1)}},l=(0,i.useCallback)(t=>e.filter(e=>e.license_type_id===t),[e]);return(0,i.useEffect)(()=>{o()},[]),{categories:e,loading:a,error:r,refetch:o,getCategoriesByType:l}},l=()=>{let e=c(),t=o(),a=e.loading||t.loading,i=e.error||t.error;return{licenseTypes:e.licenseTypes,categories:t.categories,loading:a,error:i,refetch:()=>{e.refetch(),t.refetch()},getLicenseTypeWithCategories:a=>({licenseType:e.licenseTypes.find(e=>e.license_type_id===a),categories:t.getCategoriesByType(a)}),getCategoriesByType:t.getCategoriesByType}}}};