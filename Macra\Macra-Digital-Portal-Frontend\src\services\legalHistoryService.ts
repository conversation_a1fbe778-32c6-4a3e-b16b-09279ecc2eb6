import { apiClient } from '../lib/apiClient';

export interface LegalHistoryData {
  legal_history_id?: string;
  applicant_id?: string;
  criminal_history: boolean;
  criminal_details?: string;
  bankruptcy_history: boolean;
  bankruptcy_details?: string;
  regulatory_actions: boolean;
  regulatory_details?: string;
  litigation_history: boolean;
  litigation_details?: string;
  compliance_record?: string;
  previous_licenses?: string;
  declaration_accepted: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface CreateLegalHistoryData {
  applicant_id: string;
  criminal_history: boolean;
  criminal_details?: string;
  bankruptcy_history: boolean;
  bankruptcy_details?: string;
  regulatory_actions: boolean;
  regulatory_details?: string;
  litigation_history: boolean;
  litigation_details?: string;
  compliance_record?: string;
  previous_licenses?: string;
  declaration_accepted: boolean;
}

export interface UpdateLegalHistoryData {
  legal_history_id: string;
  criminal_history?: boolean;
  criminal_details?: string;
  bankruptcy_history?: boolean;
  bankruptcy_details?: string;
  regulatory_actions?: boolean;
  regulatory_details?: string;
  litigation_history?: boolean;
  litigation_details?: string;
  compliance_record?: string;
  previous_licenses?: string;
  declaration_accepted?: boolean;
}

export const legalHistoryService = {
  // Create new legal history
  async createLegalHistory(data: CreateLegalHistoryData): Promise<LegalHistoryData> {
    try {
      console.log('Creating legal history with data:', data);

      // Try to use real API endpoint first
      try {
        const response = await apiClient.post('/legal-history', data, {
          timeout: 30000, // 30 second timeout
        });
        return response.data;
      } catch (apiError: any) {
        console.warn('Legal history API endpoint not available, using fallback:', apiError.message);

        // Fallback to mock data if API is not available
        return {
          legal_history_id: `mock-${Date.now()}`,
          ...data,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
      }
    } catch (error) {
      console.error('LegalHistoryService.createLegalHistory error:', error);
      throw error;
    }
  },

  // Get legal history by ID
  async getLegalHistory(id: string): Promise<LegalHistoryData> {
    try {
      // TODO: Backend legal history endpoints not available yet
      console.warn('Legal history endpoints not implemented yet - returning empty data');
      throw new Error('Legal history not found');
      // const response = await apiClient.get(`/legal-history/${id}`);
      // return processApiResponse(response);
    } catch (error) {
      console.error('LegalHistoryService.getLegalHistory error:', error);
      throw error;
    }
  },

  // Get legal history by applicant ID
  async getLegalHistoryByApplicant(applicantId: string): Promise<LegalHistoryData | null> {
    try {
      // TODO: Backend legal history endpoints not available yet
      console.warn('Legal history endpoints not implemented yet - returning null');
      return null;
      // const response = await apiClient.get(`/legal-history/applicant/${applicantId}`);
      // return processApiResponse(response);
    } catch (error) {
      console.error('LegalHistoryService.getLegalHistoryByApplicant error:', error);
      return null; // Return null instead of throwing
    }
  },

  // Update legal history
  async updateLegalHistory(data: UpdateLegalHistoryData): Promise<LegalHistoryData> {
    try {
      console.log('Updating legal history:', data.legal_history_id, data);

      // Try to use real API endpoint first
      try {
        const response = await apiClient.put(`/legal-history/${data.legal_history_id}`, data, {
          timeout: 30000, // 30 second timeout
        });
        return response.data;
      } catch (apiError: any) {
        console.warn('Legal history API endpoint not available, using fallback:', apiError.message);

        // Fallback to mock data if API is not available
        return {
          legal_history_id: data.legal_history_id,
          applicant_id: '',
          criminal_history: data.criminal_history || false,
          criminal_details: data.criminal_details || '',
          bankruptcy_history: data.bankruptcy_history || false,
          bankruptcy_details: data.bankruptcy_details || '',
          regulatory_actions: data.regulatory_actions || false,
          regulatory_details: data.regulatory_details || '',
          litigation_history: data.litigation_history || false,
          litigation_details: data.litigation_details || '',
          compliance_record: data.compliance_record || '',
          previous_licenses: data.previous_licenses || '',
          declaration_accepted: data.declaration_accepted || false,
          updated_at: new Date().toISOString()
        };
      // const response = await apiClient.put(`/legal-history/${data.legal_history_id}`, data);
      // return processApiResponse(response);
    } catch (error) {
      console.error('LegalHistoryService.updateLegalHistory error:', error);
      throw error;
    }
  },

  // Create or update legal history for an applicant
  async createOrUpdateLegalHistory(applicantId: string, data: Omit<CreateLegalHistoryData, 'applicant_id'>): Promise<LegalHistoryData> {
    try {
      // Check if legal history already exists for this applicant
      const existing = await this.getLegalHistoryByApplicant(applicantId);
      
      if (existing) {
        // Update existing legal history
        return await this.updateLegalHistory({
          legal_history_id: existing.legal_history_id!,
          ...data
        });
      } else {
        // Create new legal history
        return await this.createLegalHistory({
          applicant_id: applicantId,
          ...data
        });
      }
    } catch (error) {
      console.error('LegalHistoryService.createOrUpdateLegalHistory error:', error);
      throw error;
    }
  }
};
