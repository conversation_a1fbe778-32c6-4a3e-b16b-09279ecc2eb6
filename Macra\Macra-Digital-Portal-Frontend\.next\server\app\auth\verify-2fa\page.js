(()=>{var e={};e.id=6961,e.ids=[6961],e.modules={2005:(e,r,t)=>{Promise.resolve().then(t.bind(t,43191))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},15573:(e,r,t)=>{Promise.resolve().then(t.bind(t,60781))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24829:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d});var s=t(65239),a=t(48088),i=t(88170),n=t.n(i),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d={children:["",{children:["auth",{children:["verify-2fa",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,43191)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\auth\\verify-2fa\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\auth\\verify-2fa\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/verify-2fa/page",pathname:"/auth/verify-2fa",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},43191:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\verify-2fa\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\auth\\verify-2fa\\page.tsx","default")},55192:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var s=t(43210);let a=s.forwardRef(function({title:e,titleId:r,...t},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},t),e?s.createElement("title",{id:r},e):null,s.createElement("path",{fillRule:"evenodd",d:"M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm-1.72 6.97a.75.75 0 1 0-1.06 1.06L10.94 12l-1.72 1.72a.75.75 0 1 0 1.06 1.06L12 13.06l1.72 1.72a.75.75 0 1 0 1.06-1.06L13.06 12l1.72-1.72a.75.75 0 1 0-1.06-1.06L12 10.94l-1.72-1.72Z",clipRule:"evenodd"}))})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},60781:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var s=t(60687),a=t(43210),i=t(30474),n=t(16189);t(63443);var o=t(55192);t(15659);var l=t(71773);function d(){(0,n.useRouter)();let[e,r]=(0,a.useState)(""),[t,d]=(0,a.useState)(""),[c,u]=(0,a.useState)(!0),x=(0,n.useSearchParams)();if(x.get("i"),x.get("unique"),x.get("c"),c)return(0,s.jsx)("div",{className:"min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900",children:(0,s.jsx)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:(0,s.jsx)(l.A,{message:"Verifying OTP..."})})});let p=t.toLowerCase().includes("enabled");return(0,s.jsxs)("div",{className:"min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900",children:[(0,s.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md text-center",children:[(0,s.jsx)(i.default,{src:"/images/macra-logo.png",alt:"MACRA Logo",width:50,height:50,className:"mx-auto h-16 w-auto"}),(0,s.jsx)("h2",{className:"mt-6 text-3xl font-extrabold text-gray-900 dark:text-white",children:t?(0,s.jsx)("span",{className:"text-green-600 dark:text-green-300",children:"Account Verification Success!"}):e?(0,s.jsx)("span",{className:"text-red-800 dark:text-red-300",children:"Error"}):(0,s.jsx)("span",{className:"text-gray-600 dark:text-gray-300",children:"Account Verification"})})]}),(0,s.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 py-8 px-6 shadow rounded-lg sm:px-10",children:[e&&!p&&(0,s.jsxs)("div",{className:"flex flex-col flex-auto items-center justify-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 mb-4 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center shadow-md",children:(0,s.jsx)(o.A,{className:"w-10 h-10 animate-pulse text-red-600 dark:text-red-300"})}),(0,s.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-md flex items-center",children:e})]}),(t||p)&&(0,s.jsxs)("div",{className:"flex flex-col flex-auto items-center justify-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 mb-4 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center animate-bounce shadow-md",children:(0,s.jsx)("svg",{className:"w-8 h-8 text-green-600 dark:text-green-300",fill:"none",stroke:"currentColor",strokeWidth:"3",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5 13l4 4L19 7"})})}),(0,s.jsxs)("div",{className:"text-center text-gray-600 dark:text-gray-400",children:[t," ",(0,s.jsx)("br",{})]})]})]})})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"256x256",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,7498,1658,2335],()=>t(24829));module.exports=s})();