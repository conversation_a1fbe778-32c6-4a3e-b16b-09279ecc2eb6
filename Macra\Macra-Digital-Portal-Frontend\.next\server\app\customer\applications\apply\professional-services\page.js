(()=>{var e={};e.id=9593,e.ids=[9593],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11982:(e,t,r)=>{"use strict";r.d(t,{l:()=>o});var s=r(25011),a=r(50996);class i{async validateStepNavigation(e,t,r){let i=(0,s.QE)(t);if(!i)return{canNavigateToStep:!1,reason:"Invalid license type",requiredSteps:[]};let o=(0,s.B5)(t,r);if(-1===o)return{canNavigateToStep:!1,reason:"Invalid step",requiredSteps:[]};let l=await a.d.getCompletedStepIds(e),n=i.steps.slice(0,o).filter(e=>e.required).map(e=>e.id).filter(e=>!l.includes(e));return n.length>0?{canNavigateToStep:!1,reason:"Required previous steps must be completed first",requiredSteps:n}:{canNavigateToStep:!0,requiredSteps:[]}}async validateNextStepNavigation(e,t,r){let i=(0,s.QE)(t);if(!i)return{canNavigateToStep:!1,reason:"Invalid license type",requiredSteps:[]};let o=(0,s.B5)(t,r);return -1===o?{canNavigateToStep:!1,reason:"Invalid current step",requiredSteps:[]}:i.steps[o].required&&!await a.d.isStepCompleted(e,r)?{canNavigateToStep:!1,reason:"Current step must be completed before proceeding",requiredSteps:[r]}:o>=i.steps.length-1?{canNavigateToStep:!1,reason:"Already at the last step",requiredSteps:[]}:{canNavigateToStep:!0,requiredSteps:[]}}async validatePreviousStepNavigation(e,t,r){return 0>=(0,s.B5)(t,r)?{canNavigateToStep:!1,reason:"Already at the first step",requiredSteps:[]}:{canNavigateToStep:!0,requiredSteps:[]}}async getNextAvailableStep(e,t){let r=(0,s.QE)(t);if(!r)return null;let i=await a.d.getCompletedStepIds(e);for(let e of r.steps)if(e.required&&!i.includes(e.id))return e.id;for(let e of r.steps)if(!e.required&&!i.includes(e.id))return e.id;return null}async validateApplicationCompletion(e,t){let r=(0,s.QE)(t);if(!r)return{isValid:!1,errors:["Invalid license type"],warnings:[]};let i=await a.d.getCompletedStepIds(e),o=r.steps.filter(e=>e.required).filter(e=>!i.includes(e.id)),l=[],n=[];o.length>0&&l.push(`Missing required steps: ${o.map(e=>e.name).join(", ")}`);let d=r.steps.filter(e=>!e.required).filter(e=>!i.includes(e.id));return d.length>0&&n.push(`Optional steps not completed: ${d.map(e=>e.name).join(", ")}`),{isValid:0===l.length,errors:l,warnings:n}}async getStepRequirements(e,t){let r=(0,s.QE)(e);if(!r)return null;let a=r.steps.find(e=>e.id===t);if(!a)return null;let i=(0,s.B5)(e,t),o=r.steps.slice(0,i).filter(e=>e.required).map(e=>e.id);return{isRequired:a.required,dependencies:o,description:a.description,estimatedTime:a.estimatedTime}}async isReadyForSubmission(e,t){return(await this.validateApplicationCompletion(e,t)).isValid}}let o=new i},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21345:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>p,routeModule:()=>u,tree:()=>d});var s=r(65239),a=r(48088),i=r(88170),o=r.n(i),l=r(30893),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);r.d(t,n);let d={children:["",{children:["customer",{children:["applications",{children:["apply",{children:["professional-services",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,51119)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\professional-services\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,68736)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,p=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\professional-services\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/customer/applications/apply/professional-services/page",pathname:"/customer/applications/apply/professional-services",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},49773:(e,t,r)=>{Promise.resolve().then(r.bind(r,55793))},50996:(e,t,r)=>{"use strict";r.d(t,{d:()=>i});var s=r(25011);class a{async initializeProgress(e,t){let r=(0,s.QE)(t);if(!r)throw Error(`Invalid license type: ${t}`);let a=r.steps.map(e=>({stepId:e.id,stepName:e.name,completed:!1})),i={applicationId:e,licenseTypeId:t,totalSteps:a.length,completedSteps:0,progressPercentage:0,steps:a,lastUpdated:new Date};return this.progressCache.set(e,i),await this.saveProgressToStorage(i),i}async markStepCompleted(e,t,r){let s=await this.getProgress(e);if(!s)throw Error(`No progress found for application: ${e}`);let a=s.steps.findIndex(e=>e.stepId===t);if(-1===a)throw Error(`Step not found: ${t}`);return s.steps[a].completed||(s.steps[a].completed=!0,s.steps[a].completedAt=new Date,s.steps[a].data=r,s.completedSteps=s.steps.filter(e=>e.completed).length,s.progressPercentage=Math.round(s.completedSteps/s.totalSteps*100),s.lastUpdated=new Date,this.progressCache.set(e,s),await this.saveProgressToStorage(s)),s}async markStepIncomplete(e,t){let r=await this.getProgress(e);if(!r)throw Error(`No progress found for application: ${e}`);let s=r.steps.findIndex(e=>e.stepId===t);if(-1===s)throw Error(`Step not found: ${t}`);return r.steps[s].completed&&(r.steps[s].completed=!1,r.steps[s].completedAt=void 0,r.steps[s].data=void 0,r.completedSteps=r.steps.filter(e=>e.completed).length,r.progressPercentage=Math.round(r.completedSteps/r.totalSteps*100),r.lastUpdated=new Date,this.progressCache.set(e,r),await this.saveProgressToStorage(r)),r}async getProgress(e){if(this.progressCache.has(e))return this.progressCache.get(e);let t=await this.loadProgressFromStorage(e);return t&&this.progressCache.set(e,t),t}async getCompletedStepIds(e){let t=await this.getProgress(e);return t?t.steps.filter(e=>e.completed).map(e=>e.stepId):[]}async isStepCompleted(e,t){let r=await this.getProgress(e);if(!r)return!1;let s=r.steps.find(e=>e.stepId===t);return s?.completed||!1}async getNextIncompleteStep(e){let t=await this.getProgress(e);return t&&t.steps.find(e=>!e.completed)||null}async getApplicationStatus(e){let t=await this.getProgress(e);return t&&0!==t.completedSteps?t.completedSteps===t.totalSteps?"completed":"in_progress":"not_started"}async saveProgressToStorage(e){try{let t=`application_progress_${e.applicationId}`;localStorage.setItem(t,JSON.stringify({...e,lastUpdated:e.lastUpdated.toISOString()}))}catch(e){}}async loadProgressFromStorage(e){try{let t=`application_progress_${e}`,r=localStorage.getItem(t);if(!r)return null;let s=JSON.parse(r);return{...s,lastUpdated:new Date(s.lastUpdated),steps:s.steps.map(e=>({...e,completedAt:e.completedAt?new Date(e.completedAt):void 0}))}}catch(e){return null}}clearCache(){this.progressCache.clear()}async deleteProgress(e){this.progressCache.delete(e);try{let t=`application_progress_${e}`;localStorage.removeItem(t)}catch(e){}}constructor(){this.progressCache=new Map}}let i=new a},51119:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\applications\\\\apply\\\\professional-services\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\professional-services\\page.tsx","default")},51981:(e,t,r)=>{Promise.resolve().then(r.bind(r,51119))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},55793:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(60687),a=r(43210),i=r(16189),o=r(94391),l=r(63213),n=r(98374);!function(){var e=Error("Cannot find module '@/components/customer/application/steps/ProfessionalServices'");throw e.code="MODULE_NOT_FOUND",e}();var d=r(25011),p=r(50996),c=r(11982);let u=()=>{let e=(0,i.useRouter)(),t=(0,i.useParams)(),r=(0,i.useSearchParams)(),{isAuthenticated:u,loading:m}=(0,l.A)(),{loading:g,licenseCategories:x}=(0,n.r2)(),h=t.categoryId,f="professional-services",v=r.get("app"),[y,b]=(0,a.useState)(!0),[S,w]=(0,a.useState)(null),[N,j]=(0,a.useState)([]),[P,k]=(0,a.useState)(0),[q,C]=(0,a.useState)(!1),[_,I]=(0,a.useState)(!1),[E,$]=(0,a.useState)(null),[T,A]=(0,a.useState)(""),[M,D]=(0,a.useState)({}),[F,O]=(0,a.useState)({}),[U,R]=(0,a.useState)(null),[G,L]=(0,a.useState)(null),[Q,B]=(0,a.useState)(0),[z,V]=(0,a.useState)(0),[J,W]=(0,a.useState)(null),[K,X]=(0,a.useState)(null);(0,a.useEffect)(()=>{if(!m&&!u)return void e.push("/customer/auth/login")},[u,m,e]),(0,a.useEffect)(()=>{let e=async()=>{try{let e=x.find(e=>e.id===h);if(e&&e.license_type_id)A(e.license_type_id);else{let e=await fetch(`/api/license-categories/${h}`);if(!e.ok)throw Error("Failed to fetch license category");let t=await e.json();if(t.license_type_id)A(t.license_type_id);else throw Error("License category does not have a license type ID")}}catch(e){w("Failed to load license category information")}};h&&!g&&e()},[h,g,x]),(0,a.useEffect)(()=>{T&&(R((0,d.QE)(T)),L((0,d.lW)(T,f)),B((0,d.B5)(T,f)),V((0,d.Yk)(T)),W((0,d.kR)(T,f)),X((0,d.WC)(T,f)))},[T,f]),(0,a.useEffect)(()=>{(async()=>{if(!g&&T){if(!U)return w(`Invalid license type: ${T}. Please check the license type configuration.`);if(!G)return w(`Invalid step: ${f} for license type ${T}`);if(!v)return e.replace(`/customer/applications/apply/${h}/applicant-info`);try{let t=await p.d.getProgress(v);if(t||(t=await p.d.initializeProgress(v,T)),t){let r=await p.d.getCompletedStepIds(v);j(r),k(t.progressPercentage);let s=await c.l.validateNextStepNavigation(v,T,G.id);C(s.canNavigateToStep);let a=await c.l.validatePreviousStepNavigation(v,T,G.id);I(a.canNavigateToStep);let i=await c.l.validateStepNavigation(v,T,G.id);if(!i.canNavigateToStep){$(i.reason||"Cannot access this step");let t=await c.l.getNextAvailableStep(v,T);if(t)return void e.replace(`/customer/applications/apply/${h}/${t}?app=${v}`)}}}catch(e){k(0),j([]),C(!1)}await Y(),b(!1)}})()},[U,G,f,T,h,v,e,g]);let Y=async()=>{},H=async e=>{try{if(v)return D(e),await Z(G?.id||f,e),v;throw Error("No application ID available")}catch(e){throw e}},Z=async(e,t)=>{try{if(v){let r=await p.d.markStepCompleted(v,e,t);if(j(r.steps.filter(e=>e.completed).map(e=>e.stepId)),k(r.progressPercentage),G){let e=await c.l.validateNextStepNavigation(v,T,G.id);C(e.canNavigateToStep)}}}catch(e){}},ee=t=>{if("next"===t&&J&&q){let t="new"!==v?`?app=${v}`:"";e.push(`/customer/applications/apply/${h}/${J.route}${t}`)}else if("previous"===t&&K&&_){let t="new"!==v?`?app=${v}`:"";e.push(`/customer/applications/apply/${h}/${K.route}${t}`)}};return m||y||!T?(0,s.jsx)(o.A,{children:(0,s.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading application step..."})]})})}):S?(0,s.jsx)(o.A,{children:(0,s.jsx)("div",{className:"max-w-4xl mx-auto p-6",children:(0,s.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("i",{className:"ri-error-warning-line text-red-600 dark:text-red-400 text-2xl mr-4"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-red-800 dark:text-red-200",children:"Error Loading Step"}),(0,s.jsx)("p",{className:"text-red-700 dark:text-red-300 mt-1",children:S}),(0,s.jsxs)("button",{onClick:()=>e.back(),className:"mt-4 inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40",children:[(0,s.jsx)("i",{className:"ri-arrow-left-line mr-2"}),"Go Back"]})]})]})})})}):(0,s.jsx)(o.A,{children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto p-6",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsxs)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2",children:[U?.name," License Application"]}),(0,s.jsxs)("p",{className:"text-gray-600 dark:text-gray-400",children:["Step ",Q+1," of ",z,": ",G?.name]}),(0,s.jsx)("div",{className:"mt-2 p-2 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg",children:(0,s.jsxs)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:[(0,s.jsx)("i",{className:"ri-edit-line mr-1"}),"Editing application: ",v]})})]}),(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsxs)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:["Step ",Q+1," of ",z]}),(0,s.jsxs)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:[P,"% Complete"]})]}),(0,s.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,s.jsx)("div",{className:"bg-primary h-2 rounded-full transition-all duration-300",style:{width:`${P}%`}})})]}),(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:U?.steps.map((e,t)=>{let r=N.includes(e.id),a=t===Q;return(0,s.jsxs)("div",{className:`px-3 py-1 rounded-full text-xs font-medium ${a?"bg-primary text-white":r?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300":"bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400"}`,children:[r&&!a&&(0,s.jsx)("i",{className:"ri-check-line mr-1"}),t+1,". ",e.name]},e.id)})})}),E&&(0,s.jsx)("div",{className:"mb-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("i",{className:"ri-warning-line text-yellow-600 dark:text-yellow-400 text-lg mr-3"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-yellow-800 dark:text-yellow-200",children:"Navigation Restriction"}),(0,s.jsx)("p",{className:"text-yellow-700 dark:text-yellow-300 text-sm mt-1",children:E})]})]})}),(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6",children:(0,s.jsx)(Object(function(){var e=Error("Cannot find module '@/components/customer/application/steps/ProfessionalServices'");throw e.code="MODULE_NOT_FOUND",e}()),{formData:M,onChange:(e,t)=>{D(r=>({...r,[e]:t})),F[e]&&O(t=>({...t,[e]:""}))},onSave:H,errors:F,applicationId:v,isLoading:y})}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsxs)("button",{onClick:()=>ee("previous"),disabled:!_||!K,className:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,s.jsx)("i",{className:"ri-arrow-left-line mr-2"}),"Previous"]}),(0,s.jsxs)("button",{onClick:()=>ee("next"),disabled:!q||!J,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed",children:["Next",(0,s.jsx)("i",{className:"ri-arrow-right-line ml-2"})]})]})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,7498,1658,5814,7563,6893,8374],()=>r(21345));module.exports=s})();