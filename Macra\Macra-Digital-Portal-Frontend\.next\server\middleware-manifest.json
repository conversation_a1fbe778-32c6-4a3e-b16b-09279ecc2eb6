{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|images).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|images).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "yRBhcstw3coeaFXDDH91s", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "7tMnmjS3vjHwtpSqQLSfq+Oz9s5f+qG1RJUKtBKj9pI=", "__NEXT_PREVIEW_MODE_ID": "c42843e34734a6b776de17a348947282", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "2359ae3f3fee8bce07be58171ac1bcff874d6c49c2ccb07b107210248ec0b60f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c8a745686994ab1eb5534c1acb689dfc5c3fddc0740153e5df084ca83a112864"}}}, "functions": {}, "sortedMiddleware": ["/"]}