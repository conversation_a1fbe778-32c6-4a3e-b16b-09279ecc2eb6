{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|images).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|images).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "7tMnmjS3vjHwtpSqQLSfq+Oz9s5f+qG1RJUKtBKj9pI=", "__NEXT_PREVIEW_MODE_ID": "cde8fd61a0274761be402ec783500529", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c19ca8091ac0d3b0672407e4430675de83ea95f9efc981896e998137fa1308e1", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "1b2c8d980afe71296eb8597147fe6301ac2e3837e08fb3c611ec188aadcf0f8e"}}}, "sortedMiddleware": ["/"], "functions": {}}