{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|images).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|images).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "3o4Wn21CpMtceV1XyYOui", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "7tMnmjS3vjHwtpSqQLSfq+Oz9s5f+qG1RJUKtBKj9pI=", "__NEXT_PREVIEW_MODE_ID": "5a31d5e741e337bb28b32c2cc6460d20", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "02ffd8bb07fc5b4ead4a12043e8f08eae45e2f1dfd5c5a343cfac5214532de0c", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c39221c3b807e3b24cf90199820e0f4a43ebb4df7660475910a34152eb3b43a3"}}}, "functions": {}, "sortedMiddleware": ["/"]}