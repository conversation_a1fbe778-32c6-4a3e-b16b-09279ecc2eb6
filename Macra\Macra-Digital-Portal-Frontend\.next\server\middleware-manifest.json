{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|images).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|images).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "7tMnmjS3vjHwtpSqQLSfq+Oz9s5f+qG1RJUKtBKj9pI=", "__NEXT_PREVIEW_MODE_ID": "29789fc85dbf829bb5724c82525cf641", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "910c1368960a88a345059899c43c48c9883dbbce0ae2206b456bcbceb10f3110", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "58654f0ed6f6a6920b20dfda1fd2e248965afbe36fb1149e73bb7437d7b52590"}}}, "sortedMiddleware": ["/"], "functions": {}}