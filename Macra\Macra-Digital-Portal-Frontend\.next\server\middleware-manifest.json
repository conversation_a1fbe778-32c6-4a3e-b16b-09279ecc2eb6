{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|images).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|images).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "7tMnmjS3vjHwtpSqQLSfq+Oz9s5f+qG1RJUKtBKj9pI=", "__NEXT_PREVIEW_MODE_ID": "27bbee8ecd5b165cba4debff3ed51623", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "97bda3c112ee8a29a7384fca6d293d0acf089c782372c008111ecbd5e9b2c87d", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "730ff2204385036bbc446587c21a11fe5fa680df269af89882fab2dc4d590493"}}}, "sortedMiddleware": ["/"], "functions": {}}