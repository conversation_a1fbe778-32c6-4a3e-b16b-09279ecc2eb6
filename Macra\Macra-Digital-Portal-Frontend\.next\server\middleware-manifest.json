{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|images).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|images).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "7tMnmjS3vjHwtpSqQLSfq+Oz9s5f+qG1RJUKtBKj9pI=", "__NEXT_PREVIEW_MODE_ID": "4796ee9bc9fb934ae38f276be3081aa0", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7c4dac647ce7d8cd9e0870939be0e1e1df216001f96d187dde7ecdf4edc5c6b8", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "621982daf2c5204f7674de70b0a35667a999e00a2e72559b7879ae8021773419"}}}, "sortedMiddleware": ["/"], "functions": {}}