export declare enum TaskType {
    APPLICATION = "application",
    COMPLAINT = "complaint",
    DATA_BREACH = "data_breach",
    EVALUATION = "evaluation",
    INSPECTION = "inspection"
}
export declare enum TaskPriority {
    LOW = "low",
    MEDIUM = "medium",
    HIGH = "high",
    URGENT = "urgent"
}
export declare enum TaskStatus {
    PENDING = "pending",
    IN_PROGRESS = "in_progress",
    COMPLETED = "completed",
    CANCELLED = "cancelled"
}
export declare class CreateTaskDto {
    task_type: TaskType;
    title: string;
    description: string;
    priority?: TaskPriority;
    status?: TaskStatus;
    due_date?: string;
    assigned_to?: string;
    metadata?: Record<string, any>;
}
