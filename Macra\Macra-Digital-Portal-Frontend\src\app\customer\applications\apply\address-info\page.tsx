'use client';

import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import ApplicationLayout from '@/components/applications/ApplicationLayout';
import { useAuth } from '@/contexts/AuthContext';
import { TextInput } from '@/components/forms';
import { FormMessages } from '@/components/forms/FormMessages';
import { useDynamicNavigation } from '@/hooks/useDynamicNavigation';
import { applicationService } from '@/services/applicationService';
import { applicantService } from '@/services/applicantService';
import { addressService } from '@/hooks/useAddressing';
import { validateSection } from '@/utils/formValidation';

export default function AddressPage() {
  const searchParams = useSearchParams();
  const { isAuthenticated, loading: authLoading } = useAuth();

  // URL parameters
  const licenseCategoryId = searchParams.get('license_category_id');
  const applicationId = searchParams.get('application_id');

  // State management
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Dynamic navigation hook
  const {
    handleNext: dynamicHandleNext,
    handlePrevious: dynamicHandlePrevious,
    nextStep
  } = useDynamicNavigation({
    currentStepRoute: 'address-info',
    licenseCategoryId,
    applicationId
  });

  // Form data state - Single address with multiple lines
  const [formData, setFormData] = useState({
    address_line_1: '',
    address_line_2: '',
    address_line_3: '',
    city: '',
    postal_code: '',
    country: 'Malawi'
  });

  // State for loading warnings
  const [loadingWarning, setLoadingWarning] = useState<string | null>(null);

  // State for existing address
  const [existingAddress, setExistingAddress] = useState<any>(null);

  // Load existing data
  useEffect(() => {
    const loadData = async () => {
      if (!applicationId || !isAuthenticated || authLoading) return;

      try {
        setIsLoading(true);
        setError(null);
        setLoadingWarning(null);


        // Load application and applicant data
        const application = await applicationService.getApplication(applicationId);
        if (application.applicant_id) {
          try {
            // const applicant = await applicantService.getApplicant(application.applicant_id);
            // Load existing addresses for this applicant using polymorphic relationship
            try {
              const addresses = await addressService.getAddressesByEntity('applicant', application.applicant_id);

              // Find business address (primary address for the application)
              const businessAddress = (addresses.data.length > 0) ? addresses.data[0]: null;
              if (businessAddress) {
                setExistingAddress(businessAddress);

                // Populate form with existing address data
                setFormData({
                  address_line_1: businessAddress.address_line_1 || '',
                  address_line_2: businessAddress.address_line_2 || '',
                  address_line_3: businessAddress.address_line_3 || '',
                  city: businessAddress.city || '',
                  postal_code: businessAddress.postal_code || '',
                  country: businessAddress.country || 'Malawi'
                });

              } else {
                setExistingAddress(null);
              }
            } catch (addressError: any) {
              setLoadingWarning('Could not load existing address data.');
              setExistingAddress(null);
            }

          } catch (applicantError: any) {
            if (applicantError.response?.status === 500) {
              setLoadingWarning('Unable to load existing applicant data due to a server issue. You can still edit addresses, but the form will start empty.');
            } else {
              setLoadingWarning('Could not load existing applicant data. The form will start empty.');
            }
          }
        } else {
          console.log('⚠️ Application exists but no applicant_id found');
        }

      } catch (err: any) {
        setError('Failed to load application data');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [applicationId, isAuthenticated, authLoading]);

  // Form handling
  const handleFormChange = (field: string, value: string | boolean) => {
    setFormData(prev => {
      const newData = { ...prev, [field]: value };
      return newData;
    });
    setHasUnsavedChanges(true);

    // Clear success message when user starts making changes
    if (successMessage) {
      setSuccessMessage(null);
    }

    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Handle input change events
  const handleInputChange = (field: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFormChange(field, e.target.value);
  };

  // Save function
  const handleSave = async () => {
    if (!applicationId) {
      setError('Application ID is required');
      return false;
    }

    setIsSaving(true);
    try {
      // Validate form data
      const validation = validateSection(formData, 'address');
      if (!validation.isValid) {
        setValidationErrors(validation.errors || {});
        return false;
      }

      // Get application and applicant
      const application = await applicationService.getApplication(applicationId);
      if (!application.applicant_id) {
        throw new Error('No applicant found for this application');
      }

      // Create or update single address record with polymorphic relationship
      const addressData = {
        address_type: 'business', // business address type
        entity_type: 'applicant', // polymorphic entity type
        entity_id: application.applicant_id, // polymorphic entity id
        address_line_1: String(formData.address_line_1 || ''),
        address_line_2: formData.address_line_2 ? String(formData.address_line_2) : undefined,
        address_line_3: formData.address_line_3 ? String(formData.address_line_3) : undefined,
        postal_code: String(formData.postal_code || '00000'),
        country: String(formData.country || 'Malawi'),
        city: String(formData.city || '')
      };

      let address;
      let addressId;

      if (existingAddress) {
        // Update existing address
        console.log('🏠 Updating existing address with ID:', existingAddress.address_id);
        const updateData = {
          address_id: existingAddress.address_id,
          ...addressData
        };
        address = await addressService.editAddress(updateData);
        addressId = address.address_id;
      } else {
        console.log('🏠 Creating address:');
        address = await addressService.createAddress(addressData);
        addressId = address.address_id;
        // Update state to track the newly created address
        setExistingAddress(address);
      }

      // Note: With polymorphic relationships, we don't need to update the applicant
      // The address is linked via entity_type='applicant' and entity_id=applicant_id
      // Update application progress
      try {
        await applicationService.updateApplication(applicationId, {
          current_step: 3,
          progress_percentage: 36 // ~3/11 steps completed
        });
      } catch (progressError) {
        console.warn('Failed to update application progress:', progressError);
      }

      setHasUnsavedChanges(false);
      setSuccessMessage('Address information saved successfully!');
      setValidationErrors({}); // Clear any previous errors

      // Auto-hide success message after 5 seconds
      setTimeout(() => {
        setSuccessMessage(null);
      }, 5000);

      console.log('✅ Address information saved successfully');
      return true;

    } catch (error: any) {
      // Extract error message from API response
      let errorMessage = 'Failed to save address information. Please try again.';

      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      console.log('Setting error message:', errorMessage);
      setValidationErrors({ save: errorMessage });
      return false;
    } finally {
      setIsSaving(false);
    }
  };

  // Navigation functions using dynamic navigation
  const handleNext = async () => {
    await dynamicHandleNext(handleSave);
  };

  const handlePrevious = () => {
    dynamicHandlePrevious();
  };

  if (authLoading || isLoading) {
    return (
      <CustomerLayout>
        <div className="flex justify-center items-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </CustomerLayout>
    );
  }

  if (error) {
    return (
      <CustomerLayout>
        <div className="max-w-4xl mx-auto p-6">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-700">{error}</p>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  return (
    <CustomerLayout>
      <ApplicationLayout
        onNext={handleNext}
        onPrevious={handlePrevious}
        onSave={handleSave}
        showNextButton={true}
        showPreviousButton={true}
        showSaveButton={true}
        nextButtonText={nextStep ? `Continue to ${nextStep.name}` : "Continue"}
        previousButtonText="Back to Previous Step"
        saveButtonText={existingAddress ? "Update Address Information" : "Save Address Information"}
        nextButtonDisabled={false}
        isSaving={isSaving}
      >
        {/* Header */}
        <div className="mb-6">
          <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            {applicationId ? 'Edit Address Information' : 'Address Information'}
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {applicationId
              ? 'Update your address information below.'
              : 'Provide your physical and postal address details.'
            }
          </p>
          {applicationId && !loadingWarning && (
            <div className="mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <p className="text-sm text-green-700 dark:text-green-300">
                {existingAddress
                  ? '✅ Editing existing application. Your saved address information has been loaded.'
                  : '📝 Editing existing application. No address information found - you can add it below.'
                }
              </p>
            </div>
          )}
          {loadingWarning && (
            <div className="mt-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
              <p className="text-sm text-yellow-700 dark:text-yellow-300">
                ⚠️ {loadingWarning}
              </p>
            </div>
          )}
        </div>

        {/* Form Messages */}
        <FormMessages
          successMessage={successMessage}
          errorMessage={validationErrors.save}
          validationErrors={Object.fromEntries(
            Object.entries(validationErrors).filter(([key]) => key !== 'save')
          )}
        />

        {/* Address Section */}
        <div className="mb-8">
          <h3 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
            Business Address
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="md:col-span-2">
              <TextInput
                label="Address Line 1"
                value={formData.address_line_1}
                onChange={handleInputChange('address_line_1')}
                error={validationErrors.address_line_1}
                required
                placeholder="Enter street address"
              />
            </div>
            <div className="md:col-span-2">
              <TextInput
                label="Address Line 2"
                value={formData.address_line_2}
                onChange={handleInputChange('address_line_2')}
                error={validationErrors.address_line_2}
                placeholder="Enter additional address information (optional)"
              />
            </div>
            <div className="md:col-span-2">
              <TextInput
                label="Address Line 3"
                value={formData.address_line_3}
                onChange={handleInputChange('address_line_3')}
                error={validationErrors.address_line_3}
                placeholder="Enter additional address information (optional)"
              />
            </div>
            <TextInput
              label="City"
              value={formData.city}
              onChange={handleInputChange('city')}
              error={validationErrors.city}
              required
              placeholder="Enter city"
            />
            <TextInput
              label="Postal Code"
              value={formData.postal_code}
              onChange={handleInputChange('postal_code')}
              error={validationErrors.postal_code}
              placeholder="Enter postal code"
            />
            <TextInput
              label="Country"
              value={formData.country}
              onChange={handleInputChange('country')}
              error={validationErrors.country}
              required
              placeholder="Enter country"
            />
          </div>
        </div>


      </ApplicationLayout>
    </CustomerLayout>
  );
}
