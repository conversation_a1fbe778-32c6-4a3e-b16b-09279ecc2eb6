0000000000000000000000000000000000000000 94b3288f23b030c893ea917de61de431a22f88f3 golden28-K <<EMAIL>> 1750688941 +0200	branch: Created from refs/remotes/origin/development
94b3288f23b030c893ea917de61de431a22f88f3 01998a13c65b75ec7ada66ac05bdef78d73fdbcc golden28-K <<EMAIL>> 1750749009 +0200	commit: added help center page & request resource page
01998a13c65b75ec7ada66ac05bdef78d73fdbcc a40767d1ce09a685711da84392d14e227633e398 golden28-K <<EMAIL>> 1750764251 +0200	commit: added standard and short code application page
a40767d1ce09a685711da84392d14e227633e398 93ddbd086496694bc879277eecb2414fbe0dfd0b golden28-K <<EMAIL>> 1750767151 +0200	pull: Fast-forward
93ddbd086496694bc879277eecb2414fbe0dfd0b d75f51593319d74b98dc19099a11ef77f67db00b golden28-K <<EMAIL>> 1750774864 +0200	commit: added courier application process
d75f51593319d74b98dc19099a11ef77f67db00b df729159d86b8de385ac24c49b837dd4c145a70c golden28-K <<EMAIL>> 1750774895 +0200	pull: Merge made by the 'ort' strategy.
df729159d86b8de385ac24c49b837dd4c145a70c 359b1c4745d9d139aa272836c5e588d2fdad242f golden28-K <<EMAIL>> 1750778114 +0200	commit: fixed visibility issues on textbox
359b1c4745d9d139aa272836c5e588d2fdad242f 3ce3c3861934f95632269115e423b71788e479b5 golden28-K <<EMAIL>> 1750778128 +0200	pull: Merge made by the 'ort' strategy.
3ce3c3861934f95632269115e423b71788e479b5 fda55674d730d2c361edecdd23ffa06b35f3008d golden28-K <<EMAIL>> 1750793758 +0200	commit: added courier service application form
fda55674d730d2c361edecdd23ffa06b35f3008d 3c4ab08e27395389b37045139df0f7f8db8856b2 golden28-K <<EMAIL>> 1750836989 +0200	commit: Implemented multi step postal application and submission
3c4ab08e27395389b37045139df0f7f8db8856b2 a9e21f56264c4520f06ea0fda1459703894cb1fa golden28-K <<EMAIL>> 1750858281 +0200	pull: Fast-forward
a9e21f56264c4520f06ea0fda1459703894cb1fa e539e2695b8cbbcad8f4b16d7adad20d813bc1ef golden28-K <<EMAIL>> 1750860260 +0200	commit: added live data from backend
e539e2695b8cbbcad8f4b16d7adad20d813bc1ef af2a8c7ef82fddde612f0fdc0bb72c1449a54125 golden28-K <<EMAIL>> 1750862659 +0200	commit: integrated the form submission with backend
af2a8c7ef82fddde612f0fdc0bb72c1449a54125 2f4cb0a68ae266fb94dcfd76e8eac445bc9b7360 golden28-K <<EMAIL>> 1750862677 +0200	pull: Merge made by the 'ort' strategy.
2f4cb0a68ae266fb94dcfd76e8eac445bc9b7360 7c467e46725ceb22dd9113b1ec57d2fbd276d347 golden28-K <<EMAIL>> ********** +0200	pull: Fast-forward
7c467e46725ceb22dd9113b1ec57d2fbd276d347 e01b69d2cd99edcb987487d4dd6eda7296cd05d7 golden28-K <<EMAIL>> ********** +0200	commit: applications being created succesfully and recieved in the staff portal
e01b69d2cd99edcb987487d4dd6eda7296cd05d7 7ab4161a78763fd551a000857c410383071a063f golden28-K <<EMAIL>> ********** +0200	commit: added my licenses page and application status bar
7ab4161a78763fd551a000857c410383071a063f c86f25ab73b58fe6bee6efdd6a5a2a2a9742a0aa golden28-K <<EMAIL>> ********** +0200	pull: Merge made by the 'ort' strategy.
c86f25ab73b58fe6bee6efdd6a5a2a2a9742a0aa 66a37c673c1e1ee8a54bed29bf94ec89cfc9919f golden28-K <<EMAIL>> ********** +0200	commit: fixed api fetch bugs on license application
66a37c673c1e1ee8a54bed29bf94ec89cfc9919f 47f90b94397a3c2005a69a16e0ca68eaff7de94d golden28-K <<EMAIL>> ********** +0200	commit: fixed new application button routing
47f90b94397a3c2005a69a16e0ca68eaff7de94d 4bc0dc0544660999efe5ef2b863d1024df470e7e golden28-K <<EMAIL>> ********** +0200	pull: Merge made by the 'ort' strategy.
4bc0dc0544660999efe5ef2b863d1024df470e7e 0ab4e64e340204418d3ac7dc87be02b9d020e753 golden28-K <<EMAIL>> ********** +0200	pull: Fast-forward
0ab4e64e340204418d3ac7dc87be02b9d020e753 98c47163f04f25b8207c2b81de227e6bf6ca73b7 golden28-K <<EMAIL>> ********** +0200	commit: fixed some page rating issues
98c47163f04f25b8207c2b81de227e6bf6ca73b7 28503ee44ce7f14660a02824fc91686e9c218541 golden28-K <<EMAIL>> ********** +0200	commit: fixed get api bugs for license management
28503ee44ce7f14660a02824fc91686e9c218541 44b03c32743473b477e98f44ab85a42578503763 golden28-K <<EMAIL>> ********** +0200	reset: moving to origin/main
44b03c32743473b477e98f44ab85a42578503763 31f5a322b31bb86d07d488006b23f8be5f109d85 golden28-K <<EMAIL>> ********** +0200	pull: Fast-forward
31f5a322b31bb86d07d488006b23f8be5f109d85 b231ddd035426fa8c025154113837e9932803bde golden28-K <<EMAIL>> ********** +0200	commit: fixing minor bugs on log in
b231ddd035426fa8c025154113837e9932803bde 88e28ee1d03fa698ada54df347af75a89e2d1381 golden28-K <<EMAIL>> ********** +0200	pull: Merge made by the 'ort' strategy.
88e28ee1d03fa698ada54df347af75a89e2d1381 ab0c128fedefd6cb3d3ceec28bd14c228264abf0 golden28-K <<EMAIL>> ********** +0200	commit: fixed some routing bugs
ab0c128fedefd6cb3d3ceec28bd14c228264abf0 a3a98a6c8b9b14a20f6682252920182240d90f28 golden28-K <<EMAIL>> ********** +0200	pull: Fast-forward
a3a98a6c8b9b14a20f6682252920182240d90f28 600e65aa257410a47252d191eefe79242c111627 golden28-K <<EMAIL>> ********** +0200	pull origin development: Fast-forward
600e65aa257410a47252d191eefe79242c111627 9aa12ad498906233f1d2c1453c02660d8d7e899a golden28-K <<EMAIL>> ********** +0200	commit: added continuation of saved form
9aa12ad498906233f1d2c1453c02660d8d7e899a ac32351302ff45e2350c55092b40d3b0b6aa9974 golden28-K <<EMAIL>> ********** +0200	commit: worked on form continuation
ac32351302ff45e2350c55092b40d3b0b6aa9974 a9631d107939e9390e8c9edd0dfab4c4836b7da7 golden28-K <<EMAIL>> ********** +0200	pull: Merge made by the 'ort' strategy.
a9631d107939e9390e8c9edd0dfab4c4836b7da7 d986da57bd0bbefa23454598d1f5f283700d7dfb golden28-K <<EMAIL>> ********** +0200	commit: added consumer and data breach pages to staff portal
d986da57bd0bbefa23454598d1f5f283700d7dfb 2273e18d59d8e20c8cffa2cf730f0aa1e5c20d5e golden28-K <<EMAIL>> 1751383339 +0200	pull: Merge made by the 'ort' strategy.
2273e18d59d8e20c8cffa2cf730f0aa1e5c20d5e 74494be5d27bf57cb704b6242fbb9a2c224cc70b golden28-K <<EMAIL>> 1751457798 +0200	commit: fix: replace 'any' type with 'unknown' in data breach page error handling
74494be5d27bf57cb704b6242fbb9a2c224cc70b 7aa449c1ee3683ad5170b2fa256f57d8c1152f51 golden28-K <<EMAIL>> 1751545255 +0200	commit: added view page
7aa449c1ee3683ad5170b2fa256f57d8c1152f51 44fc1bc9a85e9e9d0abf9d2949b82605e59a3ba8 golden28-K <<EMAIL>> 1751610975 +0200	pull: Fast-forward
44fc1bc9a85e9e9d0abf9d2949b82605e59a3ba8 ec0e393c303aa3987ef45d68fa093b69d700f564 golden28-K <<EMAIL>> 1751613578 +0200	pull: Fast-forward
ec0e393c303aa3987ef45d68fa093b69d700f564 10d72a08a3b043723b06e770a8661b4e52bf7f9b golden28-K <<EMAIL>> 1751617308 +0200	pull: Fast-forward
10d72a08a3b043723b06e770a8661b4e52bf7f9b 7522b7012d9a0f86365343da2c2ed65a5709d738 golden28-K <<EMAIL>> 1751623290 +0200	pull: Fast-forward
7522b7012d9a0f86365343da2c2ed65a5709d738 86c294f31e1aa88f83b33dad0ca1fa9939ab2cd3 golden28-K <<EMAIL>> 1751827629 +0200	pull: Fast-forward
86c294f31e1aa88f83b33dad0ca1fa9939ab2cd3 dba521154d52dc886bd46c24dd0adf0c16b27bde golden28-K <<EMAIL>> 1751828260 +0200	commit: fixed get document error and documents are visible
dba521154d52dc886bd46c24dd0adf0c16b27bde e9b01e655a44a38616d6a7e507bc5dfec2e5fb80 golden28-K <<EMAIL>> 1751828665 +0200	commit: created reusable component for viewing documents
e9b01e655a44a38616d6a7e507bc5dfec2e5fb80 1472cb6069018abac8cb6202c7d4f82a99a8a034 golden28-K <<EMAIL>> 1751957666 +0200	commit: fix: resolve React hydration error by changing p elements to div elements
1472cb6069018abac8cb6202c7d4f82a99a8a034 aa8e20860208abd5e27369aa39997fa6768704c4 golden28-K <<EMAIL>> 1751957791 +0200	commit: feat: improve TypeScript type safety in DocumentViewer component
aa8e20860208abd5e27369aa39997fa6768704c4 75056917de9d235d19e5c4cf0fbf173a001b876d golden28-K <<EMAIL>> 1751960622 +0200	commit: feat: add application view modal to staff portal with evaluate and close buttons
75056917de9d235d19e5c4cf0fbf173a001b876d aa8e20860208abd5e27369aa39997fa6768704c4 golden28-K <<EMAIL>> 1751967707 +0200	reset: moving to HEAD~1
aa8e20860208abd5e27369aa39997fa6768704c4 b869222ab0e902936dd5a2b89ac098a4bb563193 golden28-K <<EMAIL>> 1751981137 +0200	pull origin development: Fast-forward
b869222ab0e902936dd5a2b89ac098a4bb563193 98f66534a67b982b1f9c83f43ff099baab0cf00a golden28-K <<EMAIL>> 1752048317 +0200	commit: added task assigment page
98f66534a67b982b1f9c83f43ff099baab0cf00a e39a312d21402f9e31f5a8353c312e82b38e0d4f golden28-K <<EMAIL>> 1752048472 +0200	commit (merge): added task assignment page
e39a312d21402f9e31f5a8353c312e82b38e0d4f 326da0c94002452ffaa6c8bbfb2cd9620d36daa7 golden28-K <<EMAIL>> 1752048481 +0200	pull: Merge made by the 'ort' strategy.
326da0c94002452ffaa6c8bbfb2cd9620d36daa7 c30dcfc0f775c9e62b84b7876ecf6cfa48bea00c golden28-K <<EMAIL>> 1752048787 +0200	pull: Fast-forward
c30dcfc0f775c9e62b84b7876ecf6cfa48bea00c 39b9d9335f82a1d7abf08c03f6ada5bd1737b90e golden28-K <<EMAIL>> 1752053718 +0200	commit: Fix: Form input text visibility in browser dark mode
39b9d9335f82a1d7abf08c03f6ada5bd1737b90e 31d58125f5fba79ea2d1abb1b2b2b833c2d35ad7 golden28-K <<EMAIL>> 1752054845 +0200	pull: Fast-forward
31d58125f5fba79ea2d1abb1b2b2b833c2d35ad7 12a7b033cd88c8f69fbdcf66f4f70cf100de9459 golden28-K <<EMAIL>> 1752061864 +0200	pull: Fast-forward
12a7b033cd88c8f69fbdcf66f4f70cf100de9459 a9a326f3d4c230551b625c174bc8290e2a89b6fc golden28-K <<EMAIL>> 1752072332 +0200	commit: complaint submission working
a9a326f3d4c230551b625c174bc8290e2a89b6fc 257c5b7039c901ff0cf9ad8f82d86f961f074250 golden28-K <<EMAIL>> 1752072342 +0200	pull: Merge made by the 'ort' strategy.
257c5b7039c901ff0cf9ad8f82d86f961f074250 d4a04d6e697307308096d0af0eb1425bf0753526 golden28-K <<EMAIL>> 1752130316 +0200	pull: Fast-forward
d4a04d6e697307308096d0af0eb1425bf0753526 ecfaae9320c2c8efc9c4565579c3fcd5e639cf84 golden28-K <<EMAIL>> 1752131014 +0200	pull: Fast-forward
ecfaae9320c2c8efc9c4565579c3fcd5e639cf84 cd586bc2cd90d1caa63c4d60a7d98dabc6dd9a53 golden28-K <<EMAIL>> 1752134472 +0200	pull: Fast-forward
cd586bc2cd90d1caa63c4d60a7d98dabc6dd9a53 1d1e8fba2f36584a84a3462770aadeb853bbb294 golden28-K <<EMAIL>> 1752138550 +0200	commit: created assign button as component
1d1e8fba2f36584a84a3462770aadeb853bbb294 ec79af49b6b5d7110258bc5f333130ef6dd3b994 golden28-K <<EMAIL>> 1752142565 +0200	commit: added working view modal
ec79af49b6b5d7110258bc5f333130ef6dd3b994 3f453aa92897ada75659221e30315d010b068752 golden28-K <<EMAIL>> 1752142582 +0200	pull: Merge made by the 'ort' strategy.
