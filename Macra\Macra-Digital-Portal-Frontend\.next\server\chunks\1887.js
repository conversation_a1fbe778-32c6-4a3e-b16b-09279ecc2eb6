"use strict";exports.id=1887,exports.ids=[1887],exports.modules={22145:(e,r,t)=>{t.d(r,{A:()=>l});var s=t(60687);let a=(0,t(43210).forwardRef)(({label:e,error:r,helperText:t,variant:a="default",fullWidth:l=!0,className:d="",required:i,disabled:n,...c},o)=>{let x=`px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 ${l?"w-full":""} ${"small"===a?"py-1.5 text-sm":"py-2"}`,m=`${x} ${r?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"} ${n?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""} ${d}`,u=`block font-medium text-gray-700 dark:text-gray-300 mb-2 ${"small"===a?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"}`;return(0,s.jsxs)("div",{className:"w-full",children:[e&&(0,s.jsxs)("label",{className:u,children:[e,i&&(0,s.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,s.jsx)("input",{ref:o,className:m,disabled:n,required:i,...c}),r&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:r}),t&&!r&&(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:t})]})});a.displayName="TextInput";let l=a},24325:(e,r,t)=>{t.d(r,{A:()=>l});var s=t(60687),a=t(43210);let l=({id:e,label:r,accept:t=".pdf",maxSize:l=10,required:d=!1,value:i,onChange:n,description:c,className:o=""})=>{let x=(0,a.useRef)(null);return(0,s.jsxs)("div",{className:o,children:[(0,s.jsxs)("label",{htmlFor:e,className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[r," ",d&&(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsxs)("div",{onClick:()=>{x.current?.click()},className:"border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-3 text-center cursor-pointer hover:border-primary hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors",children:[(0,s.jsx)("input",{ref:x,type:"file",accept:t,onChange:e=>{let r=e.target.files?.[0]||null;if(r){if(r.size>1024*l*1024)return void alert(`File size must be less than ${l}MB`);if(t&&!t.split(",").some(e=>r.name.toLowerCase().endsWith(e.trim().replace("*",""))))return void alert(`File type must be: ${t}`)}n(r)},className:"hidden",id:e,required:d}),i?(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("div",{className:"flex items-center justify-center",children:(0,s.jsx)("i",{className:"ri-file-text-line text-2xl text-green-500"})}),(0,s.jsx)("p",{className:"text-sm font-medium text-green-600 dark:text-green-400",children:i.name}),(0,s.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:[(i.size/1024/1024).toFixed(2)," MB"]}),(0,s.jsxs)("button",{type:"button",onClick:e=>{e.stopPropagation(),n(null),x.current&&(x.current.value="")},className:"inline-flex items-center px-3 py-1 bg-red-100 text-red-700 hover:bg-red-200 rounded-md text-xs font-medium transition-colors",children:[(0,s.jsx)("i",{className:"ri-delete-bin-line mr-1"}),"Remove"]})]}):(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("div",{className:"flex items-center justify-center",children:(0,s.jsx)("i",{className:"ri-upload-cloud-2-line text-2xl text-gray-400"})}),(0,s.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Click to upload ",r.toLowerCase()]}),c&&(0,s.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-500",children:c}),(0,s.jsxs)("div",{className:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors",children:[(0,s.jsx)("i",{className:"ri-folder-upload-line mr-2"}),"Choose File"]})]})]}),c&&!i&&(0,s.jsx)("p",{className:"mt-2 text-xs text-gray-500 dark:text-gray-400",children:c})]})}},25890:(e,r,t)=>{t.d(r,{f:()=>i});var s=t(43210),a=t(16189),l=t(25011),d=t(36212);let i=({currentStepRoute:e,licenseCategoryId:r,applicationId:t})=>{let i=(0,a.useRouter)(),[n,c]=(0,s.useState)(!0),[o,x]=(0,s.useState)(null),[m,u]=(0,s.useState)(null),[g,y]=(0,s.useState)([]),b=(0,s.useMemo)(()=>new d.ef,[]),p=(0,s.useCallback)(async()=>{if(!r){x("License category ID is required"),c(!1);return}try{c(!0),x(null);let e=await b.getLicenseCategory(r);if(!e?.license_type_id)throw Error("License category does not have a license type ID");await new Promise(e=>setTimeout(e,500));let t=await b.getLicenseType(e.license_type_id);if(!t)throw Error("License type not found");let s=t.code||t.license_type_id;u(s);let a=[];a=(0,l.nF)(s)?(0,l.PY)(s):(0,l.QE)(s).steps,y(a)}catch(e){x(e.message||"Failed to load navigation configuration"),y((0,l.QE)("default").steps),u("default")}finally{c(!1)}},[r,b]);(0,s.useEffect)(()=>{p()},[p]);let h=(0,s.useMemo)(()=>g.findIndex(r=>r.route===e),[g,e]),f=(0,s.useMemo)(()=>g[h]||null,[g,h]),k=(0,s.useMemo)(()=>h>=0&&h<g.length-1?g[h+1]:null,[g,h]),j=(0,s.useMemo)(()=>h>0?g[h-1]:null,[g,h]),N=g.length,v=0===h,w=h===g.length-1,$=!w&&null!==k,C=!v&&null!==j,_=(0,s.useCallback)(e=>{let s=new URLSearchParams;return s.set("license_category_id",r||""),t&&s.set("application_id",t),`/customer/applications/apply/${e}?${s.toString()}`},[r,t]),A=(0,s.useCallback)(e=>{let r=_(e);i.push(r)},[_,i]);return{handleNext:(0,s.useCallback)(async e=>{if($&&k){if(e)try{if(!await e())return}catch(e){e.message?.includes("timeout")||e.message?.includes("Bad Request")||e.message?.includes("Too many requests");return}A(k.route)}},[$,k,A]),handlePrevious:(0,s.useCallback)(()=>{C&&j&&A(j.route)},[C,j,A]),navigateToStep:A,currentStep:f,nextStep:k,previousStep:j,currentStepIndex:h,totalSteps:N,loading:n,error:o,licenseTypeCode:m,isFirstStep:v,isLastStep:w,canNavigateNext:$,canNavigatePrevious:C}}},62978:(e,r,t)=>{t.d(r,{A:()=>l});var s=t(60687);let a=(0,t(43210).forwardRef)(({label:e,error:r,helperText:t,variant:a="default",fullWidth:l=!0,className:d="",required:i,disabled:n,rows:c=3,...o},x)=>{let m=`px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 resize-y ${l?"w-full":""} ${"small"===a?"py-1.5 text-sm":"py-2"}`,u=`${m} ${r?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"} ${n?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""} ${d}`,g=`block font-medium text-gray-700 dark:text-gray-300 mb-2 ${"small"===a?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"}`;return(0,s.jsxs)("div",{className:"w-full",children:[e&&(0,s.jsxs)("label",{className:g,children:[e,i&&(0,s.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,s.jsx)("textarea",{ref:x,className:u,disabled:n,required:i,rows:c,...o}),r&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:r}),t&&!r&&(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:t})]})});a.displayName="TextArea";let l=a},76377:(e,r,t)=>{t.d(r,{l6:()=>l.A,fs:()=>a.A,ks:()=>s.A}),t(24325),t(60687),t(43210);var s=t(22145),a=t(62978),l=t(86732)},86732:(e,r,t)=>{t.d(r,{A:()=>l});var s=t(60687);let a=(0,t(43210).forwardRef)(({label:e,error:r,helperText:t,variant:a="default",fullWidth:l=!0,className:d="",required:i,disabled:n,options:c,children:o,...x},m)=>{let u=`px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white transition-colors duration-200 ${l?"w-full":""} ${"small"===a?"py-1.5 text-sm":"py-2"}`,g=`${u} ${r?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"} ${n?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""} ${d}`,y=`block font-medium text-gray-700 dark:text-gray-300 mb-2 ${"small"===a?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"}`;return(0,s.jsxs)("div",{className:"w-full",children:[e&&(0,s.jsxs)("label",{className:y,children:[e,i&&(0,s.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,s.jsx)("select",{ref:m,className:g,disabled:n,required:i,...x,children:c?c.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value)):o}),r&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:r}),t&&!r&&(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:t})]})});a.displayName="Select";let l=a},99798:(e,r,t)=>{t.d(r,{bc:()=>a});var s=t(60687);t(43210);let a=({successMessage:e,errorMessage:r,validationErrors:t={},className:a=""})=>(r||Object.keys(t).length,(0,s.jsxs)("div",{className:a,children:[e&&(0,s.jsx)("div",{className:"mb-6 bg-green-50 border border-green-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("i",{className:"ri-check-circle-line text-green-500 mr-2"}),(0,s.jsx)("p",{className:"text-green-700",children:e})]})}),r&&(0,s.jsx)("div",{className:"mb-6 bg-red-50 border border-red-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("i",{className:"ri-error-warning-line text-red-500 mr-2"}),(0,s.jsx)("p",{className:"text-red-700",children:r})]})}),Object.keys(t).length>0&&!r&&(0,s.jsx)("div",{className:"mb-6 bg-red-50 border border-red-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("i",{className:"ri-error-warning-line text-red-500 mr-2 mt-0.5"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-red-900 mb-2",children:"Please fix the following issues:"}),(0,s.jsx)("ul",{className:"text-sm text-red-700 space-y-1",children:Object.entries(t).map(([e,r])=>(0,s.jsxs)("li",{children:["• ",r]},e))})]})]})})]}))}};