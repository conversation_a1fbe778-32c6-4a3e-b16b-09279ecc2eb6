(()=>{var e={};e.id=3011,e.ids=[3011],e.modules={550:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var s=t(60687),o=t(43210),i=t(16189),a=t(63213),n=t(21891),l=t(60417);function d({children:e}){let{isAuthenticated:r,loading:t}=(0,a.A)();(0,i.useRouter)();let[d,c]=(0,o.useState)("overview"),[u,p]=(0,o.useState)(!1);return t?(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"})}):r?(0,s.jsxs)("div",{className:"flex h-screen overflow-hidden",children:[(0,s.jsx)("div",{id:"mobileSidebarOverlay",className:`mobile-sidebar-overlay ${u?"show":""}`,onClick:()=>p(!1)}),(0,s.jsx)(l.A,{}),(0,s.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,s.jsx)(n.A,{activeTab:d,onTabChange:c,onMobileMenuToggle:()=>{p(!u)}}),e]})]}):null}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28473:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=t(65239),o=t(48088),i=t(88170),a=t.n(i),n=t(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);t.d(r,l);let d={children:["",{children:["test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,96663)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\test\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,76e3)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\test\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\test\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/test/page",pathname:"/test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},50691:(e,r,t)=>{Promise.resolve().then(t.bind(t,550))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},76e3:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\test\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\test\\layout.tsx","default")},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91019:(e,r,t)=>{Promise.resolve().then(t.bind(t,76e3))},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{},96663:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var s=t(37413);function o(){return(0,s.jsxs)("div",{style:{padding:"20px",fontFamily:"Arial, sans-serif"},children:[(0,s.jsx)("h1",{children:"MACRA Test Page"}),(0,s.jsx)("p",{children:"If you can see this, Next.js is working!"}),(0,s.jsxs)("div",{style:{marginTop:"20px"},children:[(0,s.jsx)("a",{href:"/auth/login",style:{padding:"10px 20px",backgroundColor:"#e02b20",color:"white",textDecoration:"none",borderRadius:"5px",marginRight:"10px"},children:"Go to Login"}),(0,s.jsx)("a",{href:"/dashboard",style:{padding:"10px 20px",backgroundColor:"#6366f1",color:"white",textDecoration:"none",borderRadius:"5px"},children:"Go to Dashboard"})]})]})}}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,7498,1658,5814,7563,6606],()=>t(28473));module.exports=s})();