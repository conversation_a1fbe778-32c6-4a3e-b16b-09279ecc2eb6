"use strict";exports.id=2324,exports.ids=[2324],exports.modules={15885:(t,e,a)=>{a.d(e,{U_:()=>n,_l:()=>s,qI:()=>i});class r{set(t,e,a=this.defaultTTL){let r=Date.now();this.cache.set(t,{data:e,timestamp:r,expiresAt:r+a})}get(t){let e=this.cache.get(t);return e?Date.now()>e.expiresAt?(this.cache.delete(t),null):e.data:null}has(t){return null!==this.get(t)}delete(t){return this.cache.delete(t)}clear(){this.cache.clear()}getStats(){return{size:this.cache.size,keys:Array.from(this.cache.keys())}}cleanup(){let t=Date.now(),e=0;for(let[e,a]of this.cache.entries())t>a.expiresAt&&this.cache.delete(e)}async getOrSet(t,e,a=this.defaultTTL){let r=this.get(t);if(null!==r)return r;let i=await e();return this.set(t,i,a),i}invalidatePattern(t){let e=new RegExp(t),a=0;for(let t of this.cache.keys())e.test(t)&&this.cache.delete(t)}constructor(){this.cache=new Map,this.defaultTTL=3e5}}let i=new r,s={LICENSE_TYPES:"license-types",LICENSE_CATEGORIES:"license-categories",LICENSE_CATEGORIES_BY_TYPE:t=>`license-categories-type-${t}`,USER_APPLICATIONS:"user-applications",APPLICATION:t=>`application-${t}`},n={SHORT:12e4,MEDIUM:3e5,LONG:9e5,VERY_LONG:36e5};setInterval(()=>{i.cleanup()},3e5)},34130:(t,e,a)=>{a.d(e,{TG:()=>c});var r=a(12234),i=a(15885);let s=t=>t.toLowerCase().replace(/[^a-z0-9\s]/g,"").replace(/\s+/g,"-").replace(/^-+|-+$/g,"").substring(0,50),n=t=>t.map(t=>({...t,code:s(t.name),children:t.children?n(t.children):void 0})),c={async getLicenseCategories(t={}){let e=new URLSearchParams;return t.page&&e.set("page",t.page.toString()),t.limit&&e.set("limit",t.limit.toString()),t.search&&e.set("search",t.search),t.sortBy&&t.sortBy.forEach(t=>e.append("sortBy",t)),t.searchBy&&t.searchBy.forEach(t=>e.append("searchBy",t)),t.filter&&Object.entries(t.filter).forEach(([t,a])=>{Array.isArray(a)?a.forEach(a=>e.append(`filter.${t}`,a)):e.set(`filter.${t}`,a)}),(await r.uE.get(`/license-categories?${e.toString()}`)).data},async getLicenseCategory(t){try{return(await r.uE.get(`/license-categories/${t}`,{timeout:3e4})).data}catch(t){if("ECONNABORTED"===t.code)throw Error("Request timeout - please try again");throw t}},async getLicenseCategoriesByType(t){try{return(await r.uE.get(`/license-categories/by-license-type/${t}`,{timeout:3e4})).data}catch(t){if("ECONNABORTED"===t.code)throw Error("Request timeout - please try again");if(t.response?.status===429)throw Error("Too many requests - please wait a moment and try again");throw t}},createLicenseCategory:async t=>(await r.uE.post("/license-categories",t)).data,updateLicenseCategory:async(t,e)=>(await r.uE.put(`/license-categories/${t}`,e)).data,deleteLicenseCategory:async t=>(await r.uE.delete(`/license-categories/${t}`)).data,async getAllLicenseCategories(){return i.qI.getOrSet(i._l.LICENSE_CATEGORIES,async()=>n((await this.getLicenseCategories({limit:100})).data),i.U_.LONG)},getCategoryTree:async t=>i.qI.getOrSet(`category-tree-${t}`,async()=>n((await r.uE.get(`/license-categories/license-type/${t}/tree`)).data),i.U_.MEDIUM),getRootCategories:async t=>i.qI.getOrSet(`root-categories-${t}`,async()=>(await r.uE.get(`/license-categories/license-type/${t}/root`)).data,i.U_.MEDIUM),async getCategoriesForParentSelection(t,e){try{try{let a=await r.uE.get(`/license-categories/license-type/${t}/for-parent-selection`,{params:e?{excludeId:e}:{}});if(a.data&&Array.isArray(a.data.data))return a.data.data;return[]}catch(i){let a=await r.uE.get(`/license-categories/by-license-type/${t}`);if(!(a.data&&Array.isArray(a.data)))return[];{let t=a.data;return e&&(t=t.filter(t=>t.license_category_id!==e)),t}}}catch(t){return[]}},getPotentialParents:async(t,e)=>(await r.uE.get(`/license-categories/license-type/${t}/potential-parents`,{params:e?{excludeId:e}:{}})).data}},78637:(t,e,a)=>{a.d(e,{k:()=>s});var r=a(51278),i=a(12234);let s={async getApplications(t){let e=new URLSearchParams;t?.page&&e.append("page",t.page.toString()),t?.limit&&e.append("limit",t.limit.toString()),t?.search&&e.append("search",t.search),t?.sortBy&&e.append("sortBy",t.sortBy),t?.sortOrder&&e.append("sortOrder",t.sortOrder),t?.filters?.licenseTypeId&&e.append("filter.license_category.license_type_id",t.filters.licenseTypeId),t?.filters?.licenseCategoryId&&e.append("filter.license_category_id",t.filters.licenseCategoryId),t?.filters?.status&&e.append("filter.status",t.filters.status);let a=await i.uE.get(`/applications?${e.toString()}`);return(0,r.zp)(a)},async getApplicationsByLicenseType(t,e){let a=new URLSearchParams;e?.page&&a.append("page",e.page.toString()),e?.limit&&a.append("limit",e.limit.toString()),e?.search&&a.append("search",e.search),e?.status&&a.append("filter.status",e.status),a.append("filter.license_category.license_type_id",t);let s=await i.uE.get(`/applications?${a.toString()}`);return(0,r.zp)(s)},async getApplication(t){let e=await i.uE.get(`/applications/${t}`);return(0,r.zp)(e)},async getApplicationsByApplicant(t){let e=await i.uE.get(`/applications/by-applicant/${t}`);return(0,r.zp)(e)},async getApplicationsByStatus(t){let e=await i.uE.get(`/applications/by-status/${t}`);return(0,r.zp)(e)},async updateApplicationStatus(t,e){let a=await i.uE.put(`/applications/${t}/status?status=${e}`);return(0,r.zp)(a)},async updateApplicationProgress(t,e,a){let s=await i.uE.put(`/applications/${t}/progress?currentStep=${e}&progressPercentage=${a}`);return(0,r.zp)(s)},async getApplicationStats(){let t=await i.uE.get("/applications/stats");return(0,r.zp)(t)},async createApplication(t){try{let e=await i.uE.post("/applications",t);return(0,r.zp)(e)}catch(t){throw t}},async updateApplication(t,e){try{let a=await i.uE.put(`/applications/${t}`,e,{timeout:3e4});return(0,r.zp)(a)}catch(t){if("ECONNABORTED"===t.code)throw Error("Request timeout - please try again");if(t.response?.status===400){let e=t.response?.data?.message||"Invalid application data";throw Error(`Bad Request: ${e}`)}if(t.response?.status===429)throw Error("Too many requests - please wait a moment and try again");throw t}},async deleteApplication(t){let e=await i.uE.delete(`/applications/${t}`);return(0,r.zp)(e)},async createApplicationWithApplicant(t){try{let e=new Date,a=e.toISOString().slice(0,10).replace(/-/g,""),r=e.toTimeString().slice(0,8).replace(/:/g,""),i=Math.random().toString(36).substr(2,3).toUpperCase(),s=`APP-${a}-${r}-${i}`;if(!/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(t.user_id))throw Error(`Invalid user_id format: ${t.user_id}. Expected UUID format.`);return await this.createApplication({application_number:s,applicant_id:t.user_id,license_category_id:t.license_category_id,current_step:1,progress_percentage:0})}catch(t){throw t}},async saveApplicationSection(t,e,a){try{let a=1,r=["applicantInfo","companyProfile","businessInfo","serviceScope","businessPlan","legalHistory","reviewSubmit"].indexOf(e);a=r>=0?r+1:1;let i=Math.min(Math.round(a/6*100),100);await this.updateApplication(t,{progress_percentage:i,current_step:a})}catch(t){throw t}},async getApplicationSection(t,e){try{let a=await i.uE.get(`/applications/${t}/sections/${e}`);return(0,r.zp)(a)}catch(t){throw t}},async submitApplication(t){try{let e=await i.uE.put(`/applications/${t}`,{status:"submitted",submitted_at:new Date().toISOString(),progress_percentage:100,current_step:7});return(0,r.zp)(e)}catch(t){throw t}},async getUserApplications(){try{let t=await i.uE.get("/applications/user-applications"),e=(0,r.zp)(t),a=[];return e?.data?a=Array.isArray(e.data)?e.data:[]:Array.isArray(e)?a=e:e&&(a=[e]),a}catch(t){throw t}},async saveAsDraft(t,e){try{let a=await i.uE.put(`/applications/${t}`,{form_data:e,status:"draft"});return(0,r.zp)(a)}catch(t){throw t}},async validateApplication(t){try{let t={},e=[];for(let a of["applicantInfo","companyProfile","businessInfo","legalHistory"])t[a]&&0!==Object.keys(t[a]).length||e.push(`${a} section is incomplete`);return{isValid:0===e.length,errors:e}}catch(t){throw t}}}},85787:(t,e,a)=>{a.d(e,{u:()=>n});var r=a(43210),i=a(78637),s=a(91923);let n=(t={})=>{let{applicationId:e,stepName:a,autoLoad:n=!0}=t,[c,p]=(0,r.useState)({}),[o,l]=(0,r.useState)(!1),[d,u]=(0,r.useState)(null),h=async t=>{try{l(!0),u(null);let e={};try{let r=await i.k.getApplication(t);if(e.application=r,r.applicant_id)try{try{e.stakeholders=await s.Y.getStakeholdersByApplication(r.application_id)}catch(t){e.stakeholders=[]}}catch(t){}if(a)e.formData={};else try{e.formData={}}catch(t){e.formData={}}}catch(t){throw Error("Failed to load application data")}return p(e),e}catch(t){throw u(t.message||"Failed to load application data"),t}finally{l(!1)}},y=async(t,a)=>{if(!e)throw Error("No application ID provided");try{return!0}catch(t){throw t}};return(0,r.useEffect)(()=>{n&&e&&h(e).catch(console.error)},[e,n]),{data:c,loading:o,error:d,loadApplicationData:h,getFormDataForStep:(t,e)=>{let a=e||c,r={};switch(a.formData&&a.formData[t]&&Object.assign(r,a.formData[t]),t){case"applicant-info":a.applicant&&Object.assign(r,{name:a.applicant.name||"",business_registration_number:a.applicant.business_registration_number||"",tpin:a.applicant.tpin||"",website:a.applicant.website||"",email:a.applicant.email||"",phone:a.applicant.phone||"",fax:a.applicant.fax||"",level_of_insurance_cover:a.applicant.level_of_insurance_cover||"",date_incorporation:a.applicant.date_incorporation||"",place_incorporation:a.applicant.place_incorporation||""});break;case"company-profile":a.applicant&&Object.assign(r,{company_name:a.applicant.name||"",business_registration_number:a.applicant.business_registration_number||"",website:a.applicant.website||"",company_email:a.applicant.email||"",company_phone:a.applicant.phone||"",incorporation_date:a.applicant.date_incorporation||"",incorporation_place:a.applicant.place_incorporation||""});break;case"management":a.stakeholders&&Object.assign(r,{stakeholders:a.stakeholders.map(t=>({stakeholder_id:t.stakeholder_id,first_name:t.first_name,last_name:t.last_name,middle_name:t.middle_name||"",nationality:t.nationality,position:t.position,profile:t.profile,contact_id:t.contact_id,cv_document_id:t.cv_document_id}))})}return r},saveFormData:y,application:c.application,applicant:c.applicant,stakeholders:c.stakeholders||[],formData:c.formData||{}}}},91923:(t,e,a)=>{a.d(e,{Y:()=>s});var r=a(12234);let i=t=>t.data?.path?t.data:t.data?.data?t.data.data:t.data,s={async createStakeholder(t){try{let e=await r.uE.post("/stakeholders",t);return i(e)}catch(t){throw t}},async getStakeholder(t){try{let e=await r.uE.get(`/stakeholders/${t}`);return i(e)}catch(t){throw t}},async getStakeholdersByApplication(t){try{let e=await r.uE.get(`/stakeholders/application/${t}`);return i(e)}catch(t){return[]}},async updateStakeholder(t,e){try{let a=await r.uE.put(`/stakeholders/${t}`,e);return i(a)}catch(t){throw t}},async deleteStakeholder(t){try{await r.uE.delete(`/stakeholders/${t}`)}catch(t){throw t}},async getStakeholders(t){try{let e=await r.uE.get("/stakeholders",{params:t});return i(e)}catch(t){throw t}},async createStakeholdersForApplicant(t,e){try{let a=e.map(e=>({...e,applicant_id:t})),r=[];for(let t of a)try{let e=await this.createStakeholder(t);r.push(e)}catch(t){}return r}catch(t){throw t}},async updateStakeholdersForApplicant(t,e){try{let a=[];for(let r of e)try{if(r.stakeholder_id){let t=await this.updateStakeholder(r.stakeholder_id,r);a.push(t)}else{let e=await this.createStakeholder({...r,application_id:t});a.push(e)}}catch(t){}return a}catch(t){throw t}}}}};