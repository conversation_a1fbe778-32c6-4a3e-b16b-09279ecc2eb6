"use strict";exports.id=2324,exports.ids=[2324],exports.modules={15885:(e,t,a)=>{a.d(t,{U_:()=>s,_l:()=>c,qI:()=>i});class r{set(e,t,a=this.defaultTTL){let r=Date.now();this.cache.set(e,{data:t,timestamp:r,expiresAt:r+a})}get(e){let t=this.cache.get(e);return t?Date.now()>t.expiresAt?(this.cache.delete(e),null):t.data:null}has(e){return null!==this.get(e)}delete(e){return this.cache.delete(e)}clear(){this.cache.clear()}getStats(){return{size:this.cache.size,keys:Array.from(this.cache.keys())}}cleanup(){let e=Date.now(),t=0;for(let[t,a]of this.cache.entries())e>a.expiresAt&&this.cache.delete(t)}async getOrSet(e,t,a=this.defaultTTL){let r=this.get(e);if(null!==r)return r;let i=await t();return this.set(e,i,a),i}invalidatePattern(e){let t=new RegExp(e),a=0;for(let e of this.cache.keys())t.test(e)&&this.cache.delete(e)}constructor(){this.cache=new Map,this.defaultTTL=3e5}}let i=new r,c={LICENSE_TYPES:"license-types",LICENSE_CATEGORIES:"license-categories",LICENSE_CATEGORIES_BY_TYPE:e=>`license-categories-type-${e}`,USER_APPLICATIONS:"user-applications",APPLICATION:e=>`application-${e}`},s={SHORT:12e4,MEDIUM:3e5,LONG:9e5,VERY_LONG:36e5};setInterval(()=>{i.cleanup()},3e5)},34130:(e,t,a)=>{a.d(t,{TG:()=>n});var r=a(72901),i=a(15885);let c=e=>e.toLowerCase().replace(/[^a-z0-9\s]/g,"").replace(/\s+/g,"-").replace(/^-+|-+$/g,"").substring(0,50),s=e=>e.map(e=>({...e,code:c(e.name),children:e.children?s(e.children):void 0})),n={async getLicenseCategories(e={}){let t=new URLSearchParams;return e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(([e,a])=>{Array.isArray(a)?a.forEach(a=>t.append(`filter.${e}`,a)):t.set(`filter.${e}`,a)}),(await r.uE.get(`/license-categories?${t.toString()}`)).data},getLicenseCategory:async e=>(await r.uE.get(`/license-categories/${e}`)).data,getLicenseCategoriesByType:async e=>(await r.uE.get(`/license-categories/by-license-type/${e}`)).data,createLicenseCategory:async e=>(await r.uE.post("/license-categories",e)).data,updateLicenseCategory:async(e,t)=>(await r.uE.put(`/license-categories/${e}`,t)).data,deleteLicenseCategory:async e=>(await r.uE.delete(`/license-categories/${e}`)).data,async getAllLicenseCategories(){return i.qI.getOrSet(i._l.LICENSE_CATEGORIES,async()=>s((await this.getLicenseCategories({limit:100})).data),i.U_.LONG)},getCategoryTree:async e=>i.qI.getOrSet(`category-tree-${e}`,async()=>s((await r.uE.get(`/license-categories/license-type/${e}/tree`)).data),i.U_.MEDIUM),getRootCategories:async e=>i.qI.getOrSet(`root-categories-${e}`,async()=>(await r.uE.get(`/license-categories/license-type/${e}/root`)).data,i.U_.MEDIUM),async getCategoriesForParentSelection(e,t){try{try{let a=await r.uE.get(`/license-categories/license-type/${e}/for-parent-selection`,{params:t?{excludeId:t}:{}});if(a.data&&Array.isArray(a.data.data))return a.data.data;return[]}catch(i){let a=await r.uE.get(`/license-categories/by-license-type/${e}`);if(!(a.data&&Array.isArray(a.data)))return[];{let e=a.data;return t&&(e=e.filter(e=>e.license_category_id!==t)),e}}}catch(e){return[]}},getPotentialParents:async(e,t)=>(await r.uE.get(`/license-categories/license-type/${e}/potential-parents`,{params:t?{excludeId:t}:{}})).data}},85787:(e,t,a)=>{a.d(t,{u:()=>n});var r=a(43210),i=a(78637),c=a(55457),s=a(91923);let n=(e={})=>{let{applicationId:t,stepName:a,autoLoad:n=!0}=e,[l,o]=(0,r.useState)({}),[p,d]=(0,r.useState)(!1),[h,y]=(0,r.useState)(null),g=async e=>{try{d(!0),y(null);let t={};try{let r=await i.k.getApplication(e);if(t.application=r,r.applicant_id)try{t.applicant=await c.W.getApplicant(r.applicant_id);try{t.stakeholders=await s.Y.getStakeholdersByApplicant(r.applicant_id)}catch(e){t.stakeholders=[]}}catch(e){}if(a)t.formData={};else try{t.formData={}}catch(e){t.formData={}}}catch(e){throw Error("Failed to load application data")}return o(t),t}catch(e){throw y(e.message||"Failed to load application data"),e}finally{d(!1)}},u=async(e,a)=>{if(!t)throw Error("No application ID provided");try{return!0}catch(e){throw e}};return(0,r.useEffect)(()=>{n&&t&&g(t).catch(console.error)},[t,n]),{data:l,loading:p,error:h,loadApplicationData:g,getFormDataForStep:(e,t)=>{let a=t||l,r={};switch(a.formData&&a.formData[e]&&Object.assign(r,a.formData[e]),e){case"applicant-info":a.applicant&&Object.assign(r,{name:a.applicant.name||"",business_registration_number:a.applicant.business_registration_number||"",tpin:a.applicant.tpin||"",website:a.applicant.website||"",email:a.applicant.email||"",phone:a.applicant.phone||"",fax:a.applicant.fax||"",level_of_insurance_cover:a.applicant.level_of_insurance_cover||"",date_incorporation:a.applicant.date_incorporation||"",place_incorporation:a.applicant.place_incorporation||""});break;case"company-profile":a.applicant&&Object.assign(r,{company_name:a.applicant.name||"",business_registration_number:a.applicant.business_registration_number||"",website:a.applicant.website||"",company_email:a.applicant.email||"",company_phone:a.applicant.phone||"",incorporation_date:a.applicant.date_incorporation||"",incorporation_place:a.applicant.place_incorporation||""});break;case"management":a.stakeholders&&Object.assign(r,{stakeholders:a.stakeholders.map(e=>({stakeholder_id:e.stakeholder_id,first_name:e.first_name,last_name:e.last_name,middle_name:e.middle_name||"",nationality:e.nationality,position:e.position,profile:e.profile,contact_id:e.contact_id,cv_document_id:e.cv_document_id}))})}return r},saveFormData:u,application:l.application,applicant:l.applicant,stakeholders:l.stakeholders||[],formData:l.formData||{}}}},91923:(e,t,a)=>{a.d(t,{Y:()=>c});var r=a(72901);let i=e=>e.data?.path?e.data:e.data?.data?e.data.data:e.data,c={async createStakeholder(e){try{let t=await r.uE.post("/stakeholders",e);return i(t)}catch(e){throw e}},async getStakeholder(e){try{let t=await r.uE.get(`/stakeholders/${e}`);return i(t)}catch(e){throw e}},async getStakeholdersByApplicant(e){try{let t=await r.uE.get(`/stakeholders/applicant/${e}`);return i(t)}catch(e){return[]}},async updateStakeholder(e,t){try{let a=await r.uE.put(`/stakeholders/${e}`,t);return i(a)}catch(e){throw e}},async deleteStakeholder(e){try{await r.uE.delete(`/stakeholders/${e}`)}catch(e){throw e}},async getStakeholders(e){try{let t=await r.uE.get("/stakeholders",{params:e});return i(t)}catch(e){throw e}},async createStakeholdersForApplicant(e,t){try{let a=t.map(t=>({...t,applicant_id:e})),r=[];for(let e of a)try{let t=await this.createStakeholder(e);r.push(t)}catch(e){}return r}catch(e){throw e}},async updateStakeholdersForApplicant(e,t){try{let a=[];for(let r of t)try{if(r.stakeholder_id){let e=await this.updateStakeholder(r.stakeholder_id,r);a.push(e)}else{let t=await this.createStakeholder({...r,applicant_id:e});a.push(t)}}catch(e){}return a}catch(e){throw e}}}}};