// Rate limiting utility to prevent API throttling

interface RequestInfo {
  timestamp: number;
  count: number;
}

class RateLimiter {
  private requests = new Map<string, RequestInfo[]>();
  private maxRequests: number;
  private windowMs: number;

  constructor(maxRequests: number = 100, windowMs: number = 60000) {
    this.maxRequests = maxRequests;
    this.windowMs = windowMs;
  }

  /**
   * Check if request is allowed and add delay if needed
   */
  async checkRateLimit(key: string): Promise<void> {
    const now = Date.now();
    const requests = this.requests.get(key) || [];

    // Remove old requests outside the window
    const validRequests = requests.filter(req => now - req.timestamp < this.windowMs);

    // Check if we're at the limit
    if (validRequests.length >= this.maxRequests) {
      const oldestRequest = validRequests[0];
      const waitTime = this.windowMs - (now - oldestRequest.timestamp);
      
      console.warn(`Rate limit reached for ${key}. Waiting ${waitTime}ms...`);
      await this.delay(waitTime);
    }

    // Add current request
    validRequests.push({ timestamp: now, count: 1 });
    this.requests.set(key, validRequests);
  }

  /**
   * Add artificial delay between requests
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Clear rate limit history for a key
   */
  clearKey(key: string): void {
    this.requests.delete(key);
  }

  /**
   * Clear all rate limit history
   */
  clearAll(): void {
    this.requests.clear();
  }

  /**
   * Get current request count for a key
   */
  getCurrentCount(key: string): number {
    const now = Date.now();
    const requests = this.requests.get(key) || [];
    return requests.filter(req => now - req.timestamp < this.windowMs).length;
  }

  /**
   * Check if key is currently rate limited
   */
  isRateLimited(key: string): boolean {
    return this.getCurrentCount(key) >= this.maxRequests;
  }
}

// Create singleton instances for different types of requests
export const apiRateLimiter = new RateLimiter(5, 60000); // 5 requests per minute
export const licenseDataRateLimiter = new RateLimiter(3, 30000); // 3 requests per 30 seconds

// Utility function to add exponential backoff
export async function withExponentialBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: Error;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error: any) {
      lastError = error;

      // Don't retry if it's not a rate limit error
      if (error.response?.status !== 429) {
        throw error;
      }

      // Don't retry on the last attempt
      if (attempt === maxRetries) {
        break;
      }

      // Calculate delay with exponential backoff
      const delay = baseDelay * Math.pow(2, attempt);
      const jitter = Math.random() * 1000; // Add some randomness
      const totalDelay = delay + jitter;

      console.warn(`Rate limited (attempt ${attempt + 1}/${maxRetries + 1}). Retrying in ${totalDelay}ms...`);
      await new Promise(resolve => setTimeout(resolve, totalDelay));
    }
  }

  throw lastError!;
}

// Utility function to batch requests with delays
export async function batchRequests<T, R>(
  items: T[],
  requestFn: (item: T) => Promise<R>,
  batchSize: number = 3,
  delayBetweenBatches: number = 1000
): Promise<R[]> {
  const results: R[] = [];
  
  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    
    // Process batch in parallel
    const batchPromises = batch.map(item => requestFn(item));
    const batchResults = await Promise.all(batchPromises);
    
    results.push(...batchResults);
    
    // Add delay between batches (except for the last batch)
    if (i + batchSize < items.length) {
      console.log(`Processed batch ${Math.floor(i / batchSize) + 1}. Waiting ${delayBetweenBatches}ms before next batch...`);
      await new Promise(resolve => setTimeout(resolve, delayBetweenBatches));
    }
  }
  
  return results;
}

// Debounce utility for frequent API calls
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Throttle utility for limiting function calls
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

export default RateLimiter;
