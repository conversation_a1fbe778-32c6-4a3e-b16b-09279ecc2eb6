{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\r\nimport type { NextRequest } from 'next/server';\r\n\r\nexport function middleware(request: NextRequest) {\r\n  const url = request.nextUrl.clone();\r\n\r\n  // Get auth tokens from cookies\r\n  const authToken = request.cookies.get('auth_token');\r\n  const authUser = request.cookies.get('auth_user');\r\n\r\n  // Parse user data if available\r\n  let user = null;\r\n\r\n  // Try to get user from customer auth first, then staff auth\r\n  if (authUser) {\r\n    try {\r\n      user = JSON.parse(authUser.value);\r\n    } catch (error) {\r\n      console.error('Failed to parse user data:', error);\r\n    }\r\n  }\r\n\r\n  // Always allow auth routes without redirection\r\n  if (url.pathname.startsWith('/customer/auth/') || url.pathname.startsWith('/auth/')) {\r\n    return NextResponse.next();\r\n  }\r\n\r\n  // Handle root path redirections\r\n  if (url.pathname === '/') {\r\n    if (user && user.roles && user.roles.includes('customer')) {\r\n      url.pathname = '/customer';\r\n      return NextResponse.redirect(url);\r\n    } else if (authToken) {\r\n      url.pathname = '/dashboard';\r\n      return NextResponse.redirect(url);\r\n    } else {\r\n      url.pathname = '/auth/login';\r\n      return NextResponse.redirect(url);\r\n    }\r\n  }\r\n\r\n  // Handle customer routes\r\n  if (url.pathname.startsWith('/customer')) {\r\n\r\n    if(!user.roles.includes('customer')){\r\n      url.pathname = '/dashboard';\r\n      return NextResponse.redirect(url);\r\n    }\r\n\r\n    // For other customer routes, check authentication\r\n    if (!authToken || !user) {\r\n      url.pathname = '/customer/auth/login';\r\n      return NextResponse.redirect(url);\r\n    }\r\n\r\n    // Allow all authenticated users to access customer portal - remove staff redirect\r\n    // This prevents any redirects from customer portal to staff dashboard\r\n    return NextResponse.next();\r\n  }\r\n\r\n  return NextResponse.next();\r\n}\r\n\r\nexport const config = {\r\n  matcher: [\r\n    /*\r\n     * Match all request paths except for the ones starting with:\r\n     * - api (API routes)\r\n     * - _next/static (static files)\r\n     * - _next/image (image optimization files)\r\n     * - favicon.ico (favicon file)\r\n     * - images (public images)\r\n     */\r\n    '/((?!api|_next/static|_next/image|favicon.ico|images).*)',\r\n  ],\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAGO,SAAS,WAAW,OAAoB;IAC7C,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;IAEjC,+BAA+B;IAC/B,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC;IACtC,MAAM,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC;IAErC,+BAA+B;IAC/B,IAAI,OAAO;IAEX,4DAA4D;IAC5D,IAAI,UAAU;QACZ,IAAI;YACF,OAAO,KAAK,KAAK,CAAC,SAAS,KAAK;QAClC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,+CAA+C;IAC/C,IAAI,IAAI,QAAQ,CAAC,UAAU,CAAC,sBAAsB,IAAI,QAAQ,CAAC,UAAU,CAAC,WAAW;QACnF,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,gCAAgC;IAChC,IAAI,IAAI,QAAQ,KAAK,KAAK;QACxB,IAAI,QAAQ,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,QAAQ,CAAC,aAAa;YACzD,IAAI,QAAQ,GAAG;YACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B,OAAO,IAAI,WAAW;YACpB,IAAI,QAAQ,GAAG;YACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B,OAAO;YACL,IAAI,QAAQ,GAAG;YACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;IACF;IAEA,yBAAyB;IACzB,IAAI,IAAI,QAAQ,CAAC,UAAU,CAAC,cAAc;QAExC,IAAG,CAAC,KAAK,KAAK,CAAC,QAAQ,CAAC,aAAY;YAClC,IAAI,QAAQ,GAAG;YACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;QAEA,kDAAkD;QAClD,IAAI,CAAC,aAAa,CAAC,MAAM;YACvB,IAAI,QAAQ,GAAG;YACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;QAEA,kFAAkF;QAClF,sEAAsE;QACtE,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;;KAOC,GACD;KACD;AACH"}}]}