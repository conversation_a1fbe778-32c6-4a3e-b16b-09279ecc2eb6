(()=>{var e={};e.id=748,e.ids=[748],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4220:(e,t,r)=>{Promise.resolve().then(r.bind(r,20844))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20844:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>_});var a=r(60687),i=r(43210),s=r(16189),n=r(94391),o=r(63213);let l=(0,i.forwardRef)(({label:e,error:t,helperText:r,required:i=!1,className:s="",containerClassName:n="",id:o,...l},c)=>{let d=o||`input-${Math.random().toString(36).substr(2,9)}`,p=`
    w-full px-3 py-2 border rounded-md shadow-sm 
    focus:outline-none focus:ring-2 focus:ring-offset-2 
    disabled:opacity-50 disabled:cursor-not-allowed
    dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600
    transition-colors duration-200
  `,u=t?`${p} border-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-600`:`${p} border-gray-300 focus:border-primary focus:ring-primary dark:focus:border-primary dark:focus:ring-primary`;return(0,a.jsxs)("div",{className:`space-y-1 ${n}`,children:[e&&(0,a.jsxs)("label",{htmlFor:d,className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:[e,i&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,a.jsx)("input",{ref:c,id:d,className:`${u} ${s}`,...l}),t&&(0,a.jsxs)("p",{className:"text-sm text-red-600 dark:text-red-400 flex items-center",children:[(0,a.jsx)("i",{className:"ri-error-warning-line mr-1"}),t]}),r&&!t&&(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:r})]})});l.displayName="TextInput";var c=r(99798),d=r(25890),p=r(78637),u=r(55457),m=r(90678),g=r(36212);r(81515);var x=r(13128);let _=()=>{let e=(0,s.useRouter)(),t=(0,s.useSearchParams)(),{isAuthenticated:r,loading:_}=(0,o.A)(),h=new g.ef,y=t.get("license_category_id"),f=t.get("application_id"),[b,v]=(0,i.useState)(!0),[j,w]=(0,i.useState)(null),[k,N]=(0,i.useState)({name:"",business_registration_number:"",tpin:"",website:"",email:"",phone:"",fax:"",level_of_insurance_cover:"",date_incorporation:"",place_incorporation:""}),[P,q]=(0,i.useState)({}),[C,$]=(0,i.useState)(!1),[S,A]=(0,i.useState)(null),[E,M]=(0,i.useState)(!1),[I,L]=(0,i.useState)(null),[B,D]=(0,i.useState)(null),{handleNext:T,handlePrevious:F,nextStep:R}=(0,d.f)({currentStepRoute:"applicant-info",licenseCategoryId:y,applicationId:f}),Y=(e,t)=>{N(r=>({...r,[e]:t})),S&&A(null),P[e]&&q(t=>{let r={...t};return delete r[e],r})},z=async(e=!1)=>{$(!0),q({});try{let r=(0,m.oQ)(k,"applicantInfo");if(!r.isValid)return q(r.errors||{}),$(!1),!1;let a=f;if(a){let e=await p.k.getApplication(a);if(e.applicant_id){let t={name:k.name,business_registration_number:k.business_registration_number,tpin:k.tpin,website:k.website,email:k.email,phone:k.phone,fax:k.fax,level_of_insurance_cover:k.level_of_insurance_cover,date_incorporation:k.date_incorporation,place_incorporation:k.place_incorporation};await u.W.updateApplicant(e.applicant_id,t)}}else{let e={name:k.name,business_registration_number:k.business_registration_number,tpin:k.tpin,website:k.website,email:k.email,phone:k.phone,fax:k.fax,level_of_insurance_cover:k.level_of_insurance_cover,date_incorporation:k.date_incorporation,place_incorporation:k.place_incorporation},t=await u.W.createApplicant(e),r={application_number:`APP-${Date.now()}-${Math.random().toString(36).substring(2,11).toUpperCase()}`,license_category_id:y,applicant_id:t.applicant_id,status:"draft"};a=(await p.k.createApplication(r)).application_id,L(a),M(!0)}try{let e=a||I;e&&await p.k.updateApplication(e,{current_step:2,progress_percentage:18})}catch(e){}A("Applicant information saved successfully!"),q({}),setTimeout(()=>{A(null)},5e3);let i=a||I;if(i&&!f){let e=new URLSearchParams(t.toString());e.set("application_id",i);let r=`/customer/applications/apply/applicant-info?${e.toString()}`;window.history.replaceState({},"",r)}return e&&U(i),!0}catch(e){return q({save:"Failed to save application. Please try again."}),!1}finally{$(!1)}},O=async()=>{await z(!0)};(0,i.useEffect)(()=>{if(!_&&!r)return void e.push("/customer/auth/login")},[r,_,e]),(0,i.useEffect)(()=>{let e=async()=>{try{if(!y){w("License category ID is required"),v(!1);return}let e=await h.getLicenseCategory(y);if(await h.getLicenseType(e.license_type_id),!e){w("License category not found"),v(!1);return}if(!e.license_type_id){w("License category is missing license type information"),v(!1);return}if(f)try{let e=await p.k.getApplication(f);if(e.applicant_id)try{let t=await u.W.getApplicant(e.applicant_id),r={name:t.name||"",business_registration_number:t.business_registration_number||"",tpin:t.tpin||"",website:t.website||"",email:t.email||"",phone:t.phone||"",fax:t.fax||"",level_of_insurance_cover:t.level_of_insurance_cover||"",date_incorporation:t.date_incorporation||"",place_incorporation:t.place_incorporation||""};N(e=>({...e,...r}))}catch(e){e.response?.status===500?D("Unable to load existing applicant data due to a server issue. You can still edit the application, but the form will start empty."):D("Could not load existing applicant data. The form will start empty.")}}catch(e){}v(!1)}catch(t){let e="Failed to load application data";t.response?.status===404?e=`License category not found (ID: ${y}). Please go back to the applications page and select a valid license category.`:t.response?.status===401?e="You are not authorized to access this license category. Please log in again.":t.response?.status===500?e="Server error occurred. Please try again later or contact support.":t.message&&(e=`Error: ${t.message}`),w(e),v(!1)}};r&&!_&&e()},[y,f,r,_]);let U=t=>{let r=t||f||I;if(r){let t=new URLSearchParams;if(t.set("license_category_id",y),t.set("application_id",r),R){let r=`/customer/applications/apply/${R.route}?${t.toString()}`;e.push(r)}}};return _||b?(0,a.jsx)(n.A,{children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading application form..."})]})})}):j?(0,a.jsx)(n.A,{children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsx)("i",{className:"ri-error-warning-line text-4xl text-red-500"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Error Loading Application"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:j}),(0,a.jsxs)("button",{onClick:()=>e.push("/customer/applications"),className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:[(0,a.jsx)("i",{className:"ri-arrow-left-line mr-2"}),"Back to Applications"]})]})})}):(0,a.jsx)(n.A,{children:(0,a.jsxs)(x.A,{onNext:O,onPrevious:()=>{F()},onSave:async()=>{await z(!1)},showNextButton:!0,showPreviousButton:!0,showSaveButton:!0,nextButtonText:R?`Save & Continue to ${R.name}`:"Save & Continue",previousButtonText:"Back to Applications",saveButtonText:f?"Save Changes":"Create Application",nextButtonDisabled:!1,isSaving:C,children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:f?"Edit Applicant Information":"Applicant Information"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:f?"Update your applicant information below.":"Please provide your personal information. This will create your application record."}),f&&!B&&(0,a.jsx)("div",{className:"mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg",children:(0,a.jsx)("p",{className:"text-sm text-green-700 dark:text-green-300",children:"✅ Editing existing application. Your saved information has been loaded."})}),B&&(0,a.jsx)("div",{className:"mt-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-yellow-700 dark:text-yellow-300",children:["⚠️ ",B]})}),!f&&(0,a.jsx)("div",{className:"mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:[(0,a.jsx)("i",{className:"ri-information-line mr-1"}),"Your application will be created when you save this step."]})}),E&&(0,a.jsx)("div",{className:"mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-green-700 dark:text-green-300",children:[(0,a.jsx)("i",{className:"ri-check-line mr-1"}),"Application created: ",I?.slice(0,8),"..."]})})]}),(0,a.jsx)(c.bc,{successMessage:S,errorMessage:P.save,validationErrors:Object.fromEntries(Object.entries(P).filter(([e])=>"save"!==e))}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)("div",{className:"md:col-span-2",children:(0,a.jsx)("h4",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Business Information"})}),(0,a.jsx)("div",{className:"md:col-span-2",children:(0,a.jsx)(l,{label:"Business/Organization Name",value:k.name||"",onChange:e=>Y("name",e.target.value),placeholder:"Enter the full legal name of your business or organization",required:!0,error:P.name})}),(0,a.jsx)(l,{label:"Business Registration Number",value:k.business_registration_number||"",onChange:e=>Y("business_registration_number",e.target.value),placeholder:"e.g., BN123456789",required:!0,error:P.business_registration_number}),(0,a.jsx)(l,{label:"TPIN (Tax Payer Identification Number)",value:k.tpin||"",onChange:e=>Y("tpin",e.target.value),placeholder:"e.g., 12-345-678-9",required:!0,error:P.tpin}),(0,a.jsx)(l,{label:"Website",type:"url",value:k.website||"",onChange:e=>Y("website",e.target.value),placeholder:"https://www.example.com",error:P.website}),(0,a.jsx)(l,{label:"Date of Incorporation",type:"date",value:k.date_incorporation||"",onChange:e=>Y("date_incorporation",e.target.value),required:!0,error:P.date_incorporation}),(0,a.jsx)("div",{className:"md:col-span-2",children:(0,a.jsx)(l,{label:"Place of Incorporation",value:k.place_incorporation||"",onChange:e=>Y("place_incorporation",e.target.value),placeholder:"e.g., Blantyre, Malawi",required:!0,error:P.place_incorporation})}),(0,a.jsx)("div",{className:"md:col-span-2",children:(0,a.jsx)("h4",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 mt-6",children:"Contact Information"})}),(0,a.jsx)(l,{label:"Email Address",type:"email",value:k.email||"",onChange:e=>Y("email",e.target.value),placeholder:"<EMAIL>",required:!0,error:P.email}),(0,a.jsx)(l,{label:"Phone Number",value:k.phone||"",onChange:e=>Y("phone",e.target.value),placeholder:"+265 1 234 567",required:!0,error:P.phone}),(0,a.jsx)(l,{label:"Fax Number",value:k.fax||"",onChange:e=>Y("fax",e.target.value),placeholder:"+265 1 234 568",error:P.fax}),(0,a.jsx)(l,{label:"Level of Insurance Cover",value:k.level_of_insurance_cover||"",onChange:e=>Y("level_of_insurance_cover",e.target.value),placeholder:"e.g., $1,000,000 USD",error:P.level_of_insurance_cover})]}),P.save&&(0,a.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"ri-error-warning-line text-red-600 dark:text-red-400 text-lg mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-red-800 dark:text-red-200",children:"Error Saving Application"}),(0,a.jsx)("p",{className:"text-red-700 dark:text-red-300 text-sm mt-1",children:P.save})]})]})}),E&&!f&&(0,a.jsx)("div",{className:"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"ri-check-circle-line text-green-600 dark:text-green-400 text-lg mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-green-800 dark:text-green-200",children:"Application Created Successfully!"}),(0,a.jsx)("p",{className:"text-green-700 dark:text-green-300 text-sm mt-1",children:"Your application has been created. You can now continue to the next step."})]})]})})]})})}},21820:e=>{"use strict";e.exports=require("os")},23308:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\applications\\\\apply\\\\applicant-info\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\applicant-info\\page.tsx","default")},25890:(e,t,r)=>{"use strict";r.d(t,{f:()=>o});var a=r(43210),i=r(16189),s=r(25011),n=r(36212);let o=({currentStepRoute:e,licenseCategoryId:t,applicationId:r})=>{let o=(0,i.useRouter)(),[l,c]=(0,a.useState)(!0),[d,p]=(0,a.useState)(null),[u,m]=(0,a.useState)(null),[g,x]=(0,a.useState)([]),_=(0,a.useMemo)(()=>new n.ef,[]),h=(0,a.useCallback)(async()=>{if(!t){p("License category ID is required"),c(!1);return}try{c(!0),p(null);let e=await _.getLicenseCategory(t);if(!e?.license_type_id)throw Error("License category does not have a license type ID");await new Promise(e=>setTimeout(e,500));let r=await _.getLicenseType(e.license_type_id);if(!r)throw Error("License type not found");let a=r.code||r.license_type_id;m(a);let i=[];i=(0,s.nF)(a)?(0,s.PY)(a):(0,s.QE)(a).steps,x(i)}catch(e){p(e.message||"Failed to load navigation configuration"),x((0,s.QE)("default").steps),m("default")}finally{c(!1)}},[t,_]);(0,a.useEffect)(()=>{h()},[h]);let y=(0,a.useMemo)(()=>g.findIndex(t=>t.route===e),[g,e]),f=(0,a.useMemo)(()=>g[y]||null,[g,y]),b=(0,a.useMemo)(()=>y>=0&&y<g.length-1?g[y+1]:null,[g,y]),v=(0,a.useMemo)(()=>y>0?g[y-1]:null,[g,y]),j=g.length,w=0===y,k=y===g.length-1,N=!k&&null!==b,P=!w&&null!==v,q=(0,a.useCallback)(e=>{let a=new URLSearchParams;return a.set("license_category_id",t||""),r&&a.set("application_id",r),`/customer/applications/apply/${e}?${a.toString()}`},[t,r]),C=(0,a.useCallback)(e=>{let t=q(e);o.push(t)},[q,o]);return{handleNext:(0,a.useCallback)(async e=>{if(N&&b){if(e)try{if(!await e())return}catch(e){e.message?.includes("timeout")||e.message?.includes("Bad Request")||e.message?.includes("Too many requests");return}C(b.route)}},[N,b,C]),handlePrevious:(0,a.useCallback)(()=>{P&&v&&C(v.route)},[P,v,C]),navigateToStep:C,currentStep:f,nextStep:b,previousStep:v,currentStepIndex:y,totalSteps:j,loading:l,error:d,licenseTypeCode:u,isFirstStep:w,isLastStep:k,canNavigateNext:N,canNavigatePrevious:P}}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},45841:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>c});var a=r(65239),i=r(48088),s=r(88170),n=r.n(s),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["customer",{children:["applications",{children:["apply",{children:["applicant-info",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,23308)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\applicant-info\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,68736)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\applicant-info\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/customer/applications/apply/applicant-info/page",pathname:"/customer/applications/apply/applicant-info",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},46076:(e,t,r)=>{Promise.resolve().then(r.bind(r,23308))},55457:(e,t,r)=>{"use strict";r.d(t,{W:()=>s});var a=r(51278),i=r(12234);let s={async createApplicant(e){try{let t=await i.uE.post("/applicants",e);return(0,a.zp)(t)}catch(e){throw e}},async getApplicant(e){try{let t=await i.uE.get(`/applicants/${e}`);return(0,a.zp)(t)}catch(e){throw e}},async updateApplicant(e,t){try{let r=await i.uE.put(`/applicants/${e}`,t);return(0,a.zp)(r)}catch(e){throw e}},async getApplicantsByUser(){try{let e=await i.uE.get("/applicants/by-user");return(0,a.zp)(e)}catch(e){throw e}},async deleteApplicant(e){try{let t=await i.uE.delete(`/applicants/${e}`);return(0,a.zp)(t)}catch(e){throw e}}}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},90678:(e,t,r)=>{"use strict";r.d(t,{oQ:()=>i});let a={email:/^[^\s@]+@[^\s@]+\.[^\s@]+$/,phone:/^(\+265|0)[0-9]{8,9}$/,percentage:/^(100|[1-9]?[0-9])$/};a.email,a.phone,a.percentage;let i=(e,t)=>{let r={};switch(t){case"applicantInfo":["name","business_registration_number","tpin","website","email","phone","date_incorporation","place_incorporation"].forEach(t=>{e[t]&&("string"!=typeof e[t]||""!==e[t].trim())||(r[t]=`${t.replace(/_/g," ")} is required`)}),e.email&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.email)&&(r.email="Please enter a valid email address"),e.phone&&!/^[+]?[\d\s\-()]+$/.test(e.phone)&&(r.phone="Please enter a valid phone number"),e.fax&&""!==e.fax.trim()&&!/^[+]?[\d\s\-()]+$/.test(e.fax)&&(r.fax="Please enter a valid fax number"),e.date_incorporation&&!/^\d{4}-\d{2}-\d{2}$/.test(e.date_incorporation)&&(r.date_incorporation="Please enter a valid date (YYYY-MM-DD)");break;case"companyProfile":["company_name","business_registration_number","tax_number","company_type","incorporation_date","incorporation_place","company_email","company_phone","company_address","company_city","company_district","number_of_employees","annual_revenue","business_description"].forEach(t=>{e[t]&&("string"!=typeof e[t]||""!==e[t].trim())||(r[t]=`${t.replace(/_/g," ")} is required`)}),e.company_email&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.company_email)&&(r.company_email="Please enter a valid email address");break;case"businessInfo":["business_model","operational_structure","target_market","competitive_advantage","facilities_description","equipment_description","operational_areas","service_delivery_model","quality_assurance","customer_support"].forEach(t=>{e[t]&&("string"!=typeof e[t]||""!==e[t].trim())||(r[t]=`${t.replace(/_/g," ")} is required`)});break;case"serviceScope":["services_offered","geographic_coverage","service_categories","target_customers","service_capacity"].forEach(t=>{e[t]&&("string"!=typeof e[t]||""!==e[t].trim())||(r[t]=`${t.replace(/_/g," ")} is required`)});break;case"businessPlan":["executive_summary","market_analysis","financial_projections","revenue_model","investment_requirements","implementation_timeline","risk_analysis","success_metrics"].forEach(t=>{e[t]&&("string"!=typeof e[t]||""!==e[t].trim())||(r[t]=`${t.replace(/_/g," ")} is required`)});break;case"legalHistory":e.compliance_record&&""!==e.compliance_record.trim()||(r.compliance_record="Compliance record is required"),e.declaration_accepted||(r.declaration_accepted="You must accept the declaration to proceed"),e.criminal_history&&(!e.criminal_details||""===e.criminal_details.trim())&&(r.criminal_details="Please provide details of your criminal history"),e.bankruptcy_history&&(!e.bankruptcy_details||""===e.bankruptcy_details.trim())&&(r.bankruptcy_details="Please provide details of your bankruptcy history"),e.regulatory_actions&&(!e.regulatory_details||""===e.regulatory_details.trim())&&(r.regulatory_details="Please provide details of regulatory actions"),e.litigation_history&&(!e.litigation_details||""===e.litigation_details.trim())&&(r.litigation_details="Please provide details of litigation history");break;case"address":["address_line_1","city","country"].forEach(t=>{e[t]&&("string"!=typeof e[t]||""!==e[t].trim())||(r[t]=`${t.replace(/_/g," ")} is required`)});break;case"contactInfo":["primary_contact_first_name","primary_contact_last_name","primary_contact_designation","primary_contact_email","primary_contact_phone"].forEach(t=>{e[t]&&("string"!=typeof e[t]||""!==e[t].trim())||(r[t]=`${t.replace(/_/g," ")} is required`)}),e.primary_contact_email&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.primary_contact_email)&&(r.primary_contact_email="Please enter a valid email address"),e.secondary_contact_email&&""!==e.secondary_contact_email.trim()&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.secondary_contact_email)&&(r.secondary_contact_email="Please enter a valid email address"),e.primary_contact_phone&&!/^[+]?[\d\s\-()]+$/.test(e.primary_contact_phone)&&(r.primary_contact_phone="Please enter a valid phone number"),e.secondary_contact_phone&&""!==e.secondary_contact_phone.trim()&&!/^[+]?[\d\s\-()]+$/.test(e.secondary_contact_phone)&&(r.secondary_contact_phone="Please enter a valid phone number")}return{isValid:0===Object.keys(r).length,errors:r}}},94735:e=>{"use strict";e.exports=require("events")},99798:(e,t,r)=>{"use strict";r.d(t,{bc:()=>i});var a=r(60687);r(43210);let i=({successMessage:e,errorMessage:t,validationErrors:r={},className:i=""})=>(t||Object.keys(r).length,(0,a.jsxs)("div",{className:i,children:[e&&(0,a.jsx)("div",{className:"mb-6 bg-green-50 border border-green-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"ri-check-circle-line text-green-500 mr-2"}),(0,a.jsx)("p",{className:"text-green-700",children:e})]})}),t&&(0,a.jsx)("div",{className:"mb-6 bg-red-50 border border-red-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"ri-error-warning-line text-red-500 mr-2"}),(0,a.jsx)("p",{className:"text-red-700",children:t})]})}),Object.keys(r).length>0&&!t&&(0,a.jsx)("div",{className:"mb-6 bg-red-50 border border-red-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("i",{className:"ri-error-warning-line text-red-500 mr-2 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-red-900 mb-2",children:"Please fix the following issues:"}),(0,a.jsx)("ul",{className:"text-sm text-red-700 space-y-1",children:Object.entries(r).map(([e,t])=>(0,a.jsxs)("li",{children:["• ",t]},e))})]})]})})]}))}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,7498,1658,5814,7563,6893,140],()=>r(45841));module.exports=a})();