(()=>{var e={};e.id=748,e.ids=[748],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4220:(e,a,t)=>{Promise.resolve().then(t.bind(t,20844))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20844:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>_});var r=t(60687),i=t(43210),s=t(16189),n=t(94391),o=t(63213);let l=(0,i.forwardRef)(({label:e,error:a,helperText:t,required:i=!1,className:s="",containerClassName:n="",id:o,...l},c)=>{let p=o||`input-${Math.random().toString(36).substr(2,9)}`,d=`
    w-full px-3 py-2 border rounded-md shadow-sm 
    focus:outline-none focus:ring-2 focus:ring-offset-2 
    disabled:opacity-50 disabled:cursor-not-allowed
    bg-white text-gray-900 placeholder:text-gray-500
    dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600 dark:placeholder:text-gray-400
    transition-colors duration-200
  `,u=a?`${d} border-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-600 dark:focus:border-red-500 dark:focus:ring-red-500`:`${d} border-gray-300 focus:border-primary focus:ring-primary dark:border-gray-600 dark:focus:border-primary dark:focus:ring-primary`;return(0,r.jsxs)("div",{className:`space-y-1 ${n}`,children:[e&&(0,r.jsxs)("label",{htmlFor:p,className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:[e,i&&(0,r.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,r.jsx)("input",{ref:c,id:p,className:`${u} ${s}`,...l}),a&&(0,r.jsxs)("p",{className:"text-sm text-red-600 dark:text-red-400 flex items-center",children:[(0,r.jsx)("i",{className:"ri-error-warning-line mr-1"}),a]}),t&&!a&&(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:t})]})});l.displayName="TextInput";var c=t(78637),p=t(55457),d=t(90678),u=t(36212);t(81515);var m=t(13128);let _=()=>{let e=(0,s.useRouter)(),a=(0,s.useSearchParams)(),{isAuthenticated:t,loading:_}=(0,o.A)(),g=new u.ef,h=a.get("license_category_id"),x=a.get("application_id"),[f,y]=(0,i.useState)(!0),[b,v]=(0,i.useState)(null),[w,j]=(0,i.useState)({name:"",business_registration_number:"",tpin:"",website:"",email:"",phone:"",fax:"",level_of_insurance_cover:"",date_incorporation:"",place_incorporation:""}),[k,P]=(0,i.useState)({}),[N,q]=(0,i.useState)(!1),[A,$]=(0,i.useState)(!1),[C,E]=(0,i.useState)(null),[S,M]=(0,i.useState)(null),I=(e,a)=>{j(t=>({...t,[e]:a})),k[e]&&P(a=>{let t={...a};return delete t[e],t})},B=async(e=!1)=>{q(!0),P({});try{let a=(0,d.oQ)(w,"applicantInfo");if(!a.isValid)return P(a.errors||{}),q(!1),!1;let t=x;if(t){let e=await c.k.getApplication(t);if(e.applicant_id){let a={name:w.name,business_registration_number:w.business_registration_number,tpin:w.tpin,website:w.website,email:w.email,phone:w.phone,...w.fax&&w.fax.trim().length>=10?{fax:w.fax.trim()}:{},...w.level_of_insurance_cover&&w.level_of_insurance_cover.trim()?{level_of_insurance_cover:w.level_of_insurance_cover.trim()}:{},date_incorporation:w.date_incorporation,place_incorporation:w.place_incorporation};await p.W.updateApplicant(e.applicant_id,a)}}else{let e={name:w.name,business_registration_number:w.business_registration_number,tpin:w.tpin,website:w.website,email:w.email,phone:w.phone,...w.fax&&w.fax.trim().length>=10?{fax:w.fax.trim()}:{},...w.level_of_insurance_cover&&w.level_of_insurance_cover.trim()?{level_of_insurance_cover:w.level_of_insurance_cover.trim()}:{},date_incorporation:w.date_incorporation,place_incorporation:w.place_incorporation},a=await p.W.createApplicant(e),r={application_number:`APP-${Date.now()}-${Math.random().toString(36).substring(2,11).toUpperCase()}`,license_category_id:h,applicant_id:a.applicant_id,status:"draft"};t=(await c.k.createApplication(r)).application_id,E(t),$(!0)}try{let e=t||C;e&&await c.k.updateApplication(e,{current_step:2,progress_percentage:18})}catch(e){}if(e){let e=t||C;F(e)}return!0}catch(e){if(e.response?.status===400&&e.response?.data?.message){let a=e.response.data.message;a.includes("fax")?P({fax:"Fax number must be at least 10 characters long and contain only numbers, spaces, dashes, and parentheses.",save:"Please fix the validation errors above."}):a.includes("phone")?P({phone:"Phone number must be between 10-20 characters and contain only numbers, spaces, dashes, and parentheses.",save:"Please fix the validation errors above."}):a.includes("email")?P({email:"Please enter a valid email address.",save:"Please fix the validation errors above."}):P({save:a})}else P({save:"Failed to save application. Please try again."});return!1}finally{q(!1)}},D=async()=>{await B(!0)};(0,i.useEffect)(()=>{if(!_&&!t)return void e.push("/customer/auth/login")},[t,_,e]),(0,i.useEffect)(()=>{let e=async()=>{try{if(!h){v("License category ID is required"),y(!1);return}let e=await g.getLicenseCategory(h);if(await g.getLicenseType(e.license_type_id),!e){v("License category not found"),y(!1);return}if(!e.license_type_id){v("License category is missing license type information"),y(!1);return}if(x)try{let e=await c.k.getApplication(x);if(e.applicant_id)try{let a=await p.W.getApplicant(e.applicant_id),t={name:a.name||"",business_registration_number:a.business_registration_number||"",tpin:a.tpin||"",website:a.website||"",email:a.email||"",phone:a.phone||"",fax:a.fax||"",level_of_insurance_cover:a.level_of_insurance_cover||"",date_incorporation:a.date_incorporation||"",place_incorporation:a.place_incorporation||""};j(e=>({...e,...t}))}catch(e){e.response?.status===500?M("Unable to load existing applicant data due to a server issue. You can still edit the application, but the form will start empty."):M("Could not load existing applicant data. The form will start empty.")}}catch(e){}y(!1)}catch(a){let e="Failed to load application data";a.response?.status===404?e=`License category not found (ID: ${h}). Please go back to the applications page and select a valid license category.`:a.response?.status===401?e="You are not authorized to access this license category. Please log in again.":a.response?.status===500?e="Server error occurred. Please try again later or contact support.":a.message&&(e=`Error: ${a.message}`),v(e),y(!1)}};t&&!_&&e()},[h,x,t,_]);let F=a=>{let t=new URLSearchParams;t.set("license_category_id",h);let r=a||x||C;r&&t.set("application_id",r);let i=`/customer/applications/apply/address-info?${t.toString()}`;e.push(i)};return _||f?(0,r.jsx)(n.A,{children:(0,r.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading application form..."})]})})}):b?(0,r.jsx)(n.A,{children:(0,r.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"mb-4",children:(0,r.jsx)("i",{className:"ri-error-warning-line text-4xl text-red-500"})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Error Loading Application"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:b}),(0,r.jsxs)("button",{onClick:()=>e.push("/customer/applications"),className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:[(0,r.jsx)("i",{className:"ri-arrow-left-line mr-2"}),"Back to Applications"]})]})})}):(0,r.jsx)(n.A,{children:(0,r.jsxs)(m.A,{onNext:D,onPrevious:()=>{e.push("/customer/applications")},onSave:async()=>{await B(!1)},showNextButton:!0,showPreviousButton:!0,showSaveButton:!0,nextButtonText:"Save & Continue to Address Information",previousButtonText:"Back to Applications",saveButtonText:x?"Save Changes":"Create Application",nextButtonDisabled:!1,isSaving:N,children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:x?"Edit Applicant Information":"Applicant Information"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:x?"Update your applicant information below.":"Please provide your personal information. This will create your application record."}),x&&!S&&(0,r.jsx)("div",{className:"mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg",children:(0,r.jsx)("p",{className:"text-sm text-green-700 dark:text-green-300",children:"✅ Editing existing application. Your saved information has been loaded."})}),S&&(0,r.jsx)("div",{className:"mt-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-yellow-700 dark:text-yellow-300",children:["⚠️ ",S]})}),!x&&(0,r.jsx)("div",{className:"mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:[(0,r.jsx)("i",{className:"ri-information-line mr-1"}),"Your application will be created when you save this step."]})}),A&&(0,r.jsx)("div",{className:"mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-green-700 dark:text-green-300",children:[(0,r.jsx)("i",{className:"ri-check-line mr-1"}),"Application created: ",C?.slice(0,8),"..."]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsx)("div",{className:"md:col-span-2",children:(0,r.jsx)("h4",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Business Information"})}),(0,r.jsx)("div",{className:"md:col-span-2",children:(0,r.jsx)(l,{label:"Business/Organization Name",value:w.name||"",onChange:e=>I("name",e.target.value),placeholder:"Enter the full legal name of your business or organization",required:!0,error:k.name})}),(0,r.jsx)(l,{label:"Business Registration Number",value:w.business_registration_number||"",onChange:e=>I("business_registration_number",e.target.value),placeholder:"e.g., BN123456789",required:!0,error:k.business_registration_number}),(0,r.jsx)(l,{label:"TPIN (Tax Payer Identification Number)",value:w.tpin||"",onChange:e=>I("tpin",e.target.value),placeholder:"e.g., 12-345-678-9",required:!0,error:k.tpin}),(0,r.jsx)(l,{label:"Website",type:"url",value:w.website||"",onChange:e=>I("website",e.target.value),placeholder:"https://www.example.com",error:k.website}),(0,r.jsx)(l,{label:"Date of Incorporation",type:"date",value:w.date_incorporation||"",onChange:e=>I("date_incorporation",e.target.value),required:!0,error:k.date_incorporation}),(0,r.jsx)("div",{className:"md:col-span-2",children:(0,r.jsx)(l,{label:"Place of Incorporation",value:w.place_incorporation||"",onChange:e=>I("place_incorporation",e.target.value),placeholder:"e.g., Blantyre, Malawi",required:!0,error:k.place_incorporation})}),(0,r.jsx)("div",{className:"md:col-span-2",children:(0,r.jsx)("h4",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 mt-6",children:"Contact Information"})}),(0,r.jsx)(l,{label:"Email Address",type:"email",value:w.email||"",onChange:e=>I("email",e.target.value),placeholder:"<EMAIL>",required:!0,error:k.email}),(0,r.jsx)(l,{label:"Phone Number",value:w.phone||"",onChange:e=>I("phone",e.target.value),placeholder:"+265 1 234 567",required:!0,error:k.phone}),(0,r.jsx)(l,{label:"Fax Number",value:w.fax||"",onChange:e=>I("fax",e.target.value),placeholder:"+265 1 234 568",error:k.fax}),(0,r.jsx)(l,{label:"Level of Insurance Cover",value:w.level_of_insurance_cover||"",onChange:e=>I("level_of_insurance_cover",e.target.value),placeholder:"e.g., $1,000,000 USD",error:k.level_of_insurance_cover})]}),k.save&&(0,r.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"ri-error-warning-line text-red-600 dark:text-red-400 text-lg mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-red-800 dark:text-red-200",children:"Error Saving Application"}),(0,r.jsx)("p",{className:"text-red-700 dark:text-red-300 text-sm mt-1",children:k.save})]})]})}),A&&!x&&(0,r.jsx)("div",{className:"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"ri-check-circle-line text-green-600 dark:text-green-400 text-lg mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-green-800 dark:text-green-200",children:"Application Created Successfully!"}),(0,r.jsx)("p",{className:"text-green-700 dark:text-green-300 text-sm mt-1",children:"Your application has been created. You can now continue to the next step."})]})]})})]})})}},21820:e=>{"use strict";e.exports=require("os")},23308:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\applications\\\\apply\\\\applicant-info\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\applicant-info\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},45841:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>n.a,__next_app__:()=>d,pages:()=>p,routeModule:()=>u,tree:()=>c});var r=t(65239),i=t(48088),s=t(88170),n=t.n(s),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(a,l);let c={children:["",{children:["customer",{children:["applications",{children:["apply",{children:["applicant-info",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,23308)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\applicant-info\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,68736)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,p=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\applicant-info\\page.tsx"],d={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/customer/applications/apply/applicant-info/page",pathname:"/customer/applications/apply/applicant-info",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},46076:(e,a,t)=>{Promise.resolve().then(t.bind(t,23308))},55457:(e,a,t)=>{"use strict";t.d(a,{W:()=>s});var r=t(51278),i=t(72901);let s={async createApplicant(e){try{let a=await i.uE.post("/applicants",e),t=(0,r.zp)(a);if(!t)throw Error("Invalid response format from applicant creation");return t}catch(e){throw e}},async getApplicant(e){try{let a=await i.uE.get(`/applicants/${e}`);return(0,r.zp)(a)}catch(e){throw e}},async updateApplicant(e,a){try{let t=await i.uE.put(`/applicants/${e}`,a);return(0,r.zp)(t)}catch(e){throw e}},async getApplicantsByUser(){try{let e=await i.uE.get("/applicants/by-user");return(0,r.zp)(e)}catch(e){throw e}},async deleteApplicant(e){try{let a=await i.uE.delete(`/applicants/${e}`);return(0,r.zp)(a)}catch(e){throw e}}}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},90678:(e,a,t)=>{"use strict";t.d(a,{oQ:()=>i});let r={email:/^[^\s@]+@[^\s@]+\.[^\s@]+$/,phone:/^(\+265|0)[0-9]{8,9}$/,percentage:/^(100|[1-9]?[0-9])$/};r.email,r.phone,r.percentage;let i=(e,a)=>{let t={};switch(a){case"applicantInfo":["name","business_registration_number","tpin","email","phone","date_incorporation","place_incorporation"].forEach(a=>{e[a]&&("string"!=typeof e[a]||""!==e[a].trim())||(t[a]=`${a.replace(/_/g," ")} is required`)}),e.email&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.email)&&(t.email="Please enter a valid email address"),e.website&&""!==e.website.trim()&&!/^https?:\/\/.+/.test(e.website)&&(t.website="Please enter a valid website URL (starting with http:// or https://)"),e.phone&&(/^[+]?[\d\s\-()]+$/.test(e.phone)?e.phone.trim().length<10?t.phone="Phone number must be at least 10 characters long":e.phone.trim().length>20&&(t.phone="Phone number must be no more than 20 characters long"):t.phone="Phone number can only contain numbers, spaces, dashes, and parentheses"),e.fax&&""!==e.fax.trim()&&(/^[+]?[\d\s\-()]+$/.test(e.fax)?e.fax.trim().length<10?t.fax="Fax number must be at least 10 characters long":e.fax.trim().length>20&&(t.fax="Fax number must be no more than 20 characters long"):t.fax="Fax number can only contain numbers, spaces, dashes, and parentheses"),e.level_of_insurance_cover&&""!==e.level_of_insurance_cover.trim()&&e.level_of_insurance_cover.trim().length<3&&(t.level_of_insurance_cover="Please provide a valid insurance cover amount"),e.date_incorporation&&!/^\d{4}-\d{2}-\d{2}$/.test(e.date_incorporation)&&(t.date_incorporation="Please enter a valid date (YYYY-MM-DD)");break;case"companyProfile":["company_name","business_registration_number","tax_number","company_type","incorporation_date","incorporation_place","company_email","company_phone","company_address","company_city","company_district","number_of_employees","annual_revenue","business_description"].forEach(a=>{e[a]&&("string"!=typeof e[a]||""!==e[a].trim())||(t[a]=`${a.replace(/_/g," ")} is required`)}),e.company_email&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.company_email)&&(t.company_email="Please enter a valid email address");break;case"businessInfo":["business_model","operational_structure","target_market","competitive_advantage","facilities_description","equipment_description","operational_areas","service_delivery_model","quality_assurance","customer_support"].forEach(a=>{e[a]&&("string"!=typeof e[a]||""!==e[a].trim())||(t[a]=`${a.replace(/_/g," ")} is required`)});break;case"serviceScope":["services_offered","geographic_coverage","service_categories","target_customers","service_capacity"].forEach(a=>{e[a]&&("string"!=typeof e[a]||""!==e[a].trim())||(t[a]=`${a.replace(/_/g," ")} is required`)});break;case"businessPlan":["executive_summary","market_analysis","financial_projections","revenue_model","investment_requirements","implementation_timeline","risk_analysis","success_metrics"].forEach(a=>{e[a]&&("string"!=typeof e[a]||""!==e[a].trim())||(t[a]=`${a.replace(/_/g," ")} is required`)});break;case"legalHistory":e.compliance_record&&""!==e.compliance_record.trim()||(t.compliance_record="Compliance record is required"),e.declaration_accepted||(t.declaration_accepted="You must accept the declaration to proceed"),e.criminal_history&&(!e.criminal_details||""===e.criminal_details.trim())&&(t.criminal_details="Please provide details of your criminal history"),e.bankruptcy_history&&(!e.bankruptcy_details||""===e.bankruptcy_details.trim())&&(t.bankruptcy_details="Please provide details of your bankruptcy history"),e.regulatory_actions&&(!e.regulatory_details||""===e.regulatory_details.trim())&&(t.regulatory_details="Please provide details of regulatory actions"),e.litigation_history&&(!e.litigation_details||""===e.litigation_details.trim())&&(t.litigation_details="Please provide details of litigation history");break;case"address":["address_line_1","city","country"].forEach(a=>{e[a]&&("string"!=typeof e[a]||""!==e[a].trim())||(t[a]=`${a.replace(/_/g," ")} is required`)});break;case"contactInfo":["primary_contact_first_name","primary_contact_last_name","primary_contact_designation","primary_contact_email","primary_contact_phone"].forEach(a=>{e[a]&&("string"!=typeof e[a]||""!==e[a].trim())||(t[a]=`${a.replace(/_/g," ")} is required`)}),e.primary_contact_email&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.primary_contact_email)&&(t.primary_contact_email="Please enter a valid email address"),e.secondary_contact_email&&""!==e.secondary_contact_email.trim()&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.secondary_contact_email)&&(t.secondary_contact_email="Please enter a valid email address"),e.primary_contact_phone&&!/^[+]?[\d\s\-()]+$/.test(e.primary_contact_phone)&&(t.primary_contact_phone="Please enter a valid phone number"),e.secondary_contact_phone&&""!==e.secondary_contact_phone.trim()&&!/^[+]?[\d\s\-()]+$/.test(e.secondary_contact_phone)&&(t.secondary_contact_phone="Please enter a valid phone number")}return{isValid:0===Object.keys(t).length,errors:t}}},94735:e=>{"use strict";e.exports=require("events")}};var a=require("../../../../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),r=a.X(0,[4447,7498,1658,5814,7563,6893,6212,140],()=>t(45841));module.exports=r})();