{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/LogoutButton.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { useRouter, usePathname } from 'next/navigation';\r\nimport { useAuth } from '../contexts/AuthContext';\r\n\r\ninterface LogoutButtonProps {\r\n  variant?: 'primary' | 'secondary' | 'text' | 'icon';\r\n  size?: 'sm' | 'md' | 'lg';\r\n  className?: string;\r\n  showConfirmation?: boolean;\r\n  redirectTo?: string;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nexport default function LogoutButton({\r\n  variant = 'primary',\r\n  size = 'md',\r\n  className = '',\r\n  showConfirmation = true,\r\n  redirectTo = '/auth/login',\r\n  children,\r\n}: LogoutButtonProps) {\r\n  const [isLoggingOut, setIsLoggingOut] = useState(false);\r\n  const [showConfirm, setShowConfirm] = useState(false);\r\n  const { logout, user } = useAuth();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  const handleLogout = async () => {\r\n    if (showConfirmation && !showConfirm) {\r\n      setShowConfirm(true);\r\n      return;\r\n    }\r\n\r\n    setIsLoggingOut(true);\r\n    \r\n    try {\r\n      // Call logout from context\r\n      logout();\r\n      \r\n      // Small delay to ensure cleanup is complete\r\n      await new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n      if(pathname.includes('customer')){\r\n        // Redirect to specified page\r\n        router.push('/customer/auth/login');\r\n      } else {\r\n        router.push('/auth/login');\r\n      }\r\n      \r\n\r\n      \r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n    } finally {\r\n      setIsLoggingOut(false);\r\n      setShowConfirm(false);\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    setShowConfirm(false);\r\n  };\r\n\r\n  // Base styles for different variants\r\n  const getVariantStyles = () => {\r\n    switch (variant) {\r\n      case 'primary':\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n      case 'secondary':\r\n        return 'bg-white hover:bg-gray-50 text-red-600 border border-red-600';\r\n      case 'text':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none';\r\n      case 'icon':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none p-2';\r\n      default:\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n    }\r\n  };\r\n\r\n  // Size styles\r\n  const getSizeStyles = () => {\r\n    switch (size) {\r\n      case 'sm':\r\n        return 'px-3 py-1.5 text-sm';\r\n      case 'md':\r\n        return 'px-4 py-2 text-base';\r\n      case 'lg':\r\n        return 'px-6 py-3 text-lg';\r\n      default:\r\n        return 'px-4 py-2 text-base';\r\n    }\r\n  };\r\n\r\n  const baseStyles = 'inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\r\n  \r\n  const buttonStyles = `${baseStyles} ${getVariantStyles()} ${variant !== 'icon' ? getSizeStyles() : ''} ${className}`;\r\n\r\n  // Confirmation dialog\r\n  if (showConfirm) {\r\n    return (\r\n      <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n        <div className=\"bg-white rounded-lg p-6 max-w-sm mx-4 shadow-xl\">\r\n          <div className=\"flex items-center mb-4\">\r\n            <div className=\"flex-shrink-0\">\r\n              <svg className=\"h-6 w-6 text-red-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z\" />\r\n              </svg>\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <h3 className=\"text-lg font-medium text-gray-900\">Confirm Logout</h3>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"mb-4\">\r\n            <p className=\"text-sm text-gray-500\">\r\n              Are you sure you want to logout{user?.first_name ? `, ${user.first_name}` : ''}? You will need to login again to access your account.\r\n            </p>\r\n          </div>\r\n          \r\n          <div className=\"flex space-x-3\">\r\n            <button\r\n              onClick={handleLogout}\r\n              disabled={isLoggingOut}\r\n              className=\"flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 disabled:opacity-50\"\r\n            >\r\n              {isLoggingOut ? 'Logging out...' : 'Yes, Logout'}\r\n            </button>\r\n            <button\r\n              onClick={handleCancel}\r\n              className=\"flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200\"\r\n            >\r\n              Cancel\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <button\r\n      onClick={handleLogout}\r\n      disabled={isLoggingOut}\r\n      className={buttonStyles}\r\n      title=\"Logout\"\r\n    >\r\n      {children || (\r\n        <>\r\n          {variant === 'icon' ? (\r\n            <svg className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n            </svg>\r\n          ) : (\r\n            <>\r\n              <svg className=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n              </svg>\r\n              {isLoggingOut ? 'Logging out...' : 'Logout'}\r\n            </>\r\n          )}\r\n        </>\r\n      )}\r\n    </button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAee,SAAS,aAAa,EACnC,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,mBAAmB,IAAI,EACvB,aAAa,aAAa,EAC1B,QAAQ,EACU;IAClB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe;QACnB,IAAI,oBAAoB,CAAC,aAAa;YACpC,eAAe;YACf;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,2BAA2B;YAC3B;YAEA,4CAA4C;YAC5C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,IAAG,SAAS,QAAQ,CAAC,aAAY;gBAC/B,6BAA6B;gBAC7B,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QAIF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,gBAAgB;YAChB,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,eAAe;IACjB;IAEA,qCAAqC;IACrC,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,cAAc;IACd,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa;IAEnB,MAAM,eAAe,GAAG,WAAW,CAAC,EAAE,mBAAmB,CAAC,EAAE,YAAY,SAAS,kBAAkB,GAAG,CAAC,EAAE,WAAW;IAEpH,sBAAsB;IACtB,IAAI,aAAa;QACf,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAAuB,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CAC3E,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;;;;;;;;;;;;kCAItD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;gCAAwB;gCACH,MAAM,aAAa,CAAC,EAAE,EAAE,KAAK,UAAU,EAAE,GAAG;gCAAG;;;;;;;;;;;;kCAInF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,eAAe,mBAAmB;;;;;;0CAErC,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,UAAU;QACV,WAAW;QACX,OAAM;kBAEL,0BACC;sBACG,YAAY,uBACX,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,SAAQ;gBAAY,QAAO;0BAC9D,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;qCAGvE;;kCACE,8OAAC;wBAAI,WAAU;wBAAe,MAAK;wBAAO,SAAQ;wBAAY,QAAO;kCACnE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAEtE,eAAe,mBAAmB;;;;;;;;;AAOjD", "debugId": null}}, {"offset": {"line": 262, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/utils/imageUtils.ts"], "sourcesContent": ["/**\r\n * Utility functions for handling profile images\r\n */\r\n\r\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';\r\n\r\n/**\r\n * Get the full URL for a profile image\r\n * @param profileImage - The profile image path from the API\r\n * @returns Full URL for the profile image\r\n */\r\nexport const getProfileImageUrl = (profileImage?: string): string | null => {\r\n  if (!profileImage) return null;\r\n  \r\n  // If it's already a full URL (starts with http), return as is\r\n  if (profileImage.startsWith('http://') || profileImage.startsWith('https://')) {\r\n    return profileImage;\r\n  }\r\n  \r\n  // If it's a relative path, prepend the API base URL\r\n  if (profileImage.startsWith('/')) {\r\n    return `${API_BASE_URL}${profileImage}`;\r\n  }\r\n  \r\n  // If it's just a filename, assume it's in the uploads directory\r\n  return `${API_BASE_URL}/uploads/avatars/${profileImage}`;\r\n};\r\n\r\n/**\r\n * Get user initials for fallback display\r\n * @param firstName - User's first name\r\n * @param lastName - User's last name\r\n * @returns Initials string (e.g., \"JD\")\r\n */\r\nexport const getUserInitials = (firstName?: string, lastName?: string): string => {\r\n  const first = firstName?.charAt(0)?.toUpperCase() || '';\r\n  const last = lastName?.charAt(0)?.toUpperCase() || '';\r\n  return `${first}${last}` || 'U';\r\n};\r\n\r\n/**\r\n * Validate image file for upload\r\n * @param file - File to validate\r\n * @returns Validation result with error message if invalid\r\n */\r\nexport const validateImageFile = (file: File): { valid: boolean; error?: string } => {\r\n  // Check file type\r\n  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];\r\n  if (!allowedTypes.includes(file.type)) {\r\n    return {\r\n      valid: false,\r\n      error: 'Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.'\r\n    };\r\n  }\r\n\r\n  // Check file size (5MB max)\r\n  const maxSize = 5 * 1024 * 1024; // 5MB\r\n  if (file.size > maxSize) {\r\n    return {\r\n      valid: false,\r\n      error: 'File size too large. Maximum size is 5MB.'\r\n    };\r\n  }\r\n\r\n  return { valid: true };\r\n};\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;AAED,MAAM,eAAe,QAAQ,GAAG,CAAC,mBAAmB,IAAI;AAOjD,MAAM,qBAAqB,CAAC;IACjC,IAAI,CAAC,cAAc,OAAO;IAE1B,8DAA8D;IAC9D,IAAI,aAAa,UAAU,CAAC,cAAc,aAAa,UAAU,CAAC,aAAa;QAC7E,OAAO;IACT;IAEA,oDAAoD;IACpD,IAAI,aAAa,UAAU,CAAC,MAAM;QAChC,OAAO,GAAG,eAAe,cAAc;IACzC;IAEA,gEAAgE;IAChE,OAAO,GAAG,aAAa,iBAAiB,EAAE,cAAc;AAC1D;AAQO,MAAM,kBAAkB,CAAC,WAAoB;IAClD,MAAM,QAAQ,WAAW,OAAO,IAAI,iBAAiB;IACrD,MAAM,OAAO,UAAU,OAAO,IAAI,iBAAiB;IACnD,OAAO,GAAG,QAAQ,MAAM,IAAI;AAC9B;AAOO,MAAM,oBAAoB,CAAC;IAChC,kBAAkB;IAClB,MAAM,eAAe;QAAC;QAAc;QAAa;QAAa;KAAa;IAC3E,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;QACrC,OAAO;YACL,OAAO;YACP,OAAO;QACT;IACF;IAEA,4BAA4B;IAC5B,MAAM,UAAU,IAAI,OAAO,MAAM,MAAM;IACvC,IAAI,KAAK,IAAI,GAAG,SAAS;QACvB,OAAO;YACL,OAAO;YACP,OAAO;QACT;IACF;IAEA,OAAO;QAAE,OAAO;IAAK;AACvB", "debugId": null}}, {"offset": {"line": 320, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/Header.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport Link from 'next/link';\r\nimport { usePathname } from 'next/navigation';\r\nimport { useAuth } from '../contexts/AuthContext';\r\nimport UserMenu from './UserMenu';\r\nimport LogoutButton from './LogoutButton';\r\nimport { getUserInitials } from '../utils/imageUtils';\r\n\r\ninterface HeaderProps {\r\n  activeTab?: string;\r\n  onTabChange?: (tab: string) => void;\r\n  onMobileMenuToggle?: () => void;\r\n}\r\n\r\nconst Header = ({ activeTab = 'overview', onTabChange, onMobileMenuToggle }: HeaderProps) => {\r\n  const { isAuthenticated, user } = useAuth();\r\n  const [dropdownOpen, setDropdownOpen] = useState(false);\r\n  const pathname = usePathname();\r\n\r\n  // Only show dashboard tabs when on dashboard routes\r\n  const showDashboardTabs = pathname.startsWith('/dashboard');\r\n\r\n  const toggleDropdown = () => {\r\n    setDropdownOpen(!dropdownOpen);\r\n  };\r\n\r\n  const tabs = [\r\n    { id: 'overview', label: 'Overview' },\r\n    { id: 'licenses', label: 'Licenses' },\r\n    { id: 'users', label: 'Users' },\r\n    { id: 'transactions', label: 'Transactions' },\r\n    { id: 'spectrum', label: 'Spectrum' },\r\n    { id: 'compliance', label: 'Compliance' },\r\n  ];\r\n\r\n  return (\r\n    <header className=\"bg-white dark:bg-gray-800 shadow-sm z-10\">\r\n      <div className=\"flex items-center justify-between h-16 px-4 sm:px-6\">\r\n        <button\r\n          id=\"mobileMenuBtn\"\r\n          type=\"button\"\r\n          onClick={onMobileMenuToggle}\r\n          className=\"md:hidden text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none\"\r\n        >\r\n          <div className=\"w-6 h-6 flex items-center justify-center\">\r\n            <i className=\"ri-menu-line ri-lg\"></i>\r\n          </div>\r\n        </button>\r\n        <div className=\"flex-1 flex items-center justify-center px-2 lg:ml-6 lg:justify-start\">\r\n          <div className=\"max-w-lg w-full\">\r\n            <label htmlFor=\"search\" className=\"sr-only\">Search</label>\r\n            <div className=\"relative\">\r\n              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                <div className=\"w-5 h-5 flex items-center justify-center text-gray-400 dark:text-gray-500\">\r\n                  <i className=\"ri-search-line\"></i>\r\n                </div>\r\n              </div>\r\n              <input\r\n                id=\"search\"\r\n                name=\"search\"\r\n                className=\"block w-full pl-10 pr-3 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-sm hover:bg-white dark:hover:bg-gray-600 transition-colors\"\r\n                placeholder=\"Search for licenses, users, or transactions...\"\r\n                type=\"search\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"flex items-center\">\r\n          <button\r\n            type=\"button\"\r\n            className=\"flex-shrink-0 p-1 mr-4 text-gray-500 dark:text-gray-400 rounded-full hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none relative\"\r\n          >\r\n            <span className=\"sr-only\">View notifications</span>\r\n            <div className=\"w-6 h-6 flex items-center justify-center\">\r\n              <i className=\"ri-notification-3-line ri-lg\"></i>\r\n            </div>\r\n            <span className=\"absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white\"></span>\r\n          </button>\r\n          <div className=\"dropdown relative\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={toggleDropdown}\r\n              className=\"flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n            >\r\n              <span className=\"sr-only\">Open user menu</span>\r\n              {user?.profile_image ? (\r\n                <img\r\n                  className=\"h-8 w-8 rounded-full object-cover\"\r\n                  src={user.profile_image}\r\n                  alt=\"Profile\"\r\n                  onError={(e) => {\r\n                    // Fallback to initials if image fails to load\r\n                    const target = e.target as HTMLImageElement;\r\n                    target.style.display = 'none';\r\n                    target.nextElementSibling?.classList.remove('hidden');\r\n                  }}\r\n                />\r\n              ) : null}\r\n              <div className={`h-8 w-8 rounded-full bg-red-600 flex items-center justify-center text-white text-sm font-medium ${user?.profile_image ? 'hidden' : ''}`}>\r\n                {user ? getUserInitials(user.first_name, user.last_name) : 'U'}\r\n              </div>\r\n            </button>\r\n            <div\r\n              className={`dropdown-content mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black dark:ring-gray-600 ring-opacity-5 ${\r\n                dropdownOpen ? 'show' : ''\r\n              }`}\r\n            >\r\n              <div className=\"py-1\">\r\n                <Link\r\n                  href=\"/profile\"\r\n                  className=\"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                >\r\n                  Your Profile\r\n                </Link>\r\n                <Link\r\n                  href=\"/settings\"\r\n                  className=\"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                >\r\n                  Settings\r\n                </Link>\r\n                <div className=\"px-4 py-2\">\r\n                  <LogoutButton\r\n                    variant=\"text\"\r\n                    size=\"sm\"\r\n                    className=\"w-full justify-start text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                    showConfirmation={true}\r\n                    redirectTo=\"/auth/login\"\r\n                  >\r\n                    Sign out\r\n                  </LogoutButton>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      {/* Secondary navigation - only show on dashboard routes */}\r\n      {showDashboardTabs && (\r\n        <div className=\"border-t border-gray-200 dark:border-gray-700 px-4 sm:px-6\">\r\n          <div className=\"py-3 flex space-x-8\">\r\n            {tabs.map((tab) => (\r\n              <button\r\n                key={tab.id}\r\n                type=\"button\"\r\n                onClick={() => {\r\n                  onTabChange?.(tab.id);\r\n                  // Dispatch custom event for dashboard to listen to\r\n                  window.dispatchEvent(new CustomEvent('tabChange', { detail: { tab: tab.id } }));\r\n                }}\r\n                className={`tab-button text-sm px-1 py-2 ${\r\n                  activeTab === tab.id ? 'active' : 'text-gray-500'\r\n                }`}\r\n              >\r\n                {tab.label}\r\n              </button>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      )}\r\n    </header>\r\n  );\r\n};\r\n\r\nexport default Header;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AARA;;;;;;;;AAgBA,MAAM,SAAS,CAAC,EAAE,YAAY,UAAU,EAAE,WAAW,EAAE,kBAAkB,EAAe;IACtF,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACxC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,oDAAoD;IACpD,MAAM,oBAAoB,SAAS,UAAU,CAAC;IAE9C,MAAM,iBAAiB;QACrB,gBAAgB,CAAC;IACnB;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAY,OAAO;QAAW;QACpC;YAAE,IAAI;YAAY,OAAO;QAAW;QACpC;YAAE,IAAI;YAAS,OAAO;QAAQ;QAC9B;YAAE,IAAI;YAAgB,OAAO;QAAe;QAC5C;YAAE,IAAI;YAAY,OAAO;QAAW;QACpC;YAAE,IAAI;YAAc,OAAO;QAAa;KACzC;IAED,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,IAAG;wBACH,MAAK;wBACL,SAAS;wBACT,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;;;;;;;;;;;;;;;kCAGjB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,SAAQ;oCAAS,WAAU;8CAAU;;;;;;8CAC5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;;;;;;;;;;;;;;;;sDAGjB,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,WAAU;4CACV,aAAY;4CACZ,MAAK;;;;;;;;;;;;;;;;;;;;;;;kCAKb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;;;;;;;;;;;kDAEf,8OAAC;wCAAK,WAAU;;;;;;;;;;;;0CAElB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,8OAAC;gDAAK,WAAU;0DAAU;;;;;;4CACzB,MAAM,8BACL,8OAAC;gDACC,WAAU;gDACV,KAAK,KAAK,aAAa;gDACvB,KAAI;gDACJ,SAAS,CAAC;oDACR,8CAA8C;oDAC9C,MAAM,SAAS,EAAE,MAAM;oDACvB,OAAO,KAAK,CAAC,OAAO,GAAG;oDACvB,OAAO,kBAAkB,EAAE,UAAU,OAAO;gDAC9C;;;;;uDAEA;0DACJ,8OAAC;gDAAI,WAAW,CAAC,gGAAgG,EAAE,MAAM,gBAAgB,WAAW,IAAI;0DACrJ,OAAO,CAAA,GAAA,0HAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,UAAU,EAAE,KAAK,SAAS,IAAI;;;;;;;;;;;;kDAG/D,8OAAC;wCACC,WAAW,CAAC,8HAA8H,EACxI,eAAe,SAAS,IACxB;kDAEF,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;8DAGD,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;8DAGD,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,kIAAA,CAAA,UAAY;wDACX,SAAQ;wDACR,MAAK;wDACL,WAAU;wDACV,kBAAkB;wDAClB,YAAW;kEACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUZ,mCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC,oBACT,8OAAC;4BAEC,MAAK;4BACL,SAAS;gCACP,cAAc,IAAI,EAAE;gCACpB,mDAAmD;gCACnD,OAAO,aAAa,CAAC,IAAI,YAAY,aAAa;oCAAE,QAAQ;wCAAE,KAAK,IAAI,EAAE;oCAAC;gCAAE;4BAC9E;4BACA,WAAW,CAAC,6BAA6B,EACvC,cAAc,IAAI,EAAE,GAAG,WAAW,iBAClC;sCAED,IAAI,KAAK;2BAXL,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;AAmB3B;uCAEe", "debugId": null}}, {"offset": {"line": 673, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/NavItem.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport { useLoading } from '@/contexts/LoadingContext';\r\n\r\ninterface NavItemProps {\r\n  href: string;\r\n  icon: string;\r\n  label: string;\r\n  isActive?: boolean;\r\n  onClick?: () => void;\r\n}\r\n\r\nconst NavItem: React.FC<NavItemProps> = ({ \r\n  href, \r\n  icon, \r\n  label, \r\n  isActive = false,\r\n  onClick \r\n}) => {\r\n  const { showLoader } = useLoading();\r\n\r\n  const handleClick = () => {\r\n    // Show loader with specific message based on the page\r\n    const pageMessages: { [key: string]: string } = {\r\n      '/dashboard': 'Loading Dashboard...',\r\n      '/applications/telecommunications': 'Loading Telecommunications...',\r\n      '/applications/postal': 'Loading Postal Services...',\r\n      '/applications/standards': 'Loading Standards...',\r\n      '/applications/clf': 'Loading CLF...',\r\n      '/resources': 'Loading Resources...',\r\n      '/procurement': 'Loading Procurement...',\r\n      '/spectrum': 'Loading Spectrum Management...',\r\n      '/financial': 'Loading Financial Data...',\r\n      '/reports': 'Loading Reports...',\r\n      '/users': 'Loading User Management...',\r\n      '/audit-trail': 'Loading Audit Trail...',\r\n      '/help': 'Loading Help & Support...'\r\n    };\r\n\r\n    const message = pageMessages[href] || 'Loading page...';\r\n    showLoader(message);\r\n\r\n    if (onClick) {\r\n      onClick();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Link\r\n      href={href}\r\n      onClick={handleClick}\r\n      className={`\r\n        flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200\r\n        ${isActive\r\n          ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-r-2 border-red-600 dark:border-red-400'\r\n          : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100'\r\n        }\r\n      `}\r\n    >\r\n      <div className={`w-5 h-5 flex items-center justify-center mr-3 ${isActive ? 'text-red-600 dark:text-red-400' : ''}`}>\r\n        <i className={icon}></i>\r\n      </div>\r\n      {label}\r\n    </Link>\r\n  );\r\n};\r\n\r\nexport default NavItem;"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAcA,MAAM,UAAkC,CAAC,EACvC,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,WAAW,KAAK,EAChB,OAAO,EACR;IACC,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,aAAU,AAAD;IAEhC,MAAM,cAAc;QAClB,sDAAsD;QACtD,MAAM,eAA0C;YAC9C,cAAc;YACd,oCAAoC;YACpC,wBAAwB;YACxB,2BAA2B;YAC3B,qBAAqB;YACrB,cAAc;YACd,gBAAgB;YAChB,aAAa;YACb,cAAc;YACd,YAAY;YACZ,UAAU;YACV,gBAAgB;YAChB,SAAS;QACX;QAEA,MAAM,UAAU,YAAY,CAAC,KAAK,IAAI;QACtC,WAAW;QAEX,IAAI,SAAS;YACX;QACF;IACF;IAEA,qBACE,8OAAC,4JAAA,CAAA,UAAI;QACH,MAAM;QACN,SAAS;QACT,WAAW,CAAC;;QAEV,EAAE,WACE,8GACA,wHACH;MACH,CAAC;;0BAED,8OAAC;gBAAI,WAAW,CAAC,8CAA8C,EAAE,WAAW,mCAAmC,IAAI;0BACjH,cAAA,8OAAC;oBAAE,WAAW;;;;;;;;;;;YAEf;;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 745, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/services/cacheService.ts"], "sourcesContent": ["// Cache service for API responses to reduce rate limiting\r\n\r\ninterface CacheItem<T> {\r\n  data: T;\r\n  timestamp: number;\r\n  expiresAt: number;\r\n}\r\n\r\nclass CacheService {\r\n  private cache = new Map<string, CacheItem<any>>();\r\n  private defaultTTL = 5 * 60 * 1000; // 5 minutes default TTL\r\n\r\n  /**\r\n   * Set cache item with TTL\r\n   */\r\n  set<T>(key: string, data: T, ttl: number = this.defaultTTL): void {\r\n    const now = Date.now();\r\n    const item: CacheItem<T> = {\r\n      data,\r\n      timestamp: now,\r\n      expiresAt: now + ttl\r\n    };\r\n    \r\n    this.cache.set(key, item);\r\n    console.log(`Cache SET: ${key} (expires in ${ttl}ms)`);\r\n  }\r\n\r\n  /**\r\n   * Get cache item if not expired\r\n   */\r\n  get<T>(key: string): T | null {\r\n    const item = this.cache.get(key);\r\n    \r\n    if (!item) {\r\n      console.log(`Cache MISS: ${key}`);\r\n      return null;\r\n    }\r\n\r\n    const now = Date.now();\r\n    if (now > item.expiresAt) {\r\n      console.log(`Cache EXPIRED: ${key}`);\r\n      this.cache.delete(key);\r\n      return null;\r\n    }\r\n\r\n    console.log(`Cache HIT: ${key} (age: ${now - item.timestamp}ms)`);\r\n    return item.data as T;\r\n  }\r\n\r\n  /**\r\n   * Check if cache has valid item\r\n   */\r\n  has(key: string): boolean {\r\n    return this.get(key) !== null;\r\n  }\r\n\r\n  /**\r\n   * Delete cache item\r\n   */\r\n  delete(key: string): boolean {\r\n    console.log(`Cache DELETE: ${key}`);\r\n    return this.cache.delete(key);\r\n  }\r\n\r\n  /**\r\n   * Clear all cache\r\n   */\r\n  clear(): void {\r\n    console.log('Cache CLEAR: All items');\r\n    this.cache.clear();\r\n  }\r\n\r\n  /**\r\n   * Get cache stats\r\n   */\r\n  getStats(): { size: number; keys: string[] } {\r\n    return {\r\n      size: this.cache.size,\r\n      keys: Array.from(this.cache.keys())\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Clean expired items\r\n   */\r\n  cleanup(): void {\r\n    const now = Date.now();\r\n    let cleaned = 0;\r\n\r\n    for (const [key, item] of this.cache.entries()) {\r\n      if (now > item.expiresAt) {\r\n        this.cache.delete(key);\r\n        cleaned++;\r\n      }\r\n    }\r\n\r\n    if (cleaned > 0) {\r\n      console.log(`Cache CLEANUP: Removed ${cleaned} expired items`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get or set pattern - fetch data if not cached\r\n   */\r\n  async getOrSet<T>(\r\n    key: string,\r\n    fetcher: () => Promise<T>,\r\n    ttl: number = this.defaultTTL\r\n  ): Promise<T> {\r\n    // Try to get from cache first\r\n    const cached = this.get<T>(key);\r\n    if (cached !== null) {\r\n      return cached;\r\n    }\r\n\r\n    // Fetch fresh data\r\n    console.log(`Cache FETCH: ${key}`);\r\n    const data = await fetcher();\r\n    \r\n    // Store in cache\r\n    this.set(key, data, ttl);\r\n    \r\n    return data;\r\n  }\r\n\r\n  /**\r\n   * Invalidate cache by pattern\r\n   */\r\n  invalidatePattern(pattern: string): void {\r\n    const regex = new RegExp(pattern);\r\n    let invalidated = 0;\r\n\r\n    for (const key of this.cache.keys()) {\r\n      if (regex.test(key)) {\r\n        this.cache.delete(key);\r\n        invalidated++;\r\n      }\r\n    }\r\n\r\n    if (invalidated > 0) {\r\n      console.log(`Cache INVALIDATE: Removed ${invalidated} items matching pattern: ${pattern}`);\r\n    }\r\n  }\r\n}\r\n\r\n// Create singleton instance\r\nexport const cacheService = new CacheService();\r\n\r\n// Cache keys constants\r\nexport const CACHE_KEYS = {\r\n  LICENSE_TYPES: 'license-types',\r\n  LICENSE_CATEGORIES: 'license-categories',\r\n  LICENSE_CATEGORIES_BY_TYPE: (typeId: string) => `license-categories-type-${typeId}`,\r\n  USER_APPLICATIONS: 'user-applications',\r\n  APPLICATION: (id: string) => `application-${id}`,\r\n} as const;\r\n\r\n// Cache TTL constants (in milliseconds)\r\nexport const CACHE_TTL = {\r\n  SHORT: 2 * 60 * 1000,      // 2 minutes\r\n  MEDIUM: 5 * 60 * 1000,     // 5 minutes\r\n  LONG: 15 * 60 * 1000,      // 15 minutes\r\n  VERY_LONG: 60 * 60 * 1000, // 1 hour\r\n} as const;\r\n\r\n// Auto cleanup every 5 minutes\r\nsetInterval(() => {\r\n  cacheService.cleanup();\r\n}, 5 * 60 * 1000);\r\n\r\nexport default cacheService;\r\n"], "names": [], "mappings": "AAAA,0DAA0D;;;;;;;AAQ1D,MAAM;IACI,QAAQ,IAAI,MAA8B;IAC1C,aAAa,IAAI,KAAK,KAAK;IAEnC;;GAEC,GACD,IAAO,GAAW,EAAE,IAAO,EAAE,MAAc,IAAI,CAAC,UAAU,EAAQ;QAChE,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,OAAqB;YACzB;YACA,WAAW;YACX,WAAW,MAAM;QACnB;QAEA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK;QACpB,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,IAAI,aAAa,EAAE,IAAI,GAAG,CAAC;IACvD;IAEA;;GAEC,GACD,IAAO,GAAW,EAAY;QAC5B,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAE5B,IAAI,CAAC,MAAM;YACT,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,KAAK;YAChC,OAAO;QACT;QAEA,MAAM,MAAM,KAAK,GAAG;QACpB,IAAI,MAAM,KAAK,SAAS,EAAE;YACxB,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,KAAK;YACnC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YAClB,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,IAAI,OAAO,EAAE,MAAM,KAAK,SAAS,CAAC,GAAG,CAAC;QAChE,OAAO,KAAK,IAAI;IAClB;IAEA;;GAEC,GACD,IAAI,GAAW,EAAW;QACxB,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS;IAC3B;IAEA;;GAEC,GACD,OAAO,GAAW,EAAW;QAC3B,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,KAAK;QAClC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IAC3B;IAEA;;GAEC,GACD,QAAc;QACZ,QAAQ,GAAG,CAAC;QACZ,IAAI,CAAC,KAAK,CAAC,KAAK;IAClB;IAEA;;GAEC,GACD,WAA6C;QAC3C,OAAO;YACL,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI;QAClC;IACF;IAEA;;GAEC,GACD,UAAgB;QACd,MAAM,MAAM,KAAK,GAAG;QACpB,IAAI,UAAU;QAEd,KAAK,MAAM,CAAC,KAAK,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,GAAI;YAC9C,IAAI,MAAM,KAAK,SAAS,EAAE;gBACxB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;gBAClB;YACF;QACF;QAEA,IAAI,UAAU,GAAG;YACf,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,QAAQ,cAAc,CAAC;QAC/D;IACF;IAEA;;GAEC,GACD,MAAM,SACJ,GAAW,EACX,OAAyB,EACzB,MAAc,IAAI,CAAC,UAAU,EACjB;QACZ,8BAA8B;QAC9B,MAAM,SAAS,IAAI,CAAC,GAAG,CAAI;QAC3B,IAAI,WAAW,MAAM;YACnB,OAAO;QACT;QAEA,mBAAmB;QACnB,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,KAAK;QACjC,MAAM,OAAO,MAAM;QAEnB,iBAAiB;QACjB,IAAI,CAAC,GAAG,CAAC,KAAK,MAAM;QAEpB,OAAO;IACT;IAEA;;GAEC,GACD,kBAAkB,OAAe,EAAQ;QACvC,MAAM,QAAQ,IAAI,OAAO;QACzB,IAAI,cAAc;QAElB,KAAK,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,GAAI;YACnC,IAAI,MAAM,IAAI,CAAC,MAAM;gBACnB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;gBAClB;YACF;QACF;QAEA,IAAI,cAAc,GAAG;YACnB,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,YAAY,yBAAyB,EAAE,SAAS;QAC3F;IACF;AACF;AAGO,MAAM,eAAe,IAAI;AAGzB,MAAM,aAAa;IACxB,eAAe;IACf,oBAAoB;IACpB,4BAA4B,CAAC,SAAmB,CAAC,wBAAwB,EAAE,QAAQ;IACnF,mBAAmB;IACnB,aAAa,CAAC,KAAe,CAAC,YAAY,EAAE,IAAI;AAClD;AAGO,MAAM,YAAY;IACvB,OAAO,IAAI,KAAK;IAChB,QAAQ,IAAI,KAAK;IACjB,MAAM,KAAK,KAAK;IAChB,WAAW,KAAK,KAAK;AACvB;AAEA,+BAA+B;AAC/B,YAAY;IACV,aAAa,OAAO;AACtB,GAAG,IAAI,KAAK;uCAEG", "debugId": null}}, {"offset": {"line": 880, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/services/licenseTypeService.ts"], "sourcesContent": ["import { processApiResponse } from '@/lib/authUtils';\r\nimport { apiClient } from '../lib/apiClient';\r\nimport { cacheService, CACHE_KEYS, CACHE_TTL } from './cacheService';\r\n\r\n// Types\r\nexport interface LicenseType {\r\n  license_type_id: string;\r\n  name: string;\r\n  code?: string;\r\n  description: string;\r\n  validity: number;\r\n  created_at: string;\r\n  updated_at: string;\r\n  created_by?: string;\r\n  updated_by?: string;\r\n  creator?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n  updater?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n}\r\n\r\nexport interface NavigationItem {\r\n  license_type_id: string;\r\n  name: string;\r\n  code: string;\r\n  href: string;\r\n  label: string;\r\n  roles: string[];\r\n}\r\n\r\nexport interface CreateLicenseTypeDto {\r\n  name: string;\r\n  description: string;\r\n  validity: number;\r\n}\r\n\r\nexport interface UpdateLicenseTypeDto {\r\n  name?: string;\r\n  description?: string;\r\n  validity?: number;\r\n}\r\n\r\nexport interface PaginatedResponse<T> {\r\n  data: T[];\r\n  meta: {\r\n    itemsPerPage: number;\r\n    totalItems?: number;\r\n    currentPage?: number;\r\n    totalPages?: number;\r\n    sortBy: [string, string][];\r\n    searchBy: string[];\r\n    search: string;\r\n    select: string[];\r\n    filter?: Record<string, string | string[]>;\r\n  };\r\n  links: {\r\n    first?: string;\r\n    previous?: string;\r\n    current: string;\r\n    next?: string;\r\n    last?: string;\r\n  };\r\n}\r\n\r\nexport type LicenseTypesResponse = PaginatedResponse<LicenseType>;\r\n\r\nexport interface PaginateQuery {\r\n  page?: number;\r\n  limit?: number;\r\n  sortBy?: string[];\r\n  searchBy?: string[];\r\n  search?: string;\r\n  filter?: Record<string, string | string[]>;\r\n}\r\n\r\nexport const licenseTypeService = {\r\n  // Get all license types with pagination\r\n  async getLicenseTypes(query: PaginateQuery = {}): Promise<LicenseTypesResponse> {\r\n    const params = new URLSearchParams();\r\n\r\n    if (query.page) params.set('page', query.page.toString());\r\n    if (query.limit) params.set('limit', query.limit.toString());\r\n    if (query.search) params.set('search', query.search);\r\n    if (query.sortBy) {\r\n      query.sortBy.forEach(sort => params.append('sortBy', sort));\r\n    }\r\n    if (query.searchBy) {\r\n      query.searchBy.forEach(search => params.append('searchBy', search));\r\n    }\r\n    if (query.filter) {\r\n      Object.entries(query.filter).forEach(([key, value]) => {\r\n        if (Array.isArray(value)) {\r\n          value.forEach(v => params.append(`filter.${key}`, v));\r\n        } else {\r\n          params.set(`filter.${key}`, value);\r\n        }\r\n      });\r\n    }\r\n\r\n    const response = await apiClient.get(`/license-types?${params.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get license type by ID\r\n  async getLicenseType(id: string): Promise<LicenseType> {\r\n    const response = await apiClient.get(`/license-types/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  \r\n  // Get license type by ID\r\n  async getLicenseTypeByCode(code: string): Promise<LicenseType> {\r\n    const response = await apiClient.get(`/license-types/by-code/${code}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n\r\n  // Create new license type\r\n  async createLicenseType(licenseTypeData: CreateLicenseTypeDto): Promise<LicenseType> {\r\n    const response = await apiClient.post('/license-types', licenseTypeData);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update license type\r\n  async updateLicenseType(id: string, licenseTypeData: UpdateLicenseTypeDto): Promise<LicenseType> {\r\n    const response = await apiClient.put(`/license-types/${id}`, licenseTypeData);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Delete license type\r\n  async deleteLicenseType(id: string): Promise<{ message: string }> {\r\n    const response = await apiClient.delete(`/license-types/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get all license types (simple list for dropdowns) with caching\r\n  async getAllLicenseTypes(): Promise<any> {\r\n    return cacheService.getOrSet(\r\n      CACHE_KEYS.LICENSE_TYPES,\r\n      async () => {\r\n        console.log('Fetching license types from API...');\r\n        // Reduce limit to avoid rate limiting\r\n        const response = await this.getLicenseTypes({ limit: 100 });\r\n        return processApiResponse(response);\r\n      },\r\n      CACHE_TTL.LONG // Cache for 15 minutes\r\n    );\r\n  },\r\n\r\n  // Get license types for sidebar navigation\r\n  async getNavigationItems(): Promise<any> {\r\n    try {\r\n      const response = await apiClient.get('/license-types/navigation/sidebar');\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('LicenseTypeService.getNavigationItems error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAiFO,MAAM,qBAAqB;IAChC,wCAAwC;IACxC,MAAM,iBAAgB,QAAuB,CAAC,CAAC;QAC7C,MAAM,SAAS,IAAI;QAEnB,IAAI,MAAM,IAAI,EAAE,OAAO,GAAG,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ;QACtD,IAAI,MAAM,KAAK,EAAE,OAAO,GAAG,CAAC,SAAS,MAAM,KAAK,CAAC,QAAQ;QACzD,IAAI,MAAM,MAAM,EAAE,OAAO,GAAG,CAAC,UAAU,MAAM,MAAM;QACnD,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,MAAM,CAAC,OAAO,CAAC,CAAA,OAAQ,OAAO,MAAM,CAAC,UAAU;QACvD;QACA,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAA,SAAU,OAAO,MAAM,CAAC,YAAY;QAC7D;QACA,IAAI,MAAM,MAAM,EAAE;YAChB,OAAO,OAAO,CAAC,MAAM,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAChD,IAAI,MAAM,OAAO,CAAC,QAAQ;oBACxB,MAAM,OAAO,CAAC,CAAA,IAAK,OAAO,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBACpD,OAAO;oBACL,OAAO,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBAC9B;YACF;QACF;QAEA,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,OAAO,QAAQ,IAAI;QAC1E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,yBAAyB;IACzB,MAAM,gBAAe,EAAU;QAC7B,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,IAAI;QAC3D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAGA,yBAAyB;IACzB,MAAM,sBAAqB,IAAY;QACrC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,MAAM;QACrE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAGA,0BAA0B;IAC1B,MAAM,mBAAkB,eAAqC;QAC3D,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,kBAAkB;QACxD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,sBAAsB;IACtB,MAAM,mBAAkB,EAAU,EAAE,eAAqC;QACvE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,IAAI,EAAE;QAC7D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,sBAAsB;IACtB,MAAM,mBAAkB,EAAU;QAChC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,eAAe,EAAE,IAAI;QAC9D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,iEAAiE;IACjE,MAAM;QACJ,OAAO,+HAAA,CAAA,eAAY,CAAC,QAAQ,CAC1B,+HAAA,CAAA,aAAU,CAAC,aAAa,EACxB;YACE,QAAQ,GAAG,CAAC;YACZ,sCAAsC;YACtC,MAAM,WAAW,MAAM,IAAI,CAAC,eAAe,CAAC;gBAAE,OAAO;YAAI;YACzD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,GACA,+HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,uBAAuB;;IAE1C;IAEA,2CAA2C;IAC3C,MAAM;QACJ,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gDAAgD;YAC9D,MAAM;QACR;IACF;AAGF", "debugId": null}}, {"offset": {"line": 968, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/Sidebar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { usePathname } from 'next/navigation';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport NavItem from './NavItem';\r\nimport Link from 'next/link';\r\nimport { getUserInitials } from '@/utils/imageUtils';\r\nimport { licenseTypeService, NavigationItem } from '@/services/licenseTypeService';\r\n\r\nconst Sidebar: React.FC = () => {\r\n  const { user } = useAuth();\r\n  const pathname = usePathname();\r\n  const [isMobileOpen, setIsMobileOpen] = useState(false);\r\n  const [licenseTypeNavItems, setLicenseTypeNavItems] = useState<NavigationItem[]>([]);\r\n  const [loadingLicenseTypes, setLoadingLicenseTypes] = useState(true);\r\n\r\n  // Close mobile sidebar when route changes\r\n  useEffect(() => {\r\n    setIsMobileOpen(false);\r\n  }, [pathname]);\r\n\r\n  // Load license types for navigation\r\n  useEffect(() => {\r\n    const loadLicenseTypes = async () => {\r\n      try {\r\n        setLoadingLicenseTypes(true);\r\n        const data = await licenseTypeService.getNavigationItems();\r\n        const navigationItems: NavigationItem[] = data.data;\r\n        setLicenseTypeNavItems(navigationItems);\r\n      } catch (error) {\r\n        console.error('Failed to load license types for navigation:', error);\r\n        // Fallback to empty array if API fails\r\n        setLicenseTypeNavItems([]);\r\n      } finally {\r\n        setLoadingLicenseTypes(false);\r\n      }\r\n    };\r\n\r\n    loadLicenseTypes();\r\n  }, []);\r\n\r\n  // Close mobile sidebar when clicking outside\r\n  useEffect(() => {\r\n    const handleClickOutside = (event: MouseEvent) => {\r\n      const sidebar = document.getElementById('mobile-sidebar');\r\n      const toggleButton = document.getElementById('mobile-sidebar-toggle');\r\n      \r\n      if (\r\n        isMobileOpen &&\r\n        sidebar &&\r\n        !sidebar.contains(event.target as Node) &&\r\n        toggleButton &&\r\n        !toggleButton.contains(event.target as Node)\r\n      ) {\r\n        setIsMobileOpen(false);\r\n      }\r\n    };\r\n\r\n    document.addEventListener('mousedown', handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener('mousedown', handleClickOutside);\r\n    };\r\n  }, [isMobileOpen]);\r\n\r\n  const toggleMobileSidebar = () => {\r\n    setIsMobileOpen(!isMobileOpen);\r\n  };\r\n\r\n  // Static navigation items\r\n  const topStaticNavigationItems = [\r\n    {\r\n      href: '/dashboard',\r\n      icon: 'ri-dashboard-line',\r\n      label: 'Dashboard',\r\n      roles: ['administrator', 'evaluator', 'customer']\r\n    },\r\n    {\r\n      href: '/task-assignment',\r\n      icon: 'ri-user-add-line',\r\n      label: 'Task Assignment',\r\n      roles: ['administrator', 'evaluator']\r\n    }\r\n  ];\r\n\r\n  const staticNavigationItems = [\r\n\r\n    {\r\n      href: '/consumer-affairs',\r\n      icon: 'ri-shield-user-line',\r\n      label: 'Consumer Affairs',\r\n      roles: ['administrator', 'evaluator', 'customer']\r\n    },\r\n    {\r\n      href: '/data-breach',\r\n      icon: 'ri-shield-cross-line',\r\n      label: 'Data Breach',\r\n      roles: ['administrator', 'evaluator']\r\n    },\r\n    {\r\n      href: '/resources',\r\n      icon: 'ri-folder-line',\r\n      label: 'Resources',\r\n      roles: ['administrator', 'evaluator']\r\n    },\r\n    {\r\n      href: '/procurement',\r\n      icon: 'ri-shopping-bag-line',\r\n      label: 'Procurement',\r\n      roles: ['administrator', 'evaluator']\r\n    },\r\n    {\r\n      href: '/financial',\r\n      icon: 'ri-money-dollar-circle-line',\r\n      label: 'Accounts & Finance',\r\n      roles: ['administrator', 'evaluator']\r\n    },\r\n    {\r\n      href: '/reports',\r\n      icon: 'ri-file-chart-line',\r\n      label: 'Reports & Analytics',\r\n      roles: ['administrator', 'evaluator']\r\n    }\r\n  ];\r\n\r\n  const settingsNavigationItems = [\r\n    {\r\n      href: '/users',\r\n      icon: 'ri-user-settings-line',\r\n      label: 'User Management',\r\n      roles: ['administrator']\r\n    },\r\n    {\r\n      href: '/settings',\r\n      icon: 'ri-settings-3-line',\r\n      label: 'Management Settings',\r\n      roles: ['administrator']\r\n    },\r\n    {\r\n      href: '/audit-trail',\r\n      icon: 'ri-shield-line',\r\n      label: 'Audit Trail',\r\n      roles: ['administrator', 'evaluator']\r\n    },\r\n    {\r\n      href: '/help',\r\n      icon: 'ri-question-line',\r\n      label: 'Help & Support',\r\n      roles: ['administrator', 'evaluator', 'customer']\r\n    }\r\n  ];\r\n\r\n  // Combine static navigation items with dynamic license types\r\n  const mainNavigationItems = [\r\n    ...licenseTypeNavItems.map(item => ({\r\n      href: item.href,\r\n      icon: 'ri-file-list-line', // Generic icon for all license types\r\n      label: item.label,\r\n      roles: item.roles\r\n    })),\r\n    ...staticNavigationItems\r\n  ];\r\n\r\n  const filteredMainNavItems = mainNavigationItems.filter(item =>\r\n    user?.roles?.some(role => item.roles.includes(role)) ||\r\n    item.roles.includes('customer')\r\n  );\r\n\r\n  const filteredSettingsNavItems = settingsNavigationItems.filter(item =>\r\n    user?.roles?.some(role => item.roles.includes(role)) ||\r\n    item.roles.includes('customer')\r\n  );\r\n\r\n  return (\r\n    <>\r\n      {/* Mobile Sidebar Toggle Button */}\r\n      <button\r\n        id=\"mobile-sidebar-toggle\"\r\n        onClick={toggleMobileSidebar}\r\n        className=\"lg:hidden fixed top-4 left-4 z-50 p-2 bg-primary text-white rounded-md shadow-lg hover:bg-red-700 transition-colors\"\r\n        aria-label=\"Toggle mobile sidebar\"\r\n      >\r\n        <i className={`fas ${isMobileOpen ? 'fa-times' : 'fa-bars'}`}></i>\r\n      </button>\r\n\r\n      {/* Mobile Overlay */}\r\n      {isMobileOpen && (\r\n        <div \r\n          className=\"lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40\"\r\n          onClick={() => setIsMobileOpen(false)}\r\n        />\r\n      )}\r\n\r\n      {/* Sidebar */}\r\n      <aside\r\n        id=\"mobile-sidebar\"\r\n        className={`\r\n          fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out\r\n          ${isMobileOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}\r\n        `}\r\n      >\r\n        <div className=\"flex flex-col h-full\">\r\n          {/* Logo/Header */}\r\n          <div className=\"h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700\">\r\n            <div className=\"flex items-center\">\r\n              <img src=\"/images/macra-logo.png\" alt=\"MACRA Logo\" className=\"max-h-12 w-auto\" />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Navigation */}\r\n          <nav className=\"mt-6 px-4 side-nav\">\r\n            {/* Main Navigation */}\r\n            {topStaticNavigationItems.length > 0 && (\r\n              <div className=\"mt-2 space-y-1\">\r\n                {topStaticNavigationItems.map((item) => (\r\n                  <NavItem\r\n                    key={item.href}\r\n                    href={item.href}\r\n                    icon={item.icon}\r\n                    label={item.label}\r\n                    isActive={pathname === item.href}\r\n                    onClick={() => setIsMobileOpen(false)}\r\n                  />\r\n              ))}\r\n            </div>\r\n            )}\r\n\r\n            {/* Main Navigation */}\r\n            {filteredMainNavItems.length > 0 && (\r\n              <div className=\"mt-8\">\r\n                <h3 className=\"px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Main Menu\r\n                </h3>\r\n                <div className=\"mt-2 space-y-1\">\r\n                  {filteredMainNavItems.map((item) => (\r\n                    <NavItem\r\n                      key={item.href}\r\n                      href={item.href}\r\n                      icon={item.icon}\r\n                      label={item.label}\r\n                      isActive={pathname === item.href}\r\n                      onClick={() => setIsMobileOpen(false)}\r\n                    />\r\n                ))}\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Settings Section */}\r\n            {filteredSettingsNavItems.length > 0 && (\r\n              <div className=\"mt-8\">\r\n                <h3 className=\"px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Settings\r\n                </h3>\r\n                <div className=\"mt-2 space-y-1\">\r\n                  {filteredSettingsNavItems.map((item) => (\r\n                    <NavItem\r\n                      key={item.href}\r\n                      href={item.href}\r\n                      icon={item.icon}\r\n                      label={item.label}\r\n                      isActive={pathname === item.href}\r\n                      onClick={() => setIsMobileOpen(false)}\r\n                    />\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            )}\r\n          </nav>\r\n\r\n          {/* User Info */}\r\n          {user && (\r\n            <div className=\"absolute bottom-0 w-64 p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800\">\r\n              <div className=\"flex items-center space-x-3\">\r\n                {user.profile_image ? (\r\n                  <img\r\n                    className=\"h-10 w-10 rounded-full object-cover\"\r\n                    src={user.profile_image}\r\n                    alt=\"Profile\"\r\n                    onError={(e) => {\r\n                      // Fallback to initials if image fails to load\r\n                      const target = e.target as HTMLImageElement;\r\n                      target.style.display = 'none';\r\n                      target.nextElementSibling?.classList.remove('hidden');\r\n                    }}\r\n                  />\r\n                ) : null}\r\n                <div className={`h-10 w-10 rounded-full bg-red-600 flex items-center justify-center text-white text-sm font-medium ${user.profile_image ? 'hidden' : ''}`}>\r\n                  {getUserInitials(user.first_name, user.last_name)}\r\n                </div>\r\n                <Link href='/profile' className=\"flex-1 min-w-0\">\r\n                  <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100 truncate\">\r\n                    {user.first_name || 'Unknown'} {user.last_name || 'User'}\r\n                  </p>\r\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400 truncate\">\r\n                    {user.roles && user.roles.length > 0\r\n                      ? user.roles.map(role =>\r\n                          typeof role === 'string'\r\n                            ? role.replace(/_/g, ' ').replace(/\\b\\w/g, (l: string) => l.toUpperCase())\r\n                            : 'Unknown'\r\n                        ).join(', ')\r\n                      : 'User'\r\n                    }\r\n                  </p>\r\n                </Link>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </aside>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Sidebar;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAUA,MAAM,UAAoB;IACxB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IACnF,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gBAAgB;IAClB,GAAG;QAAC;KAAS;IAEb,oCAAoC;IACpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI;gBACF,uBAAuB;gBACvB,MAAM,OAAO,MAAM,qIAAA,CAAA,qBAAkB,CAAC,kBAAkB;gBACxD,MAAM,kBAAoC,KAAK,IAAI;gBACnD,uBAAuB;YACzB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gDAAgD;gBAC9D,uCAAuC;gBACvC,uBAAuB,EAAE;YAC3B,SAAU;gBACR,uBAAuB;YACzB;QACF;QAEA;IACF,GAAG,EAAE;IAEL,6CAA6C;IAC7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,MAAM,UAAU,SAAS,cAAc,CAAC;YACxC,MAAM,eAAe,SAAS,cAAc,CAAC;YAE7C,IACE,gBACA,WACA,CAAC,QAAQ,QAAQ,CAAC,MAAM,MAAM,KAC9B,gBACA,CAAC,aAAa,QAAQ,CAAC,MAAM,MAAM,GACnC;gBACA,gBAAgB;YAClB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG;QAAC;KAAa;IAEjB,MAAM,sBAAsB;QAC1B,gBAAgB,CAAC;IACnB;IAEA,0BAA0B;IAC1B,MAAM,2BAA2B;QAC/B;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;gBAAa;aAAW;QACnD;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;aAAY;QACvC;KACD;IAED,MAAM,wBAAwB;QAE5B;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;gBAAa;aAAW;QACnD;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;aAAY;QACvC;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;aAAY;QACvC;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;aAAY;QACvC;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;aAAY;QACvC;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;aAAY;QACvC;KACD;IAED,MAAM,0BAA0B;QAC9B;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;aAAgB;QAC1B;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;aAAgB;QAC1B;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;aAAY;QACvC;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;gBAAa;aAAW;QACnD;KACD;IAED,6DAA6D;IAC7D,MAAM,sBAAsB;WACvB,oBAAoB,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAClC,MAAM,KAAK,IAAI;gBACf,MAAM;gBACN,OAAO,KAAK,KAAK;gBACjB,OAAO,KAAK,KAAK;YACnB,CAAC;WACE;KACJ;IAED,MAAM,uBAAuB,oBAAoB,MAAM,CAAC,CAAA,OACtD,MAAM,OAAO,KAAK,CAAA,OAAQ,KAAK,KAAK,CAAC,QAAQ,CAAC,UAC9C,KAAK,KAAK,CAAC,QAAQ,CAAC;IAGtB,MAAM,2BAA2B,wBAAwB,MAAM,CAAC,CAAA,OAC9D,MAAM,OAAO,KAAK,CAAA,OAAQ,KAAK,KAAK,CAAC,QAAQ,CAAC,UAC9C,KAAK,KAAK,CAAC,QAAQ,CAAC;IAGtB,qBACE;;0BAEE,8OAAC;gBACC,IAAG;gBACH,SAAS;gBACT,WAAU;gBACV,cAAW;0BAEX,cAAA,8OAAC;oBAAE,WAAW,CAAC,IAAI,EAAE,eAAe,aAAa,WAAW;;;;;;;;;;;YAI7D,8BACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,gBAAgB;;;;;;0BAKnC,8OAAC;gBACC,IAAG;gBACH,WAAW,CAAC;;UAEV,EAAE,eAAe,kBAAkB,qCAAqC;QAC1E,CAAC;0BAED,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,KAAI;oCAAyB,KAAI;oCAAa,WAAU;;;;;;;;;;;;;;;;sCAKjE,8OAAC;4BAAI,WAAU;;gCAEZ,yBAAyB,MAAM,GAAG,mBACjC,8OAAC;oCAAI,WAAU;8CACZ,yBAAyB,GAAG,CAAC,CAAC,qBAC7B,8OAAC,6HAAA,CAAA,UAAO;4CAEN,MAAM,KAAK,IAAI;4CACf,MAAM,KAAK,IAAI;4CACf,OAAO,KAAK,KAAK;4CACjB,UAAU,aAAa,KAAK,IAAI;4CAChC,SAAS,IAAM,gBAAgB;2CAL1B,KAAK,IAAI;;;;;;;;;;gCAYrB,qBAAqB,MAAM,GAAG,mBAC7B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuF;;;;;;sDAGrG,8OAAC;4CAAI,WAAU;sDACZ,qBAAqB,GAAG,CAAC,CAAC,qBACzB,8OAAC,6HAAA,CAAA,UAAO;oDAEN,MAAM,KAAK,IAAI;oDACf,MAAM,KAAK,IAAI;oDACf,OAAO,KAAK,KAAK;oDACjB,UAAU,aAAa,KAAK,IAAI;oDAChC,SAAS,IAAM,gBAAgB;mDAL1B,KAAK,IAAI;;;;;;;;;;;;;;;;gCAavB,yBAAyB,MAAM,GAAG,mBACjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuF;;;;;;sDAGrG,8OAAC;4CAAI,WAAU;sDACZ,yBAAyB,GAAG,CAAC,CAAC,qBAC7B,8OAAC,6HAAA,CAAA,UAAO;oDAEN,MAAM,KAAK,IAAI;oDACf,MAAM,KAAK,IAAI;oDACf,OAAO,KAAK,KAAK;oDACjB,UAAU,aAAa,KAAK,IAAI;oDAChC,SAAS,IAAM,gBAAgB;mDAL1B,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;wBAczB,sBACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;oCACZ,KAAK,aAAa,iBACjB,8OAAC;wCACC,WAAU;wCACV,KAAK,KAAK,aAAa;wCACvB,KAAI;wCACJ,SAAS,CAAC;4CACR,8CAA8C;4CAC9C,MAAM,SAAS,EAAE,MAAM;4CACvB,OAAO,KAAK,CAAC,OAAO,GAAG;4CACvB,OAAO,kBAAkB,EAAE,UAAU,OAAO;wCAC9C;;;;;+CAEA;kDACJ,8OAAC;wCAAI,WAAW,CAAC,kGAAkG,EAAE,KAAK,aAAa,GAAG,WAAW,IAAI;kDACtJ,CAAA,GAAA,0HAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,UAAU,EAAE,KAAK,SAAS;;;;;;kDAElD,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;;0DAC9B,8OAAC;gDAAE,WAAU;;oDACV,KAAK,UAAU,IAAI;oDAAU;oDAAE,KAAK,SAAS,IAAI;;;;;;;0DAEpD,8OAAC;gDAAE,WAAU;0DACV,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,IAC/B,KAAK,KAAK,CAAC,GAAG,CAAC,CAAA,OACb,OAAO,SAAS,WACZ,KAAK,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,SAAS,CAAC,IAAc,EAAE,WAAW,MACrE,WACJ,IAAI,CAAC,QACP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWxB;uCAEe", "debugId": null}}, {"offset": {"line": 1411, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/app/data-breach/layout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useState } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { useAuth } from '../../contexts/AuthContext';\r\nimport Header from \"@/components/Header\";\r\nimport Sidebar from \"@/components/Sidebar\";\r\nimport AuthDebug from \"@/components/AuthDebug\";\r\n\r\nexport default function DataBreachLayout({ children }: { children: React.ReactNode }) {\r\n  const { isAuthenticated, loading } = useAuth();\r\n  const router = useRouter();\r\n  const [activeTab, setActiveTab] = useState('overview');\r\n  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);\r\n\r\n  useEffect(() => {\r\n    if (!loading && !isAuthenticated) {\r\n      router.push('/auth/login');\r\n    }\r\n  }, [isAuthenticated, loading, router]);\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\">\r\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600\"></div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (!isAuthenticated) {\r\n    return null; // Will redirect\r\n  }\r\n\r\n  const toggleMobileSidebar = () => {\r\n    setIsMobileSidebarOpen(!isMobileSidebarOpen);\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex h-screen overflow-hidden bg-gray-50 dark:bg-gray-900\">\r\n      {/* Mobile sidebar overlay */}\r\n      <div\r\n        id=\"mobileSidebarOverlay\"\r\n        className={`mobile-sidebar-overlay ${isMobileSidebarOpen ? 'show' : ''}`}\r\n        onClick={() => setIsMobileSidebarOpen(false)}\r\n      ></div>\r\n\r\n      <Sidebar />\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        <Header\r\n          activeTab={activeTab}\r\n          onTabChange={setActiveTab}\r\n          onMobileMenuToggle={toggleMobileSidebar}\r\n        />\r\n        <main className=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900\">\r\n          {children}\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AASe,SAAS,iBAAiB,EAAE,QAAQ,EAAiC;IAClF,MAAM,EAAE,eAAe,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,iBAAiB;YAChC,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAiB;QAAS;KAAO;IAErC,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO,MAAM,gBAAgB;IAC/B;IAEA,MAAM,sBAAsB;QAC1B,uBAAuB,CAAC;IAC1B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,IAAG;gBACH,WAAW,CAAC,uBAAuB,EAAE,sBAAsB,SAAS,IAAI;gBACxE,SAAS,IAAM,uBAAuB;;;;;;0BAGxC,8OAAC,6HAAA,CAAA,UAAO;;;;;0BACR,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,4HAAA,CAAA,UAAM;wBACL,WAAW;wBACX,aAAa;wBACb,oBAAoB;;;;;;kCAEtB,8OAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}]}