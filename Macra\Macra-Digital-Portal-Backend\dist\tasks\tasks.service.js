"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TasksService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const tasks_entity_1 = require("../entities/tasks.entity");
const nestjs_paginate_1 = require("nestjs-paginate");
let TasksService = class TasksService {
    tasksRepository;
    constructor(tasksRepository) {
        this.tasksRepository = tasksRepository;
    }
    paginateConfig = {
        sortableColumns: ['created_at', 'updated_at', 'task_number', 'title', 'status', 'priority'],
        searchableColumns: ['task_number', 'title', 'description'],
        defaultSortBy: [['created_at', 'DESC']],
        defaultLimit: 20,
        maxLimit: 100,
        relations: ['assignee', 'assigner', 'application'],
        select: [
            'task_id',
            'task_number',
            'title',
            'description',
            'task_type',
            'status',
            'priority',
            'assigned_to',
            'assigned_by',
            'assigned_at',
            'due_date',
            'completed_at',
            'application_id',
            'review_notes',
            'completion_notes',
            'created_at',
            'updated_at',
            'assignee.user_id',
            'assignee.first_name',
            'assignee.last_name',
            'assignee.email',
            'assigner.user_id',
            'assigner.first_name',
            'assigner.last_name',
            'assigner.email',
            'application.application_id',
            'application.application_number',
        ],
    };
    async create(createTaskDto, assignerId) {
        const taskCount = await this.tasksRepository.count();
        const taskNumber = `TASK-${String(taskCount + 1).padStart(6, '0')}`;
        const taskData = {
            task_type: createTaskDto.task_type,
            title: createTaskDto.title,
            description: createTaskDto.description,
            priority: createTaskDto.priority,
            status: createTaskDto.status,
            task_number: taskNumber,
            assigned_by: assignerId,
            assigned_to: createTaskDto.assigned_to,
        };
        if (createTaskDto.due_date) {
            taskData.due_date = new Date(createTaskDto.due_date);
        }
        if (createTaskDto.assigned_to) {
            taskData.assigned_at = new Date();
        }
        const task = this.tasksRepository.create(taskData);
        return this.tasksRepository.save(task);
    }
    async findAll(query) {
        return (0, nestjs_paginate_1.paginate)(query, this.tasksRepository, this.paginateConfig);
    }
    async findUnassigned(query) {
        const config = {
            ...this.paginateConfig,
            where: { assigned_to: (0, typeorm_2.IsNull)() },
        };
        return (0, nestjs_paginate_1.paginate)(query, this.tasksRepository, config);
    }
    async findAssigned(query) {
        const config = {
            ...this.paginateConfig,
            where: { assigned_to: (0, typeorm_2.Not)((0, typeorm_2.IsNull)()) },
        };
        return (0, nestjs_paginate_1.paginate)(query, this.tasksRepository, config);
    }
    async findAssignedToUser(userId, query) {
        const config = {
            ...this.paginateConfig,
            where: { assigned_to: userId },
        };
        return (0, nestjs_paginate_1.paginate)(query, this.tasksRepository, config);
    }
    async findOne(id) {
        const task = await this.tasksRepository.findOne({
            where: { task_id: id },
            relations: ['assignee', 'assigner', 'application'],
        });
        if (!task) {
            throw new common_1.NotFoundException(`Task with ID ${id} not found`);
        }
        return task;
    }
    async update(id, updateTaskDto) {
        const task = await this.findOne(id);
        if (updateTaskDto.status === 'completed' && task.status !== 'completed') {
            task.completed_at = new Date();
        }
        Object.assign(task, updateTaskDto);
        return this.tasksRepository.save(task);
    }
    async assign(id, assignTaskDto, assignerId) {
        const task = await this.findOne(id);
        if (task.assigned_to) {
            throw new common_1.BadRequestException('Task is already assigned to another user');
        }
        task.assigned_to = assignTaskDto.assignedTo;
        task.assigned_by = assignerId;
        task.assigned_at = new Date();
        if (assignTaskDto.comment) {
            task.review_notes = assignTaskDto.comment;
        }
        return this.tasksRepository.save(task);
    }
    async reassign(id, assignTaskDto, assignerId) {
        const task = await this.findOne(id);
        task.assigned_to = assignTaskDto.assignedTo;
        task.assigned_by = assignerId;
        task.assigned_at = new Date();
        if (assignTaskDto.comment) {
            task.review_notes = assignTaskDto.comment;
        }
        return this.tasksRepository.save(task);
    }
    async remove(id) {
        await this.findOne(id);
        await this.tasksRepository.softDelete(id);
    }
    async getTaskStats() {
        const [total, unassigned, assigned, completed, overdue] = await Promise.all([
            this.tasksRepository.count(),
            this.tasksRepository.count({ where: { assigned_to: (0, typeorm_2.IsNull)() } }),
            this.tasksRepository.count({ where: { assigned_to: (0, typeorm_2.Not)((0, typeorm_2.IsNull)()) } }),
            this.tasksRepository.count({ where: { status: tasks_entity_1.TaskStatus.COMPLETED } }),
            this.tasksRepository.count({
                where: {
                    due_date: (0, typeorm_2.Not)((0, typeorm_2.IsNull)()),
                    status: (0, typeorm_2.Not)(tasks_entity_1.TaskStatus.COMPLETED),
                },
            }),
        ]);
        return {
            total,
            unassigned,
            assigned,
            completed,
            overdue,
        };
    }
};
exports.TasksService = TasksService;
exports.TasksService = TasksService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(tasks_entity_1.Task)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], TasksService);
//# sourceMappingURL=tasks.service.js.map