import { CustomerApiService } from '@/lib/customer-api';
import { processApiResponse } from '@/lib/authUtils';

const customerApi = new CustomerApiService();

export interface ContactPerson {
  contact_id: string;
  application_id: string;
  first_name: string;
  last_name: string;
  middle_name?: string;
  designation: string;
  email: string;
  phone: string;
  is_primary: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateContactPersonData {
  application_id: string;
  first_name: string;
  last_name: string;
  middle_name?: string;
  designation: string;
  email: string;
  phone: string;
  is_primary?: boolean;
}

export interface UpdateContactPersonData {
  contact_id: string;
  first_name?: string;
  last_name?: string;
  middle_name?: string;
  designation?: string;
  email?: string;
  phone?: string;
  is_primary?: boolean;
}

export interface ContactPersonsByType {
  primary: ContactPerson | null;
  secondary: ContactPerson[];
}

export const contactPersonService = {
  async createContactPerson(data: CreateContactPersonData): Promise<ContactPerson> {
    const response = await customerApi.api.post('/contact-persons', data);
    return processApiResponse(response);
  },

  async getContactPerson(contactId: string): Promise<ContactPerson> {
    const response = await customerApi.api.get(`/contact-persons/${contactId}`);
    return processApiResponse(response);
  },

  async updateContactPerson(data: UpdateContactPersonData): Promise<ContactPerson> {
    const { contact_id, ...updateData } = data;
    const response = await customerApi.api.put(`/contact-persons/${contact_id}`, updateData);
    return processApiResponse(response);
  },

  async deleteContactPerson(contactId: string): Promise<void> {
    await customerApi.api.delete(`/contact-persons/${contactId}`);
  },

  async getContactPersonsByApplication(applicationId: string): Promise<ContactPerson[]> {
    const response = await customerApi.api.get(`/contact-persons/application/${applicationId}`);
    return processApiResponse(response);
  },

  async getContactPersonsByApplicationGrouped(applicationId: string): Promise<ContactPersonsByType> {
    const response = await customerApi.api.get(`/contact-persons/application/${applicationId}/grouped`);
    return processApiResponse(response);
  },

  // Legacy method for backward compatibility
  async getContactPersonsByApplicant(applicantId: string): Promise<ContactPerson[]> {
    // This method is deprecated - use getContactPersonsByApplication instead
    console.warn('getContactPersonsByApplicant is deprecated. Use getContactPersonsByApplication instead.');
    const response = await customerApi.api.get(`/contact-persons/application/${applicantId}`);
    return processApiResponse(response);
  },

  async getContactPersonsByApplicantGrouped(applicantId: string): Promise<ContactPersonsByType> {
    // This method is deprecated - use getContactPersonsByApplicationGrouped instead
    console.warn('getContactPersonsByApplicantGrouped is deprecated. Use getContactPersonsByApplicationGrouped instead.');
    const response = await customerApi.api.get(`/contact-persons/application/${applicantId}/grouped`);
    return processApiResponse(response);
  },

  async setPrimaryContact(applicationId: string, contactPersonId: string): Promise<ContactPerson> {
    const response = await customerApi.api.put(`/contact-persons/${contactPersonId}/set-primary`, {
      application_id: applicationId
    });
    return processApiResponse(response);
  },

  async searchContactPersons(searchTerm: string): Promise<ContactPerson[]> {
    const response = await customerApi.api.get(`/contact-persons/search?q=${encodeURIComponent(searchTerm)}`);
    return processApiResponse(response);
  },

  // Helper methods for creating different types of contact persons
  async createPrimaryContact(applicationId: string, contactData: Omit<CreateContactPersonData, 'application_id' | 'is_primary'>): Promise<ContactPerson> {
    return this.createContactPerson({
      ...contactData,
      application_id: applicationId,
      is_primary: true
    });
  },

  async createSecondaryContact(applicationId: string, contactData: Omit<CreateContactPersonData, 'application_id' | 'is_primary'>): Promise<ContactPerson> {
    return this.createContactPerson({
      ...contactData,
      application_id: applicationId,
      is_primary: false
    });
  },


};
