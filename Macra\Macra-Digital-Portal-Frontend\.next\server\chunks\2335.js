exports.id=2335,exports.ids=[2335],exports.modules={2677:(e,t,r)=>{"use strict";r.d(t,{M:()=>l,o:()=>c});var s=r(60687),a=r(43210),o=r(16189),n=r(71773);let i=(0,a.createContext)(void 0),l=()=>{let e=(0,a.useContext)(i);if(!e)throw Error("useLoading must be used within a LoadingProvider");return e},c=({children:e})=>{let[t,r]=(0,a.useState)(!1),[l,c]=(0,a.useState)("Loading..."),d=(0,o.usePathname)();return(0,a.useEffect)(()=>{r(!0),c("Loading page..."),setTimeout(()=>{r(!1)},300)},[d]),(0,s.jsxs)(i.Provider,{value:{isLoading:t,setLoading:e=>{r(e)},showLoader:(e="Loading...")=>{c(e),r(!0)},hideLoader:()=>{r(!1)}},children:[e,t&&(0,s.jsx)("div",{className:"fixed inset-0 bg-white dark:bg-gray-900 bg-opacity-80 dark:bg-opacity-80 backdrop-blur-sm z-50 flex items-center justify-center",children:(0,s.jsx)(n.A,{message:l})})]})}},3063:(e,t,r)=>{Promise.resolve().then(r.bind(r,37590)),Promise.resolve().then(r.bind(r,69270)),Promise.resolve().then(r.bind(r,53521))},12234:(e,t,r)=>{"use strict";r.d(t,{Gf:()=>d,Y0:()=>c,Zl:()=>h,rV:()=>u,uE:()=>l});var s=r(51060),a=r(63523),o=r(51278);let n=process.env.NEXT_PUBLIC_API_URL||"http://localhost:3001",i=(e=n)=>{let t=s.A.create({baseURL:e,timeout:12e4,headers:{"Content-Type":"application/json"}});return t.interceptors.request.use(async e=>{let t=(0,a.c4)();return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e)),t.interceptors.response.use(e=>e,async e=>{let r=e.config;if(e.response?.status===429&&r&&!r._retry){r._retry=!0;let s=e.response.headers["retry-after"],a=s?1e3*parseInt(s):Math.min(1e3*Math.pow(2,r._retryCount||0),1e4);if(r._retryCount=(r._retryCount||0)+1,r._retryCount<=10)return await new Promise(e=>setTimeout(e,a)),t(r)}return e.response?.status===401?((0,o.Wf)(),Promise.reject(Error("Authentication failed. Please log in again."))):(e.response?.status,(e.response?.status===409||e.response?.status===422)&&e.response?.data,"ERR_NETWORK"===e.code||e.message,e.code,Promise.reject(e))}),t},l=i(),c=i(`${n}/auth`),d=i(`${n}/users`),u=i(`${n}/roles`),h=i(`${n}/audit-trail`)},21893:()=>{},39659:(e,t,r)=>{"use strict";r.d(t,{D:()=>i,N:()=>n});var s=r(60687),a=r(43210);let o=(0,a.createContext)();function n({children:e}){let[t,r]=(0,a.useState)("system"),[n,i]=(0,a.useState)("light"),[l,c]=(0,a.useState)(!1),d=e=>{r(e),l&&localStorage.setItem("theme",e)};return l?(0,s.jsx)(o.Provider,{value:{theme:t,resolvedTheme:n,setTheme:d,toggleTheme:()=>{d("light"===n?"dark":"light")}},children:e}):(0,s.jsx)(o.Provider,{value:{theme:"light",resolvedTheme:"light",setTheme:()=>{},toggleTheme:()=>{}},children:e})}function i(){let e=(0,a.useContext)(o);if(!e)throw Error("useTheme must be used within a ThemeProvider");return e}},44314:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\ClientWrapper.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\components\\ClientWrapper.tsx","default")},51278:(e,t,r)=>{"use strict";r.d(t,{Hm:()=>o,Wf:()=>i,_4:()=>l,zp:()=>c});var s=r(15659),a=r(63523);let o=e=>{if(!e)return!0;try{let t=JSON.parse(atob(e.split(".")[1])),r=Math.floor(Date.now()/1e3);return t.exp<r}catch(e){return!0}},n=()=>{let e=(0,a.c4)(),t=s.A.get("auth_user");if(!e||o(e)||!t)return!1;try{return JSON.parse(t),!0}catch(e){return!1}},i=()=>{(0,a.QF)(),s.A.remove("auth_token"),s.A.remove("auth_user")},l=(e=6e4)=>setInterval(()=>{n()||i()},e),c=e=>e?.data?.meta!==void 0&&e.data.data?e.data:e?.data?.data?e.data.data:(e.data,e.data)},53521:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var s=r(60687),a=r(43210);let o=({children:e})=>((0,a.useEffect)(()=>{let e=()=>{let e=document.querySelectorAll("img[data-src]"),t=new IntersectionObserver(e=>{e.forEach(e=>{if(e.isIntersecting){let r=e.target;r.src=r.dataset.src||"",r.classList.remove("lazy"),t.unobserve(r)}})});e.forEach(e=>t.observe(e))},t=()=>{document.querySelectorAll(".nav-item, .dashboard-card, .btn-primary").forEach(e=>{e.style.willChange="transform, background-color"}),document.documentElement.style.scrollBehavior="smooth"},r=setTimeout(()=>{e(),t()},100);return()=>{clearTimeout(r),document.querySelectorAll(".nav-item, .dashboard-card, .btn-primary").forEach(e=>{e.style.willChange="auto"})}},[]),(0,s.jsx)(s.Fragment,{children:e}))},61135:()=>{},63213:(e,t,r)=>{"use strict";r.d(t,{A:()=>c,O:()=>d});var s=r(60687),a=r(43210),o=r(63443),n=r(15659),i=r(51278);let l=(0,a.createContext)(void 0),c=()=>{let e=(0,a.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},d=({children:e})=>{let[t,r]=(0,a.useState)(null),[c,d]=(0,a.useState)(null),[u,h]=(0,a.useState)(!0),[m,f]=(0,a.useState)(!1);(0,a.useEffect)(()=>{f(!0)},[]),(0,a.useEffect)(()=>{if(!m||!t||!c)return;let e=(0,i._4)(3e5);return()=>{clearInterval(e)}},[m,t,c]),(0,a.useEffect)(()=>{m&&(()=>{let e=n.A.get("auth_token"),t=n.A.get("auth_user");if(e&&t)try{let s=JSON.parse(t),a={...s,isAdmin:s.roles?.includes("administrator")||s.isAdmin||!1,isCustomer:s.roles?.includes("customer")};(0,i.Hm)(e)?(n.A.remove("auth_token"),n.A.remove("auth_user"),o.y.clearAuthToken()):(d(e),r(a),o.y.setAuthToken(e))}catch(e){n.A.remove("auth_token"),n.A.remove("auth_user"),o.y.clearAuthToken()}h(!1)})()},[m]);let p=async(e,t)=>{try{let s=await o.y.login({email:e,password:t});if(!s||!s.user||!s.access_token)throw Error("Invalid response from authentication service");let a=Array.isArray(s.user.roles)?s.user.roles:[],i={...s.user,roles:a,isAdmin:a.includes("administrator"),isCustomer:a.includes("customer")};if(d(s.access_token),r(i),n.A.set("auth_token",s.access_token,{expires:1}),n.A.set("auth_user",JSON.stringify(i),{expires:1}),o.y.setAuthToken(s.access_token),s.user)return{requiresTwoFactor:!0,userId:s.user.user_id,user:s.user}}catch(e){throw e}},g=async(e,t)=>{try{let s=Array.isArray(t.roles)?t.roles:[],a={...t,roles:s,isAdmin:s.includes("administrator"),isCustomer:s.includes("customer")};d(e),r(a),n.A.set("auth_token",e,{expires:1}),n.A.set("auth_user",JSON.stringify(a),{expires:1}),o.y.setAuthToken(e)}catch(e){throw e}},y=async e=>{await o.y.register(e)};return(0,s.jsx)(l.Provider,{value:{user:t,token:c,login:p,completeTwoFactorLogin:g,register:y,logout:()=>{r(null),d(null),n.A.remove("auth_token"),n.A.remove("auth_user"),o.y.clearAuthToken()},updateUser:e=>{let t=[];e.roles&&(t=e.roles.map(e=>"string"==typeof e?e:e.name||e.role_name||"unknown"));let s={user_id:e.user_id,email:e.email,first_name:e.first_name,last_name:e.last_name,middle_name:e.middle_name,phone:e.phone,status:e.status,profile_image:e.profile_image,roles:t,isAdmin:t.includes("administrator")||e.isAdmin||!1,isCustomer:t.includes("customer")};r(s),n.A.set("auth_user",JSON.stringify(s),{expires:1})},loading:u||!m,isAuthenticated:m&&!!t&&!!c},children:e})}},63443:(e,t,r)=>{"use strict";r.d(t,{y:()=>n});var s=r(12234),a=r(51278);class o{constructor(){this.api=s.Y0}async login(e){let t=await this.api.post("/login",e),r=(0,a.zp)(t);if(!r||0===Object.keys(r).length)throw Error("Authentication failed - invalid credentials");if(!r.access_token||!r.user)throw Error("Authentication failed - incomplete response");return r}async register(e){return(await this.api.post("/register",e)).data}async forgotPassword(e){return(await this.api.post("/forgot-password",e)).data}async resetPassword(e){try{return(await this.api.post("/reset-password",e)).data}catch(e){throw e}}async verify2FA(e){try{return(await this.api.post("/verify-2fa",e)).data}catch(e){throw e}}async verifyEmail(e){return(await this.api.post("/verify-email",e)).data}async setupTwoFactorAuth(e){return(await this.api.post("/setup-2fa",e)).data}async verifyTwoFactorCode(e){return(await this.api.post("/verify-2fa",e)).data}async generateTwoFactorCode(e,t){return(await this.api.post("/generate-2fa",{user_id:e,action:t})).data}async refreshToken(){return(await this.api.post("/refresh")).data}setAuthToken(e){}getAuthToken(){return null}clearAuthToken(){}}let n=new o},63523:(e,t,r)=>{"use strict";r.d(t,{QF:()=>a,c4:()=>s});let s=()=>null,a=()=>{}},65845:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},66082:()=>{},66111:(e,t,r)=>{Promise.resolve().then(r.bind(r,5625)),Promise.resolve().then(r.bind(r,44314)),Promise.resolve().then(r.bind(r,81103))},69270:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var s=r(60687),a=r(63213),o=r(2677),n=r(39659),i=r(87911),l=r(71773),c=r(43210);function d({children:e,fallback:t=null}){let[r,a]=(0,c.useState)(!1);return r?(0,s.jsx)(s.Fragment,{children:e}):(0,s.jsx)(s.Fragment,{children:t})}function u({children:e}){return(0,s.jsx)(d,{fallback:(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center",children:(0,s.jsx)(l.A,{message:"Initializing application..."})}),children:(0,s.jsx)(n.N,{children:(0,s.jsx)(o.o,{children:(0,s.jsx)(i.t,{children:(0,s.jsx)(a.O,{children:e})})})})})}},71773:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(60687),a=r(30474);let o=({message:e="Loading..."})=>(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("div",{className:"relative w-20 h-20 mx-auto",children:[(0,s.jsxs)("svg",{className:"absolute inset-0 animate-spin",viewBox:"0 0 50 50",fill:"none",children:[(0,s.jsx)("defs",{children:(0,s.jsxs)("linearGradient",{id:"fadeGradient",x1:"0%",y1:"0%",x2:"100%",y2:"0%",children:[(0,s.jsx)("stop",{offset:"0%",stopColor:"#dc2626",stopOpacity:"0"}),(0,s.jsx)("stop",{offset:"20%",stopColor:"#dc2626",stopOpacity:"1"}),(0,s.jsx)("stop",{offset:"80%",stopColor:"#dc2626",stopOpacity:"1"}),(0,s.jsx)("stop",{offset:"100%",stopColor:"#dc2626",stopOpacity:"0"})]})}),(0,s.jsx)("circle",{cx:"25",cy:"25",r:"20",stroke:"rgba(255, 255, 255, 0.0)",strokeWidth:"2"}),(0,s.jsx)("circle",{cx:"25",cy:"25",r:"20",stroke:"url(#fadeGradient)",strokeWidth:"1",strokeDasharray:"70",strokeDashoffset:"10",fill:"none"})]}),(0,s.jsx)(a.default,{src:"/images/macra-logo.png",alt:"MACRA Logo",width:40,height:40,className:"object-contain absolute inset-0 h-10 w-10 m-auto animate-fadeLoop"})]}),(0,s.jsx)("p",{className:"mt-4 text-gray-600",children:e})]})},79413:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},81103:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\LoadingOptimizer.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\components\\LoadingOptimizer.tsx","default")},87911:(e,t,r)=>{"use strict";r.d(t,{t:()=>i,d:()=>l});var s=r(60687),a=r(43210);let o=({message:e,type:t,duration:r=5e3,onClose:o})=>{let[n,i]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{let e=setTimeout(()=>{i(!1),setTimeout(o,300)},r);return()=>clearTimeout(e)},[r,o]),(0,s.jsx)("div",{className:`relative max-w-sm w-full border rounded-lg p-4 shadow-lg transition-all duration-300 ${n?"opacity-100 translate-y-0":"opacity-0 -translate-y-2"} ${(()=>{switch(t){case"success":return"bg-green-50 border-green-200 text-green-800 dark:bg-green-900/20 dark:border-green-800 dark:text-green-300";case"error":return"bg-red-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-300";case"warning":return"bg-yellow-50 border-yellow-200 text-yellow-800 dark:bg-yellow-900/20 dark:border-yellow-800 dark:text-yellow-300";case"info":return"bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-300";default:return"bg-gray-50 border-gray-200 text-gray-800 dark:bg-gray-900/20 dark:border-gray-800 dark:text-gray-300"}})()}`,children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("i",{className:`${(()=>{switch(t){case"success":return"ri-check-circle-line";case"error":return"ri-error-warning-line";case"warning":return"ri-alert-line";default:return"ri-information-line"}})()} text-lg`})}),(0,s.jsx)("div",{className:"ml-3 flex-1",children:(0,s.jsx)("p",{className:"text-sm font-medium",children:e})}),(0,s.jsx)("div",{className:"ml-4 flex-shrink-0",children:(0,s.jsxs)("button",{type:"button",onClick:()=>{i(!1),setTimeout(o,300)},className:"inline-flex rounded-md p-1.5 hover:bg-black/5 dark:hover:bg-white/5 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-current",children:[(0,s.jsx)("span",{className:"sr-only",children:"Dismiss"}),(0,s.jsx)("i",{className:"ri-close-line text-sm"})]})})]})})};r(21893);let n=(0,a.createContext)(void 0),i=({children:e})=>{let[t,r]=(0,a.useState)([]),i=(e,t,s)=>{let a={id:Math.random().toString(36).substr(2,9),message:e,type:t,duration:s};r(e=>[...e,a])},l=e=>{r(t=>t.filter(t=>t.id!==e))};return(0,s.jsxs)(n.Provider,{value:{showToast:i,showSuccess:(e,t)=>{i(e,"success",t)},showError:(e,t)=>{i(e,"error",t)},showWarning:(e,t)=>{i(e,"warning",t)},showInfo:(e,t)=>{i(e,"info",t)}},children:[e,(0,s.jsx)("div",{className:"toast-container",children:t.map((e,t)=>(0,s.jsx)("div",{className:"toast-wrapper","data-index":t,"data-toast-index":t,children:(0,s.jsx)(o,{message:e.message,type:e.type,duration:e.duration,onClose:()=>l(e.id)})},e.id))})]})},l=()=>{let e=(0,a.useContext)(n);if(!e)throw Error("useToast must be used within a ToastProvider");return e}},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(37413),a=r(45481),o=r.n(a),n=r(5625),i=r(44314),l=r(81103);function c({children:e}){return(0,s.jsxs)("html",{lang:"en",className:o().variable,suppressHydrationWarning:!0,children:[(0,s.jsxs)("head",{children:[(0,s.jsx)("script",{dangerouslySetInnerHTML:{__html:`
              (function() {
                try {
                  const savedTheme = localStorage.getItem('theme');
                  const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

                  if (
                    savedTheme === 'dark' || 
                    (savedTheme === 'system' && systemPrefersDark) || 
                    (!savedTheme && systemPrefersDark)
                  ) {
                    document.documentElement.classList.add('dark');
                  } else {
                    document.documentElement.classList.remove('dark');
                  }
                } catch (e) {
                  document.documentElement.classList.remove('dark');
                }
              })();
            `}}),(0,s.jsx)("link",{rel:"preconnect",href:"https://cdnjs.cloudflare.com"}),(0,s.jsx)("link",{rel:"stylesheet",href:"https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"}),(0,s.jsx)("link",{rel:"stylesheet",href:"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css",integrity:"sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==",crossOrigin:"anonymous",referrerPolicy:"no-referrer"}),(0,s.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),(0,s.jsx)("meta",{httpEquiv:"X-UA-Compatible",content:"IE=edge"})]}),(0,s.jsxs)("body",{className:`${o().className} antialiased`,suppressHydrationWarning:!0,children:[(0,s.jsx)(l.default,{children:(0,s.jsx)(i.default,{children:e})}),(0,s.jsx)(n.Toaster,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,style:{background:"#10b981",color:"#fff"}},error:{duration:5e3,style:{background:"#ef4444",color:"#fff"}}}})]})]})}r(61135),r(66082)}};