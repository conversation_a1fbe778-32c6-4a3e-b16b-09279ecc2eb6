"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const user_entity_1 = require("../entities/user.entity");
const role_entity_1 = require("../entities/role.entity");
const bcrypt = __importStar(require("bcryptjs"));
const nestjs_paginate_1 = require("nestjs-paginate");
const pagination_interface_1 = require("../common/interfaces/pagination.interface");
const user_constants_1 = require("../common/constants/user.constants");
const user_types_1 = require("../common/types/user.types");
let UsersService = class UsersService {
    usersRepository;
    rolesRepository;
    constructor(usersRepository, rolesRepository) {
        this.usersRepository = usersRepository;
        this.rolesRepository = rolesRepository;
    }
    async findByEmail(email) {
        return this.usersRepository.findOne({
            where: { email },
            relations: [...user_constants_1.UserConstants.RELATIONS.BASIC],
        });
    }
    async findById(id) {
        return this.usersRepository.findOne({
            where: { user_id: id },
            relations: [...user_constants_1.UserConstants.RELATIONS.BASIC],
        });
    }
    async create(createUserDto) {
        if (!(0, user_types_1.hasRequiredUserFields)(createUserDto)) {
            throw new common_1.BadRequestException('Missing required user fields');
        }
        if (!(0, user_types_1.isValidEmail)(createUserDto.email)) {
            throw new common_1.BadRequestException('Invalid email format');
        }
        await this.validateUserDoesNotExist(createUserDto.email);
        const roles = await this.validateAndGetRoles(createUserDto.role_ids);
        const hashedPassword = await this.hashPassword(createUserDto.password);
        const user = this.usersRepository.create({
            ...createUserDto,
            password: hashedPassword,
            status: createUserDto.status || user_entity_1.UserStatus.ACTIVE,
            roles,
        });
        return this.usersRepository.save(user);
    }
    async validateUserDoesNotExist(email) {
        const existingUser = await this.findByEmail(email);
        if (existingUser) {
            throw new common_1.ConflictException(user_constants_1.UserMessages.USER_ALREADY_EXISTS);
        }
    }
    async validateAndGetRoles(roleIds) {
        if (user_constants_1.UserUtils.validateRoleIds(roleIds)) {
            const foundRoles = await this.rolesRepository.find({
                where: roleIds.map(id => ({ role_id: id }))
            });
            if (foundRoles.length !== roleIds.length) {
                throw new common_1.NotFoundException(user_constants_1.UserMessages.ROLES_NOT_FOUND);
            }
            return foundRoles;
        }
        else {
            const defaultRole = await this.rolesRepository.findOne({
                where: { name: role_entity_1.RoleName.CUSTOMER },
            });
            return defaultRole ? [defaultRole] : [];
        }
    }
    async hashPassword(password) {
        return bcrypt.hash(password, user_constants_1.UserConstants.PASSWORD_HASH_ROUNDS);
    }
    async validatePassword(plainPassword, hashedPassword) {
        return bcrypt.compare(plainPassword, hashedPassword);
    }
    async updateLastLogin(userId) {
        await this.usersRepository.update(userId, {
            last_login: new Date(),
        });
    }
    async updatePassword(userId, newPassword) {
        const hashedPassword = await this.hashPassword(newPassword);
        await this.usersRepository.update(userId, {
            password: hashedPassword,
        });
    }
    async setTwoFactorCode(userId, code, expiresAt) {
        await this.usersRepository.update(userId, {
            two_factor_code: code,
            two_factor_next_verification: expiresAt,
            two_factor_temp: null,
        });
    }
    async setTempTwoFactorCode(userId, secret, expiresAt) {
        await this.usersRepository.update(userId, {
            two_factor_temp: secret,
            two_factor_enabled: false,
            two_factor_next_verification: expiresAt,
        });
    }
    async clearTempTwoFactorCode(userId) {
        await this.usersRepository.update(userId, {
            two_factor_temp: null,
        });
    }
    async clearTwoFactorCode(userId) {
        await this.usersRepository.update(userId, {
            two_factor_code: null,
            two_factor_temp: null,
        });
    }
    async setTwoFactorCodeTempReset(userId, secret, code, expiresAt) {
        await this.usersRepository.update(userId, {
            two_factor_code: code,
            two_factor_temp: secret,
            two_factor_next_verification: expiresAt,
        });
    }
    async disableTwoFactor(userId) {
        await this.usersRepository.update(userId, {
            two_factor_enabled: false,
            two_factor_code: null,
            two_factor_temp: null,
        });
    }
    async enableTwoFactor(userId, expiresAt) {
        const user = await this.usersRepository.findOne({ where: { user_id: userId } });
        await this.usersRepository.update(userId, {
            two_factor_enabled: true,
            two_factor_next_verification: expiresAt,
            two_factor_temp: null,
            email_verified_at: user?.email_verified_at || new Date(),
        });
    }
    async verifyEmail(userId) {
        await this.usersRepository.update(userId, {
            email_verified_at: new Date(),
        });
    }
    async updateStatus(userId, status) {
        await this.usersRepository.update(userId, { status });
    }
    async findAll(query) {
        console.log('UsersService: findAll called with query:', JSON.stringify(query, null, 2));
        const queryBuilder = this.usersRepository
            .createQueryBuilder('user')
            .leftJoinAndSelect('user.roles', 'roles')
            .leftJoinAndSelect('user.department', 'department');
        if (query.filter) {
            if (query.filter.department) {
                queryBuilder.andWhere('user.department_id = :departmentId', {
                    departmentId: query.filter.department
                });
            }
            if (query.filter.role) {
                queryBuilder.andWhere('roles.role_id = :roleId', {
                    roleId: query.filter.role
                });
            }
            if (query.filter.status) {
                queryBuilder.andWhere('user.status = :status', {
                    status: query.filter.status
                });
            }
        }
        const config = user_constants_1.UserUtils.createPaginationConfig();
        console.log('UsersService: Using config:', JSON.stringify(config, null, 2));
        const result = await (0, nestjs_paginate_1.paginate)(query, queryBuilder, config);
        console.log('UsersService: Raw pagination result:', JSON.stringify(result, null, 2));
        const transformedResult = pagination_interface_1.PaginationTransformer.transform(result);
        console.log('UsersService: Transformed result meta:', JSON.stringify(transformedResult.meta, null, 2));
        return transformedResult;
    }
    async update(userId, updateUserDto) {
        if (!(0, user_types_1.isValidUserId)(userId)) {
            throw new common_1.BadRequestException('Invalid user ID format');
        }
        if (updateUserDto.email && !(0, user_types_1.isValidEmail)(updateUserDto.email)) {
            throw new common_1.BadRequestException('Invalid email format');
        }
        const user = await this.validateUserExists(userId);
        const roles = await this.validateRolesForUpdate(updateUserDto.role_ids);
        const hashedPassword = updateUserDto.password
            ? await this.hashPassword(updateUserDto.password)
            : undefined;
        const { role_ids, ...updateDataWithoutRoles } = updateUserDto;
        const updateData = {
            ...updateDataWithoutRoles,
            ...(hashedPassword && { password: hashedPassword }),
        };
        const safeUpdateData = user_constants_1.UserUtils.sanitizeUpdateData(updateData);
        Object.assign(user, safeUpdateData);
        if (roles) {
            user.roles = roles;
        }
        await this.usersRepository.save(user);
        return this.getUpdatedUser(userId);
    }
    async validateUserExists(userId) {
        const user = await this.findById(userId);
        if (!user) {
            throw new common_1.NotFoundException(user_constants_1.UserMessages.USER_NOT_FOUND);
        }
        return user;
    }
    async validateRolesForUpdate(roleIds) {
        if (!user_constants_1.UserUtils.validateRoleIds(roleIds)) {
            return undefined;
        }
        const foundRoles = await this.rolesRepository.find({
            where: roleIds.map(id => ({ role_id: id }))
        });
        if (foundRoles.length !== roleIds.length) {
            throw new common_1.NotFoundException(user_constants_1.UserMessages.ROLES_NOT_FOUND);
        }
        return foundRoles;
    }
    async getUpdatedUser(userId) {
        const updatedUser = await this.findById(userId);
        if (!updatedUser) {
            throw new common_1.NotFoundException(user_constants_1.UserMessages.USER_NOT_FOUND_AFTER_UPDATE);
        }
        return updatedUser;
    }
    async delete(userId) {
        const user = await this.findById(userId);
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        await this.usersRepository.softDelete(userId);
    }
    async updateProfile(userId, updateProfileDto) {
        const user = await this.validateUserExists(userId);
        if (user_constants_1.UserUtils.isEmailChanged(user.email, updateProfileDto.email)) {
            await this.validateEmailNotTaken(updateProfileDto.email);
        }
        await this.usersRepository.update(userId, updateProfileDto);
        return this.getUpdatedUser(userId);
    }
    async validateEmailNotTaken(email) {
        const existingUser = await this.findByEmail(email);
        if (existingUser) {
            throw new common_1.ConflictException(user_constants_1.UserMessages.EMAIL_ALREADY_TAKEN);
        }
    }
    async changePassword(userId, changePasswordDto) {
        const user = await this.validateUserExists(userId);
        const isCurrentPasswordValid = await this.validatePassword(changePasswordDto.current_password, user.password);
        if (!isCurrentPasswordValid) {
            throw new common_1.BadRequestException(user_constants_1.UserMessages.CURRENT_PASSWORD_INCORRECT);
        }
        if (changePasswordDto.new_password !== changePasswordDto.confirm_password) {
            throw new common_1.BadRequestException(user_constants_1.UserMessages.PASSWORD_MISMATCH);
        }
        await this.updatePassword(userId, changePasswordDto.new_password);
        return { message: user_constants_1.UserMessages.PASSWORD_CHANGED_SUCCESS };
    }
    async uploadAvatar(userId, file) {
        console.log('UsersService: uploadAvatar called', { userId, file: file ? file.originalname : 'no file' });
        if (!(0, user_types_1.isValidUserId)(userId)) {
            throw new common_1.BadRequestException('Invalid user ID format');
        }
        await this.validateUserExists(userId);
        if (!file) {
            throw new common_1.BadRequestException(user_constants_1.UserMessages.NO_FILE_UPLOADED);
        }
        console.log('UsersService: File details', {
            originalname: file.originalname,
            mimetype: file.mimetype,
            size: file.size,
            filename: file.filename,
            path: file.path
        });
        try {
            const base64Image = user_constants_1.UserUtils.createBase64Image(file);
            console.log('UsersService: Updating user with base64 image');
            await this.usersRepository.update(userId, {
                profile_image: base64Image,
            });
            console.log('UsersService: Avatar upload successful');
            return this.getUpdatedUser(userId);
        }
        catch (error) {
            console.error('UsersService: Error uploading avatar', error);
            throw new common_1.BadRequestException(user_constants_1.UserMessages.AVATAR_UPLOAD_FAILED);
        }
    }
    async removeAvatar(userId) {
        await this.validateUserExists(userId);
        await this.usersRepository.update(userId, {
            profile_image: null,
        });
        return this.getUpdatedUser(userId);
    }
    async mailUser(userEmail) {
        const user = await this.findByEmail(userEmail);
        if (!user) {
            throw new common_1.NotFoundException(user_constants_1.UserMessages.USER_NOT_FOUND);
        }
        return { message: user_constants_1.UserMessages.EMAIL_SENT_SUCCESS };
    }
};
exports.UsersService = UsersService;
exports.UsersService = UsersService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(1, (0, typeorm_1.InjectRepository)(role_entity_1.Role)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], UsersService);
//# sourceMappingURL=users.service.js.map