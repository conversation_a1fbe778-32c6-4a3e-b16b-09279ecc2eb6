{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/LogoutButton.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { useRouter, usePathname } from 'next/navigation';\r\nimport { useAuth } from '../contexts/AuthContext';\r\n\r\ninterface LogoutButtonProps {\r\n  variant?: 'primary' | 'secondary' | 'text' | 'icon';\r\n  size?: 'sm' | 'md' | 'lg';\r\n  className?: string;\r\n  showConfirmation?: boolean;\r\n  redirectTo?: string;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nexport default function LogoutButton({\r\n  variant = 'primary',\r\n  size = 'md',\r\n  className = '',\r\n  showConfirmation = true,\r\n  redirectTo = '/auth/login',\r\n  children,\r\n}: LogoutButtonProps) {\r\n  const [isLoggingOut, setIsLoggingOut] = useState(false);\r\n  const [showConfirm, setShowConfirm] = useState(false);\r\n  const { logout, user } = useAuth();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  const handleLogout = async () => {\r\n    if (showConfirmation && !showConfirm) {\r\n      setShowConfirm(true);\r\n      return;\r\n    }\r\n\r\n    setIsLoggingOut(true);\r\n    \r\n    try {\r\n      // Call logout from context\r\n      logout();\r\n      \r\n      // Small delay to ensure cleanup is complete\r\n      await new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n      if(pathname.includes('customer')){\r\n        // Redirect to specified page\r\n        router.push('/customer/auth/login');\r\n      } else {\r\n        router.push('/auth/login');\r\n      }\r\n      \r\n\r\n      \r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n    } finally {\r\n      setIsLoggingOut(false);\r\n      setShowConfirm(false);\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    setShowConfirm(false);\r\n  };\r\n\r\n  // Base styles for different variants\r\n  const getVariantStyles = () => {\r\n    switch (variant) {\r\n      case 'primary':\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n      case 'secondary':\r\n        return 'bg-white hover:bg-gray-50 text-red-600 border border-red-600';\r\n      case 'text':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none';\r\n      case 'icon':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none p-2';\r\n      default:\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n    }\r\n  };\r\n\r\n  // Size styles\r\n  const getSizeStyles = () => {\r\n    switch (size) {\r\n      case 'sm':\r\n        return 'px-3 py-1.5 text-sm';\r\n      case 'md':\r\n        return 'px-4 py-2 text-base';\r\n      case 'lg':\r\n        return 'px-6 py-3 text-lg';\r\n      default:\r\n        return 'px-4 py-2 text-base';\r\n    }\r\n  };\r\n\r\n  const baseStyles = 'inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\r\n  \r\n  const buttonStyles = `${baseStyles} ${getVariantStyles()} ${variant !== 'icon' ? getSizeStyles() : ''} ${className}`;\r\n\r\n  // Confirmation dialog\r\n  if (showConfirm) {\r\n    return (\r\n      <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n        <div className=\"bg-white rounded-lg p-6 max-w-sm mx-4 shadow-xl\">\r\n          <div className=\"flex items-center mb-4\">\r\n            <div className=\"flex-shrink-0\">\r\n              <svg className=\"h-6 w-6 text-red-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z\" />\r\n              </svg>\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <h3 className=\"text-lg font-medium text-gray-900\">Confirm Logout</h3>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"mb-4\">\r\n            <p className=\"text-sm text-gray-500\">\r\n              Are you sure you want to logout{user?.first_name ? `, ${user.first_name}` : ''}? You will need to login again to access your account.\r\n            </p>\r\n          </div>\r\n          \r\n          <div className=\"flex space-x-3\">\r\n            <button\r\n              onClick={handleLogout}\r\n              disabled={isLoggingOut}\r\n              className=\"flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 disabled:opacity-50\"\r\n            >\r\n              {isLoggingOut ? 'Logging out...' : 'Yes, Logout'}\r\n            </button>\r\n            <button\r\n              onClick={handleCancel}\r\n              className=\"flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200\"\r\n            >\r\n              Cancel\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <button\r\n      onClick={handleLogout}\r\n      disabled={isLoggingOut}\r\n      className={buttonStyles}\r\n      title=\"Logout\"\r\n    >\r\n      {children || (\r\n        <>\r\n          {variant === 'icon' ? (\r\n            <svg className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n            </svg>\r\n          ) : (\r\n            <>\r\n              <svg className=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n              </svg>\r\n              {isLoggingOut ? 'Logging out...' : 'Logout'}\r\n            </>\r\n          )}\r\n        </>\r\n      )}\r\n    </button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAee,SAAS,aAAa,EACnC,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,mBAAmB,IAAI,EACvB,aAAa,aAAa,EAC1B,QAAQ,EACU;IAClB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe;QACnB,IAAI,oBAAoB,CAAC,aAAa;YACpC,eAAe;YACf;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,2BAA2B;YAC3B;YAEA,4CAA4C;YAC5C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,IAAG,SAAS,QAAQ,CAAC,aAAY;gBAC/B,6BAA6B;gBAC7B,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QAIF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,gBAAgB;YAChB,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,eAAe;IACjB;IAEA,qCAAqC;IACrC,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,cAAc;IACd,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa;IAEnB,MAAM,eAAe,GAAG,WAAW,CAAC,EAAE,mBAAmB,CAAC,EAAE,YAAY,SAAS,kBAAkB,GAAG,CAAC,EAAE,WAAW;IAEpH,sBAAsB;IACtB,IAAI,aAAa;QACf,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAAuB,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CAC3E,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;;;;;;;;;;;;kCAItD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;gCAAwB;gCACH,MAAM,aAAa,CAAC,EAAE,EAAE,KAAK,UAAU,EAAE,GAAG;gCAAG;;;;;;;;;;;;kCAInF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,eAAe,mBAAmB;;;;;;0CAErC,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,UAAU;QACV,WAAW;QACX,OAAM;kBAEL,0BACC;sBACG,YAAY,uBACX,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,SAAQ;gBAAY,QAAO;0BAC9D,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;qCAGvE;;kCACE,8OAAC;wBAAI,WAAU;wBAAe,MAAK;wBAAO,SAAQ;wBAAY,QAAO;kCACnE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAEtE,eAAe,mBAAmB;;;;;;;;;AAOjD", "debugId": null}}, {"offset": {"line": 262, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/customer/CustomerLayout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { usePathname, useRouter } from 'next/navigation';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useLoading } from '@/contexts/LoadingContext';\r\nimport LogoutButton from '../LogoutButton';\r\n\r\ninterface BreadcrumbItem {\r\n  label: string;\r\n  href?: string;\r\n}\r\n\r\ninterface CustomerLayoutProps {\r\n  children: React.ReactNode;\r\n  breadcrumbs?: BreadcrumbItem[];\r\n}\r\n\r\nconst CustomerLayout: React.FC<CustomerLayoutProps> = ({ children, breadcrumbs }) => {\r\n  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);\r\n  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);\r\n  const pathname = usePathname();\r\n  const router = useRouter();\r\n  const { user, logout } = useAuth();\r\n  const { showLoader } = useLoading();\r\n\r\n  // Memoize navigation items to prevent unnecessary re-renders\r\n  const navigationItems = useMemo(() => [\r\n    {\r\n      name: 'Dashboard',\r\n      href: '/customer',\r\n      icon: 'ri-dashboard-line',\r\n      current: pathname === '/customer'\r\n    },\r\n    {\r\n      name: 'My Licenses',\r\n      href: '/customer/my-licenses',\r\n      icon: 'ri-key-line',\r\n      current: pathname === '/customer/my-licenses'\r\n    },\r\n    {\r\n      name: 'New Applications',\r\n      href: '/customer/applications',\r\n      icon: 'ri-file-list-3-line',\r\n      current: pathname === '/customer/applications'\r\n    },\r\n    {\r\n      name: 'Payments',\r\n      href: '/customer/payments',\r\n      icon: 'ri-bank-card-line',\r\n      current: pathname === '/customer/payments'\r\n    },\r\n    {\r\n      name: 'Documents',\r\n      href: '/customer/documents',\r\n      icon: 'ri-file-text-line',\r\n      current: pathname === '/customer/documents'\r\n    },\r\n    {\r\n      name: 'Procurement',\r\n      href: '/customer/procurement',\r\n      icon: 'ri-auction-line',\r\n      current: pathname === '/customer/procurement'\r\n    },\r\n    {\r\n      name: 'Request Resource',\r\n      href: '/customer/resources',\r\n      icon: 'ri-hand-heart-line',\r\n      current: pathname === '/customer/resources'\r\n    }\r\n  ], [pathname]);\r\n\r\n  const supportItems = useMemo(() => [\r\n    {\r\n      name: 'Data Protection',\r\n      href: '/customer/data-protection',\r\n      icon: 'ri-shield-keyhole-line'\r\n    },\r\n    {\r\n      name: 'Help Center',\r\n      href: '/customer/help',\r\n      icon: 'ri-question-line'\r\n    }\r\n  ], []);\r\n\r\n  // Prefetch customer pages on mount for faster navigation\r\n  useEffect(() => {\r\n    const prefetchPages = () => {\r\n      const customerPages = [\r\n        '/customer',\r\n        '/customer/applications',\r\n        '/customer/applications/standards',\r\n        '/customer/payments',\r\n        '/customer/my-licenses',\r\n        '/customer/procurement',\r\n        '/customer/profile',\r\n        '/customer/data-protection',\r\n        '/customer/resources',\r\n        '/customer/help'\r\n      ];\r\n\r\n      customerPages.forEach(page => {\r\n        router.prefetch(page);\r\n      });\r\n    };\r\n\r\n    // Delay prefetching to not interfere with initial page load\r\n    const timer = setTimeout(prefetchPages, 1000);\r\n    return () => clearTimeout(timer);\r\n  }, [router]);\r\n\r\n  const toggleMobileSidebar = useCallback(() => {\r\n    setIsMobileSidebarOpen(!isMobileSidebarOpen);\r\n  }, [isMobileSidebarOpen]);\r\n\r\n  const handleNavClick = useCallback((href: string, name: string) => {\r\n    const pageMessages: { [key: string]: string } = {\r\n      '/customer': 'Loading Dashboard...',\r\n      '/customer/my-licenses': 'Loading My Licenses...',\r\n      '/customer/applications': 'Loading Applications...',\r\n      '/customer/applications/apply/': 'Loading Standards License Options...',\r\n      '/customer/payments': 'Loading Payments...',\r\n      '/customer/documents': 'Loading Documents...',\r\n      '/customer/procurement': 'Loading Procurement...',\r\n      '/customer/resources': 'Loading Resources...',\r\n      '/customer/data-protection': 'Loading Data Protection...',\r\n      '/customer/help': 'Loading Help Center...',\r\n      '/customer/profile': 'Loading Profile...',\r\n      '/customer/settings': 'Loading Settings...'\r\n    };\r\n\r\n    const message = pageMessages[href] || `Loading ${name}...`;\r\n    showLoader(message);\r\n    setIsMobileSidebarOpen(false);\r\n  }, [showLoader]);\r\n\r\n  const handleNavHover = useCallback((href: string) => {\r\n    // Prefetch on hover for instant navigation\r\n    router.prefetch(href);\r\n  }, [router]);\r\n\r\n  const toggleUserDropdown = useCallback(() => {\r\n    setIsUserDropdownOpen(!isUserDropdownOpen);\r\n  }, [isUserDropdownOpen]);\r\n\r\n  return (\r\n    <div className=\"flex h-screen overflow-hidden\">\r\n      {/* Mobile sidebar overlay */}\r\n      {isMobileSidebarOpen && (\r\n        <div \r\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden\"\r\n          onClick={() => setIsMobileSidebarOpen(false)}\r\n        />\r\n      )}\r\n\r\n      {/* Sidebar */}\r\n      <aside className={`\r\n        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out gpu-accelerated\r\n        ${isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}\r\n      `}>\r\n        <div className=\"flex flex-col h-full\">\r\n          {/* Logo/Header */}\r\n          <div className=\"h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700\">\r\n            <div className=\"flex items-center\">\r\n              <Image \r\n                src=\"/images/macra-logo.png\" \r\n                alt=\"MACRA Logo\" \r\n                className=\"max-h-12 w-auto\" \r\n                width={120}\r\n                height={48}\r\n                priority\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Navigation */}\r\n          <nav className=\"mt-6 px-4 side-nav\">\r\n            {/* Main Navigation */}\r\n            <div className=\"space-y-1\">\r\n              {navigationItems.map((item) => (\r\n                <Link\r\n                  key={item.name}\r\n                  href={item.href}\r\n                  onClick={() => handleNavClick(item.href, item.name)}\r\n                  onMouseEnter={() => handleNavHover(item.href)}\r\n                  className={`\r\n                    flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated\r\n                    ${item.current\r\n                      ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-r-2 border-red-600 dark:border-red-400'\r\n                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100'\r\n                    }\r\n                  `}\r\n                >\r\n                  <div className={`w-5 h-5 flex items-center justify-center mr-3 ${item.current ? 'text-red-600 dark:text-red-400' : ''}`}>\r\n                    <i className={item.icon}></i>\r\n                  </div>\r\n                  {item.name}\r\n                </Link>\r\n              ))}\r\n            </div>\r\n\r\n            {/* Support Section */}\r\n            <div className=\"mt-8\">\r\n              <h3 className=\"px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                Support\r\n              </h3>\r\n              <div className=\"mt-2 space-y-1\">\r\n                {supportItems.map((item) => (\r\n                  <Link\r\n                    key={item.name}\r\n                    href={item.href}\r\n                    onClick={() => handleNavClick(item.href, item.name)}\r\n                    onMouseEnter={() => handleNavHover(item.href)}\r\n                    className=\"flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100\"\r\n                  >\r\n                    <div className=\"w-5 h-5 flex items-center justify-center mr-3\">\r\n                      <i className={item.icon}></i>\r\n                    </div>\r\n                    {item.name}\r\n                  </Link>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </nav>\r\n\r\n          {/* User Info */}\r\n          <div className=\"absolute bottom-0 w-64 p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800\">\r\n            <div className=\"flex items-center space-x-3\">\r\n              <Image\r\n                className=\"h-10 w-10 rounded-full object-cover\"\r\n                src={user?.profile_image || \"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp\"}\r\n                alt=\"Profile\"\r\n                width={40}\r\n                height={40}\r\n              />\r\n              <Link \r\n                href='/customer/profile' \r\n                className=\"flex-1 min-w-0\"\r\n                onClick={() => handleNavClick('/customer/profile', 'Profile')}\r\n              >\r\n                <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100 truncate\">\r\n                  {user ? `${user.first_name} ${user.last_name}` : 'Customer'}\r\n                </p>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 truncate\">\r\n                  {/* {user?.organizationName || 'Organization'} */}\r\n                </p>\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </aside>\r\n\r\n      {/* Main content */}\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        {/* Top header */}\r\n        <header className=\"bg-white dark:bg-gray-800 shadow-sm z-10\">\r\n          <div className=\"flex items-center justify-between h-16 px-4 sm:px-6\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={toggleMobileSidebar}\r\n              className=\"md:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none\"\r\n              aria-label=\"Open mobile menu\"\r\n            >\r\n              <div className=\"w-6 h-6 flex items-center justify-center\">\r\n                <i className=\"ri-menu-line ri-lg\"></i>\r\n              </div>\r\n            </button>\r\n            \r\n            <div className=\"flex-1\">\r\n              {breadcrumbs && breadcrumbs.length > 0 && (\r\n                <nav className=\"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400\">\r\n                  {breadcrumbs.map((item, index) => (\r\n                    <React.Fragment key={index}>\r\n                      {index > 0 && <i className=\"ri-arrow-right-s-line\"></i>}\r\n                      {item.href ? (\r\n                        <Link href={item.href} className=\"hover:text-primary\">\r\n                          {item.label}\r\n                        </Link>\r\n                      ) : (\r\n                        <span className=\"text-gray-900 dark:text-gray-100\">{item.label}</span>\r\n                      )}\r\n                    </React.Fragment>\r\n                  ))}\r\n                </nav>\r\n              )}\r\n            </div>\r\n            \r\n            <div className=\"flex items-center\">\r\n              <button\r\n                type=\"button\"\r\n                className=\"flex-shrink-0 p-1 mr-4 text-gray-500 dark:text-gray-400 rounded-full hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none relative\"\r\n              >\r\n                <span className=\"sr-only\">View notifications</span>\r\n                <div className=\"w-6 h-6 flex items-center justify-center\">\r\n                  <i className=\"ri-notification-3-line ri-lg\"></i>\r\n                </div>\r\n                <span className=\"absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white dark:ring-gray-800\"></span>\r\n              </button>\r\n              \r\n              <div className=\"relative\">\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={toggleUserDropdown}\r\n                  className=\"flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n                >\r\n                  <span className=\"sr-only\">Open user menu</span>\r\n                  <Image\r\n                    className=\"h-8 w-8 rounded-full\"\r\n                    src={user?.profile_image || \"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp\"}\r\n                    alt=\"Profile\"\r\n                    width={32}\r\n                    height={32}\r\n                  />\r\n                </button>\r\n                \r\n                {isUserDropdownOpen && (\r\n                  <div className=\"absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 dark:ring-gray-700 z-50\">\r\n                    <div className=\"py-1\">\r\n                      <Link\r\n                        href=\"/customer/profile\"\r\n                        className=\"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        onClick={() => {\r\n                          handleNavClick('/customer/profile', 'Profile');\r\n                          setIsUserDropdownOpen(false);\r\n                        }}\r\n                      >\r\n                        Your Profile\r\n                      </Link>\r\n          \r\n                      <LogoutButton\r\n                        variant=\"text\"\r\n                        size=\"sm\"\r\n                        className=\"w-full justify-start text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        showConfirmation={true}\r\n                      >\r\n                        Sign out\r\n                      </LogoutButton>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </header>\r\n\r\n        {/* Main content area */}\r\n        <main className=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900\">\r\n          <div className=\"py-6\">\r\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n              {children}\r\n            </div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CustomerLayout;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAoBA,MAAM,iBAAgD,CAAC,EAAE,QAAQ,EAAE,WAAW,EAAE;IAC9E,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,aAAU,AAAD;IAEhC,6DAA6D;IAC7D,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM;YACpC;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;SACD,EAAE;QAAC;KAAS;IAEb,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM;YACjC;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;YACR;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;YACR;SACD,EAAE,EAAE;IAEL,yDAAyD;IACzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,MAAM,gBAAgB;gBACpB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YAED,cAAc,OAAO,CAAC,CAAA;gBACpB,OAAO,QAAQ,CAAC;YAClB;QACF;QAEA,4DAA4D;QAC5D,MAAM,QAAQ,WAAW,eAAe;QACxC,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAO;IAEX,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,uBAAuB,CAAC;IAC1B,GAAG;QAAC;KAAoB;IAExB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,MAAc;QAChD,MAAM,eAA0C;YAC9C,aAAa;YACb,yBAAyB;YACzB,0BAA0B;YAC1B,iCAAiC;YACjC,sBAAsB;YACtB,uBAAuB;YACvB,yBAAyB;YACzB,uBAAuB;YACvB,6BAA6B;YAC7B,kBAAkB;YAClB,qBAAqB;YACrB,sBAAsB;QACxB;QAEA,MAAM,UAAU,YAAY,CAAC,KAAK,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,CAAC;QAC1D,WAAW;QACX,uBAAuB;IACzB,GAAG;QAAC;KAAW;IAEf,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,2CAA2C;QAC3C,OAAO,QAAQ,CAAC;IAClB,GAAG;QAAC;KAAO;IAEX,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,sBAAsB,CAAC;IACzB,GAAG;QAAC;KAAmB;IAEvB,qBACE,8OAAC;QAAI,WAAU;;YAEZ,qCACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,uBAAuB;;;;;;0BAK1C,8OAAC;gBAAM,WAAW,CAAC;;QAEjB,EAAE,sBAAsB,kBAAkB,qCAAqC;MACjF,CAAC;0BACC,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,WAAU;oCACV,OAAO;oCACP,QAAQ;oCACR,QAAQ;;;;;;;;;;;;;;;;sCAMd,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;4CAClD,cAAc,IAAM,eAAe,KAAK,IAAI;4CAC5C,WAAW,CAAC;;oBAEV,EAAE,KAAK,OAAO,GACV,8GACA,wHACH;kBACH,CAAC;;8DAED,8OAAC;oDAAI,WAAW,CAAC,8CAA8C,EAAE,KAAK,OAAO,GAAG,mCAAmC,IAAI;8DACrH,cAAA,8OAAC;wDAAE,WAAW,KAAK,IAAI;;;;;;;;;;;gDAExB,KAAK,IAAI;;2CAfL,KAAK,IAAI;;;;;;;;;;8CAqBpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuF;;;;;;sDAGrG,8OAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,4JAAA,CAAA,UAAI;oDAEH,MAAM,KAAK,IAAI;oDACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;oDAClD,cAAc,IAAM,eAAe,KAAK,IAAI;oDAC5C,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAE,WAAW,KAAK,IAAI;;;;;;;;;;;wDAExB,KAAK,IAAI;;mDATL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;sCAiBxB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,6HAAA,CAAA,UAAK;wCACJ,WAAU;wCACV,KAAK,MAAM,iBAAiB;wCAC5B,KAAI;wCACJ,OAAO;wCACP,QAAQ;;;;;;kDAEV,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,eAAe,qBAAqB;;0DAEnD,8OAAC;gDAAE,WAAU;0DACV,OAAO,GAAG,KAAK,UAAU,CAAC,CAAC,EAAE,KAAK,SAAS,EAAE,GAAG;;;;;;0DAEnD,8OAAC;gDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUvB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAO,WAAU;kCAChB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,cAAW;8CAEX,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;;;;;;;;;;;;;;;;8CAIjB,8OAAC;oCAAI,WAAU;8CACZ,eAAe,YAAY,MAAM,GAAG,mBACnC,8OAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;;oDACZ,QAAQ,mBAAK,8OAAC;wDAAE,WAAU;;;;;;oDAC1B,KAAK,IAAI,iBACR,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAM,KAAK,IAAI;wDAAE,WAAU;kEAC9B,KAAK,KAAK;;;;;6EAGb,8OAAC;wDAAK,WAAU;kEAAoC,KAAK,KAAK;;;;;;;+CAP7C;;;;;;;;;;;;;;;8CAe7B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,WAAU;;8DAEV,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;;;;;;;;;;;8DAEf,8OAAC;oDAAK,WAAU;;;;;;;;;;;;sDAGlB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;;sEAEV,8OAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,8OAAC,6HAAA,CAAA,UAAK;4DACJ,WAAU;4DACV,KAAK,MAAM,iBAAiB;4DAC5B,KAAI;4DACJ,OAAO;4DACP,QAAQ;;;;;;;;;;;;gDAIX,oCACC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,4JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;gEACV,SAAS;oEACP,eAAe,qBAAqB;oEACpC,sBAAsB;gEACxB;0EACD;;;;;;0EAID,8OAAC,kIAAA,CAAA,UAAY;gEACX,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,kBAAkB;0EACnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAYf,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;uCAEe", "debugId": null}}, {"offset": {"line": 873, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/common/TextInput.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { forwardRef } from 'react';\r\n\r\ninterface TextInputProps extends React.InputHTMLAttributes<HTMLInputElement> {\r\n  label?: string;\r\n  error?: string;\r\n  helperText?: string;\r\n  required?: boolean;\r\n  className?: string;\r\n  containerClassName?: string;\r\n}\r\n\r\nconst TextInput = forwardRef<HTMLInputElement, TextInputProps>(({\r\n  label,\r\n  error,\r\n  helperText,\r\n  required = false,\r\n  className = '',\r\n  containerClassName = '',\r\n  id,\r\n  ...props\r\n}, ref) => {\r\n  const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;\r\n  \r\n  const baseInputClasses = `\r\n    w-full px-3 py-2 border rounded-md shadow-sm \r\n    focus:outline-none focus:ring-2 focus:ring-offset-2 \r\n    disabled:opacity-50 disabled:cursor-not-allowed\r\n    bg-white text-gray-900 placeholder:text-gray-500\r\n    dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600 dark:placeholder:text-gray-400\r\n    transition-colors duration-200\r\n  `;\r\n  \r\n  const inputClasses = error\r\n    ? `${baseInputClasses} border-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-600 dark:focus:border-red-500 dark:focus:ring-red-500`\r\n    : `${baseInputClasses} border-gray-300 focus:border-primary focus:ring-primary dark:border-gray-600 dark:focus:border-primary dark:focus:ring-primary`;\r\n\r\n  return (\r\n    <div className={`space-y-1 ${containerClassName}`}>\r\n      {label && (\r\n        <label \r\n          htmlFor={inputId}\r\n          className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\"\r\n        >\r\n          {label}\r\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\r\n        </label>\r\n      )}\r\n      \r\n      <input\r\n        ref={ref}\r\n        id={inputId}\r\n        className={`${inputClasses} ${className}`}\r\n        {...props}\r\n      />\r\n      \r\n      {error && (\r\n        <p className=\"text-sm text-red-600 dark:text-red-400 flex items-center\">\r\n          <i className=\"ri-error-warning-line mr-1\"></i>\r\n          {error}\r\n        </p>\r\n      )}\r\n      \r\n      {helperText && !error && (\r\n        <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n          {helperText}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n});\r\n\r\nTextInput.displayName = 'TextInput';\r\n\r\nexport default TextInput;\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAaA,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAoC,CAAC,EAC9D,KAAK,EACL,KAAK,EACL,UAAU,EACV,WAAW,KAAK,EAChB,YAAY,EAAE,EACd,qBAAqB,EAAE,EACvB,EAAE,EACF,GAAG,OACJ,EAAE;IACD,MAAM,UAAU,MAAM,CAAC,MAAM,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAExE,MAAM,mBAAmB,CAAC;;;;;;;EAO1B,CAAC;IAED,MAAM,eAAe,QACjB,GAAG,iBAAiB,6HAA6H,CAAC,GAClJ,GAAG,iBAAiB,+HAA+H,CAAC;IAExJ,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,oBAAoB;;YAC9C,uBACC,8OAAC;gBACC,SAAS;gBACT,WAAU;;oBAET;oBACA,0BAAY,8OAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,8OAAC;gBACC,KAAK;gBACL,IAAI;gBACJ,WAAW,GAAG,aAAa,CAAC,EAAE,WAAW;gBACxC,GAAG,KAAK;;;;;;YAGV,uBACC,8OAAC;gBAAE,WAAU;;kCACX,8OAAC;wBAAE,WAAU;;;;;;oBACZ;;;;;;;YAIJ,cAAc,CAAC,uBACd,8OAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;AAEA,UAAU,WAAW,GAAG;uCAET", "debugId": null}}, {"offset": {"line": 964, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/services/applicationService.ts"], "sourcesContent": ["import { processApiResponse } from '@/lib/authUtils';\r\nimport { apiClient } from '../lib/apiClient';\r\nimport { Application, ApplicationStatus, ApplicationFilters, PaginatedResponse } from '../types/license';\r\n\r\nexport const applicationService = {\r\n  // Get all applications with pagination and filters\r\n  async getApplications(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    sortBy?: string;\r\n    sortOrder?: 'ASC' | 'DESC';\r\n    filters?: ApplicationFilters;\r\n  }): Promise<PaginatedResponse<Application>> {\r\n    const queryParams = new URLSearchParams();\r\n    \r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.sortBy) queryParams.append('sortBy', params.sortBy);\r\n    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);\r\n    \r\n    // Add filters\r\n    if (params?.filters?.licenseTypeId) {\r\n      queryParams.append('filter.license_category.license_type_id', params.filters.licenseTypeId);\r\n    }\r\n    if (params?.filters?.licenseCategoryId) {\r\n      queryParams.append('filter.license_category_id', params.filters.licenseCategoryId);\r\n    }\r\n    if (params?.filters?.status) {\r\n      queryParams.append('filter.status', params.filters.status);\r\n    }\r\n\r\n    const response = await apiClient.get(`/applications?${queryParams.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get applications by license type (through license category)\r\n  async getApplicationsByLicenseType(licenseTypeId: string, params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    status?: ApplicationStatus;\r\n  }): Promise<PaginatedResponse<Application>> {\r\n    const queryParams = new URLSearchParams();\r\n    \r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.status) queryParams.append('filter.status', params.status);\r\n    \r\n    // Filter by license type through license category\r\n    queryParams.append('filter.license_category.license_type_id', licenseTypeId);\r\n\r\n    const response = await apiClient.get(`/applications?${queryParams.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get single application by ID\r\n  async getApplication(id: string): Promise<Application> {\r\n    const response = await apiClient.get(`/applications/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get applications by applicant\r\n  async getApplicationsByApplicant(applicantId: string): Promise<Application[]> {\r\n    const response = await apiClient.get(`/applications/by-applicant/${applicantId}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get applications by status\r\n  async getApplicationsByStatus(status: ApplicationStatus): Promise<Application[]> {\r\n    const response = await apiClient.get(`/applications/by-status/${status}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update application status\r\n  async updateApplicationStatus(id: string, status: ApplicationStatus): Promise<Application> {\r\n    const response = await apiClient.put(`/applications/${id}/status?status=${status}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update application progress\r\n  async updateApplicationProgress(id: string, currentStep: number, progressPercentage: number): Promise<Application> {\r\n    const response = await apiClient.put(\r\n      `/applications/${id}/progress?currentStep=${currentStep}&progressPercentage=${progressPercentage}`\r\n    );\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get application statistics\r\n  async getApplicationStats(): Promise<Record<string, number>> {\r\n    const response = await apiClient.get('/applications/stats');\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Create new application\r\n  async createApplication(data: {\r\n    application_number: string;\r\n    applicant_id: string;\r\n    license_category_id: string;\r\n    status?: ApplicationStatus;\r\n    current_step?: number;\r\n    progress_percentage?: number;\r\n    submitted_at?: Date;\r\n  }): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.post('/applications', data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Update application\r\n  async updateApplication(id: string, data: Partial<Application>): Promise<Application> {\r\n    const response = await apiClient.put(`/applications/${id}`, data);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Delete application\r\n  async deleteApplication(id: string): Promise<{ message: string }> {\r\n    const response = await apiClient.delete(`/applications/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Create new application with applicant data\r\n  async createApplicationWithApplicant(data: {\r\n    license_type_id: string;\r\n    license_category_id: string;\r\n    applicant_data: Record<string, any>;\r\n    user_id: string;\r\n  }): Promise<Application> {\r\n    try {\r\n      // Generate proper application number (format: APP-YYYYMMDD-HHMMSS-XXX)\r\n      const now = new Date();\r\n      const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');\r\n      const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');\r\n      const randomStr = Math.random().toString(36).substr(2, 3).toUpperCase();\r\n      const applicationNumber = `APP-${dateStr}-${timeStr}-${randomStr}`;\r\n\r\n      // Validate user_id is a proper UUID\r\n      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\r\n      if (!uuidRegex.test(data.user_id)) {\r\n        throw new Error(`Invalid user_id format: ${data.user_id}. Expected UUID format.`);\r\n      }\r\n\r\n      // Create application using user_id as applicant_id\r\n      // In most systems, the authenticated user is the applicant\r\n      const application = await this.createApplication({\r\n        application_number: applicationNumber,\r\n        applicant_id: data.user_id, // Use user_id as applicant_id\r\n        license_category_id: data.license_category_id,\r\n        current_step: 1,\r\n        progress_percentage: 0 // Start with 0% progress\r\n      });\r\n\r\n      return application;\r\n    } catch (error) {\r\n      console.error('Error creating application with applicant:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Save application section data\r\n  async saveApplicationSection(\r\n    applicationId: string,\r\n    sectionName: string,\r\n    sectionData: Record<string, any>\r\n  ): Promise<void> {\r\n    try {\r\n      console.log(`Saving section ${sectionName} for application ${applicationId}:`, sectionData);\r\n\r\n      // Try to save using form data service, but continue if it fails\r\n      let completedSections = 1; // At least one section is being saved\r\n\r\n      // Use entity-specific APIs instead of form data service\r\n      console.warn('saveApplicationSection is deprecated. Use entity-specific APIs instead.');\r\n\r\n      // Estimate progress based on section name\r\n      const sectionOrder = ['applicantInfo', 'companyProfile', 'businessInfo', 'serviceScope', 'businessPlan', 'legalHistory', 'reviewSubmit'];\r\n      const sectionIndex = sectionOrder.indexOf(sectionName);\r\n      completedSections = sectionIndex >= 0 ? sectionIndex + 1 : 1;\r\n\r\n      // Calculate progress based on completed sections (excluding reviewSubmit from total)\r\n      const totalSections = 6; // Total number of form sections (excluding reviewSubmit)\r\n      const progressPercentage = Math.min(Math.round((completedSections / totalSections) * 100), 100);\r\n\r\n      // Update the application progress\r\n      await this.updateApplication(applicationId, {\r\n        progress_percentage: progressPercentage,\r\n        current_step: completedSections\r\n      });\r\n\r\n      console.log(`Section ${sectionName} saved successfully with ${progressPercentage}% progress`);\r\n    } catch (error) {\r\n      console.error(`Error saving section ${sectionName}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get application section data\r\n  async getApplicationSection(applicationId: string, sectionName: string): Promise<any> {\r\n    try {\r\n      const response = await apiClient.get(`/applications/${applicationId}/sections/${sectionName}`);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error(`Error fetching section ${sectionName}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Submit application for review\r\n  async submitApplication(applicationId: string): Promise<Application> {\r\n    try {\r\n      console.log('Submitting application:', applicationId);\r\n\r\n      // Update application status to submitted and set submission date\r\n      const response = await apiClient.put(`/applications/${applicationId}`, {\r\n        status: 'submitted',\r\n        submitted_at: new Date().toISOString(),\r\n        progress_percentage: 100,\r\n        current_step: 7\r\n      });\r\n\r\n      console.log('Application submitted successfully:', processApiResponse(response));\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error submitting application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get user's applications (filtered by authenticated user)\r\n  async getUserApplications(): Promise<Application[]> {\r\n    try {\r\n      console.log('Fetching user applications...');\r\n\r\n      // Use dedicated endpoint that explicitly filters by current user\r\n      const response = await apiClient.get('/applications/user-applications');\r\n      const processedResponse = processApiResponse(response);\r\n\r\n      console.log('User applications API response:', processedResponse);\r\n\r\n      // Handle paginated response structure\r\n      let applications = [];\r\n      if (processedResponse?.data) {\r\n        applications = Array.isArray(processedResponse.data) ? processedResponse.data : [];\r\n      } else if (Array.isArray(processedResponse)) {\r\n        applications = processedResponse;\r\n      } else if (processedResponse) {\r\n        // Single application or other structure\r\n        applications = [processedResponse];\r\n      }\r\n\r\n      console.log('Processed user applications:', applications);\r\n      return applications;\r\n    } catch (error) {\r\n      console.error('Error fetching user applications:', error);\r\n      console.error('Error details:', {\r\n        message: (error as any)?.message,\r\n        response: (error as any)?.response?.data,\r\n        status: (error as any)?.response?.status\r\n      });\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Save application as draft\r\n  async saveAsDraft(applicationId: string, formData: Record<string, any>): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.put(`/applications/${applicationId}`, {\r\n        form_data: formData,\r\n        status: 'draft'\r\n      });\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error saving application as draft:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Validate application before submission\r\n  async validateApplication(applicationId: string): Promise<{ isValid: boolean; errors: any[] }> {\r\n    try {\r\n      // Get data from entity-specific APIs for validation\r\n      let formData: Record<string, any> = {};\r\n      console.warn('Application validation should use entity-specific APIs instead of form data service');\r\n      // Continue with empty form data for now\r\n\r\n      const errors: any[] = [];\r\n      const requiredSections = ['applicantInfo', 'companyProfile', 'businessInfo', 'legalHistory'];\r\n\r\n      // Check if all required sections are completed\r\n      for (const section of requiredSections) {\r\n        if (!formData[section] || Object.keys(formData[section]).length === 0) {\r\n          errors.push(`${section} section is incomplete`);\r\n        }\r\n      }\r\n\r\n      return {\r\n        isValid: errors.length === 0,\r\n        errors\r\n      };\r\n    } catch (error) {\r\n      console.error('Error validating application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,MAAM,qBAAqB;IAChC,mDAAmD;IACnD,MAAM,iBAAgB,MAOrB;QACC,MAAM,cAAc,IAAI;QAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,WAAW,YAAY,MAAM,CAAC,aAAa,OAAO,SAAS;QAEvE,cAAc;QACd,IAAI,QAAQ,SAAS,eAAe;YAClC,YAAY,MAAM,CAAC,2CAA2C,OAAO,OAAO,CAAC,aAAa;QAC5F;QACA,IAAI,QAAQ,SAAS,mBAAmB;YACtC,YAAY,MAAM,CAAC,8BAA8B,OAAO,OAAO,CAAC,iBAAiB;QACnF;QACA,IAAI,QAAQ,SAAS,QAAQ;YAC3B,YAAY,MAAM,CAAC,iBAAiB,OAAO,OAAO,CAAC,MAAM;QAC3D;QAEA,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,YAAY,QAAQ,IAAI;QAC9E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8DAA8D;IAC9D,MAAM,8BAA6B,aAAqB,EAAE,MAKzD;QACC,MAAM,cAAc,IAAI;QAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,iBAAiB,OAAO,MAAM;QAErE,kDAAkD;QAClD,YAAY,MAAM,CAAC,2CAA2C;QAE9D,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,YAAY,QAAQ,IAAI;QAC9E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,+BAA+B;IAC/B,MAAM,gBAAe,EAAU;QAC7B,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI;QAC1D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gCAAgC;IAChC,MAAM,4BAA2B,WAAmB;QAClD,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,2BAA2B,EAAE,aAAa;QAChF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6BAA6B;IAC7B,MAAM,yBAAwB,MAAyB;QACrD,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,wBAAwB,EAAE,QAAQ;QACxE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,4BAA4B;IAC5B,MAAM,yBAAwB,EAAU,EAAE,MAAyB;QACjE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,GAAG,eAAe,EAAE,QAAQ;QAClF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8BAA8B;IAC9B,MAAM,2BAA0B,EAAU,EAAE,WAAmB,EAAE,kBAA0B;QACzF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAC,cAAc,EAAE,GAAG,sBAAsB,EAAE,YAAY,oBAAoB,EAAE,oBAAoB;QAEpG,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6BAA6B;IAC7B,MAAM;QACJ,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,yBAAyB;IACzB,MAAM,mBAAkB,IAQvB;QACC,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,iBAAiB;YACvD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,qBAAqB;IACrB,MAAM,mBAAkB,EAAU,EAAE,IAA0B;QAC5D,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI,EAAE;QAC5D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,qBAAqB;IACrB,MAAM,mBAAkB,EAAU;QAChC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,cAAc,EAAE,IAAI;QAC7D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6CAA6C;IAC7C,MAAM,gCAA+B,IAKpC;QACC,IAAI;YACF,uEAAuE;YACvE,MAAM,MAAM,IAAI;YAChB,MAAM,UAAU,IAAI,WAAW,GAAG,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,MAAM;YAC7D,MAAM,UAAU,IAAI,YAAY,GAAG,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM;YAC7D,MAAM,YAAY,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG,WAAW;YACrE,MAAM,oBAAoB,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,WAAW;YAElE,oCAAoC;YACpC,MAAM,YAAY;YAClB,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,OAAO,GAAG;gBACjC,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,KAAK,OAAO,CAAC,uBAAuB,CAAC;YAClF;YAEA,mDAAmD;YACnD,2DAA2D;YAC3D,MAAM,cAAc,MAAM,IAAI,CAAC,iBAAiB,CAAC;gBAC/C,oBAAoB;gBACpB,cAAc,KAAK,OAAO;gBAC1B,qBAAqB,KAAK,mBAAmB;gBAC7C,cAAc;gBACd,qBAAqB,EAAE,yBAAyB;YAClD;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,MAAM,wBACJ,aAAqB,EACrB,WAAmB,EACnB,WAAgC;QAEhC,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,YAAY,iBAAiB,EAAE,cAAc,CAAC,CAAC,EAAE;YAE/E,gEAAgE;YAChE,IAAI,oBAAoB,GAAG,sCAAsC;YAEjE,wDAAwD;YACxD,QAAQ,IAAI,CAAC;YAEb,0CAA0C;YAC1C,MAAM,eAAe;gBAAC;gBAAiB;gBAAkB;gBAAgB;gBAAgB;gBAAgB;gBAAgB;aAAe;YACxI,MAAM,eAAe,aAAa,OAAO,CAAC;YAC1C,oBAAoB,gBAAgB,IAAI,eAAe,IAAI;YAE3D,qFAAqF;YACrF,MAAM,gBAAgB,GAAG,yDAAyD;YAClF,MAAM,qBAAqB,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,AAAC,oBAAoB,gBAAiB,MAAM;YAE3F,kCAAkC;YAClC,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe;gBAC1C,qBAAqB;gBACrB,cAAc;YAChB;YAEA,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,YAAY,yBAAyB,EAAE,mBAAmB,UAAU,CAAC;QAC9F,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,qBAAqB,EAAE,YAAY,CAAC,CAAC,EAAE;YACtD,MAAM;QACR;IACF;IAEA,+BAA+B;IAC/B,MAAM,uBAAsB,aAAqB,EAAE,WAAmB;QACpE,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,cAAc,UAAU,EAAE,aAAa;YAC7F,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,YAAY,CAAC,CAAC,EAAE;YACxD,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,MAAM,mBAAkB,aAAqB;QAC3C,IAAI;YACF,QAAQ,GAAG,CAAC,2BAA2B;YAEvC,iEAAiE;YACjE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe,EAAE;gBACrE,QAAQ;gBACR,cAAc,IAAI,OAAO,WAAW;gBACpC,qBAAqB;gBACrB,cAAc;YAChB;YAEA,QAAQ,GAAG,CAAC,uCAAuC,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;YACtE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA,2DAA2D;IAC3D,MAAM;QACJ,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,iEAAiE;YACjE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,MAAM,oBAAoB,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;YAE7C,QAAQ,GAAG,CAAC,mCAAmC;YAE/C,sCAAsC;YACtC,IAAI,eAAe,EAAE;YACrB,IAAI,mBAAmB,MAAM;gBAC3B,eAAe,MAAM,OAAO,CAAC,kBAAkB,IAAI,IAAI,kBAAkB,IAAI,GAAG,EAAE;YACpF,OAAO,IAAI,MAAM,OAAO,CAAC,oBAAoB;gBAC3C,eAAe;YACjB,OAAO,IAAI,mBAAmB;gBAC5B,wCAAwC;gBACxC,eAAe;oBAAC;iBAAkB;YACpC;YAEA,QAAQ,GAAG,CAAC,gCAAgC;YAC5C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,QAAQ,KAAK,CAAC,kBAAkB;gBAC9B,SAAU,OAAe;gBACzB,UAAW,OAAe,UAAU;gBACpC,QAAS,OAAe,UAAU;YACpC;YACA,MAAM;QACR;IACF;IAEA,4BAA4B;IAC5B,MAAM,aAAY,aAAqB,EAAE,QAA6B;QACpE,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe,EAAE;gBACrE,WAAW;gBACX,QAAQ;YACV;YACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACR;IACF;IAEA,yCAAyC;IACzC,MAAM,qBAAoB,aAAqB;QAC7C,IAAI;YACF,oDAAoD;YACpD,IAAI,WAAgC,CAAC;YACrC,QAAQ,IAAI,CAAC;YACb,wCAAwC;YAExC,MAAM,SAAgB,EAAE;YACxB,MAAM,mBAAmB;gBAAC;gBAAiB;gBAAkB;gBAAgB;aAAe;YAE5F,+CAA+C;YAC/C,KAAK,MAAM,WAAW,iBAAkB;gBACtC,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG;oBACrE,OAAO,IAAI,CAAC,GAAG,QAAQ,sBAAsB,CAAC;gBAChD;YACF;YAEA,OAAO;gBACL,SAAS,OAAO,MAAM,KAAK;gBAC3B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 1226, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/services/applicantService.ts"], "sourcesContent": ["import { processApiResponse } from '@/lib/authUtils';\r\nimport { apiClient } from '../lib/apiClient';\r\nimport { Applicant } from '../types/license';\r\n\r\nexport interface CreateApplicantData {\r\n  name: string;\r\n  business_registration_number: string;\r\n  tpin: string;\r\n  website: string;\r\n  email: string;\r\n  phone: string;\r\n  fax?: string;\r\n  level_of_insurance_cover?: string;\r\n  physical_address_id?: string;\r\n  postal_address_id?: string;\r\n  contact_id?: string;\r\n  date_incorporation: string; // Changed from Date to string to match backend DTO\r\n  place_incorporation: string;\r\n}\r\n\r\nexport const applicantService = {\r\n  // Create new applicant\r\n  async createApplicant(data: CreateApplicantData): Promise<Applicant> {\r\n    try {\r\n      console.log('Creating applicant with data:', data);\r\n\r\n      const response = await apiClient.post('/applicants', data);\r\n      const result = processApiResponse(response);\r\n      \r\n      if (!result) {\r\n        throw new Error('Invalid response format from applicant creation');\r\n      }\r\n      \r\n      return result;\r\n    } catch (error) {\r\n      console.error('Error creating applicant:', error);\r\n      console.error('Error details:', (error as any)?.response?.data);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get applicant by ID\r\n  async getApplicant(id: string): Promise<Applicant> {\r\n    try {\r\n      const response = await apiClient.get(`/applicants/${id}`);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error fetching applicant:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Update applicant\r\n  async updateApplicant(id: string, data: Partial<CreateApplicantData>): Promise<Applicant> {\r\n    try {\r\n      console.log('Updating applicant:', id, data);\r\n      \r\n      const response = await apiClient.put(`/applicants/${id}`, data);\r\n      \r\n      console.log('Applicant updated successfully:', response.data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error updating applicant:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get applicants by user (if user can have multiple applicants)\r\n  async getApplicantsByUser(): Promise<Applicant[]> {\r\n    try {\r\n      const response = await apiClient.get('/applicants/by-user');\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error fetching user applicants:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Delete applicant\r\n  async deleteApplicant(id: string): Promise<{ message: string }> {\r\n    try {\r\n      const response = await apiClient.delete(`/applicants/${id}`);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error deleting applicant:', error);\r\n      throw error;\r\n    }\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAmBO,MAAM,mBAAmB;IAC9B,uBAAuB;IACvB,MAAM,iBAAgB,IAAyB;QAC7C,IAAI;YACF,QAAQ,GAAG,CAAC,iCAAiC;YAE7C,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,eAAe;YACrD,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;YAElC,IAAI,CAAC,QAAQ;gBACX,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,QAAQ,KAAK,CAAC,kBAAmB,OAAe,UAAU;YAC1D,MAAM;QACR;IACF;IAEA,sBAAsB;IACtB,MAAM,cAAa,EAAU;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI;YACxD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,mBAAmB;IACnB,MAAM,iBAAgB,EAAU,EAAE,IAAkC;QAClE,IAAI;YACF,QAAQ,GAAG,CAAC,uBAAuB,IAAI;YAEvC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI,EAAE;YAE1D,QAAQ,GAAG,CAAC,mCAAmC,SAAS,IAAI;YAC5D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,gEAAgE;IAChE,MAAM;QACJ,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;QACR;IACF;IAEA,mBAAmB;IACnB,MAAM,iBAAgB,EAAU;QAC9B,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,IAAI;YAC3D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 1299, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/utils/formValidation.ts"], "sourcesContent": ["// Form validation utilities\r\n\r\nexport interface ValidationRule {\r\n  required?: boolean;\r\n  minLength?: number;\r\n  maxLength?: number;\r\n  pattern?: RegExp;\r\n  custom?: (value: any) => string | null;\r\n}\r\n\r\nexport interface ValidationErrors {\r\n  [key: string]: string;\r\n}\r\n\r\nexport const validateField = (value: any, rules: ValidationRule): string | null => {\r\n  // Required validation\r\n  if (rules.required && (!value || (typeof value === 'string' && value.trim() === ''))) {\r\n    return 'This field is required';\r\n  }\r\n\r\n  // Skip other validations if field is empty and not required\r\n  if (!value || (typeof value === 'string' && value.trim() === '')) {\r\n    return null;\r\n  }\r\n\r\n  // String validations\r\n  if (typeof value === 'string') {\r\n    // Min length validation\r\n    if (rules.minLength && value.length < rules.minLength) {\r\n      return `Must be at least ${rules.minLength} characters`;\r\n    }\r\n\r\n    // Max length validation\r\n    if (rules.maxLength && value.length > rules.maxLength) {\r\n      return `Must be no more than ${rules.maxLength} characters`;\r\n    }\r\n\r\n    // Pattern validation\r\n    if (rules.pattern && !rules.pattern.test(value)) {\r\n      return 'Invalid format';\r\n    }\r\n  }\r\n\r\n  // Custom validation\r\n  if (rules.custom) {\r\n    return rules.custom(value);\r\n  }\r\n\r\n  return null;\r\n};\r\n\r\nexport const validateForm = (data: any, rules: { [key: string]: ValidationRule }): ValidationErrors => {\r\n  const errors: ValidationErrors = {};\r\n\r\n  Object.keys(rules).forEach(field => {\r\n    const error = validateField(data[field], rules[field]);\r\n    if (error) {\r\n      errors[field] = error;\r\n    }\r\n  });\r\n\r\n  return errors;\r\n};\r\n\r\n// Common validation patterns\r\nexport const patterns = {\r\n  email: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\r\n  phone: /^(\\+265|0)[0-9]{8,9}$/,\r\n  url: /^https?:\\/\\/.+/,\r\n  alphanumeric: /^[a-zA-Z0-9]+$/,\r\n  alphabetic: /^[a-zA-Z\\s]+$/,\r\n  numeric: /^[0-9]+$/,\r\n  percentage: /^(100|[1-9]?[0-9])$/\r\n};\r\n\r\n// Common validation rules\r\nexport const commonRules = {\r\n  required: { required: true },\r\n  email: { \r\n    required: true, \r\n    pattern: patterns.email,\r\n    custom: (value: string) => {\r\n      if (value && !patterns.email.test(value)) {\r\n        return 'Please enter a valid email address';\r\n      }\r\n      return null;\r\n    }\r\n  },\r\n  phone: {\r\n    required: true,\r\n    pattern: patterns.phone,\r\n    custom: (value: string) => {\r\n      if (value && !patterns.phone.test(value)) {\r\n        return 'Please enter a valid Malawi phone number (e.g., +265123456789 or 0123456789)';\r\n      }\r\n      return null;\r\n    }\r\n  },\r\n  businessRegistration: {\r\n    required: true,\r\n    minLength: 5,\r\n    custom: (value: string) => {\r\n      if (value && value.length < 5) {\r\n        return 'Business registration number must be at least 5 characters';\r\n      }\r\n      return null;\r\n    }\r\n  },\r\n  percentage: {\r\n    pattern: patterns.percentage,\r\n    custom: (value: string) => {\r\n      if (value && !patterns.percentage.test(value)) {\r\n        return 'Please enter a valid percentage (0-100)';\r\n      }\r\n      return null;\r\n    }\r\n  }\r\n};\r\n\r\n// Utility function to check if form has errors\r\nexport const hasErrors = (errors: ValidationErrors): boolean => {\r\n  return Object.keys(errors).length > 0;\r\n};\r\n\r\n// Utility function to get first error message\r\nexport const getFirstError = (errors: ValidationErrors): string | null => {\r\n  const firstKey = Object.keys(errors)[0];\r\n  return firstKey ? errors[firstKey] : null;\r\n};\r\n\r\n// Utility function to validate array fields\r\nexport const validateArrayField = (\r\n  array: any[], \r\n  rules: { [key: string]: ValidationRule },\r\n  minItems?: number,\r\n  maxItems?: number\r\n): { [key: string]: ValidationErrors } => {\r\n  const arrayErrors: { [key: string]: ValidationErrors } = {};\r\n\r\n  // Check array length\r\n  if (minItems && array.length < minItems) {\r\n    arrayErrors._array = { length: `Must have at least ${minItems} items` };\r\n  }\r\n  if (maxItems && array.length > maxItems) {\r\n    arrayErrors._array = { length: `Must have no more than ${maxItems} items` };\r\n  }\r\n\r\n  // Validate each item in array\r\n  array.forEach((item, index) => {\r\n    const itemErrors = validateForm(item, rules);\r\n    if (hasErrors(itemErrors)) {\r\n      arrayErrors[index] = itemErrors;\r\n    }\r\n  });\r\n\r\n  return arrayErrors;\r\n};\r\n\r\n// File validation\r\nexport const validateFile = (\r\n  file: File | null, \r\n  required: boolean = false,\r\n  maxSize: number = 10, // MB\r\n  allowedTypes: string[] = ['.pdf']\r\n): string | null => {\r\n  if (required && !file) {\r\n    return 'File is required';\r\n  }\r\n\r\n  if (!file) {\r\n    return null;\r\n  }\r\n\r\n  // Check file size\r\n  if (file.size > maxSize * 1024 * 1024) {\r\n    return `File size must be less than ${maxSize}MB`;\r\n  }\r\n\r\n  // Check file type\r\n  const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();\r\n  if (!allowedTypes.includes(fileExtension)) {\r\n    return `File type must be: ${allowedTypes.join(', ')}`;\r\n  }\r\n\r\n  return null;\r\n};\r\n\r\n// Section validation interface\r\nexport interface ValidationResult {\r\n  isValid: boolean;\r\n  errors: Record<string, string>;\r\n}\r\n\r\n// Validate section data for application forms\r\nexport const validateSection = (data: Record<string, any>, sectionName: string): ValidationResult => {\r\n  const errors: Record<string, string> = {};\r\n\r\n  switch (sectionName) {\r\n    case 'applicantInfo':\r\n      // Required fields for applicant info - matching the actual form fields\r\n      const applicantRequiredFields = [\r\n        'name', 'business_registration_number', 'tpin', 'email', 'phone',\r\n        'date_incorporation', 'place_incorporation'\r\n      ];\r\n\r\n      applicantRequiredFields.forEach(field => {\r\n        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {\r\n          errors[field] = `${field.replace(/_/g, ' ')} is required`;\r\n        }\r\n      });\r\n\r\n      // Email validation\r\n      if (data.email && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(data.email)) {\r\n        errors.email = 'Please enter a valid email address';\r\n      }\r\n\r\n      // Website validation (optional field)\r\n      if (data.website && data.website.trim() !== '') {\r\n        if (!/^https?:\\/\\/.+/.test(data.website)) {\r\n          errors.website = 'Please enter a valid website URL (starting with http:// or https://)';\r\n        }\r\n      }\r\n\r\n      // Phone validation - using backend validation pattern\r\n      if (data.phone) {\r\n        if (!/^[+]?[\\d\\s\\-()]+$/.test(data.phone)) {\r\n            errors.phone = 'Phone number can only contain numbers, spaces, dashes, and parentheses';\r\n        } else if (data.phone.trim().length < 10) {\r\n          errors.phone = 'Phone number must be at least 10 characters long';\r\n        } else if (data.phone.trim().length > 20) {\r\n          errors.phone = 'Phone number must be no more than 20 characters long';\r\n        }\r\n      }\r\n\r\n      // Fax validation (optional field)\r\n      if (data.fax && data.fax.trim() !== '') {\r\n        if (!/^[+]?[\\d\\s\\-()]+$/.test(data.fax)) {\r\n            errors.fax = 'Fax number can only contain numbers, spaces, dashes, and parentheses';\r\n        } else if (data.fax.trim().length < 10) {\r\n          errors.fax = 'Fax number must be at least 10 characters long';\r\n        } else if (data.fax.trim().length > 20) {\r\n          errors.fax = 'Fax number must be no more than 20 characters long';\r\n        }\r\n      }\r\n\r\n      // Level of insurance cover validation (optional field)\r\n      if (data.level_of_insurance_cover && data.level_of_insurance_cover.trim() !== '' && \r\n          data.level_of_insurance_cover.trim().length < 3) {\r\n        errors.level_of_insurance_cover = 'Please provide a valid insurance cover amount';\r\n      }\r\n\r\n      // Date validation\r\n      if (data.date_incorporation && !/^\\d{4}-\\d{2}-\\d{2}$/.test(data.date_incorporation)) {\r\n        errors.date_incorporation = 'Please enter a valid date (YYYY-MM-DD)';\r\n      }\r\n\r\n      break;\r\n\r\n    case 'companyProfile':\r\n      const companyRequiredFields = [\r\n        'company_name', 'business_registration_number', 'tax_number', 'company_type',\r\n        'incorporation_date', 'incorporation_place', 'company_email', 'company_phone',\r\n        'company_address', 'company_city', 'company_district', 'number_of_employees',\r\n        'annual_revenue', 'business_description'\r\n      ];\r\n\r\n      companyRequiredFields.forEach(field => {\r\n        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {\r\n          errors[field] = `${field.replace(/_/g, ' ')} is required`;\r\n        }\r\n      });\r\n\r\n      // Email validation\r\n      if (data.company_email && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(data.company_email)) {\r\n        errors.company_email = 'Please enter a valid email address';\r\n      }\r\n\r\n      break;\r\n\r\n    case 'businessInfo':\r\n      const businessRequiredFields = [\r\n        'business_model', 'operational_structure', 'target_market', 'competitive_advantage',\r\n        'facilities_description', 'equipment_description', 'operational_areas',\r\n        'service_delivery_model', 'quality_assurance', 'customer_support'\r\n      ];\r\n\r\n      businessRequiredFields.forEach(field => {\r\n        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {\r\n          errors[field] = `${field.replace(/_/g, ' ')} is required`;\r\n        }\r\n      });\r\n\r\n      break;\r\n\r\n    case 'serviceScope':\r\n      const serviceScopeRequiredFields = [\r\n        'services_offered', 'geographic_coverage', 'service_categories',\r\n        'target_customers', 'service_capacity'\r\n      ];\r\n\r\n      serviceScopeRequiredFields.forEach(field => {\r\n        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {\r\n          errors[field] = `${field.replace(/_/g, ' ')} is required`;\r\n        }\r\n      });\r\n\r\n      break;\r\n\r\n    case 'businessPlan':\r\n      const businessPlanRequiredFields = [\r\n        'executive_summary', 'market_analysis', 'financial_projections',\r\n        'revenue_model', 'investment_requirements', 'implementation_timeline',\r\n        'risk_analysis', 'success_metrics'\r\n      ];\r\n\r\n      businessPlanRequiredFields.forEach(field => {\r\n        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {\r\n          errors[field] = `${field.replace(/_/g, ' ')} is required`;\r\n        }\r\n      });\r\n\r\n      break;\r\n\r\n    case 'legalHistory':\r\n      // Required fields\r\n      if (!data.compliance_record || data.compliance_record.trim() === '') {\r\n        errors.compliance_record = 'Compliance record is required';\r\n      }\r\n\r\n      // Declaration must be accepted\r\n      if (!data.declaration_accepted) {\r\n        errors.declaration_accepted = 'You must accept the declaration to proceed';\r\n      }\r\n\r\n      // Conditional validations\r\n      if (data.criminal_history && (!data.criminal_details || data.criminal_details.trim() === '')) {\r\n        errors.criminal_details = 'Please provide details of your criminal history';\r\n      }\r\n\r\n      if (data.bankruptcy_history && (!data.bankruptcy_details || data.bankruptcy_details.trim() === '')) {\r\n        errors.bankruptcy_details = 'Please provide details of your bankruptcy history';\r\n      }\r\n\r\n      if (data.regulatory_actions && (!data.regulatory_details || data.regulatory_details.trim() === '')) {\r\n        errors.regulatory_details = 'Please provide details of regulatory actions';\r\n      }\r\n\r\n      if (data.litigation_history && (!data.litigation_details || data.litigation_details.trim() === '')) {\r\n        errors.litigation_details = 'Please provide details of litigation history';\r\n      }\r\n\r\n      break;\r\n\r\n    case 'address':\r\n      // Required fields for address information\r\n      const addressRequiredFields = [\r\n        'address_line_1', 'city', 'country'\r\n      ];\r\n\r\n      addressRequiredFields.forEach(field => {\r\n        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {\r\n          errors[field] = `${field.replace(/_/g, ' ')} is required`;\r\n        }\r\n      });\r\n\r\n      break;\r\n\r\n    case 'contactInfo':\r\n      // Required fields for contact information\r\n      const contactRequiredFields = [\r\n        'primary_contact_first_name', 'primary_contact_last_name', 'primary_contact_designation',\r\n        'primary_contact_email', 'primary_contact_phone'\r\n      ];\r\n\r\n      contactRequiredFields.forEach(field => {\r\n        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {\r\n          errors[field] = `${field.replace(/_/g, ' ')} is required`;\r\n        }\r\n      });\r\n\r\n      // Email validation for primary contact\r\n      if (data.primary_contact_email && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(data.primary_contact_email)) {\r\n        errors.primary_contact_email = 'Please enter a valid email address';\r\n      }\r\n\r\n      // Email validation for secondary contact (if provided)\r\n      if (data.secondary_contact_email && data.secondary_contact_email.trim() !== '' &&\r\n          !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(data.secondary_contact_email)) {\r\n        errors.secondary_contact_email = 'Please enter a valid email address';\r\n      }\r\n\r\n\r\n\r\n      // Phone validation\r\n      if (data.primary_contact_phone && !/^[+]?[\\d\\s\\-()]+$/.test(data.primary_contact_phone)) {\r\n        errors.primary_contact_phone = 'Please enter a valid phone number';\r\n      }\r\n\r\n      if (data.secondary_contact_phone && data.secondary_contact_phone.trim() !== '' &&\r\n          !/^[+]?[\\d\\s\\-()]+$/.test(data.secondary_contact_phone)) {\r\n        errors.secondary_contact_phone = 'Please enter a valid phone number';\r\n      }\r\n\r\n\r\n\r\n      break;\r\n\r\n    default:\r\n      // No validation for unknown sections\r\n      break;\r\n  }\r\n\r\n  return {\r\n    isValid: Object.keys(errors).length === 0,\r\n    errors\r\n  };\r\n};\r\n"], "names": [], "mappings": "AAAA,4BAA4B;;;;;;;;;;;;AAcrB,MAAM,gBAAgB,CAAC,OAAY;IACxC,sBAAsB;IACtB,IAAI,MAAM,QAAQ,IAAI,CAAC,CAAC,SAAU,OAAO,UAAU,YAAY,MAAM,IAAI,OAAO,EAAG,GAAG;QACpF,OAAO;IACT;IAEA,4DAA4D;IAC5D,IAAI,CAAC,SAAU,OAAO,UAAU,YAAY,MAAM,IAAI,OAAO,IAAK;QAChE,OAAO;IACT;IAEA,qBAAqB;IACrB,IAAI,OAAO,UAAU,UAAU;QAC7B,wBAAwB;QACxB,IAAI,MAAM,SAAS,IAAI,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE;YACrD,OAAO,CAAC,iBAAiB,EAAE,MAAM,SAAS,CAAC,WAAW,CAAC;QACzD;QAEA,wBAAwB;QACxB,IAAI,MAAM,SAAS,IAAI,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE;YACrD,OAAO,CAAC,qBAAqB,EAAE,MAAM,SAAS,CAAC,WAAW,CAAC;QAC7D;QAEA,qBAAqB;QACrB,IAAI,MAAM,OAAO,IAAI,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,QAAQ;YAC/C,OAAO;QACT;IACF;IAEA,oBAAoB;IACpB,IAAI,MAAM,MAAM,EAAE;QAChB,OAAO,MAAM,MAAM,CAAC;IACtB;IAEA,OAAO;AACT;AAEO,MAAM,eAAe,CAAC,MAAW;IACtC,MAAM,SAA2B,CAAC;IAElC,OAAO,IAAI,CAAC,OAAO,OAAO,CAAC,CAAA;QACzB,MAAM,QAAQ,cAAc,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM;QACrD,IAAI,OAAO;YACT,MAAM,CAAC,MAAM,GAAG;QAClB;IACF;IAEA,OAAO;AACT;AAGO,MAAM,WAAW;IACtB,OAAO;IACP,OAAO;IACP,KAAK;IACL,cAAc;IACd,YAAY;IACZ,SAAS;IACT,YAAY;AACd;AAGO,MAAM,cAAc;IACzB,UAAU;QAAE,UAAU;IAAK;IAC3B,OAAO;QACL,UAAU;QACV,SAAS,SAAS,KAAK;QACvB,QAAQ,CAAC;YACP,IAAI,SAAS,CAAC,SAAS,KAAK,CAAC,IAAI,CAAC,QAAQ;gBACxC,OAAO;YACT;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,UAAU;QACV,SAAS,SAAS,KAAK;QACvB,QAAQ,CAAC;YACP,IAAI,SAAS,CAAC,SAAS,KAAK,CAAC,IAAI,CAAC,QAAQ;gBACxC,OAAO;YACT;YACA,OAAO;QACT;IACF;IACA,sBAAsB;QACpB,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;YACP,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;gBAC7B,OAAO;YACT;YACA,OAAO;QACT;IACF;IACA,YAAY;QACV,SAAS,SAAS,UAAU;QAC5B,QAAQ,CAAC;YACP,IAAI,SAAS,CAAC,SAAS,UAAU,CAAC,IAAI,CAAC,QAAQ;gBAC7C,OAAO;YACT;YACA,OAAO;QACT;IACF;AACF;AAGO,MAAM,YAAY,CAAC;IACxB,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,GAAG;AACtC;AAGO,MAAM,gBAAgB,CAAC;IAC5B,MAAM,WAAW,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE;IACvC,OAAO,WAAW,MAAM,CAAC,SAAS,GAAG;AACvC;AAGO,MAAM,qBAAqB,CAChC,OACA,OACA,UACA;IAEA,MAAM,cAAmD,CAAC;IAE1D,qBAAqB;IACrB,IAAI,YAAY,MAAM,MAAM,GAAG,UAAU;QACvC,YAAY,MAAM,GAAG;YAAE,QAAQ,CAAC,mBAAmB,EAAE,SAAS,MAAM,CAAC;QAAC;IACxE;IACA,IAAI,YAAY,MAAM,MAAM,GAAG,UAAU;QACvC,YAAY,MAAM,GAAG;YAAE,QAAQ,CAAC,uBAAuB,EAAE,SAAS,MAAM,CAAC;QAAC;IAC5E;IAEA,8BAA8B;IAC9B,MAAM,OAAO,CAAC,CAAC,MAAM;QACnB,MAAM,aAAa,aAAa,MAAM;QACtC,IAAI,UAAU,aAAa;YACzB,WAAW,CAAC,MAAM,GAAG;QACvB;IACF;IAEA,OAAO;AACT;AAGO,MAAM,eAAe,CAC1B,MACA,WAAoB,KAAK,EACzB,UAAkB,EAAE,EACpB,eAAyB;IAAC;CAAO;IAEjC,IAAI,YAAY,CAAC,MAAM;QACrB,OAAO;IACT;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,kBAAkB;IAClB,IAAI,KAAK,IAAI,GAAG,UAAU,OAAO,MAAM;QACrC,OAAO,CAAC,4BAA4B,EAAE,QAAQ,EAAE,CAAC;IACnD;IAEA,kBAAkB;IAClB,MAAM,gBAAgB,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI;IACxD,IAAI,CAAC,aAAa,QAAQ,CAAC,gBAAgB;QACzC,OAAO,CAAC,mBAAmB,EAAE,aAAa,IAAI,CAAC,OAAO;IACxD;IAEA,OAAO;AACT;AASO,MAAM,kBAAkB,CAAC,MAA2B;IACzD,MAAM,SAAiC,CAAC;IAExC,OAAQ;QACN,KAAK;YACH,uEAAuE;YACvE,MAAM,0BAA0B;gBAC9B;gBAAQ;gBAAgC;gBAAQ;gBAAS;gBACzD;gBAAsB;aACvB;YAED,wBAAwB,OAAO,CAAC,CAAA;gBAC9B,IAAI,CAAC,IAAI,CAAC,MAAM,IAAK,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAK;oBAClF,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC;gBAC3D;YACF;YAEA,mBAAmB;YACnB,IAAI,KAAK,KAAK,IAAI,CAAC,6BAA6B,IAAI,CAAC,KAAK,KAAK,GAAG;gBAChE,OAAO,KAAK,GAAG;YACjB;YAEA,sCAAsC;YACtC,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,IAAI,OAAO,IAAI;gBAC9C,IAAI,CAAC,iBAAiB,IAAI,CAAC,KAAK,OAAO,GAAG;oBACxC,OAAO,OAAO,GAAG;gBACnB;YACF;YAEA,sDAAsD;YACtD,IAAI,KAAK,KAAK,EAAE;gBACd,IAAI,CAAC,oBAAoB,IAAI,CAAC,KAAK,KAAK,GAAG;oBACvC,OAAO,KAAK,GAAG;gBACnB,OAAO,IAAI,KAAK,KAAK,CAAC,IAAI,GAAG,MAAM,GAAG,IAAI;oBACxC,OAAO,KAAK,GAAG;gBACjB,OAAO,IAAI,KAAK,KAAK,CAAC,IAAI,GAAG,MAAM,GAAG,IAAI;oBACxC,OAAO,KAAK,GAAG;gBACjB;YACF;YAEA,kCAAkC;YAClC,IAAI,KAAK,GAAG,IAAI,KAAK,GAAG,CAAC,IAAI,OAAO,IAAI;gBACtC,IAAI,CAAC,oBAAoB,IAAI,CAAC,KAAK,GAAG,GAAG;oBACrC,OAAO,GAAG,GAAG;gBACjB,OAAO,IAAI,KAAK,GAAG,CAAC,IAAI,GAAG,MAAM,GAAG,IAAI;oBACtC,OAAO,GAAG,GAAG;gBACf,OAAO,IAAI,KAAK,GAAG,CAAC,IAAI,GAAG,MAAM,GAAG,IAAI;oBACtC,OAAO,GAAG,GAAG;gBACf;YACF;YAEA,uDAAuD;YACvD,IAAI,KAAK,wBAAwB,IAAI,KAAK,wBAAwB,CAAC,IAAI,OAAO,MAC1E,KAAK,wBAAwB,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;gBACnD,OAAO,wBAAwB,GAAG;YACpC;YAEA,kBAAkB;YAClB,IAAI,KAAK,kBAAkB,IAAI,CAAC,sBAAsB,IAAI,CAAC,KAAK,kBAAkB,GAAG;gBACnF,OAAO,kBAAkB,GAAG;YAC9B;YAEA;QAEF,KAAK;YACH,MAAM,wBAAwB;gBAC5B;gBAAgB;gBAAgC;gBAAc;gBAC9D;gBAAsB;gBAAuB;gBAAiB;gBAC9D;gBAAmB;gBAAgB;gBAAoB;gBACvD;gBAAkB;aACnB;YAED,sBAAsB,OAAO,CAAC,CAAA;gBAC5B,IAAI,CAAC,IAAI,CAAC,MAAM,IAAK,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAK;oBAClF,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC;gBAC3D;YACF;YAEA,mBAAmB;YACnB,IAAI,KAAK,aAAa,IAAI,CAAC,6BAA6B,IAAI,CAAC,KAAK,aAAa,GAAG;gBAChF,OAAO,aAAa,GAAG;YACzB;YAEA;QAEF,KAAK;YACH,MAAM,yBAAyB;gBAC7B;gBAAkB;gBAAyB;gBAAiB;gBAC5D;gBAA0B;gBAAyB;gBACnD;gBAA0B;gBAAqB;aAChD;YAED,uBAAuB,OAAO,CAAC,CAAA;gBAC7B,IAAI,CAAC,IAAI,CAAC,MAAM,IAAK,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAK;oBAClF,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC;gBAC3D;YACF;YAEA;QAEF,KAAK;YACH,MAAM,6BAA6B;gBACjC;gBAAoB;gBAAuB;gBAC3C;gBAAoB;aACrB;YAED,2BAA2B,OAAO,CAAC,CAAA;gBACjC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAK,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAK;oBAClF,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC;gBAC3D;YACF;YAEA;QAEF,KAAK;YACH,MAAM,6BAA6B;gBACjC;gBAAqB;gBAAmB;gBACxC;gBAAiB;gBAA2B;gBAC5C;gBAAiB;aAClB;YAED,2BAA2B,OAAO,CAAC,CAAA;gBACjC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAK,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAK;oBAClF,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC;gBAC3D;YACF;YAEA;QAEF,KAAK;YACH,kBAAkB;YAClB,IAAI,CAAC,KAAK,iBAAiB,IAAI,KAAK,iBAAiB,CAAC,IAAI,OAAO,IAAI;gBACnE,OAAO,iBAAiB,GAAG;YAC7B;YAEA,+BAA+B;YAC/B,IAAI,CAAC,KAAK,oBAAoB,EAAE;gBAC9B,OAAO,oBAAoB,GAAG;YAChC;YAEA,0BAA0B;YAC1B,IAAI,KAAK,gBAAgB,IAAI,CAAC,CAAC,KAAK,gBAAgB,IAAI,KAAK,gBAAgB,CAAC,IAAI,OAAO,EAAE,GAAG;gBAC5F,OAAO,gBAAgB,GAAG;YAC5B;YAEA,IAAI,KAAK,kBAAkB,IAAI,CAAC,CAAC,KAAK,kBAAkB,IAAI,KAAK,kBAAkB,CAAC,IAAI,OAAO,EAAE,GAAG;gBAClG,OAAO,kBAAkB,GAAG;YAC9B;YAEA,IAAI,KAAK,kBAAkB,IAAI,CAAC,CAAC,KAAK,kBAAkB,IAAI,KAAK,kBAAkB,CAAC,IAAI,OAAO,EAAE,GAAG;gBAClG,OAAO,kBAAkB,GAAG;YAC9B;YAEA,IAAI,KAAK,kBAAkB,IAAI,CAAC,CAAC,KAAK,kBAAkB,IAAI,KAAK,kBAAkB,CAAC,IAAI,OAAO,EAAE,GAAG;gBAClG,OAAO,kBAAkB,GAAG;YAC9B;YAEA;QAEF,KAAK;YACH,0CAA0C;YAC1C,MAAM,wBAAwB;gBAC5B;gBAAkB;gBAAQ;aAC3B;YAED,sBAAsB,OAAO,CAAC,CAAA;gBAC5B,IAAI,CAAC,IAAI,CAAC,MAAM,IAAK,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAK;oBAClF,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC;gBAC3D;YACF;YAEA;QAEF,KAAK;YACH,0CAA0C;YAC1C,MAAM,wBAAwB;gBAC5B;gBAA8B;gBAA6B;gBAC3D;gBAAyB;aAC1B;YAED,sBAAsB,OAAO,CAAC,CAAA;gBAC5B,IAAI,CAAC,IAAI,CAAC,MAAM,IAAK,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAK;oBAClF,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC;gBAC3D;YACF;YAEA,uCAAuC;YACvC,IAAI,KAAK,qBAAqB,IAAI,CAAC,6BAA6B,IAAI,CAAC,KAAK,qBAAqB,GAAG;gBAChG,OAAO,qBAAqB,GAAG;YACjC;YAEA,uDAAuD;YACvD,IAAI,KAAK,uBAAuB,IAAI,KAAK,uBAAuB,CAAC,IAAI,OAAO,MACxE,CAAC,6BAA6B,IAAI,CAAC,KAAK,uBAAuB,GAAG;gBACpE,OAAO,uBAAuB,GAAG;YACnC;YAIA,mBAAmB;YACnB,IAAI,KAAK,qBAAqB,IAAI,CAAC,oBAAoB,IAAI,CAAC,KAAK,qBAAqB,GAAG;gBACvF,OAAO,qBAAqB,GAAG;YACjC;YAEA,IAAI,KAAK,uBAAuB,IAAI,KAAK,uBAAuB,CAAC,IAAI,OAAO,MACxE,CAAC,oBAAoB,IAAI,CAAC,KAAK,uBAAuB,GAAG;gBAC3D,OAAO,uBAAuB,GAAG;YACnC;YAIA;QAEF;YAEE;IACJ;IAEA,OAAO;QACL,SAAS,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;QACxC;IACF;AACF", "debugId": null}}, {"offset": {"line": 1668, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/types/license.ts"], "sourcesContent": ["export interface LicenseType {\r\n  license_type_id: string;\r\n  name: string;\r\n  code?: string;\r\n  description?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface LicenseCategory {\r\n  license_category_id: string;\r\n  name: string;\r\n  description?: string;\r\n  license_type_id: string;\r\n  license_type?: LicenseType;\r\n  parent_id?: string;\r\n  parent?: LicenseCategory;\r\n  children?: LicenseCategory[];\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface Applicant {\r\n  applicant_id: string;\r\n  name: string;\r\n  business_registration_number: string;\r\n  tpin: string;\r\n  website: string;\r\n  email: string;\r\n  phone: string;\r\n  fax?: string;\r\n  level_of_insurance_cover?: string;\r\n  address_id?: string;\r\n  contact_id?: string;\r\n  date_incorporation: string;\r\n  place_incorporation: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface Application {\r\n  application_id: string;\r\n  application_number: string;\r\n  applicant_id: string;\r\n  license_category_id: string;\r\n  status: ApplicationStatus;\r\n  current_step: number;\r\n  progress_percentage: number;\r\n  submitted_at?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  applicant?: Applicant;\r\n  license_category?: LicenseCategory;\r\n}\r\n\r\nexport enum ApplicationStatus {\r\n  DRAFT = 'draft',\r\n  SUBMITTED = 'submitted',\r\n  UNDER_REVIEW = 'under_review',\r\n  EVALUATION = 'evaluation',\r\n  APPROVED = 'approved',\r\n  REJECTED = 'rejected',\r\n  WITHDRAWN = 'withdrawn',\r\n}\r\n\r\nexport interface ApplicationFilters {\r\n  licenseTypeId?: string;\r\n  licenseCategoryId?: string;\r\n  status?: ApplicationStatus | '';\r\n  dateRange?: string;\r\n  search?: string;\r\n}\r\n\r\nexport interface PaginatedResponse<T> {\r\n  data: T[];\r\n  meta: {\r\n    itemsPerPage: number;\r\n    totalItems: number;\r\n    currentPage: number;\r\n    totalPages: number;\r\n    sortBy: [string, string][];\r\n    searchBy: string[];\r\n    search: string;\r\n    filter: Record<string, string | string[]>;\r\n  };\r\n  links: {\r\n    first: string;\r\n    previous: string;\r\n    current: string;\r\n    next: string;\r\n    last: string;\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAuDO,IAAA,AAAK,2CAAA;;;;;;;;WAAA", "debugId": null}}, {"offset": {"line": 1687, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/lib/customer-api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';\r\nimport Cookies from 'js-cookie';\r\nimport { processApiResponse } from './authUtils';\r\n\r\n// API Configuration\r\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';\r\n\r\n// Create axios instance for customer portal (same as staff portal)\r\nconst customerApiClient: AxiosInstance = axios.create({\r\n  baseURL: API_BASE_URL,\r\n  timeout: 15000, // Increased timeout for better reliability\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n    'Accept': 'application/json',\r\n  },\r\n});\r\n\r\n// Create auth-specific client (same as staff portal)\r\nconst customerAuthApiClient: AxiosInstance = axios.create({\r\n  baseURL: `${API_BASE_URL}/auth`,\r\n  timeout: 15000, // Increased timeout for better reliability\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n    'Accept': 'application/json',\r\n  },\r\n});\r\n\r\n// Add debug logging to auth client (only in development)\r\ncustomerAuthApiClient.interceptors.request.use(\r\n  (config) => {\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log('Customer Auth API Request:', {\r\n        url: `${config.baseURL}${config.url}`,\r\n        method: config.method,\r\n        headers: config.headers,\r\n        data: config.data\r\n      });\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    console.error('Customer Auth API Request Error:', error);\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\ncustomerAuthApiClient.interceptors.response.use(\r\n  (response) => {\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log('Customer Auth API Response Success:', {\r\n        status: response.status,\r\n        statusText: response.statusText,\r\n        url: response.config.url\r\n      });\r\n    }\r\n    return response;\r\n  },\r\n  (error) => {\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.error('Customer Auth API Interceptor Error:', {\r\n        message: error?.message || 'Unknown error',\r\n        code: error?.code || 'NO_CODE',\r\n        status: error?.response?.status || 'NO_STATUS',\r\n        statusText: error?.response?.statusText || 'NO_STATUS_TEXT',\r\n        url: error?.config?.url || 'NO_URL',\r\n        method: error?.config?.method || 'NO_METHOD',\r\n        baseURL: error?.config?.baseURL || 'NO_BASE_URL',\r\n        isAxiosError: error?.isAxiosError || false,\r\n        responseData: error?.response?.data || 'NO_RESPONSE_DATA',\r\n        requestData: error?.config?.data || 'NO_REQUEST_DATA',\r\n        headers: error?.config?.headers || 'NO_HEADERS'\r\n      });\r\n    }\r\n\r\n    // Don't handle 401 here, let the login method handle it\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Request interceptor to add auth token\r\ncustomerApiClient.interceptors.request.use(\r\n  (config) => {\r\n    const token = Cookies.get('auth_token');\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Response interceptor for error handling with retry logic\r\ncustomerApiClient.interceptors.response.use(\r\n  (response: AxiosResponse) => {\r\n    return response;\r\n  },\r\n  async (error: AxiosError) => {\r\n    const originalRequest = error.config as AxiosError['config'] & {\r\n      _retry?: boolean;\r\n      _retryCount?: number;\r\n    };\r\n\r\n    // Handle 429 Rate Limiting\r\n    if (error.response?.status === 429) {\r\n      if (!originalRequest._retry) {\r\n        originalRequest._retry = true;\r\n        \r\n        // Get retry delay from headers or use exponential backoff\r\n        const retryAfter = error.response.headers['retry-after'];\r\n        const delay = retryAfter ? parseInt(retryAfter) * 1000 : Math.min(1000 * Math.pow(2, originalRequest._retryCount || 0), 10000);\r\n        \r\n        originalRequest._retryCount = (originalRequest._retryCount || 0) + 1;\r\n        \r\n        // Don't retry more than 3 times\r\n        if (originalRequest._retryCount <= 3) {\r\n          console.warn(`Rate limited. Retrying after ${delay}ms (attempt ${originalRequest._retryCount})`);\r\n          \r\n          await new Promise(resolve => setTimeout(resolve, delay));\r\n          return customerApiClient(originalRequest);\r\n        }\r\n      }\r\n    }\r\n\r\n    // Handle 401 Unauthorized\r\n    if (error.response?.status === 401) {\r\n      // Clear auth token and redirect to login\r\n      Cookies.remove('auth_token');\r\n      Cookies.remove('auth_user');\r\n      window.location.href = '/auth/login';\r\n    }\r\n    \r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// API Service Class\r\nexport class CustomerApiService {\r\n  public api: AxiosInstance;\r\n  private pendingRequests: Map<string, Promise<unknown>> = new Map();\r\n\r\n  constructor() {\r\n    this.api = customerApiClient;\r\n  }\r\n\r\n  // Request deduplication helper\r\n  private async deduplicateRequest<T>(key: string, requestFn: () => Promise<T>): Promise<T> {\r\n    if (this.pendingRequests.has(key)) {\r\n      return this.pendingRequests.get(key) as Promise<T>;\r\n    }\r\n\r\n    const promise = requestFn().finally(() => {\r\n      this.pendingRequests.delete(key);\r\n    });\r\n\r\n    this.pendingRequests.set(key, promise);\r\n    return promise;\r\n  }\r\n\r\n  // Set auth token\r\n  setAuthToken(token: string) {\r\n    this.api.defaults.headers.common['Authorization'] = `Bearer ${token}`;\r\n  }\r\n\r\n  // Remove auth token\r\n  removeAuthToken() {\r\n    delete this.api.defaults.headers.common['Authorization'];\r\n  }\r\n\r\n  // // Authentication endpoints (copied from working staff portal)\r\n  // async login(credentials: { email: string; password: string }) {\r\n  //   try {\r\n  //     if (process.env.NODE_ENV === 'development') {\r\n  //       console.log('CustomerAPI: Starting login request with credentials:', { email: credentials.email });\r\n  //       console.log('CustomerAPI: API Base URL:', API_BASE_URL);\r\n  //       console.log('CustomerAPI: Full request URL:', `${API_BASE_URL}/auth/login`);\r\n  //       console.log('CustomerAPI: Request payload:', { email: credentials.email, password: '***' });\r\n  //     }\r\n      \r\n  //     // Make the request\r\n  //     const response = await customerAuthApiClient.post('/login', credentials);\r\n\r\n  //     if (process.env.NODE_ENV === 'development') {\r\n  //       console.log('CustomerAPI: Raw response received:', {\r\n  //         status: response.status,\r\n  //         statusText: response.statusText,\r\n  //         headers: response.headers,\r\n  //         data: processApiResponse(response),\r\n  //         dataType: typeof processApiResponse(response),\r\n  //         dataKeys: processApiResponse(response) ? Object.keys(processApiResponse(response)) : 'NO_DATA'\r\n  //       });\r\n  //     }\r\n\r\n  //     // Check if the response has the expected structure\r\n  //     // Backend returns data wrapped in a response envelope: { success, message, data, timestamp, path, statusCode }\r\n  //     if (!processApiResponse(response) || !processApiResponse(response).data) {\r\n  //       console.error('CustomerAPI: Invalid response structure:', processApiResponse(response));\r\n  //       throw new Error('Invalid response from server');\r\n  //     }\r\n\r\n  //     const authData = processApiResponse(response).data;\r\n  //     if (process.env.NODE_ENV === 'development') {\r\n  //       console.log('CustomerAPI: Extracted auth data:', {\r\n  //         authData: authData,\r\n  //         authDataType: typeof authData,\r\n  //         authDataKeys: authData ? Object.keys(authData) : 'NO_AUTH_DATA',\r\n  //         hasAccessToken: authData ? 'access_token' in authData : false,\r\n  //         hasUser: authData ? 'user' in authData : false,\r\n  //         accessTokenValue: authData?.access_token,\r\n  //         userValue: authData?.user\r\n  //       });\r\n  //     }\r\n\r\n  //     // Check if the auth data is empty (which indicates an error)\r\n  //     if (!authData || Object.keys(authData).length === 0) {\r\n  //       console.error('CustomerAPI: Empty auth data received');\r\n  //       throw new Error('Authentication failed - invalid credentials');\r\n  //     }\r\n\r\n  //     // Validate that we have the required fields\r\n  //     if (!authData.access_token || !authData.user) {\r\n  //       console.error('CustomerAPI: Missing required fields in response:', {\r\n  //         authData: authData,\r\n  //         hasToken: !!authData.access_token,\r\n  //         hasUser: !!authData.user,\r\n  //         tokenValue: authData.access_token,\r\n  //         userValue: authData.user,\r\n  //         allKeys: Object.keys(authData || {})\r\n  //       });\r\n  //       throw new Error('Authentication failed - incomplete response');\r\n  //     }\r\n\r\n  //     // Map backend field names to frontend expected format\r\n  //     // Backend response structure: { access_token, user: { user_id, first_name, last_name, email, roles, ... } }\r\n  //     const mappedAuthData = {\r\n  //       access_token: authData.access_token,\r\n  //       user: {\r\n  //         id: authData.user.user_id,\r\n  //         firstName: authData.user.first_name,\r\n  //         lastName: authData.user.last_name,\r\n  //         email: authData.user.email,\r\n  //         roles: authData.user.roles || [],\r\n  //         isAdmin: (authData.user.roles || []).includes('administrator'),\r\n  //         profileImage: authData.user.profile_image,\r\n  //         createdAt: authData.user.created_at || new Date().toISOString(),\r\n  //         lastLogin: authData.user.last_login,\r\n  //         organizationName: authData.user.organization_name,\r\n  //         two_factor_enabled: authData.user.two_factor_enabled\r\n  //       }\r\n  //     };\r\n\r\n  //     if (process.env.NODE_ENV === 'development') {\r\n  //       console.log('CustomerAPI: Mapped auth data:', mappedAuthData);\r\n  //     }\r\n  //     return mappedAuthData;\r\n  //   } catch (error) {\r\n  //     const isAxiosError = error && typeof error === 'object' && 'isAxiosError' in error;\r\n  //     const axiosError = isAxiosError ? error as AxiosError : null;\r\n      \r\n  //     if (process.env.NODE_ENV === 'development') {\r\n  //       console.error('CustomerAPI: Login error details:', {\r\n  //         error: error,\r\n  //         errorType: typeof error,\r\n  //         errorConstructor: error?.constructor?.name,\r\n  //         errorMessage: error instanceof Error ? error.message : String(error),\r\n  //         errorStack: error instanceof Error ? error.stack : undefined,\r\n  //         isAxiosError: isAxiosError,\r\n  //         responseStatus: axiosError?.response?.status,\r\n  //         responseStatusText: axiosError?.response?.statusText,\r\n  //         responseData: axiosError?.response?.data,\r\n  //         requestURL: axiosError?.config?.url,\r\n  //         requestMethod: axiosError?.config?.method,\r\n  //         requestData: axiosError?.config?.data,\r\n  //         requestHeaders: axiosError?.config?.headers\r\n  //       });\r\n  //     }\r\n  //     throw error;\r\n  //   }\r\n  // }\r\n\r\n  // async register(userData: {\r\n  //   firstName: string;\r\n  //   lastName: string;\r\n  //   email: string;\r\n  //   password: string;\r\n  //   organizationName?: string;\r\n  // }) {\r\n  //   // Map frontend field names to backend expected format\r\n  //   const backendUserData = {\r\n  //     first_name: userData.firstName,\r\n  //     last_name: userData.lastName,\r\n  //     email: userData.email,\r\n  //     password: userData.password,\r\n  //     organization_name: userData.organizationName\r\n  //   };\r\n\r\n  //   const response = await customerAuthApiClient.post('/register', backendUserData);\r\n\r\n  //   // Handle response structure consistently with login\r\n  //   if (processApiResponse(response)?.data) {\r\n  //     const authData = processApiResponse(response).data;\r\n      \r\n  //     // Map backend field names to frontend expected format\r\n  //     const mappedAuthData = {\r\n  //       access_token: authData.access_token,\r\n  //       user: {\r\n  //         id: authData.user.user_id,\r\n  //         firstName: authData.user.first_name,\r\n  //         lastName: authData.user.last_name,\r\n  //         email: authData.user.email,\r\n  //         roles: authData.user.roles || [],\r\n  //         isAdmin: (authData.user.roles || []).includes('administrator'),\r\n  //         profileImage: authData.user.profile_image,\r\n  //         createdAt: authData.user.created_at || new Date().toISOString(),\r\n  //         lastLogin: authData.user.last_login,\r\n  //         organizationName: authData.user.organization_name\r\n  //       }\r\n  //     };\r\n      \r\n  //     return mappedAuthData;\r\n  //   }\r\n\r\n  //   // If no nested data property, return direct response\r\n  //   return processApiResponse(response);\r\n  // }\r\n\r\n  async logout() {\r\n    const response = await customerAuthApiClient.post('/logout');\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async refreshToken() {\r\n    const response = await customerAuthApiClient.post('/refresh');\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // 2FA endpoints\r\n  async generateTwoFactorCode(userId: string, action: string) {\r\n    const response = await customerAuthApiClient.post('/generate-2fa', { user_id: userId, action });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async verify2FA(data: { user_id: string; code: string; unique: string }) {\r\n    const response = await customerAuthApiClient.post('/verify-2fa', data);\r\n\r\n    // Handle response structure consistently with login\r\n    if (processApiResponse(response)?.data) {\r\n      const authData = processApiResponse(response).data;\r\n      \r\n      // Map backend field names to frontend expected format\r\n      const mappedAuthData = {\r\n        access_token: authData.access_token,\r\n        user: {\r\n          id: authData.user.user_id,\r\n          firstName: authData.user.first_name,\r\n          lastName: authData.user.last_name,\r\n          email: authData.user.email,\r\n          roles: authData.user.roles || [],\r\n          isAdmin: (authData.user.roles || []).includes('administrator'),\r\n          profileImage: authData.user.profile_image,\r\n          createdAt: authData.user.created_at || new Date().toISOString(),\r\n          lastLogin: authData.user.last_login,\r\n          organizationName: authData.user.organization_name,\r\n          two_factor_enabled: authData.user.two_factor_enabled\r\n        }\r\n      };\r\n      \r\n      return mappedAuthData;\r\n    }\r\n\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async setupTwoFactorAuth(data: { access_token: string; user_id: string }) {\r\n    const response = await customerAuthApiClient.post('/setup-2fa', data);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // User profile endpoints\r\n  async getProfile() {\r\n    return this.deduplicateRequest('getProfile', async () => {\r\n      const response = await this.api.get('/users/profile');\r\n      return processApiResponse(response);\r\n    });\r\n  }\r\n\r\n  async updateProfile(profileData: ProfileUpdateData) {\r\n    const response = await this.api.put('/users/profile', profileData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Addressing endpoints\r\n  async getAddresses() {\r\n    const response = await this.api.get('/address/all');\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async createAddress(addressData: CreateAddressData) {\r\n    const response = await this.api.post('/address/create', addressData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getAddress(id: string) {\r\n    const response = await this.api.get(`/address/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async editAddress(addressData: EditAddressData) {\r\n    const { address_id, ...updateData } = addressData;\r\n    if (!address_id) {\r\n      throw new Error('Address ID is required for updating');\r\n    }\r\n    const response = await this.api.put(`/address/${address_id}`, updateData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getAddressesByEntity(entityType: string, entityId: string) {\r\n    const response = await this.api.get(`/address/all?entity_type=${encodeURIComponent(entityType)}&entity_id=${encodeURIComponent(entityId)}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async deleteAddress(id: string) {\r\n    const response = await this.api.delete(`/address/soft/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async searchPostcodes(searchParams: SearchPostcodes) {\r\n    const response = await this.api.post('/postal-codes/search', searchParams);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // License endpoints\r\n  async getLicenses(params?: { status?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/licenses', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getLicense(id: string) {\r\n    const response = await this.api.get(`/licenses/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async createLicenseApplication(applicationData: LicenseApplicationData) {\r\n    const response = await this.api.post('/license-applications', applicationData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // License Types endpoints\r\n  async getLicenseTypes(params?: { page?: number; limit?: number }) {\r\n    const response = await this.api.get('/license-types', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getLicenseType(id: string) {\r\n    const response = await this.api.get(`/license-types/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // License Categories endpoints\r\n  async getLicenseCategories(params?: { page?: number; limit?: number }) {\r\n    const response = await this.api.get('/license-categories', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getLicenseCategoriesByType(licenseTypeId: string) {\r\n    const response = await this.api.get(`/license-categories/by-license-type/${licenseTypeId}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getLicenseCategoryTree(licenseTypeId: string) {\r\n    const response = await this.api.get(`/license-categories/license-type/${licenseTypeId}/tree`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getLicenseCategory(id: string) {\r\n    const response = await this.api.get(`/license-categories/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Application endpoints\r\n  async getApplications(params?: { status?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/applications', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getApplication(id: string) {\r\n    const response = await this.api.get(`/applications/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async createApplication(applicationData: any) {\r\n    const response = await this.api.post('/applications', applicationData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async updateApplication(id: string, applicationData: Partial<LicenseApplicationData>) {\r\n    const response = await this.api.put(`/applications/${id}`, applicationData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Payment endpoints\r\n  async getPayments(params?: { status?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/payments', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getPayment(id: string) {\r\n    const response = await this.api.get(`/payments/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async createPayment(paymentData: PaymentCreateData) {\r\n    const response = await this.api.post('/payments', paymentData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Document endpoints\r\n  async getDocuments(params?: { type?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/documents', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async uploadDocument(formData: FormData) {\r\n    const response = await this.api.post('/documents/upload', formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async downloadDocument(id: string) {\r\n    const response = await this.api.get(`/documents/${id}/download`, {\r\n      responseType: 'blob',\r\n    });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Dashboard statistics\r\n  async getDashboardStats() {\r\n    const response = await this.api.get('/dashboard/stats');\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Notifications\r\n  async getNotifications(params?: { read?: boolean; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/notifications', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async markNotificationAsRead(id: string) {\r\n    const response = await this.api.patch(`/notifications/${id}/read`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Procurement endpoints\r\n  async getTenders(params?: { status?: string; category?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/procurement/tenders', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getTender(id: string) {\r\n    const response = await this.api.get(`/procurement/tenders/${id}`);\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async payForTenderAccess(tenderId: string, paymentData: TenderPaymentData) {\r\n    const response = await this.api.post(`/procurement/tenders/${tenderId}/pay-access`, paymentData);\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async downloadTenderDocument(documentId: string) {\r\n    const response = await this.api.get(`/procurement/documents/${documentId}/download`, {\r\n      responseType: 'blob',\r\n    });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async getMyBids(params?: { status?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/procurement/my-bids', { params });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async getBid(id: string) {\r\n    const response = await this.api.get(`/procurement/bids/${id}`);\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async submitBid(formData: FormData) {\r\n    const response = await this.api.post('/procurement/bids', formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async updateBid(id: string, formData: FormData) {\r\n    const response = await this.api.put(`/procurement/bids/${id}`, formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async getProcurementPayments(params?: { status?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/procurement/payments', { params });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async getProcurementPayment(id: string) {\r\n    const response = await this.api.get(`/procurement/payments/${id}`);\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  // Consumer Affairs endpoints\r\n  async getComplaints(params?: { status?: string; category?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/consumer-affairs/complaints', { params });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async getComplaint(id: string) {\r\n    const response = await this.api.get(`/consumer-affairs/complaints/${id}`);\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async submitComplaint(complaintData: ComplaintData) {\r\n    const formData = new FormData();\r\n    formData.append('title', complaintData.title);\r\n    formData.append('description', complaintData.description);\r\n    formData.append('category', complaintData.category);\r\n\r\n    if (complaintData.attachments) {\r\n      complaintData.attachments.forEach((file, index) => {\r\n        formData.append(`attachments[${index}]`, file);\r\n      });\r\n    }\r\n\r\n    const response = await this.api.post('/consumer-affairs/complaints', formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async updateComplaint(id: string, updates: Partial<ComplaintData>) {\r\n    const response = await this.api.put(`/consumer-affairs/complaints/${id}`, updates);\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async downloadComplaintAttachment(complaintId: string, attachmentId: string) {\r\n    const response = await this.api.get(`/consumer-affairs/complaints/${complaintId}/attachments/${attachmentId}/download`, {\r\n      responseType: 'blob',\r\n    });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const customerApi = new CustomerApiService();\r\n\r\n// Export axios instance for direct use if needed\r\nexport { customerApiClient };\r\n\r\n// Export types\r\nexport interface ApiResponse<T = unknown> {\r\n  success: boolean;\r\n  data: T;\r\n  message?: string;\r\n  errors?: string[] | { [key: string]: string[] } | string;\r\n}\r\n\r\nexport interface PaginatedResponse<T = unknown> {\r\n  data: T[];\r\n  total: number;\r\n  page: number;\r\n  limit: number;\r\n  totalPages: number;\r\n}\r\n\r\nexport interface License {\r\n  id: string;\r\n  licenseNumber: string;\r\n  type: string;\r\n  status: 'active' | 'expired' | 'suspended' | 'pending';\r\n  issueDate: string;\r\n  expirationDate: string;\r\n  organizationName: string;\r\n  description?: string;\r\n}\r\n\r\nexport interface Application {\r\n  id: string;\r\n  applicationNumber: string;\r\n  type: string;\r\n  status: 'draft' | 'submitted' | 'under_review' | 'approved' | 'rejected';\r\n  submittedDate: string;\r\n  lastUpdated: string;\r\n  organizationName: string;\r\n  description?: string;\r\n}\r\n\r\nexport interface Payment {\r\n  id: string;\r\n  invoiceNumber: string;\r\n  amount: number;\r\n  currency: string;\r\n  status: 'pending' | 'paid' | 'overdue' | 'cancelled';\r\n  dueDate: string;\r\n  paidDate?: string;\r\n  issueDate: string;\r\n  description: string;\r\n  paymentType: string;\r\n  clientName: string;\r\n  clientEmail: string;\r\n  paymentMethod?: string;\r\n  notes?: string;\r\n  relatedLicense?: string;\r\n  relatedApplication?: string;\r\n}\r\n\r\nexport interface User {\r\n  id: string;\r\n  firstName: string;\r\n  lastName: string;\r\n  email: string;\r\n  organizationName?: string;\r\n  roles: string[];\r\n  isAdmin: boolean;\r\n  profileImage?: string;\r\n  createdAt: string;\r\n  lastLogin?: string;\r\n  phone?: string;\r\n  address?: string;\r\n  city?: string;\r\n  country?: string;\r\n  two_factor_enabled?: boolean;\r\n}\r\n\r\nexport interface ProfileUpdateData {\r\n  firstName?: string;\r\n  lastName?: string;\r\n  email?: string;\r\n  organizationName?: string;\r\n  profileImage?: string;\r\n}\r\n\r\nexport interface CreateAddressData {\r\n  address_type: string;\r\n  entity_type?: string;\r\n  entity_id?:string;\r\n  address_line_1: string;\r\n  address_line_2?: string;\r\n  postal_code: string;\r\n  country: string;\r\n  city: string;\r\n}\r\n\r\nexport interface EditAddressData {\r\n  address_id: string;\r\n  address_type?: string;\r\n  address_line_1?: string;\r\n  address_line_2?: string;\r\n  postal_code?: string;\r\n  country?: string;\r\n  city?: string;\r\n}\r\n\r\nexport interface SearchPostcodes {\r\n  region?: string;\r\n  district?: string;\r\n  location?: string;\r\n  postal_code?: string;\r\n}\r\n\r\nexport interface PostalCodeLookupResult {\r\n  postal_code_id: string;\r\n  region: string;\r\n  district: string;\r\n  location: string;\r\n  postal_code: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  deleted_at?: string | null;\r\n}\r\n\r\n\r\nexport interface LicenseApplicationData {\r\n  type: string;\r\n  organizationName: string;\r\n  description?: string;\r\n  contactEmail?: string;\r\n  contactPhone?: string;\r\n  businessAddress?: string;\r\n  businessType?: string;\r\n  requestedStartDate?: string;\r\n  additionalDocuments?: string[];\r\n  notes?: string;\r\n}\r\n\r\nexport interface PaymentCreateData {\r\n  amount: number;\r\n  currency: string;\r\n  dueDate: string;\r\n  issueDate: string;\r\n  description: string;\r\n  paymentType: string;\r\n  clientName: string;\r\n  clientEmail: string;\r\n  paymentMethod?: string;\r\n  notes?: string;\r\n  relatedLicense?: string;\r\n  relatedApplication?: string;\r\n}\r\n\r\nexport interface TenderPaymentData {\r\n  amount: number;\r\n  currency: string;\r\n  paymentMethod: string;\r\n  description?: string;\r\n}\r\n\r\nexport interface ComplaintData {\r\n  title: string;\r\n  description: string;\r\n  category: string;\r\n  attachments?: File[];\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAEA,oBAAoB;AACpB,MAAM,eAAe,QAAQ,GAAG,CAAC,mBAAmB,IAAI;AAExD,mEAAmE;AACnE,MAAM,oBAAmC,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACpD,SAAS;IACT,SAAS;IACT,SAAS;QACP,gBAAgB;QAChB,UAAU;IACZ;AACF;AAEA,qDAAqD;AACrD,MAAM,wBAAuC,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACxD,SAAS,GAAG,aAAa,KAAK,CAAC;IAC/B,SAAS;IACT,SAAS;QACP,gBAAgB;QAChB,UAAU;IACZ;AACF;AAEA,yDAAyD;AACzD,sBAAsB,YAAY,CAAC,OAAO,CAAC,GAAG,CAC5C,CAAC;IACC,wCAA4C;QAC1C,QAAQ,GAAG,CAAC,8BAA8B;YACxC,KAAK,GAAG,OAAO,OAAO,GAAG,OAAO,GAAG,EAAE;YACrC,QAAQ,OAAO,MAAM;YACrB,SAAS,OAAO,OAAO;YACvB,MAAM,OAAO,IAAI;QACnB;IACF;IACA,OAAO;AACT,GACA,CAAC;IACC,QAAQ,KAAK,CAAC,oCAAoC;IAClD,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,sBAAsB,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC7C,CAAC;IACC,wCAA4C;QAC1C,QAAQ,GAAG,CAAC,uCAAuC;YACjD,QAAQ,SAAS,MAAM;YACvB,YAAY,SAAS,UAAU;YAC/B,KAAK,SAAS,MAAM,CAAC,GAAG;QAC1B;IACF;IACA,OAAO;AACT,GACA,CAAC;IACC,wCAA4C;QAC1C,QAAQ,KAAK,CAAC,wCAAwC;YACpD,SAAS,OAAO,WAAW;YAC3B,MAAM,OAAO,QAAQ;YACrB,QAAQ,OAAO,UAAU,UAAU;YACnC,YAAY,OAAO,UAAU,cAAc;YAC3C,KAAK,OAAO,QAAQ,OAAO;YAC3B,QAAQ,OAAO,QAAQ,UAAU;YACjC,SAAS,OAAO,QAAQ,WAAW;YACnC,cAAc,OAAO,gBAAgB;YACrC,cAAc,OAAO,UAAU,QAAQ;YACvC,aAAa,OAAO,QAAQ,QAAQ;YACpC,SAAS,OAAO,QAAQ,WAAW;QACrC;IACF;IAEA,wDAAwD;IACxD,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,wCAAwC;AACxC,kBAAkB,YAAY,CAAC,OAAO,CAAC,GAAG,CACxC,CAAC;IACC,MAAM,QAAQ,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;IAC1B,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,2DAA2D;AAC3D,kBAAkB,YAAY,CAAC,QAAQ,CAAC,GAAG,CACzC,CAAC;IACC,OAAO;AACT,GACA,OAAO;IACL,MAAM,kBAAkB,MAAM,MAAM;IAKpC,2BAA2B;IAC3B,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;QAClC,IAAI,CAAC,gBAAgB,MAAM,EAAE;YAC3B,gBAAgB,MAAM,GAAG;YAEzB,0DAA0D;YAC1D,MAAM,aAAa,MAAM,QAAQ,CAAC,OAAO,CAAC,cAAc;YACxD,MAAM,QAAQ,aAAa,SAAS,cAAc,OAAO,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,CAAC,GAAG,gBAAgB,WAAW,IAAI,IAAI;YAExH,gBAAgB,WAAW,GAAG,CAAC,gBAAgB,WAAW,IAAI,CAAC,IAAI;YAEnE,gCAAgC;YAChC,IAAI,gBAAgB,WAAW,IAAI,GAAG;gBACpC,QAAQ,IAAI,CAAC,CAAC,6BAA6B,EAAE,MAAM,YAAY,EAAE,gBAAgB,WAAW,CAAC,CAAC,CAAC;gBAE/F,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACjD,OAAO,kBAAkB;YAC3B;QACF;IACF;IAEA,0BAA0B;IAC1B,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;QAClC,yCAAyC;QACzC,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACf,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACf,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAIK,MAAM;IACJ,IAAmB;IAClB,kBAAiD,IAAI,MAAM;IAEnE,aAAc;QACZ,IAAI,CAAC,GAAG,GAAG;IACb;IAEA,+BAA+B;IAC/B,MAAc,mBAAsB,GAAW,EAAE,SAA2B,EAAc;QACxF,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM;YACjC,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;QAClC;QAEA,MAAM,UAAU,YAAY,OAAO,CAAC;YAClC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;QAC9B;QAEA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK;QAC9B,OAAO;IACT;IAEA,iBAAiB;IACjB,aAAa,KAAa,EAAE;QAC1B,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;IACvE;IAEA,oBAAoB;IACpB,kBAAkB;QAChB,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB;IAC1D;IAEA,iEAAiE;IACjE,kEAAkE;IAClE,UAAU;IACV,oDAAoD;IACpD,4GAA4G;IAC5G,iEAAiE;IACjE,qFAAqF;IACrF,qGAAqG;IACrG,QAAQ;IAER,0BAA0B;IAC1B,gFAAgF;IAEhF,oDAAoD;IACpD,6DAA6D;IAC7D,mCAAmC;IACnC,2CAA2C;IAC3C,qCAAqC;IACrC,8CAA8C;IAC9C,yDAAyD;IACzD,yGAAyG;IACzG,YAAY;IACZ,QAAQ;IAER,0DAA0D;IAC1D,sHAAsH;IACtH,iFAAiF;IACjF,iGAAiG;IACjG,yDAAyD;IACzD,QAAQ;IAER,0DAA0D;IAC1D,oDAAoD;IACpD,2DAA2D;IAC3D,8BAA8B;IAC9B,yCAAyC;IACzC,2EAA2E;IAC3E,yEAAyE;IACzE,0DAA0D;IAC1D,oDAAoD;IACpD,oCAAoC;IACpC,YAAY;IACZ,QAAQ;IAER,oEAAoE;IACpE,6DAA6D;IAC7D,gEAAgE;IAChE,wEAAwE;IACxE,QAAQ;IAER,mDAAmD;IACnD,sDAAsD;IACtD,6EAA6E;IAC7E,8BAA8B;IAC9B,6CAA6C;IAC7C,oCAAoC;IACpC,6CAA6C;IAC7C,oCAAoC;IACpC,+CAA+C;IAC/C,YAAY;IACZ,wEAAwE;IACxE,QAAQ;IAER,6DAA6D;IAC7D,mHAAmH;IACnH,+BAA+B;IAC/B,6CAA6C;IAC7C,gBAAgB;IAChB,qCAAqC;IACrC,+CAA+C;IAC/C,6CAA6C;IAC7C,sCAAsC;IACtC,4CAA4C;IAC5C,0EAA0E;IAC1E,qDAAqD;IACrD,2EAA2E;IAC3E,+CAA+C;IAC/C,6DAA6D;IAC7D,+DAA+D;IAC/D,UAAU;IACV,SAAS;IAET,oDAAoD;IACpD,uEAAuE;IACvE,QAAQ;IACR,6BAA6B;IAC7B,sBAAsB;IACtB,0FAA0F;IAC1F,oEAAoE;IAEpE,oDAAoD;IACpD,6DAA6D;IAC7D,wBAAwB;IACxB,mCAAmC;IACnC,sDAAsD;IACtD,gFAAgF;IAChF,wEAAwE;IACxE,sCAAsC;IACtC,wDAAwD;IACxD,gEAAgE;IAChE,oDAAoD;IACpD,+CAA+C;IAC/C,qDAAqD;IACrD,iDAAiD;IACjD,sDAAsD;IACtD,YAAY;IACZ,QAAQ;IACR,mBAAmB;IACnB,MAAM;IACN,IAAI;IAEJ,6BAA6B;IAC7B,uBAAuB;IACvB,sBAAsB;IACtB,mBAAmB;IACnB,sBAAsB;IACtB,+BAA+B;IAC/B,OAAO;IACP,2DAA2D;IAC3D,8BAA8B;IAC9B,sCAAsC;IACtC,oCAAoC;IACpC,6BAA6B;IAC7B,mCAAmC;IACnC,mDAAmD;IACnD,OAAO;IAEP,qFAAqF;IAErF,yDAAyD;IACzD,8CAA8C;IAC9C,0DAA0D;IAE1D,6DAA6D;IAC7D,+BAA+B;IAC/B,6CAA6C;IAC7C,gBAAgB;IAChB,qCAAqC;IACrC,+CAA+C;IAC/C,6CAA6C;IAC7C,sCAAsC;IACtC,4CAA4C;IAC5C,0EAA0E;IAC1E,qDAAqD;IACrD,2EAA2E;IAC3E,+CAA+C;IAC/C,4DAA4D;IAC5D,UAAU;IACV,SAAS;IAET,6BAA6B;IAC7B,MAAM;IAEN,0DAA0D;IAC1D,yCAAyC;IACzC,IAAI;IAEJ,MAAM,SAAS;QACb,MAAM,WAAW,MAAM,sBAAsB,IAAI,CAAC;QAClD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,eAAe;QACnB,MAAM,WAAW,MAAM,sBAAsB,IAAI,CAAC;QAClD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gBAAgB;IAChB,MAAM,sBAAsB,MAAc,EAAE,MAAc,EAAE;QAC1D,MAAM,WAAW,MAAM,sBAAsB,IAAI,CAAC,iBAAiB;YAAE,SAAS;YAAQ;QAAO;QAC7F,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,UAAU,IAAuD,EAAE;QACvE,MAAM,WAAW,MAAM,sBAAsB,IAAI,CAAC,eAAe;QAEjE,oDAAoD;QACpD,IAAI,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW,MAAM;YACtC,MAAM,WAAW,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU,IAAI;YAElD,sDAAsD;YACtD,MAAM,iBAAiB;gBACrB,cAAc,SAAS,YAAY;gBACnC,MAAM;oBACJ,IAAI,SAAS,IAAI,CAAC,OAAO;oBACzB,WAAW,SAAS,IAAI,CAAC,UAAU;oBACnC,UAAU,SAAS,IAAI,CAAC,SAAS;oBACjC,OAAO,SAAS,IAAI,CAAC,KAAK;oBAC1B,OAAO,SAAS,IAAI,CAAC,KAAK,IAAI,EAAE;oBAChC,SAAS,CAAC,SAAS,IAAI,CAAC,KAAK,IAAI,EAAE,EAAE,QAAQ,CAAC;oBAC9C,cAAc,SAAS,IAAI,CAAC,aAAa;oBACzC,WAAW,SAAS,IAAI,CAAC,UAAU,IAAI,IAAI,OAAO,WAAW;oBAC7D,WAAW,SAAS,IAAI,CAAC,UAAU;oBACnC,kBAAkB,SAAS,IAAI,CAAC,iBAAiB;oBACjD,oBAAoB,SAAS,IAAI,CAAC,kBAAkB;gBACtD;YACF;YAEA,OAAO;QACT;QAEA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,mBAAmB,IAA+C,EAAE;QACxE,MAAM,WAAW,MAAM,sBAAsB,IAAI,CAAC,cAAc;QAChE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,yBAAyB;IACzB,MAAM,aAAa;QACjB,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc;YAC3C,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;YACpC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B;IACF;IAEA,MAAM,cAAc,WAA8B,EAAE;QAClD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,kBAAkB;QACtD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,uBAAuB;IACvB,MAAM,eAAe;QACnB,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;QACpC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,cAAc,WAA8B,EAAE;QAClD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,mBAAmB;QACxD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,WAAW,EAAU,EAAE;QAC3B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI;QACpD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,YAAY,WAA4B,EAAE;QAC9C,MAAM,EAAE,UAAU,EAAE,GAAG,YAAY,GAAG;QACtC,IAAI,CAAC,YAAY;YACf,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,YAAY,EAAE;QAC9D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,qBAAqB,UAAkB,EAAE,QAAgB,EAAE;QAC/D,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,yBAAyB,EAAE,mBAAmB,YAAY,WAAW,EAAE,mBAAmB,WAAW;QAC1I,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,cAAc,EAAU,EAAE;QAC9B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,cAAc,EAAE,IAAI;QAC5D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,gBAAgB,YAA6B,EAAE;QACnD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,wBAAwB;QAC7D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,oBAAoB;IACpB,MAAM,YAAY,MAA2D,EAAE;QAC7E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,aAAa;YAAE;QAAO;QAC1D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,WAAW,EAAU,EAAE;QAC3B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI;QACrD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,yBAAyB,eAAuC,EAAE;QACtE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,yBAAyB;QAC9D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,0BAA0B;IAC1B,MAAM,gBAAgB,MAA0C,EAAE;QAChE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,kBAAkB;YAAE;QAAO;QAC/D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,eAAe,EAAU,EAAE;QAC/B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,IAAI;QAC1D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,+BAA+B;IAC/B,MAAM,qBAAqB,MAA0C,EAAE;QACrE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,uBAAuB;YAAE;QAAO;QACpE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,2BAA2B,aAAqB,EAAE;QACtD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,oCAAoC,EAAE,eAAe;QAC1F,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,uBAAuB,aAAqB,EAAE;QAClD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,KAAK,CAAC;QAC5F,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,mBAAmB,EAAU,EAAE;QACnC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,IAAI;QAC/D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,wBAAwB;IACxB,MAAM,gBAAgB,MAA2D,EAAE;QACjF,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,iBAAiB;YAAE;QAAO;QAC9D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,eAAe,EAAU,EAAE;QAC/B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI;QACzD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,kBAAkB,eAAoB,EAAE;QAC5C,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB;QACtD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,kBAAkB,EAAU,EAAE,eAAgD,EAAE;QACpF,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI,EAAE;QAC3D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,oBAAoB;IACpB,MAAM,YAAY,MAA2D,EAAE;QAC7E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,aAAa;YAAE;QAAO;QAC1D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,WAAW,EAAU,EAAE;QAC3B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI;QACrD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,cAAc,WAA8B,EAAE;QAClD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa;QAClD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,qBAAqB;IACrB,MAAM,aAAa,MAAyD,EAAE;QAC5E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,cAAc;YAAE;QAAO;QAC3D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,eAAe,QAAkB,EAAE;QACvC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,qBAAqB,UAAU;YAClE,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,iBAAiB,EAAU,EAAE;QACjC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,GAAG,SAAS,CAAC,EAAE;YAC/D,cAAc;QAChB;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,uBAAuB;IACvB,MAAM,oBAAoB;QACxB,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;QACpC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gBAAgB;IAChB,MAAM,iBAAiB,MAA0D,EAAE;QACjF,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,kBAAkB;YAAE;QAAO;QAC/D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,uBAAuB,EAAU,EAAE;QACvC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,eAAe,EAAE,GAAG,KAAK,CAAC;QACjE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,wBAAwB;IACxB,MAAM,WAAW,MAA8E,EAAE;QAC/F,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,wBAAwB;YAAE;QAAO;QACrE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,UAAU,EAAU,EAAE;QAC1B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,IAAI;QAChE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,mBAAmB,QAAgB,EAAE,WAA8B,EAAE;QACzE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,qBAAqB,EAAE,SAAS,WAAW,CAAC,EAAE;QACpF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,uBAAuB,UAAkB,EAAE;QAC/C,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,WAAW,SAAS,CAAC,EAAE;YACnF,cAAc;QAChB;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,UAAU,MAA2D,EAAE;QAC3E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,wBAAwB;YAAE;QAAO;QACrE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,OAAO,EAAU,EAAE;QACvB,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,IAAI;QAC7D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,UAAU,QAAkB,EAAE;QAClC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,qBAAqB,UAAU;YAClE,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,UAAU,EAAU,EAAE,QAAkB,EAAE;QAC9C,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,IAAI,EAAE,UAAU;YACvE,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,uBAAuB,MAA2D,EAAE;QACxF,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,yBAAyB;YAAE;QAAO;QACtE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,sBAAsB,EAAU,EAAE;QACtC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,sBAAsB,EAAE,IAAI;QACjE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,6BAA6B;IAC7B,MAAM,cAAc,MAA8E,EAAE;QAClG,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,gCAAgC;YAAE;QAAO;QAC7E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,aAAa,EAAU,EAAE;QAC7B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,IAAI;QACxE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,gBAAgB,aAA4B,EAAE;QAClD,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS,cAAc,KAAK;QAC5C,SAAS,MAAM,CAAC,eAAe,cAAc,WAAW;QACxD,SAAS,MAAM,CAAC,YAAY,cAAc,QAAQ;QAElD,IAAI,cAAc,WAAW,EAAE;YAC7B,cAAc,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM;gBACvC,SAAS,MAAM,CAAC,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC,EAAE;YAC3C;QACF;QAEA,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,gCAAgC,UAAU;YAC7E,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,gBAAgB,EAAU,EAAE,OAA+B,EAAE;QACjE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,IAAI,EAAE;QAC1E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,4BAA4B,WAAmB,EAAE,YAAoB,EAAE;QAC3E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,YAAY,aAAa,EAAE,aAAa,SAAS,CAAC,EAAE;YACtH,cAAc;QAChB;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;AACF;AAGO,MAAM,cAAc,IAAI", "debugId": null}}, {"offset": {"line": 2277, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/config/licenseTypeStepConfig.ts"], "sourcesContent": ["/**\r\n * License Type Step Configuration System\r\n *\r\n * SINGLE SOURCE OF TRUTH for all license type step configurations\r\n *\r\n * This is the consolidated configuration system that defines:\r\n * - Which form steps are required for each license type\r\n * - Step order and navigation flow\r\n * - Validation requirements and estimated times\r\n * - Fallback configurations for unknown license types\r\n *\r\n * Supports the 5 specified license type codes:\r\n * - telecommunications\r\n * - postal_services\r\n * - standards_compliance\r\n * - broadcasting\r\n * - spectrum_management\r\n *\r\n * Features:\r\n * - Optimized step loading based on license type codes\r\n * - Automatic fallback for unsupported types\r\n * - Smart license type resolution (UUID, code, name mapping)\r\n * - Comprehensive helper functions for navigation and progress tracking\r\n */\r\n\r\nexport interface StepConfig {\r\n  id: string;\r\n  name: string;\r\n  component: string;\r\n  route: string;\r\n  required: boolean;\r\n  description: string;\r\n  estimatedTime: string; // in minutes\r\n}\r\n\r\nexport interface LicenseTypeStepConfig {\r\n  licenseTypeId: string;\r\n  name: string;\r\n  description: string;\r\n  steps: StepConfig[];\r\n  estimatedTotalTime: string;\r\n  requirements: string[];\r\n}\r\n\r\n// Base steps that can be used across license types\r\nconst BASE_STEPS: Record<string, StepConfig> = {\r\n  applicantInfo: {\r\n    id: 'applicant-info',\r\n    name: 'Applicant Information',\r\n    component: 'ApplicantInfo',\r\n    route: 'applicant-info',\r\n    required: true,\r\n    description: 'Personal or company information of the applicant',\r\n    estimatedTime: '5'\r\n  },\r\n  addressInfo: {\r\n    id: 'address-info',\r\n    name: 'Address Information',\r\n    component: 'AddressInfo',\r\n    route: 'address-info',\r\n    required: true,\r\n    description: 'Physical and postal address details',\r\n    estimatedTime: '3'\r\n  },\r\n  contactInfo: {\r\n    id: 'contact-info',\r\n    name: 'Contact Information',\r\n    component: 'ContactInfo',\r\n    route: 'contact-info',\r\n    required: true,\r\n    description: 'Contact details and communication preferences',\r\n    estimatedTime: '5'\r\n  },\r\n\r\n  management: {\r\n    id: 'management',\r\n    name: 'Management Structure',\r\n    component: 'Management',\r\n    route: 'management',\r\n    required: false,\r\n    description: 'Management team and organizational structure',\r\n    estimatedTime: '8'\r\n  },\r\n  professionalServices: {\r\n    id: 'professional-services',\r\n    name: 'Professional Services',\r\n    component: 'ProfessionalServices',\r\n    route: 'professional-services',\r\n    required: false,\r\n    description: 'External consultants and service providers',\r\n    estimatedTime: '6'\r\n  },\r\n  serviceScope: {\r\n    id: 'service-scope',\r\n    name: 'Service Scope',\r\n    component: 'ServiceScope',\r\n    route: 'service-scope',\r\n    required: true,\r\n    description: 'Services offered and geographic coverage',\r\n    estimatedTime: '8'\r\n  },\r\n\r\n  legalHistory: {\r\n    id: 'legal-history',\r\n    name: 'Legal History',\r\n    component: 'LegalHistory',\r\n    route: 'legal-history',\r\n    required: true,\r\n    description: 'Legal compliance and regulatory history',\r\n    estimatedTime: '5'\r\n  },\r\n  documents: {\r\n    id: 'documents',\r\n    name: 'Required Documents',\r\n    component: 'Documents',\r\n    route: 'documents',\r\n    required: true,\r\n    description: 'Upload required documents for license application',\r\n    estimatedTime: '10'\r\n  },\r\n  submit: {\r\n    id: 'submit',\r\n    name: 'Submit Application',\r\n    component: 'Submit',\r\n    route: 'submit',\r\n    required: true,\r\n    description: 'Review and submit your application',\r\n    estimatedTime: '5'\r\n  }\r\n};\r\n\r\n// License type specific configurations\r\nexport const LICENSE_TYPE_STEP_CONFIGS: Record<string, LicenseTypeStepConfig> = {\r\n  telecommunications: {\r\n    licenseTypeId: 'telecommunications',\r\n    name: 'Telecommunications License',\r\n    description: 'License for telecommunications service providers including ISPs, mobile operators, and fixed-line services',\r\n    steps: [\r\n      BASE_STEPS.applicantInfo,\r\n      BASE_STEPS.addressInfo,\r\n      BASE_STEPS.contactInfo,\r\n      BASE_STEPS.management,\r\n      BASE_STEPS.serviceScope,\r\n      BASE_STEPS.legalHistory,\r\n      BASE_STEPS.documents,\r\n      BASE_STEPS.submit\r\n    ],\r\n    estimatedTotalTime: '97 minutes',\r\n    requirements: [\r\n      'Business registration certificate',\r\n      'Tax compliance certificate',\r\n      'Technical specifications',\r\n      'Financial statements',\r\n      'Management CVs',\r\n      'Network coverage plans'\r\n    ]\r\n  },\r\n\r\n  postal_services: {\r\n    licenseTypeId: 'postal_services',\r\n    name: 'Postal Services License',\r\n    description: 'License for postal and courier service providers',\r\n    steps: [\r\n      BASE_STEPS.applicantInfo,\r\n      BASE_STEPS.addressInfo,\r\n      BASE_STEPS.contactInfo,\r\n      BASE_STEPS.legalHistory,\r\n      BASE_STEPS.documents,\r\n      BASE_STEPS.submit\r\n    ],\r\n    estimatedTotalTime: '65 minutes',\r\n    requirements: [\r\n      'Business registration certificate',\r\n      'Fleet inventory',\r\n      'Service coverage map',\r\n      'Insurance certificates',\r\n      'Premises documentation'\r\n    ]\r\n  },\r\n\r\n  standards_compliance: {\r\n    licenseTypeId: 'standards_compliance',\r\n    name: 'Standards Compliance License',\r\n    description: 'License for standards compliance and certification services',\r\n    steps: [\r\n      BASE_STEPS.applicantInfo,\r\n      BASE_STEPS.addressInfo,\r\n      BASE_STEPS.contactInfo,\r\n      BASE_STEPS.management,\r\n      BASE_STEPS.serviceScope,\r\n      BASE_STEPS.legalHistory,\r\n      BASE_STEPS.documents,\r\n      BASE_STEPS.submit\r\n    ],\r\n    estimatedTotalTime: '82 minutes',\r\n    requirements: [\r\n      'Accreditation certificates',\r\n      'Technical competency proof',\r\n      'Quality management system',\r\n      'Laboratory facilities documentation',\r\n      'Staff qualifications'\r\n    ]\r\n  },\r\n\r\n  broadcasting: {\r\n    licenseTypeId: 'broadcasting',\r\n    name: 'Broadcasting License',\r\n    description: 'License for radio and television broadcasting services',\r\n    steps: [\r\n      BASE_STEPS.applicantInfo,\r\n      BASE_STEPS.addressInfo,\r\n      BASE_STEPS.contactInfo,\r\n      BASE_STEPS.management,\r\n      BASE_STEPS.serviceScope,\r\n      BASE_STEPS.legalHistory,\r\n      BASE_STEPS.documents,\r\n      BASE_STEPS.submit\r\n    ],\r\n    estimatedTotalTime: '86 minutes',\r\n    requirements: [\r\n      'Broadcasting equipment specifications',\r\n      'Content programming plan',\r\n      'Studio facility documentation',\r\n      'Transmission coverage maps',\r\n      'Local content compliance plan'\r\n    ]\r\n  },\r\n\r\n  spectrum_management: {\r\n    licenseTypeId: 'spectrum_management',\r\n    name: 'Spectrum Management License',\r\n    description: 'License for radio frequency spectrum management and allocation',\r\n    steps: [\r\n      BASE_STEPS.applicantInfo,\r\n      BASE_STEPS.management,\r\n      BASE_STEPS.serviceScope,\r\n      BASE_STEPS.legalHistory,\r\n      BASE_STEPS.documents,\r\n      BASE_STEPS.submit\r\n    ],\r\n    estimatedTotalTime: '89 minutes',\r\n    requirements: [\r\n      'Spectrum usage plan',\r\n      'Technical interference analysis',\r\n      'Equipment type approval',\r\n      'Frequency coordination agreements',\r\n      'Monitoring capabilities documentation'\r\n    ]\r\n  },\r\n\r\n  clf: {\r\n    licenseTypeId: 'clf',\r\n    name: 'CLF License',\r\n    description: 'Consumer Lending and Finance license',\r\n    steps: [\r\n      BASE_STEPS.applicantInfo,\r\n      BASE_STEPS.addressInfo,\r\n      BASE_STEPS.contactInfo,\r\n      BASE_STEPS.management,\r\n      BASE_STEPS.legalHistory,\r\n      BASE_STEPS.documents,\r\n      BASE_STEPS.submit\r\n    ],\r\n    estimatedTotalTime: '51 minutes',\r\n    requirements: [\r\n      'Financial institution license',\r\n      'Capital adequacy documentation',\r\n      'Risk management framework',\r\n      'Consumer protection policies',\r\n      'Anti-money laundering procedures'\r\n    ]\r\n  }\r\n};\r\n\r\n// License type name to config key mapping\r\nconst LICENSE_TYPE_NAME_MAPPING: Record<string, string> = {\r\n  'telecommunications': 'telecommunications',\r\n  'postal services': 'postal_services',\r\n  'postal_services': 'postal_services',\r\n  'standards compliance': 'standards_compliance',\r\n  'standards_compliance': 'standards_compliance',\r\n  'broadcasting': 'broadcasting',\r\n  'spectrum management': 'spectrum_management',\r\n  'spectrum_management': 'spectrum_management',\r\n  'clf': 'clf',\r\n  'consumer lending and finance': 'clf'\r\n};\r\n\r\n// Default fallback configuration for unknown license types\r\nconst DEFAULT_FALLBACK_CONFIG: LicenseTypeStepConfig = {\r\n  licenseTypeId: 'default',\r\n  name: 'Standard License Application',\r\n  description: 'Standard license application process with all required steps',\r\n  steps: [\r\n    BASE_STEPS.applicantInfo,\r\n    BASE_STEPS.addressInfo,\r\n    BASE_STEPS.contactInfo,\r\n    BASE_STEPS.management,\r\n    BASE_STEPS.professionalServices,\r\n    BASE_STEPS.serviceScope,\r\n    BASE_STEPS.legalHistory,\r\n    BASE_STEPS.documents,\r\n    BASE_STEPS.submit\r\n  ],\r\n  estimatedTotalTime: '120 minutes',\r\n  requirements: [\r\n    'Business registration certificate',\r\n    'Tax compliance certificate',\r\n    'Financial statements',\r\n    'Management CVs',\r\n    'Professional qualifications',\r\n    'Service documentation'\r\n  ]\r\n};\r\n\r\n// Helper functions\r\nexport const getLicenseTypeStepConfig = (licenseTypeId: string): LicenseTypeStepConfig => {\r\n  console.log('🔍 Getting step config for license type:', licenseTypeId);\r\n\r\n  // Check if licenseTypeId is valid\r\n  if (!licenseTypeId || typeof licenseTypeId !== 'string') {\r\n    console.log('⚠️ Invalid licenseTypeId provided, using default config');\r\n    return DEFAULT_FALLBACK_CONFIG;\r\n  }\r\n\r\n  // First try direct lookup with exact match\r\n  let config = LICENSE_TYPE_STEP_CONFIGS[licenseTypeId];\r\n  if (config) {\r\n    console.log('✅ Found config via direct lookup:', config.name);\r\n    return config;\r\n  }\r\n\r\n  // Try normalized lookup (lowercase with underscores)\r\n  const normalizedId = licenseTypeId.toLowerCase().replace(/[^a-z0-9]/g, '_');\r\n  config = LICENSE_TYPE_STEP_CONFIGS[normalizedId];\r\n  if (config) {\r\n    console.log('✅ Found config via normalized lookup:', config.name);\r\n    return config;\r\n  }\r\n\r\n  // Try name mapping for common variations\r\n  const mappedKey = LICENSE_TYPE_NAME_MAPPING[normalizedId];\r\n  if (mappedKey) {\r\n    console.log('✅ Found config via name mapping:', mappedKey);\r\n    return LICENSE_TYPE_STEP_CONFIGS[mappedKey];\r\n  }\r\n\r\n  // If licenseTypeId looks like a UUID, try to get the code from license types\r\n  if (isUUID(licenseTypeId)) {\r\n    console.log('🔍 Detected UUID, trying to get code...');\r\n    const code = getLicenseTypeCodeFromUUID(licenseTypeId);\r\n    console.log('📋 Got code from UUID:', code);\r\n    if (code) {\r\n      const foundConfig = LICENSE_TYPE_STEP_CONFIGS[code];\r\n      if (foundConfig) {\r\n        console.log('✅ Found config via UUID mapping:', foundConfig.name);\r\n        return foundConfig;\r\n      }\r\n    }\r\n  }\r\n\r\n  // Try partial matching for known license type codes\r\n  const knownCodes = Object.keys(LICENSE_TYPE_STEP_CONFIGS);\r\n  const partialMatch = knownCodes.find(code =>\r\n    licenseTypeId.toLowerCase().includes(code) ||\r\n    code.includes(licenseTypeId.toLowerCase())\r\n  );\r\n\r\n  if (partialMatch) {\r\n    console.log('✅ Found config via partial matching:', partialMatch);\r\n    return LICENSE_TYPE_STEP_CONFIGS[partialMatch];\r\n  }\r\n\r\n  console.log('⚠️ No specific config found for license type:', licenseTypeId, '- using default fallback');\r\n  return DEFAULT_FALLBACK_CONFIG;\r\n};\r\n\r\n// Helper function to check if a string is a UUID\r\nconst isUUID = (str: string): boolean => {\r\n  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\r\n  return uuidRegex.test(str);\r\n};\r\n\r\n// Helper function to get license type code from UUID\r\n// This will be populated by the license type service\r\nlet licenseTypeUUIDToCodeMap: Record<string, string> = {};\r\n\r\nexport const setLicenseTypeUUIDToCodeMap = (map: Record<string, string>) => {\r\n  licenseTypeUUIDToCodeMap = map;\r\n};\r\n\r\nconst getLicenseTypeCodeFromUUID = (uuid: string): string | null => {\r\n  return licenseTypeUUIDToCodeMap[uuid] || null;\r\n};\r\n\r\n// Optimized function to get steps by license type code\r\nexport const getStepsByLicenseTypeCode = (licenseTypeCode: string): StepConfig[] => {\r\n  console.log('🔍 Getting steps for license type code:', licenseTypeCode);\r\n\r\n  // Validate known license type codes\r\n  const validCodes = ['telecommunications', 'postal_services', 'standards_compliance', 'broadcasting', 'spectrum_management'];\r\n\r\n  if (validCodes.includes(licenseTypeCode)) {\r\n    const config = LICENSE_TYPE_STEP_CONFIGS[licenseTypeCode];\r\n    if (config) {\r\n      console.log('✅ Found steps for license type code:', licenseTypeCode, '- Steps:', config.steps.length);\r\n      return config.steps;\r\n    }\r\n  }\r\n\r\n  console.log('⚠️ License type code not found or invalid, returning default steps');\r\n  return DEFAULT_FALLBACK_CONFIG.steps;\r\n};\r\n\r\n// Enhanced function to check if a license type code is supported\r\nexport const isLicenseTypeCodeSupported = (licenseTypeCode: string): boolean => {\r\n  const validCodes = ['telecommunications', 'postal_services', 'standards_compliance', 'broadcasting', 'spectrum_management'];\r\n  return validCodes.includes(licenseTypeCode);\r\n};\r\n\r\n// Function to get all supported license type codes\r\nexport const getSupportedLicenseTypeCodes = (): string[] => {\r\n  return ['telecommunications', 'postal_services', 'standards_compliance', 'broadcasting', 'spectrum_management'];\r\n};\r\n\r\nexport const getStepByRoute = (licenseTypeId: string, stepRoute: string): StepConfig | null => {\r\n  const config = getLicenseTypeStepConfig(licenseTypeId);\r\n  if (!config) return null;\r\n\r\n  return config.steps.find(step => step.route === stepRoute) || null;\r\n};\r\n\r\nexport const getStepByIndex = (licenseTypeId: string, stepIndex: number): StepConfig | null => {\r\n  const config = getLicenseTypeStepConfig(licenseTypeId);\r\n  if (stepIndex < 0 || stepIndex >= config.steps.length) return null;\r\n\r\n  return config.steps[stepIndex];\r\n};\r\n\r\nexport const getStepIndex = (licenseTypeId: string, stepRoute: string): number => {\r\n  const config = getLicenseTypeStepConfig(licenseTypeId);\r\n  return config.steps.findIndex(step => step.route === stepRoute);\r\n};\r\n\r\nexport const getTotalSteps = (licenseTypeId: string): number => {\r\n  const config = getLicenseTypeStepConfig(licenseTypeId);\r\n  return config.steps.length;\r\n};\r\n\r\nexport const getRequiredSteps = (licenseTypeId: string): StepConfig[] => {\r\n  const config = getLicenseTypeStepConfig(licenseTypeId);\r\n  return config.steps.filter(step => step.required);\r\n};\r\n\r\nexport const getOptionalSteps = (licenseTypeId: string): StepConfig[] => {\r\n  const config = getLicenseTypeStepConfig(licenseTypeId);\r\n  return config.steps.filter(step => !step.required);\r\n};\r\n\r\nexport const calculateProgress = (licenseTypeId: string, completedSteps: string[]): number => {\r\n  const config = getLicenseTypeStepConfig(licenseTypeId);\r\n  const totalSteps = config.steps.length;\r\n  const completed = completedSteps.length;\r\n\r\n  return Math.round((completed / totalSteps) * 100);\r\n};\r\n\r\nexport const getNextStep = (licenseTypeId: string, currentStepRoute: string): StepConfig | null => {\r\n  const config = getLicenseTypeStepConfig(licenseTypeId);\r\n  const currentIndex = getStepIndex(licenseTypeId, currentStepRoute);\r\n\r\n  if (currentIndex === -1 || currentIndex >= config.steps.length - 1) return null;\r\n\r\n  return config.steps[currentIndex + 1];\r\n};\r\n\r\nexport const getPreviousStep = (licenseTypeId: string, currentStepRoute: string): StepConfig | null => {\r\n  const config = getLicenseTypeStepConfig(licenseTypeId);\r\n  const currentIndex = getStepIndex(licenseTypeId, currentStepRoute);\r\n\r\n  if (currentIndex <= 0) return null;\r\n\r\n  return config.steps[currentIndex - 1];\r\n};\r\n\r\n// Enhanced function to get step configuration with license type code validation\r\nexport const getOptimizedStepConfig = (licenseTypeCode: string): LicenseTypeStepConfig => {\r\n  console.log('🚀 Getting optimized step config for:', licenseTypeCode);\r\n\r\n  // Check if it's a supported license type code\r\n  if (isLicenseTypeCodeSupported(licenseTypeCode)) {\r\n    const config = LICENSE_TYPE_STEP_CONFIGS[licenseTypeCode];\r\n    console.log('✅ Using specific config for:', licenseTypeCode);\r\n    return config;\r\n  }\r\n\r\n  console.log('⚠️ Using default fallback config for unsupported type:', licenseTypeCode);\r\n  return DEFAULT_FALLBACK_CONFIG;\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;CAuBC;;;;;;;;;;;;;;;;;;AAqBD,mDAAmD;AACnD,MAAM,aAAyC;IAC7C,eAAe;QACb,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,aAAa;QACX,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,aAAa;QACX,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IAEA,YAAY;QACV,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,sBAAsB;QACpB,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,cAAc;QACZ,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IAEA,cAAc;QACZ,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,WAAW;QACT,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,QAAQ;QACN,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;AACF;AAGO,MAAM,4BAAmE;IAC9E,oBAAoB;QAClB,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,WAAW;YACtB,WAAW,WAAW;YACtB,WAAW,UAAU;YACrB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,SAAS;YACpB,WAAW,MAAM;SAClB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,iBAAiB;QACf,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,WAAW;YACtB,WAAW,WAAW;YACtB,WAAW,YAAY;YACvB,WAAW,SAAS;YACpB,WAAW,MAAM;SAClB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,sBAAsB;QACpB,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,WAAW;YACtB,WAAW,WAAW;YACtB,WAAW,UAAU;YACrB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,SAAS;YACpB,WAAW,MAAM;SAClB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,cAAc;QACZ,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,WAAW;YACtB,WAAW,WAAW;YACtB,WAAW,UAAU;YACrB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,SAAS;YACpB,WAAW,MAAM;SAClB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,qBAAqB;QACnB,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,UAAU;YACrB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,SAAS;YACpB,WAAW,MAAM;SAClB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,KAAK;QACH,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,WAAW;YACtB,WAAW,WAAW;YACtB,WAAW,UAAU;YACrB,WAAW,YAAY;YACvB,WAAW,SAAS;YACpB,WAAW,MAAM;SAClB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;AACF;AAEA,0CAA0C;AAC1C,MAAM,4BAAoD;IACxD,sBAAsB;IACtB,mBAAmB;IACnB,mBAAmB;IACnB,wBAAwB;IACxB,wBAAwB;IACxB,gBAAgB;IAChB,uBAAuB;IACvB,uBAAuB;IACvB,OAAO;IACP,gCAAgC;AAClC;AAEA,2DAA2D;AAC3D,MAAM,0BAAiD;IACrD,eAAe;IACf,MAAM;IACN,aAAa;IACb,OAAO;QACL,WAAW,aAAa;QACxB,WAAW,WAAW;QACtB,WAAW,WAAW;QACtB,WAAW,UAAU;QACrB,WAAW,oBAAoB;QAC/B,WAAW,YAAY;QACvB,WAAW,YAAY;QACvB,WAAW,SAAS;QACpB,WAAW,MAAM;KAClB;IACD,oBAAoB;IACpB,cAAc;QACZ;QACA;QACA;QACA;QACA;QACA;KACD;AACH;AAGO,MAAM,2BAA2B,CAAC;IACvC,QAAQ,GAAG,CAAC,4CAA4C;IAExD,kCAAkC;IAClC,IAAI,CAAC,iBAAiB,OAAO,kBAAkB,UAAU;QACvD,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT;IAEA,2CAA2C;IAC3C,IAAI,SAAS,yBAAyB,CAAC,cAAc;IACrD,IAAI,QAAQ;QACV,QAAQ,GAAG,CAAC,qCAAqC,OAAO,IAAI;QAC5D,OAAO;IACT;IAEA,qDAAqD;IACrD,MAAM,eAAe,cAAc,WAAW,GAAG,OAAO,CAAC,cAAc;IACvE,SAAS,yBAAyB,CAAC,aAAa;IAChD,IAAI,QAAQ;QACV,QAAQ,GAAG,CAAC,yCAAyC,OAAO,IAAI;QAChE,OAAO;IACT;IAEA,yCAAyC;IACzC,MAAM,YAAY,yBAAyB,CAAC,aAAa;IACzD,IAAI,WAAW;QACb,QAAQ,GAAG,CAAC,oCAAoC;QAChD,OAAO,yBAAyB,CAAC,UAAU;IAC7C;IAEA,6EAA6E;IAC7E,IAAI,OAAO,gBAAgB;QACzB,QAAQ,GAAG,CAAC;QACZ,MAAM,OAAO,2BAA2B;QACxC,QAAQ,GAAG,CAAC,0BAA0B;QACtC,IAAI,MAAM;YACR,MAAM,cAAc,yBAAyB,CAAC,KAAK;YACnD,IAAI,aAAa;gBACf,QAAQ,GAAG,CAAC,oCAAoC,YAAY,IAAI;gBAChE,OAAO;YACT;QACF;IACF;IAEA,oDAAoD;IACpD,MAAM,aAAa,OAAO,IAAI,CAAC;IAC/B,MAAM,eAAe,WAAW,IAAI,CAAC,CAAA,OACnC,cAAc,WAAW,GAAG,QAAQ,CAAC,SACrC,KAAK,QAAQ,CAAC,cAAc,WAAW;IAGzC,IAAI,cAAc;QAChB,QAAQ,GAAG,CAAC,wCAAwC;QACpD,OAAO,yBAAyB,CAAC,aAAa;IAChD;IAEA,QAAQ,GAAG,CAAC,iDAAiD,eAAe;IAC5E,OAAO;AACT;AAEA,iDAAiD;AACjD,MAAM,SAAS,CAAC;IACd,MAAM,YAAY;IAClB,OAAO,UAAU,IAAI,CAAC;AACxB;AAEA,qDAAqD;AACrD,qDAAqD;AACrD,IAAI,2BAAmD,CAAC;AAEjD,MAAM,8BAA8B,CAAC;IAC1C,2BAA2B;AAC7B;AAEA,MAAM,6BAA6B,CAAC;IAClC,OAAO,wBAAwB,CAAC,KAAK,IAAI;AAC3C;AAGO,MAAM,4BAA4B,CAAC;IACxC,QAAQ,GAAG,CAAC,2CAA2C;IAEvD,oCAAoC;IACpC,MAAM,aAAa;QAAC;QAAsB;QAAmB;QAAwB;QAAgB;KAAsB;IAE3H,IAAI,WAAW,QAAQ,CAAC,kBAAkB;QACxC,MAAM,SAAS,yBAAyB,CAAC,gBAAgB;QACzD,IAAI,QAAQ;YACV,QAAQ,GAAG,CAAC,wCAAwC,iBAAiB,YAAY,OAAO,KAAK,CAAC,MAAM;YACpG,OAAO,OAAO,KAAK;QACrB;IACF;IAEA,QAAQ,GAAG,CAAC;IACZ,OAAO,wBAAwB,KAAK;AACtC;AAGO,MAAM,6BAA6B,CAAC;IACzC,MAAM,aAAa;QAAC;QAAsB;QAAmB;QAAwB;QAAgB;KAAsB;IAC3H,OAAO,WAAW,QAAQ,CAAC;AAC7B;AAGO,MAAM,+BAA+B;IAC1C,OAAO;QAAC;QAAsB;QAAmB;QAAwB;QAAgB;KAAsB;AACjH;AAEO,MAAM,iBAAiB,CAAC,eAAuB;IACpD,MAAM,SAAS,yBAAyB;IACxC,IAAI,CAAC,QAAQ,OAAO;IAEpB,OAAO,OAAO,KAAK,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK,cAAc;AAChE;AAEO,MAAM,iBAAiB,CAAC,eAAuB;IACpD,MAAM,SAAS,yBAAyB;IACxC,IAAI,YAAY,KAAK,aAAa,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO;IAE9D,OAAO,OAAO,KAAK,CAAC,UAAU;AAChC;AAEO,MAAM,eAAe,CAAC,eAAuB;IAClD,MAAM,SAAS,yBAAyB;IACxC,OAAO,OAAO,KAAK,CAAC,SAAS,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK;AACvD;AAEO,MAAM,gBAAgB,CAAC;IAC5B,MAAM,SAAS,yBAAyB;IACxC,OAAO,OAAO,KAAK,CAAC,MAAM;AAC5B;AAEO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,SAAS,yBAAyB;IACxC,OAAO,OAAO,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ;AAClD;AAEO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,SAAS,yBAAyB;IACxC,OAAO,OAAO,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,QAAQ;AACnD;AAEO,MAAM,oBAAoB,CAAC,eAAuB;IACvD,MAAM,SAAS,yBAAyB;IACxC,MAAM,aAAa,OAAO,KAAK,CAAC,MAAM;IACtC,MAAM,YAAY,eAAe,MAAM;IAEvC,OAAO,KAAK,KAAK,CAAC,AAAC,YAAY,aAAc;AAC/C;AAEO,MAAM,cAAc,CAAC,eAAuB;IACjD,MAAM,SAAS,yBAAyB;IACxC,MAAM,eAAe,aAAa,eAAe;IAEjD,IAAI,iBAAiB,CAAC,KAAK,gBAAgB,OAAO,KAAK,CAAC,MAAM,GAAG,GAAG,OAAO;IAE3E,OAAO,OAAO,KAAK,CAAC,eAAe,EAAE;AACvC;AAEO,MAAM,kBAAkB,CAAC,eAAuB;IACrD,MAAM,SAAS,yBAAyB;IACxC,MAAM,eAAe,aAAa,eAAe;IAEjD,IAAI,gBAAgB,GAAG,OAAO;IAE9B,OAAO,OAAO,KAAK,CAAC,eAAe,EAAE;AACvC;AAGO,MAAM,yBAAyB,CAAC;IACrC,QAAQ,GAAG,CAAC,yCAAyC;IAErD,8CAA8C;IAC9C,IAAI,2BAA2B,kBAAkB;QAC/C,MAAM,SAAS,yBAAyB,CAAC,gBAAgB;QACzD,QAAQ,GAAG,CAAC,gCAAgC;QAC5C,OAAO;IACT;IAEA,QAAQ,GAAG,CAAC,0DAA0D;IACtE,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2740, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/applications/ApplicationProgress.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\r\nimport { useRouter, useSearchParams, usePathname } from 'next/navigation';\r\nimport {\r\n  getLicenseTypeStepConfig,\r\n  getOptimizedStepConfig,\r\n  getStepsByLicenseTypeCode,\r\n  isLicenseTypeCodeSupported,\r\n  StepConfig\r\n} from '@/config/licenseTypeStepConfig';\r\nimport { CustomerApiService } from '@/lib/customer-api';\r\n\r\n// Cache for license data to avoid repeated API calls\r\nconst licenseDataCache = new Map<string, {\r\n  category: any;\r\n  licenseType: any;\r\n  steps: StepConfig[];\r\n  timestamp: number;\r\n}>();\r\n\r\nconst CACHE_DURATION = 5 * 60 * 1000; // 5 minutes\r\n\r\ninterface ApplicationProgressProps {\r\n  className?: string;\r\n}\r\n\r\nconst ApplicationProgress: React.FC<ApplicationProgressProps> = ({ className = '' }) => {\r\n  const router = useRouter();\r\n  const searchParams = useSearchParams();\r\n  const pathname = usePathname();\r\n\r\n  // Create customer API service instance\r\n  const customerApi = useMemo(() => new CustomerApiService(), []);\r\n\r\n  // Get query parameters\r\n  const licenseCategoryId = searchParams.get('license_category_id');\r\n  const applicationId = searchParams.get('application_id');\r\n\r\n  // State\r\n  const [applicationSteps, setApplicationSteps] = useState<StepConfig[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  // Get current step from pathname (memoized)\r\n  const currentStepIndex = useMemo(() => {\r\n    if (!applicationSteps.length) return -1;\r\n    const pathSegments = pathname.split('/');\r\n    const currentStepId = pathSegments[pathSegments.length - 1];\r\n    return applicationSteps.findIndex(step => step.id === currentStepId);\r\n  }, [pathname, applicationSteps]);\r\n\r\n  // Check cache for license data\r\n  const getCachedLicenseData = useCallback((licenseCategoryId: string) => {\r\n    const cached = licenseDataCache.get(licenseCategoryId);\r\n    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {\r\n      return cached;\r\n    }\r\n    return null;\r\n  }, []);\r\n\r\n  // Cache license data\r\n  const cacheLicenseData = useCallback((licenseCategoryId: string, data: any) => {\r\n    licenseDataCache.set(licenseCategoryId, {\r\n      ...data,\r\n      timestamp: Date.now()\r\n    });\r\n  }, []);\r\n\r\n  // Load data with caching and optimization\r\n  useEffect(() => {\r\n    const loadData = async () => {\r\n      try {\r\n        if (!licenseCategoryId) {\r\n          setLoading(false);\r\n          return;\r\n        }\r\n\r\n        setError(null);\r\n\r\n        // Check cache first\r\n        const cachedData = getCachedLicenseData(licenseCategoryId);\r\n        if (cachedData) {\r\n          console.log('Using cached license data for:', licenseCategoryId);\r\n          setApplicationSteps(cachedData.steps);\r\n          setLoading(false);\r\n          return;\r\n        }\r\n\r\n        console.log('Fetching license data for:', licenseCategoryId);\r\n\r\n        // Load license category\r\n        const category = await customerApi.getLicenseCategory(licenseCategoryId);\r\n        if (!category?.license_type_id) {\r\n          throw new Error('License category does not have a license type ID');\r\n        }\r\n\r\n        const licenseType = await customerApi.getLicenseType(category.license_type_id);\r\n        if (!licenseType) {\r\n          throw new Error('License type not found');\r\n        }\r\n\r\n        console.log('🔍 License type loaded:', {\r\n          id: licenseType.license_type_id,\r\n          name: licenseType.name,\r\n          code: licenseType.code\r\n        });\r\n\r\n        // Use optimized step configuration\r\n        let steps: StepConfig[] = [];\r\n\r\n        if (licenseType.code && isLicenseTypeCodeSupported(licenseType.code)) {\r\n          console.log('✅ Using optimized config for supported license type:', licenseType.code);\r\n          steps = getStepsByLicenseTypeCode(licenseType.code);\r\n        } else {\r\n          console.log('⚠️ Using fallback config for license type:', licenseType.code || 'unknown');\r\n          const config = getLicenseTypeStepConfig(licenseType.code || licenseType.license_type_id);\r\n          steps = config.steps;\r\n        }\r\n\r\n        // Cache the data\r\n        cacheLicenseData(licenseCategoryId, {\r\n          category,\r\n          licenseType,\r\n          steps\r\n        });\r\n\r\n        setApplicationSteps(steps);\r\n        setLoading(false);\r\n\r\n      } catch (err: any) {\r\n        console.error('Error loading application steps:', err);\r\n        setError(err.message || 'Failed to load license information');\r\n        setApplicationSteps([]);\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    loadData();\r\n  }, [licenseCategoryId, applicationId, customerApi, getCachedLicenseData, cacheLicenseData]);\r\n\r\n  // Navigation handlers\r\n  const handleStepClick = (stepIndex: number) => {\r\n    // Prevent navigation to future steps if not editing an existing application\r\n    if (!applicationId && stepIndex > currentStepIndex) {\r\n      return;\r\n    }\r\n\r\n    const step = applicationSteps[stepIndex];\r\n    const params = new URLSearchParams();\r\n    params.set('license_category_id', licenseCategoryId!);\r\n    if (applicationId) {\r\n      params.set('application_id', applicationId);\r\n    }\r\n    router.push(`/customer/applications/apply/${step.id}?${params.toString()}`);\r\n  };\r\n\r\n  // Loading state\r\n  if (loading) {\r\n    return (\r\n      <div className={`mb-8 ${className}`}>\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4\">\r\n          <div className=\"animate-pulse\">\r\n            <div className=\"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4\"></div>\r\n            <div className=\"space-y-2\">\r\n              {[...Array(4)].map((_, i) => (\r\n                <div key={i} className=\"flex items-center p-2\">\r\n                  <div className=\"w-6 h-6 bg-gray-200 dark:bg-gray-700 rounded-full mr-3\"></div>\r\n                  <div className=\"flex-1\">\r\n                    <div className=\"h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3 mb-1\"></div>\r\n                    <div className=\"h-2 bg-gray-200 dark:bg-gray-700 rounded w-1/2\"></div>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Error state\r\n  if (error) {\r\n    return (\r\n      <div className={`mb-8 ${className}`}>\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-red-200 dark:border-red-800 p-4\">\r\n          <div className=\"flex items-center text-red-600 dark:text-red-400\">\r\n            <i className=\"ri-error-warning-line mr-2\"></i>\r\n            <span className=\"text-sm font-medium\">Failed to load progress</span>\r\n          </div>\r\n          <p className=\"text-xs text-red-500 dark:text-red-400 mt-1\">{error}</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // No steps available\r\n  if (!applicationSteps.length) {\r\n    return (\r\n      <div className={`mb-8 ${className}`}>\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4\">\r\n          <div className=\"text-center text-gray-500 dark:text-gray-400\">\r\n            <i className=\"ri-file-list-line text-2xl mb-2\"></i>\r\n            <p className=\"text-sm\">No application steps available</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className={`mb-8 ${className}`}>\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4\">\r\n        <h3 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-4\">\r\n          Application Progress ({currentStepIndex + 1} of {applicationSteps.length})\r\n        </h3>\r\n        <div className=\"space-y-2\">\r\n          {applicationSteps.map((step, index) => {\r\n            const isAccessible = applicationId || index <= currentStepIndex;\r\n            return (\r\n              <div\r\n                key={step.id}\r\n                className={`flex items-center p-2 rounded-md transition-colors ${\r\n                  isAccessible ? 'cursor-pointer' : 'cursor-not-allowed opacity-60'\r\n                } ${\r\n                  index === currentStepIndex\r\n                    ? 'bg-primary/10 border border-primary/20'\r\n                    : index < currentStepIndex\r\n                    ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800'\r\n                    : 'bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600'\r\n                }`}\r\n                onClick={() => isAccessible && handleStepClick(index)}\r\n              >\r\n                <div\r\n                  className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium mr-3 ${\r\n                    index === currentStepIndex\r\n                      ? 'bg-primary text-white'\r\n                      : index < currentStepIndex\r\n                      ? 'bg-green-500 text-white'\r\n                      : 'bg-gray-300 text-gray-600 dark:bg-gray-600 dark:text-gray-300'\r\n                  }`}\r\n                >\r\n                  {index < currentStepIndex ? (\r\n                    <i className=\"ri-check-line\"></i>\r\n                  ) : (\r\n                    index + 1\r\n                  )}\r\n                </div>\r\n                <div className=\"flex-1\">\r\n                  <div className={`text-sm font-medium ${\r\n                    index === currentStepIndex\r\n                      ? 'text-primary'\r\n                      : index < currentStepIndex\r\n                      ? 'text-green-700 dark:text-green-300'\r\n                      : 'text-gray-600 dark:text-gray-400'\r\n                  }`}>\r\n                    {step.name}\r\n                  </div>\r\n                  {step.description && (\r\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                      {step.description}\r\n                    </div>\r\n                  )}\r\n                </div>\r\n                {step.required && (\r\n                  <span className=\"text-xs text-red-500 ml-2\">*</span>\r\n                )}\r\n              </div>\r\n            );\r\n          })}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ApplicationProgress;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAOA;AAXA;;;;;;AAaA,qDAAqD;AACrD,MAAM,mBAAmB,IAAI;AAO7B,MAAM,iBAAiB,IAAI,KAAK,MAAM,YAAY;AAMlD,MAAM,sBAA0D,CAAC,EAAE,YAAY,EAAE,EAAE;IACjF,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,uCAAuC;IACvC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,IAAI,6HAAA,CAAA,qBAAkB,IAAI,EAAE;IAE9D,uBAAuB;IACvB,MAAM,oBAAoB,aAAa,GAAG,CAAC;IAC3C,MAAM,gBAAgB,aAAa,GAAG,CAAC;IAEvC,QAAQ;IACR,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACzE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,4CAA4C;IAC5C,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC/B,IAAI,CAAC,iBAAiB,MAAM,EAAE,OAAO,CAAC;QACtC,MAAM,eAAe,SAAS,KAAK,CAAC;QACpC,MAAM,gBAAgB,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE;QAC3D,OAAO,iBAAiB,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IACxD,GAAG;QAAC;QAAU;KAAiB;IAE/B,+BAA+B;IAC/B,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACxC,MAAM,SAAS,iBAAiB,GAAG,CAAC;QACpC,IAAI,UAAU,KAAK,GAAG,KAAK,OAAO,SAAS,GAAG,gBAAgB;YAC5D,OAAO;QACT;QACA,OAAO;IACT,GAAG,EAAE;IAEL,qBAAqB;IACrB,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,mBAA2B;QAC/D,iBAAiB,GAAG,CAAC,mBAAmB;YACtC,GAAG,IAAI;YACP,WAAW,KAAK,GAAG;QACrB;IACF,GAAG,EAAE;IAEL,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW;YACf,IAAI;gBACF,IAAI,CAAC,mBAAmB;oBACtB,WAAW;oBACX;gBACF;gBAEA,SAAS;gBAET,oBAAoB;gBACpB,MAAM,aAAa,qBAAqB;gBACxC,IAAI,YAAY;oBACd,QAAQ,GAAG,CAAC,kCAAkC;oBAC9C,oBAAoB,WAAW,KAAK;oBACpC,WAAW;oBACX;gBACF;gBAEA,QAAQ,GAAG,CAAC,8BAA8B;gBAE1C,wBAAwB;gBACxB,MAAM,WAAW,MAAM,YAAY,kBAAkB,CAAC;gBACtD,IAAI,CAAC,UAAU,iBAAiB;oBAC9B,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,cAAc,MAAM,YAAY,cAAc,CAAC,SAAS,eAAe;gBAC7E,IAAI,CAAC,aAAa;oBAChB,MAAM,IAAI,MAAM;gBAClB;gBAEA,QAAQ,GAAG,CAAC,2BAA2B;oBACrC,IAAI,YAAY,eAAe;oBAC/B,MAAM,YAAY,IAAI;oBACtB,MAAM,YAAY,IAAI;gBACxB;gBAEA,mCAAmC;gBACnC,IAAI,QAAsB,EAAE;gBAE5B,IAAI,YAAY,IAAI,IAAI,CAAA,GAAA,sIAAA,CAAA,6BAA0B,AAAD,EAAE,YAAY,IAAI,GAAG;oBACpE,QAAQ,GAAG,CAAC,wDAAwD,YAAY,IAAI;oBACpF,QAAQ,CAAA,GAAA,sIAAA,CAAA,4BAAyB,AAAD,EAAE,YAAY,IAAI;gBACpD,OAAO;oBACL,QAAQ,GAAG,CAAC,8CAA8C,YAAY,IAAI,IAAI;oBAC9E,MAAM,SAAS,CAAA,GAAA,sIAAA,CAAA,2BAAwB,AAAD,EAAE,YAAY,IAAI,IAAI,YAAY,eAAe;oBACvF,QAAQ,OAAO,KAAK;gBACtB;gBAEA,iBAAiB;gBACjB,iBAAiB,mBAAmB;oBAClC;oBACA;oBACA;gBACF;gBAEA,oBAAoB;gBACpB,WAAW;YAEb,EAAE,OAAO,KAAU;gBACjB,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,SAAS,IAAI,OAAO,IAAI;gBACxB,oBAAoB,EAAE;gBACtB,WAAW;YACb;QACF;QAEA;IACF,GAAG;QAAC;QAAmB;QAAe;QAAa;QAAsB;KAAiB;IAE1F,sBAAsB;IACtB,MAAM,kBAAkB,CAAC;QACvB,4EAA4E;QAC5E,IAAI,CAAC,iBAAiB,YAAY,kBAAkB;YAClD;QACF;QAEA,MAAM,OAAO,gBAAgB,CAAC,UAAU;QACxC,MAAM,SAAS,IAAI;QACnB,OAAO,GAAG,CAAC,uBAAuB;QAClC,IAAI,eAAe;YACjB,OAAO,GAAG,CAAC,kBAAkB;QAC/B;QACA,OAAO,IAAI,CAAC,CAAC,6BAA6B,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI;IAC5E;IAEA,gBAAgB;IAChB,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAW,CAAC,KAAK,EAAE,WAAW;sBACjC,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;sCACZ;mCAAI,MAAM;6BAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;oCAAY,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;mCAJT;;;;;;;;;;;;;;;;;;;;;;;;;;IAaxB;IAEA,cAAc;IACd,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAW,CAAC,KAAK,EAAE,WAAW;sBACjC,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAsB;;;;;;;;;;;;kCAExC,8OAAC;wBAAE,WAAU;kCAA+C;;;;;;;;;;;;;;;;;IAIpE;IAEA,qBAAqB;IACrB,IAAI,CAAC,iBAAiB,MAAM,EAAE;QAC5B,qBACE,8OAAC;YAAI,WAAW,CAAC,KAAK,EAAE,WAAW;sBACjC,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;;;;;sCACb,8OAAC;4BAAE,WAAU;sCAAU;;;;;;;;;;;;;;;;;;;;;;IAKjC;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,KAAK,EAAE,WAAW;kBACjC,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;;wBAA4D;wBACjD,mBAAmB;wBAAE;wBAAK,iBAAiB,MAAM;wBAAC;;;;;;;8BAE3E,8OAAC;oBAAI,WAAU;8BACZ,iBAAiB,GAAG,CAAC,CAAC,MAAM;wBAC3B,MAAM,eAAe,iBAAiB,SAAS;wBAC/C,qBACE,8OAAC;4BAEC,WAAW,CAAC,mDAAmD,EAC7D,eAAe,mBAAmB,gCACnC,CAAC,EACA,UAAU,mBACN,2CACA,QAAQ,mBACR,mFACA,8EACJ;4BACF,SAAS,IAAM,gBAAgB,gBAAgB;;8CAE/C,8OAAC;oCACC,WAAW,CAAC,+EAA+E,EACzF,UAAU,mBACN,0BACA,QAAQ,mBACR,4BACA,iEACJ;8CAED,QAAQ,iCACP,8OAAC;wCAAE,WAAU;;;;;+CAEb,QAAQ;;;;;;8CAGZ,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAW,CAAC,oBAAoB,EACnC,UAAU,mBACN,iBACA,QAAQ,mBACR,uCACA,oCACJ;sDACC,KAAK,IAAI;;;;;;wCAEX,KAAK,WAAW,kBACf,8OAAC;4CAAI,WAAU;sDACZ,KAAK,WAAW;;;;;;;;;;;;gCAItB,KAAK,QAAQ,kBACZ,8OAAC;oCAAK,WAAU;8CAA4B;;;;;;;2BA5CzC,KAAK,EAAE;;;;;oBAgDlB;;;;;;;;;;;;;;;;;AAKV;uCAEe", "debugId": null}}, {"offset": {"line": 3152, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/applications/ApplicationLayout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { Suspense } from 'react';\r\nimport ApplicationProgress from './ApplicationProgress';\r\n\r\ninterface ApplicationLayoutProps {\r\n  children: React.ReactNode;\r\n  onSubmit?: () => void;\r\n  onSave?: () => void;\r\n  onNext?: () => void;\r\n  onPrevious?: () => void;\r\n  isSubmitting?: boolean;\r\n  isSaving?: boolean;\r\n  showNextButton?: boolean;\r\n  showPreviousButton?: boolean;\r\n  showSaveButton?: boolean;\r\n  showSubmitButton?: boolean;\r\n  nextButtonText?: string;\r\n  previousButtonText?: string;\r\n  saveButtonText?: string;\r\n  submitButtonText?: string;\r\n  nextButtonDisabled?: boolean;\r\n  previousButtonDisabled?: boolean;\r\n  saveButtonDisabled?: boolean;\r\n  submitButtonDisabled?: boolean;\r\n  className?: string;\r\n  showProgress?: boolean; // Allow disabling progress for better performance\r\n  progressFallback?: React.ReactNode; // Custom loading fallback\r\n\r\n  // Enhanced props for optimized step configuration\r\n  licenseTypeCode?: string; // For step validation and navigation\r\n  currentStepRoute?: string; // Current step identifier\r\n  stepValidationErrors?: string[]; // Step-specific validation errors\r\n  showStepInfo?: boolean; // Show step information and requirements\r\n}\r\n\r\n// Progress Loading Fallback Component\r\nconst ProgressLoadingFallback: React.FC = () => (\r\n  <div className=\"mb-8\">\r\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4\">\r\n      <div className=\"animate-pulse\">\r\n        <div className=\"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4\"></div>\r\n        <div className=\"space-y-2\">\r\n          {[...Array(4)].map((_, i) => (\r\n            <div key={i} className=\"flex items-center p-2\">\r\n              <div className=\"w-6 h-6 bg-gray-200 dark:bg-gray-700 rounded-full mr-3\"></div>\r\n              <div className=\"flex-1\">\r\n                <div className=\"h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3 mb-1\"></div>\r\n                <div className=\"h-2 bg-gray-200 dark:bg-gray-700 rounded w-1/2\"></div>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n);\r\n\r\nconst ApplicationLayout: React.FC<ApplicationLayoutProps> = ({\r\n  children,\r\n  onSubmit,\r\n  onSave,\r\n  onNext,\r\n  onPrevious,\r\n  isSubmitting = false,\r\n  isSaving = false,\r\n  showNextButton = true,\r\n  showPreviousButton = true,\r\n  showSaveButton = false,\r\n  showSubmitButton = false,\r\n  nextButtonText = 'Next',\r\n  previousButtonText = 'Previous',\r\n  saveButtonText = 'Save',\r\n  submitButtonText = 'Submit',\r\n  nextButtonDisabled = false,\r\n  previousButtonDisabled = false,\r\n  saveButtonDisabled = false,\r\n  submitButtonDisabled = false,\r\n  className = '',\r\n  showProgress = true,\r\n  progressFallback,\r\n  licenseTypeCode,\r\n  currentStepRoute,\r\n  stepValidationErrors = [],\r\n  showStepInfo = false\r\n}) => {\r\n  return (\r\n    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 ${className}`}>\r\n      <div className=\"max-w-7xl mx-auto\">\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8\">\r\n          {/* Progress Steps - Left Sidebar */}\r\n          {showProgress && (\r\n            <div className=\"lg:col-span-1\">\r\n              <div className=\"sticky top-8\">\r\n                <Suspense fallback={progressFallback || <ProgressLoadingFallback />}>\r\n                  <ApplicationProgress />\r\n                </Suspense>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Main Content Area */}\r\n          <div className={showProgress ? \"lg:col-span-3\" : \"lg:col-span-4\"}>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\">\r\n              {/* Step Information Banner */}\r\n              {showStepInfo && licenseTypeCode && currentStepRoute && (\r\n                <div className=\"border-b border-gray-200 dark:border-gray-700 p-4 bg-blue-50 dark:bg-blue-900/20\">\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <div>\r\n                      <h3 className=\"text-sm font-medium text-blue-900 dark:text-blue-100\">\r\n                        License Type: {licenseTypeCode.replace(/_/g, ' ').toUpperCase()}\r\n                      </h3>\r\n                      <p className=\"text-xs text-blue-700 dark:text-blue-300 mt-1\">\r\n                        Current Step: {currentStepRoute.replace(/-/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase())}\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"text-xs text-blue-600 dark:text-blue-400\">\r\n                      <i className=\"ri-information-line mr-1\"></i>\r\n                      Optimized Configuration Active\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {/* Validation Errors */}\r\n              {stepValidationErrors.length > 0 && (\r\n                <div className=\"border-b border-gray-200 dark:border-gray-700 p-4 bg-red-50 dark:bg-red-900/20\">\r\n                  <div className=\"flex items-start\">\r\n                    <i className=\"ri-error-warning-line text-red-500 mr-2 mt-0.5\"></i>\r\n                    <div>\r\n                      <h4 className=\"text-sm font-medium text-red-900 dark:text-red-100 mb-2\">\r\n                        Please fix the following issues:\r\n                      </h4>\r\n                      <ul className=\"text-xs text-red-700 dark:text-red-300 space-y-1\">\r\n                        {stepValidationErrors.map((error, index) => (\r\n                          <li key={index}>• {error}</li>\r\n                        ))}\r\n                      </ul>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {/* Form Content */}\r\n              <div className=\"p-6\">\r\n                {children}\r\n              </div>\r\n\r\n              {/* Footer with Action Buttons */}\r\n              <div className=\"px-6 py-4 bg-gray-50 dark:bg-gray-700/50 border-t border-gray-200 dark:border-gray-600 rounded-b-lg\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  {/* Left Side - Previous Button */}\r\n                  <div>\r\n                    {showPreviousButton && onPrevious && (\r\n                      <button\r\n                        type=\"button\"\r\n                        onClick={onPrevious}\r\n                        disabled={previousButtonDisabled}\r\n                        className=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                      >\r\n                        <i className=\"ri-arrow-left-line mr-2\"></i>\r\n                        {previousButtonText}\r\n                      </button>\r\n                    )}\r\n                  </div>\r\n\r\n                  {/* Right Side - Action Buttons */}\r\n                  <div className=\"flex items-center space-x-3\">\r\n                    {/* Save Button */}\r\n                    {showSaveButton && onSave && (\r\n                      <button\r\n                        type=\"button\"\r\n                        onClick={() => {\r\n                          console.log('🔘 Save button clicked in ApplicationLayout');\r\n                          onSave();\r\n                        }}\r\n                        disabled={saveButtonDisabled || isSaving}\r\n                        className=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                      >\r\n                        {isSaving ? (\r\n                          <>\r\n                            <i className=\"ri-loader-4-line animate-spin mr-2\"></i>\r\n                            Saving...\r\n                          </>\r\n                        ) : (\r\n                          <>\r\n                            <i className=\"ri-save-line mr-2\"></i>\r\n                            {saveButtonText}\r\n                          </>\r\n                        )}\r\n                      </button>\r\n                    )}\r\n\r\n                    {/* Submit Button */}\r\n                    {showSubmitButton && onSubmit && (\r\n                      <button\r\n                        type=\"button\"\r\n                        onClick={onSubmit}\r\n                        disabled={submitButtonDisabled || isSubmitting}\r\n                        className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                      >\r\n                        {isSubmitting ? (\r\n                          <>\r\n                            <i className=\"ri-loader-4-line animate-spin mr-2\"></i>\r\n                            Submitting...\r\n                          </>\r\n                        ) : (\r\n                          <>\r\n                            <i className=\"ri-send-plane-line mr-2\"></i>\r\n                            {submitButtonText}\r\n                          </>\r\n                        )}\r\n                      </button>\r\n                    )}\r\n\r\n                    {/* Next Button */}\r\n                    {showNextButton && onNext && (\r\n                      <button\r\n                        type=\"button\"\r\n                        onClick={() => {\r\n                          console.log('🔘 Next button clicked in ApplicationLayout');\r\n                          onNext();\r\n                        }}\r\n                        disabled={nextButtonDisabled}\r\n                        className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                      >\r\n                        {nextButtonText}\r\n                        <i className=\"ri-arrow-right-line ml-2\"></i>\r\n                      </button>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ApplicationLayout;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAoCA,sCAAsC;AACtC,MAAM,0BAAoC,kBACxC,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;gCAAY,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;+BAJT;;;;;;;;;;;;;;;;;;;;;;;;;;AActB,MAAM,oBAAsD,CAAC,EAC3D,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,MAAM,EACN,UAAU,EACV,eAAe,KAAK,EACpB,WAAW,KAAK,EAChB,iBAAiB,IAAI,EACrB,qBAAqB,IAAI,EACzB,iBAAiB,KAAK,EACtB,mBAAmB,KAAK,EACxB,iBAAiB,MAAM,EACvB,qBAAqB,UAAU,EAC/B,iBAAiB,MAAM,EACvB,mBAAmB,QAAQ,EAC3B,qBAAqB,KAAK,EAC1B,yBAAyB,KAAK,EAC9B,qBAAqB,KAAK,EAC1B,uBAAuB,KAAK,EAC5B,YAAY,EAAE,EACd,eAAe,IAAI,EACnB,gBAAgB,EAChB,eAAe,EACf,gBAAgB,EAChB,uBAAuB,EAAE,EACzB,eAAe,KAAK,EACrB;IACC,qBACE,8OAAC;QAAI,WAAW,CAAC,yCAAyC,EAAE,WAAW;kBACrE,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;oBAEZ,8BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,qMAAA,CAAA,WAAQ;gCAAC,UAAU,kCAAoB,8OAAC;;;;;0CACvC,cAAA,8OAAC,yJAAA,CAAA,UAAmB;;;;;;;;;;;;;;;;;;;;kCAO5B,8OAAC;wBAAI,WAAW,eAAe,kBAAkB;kCAC/C,cAAA,8OAAC;4BAAI,WAAU;;gCAEZ,gBAAgB,mBAAmB,kCAClC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;;4DAAuD;4DACpD,gBAAgB,OAAO,CAAC,MAAM,KAAK,WAAW;;;;;;;kEAE/D,8OAAC;wDAAE,WAAU;;4DAAgD;4DAC5C,iBAAiB,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;;;;;;;;;;;;;0DAG1F,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;;;;;;oDAA+B;;;;;;;;;;;;;;;;;;gCAQnD,qBAAqB,MAAM,GAAG,mBAC7B,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;;;;;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA0D;;;;;;kEAGxE,8OAAC;wDAAG,WAAU;kEACX,qBAAqB,GAAG,CAAC,CAAC,OAAO,sBAChC,8OAAC;;oEAAe;oEAAG;;+DAAV;;;;;;;;;;;;;;;;;;;;;;;;;;;8CASrB,8OAAC;oCAAI,WAAU;8CACZ;;;;;;8CAIH,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;0DACE,sBAAsB,4BACrB,8OAAC;oDACC,MAAK;oDACL,SAAS;oDACT,UAAU;oDACV,WAAU;;sEAEV,8OAAC;4DAAE,WAAU;;;;;;wDACZ;;;;;;;;;;;;0DAMP,8OAAC;gDAAI,WAAU;;oDAEZ,kBAAkB,wBACjB,8OAAC;wDACC,MAAK;wDACL,SAAS;4DACP,QAAQ,GAAG,CAAC;4DACZ;wDACF;wDACA,UAAU,sBAAsB;wDAChC,WAAU;kEAET,yBACC;;8EACE,8OAAC;oEAAE,WAAU;;;;;;gEAAyC;;yFAIxD;;8EACE,8OAAC;oEAAE,WAAU;;;;;;gEACZ;;;;;;;;oDAOR,oBAAoB,0BACnB,8OAAC;wDACC,MAAK;wDACL,SAAS;wDACT,UAAU,wBAAwB;wDAClC,WAAU;kEAET,6BACC;;8EACE,8OAAC;oEAAE,WAAU;;;;;;gEAAyC;;yFAIxD;;8EACE,8OAAC;oEAAE,WAAU;;;;;;gEACZ;;;;;;;;oDAOR,kBAAkB,wBACjB,8OAAC;wDACC,MAAK;wDACL,SAAS;4DACP,QAAQ,GAAG,CAAC;4DACZ;wDACF;wDACA,UAAU;wDACV,WAAU;;4DAET;0EACD,8OAAC;gEAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrC;uCAEe", "debugId": null}}, {"offset": {"line": 3589, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/applications/index.ts"], "sourcesContent": ["// Application Progress Component\r\nexport { default as ApplicationProgress } from './ApplicationProgress';\r\n\r\n// Application Layout Component\r\nexport { default as ApplicationLayout } from './ApplicationLayout';\r\n\r\n// Types for application form data\r\nexport interface ApplicantInfoData {\r\n  applicantName: string;\r\n  postalPoBox: string;\r\n  postalCity: string;\r\n  postalCountry: string;\r\n  physicalStreet: string;\r\n  physicalCity: string;\r\n  physicalCountry: string;\r\n  telephone: string;\r\n  fax: string;\r\n  email: string;\r\n}\r\n\r\nexport interface ShareholderData {\r\n  name: string;\r\n  nationality: string;\r\n  address: string;\r\n  shareholding: string;\r\n}\r\n\r\nexport interface DirectorData {\r\n  name: string;\r\n  nationality: string;\r\n  address: string;\r\n}\r\n\r\nexport interface CompanyProfileData {\r\n  shareholders: ShareholderData[];\r\n  directors: DirectorData[];\r\n  foreignOwnership: string;\r\n  businessRegistrationNo: string;\r\n  tpin: string;\r\n  website: string;\r\n  dateOfIncorporation: string;\r\n  placeOfIncorporation: string;\r\n}\r\n\r\nexport interface ManagementTeamMember {\r\n  name: string;\r\n  position: string;\r\n  qualifications: string;\r\n  experience: string;\r\n}\r\n\r\nexport interface ManagementData {\r\n  managementTeam: ManagementTeamMember[];\r\n  organizationalStructure: string;\r\n  keyPersonnel: string;\r\n}\r\n\r\nexport interface ProfessionalServicesData {\r\n  consultants: string;\r\n  serviceProviders: string;\r\n  technicalSupport: string;\r\n  maintenanceArrangements: string;\r\n}\r\n\r\nexport interface BusinessInfoData {\r\n  businessDescription: string;\r\n  operationalAreas: string;\r\n  facilities: string;\r\n  equipment: string;\r\n  businessModel: string;\r\n}\r\n\r\nexport interface ServiceScopeData {\r\n  servicesOffered: string;\r\n  targetMarket: string;\r\n  geographicCoverage: string;\r\n  serviceStandards: string;\r\n}\r\n\r\nexport interface BusinessPlanData {\r\n  marketAnalysis: string;\r\n  financialProjections: string;\r\n  competitiveAdvantage: string;\r\n  riskAssessment: string;\r\n  implementationTimeline: string;\r\n}\r\n\r\nexport interface LegalHistoryData {\r\n  previousViolations: string;\r\n  courtCases: string;\r\n  regulatoryHistory: string;\r\n  complianceRecord: string;\r\n}\r\n\r\nexport interface ApplicationFormData {\r\n  applicantInfo: ApplicantInfoData;\r\n  companyProfile: CompanyProfileData;\r\n  management: ManagementData;\r\n  professionalServices: ProfessionalServicesData;\r\n  businessInfo: BusinessInfoData;\r\n  serviceScope: ServiceScopeData;\r\n  businessPlan: BusinessPlanData;\r\n  legalHistory: LegalHistoryData;\r\n}\r\n\r\n// Component props interfaces\r\nexport interface ApplicationFormComponentProps {\r\n  data: any;\r\n  onChange: (data: any) => void;\r\n  errors?: Record<string, string>;\r\n  disabled?: boolean;\r\n}\r\n"], "names": [], "mappings": "AAAA,iCAAiC;;AACjC;AAEA,+BAA+B;AAC/B", "debugId": null}}, {"offset": {"line": 3622, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/app/customer/applications/apply/applicant-info/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { useRouter, useSearchParams } from 'next/navigation';\r\nimport CustomerLayout from '@/components/customer/CustomerLayout';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport TextInput from '@/components/common/TextInput';\r\nimport { applicationService } from '@/services/applicationService';\r\nimport { applicantService } from '@/services/applicantService';\r\nimport { validateSection } from '@/utils/formValidation';\r\nimport { ApplicationStatus } from '@/types/license';\r\n\r\nimport { CustomerApiService } from '@/lib/customer-api';\r\nimport { ApplicationLayout } from '@/components/applications';\r\n\r\nconst ApplicantInfoPage: React.FC = () => {\r\n  const router = useRouter();\r\n  const searchParams = useSearchParams();\r\n  const { isAuthenticated, loading: authLoading } = useAuth();\r\n\r\n  // Create customer API service instance\r\n  const customerApi = new CustomerApiService();\r\n\r\n  // Get query parameters\r\n  const licenseCategoryId = searchParams.get('license_category_id');\r\n  const applicationId = searchParams.get('application_id');\r\n\r\n  // State\r\n\r\n\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  // Form data state - aligned with Applicant interface\r\n  const [formData, setFormData] = useState({\r\n    name: '',\r\n    business_registration_number: '',\r\n    tpin: '',\r\n    website: '',\r\n    email: '',\r\n    phone: '',\r\n    fax: '',\r\n    level_of_insurance_cover: '',\r\n    date_incorporation: '',\r\n    place_incorporation: ''\r\n  });\r\n\r\n  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});\r\n  const [isSaving, setIsSaving] = useState(false);\r\n\r\n  const [applicationCreated, setApplicationCreated] = useState(false);\r\n  const [createdApplicationId, setCreatedApplicationId] = useState<string | null>(null);\r\n  const [loadingWarning, setLoadingWarning] = useState<string | null>(null);\r\n\r\n\r\n\r\n  // Form handling functions\r\n  const handleFormChange = (field: string, value: string) => {\r\n    setFormData(prev => ({ ...prev, [field]: value }));\r\n\r\n    // Clear validation error for this field\r\n    if (validationErrors[field]) {\r\n      setValidationErrors(prev => {\r\n        const newErrors = { ...prev };\r\n        delete newErrors[field];\r\n        return newErrors;\r\n      });\r\n    }\r\n  };\r\n\r\n  // Save function\r\n  const handleSave = async (navigateAfterSave = false) => {\r\n    console.log('💾 Starting save process...', { navigateAfterSave });\r\n    setIsSaving(true);\r\n    setValidationErrors({});\r\n\r\n    try {\r\n      // Validate form data\r\n      console.log('🔍 Validating form data:', formData);\r\n      const validation = validateSection(formData as Record<string, string>, 'applicantInfo');\r\n      if (!validation.isValid) {\r\n        console.error('❌ Validation failed:', validation.errors);\r\n        setValidationErrors(validation.errors || {});\r\n        setIsSaving(false);\r\n        return false;\r\n      }\r\n      console.log('✅ Validation passed');\r\n\r\n      let currentApplicationId = applicationId;\r\n\r\n      // If no application exists, create one\r\n      if (!currentApplicationId) {\r\n        console.log('Creating new application...');\r\n\r\n        // Address handling will be done in the dedicated address page\r\n\r\n        // Create applicant data (addresses will be handled in the address page)\r\n        const applicantData = {\r\n          name: formData.name,\r\n          business_registration_number: formData.business_registration_number,\r\n          tpin: formData.tpin,\r\n          website: formData.website,\r\n          email: formData.email,\r\n          phone: formData.phone,\r\n          // Only include fax if it's provided and has valid length\r\n          ...(formData.fax && formData.fax.trim().length >= 10 ? { fax: formData.fax.trim() } : {}),\r\n          // Only include level_of_insurance_cover if it's provided\r\n          ...(formData.level_of_insurance_cover && formData.level_of_insurance_cover.trim() \r\n            ? { level_of_insurance_cover: formData.level_of_insurance_cover.trim() } : {}),\r\n          date_incorporation: formData.date_incorporation,\r\n          place_incorporation: formData.place_incorporation\r\n        };\r\n\r\n        const applicant = await applicantService.createApplicant(applicantData);\r\n        console.log('Applicant created:', applicant);\r\n\r\n        // Then create application\r\n        const applicationNumber = `APP-${Date.now()}-${Math.random().toString(36).substring(2, 11).toUpperCase()}`;\r\n        const applicationData = {\r\n          application_number: applicationNumber,\r\n          license_category_id: licenseCategoryId!,\r\n          applicant_id: applicant.applicant_id,\r\n          status: ApplicationStatus.DRAFT\r\n        };\r\n\r\n        const application = await applicationService.createApplication(applicationData);\r\n        console.log('✅ Application created:', application);\r\n\r\n        currentApplicationId = application.application_id;\r\n        console.log('📝 Setting currentApplicationId:', currentApplicationId);\r\n        setCreatedApplicationId(currentApplicationId);\r\n        setApplicationCreated(true);\r\n      } else {\r\n        // Update existing applicant data\r\n        console.log('Updating existing application...');\r\n\r\n        // Get application to find applicant_id\r\n        const application = await applicationService.getApplication(currentApplicationId);\r\n        if (application.applicant_id) {\r\n          // Update existing applicant data\r\n\r\n\r\n\r\n\r\n          // Update applicant data (addresses will be handled in the address page)\r\n          const applicantData = {\r\n            name: formData.name,\r\n            business_registration_number: formData.business_registration_number,\r\n            tpin: formData.tpin,\r\n            website: formData.website,\r\n            email: formData.email,\r\n            phone: formData.phone,\r\n            // Only include fax if it's provided and has valid length\r\n            ...(formData.fax && formData.fax.trim().length >= 10 ? { fax: formData.fax.trim() } : {}),\r\n            // Only include level_of_insurance_cover if it's provided\r\n            ...(formData.level_of_insurance_cover && formData.level_of_insurance_cover.trim() \r\n              ? { level_of_insurance_cover: formData.level_of_insurance_cover.trim() } : {}),\r\n            date_incorporation: formData.date_incorporation,\r\n            place_incorporation: formData.place_incorporation,\r\n          };\r\n\r\n          await applicantService.updateApplicant(application.applicant_id, applicantData);\r\n          console.log('Applicant updated successfully');\r\n        }\r\n      }\r\n      // Update application progress\r\n      try {\r\n        const finalApplicationId = currentApplicationId || createdApplicationId;\r\n        if (finalApplicationId) {\r\n          await applicationService.updateApplication(finalApplicationId, {\r\n            current_step: 2,\r\n            progress_percentage: 18 // ~2/11 steps completed\r\n          });\r\n          console.log('Application progress updated');\r\n        }\r\n      } catch (progressError) {\r\n        console.warn('Failed to update application progress:', progressError);\r\n      }\r\n\r\n      // Save form data\r\n\r\n      // Show success message\r\n      console.log('✅ Applicant information saved successfully');\r\n\r\n      // Navigate to next step if requested\r\n      if (navigateAfterSave) {\r\n        console.log('🚀 Navigating to next step...');\r\n        const finalApplicationId = currentApplicationId || createdApplicationId;\r\n        console.log('📋 Navigation IDs - currentApplicationId:', currentApplicationId, 'createdApplicationId:', createdApplicationId, 'finalApplicationId:', finalApplicationId);\r\n        handleNext(finalApplicationId);\r\n      }\r\n\r\n      return true; // Indicate success\r\n\r\n    } catch (error: any) {\r\n      console.error('❌ Error saving applicant information:', error);\r\n      console.error('Error details:', error.response?.data || error.message);\r\n      \r\n      // Handle specific validation errors from backend\r\n      if (error.response?.status === 400 && error.response?.data?.message) {\r\n        const backendMessage = error.response.data.message;\r\n        \r\n        // Map backend validation errors to specific fields\r\n        if (backendMessage.includes('fax')) {\r\n          setValidationErrors({ \r\n            fax: 'Fax number must be at least 10 characters long and contain only numbers, spaces, dashes, and parentheses.',\r\n            save: 'Please fix the validation errors above.' \r\n          });\r\n        } else if (backendMessage.includes('phone')) {\r\n          setValidationErrors({ \r\n            phone: 'Phone number must be between 10-20 characters and contain only numbers, spaces, dashes, and parentheses.',\r\n            save: 'Please fix the validation errors above.' \r\n          });\r\n        } else if (backendMessage.includes('email')) {\r\n          setValidationErrors({ \r\n            email: 'Please enter a valid email address.',\r\n            save: 'Please fix the validation errors above.' \r\n          });\r\n        } else {\r\n          setValidationErrors({ save: backendMessage });\r\n        }\r\n      } else {\r\n        setValidationErrors({ save: 'Failed to save application. Please try again.' });\r\n      }\r\n      return false; // Indicate failure\r\n    } finally {\r\n      setIsSaving(false);\r\n    }\r\n  };\r\n\r\n  // Save and continue function\r\n  const handleSaveAndContinue = async () => {\r\n    console.log('🚀 Save and Continue clicked');\r\n    const success = await handleSave(true);\r\n    if (!success) {\r\n      console.error('❌ Save failed, not navigating');\r\n    }\r\n    // Navigation is handled in handleSave if successful\r\n  };\r\n\r\n  // Authentication check\r\n  useEffect(() => {\r\n    if (!authLoading && !isAuthenticated) {\r\n      router.push('/customer/auth/login');\r\n      return;\r\n    }\r\n  }, [isAuthenticated, authLoading, router]);\r\n\r\n  // Load data\r\n  useEffect(() => {\r\n    const loadData = async () => {\r\n      try {\r\n        if (!licenseCategoryId) {\r\n          setError('License category ID is required');\r\n          setLoading(false);\r\n          return;\r\n        }\r\n        // Load license category\r\n        const category = await customerApi.getLicenseCategory(licenseCategoryId);\r\n        await customerApi.getLicenseType(category.license_type_id);\r\n\r\n        if (!category) {\r\n          setError('License category not found');\r\n          setLoading(false);\r\n          return;\r\n        }\r\n\r\n        // License category and type loaded successfully\r\n\r\n        if (!category.license_type_id) {\r\n          setError('License category is missing license type information');\r\n          setLoading(false);\r\n          return;\r\n        }\r\n\r\n        // Load existing application data if application_id is provided\r\n        if (applicationId) {\r\n          try {\r\n            console.log('🔄 Loading existing application data for ID:', applicationId);\r\n            const application = await applicationService.getApplication(applicationId);\r\n            console.log('📋 Application loaded:', application);\r\n\r\n            if (application.applicant_id) {\r\n              console.log('👤 Loading existing applicant data for ID:', application.applicant_id);\r\n\r\n              try {\r\n                const applicant = await applicantService.getApplicant(application.applicant_id);\r\n                console.log('👤 Applicant data loaded:', applicant);\r\n\r\n                // Auto-populate form with existing applicant data\r\n                const populatedData = {\r\n                  name: applicant.name || '',\r\n                  business_registration_number: applicant.business_registration_number || '',\r\n                  tpin: applicant.tpin || '',\r\n                  website: applicant.website || '',\r\n                  email: applicant.email || '',\r\n                  phone: applicant.phone || '',\r\n                  fax: applicant.fax || '',\r\n                  level_of_insurance_cover: applicant.level_of_insurance_cover || '',\r\n                  date_incorporation: applicant.date_incorporation || '',\r\n                  place_incorporation: applicant.place_incorporation || ''\r\n                };\r\n\r\n                console.log('📝 Populating form with data:', populatedData);\r\n                setFormData(prev => {\r\n                  const newData = { ...prev, ...populatedData };\r\n                  console.log('📝 Form data after population:', newData);\r\n                  return newData;\r\n                });\r\n\r\n                console.log('✅ Form auto-populated successfully');\r\n              } catch (applicantError: any) {\r\n                console.error('❌ Error loading applicant data:', applicantError);\r\n                console.error('Applicant error details:', {\r\n                  applicantId: application.applicant_id,\r\n                  message: applicantError.message,\r\n                  response: applicantError.response?.data,\r\n                  status: applicantError.response?.status\r\n                });\r\n\r\n                // Show a user-friendly message but don't fail the entire page\r\n                console.warn('⚠️ Could not load existing applicant data. You can still edit the application with empty form.');\r\n\r\n                // Set warning message for user\r\n                if (applicantError.response?.status === 500) {\r\n                  setLoadingWarning('Unable to load existing applicant data due to a server issue. You can still edit the application, but the form will start empty.');\r\n                  console.warn('🔧 Backend issue detected - applicant service may be temporarily unavailable');\r\n                } else {\r\n                  setLoadingWarning('Could not load existing applicant data. The form will start empty.');\r\n                }\r\n              }\r\n            } else {\r\n              console.log('⚠️ Application exists but no applicant_id found in application:', application);\r\n            }\r\n          } catch (appError: any) {\r\n            console.error('❌ Error loading existing application data:', appError);\r\n            console.error('Error details:', {\r\n              message: appError.message,\r\n              response: appError.response?.data,\r\n              status: appError.response?.status\r\n            });\r\n            // Don't fail the entire load process if application data can't be loaded\r\n          }\r\n        } else {\r\n          console.log('🆕 No application_id provided - creating new application');\r\n        }\r\n\r\n        console.log('Data loading completed successfully');\r\n        setLoading(false);\r\n      } catch (err: any) {\r\n        console.error('Error loading data:', err);\r\n        console.error('Error details:', {\r\n          message: err.message,\r\n          response: err.response?.data,\r\n          status: err.response?.status\r\n        });\r\n\r\n        // Provide more specific error message\r\n        let errorMessage = 'Failed to load application data';\r\n        if (err.response?.status === 404) {\r\n          errorMessage = `License category not found (ID: ${licenseCategoryId}). Please go back to the applications page and select a valid license category.`;\r\n        } else if (err.response?.status === 401) {\r\n          errorMessage = 'You are not authorized to access this license category. Please log in again.';\r\n        } else if (err.response?.status === 500) {\r\n          errorMessage = 'Server error occurred. Please try again later or contact support.';\r\n        } else if (err.message) {\r\n          errorMessage = `Error: ${err.message}`;\r\n        }\r\n\r\n        setError(errorMessage);\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    if (isAuthenticated && !authLoading) {\r\n      loadData();\r\n    }\r\n  }, [licenseCategoryId, applicationId, isAuthenticated, authLoading]);\r\n\r\n  // Navigation handlers for the ApplicantInfo component\r\n  const handleNext = (overrideApplicationId?: string | null) => {\r\n    console.log('🔄 Navigating to address-info page...');\r\n    const params = new URLSearchParams();\r\n    params.set('license_category_id', licenseCategoryId!);\r\n\r\n    // Use override ID first, then existing IDs\r\n    const finalApplicationId = overrideApplicationId || applicationId || createdApplicationId;\r\n    if (finalApplicationId) {\r\n      params.set('application_id', finalApplicationId);\r\n    } else {\r\n      console.warn('⚠️ No application ID available for navigation!', {\r\n        overrideApplicationId,\r\n        applicationId,\r\n        createdApplicationId\r\n      });\r\n    }\r\n\r\n    const nextUrl = `/customer/applications/apply/address-info?${params.toString()}`;\r\n    console.log('🔗 Next URL:', nextUrl);\r\n    console.log('🔗 Using application_id:', finalApplicationId);\r\n    router.push(nextUrl);\r\n  };\r\n\r\n  const handlePrevious = () => {\r\n    router.push('/customer/applications');\r\n  };\r\n\r\n\r\n  // Loading state\r\n  if (authLoading || loading) {\r\n    return (\r\n      <CustomerLayout>\r\n        <div className=\"flex items-center justify-center min-h-96\">\r\n          <div className=\"text-center\">\r\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4\"></div>\r\n            <p className=\"text-gray-600 dark:text-gray-400\">Loading application form...</p>\r\n          </div>\r\n        </div>\r\n      </CustomerLayout>\r\n    );\r\n  }\r\n\r\n  // Error state\r\n  if (error) {\r\n    return (\r\n      <CustomerLayout>\r\n        <div className=\"flex items-center justify-center min-h-96\">\r\n          <div className=\"text-center\">\r\n            <div className=\"mb-4\">\r\n              <i className=\"ri-error-warning-line text-4xl text-red-500\"></i>\r\n            </div>\r\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\">\r\n              Error Loading Application\r\n            </h3>\r\n            <p className=\"text-gray-600 dark:text-gray-400 mb-6\">\r\n              {error}\r\n            </p>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => router.push('/customer/applications')}\r\n              className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n            >\r\n              <i className=\"ri-arrow-left-line mr-2\"></i>\r\n              Back to Applications\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </CustomerLayout>\r\n    );\r\n  }\r\n\r\n\r\n\r\n  return (\r\n    <CustomerLayout>\r\n      <ApplicationLayout\r\n        onNext={handleSaveAndContinue}\r\n        onPrevious={handlePrevious}\r\n        onSave={async () => {\r\n          console.log('💾 Save button clicked');\r\n          await handleSave(false);\r\n        }}\r\n        showNextButton={true}\r\n        showPreviousButton={true}\r\n        showSaveButton={true}\r\n        nextButtonText=\"Save & Continue to Address Information\"\r\n        previousButtonText=\"Back to Applications\"\r\n        saveButtonText={applicationId ? 'Save Changes' : 'Create Application'}\r\n        nextButtonDisabled={false}\r\n        isSaving={isSaving}\r\n      >\r\n\r\n        {/* Header */}\r\n        <div className=\"mb-6\">\r\n          <h2 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\r\n            {applicationId ? 'Edit Applicant Information' : 'Applicant Information'}\r\n          </h2>\r\n          <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\r\n            {applicationId\r\n              ? 'Update your applicant information below.'\r\n              : 'Please provide your personal information. This will create your application record.'\r\n            }\r\n          </p>\r\n          {applicationId && !loadingWarning && (\r\n            <div className=\"mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg\">\r\n              <p className=\"text-sm text-green-700 dark:text-green-300\">\r\n                ✅ Editing existing application. Your saved information has been loaded.\r\n              </p>\r\n            </div>\r\n          )}\r\n          {loadingWarning && (\r\n            <div className=\"mt-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg\">\r\n              <p className=\"text-sm text-yellow-700 dark:text-yellow-300\">\r\n                ⚠️ {loadingWarning}\r\n              </p>\r\n            </div>\r\n          )}\r\n          {!applicationId && (\r\n            <div className=\"mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\">\r\n              <p className=\"text-sm text-blue-700 dark:text-blue-300\">\r\n                    <i className=\"ri-information-line mr-1\"></i>\r\n                    Your application will be created when you save this step.\r\n                  </p>\r\n                </div>\r\n              )}\r\n              {applicationCreated && (\r\n                <div className=\"mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg\">\r\n                  <p className=\"text-sm text-green-700 dark:text-green-300\">\r\n                    <i className=\"ri-check-line mr-1\"></i>\r\n                    Application created: {createdApplicationId?.slice(0, 8)}...\r\n                  </p>\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n              {/* Business Information */}\r\n              <div className=\"md:col-span-2\">\r\n                <h4 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4\">Business Information</h4>\r\n              </div>\r\n\r\n              <div className=\"md:col-span-2\">\r\n                <TextInput\r\n                  label=\"Business/Organization Name\"\r\n                  value={formData.name || ''}\r\n                  onChange={(e) => handleFormChange('name', e.target.value)}\r\n                  placeholder=\"Enter the full legal name of your business or organization\"\r\n                  required\r\n                  error={validationErrors.name}\r\n                />\r\n              </div>\r\n\r\n              <TextInput\r\n                label=\"Business Registration Number\"\r\n                value={formData.business_registration_number || ''}\r\n                onChange={(e) => handleFormChange('business_registration_number', e.target.value)}\r\n                placeholder=\"e.g., BN123456789\"\r\n                required\r\n                error={validationErrors.business_registration_number}\r\n              />\r\n\r\n              <TextInput\r\n                label=\"TPIN (Tax Payer Identification Number)\"\r\n                value={formData.tpin || ''}\r\n                onChange={(e) => handleFormChange('tpin', e.target.value)}\r\n                placeholder=\"e.g., 12-345-678-9\"\r\n                required\r\n                error={validationErrors.tpin}\r\n              />\r\n\r\n              <TextInput\r\n                label=\"Website\"\r\n                type=\"url\"\r\n                value={formData.website || ''}\r\n                onChange={(e) => handleFormChange('website', e.target.value)}\r\n                placeholder=\"https://www.example.com\"\r\n                error={validationErrors.website}\r\n              />\r\n\r\n              <TextInput\r\n                label=\"Date of Incorporation\"\r\n                type=\"date\"\r\n                value={formData.date_incorporation || ''}\r\n                onChange={(e) => handleFormChange('date_incorporation', e.target.value)}\r\n                required\r\n                error={validationErrors.date_incorporation}\r\n              />\r\n\r\n              <div className=\"md:col-span-2\">\r\n                <TextInput\r\n                  label=\"Place of Incorporation\"\r\n                  value={formData.place_incorporation || ''}\r\n                  onChange={(e) => handleFormChange('place_incorporation', e.target.value)}\r\n                  placeholder=\"e.g., Blantyre, Malawi\"\r\n                  required\r\n                  error={validationErrors.place_incorporation}\r\n                />\r\n              </div>\r\n\r\n              {/* Contact Information */}\r\n              <div className=\"md:col-span-2\">\r\n                <h4 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 mt-6\">Contact Information</h4>\r\n              </div>\r\n\r\n              <TextInput\r\n                label=\"Email Address\"\r\n                type=\"email\"\r\n                value={formData.email || ''}\r\n                onChange={(e) => handleFormChange('email', e.target.value)}\r\n                placeholder=\"<EMAIL>\"\r\n                required\r\n                error={validationErrors.email}\r\n              />\r\n\r\n              <TextInput\r\n                label=\"Phone Number\"\r\n                value={formData.phone || ''}\r\n                onChange={(e) => handleFormChange('phone', e.target.value)}\r\n                placeholder=\"+265 1 234 567\"\r\n                required\r\n                error={validationErrors.phone}\r\n              />\r\n\r\n              <TextInput\r\n                label=\"Fax Number\"\r\n                value={formData.fax || ''}\r\n                onChange={(e) => handleFormChange('fax', e.target.value)}\r\n                placeholder=\"+265 1 234 568\"\r\n                error={validationErrors.fax}\r\n              />\r\n\r\n              <TextInput\r\n                label=\"Level of Insurance Cover\"\r\n                value={formData.level_of_insurance_cover || ''}\r\n                onChange={(e) => handleFormChange('level_of_insurance_cover', e.target.value)}\r\n                placeholder=\"e.g., $1,000,000 USD\"\r\n                error={validationErrors.level_of_insurance_cover}\r\n              />\r\n            </div>\r\n\r\n\r\n\r\n            {/* Error Display */}\r\n            {validationErrors.save && (\r\n              <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\">\r\n                <div className=\"flex items-center\">\r\n                  <i className=\"ri-error-warning-line text-red-600 dark:text-red-400 text-lg mr-3\"></i>\r\n                  <div>\r\n                    <h3 className=\"text-sm font-medium text-red-800 dark:text-red-200\">\r\n                      Error Saving Application\r\n                    </h3>\r\n                    <p className=\"text-red-700 dark:text-red-300 text-sm mt-1\">\r\n                      {validationErrors.save}\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Success Message */}\r\n            {applicationCreated && !applicationId && (\r\n              <div className=\"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4\">\r\n                <div className=\"flex items-center\">\r\n                  <i className=\"ri-check-circle-line text-green-600 dark:text-green-400 text-lg mr-3\"></i>\r\n                  <div>\r\n                    <h3 className=\"text-sm font-medium text-green-800 dark:text-green-200\">\r\n                      Application Created Successfully!\r\n                    </h3>\r\n                    <p className=\"text-green-700 dark:text-green-300 text-sm mt-1\">\r\n                      Your application has been created. You can now continue to the next step.\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n\r\n      </ApplicationLayout>\r\n    </CustomerLayout>\r\n  );\r\n};\r\n\r\nexport default ApplicantInfoPage;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAbA;;;;;;;;;;;;;AAeA,MAAM,oBAA8B;IAClC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,EAAE,eAAe,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAExD,uCAAuC;IACvC,MAAM,cAAc,IAAI,6HAAA,CAAA,qBAAkB;IAE1C,uBAAuB;IACvB,MAAM,oBAAoB,aAAa,GAAG,CAAC;IAC3C,MAAM,gBAAgB,aAAa,GAAG,CAAC;IAEvC,QAAQ;IAGR,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,qDAAqD;IACrD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,8BAA8B;QAC9B,MAAM;QACN,SAAS;QACT,OAAO;QACP,OAAO;QACP,KAAK;QACL,0BAA0B;QAC1B,oBAAoB;QACpB,qBAAqB;IACvB;IAEA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAClF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAChF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAIpE,0BAA0B;IAC1B,MAAM,mBAAmB,CAAC,OAAe;QACvC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAEhD,wCAAwC;QACxC,IAAI,gBAAgB,CAAC,MAAM,EAAE;YAC3B,oBAAoB,CAAA;gBAClB,MAAM,YAAY;oBAAE,GAAG,IAAI;gBAAC;gBAC5B,OAAO,SAAS,CAAC,MAAM;gBACvB,OAAO;YACT;QACF;IACF;IAEA,gBAAgB;IAChB,MAAM,aAAa,OAAO,oBAAoB,KAAK;QACjD,QAAQ,GAAG,CAAC,+BAA+B;YAAE;QAAkB;QAC/D,YAAY;QACZ,oBAAoB,CAAC;QAErB,IAAI;YACF,qBAAqB;YACrB,QAAQ,GAAG,CAAC,4BAA4B;YACxC,MAAM,aAAa,CAAA,GAAA,8HAAA,CAAA,kBAAe,AAAD,EAAE,UAAoC;YACvE,IAAI,CAAC,WAAW,OAAO,EAAE;gBACvB,QAAQ,KAAK,CAAC,wBAAwB,WAAW,MAAM;gBACvD,oBAAoB,WAAW,MAAM,IAAI,CAAC;gBAC1C,YAAY;gBACZ,OAAO;YACT;YACA,QAAQ,GAAG,CAAC;YAEZ,IAAI,uBAAuB;YAE3B,uCAAuC;YACvC,IAAI,CAAC,sBAAsB;gBACzB,QAAQ,GAAG,CAAC;gBAEZ,8DAA8D;gBAE9D,wEAAwE;gBACxE,MAAM,gBAAgB;oBACpB,MAAM,SAAS,IAAI;oBACnB,8BAA8B,SAAS,4BAA4B;oBACnE,MAAM,SAAS,IAAI;oBACnB,SAAS,SAAS,OAAO;oBACzB,OAAO,SAAS,KAAK;oBACrB,OAAO,SAAS,KAAK;oBACrB,yDAAyD;oBACzD,GAAI,SAAS,GAAG,IAAI,SAAS,GAAG,CAAC,IAAI,GAAG,MAAM,IAAI,KAAK;wBAAE,KAAK,SAAS,GAAG,CAAC,IAAI;oBAAG,IAAI,CAAC,CAAC;oBACxF,yDAAyD;oBACzD,GAAI,SAAS,wBAAwB,IAAI,SAAS,wBAAwB,CAAC,IAAI,KAC3E;wBAAE,0BAA0B,SAAS,wBAAwB,CAAC,IAAI;oBAAG,IAAI,CAAC,CAAC;oBAC/E,oBAAoB,SAAS,kBAAkB;oBAC/C,qBAAqB,SAAS,mBAAmB;gBACnD;gBAEA,MAAM,YAAY,MAAM,mIAAA,CAAA,mBAAgB,CAAC,eAAe,CAAC;gBACzD,QAAQ,GAAG,CAAC,sBAAsB;gBAElC,0BAA0B;gBAC1B,MAAM,oBAAoB,CAAC,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW,IAAI;gBAC1G,MAAM,kBAAkB;oBACtB,oBAAoB;oBACpB,qBAAqB;oBACrB,cAAc,UAAU,YAAY;oBACpC,QAAQ,uHAAA,CAAA,oBAAiB,CAAC,KAAK;gBACjC;gBAEA,MAAM,cAAc,MAAM,qIAAA,CAAA,qBAAkB,CAAC,iBAAiB,CAAC;gBAC/D,QAAQ,GAAG,CAAC,0BAA0B;gBAEtC,uBAAuB,YAAY,cAAc;gBACjD,QAAQ,GAAG,CAAC,oCAAoC;gBAChD,wBAAwB;gBACxB,sBAAsB;YACxB,OAAO;gBACL,iCAAiC;gBACjC,QAAQ,GAAG,CAAC;gBAEZ,uCAAuC;gBACvC,MAAM,cAAc,MAAM,qIAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;gBAC5D,IAAI,YAAY,YAAY,EAAE;oBAC5B,iCAAiC;oBAKjC,wEAAwE;oBACxE,MAAM,gBAAgB;wBACpB,MAAM,SAAS,IAAI;wBACnB,8BAA8B,SAAS,4BAA4B;wBACnE,MAAM,SAAS,IAAI;wBACnB,SAAS,SAAS,OAAO;wBACzB,OAAO,SAAS,KAAK;wBACrB,OAAO,SAAS,KAAK;wBACrB,yDAAyD;wBACzD,GAAI,SAAS,GAAG,IAAI,SAAS,GAAG,CAAC,IAAI,GAAG,MAAM,IAAI,KAAK;4BAAE,KAAK,SAAS,GAAG,CAAC,IAAI;wBAAG,IAAI,CAAC,CAAC;wBACxF,yDAAyD;wBACzD,GAAI,SAAS,wBAAwB,IAAI,SAAS,wBAAwB,CAAC,IAAI,KAC3E;4BAAE,0BAA0B,SAAS,wBAAwB,CAAC,IAAI;wBAAG,IAAI,CAAC,CAAC;wBAC/E,oBAAoB,SAAS,kBAAkB;wBAC/C,qBAAqB,SAAS,mBAAmB;oBACnD;oBAEA,MAAM,mIAAA,CAAA,mBAAgB,CAAC,eAAe,CAAC,YAAY,YAAY,EAAE;oBACjE,QAAQ,GAAG,CAAC;gBACd;YACF;YACA,8BAA8B;YAC9B,IAAI;gBACF,MAAM,qBAAqB,wBAAwB;gBACnD,IAAI,oBAAoB;oBACtB,MAAM,qIAAA,CAAA,qBAAkB,CAAC,iBAAiB,CAAC,oBAAoB;wBAC7D,cAAc;wBACd,qBAAqB,GAAG,wBAAwB;oBAClD;oBACA,QAAQ,GAAG,CAAC;gBACd;YACF,EAAE,OAAO,eAAe;gBACtB,QAAQ,IAAI,CAAC,0CAA0C;YACzD;YAEA,iBAAiB;YAEjB,uBAAuB;YACvB,QAAQ,GAAG,CAAC;YAEZ,qCAAqC;YACrC,IAAI,mBAAmB;gBACrB,QAAQ,GAAG,CAAC;gBACZ,MAAM,qBAAqB,wBAAwB;gBACnD,QAAQ,GAAG,CAAC,6CAA6C,sBAAsB,yBAAyB,sBAAsB,uBAAuB;gBACrJ,WAAW;YACb;YAEA,OAAO,MAAM,mBAAmB;QAElC,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,yCAAyC;YACvD,QAAQ,KAAK,CAAC,kBAAkB,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;YAErE,iDAAiD;YACjD,IAAI,MAAM,QAAQ,EAAE,WAAW,OAAO,MAAM,QAAQ,EAAE,MAAM,SAAS;gBACnE,MAAM,iBAAiB,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;gBAElD,mDAAmD;gBACnD,IAAI,eAAe,QAAQ,CAAC,QAAQ;oBAClC,oBAAoB;wBAClB,KAAK;wBACL,MAAM;oBACR;gBACF,OAAO,IAAI,eAAe,QAAQ,CAAC,UAAU;oBAC3C,oBAAoB;wBAClB,OAAO;wBACP,MAAM;oBACR;gBACF,OAAO,IAAI,eAAe,QAAQ,CAAC,UAAU;oBAC3C,oBAAoB;wBAClB,OAAO;wBACP,MAAM;oBACR;gBACF,OAAO;oBACL,oBAAoB;wBAAE,MAAM;oBAAe;gBAC7C;YACF,OAAO;gBACL,oBAAoB;oBAAE,MAAM;gBAAgD;YAC9E;YACA,OAAO,OAAO,mBAAmB;QACnC,SAAU;YACR,YAAY;QACd;IACF;IAEA,6BAA6B;IAC7B,MAAM,wBAAwB;QAC5B,QAAQ,GAAG,CAAC;QACZ,MAAM,UAAU,MAAM,WAAW;QACjC,IAAI,CAAC,SAAS;YACZ,QAAQ,KAAK,CAAC;QAChB;IACA,oDAAoD;IACtD;IAEA,uBAAuB;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,eAAe,CAAC,iBAAiB;YACpC,OAAO,IAAI,CAAC;YACZ;QACF;IACF,GAAG;QAAC;QAAiB;QAAa;KAAO;IAEzC,YAAY;IACZ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW;YACf,IAAI;gBACF,IAAI,CAAC,mBAAmB;oBACtB,SAAS;oBACT,WAAW;oBACX;gBACF;gBACA,wBAAwB;gBACxB,MAAM,WAAW,MAAM,YAAY,kBAAkB,CAAC;gBACtD,MAAM,YAAY,cAAc,CAAC,SAAS,eAAe;gBAEzD,IAAI,CAAC,UAAU;oBACb,SAAS;oBACT,WAAW;oBACX;gBACF;gBAEA,gDAAgD;gBAEhD,IAAI,CAAC,SAAS,eAAe,EAAE;oBAC7B,SAAS;oBACT,WAAW;oBACX;gBACF;gBAEA,+DAA+D;gBAC/D,IAAI,eAAe;oBACjB,IAAI;wBACF,QAAQ,GAAG,CAAC,gDAAgD;wBAC5D,MAAM,cAAc,MAAM,qIAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;wBAC5D,QAAQ,GAAG,CAAC,0BAA0B;wBAEtC,IAAI,YAAY,YAAY,EAAE;4BAC5B,QAAQ,GAAG,CAAC,8CAA8C,YAAY,YAAY;4BAElF,IAAI;gCACF,MAAM,YAAY,MAAM,mIAAA,CAAA,mBAAgB,CAAC,YAAY,CAAC,YAAY,YAAY;gCAC9E,QAAQ,GAAG,CAAC,6BAA6B;gCAEzC,kDAAkD;gCAClD,MAAM,gBAAgB;oCACpB,MAAM,UAAU,IAAI,IAAI;oCACxB,8BAA8B,UAAU,4BAA4B,IAAI;oCACxE,MAAM,UAAU,IAAI,IAAI;oCACxB,SAAS,UAAU,OAAO,IAAI;oCAC9B,OAAO,UAAU,KAAK,IAAI;oCAC1B,OAAO,UAAU,KAAK,IAAI;oCAC1B,KAAK,UAAU,GAAG,IAAI;oCACtB,0BAA0B,UAAU,wBAAwB,IAAI;oCAChE,oBAAoB,UAAU,kBAAkB,IAAI;oCACpD,qBAAqB,UAAU,mBAAmB,IAAI;gCACxD;gCAEA,QAAQ,GAAG,CAAC,iCAAiC;gCAC7C,YAAY,CAAA;oCACV,MAAM,UAAU;wCAAE,GAAG,IAAI;wCAAE,GAAG,aAAa;oCAAC;oCAC5C,QAAQ,GAAG,CAAC,kCAAkC;oCAC9C,OAAO;gCACT;gCAEA,QAAQ,GAAG,CAAC;4BACd,EAAE,OAAO,gBAAqB;gCAC5B,QAAQ,KAAK,CAAC,mCAAmC;gCACjD,QAAQ,KAAK,CAAC,4BAA4B;oCACxC,aAAa,YAAY,YAAY;oCACrC,SAAS,eAAe,OAAO;oCAC/B,UAAU,eAAe,QAAQ,EAAE;oCACnC,QAAQ,eAAe,QAAQ,EAAE;gCACnC;gCAEA,8DAA8D;gCAC9D,QAAQ,IAAI,CAAC;gCAEb,+BAA+B;gCAC/B,IAAI,eAAe,QAAQ,EAAE,WAAW,KAAK;oCAC3C,kBAAkB;oCAClB,QAAQ,IAAI,CAAC;gCACf,OAAO;oCACL,kBAAkB;gCACpB;4BACF;wBACF,OAAO;4BACL,QAAQ,GAAG,CAAC,mEAAmE;wBACjF;oBACF,EAAE,OAAO,UAAe;wBACtB,QAAQ,KAAK,CAAC,8CAA8C;wBAC5D,QAAQ,KAAK,CAAC,kBAAkB;4BAC9B,SAAS,SAAS,OAAO;4BACzB,UAAU,SAAS,QAAQ,EAAE;4BAC7B,QAAQ,SAAS,QAAQ,EAAE;wBAC7B;oBACA,yEAAyE;oBAC3E;gBACF,OAAO;oBACL,QAAQ,GAAG,CAAC;gBACd;gBAEA,QAAQ,GAAG,CAAC;gBACZ,WAAW;YACb,EAAE,OAAO,KAAU;gBACjB,QAAQ,KAAK,CAAC,uBAAuB;gBACrC,QAAQ,KAAK,CAAC,kBAAkB;oBAC9B,SAAS,IAAI,OAAO;oBACpB,UAAU,IAAI,QAAQ,EAAE;oBACxB,QAAQ,IAAI,QAAQ,EAAE;gBACxB;gBAEA,sCAAsC;gBACtC,IAAI,eAAe;gBACnB,IAAI,IAAI,QAAQ,EAAE,WAAW,KAAK;oBAChC,eAAe,CAAC,gCAAgC,EAAE,kBAAkB,+EAA+E,CAAC;gBACtJ,OAAO,IAAI,IAAI,QAAQ,EAAE,WAAW,KAAK;oBACvC,eAAe;gBACjB,OAAO,IAAI,IAAI,QAAQ,EAAE,WAAW,KAAK;oBACvC,eAAe;gBACjB,OAAO,IAAI,IAAI,OAAO,EAAE;oBACtB,eAAe,CAAC,OAAO,EAAE,IAAI,OAAO,EAAE;gBACxC;gBAEA,SAAS;gBACT,WAAW;YACb;QACF;QAEA,IAAI,mBAAmB,CAAC,aAAa;YACnC;QACF;IACF,GAAG;QAAC;QAAmB;QAAe;QAAiB;KAAY;IAEnE,sDAAsD;IACtD,MAAM,aAAa,CAAC;QAClB,QAAQ,GAAG,CAAC;QACZ,MAAM,SAAS,IAAI;QACnB,OAAO,GAAG,CAAC,uBAAuB;QAElC,2CAA2C;QAC3C,MAAM,qBAAqB,yBAAyB,iBAAiB;QACrE,IAAI,oBAAoB;YACtB,OAAO,GAAG,CAAC,kBAAkB;QAC/B,OAAO;YACL,QAAQ,IAAI,CAAC,kDAAkD;gBAC7D;gBACA;gBACA;YACF;QACF;QAEA,MAAM,UAAU,CAAC,0CAA0C,EAAE,OAAO,QAAQ,IAAI;QAChF,QAAQ,GAAG,CAAC,gBAAgB;QAC5B,QAAQ,GAAG,CAAC,4BAA4B;QACxC,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,iBAAiB;QACrB,OAAO,IAAI,CAAC;IACd;IAGA,gBAAgB;IAChB,IAAI,eAAe,SAAS;QAC1B,qBACE,8OAAC,gJAAA,CAAA,UAAc;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;;;;;;;;;;;IAK1D;IAEA,cAAc;IACd,IAAI,OAAO;QACT,qBACE,8OAAC,gJAAA,CAAA,UAAc;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;;;;;;;;;;sCAEf,8OAAC;4BAAG,WAAU;sCAA4D;;;;;;sCAG1E,8OAAC;4BAAE,WAAU;sCACV;;;;;;sCAEH,8OAAC;4BACC,MAAK;4BACL,SAAS,IAAM,OAAO,IAAI,CAAC;4BAC3B,WAAU;;8CAEV,8OAAC;oCAAE,WAAU;;;;;;gCAA8B;;;;;;;;;;;;;;;;;;;;;;;IAOvD;IAIA,qBACE,8OAAC,gJAAA,CAAA,UAAc;kBACb,cAAA,8OAAC,uMAAA,CAAA,oBAAiB;YAChB,QAAQ;YACR,YAAY;YACZ,QAAQ;gBACN,QAAQ,GAAG,CAAC;gBACZ,MAAM,WAAW;YACnB;YACA,gBAAgB;YAChB,oBAAoB;YACpB,gBAAgB;YAChB,gBAAe;YACf,oBAAmB;YACnB,gBAAgB,gBAAgB,iBAAiB;YACjD,oBAAoB;YACpB,UAAU;;8BAIV,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,gBAAgB,+BAA+B;;;;;;sCAElD,8OAAC;4BAAE,WAAU;sCACV,gBACG,6CACA;;;;;;wBAGL,iBAAiB,CAAC,gCACjB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAA6C;;;;;;;;;;;wBAK7D,gCACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;oCAA+C;oCACtD;;;;;;;;;;;;wBAIT,CAAC,+BACA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;kDACP,8OAAC;wCAAE,WAAU;;;;;;oCAA+B;;;;;;;;;;;;wBAKjD,oCACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;kDACX,8OAAC;wCAAE,WAAU;;;;;;oCAAyB;oCAChB,sBAAsB,MAAM,GAAG;oCAAG;;;;;;;;;;;;;;;;;;8BAMhE,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CAA4D;;;;;;;;;;;sCAG5E,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,yIAAA,CAAA,UAAS;gCACR,OAAM;gCACN,OAAO,SAAS,IAAI,IAAI;gCACxB,UAAU,CAAC,IAAM,iBAAiB,QAAQ,EAAE,MAAM,CAAC,KAAK;gCACxD,aAAY;gCACZ,QAAQ;gCACR,OAAO,iBAAiB,IAAI;;;;;;;;;;;sCAIhC,8OAAC,yIAAA,CAAA,UAAS;4BACR,OAAM;4BACN,OAAO,SAAS,4BAA4B,IAAI;4BAChD,UAAU,CAAC,IAAM,iBAAiB,gCAAgC,EAAE,MAAM,CAAC,KAAK;4BAChF,aAAY;4BACZ,QAAQ;4BACR,OAAO,iBAAiB,4BAA4B;;;;;;sCAGtD,8OAAC,yIAAA,CAAA,UAAS;4BACR,OAAM;4BACN,OAAO,SAAS,IAAI,IAAI;4BACxB,UAAU,CAAC,IAAM,iBAAiB,QAAQ,EAAE,MAAM,CAAC,KAAK;4BACxD,aAAY;4BACZ,QAAQ;4BACR,OAAO,iBAAiB,IAAI;;;;;;sCAG9B,8OAAC,yIAAA,CAAA,UAAS;4BACR,OAAM;4BACN,MAAK;4BACL,OAAO,SAAS,OAAO,IAAI;4BAC3B,UAAU,CAAC,IAAM,iBAAiB,WAAW,EAAE,MAAM,CAAC,KAAK;4BAC3D,aAAY;4BACZ,OAAO,iBAAiB,OAAO;;;;;;sCAGjC,8OAAC,yIAAA,CAAA,UAAS;4BACR,OAAM;4BACN,MAAK;4BACL,OAAO,SAAS,kBAAkB,IAAI;4BACtC,UAAU,CAAC,IAAM,iBAAiB,sBAAsB,EAAE,MAAM,CAAC,KAAK;4BACtE,QAAQ;4BACR,OAAO,iBAAiB,kBAAkB;;;;;;sCAG5C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,yIAAA,CAAA,UAAS;gCACR,OAAM;gCACN,OAAO,SAAS,mBAAmB,IAAI;gCACvC,UAAU,CAAC,IAAM,iBAAiB,uBAAuB,EAAE,MAAM,CAAC,KAAK;gCACvE,aAAY;gCACZ,QAAQ;gCACR,OAAO,iBAAiB,mBAAmB;;;;;;;;;;;sCAK/C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CAAiE;;;;;;;;;;;sCAGjF,8OAAC,yIAAA,CAAA,UAAS;4BACR,OAAM;4BACN,MAAK;4BACL,OAAO,SAAS,KAAK,IAAI;4BACzB,UAAU,CAAC,IAAM,iBAAiB,SAAS,EAAE,MAAM,CAAC,KAAK;4BACzD,aAAY;4BACZ,QAAQ;4BACR,OAAO,iBAAiB,KAAK;;;;;;sCAG/B,8OAAC,yIAAA,CAAA,UAAS;4BACR,OAAM;4BACN,OAAO,SAAS,KAAK,IAAI;4BACzB,UAAU,CAAC,IAAM,iBAAiB,SAAS,EAAE,MAAM,CAAC,KAAK;4BACzD,aAAY;4BACZ,QAAQ;4BACR,OAAO,iBAAiB,KAAK;;;;;;sCAG/B,8OAAC,yIAAA,CAAA,UAAS;4BACR,OAAM;4BACN,OAAO,SAAS,GAAG,IAAI;4BACvB,UAAU,CAAC,IAAM,iBAAiB,OAAO,EAAE,MAAM,CAAC,KAAK;4BACvD,aAAY;4BACZ,OAAO,iBAAiB,GAAG;;;;;;sCAG7B,8OAAC,yIAAA,CAAA,UAAS;4BACR,OAAM;4BACN,OAAO,SAAS,wBAAwB,IAAI;4BAC5C,UAAU,CAAC,IAAM,iBAAiB,4BAA4B,EAAE,MAAM,CAAC,KAAK;4BAC5E,aAAY;4BACZ,OAAO,iBAAiB,wBAAwB;;;;;;;;;;;;gBAOnD,iBAAiB,IAAI,kBACpB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;;;;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAqD;;;;;;kDAGnE,8OAAC;wCAAE,WAAU;kDACV,iBAAiB,IAAI;;;;;;;;;;;;;;;;;;;;;;;gBAQ/B,sBAAsB,CAAC,+BACtB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;;;;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAyD;;;;;;kDAGvE,8OAAC;wCAAE,WAAU;kDAAkD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYnF;uCAEe", "debugId": null}}]}