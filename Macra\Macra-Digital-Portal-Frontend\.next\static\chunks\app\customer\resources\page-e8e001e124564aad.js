(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6737],{25665:(e,r,a)=>{Promise.resolve().then(a.bind(a,85503))},30606:(e,r,a)=>{"use strict";a.d(r,{A:()=>i});var t=a(95155);let s=(0,a(12115).forwardRef)((e,r)=>{let{label:a,error:s,helperText:i,variant:l="default",fullWidth:d=!0,className:c="",required:n,disabled:o,rows:m=3,...x}=e,u="px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 resize-y ".concat(d?"w-full":""," ").concat("small"===l?"py-1.5 text-sm":"py-2"),g="".concat(u," ").concat(s?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"," ").concat(o?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""," ").concat(c);return(0,t.jsxs)("div",{className:"w-full",children:[a&&(0,t.jsxs)("label",{className:"block font-medium text-gray-700 dark:text-gray-300 mb-2 ".concat("small"===l?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"),children:[a,n&&(0,t.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,t.jsx)("textarea",{ref:r,className:g,disabled:o,required:n,rows:m,...x}),s&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:s}),i&&!s&&(0,t.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:i})]})});s.displayName="TextArea";let i=s},35695:(e,r,a)=>{"use strict";var t=a(18999);a.o(t,"useParams")&&a.d(r,{useParams:function(){return t.useParams}}),a.o(t,"usePathname")&&a.d(r,{usePathname:function(){return t.usePathname}}),a.o(t,"useRouter")&&a.d(r,{useRouter:function(){return t.useRouter}}),a.o(t,"useSearchParams")&&a.d(r,{useSearchParams:function(){return t.useSearchParams}})},61967:(e,r,a)=>{"use strict";a.d(r,{A:()=>i});var t=a(95155);let s=(0,a(12115).forwardRef)((e,r)=>{let{label:a,error:s,helperText:i,variant:l="default",fullWidth:d=!0,className:c="",required:n,disabled:o,...m}=e,x="px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 ".concat(d?"w-full":""," ").concat("small"===l?"py-1.5 text-sm":"py-2"),u="".concat(x," ").concat(s?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"," ").concat(o?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""," ").concat(c);return(0,t.jsxs)("div",{className:"w-full",children:[a&&(0,t.jsxs)("label",{className:"block font-medium text-gray-700 dark:text-gray-300 mb-2 ".concat("small"===l?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"),children:[a,n&&(0,t.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,t.jsx)("input",{ref:r,className:u,disabled:o,required:n,...m}),s&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:s}),i&&!s&&(0,t.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:i})]})});s.displayName="TextInput";let i=s},63956:(e,r,a)=>{"use strict";a.d(r,{A:()=>i});var t=a(95155);let s=(0,a(12115).forwardRef)((e,r)=>{let{label:a,error:s,helperText:i,variant:l="default",fullWidth:d=!0,className:c="",required:n,disabled:o,options:m,children:x,...u}=e,g="px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white transition-colors duration-200 ".concat(d?"w-full":""," ").concat("small"===l?"py-1.5 text-sm":"py-2"),h="".concat(g," ").concat(s?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"," ").concat(o?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""," ").concat(c);return(0,t.jsxs)("div",{className:"w-full",children:[a&&(0,t.jsxs)("label",{className:"block font-medium text-gray-700 dark:text-gray-300 mb-2 ".concat("small"===l?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"),children:[a,n&&(0,t.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,t.jsx)("select",{ref:r,className:h,disabled:o,required:n,...u,children:m?m.map(e=>(0,t.jsx)("option",{value:e.value,children:e.label},e.value)):x}),s&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:s}),i&&!s&&(0,t.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:i})]})});s.displayName="Select";let i=s},85503:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>u});var t=a(95155),s=a(12115),i=a(6874),l=a.n(i),d=a(35695),c=a(73209),n=a(40283),o=a(61967),m=a(30606),x=a(63956);let u=()=>{let{isAuthenticated:e,loading:r}=(0,n.A)(),a=(0,d.useRouter)(),[i,u]=(0,s.useState)(!1),[g,h]=(0,s.useState)({requestType:"",subject:"",description:"",relatedLicense:"",contactMethod:[]}),[y,p]=(0,s.useState)([]),[b,j]=(0,s.useState)(0),[f,N]=(0,s.useState)(!1),[v,k]=(0,s.useState)(""),[w,S]=(0,s.useState)(!1);if((0,s.useEffect)(()=>{u(!0)},[]),(0,s.useEffect)(()=>{!i||r||e||a.push("/customer/auth/login")},[i,r,e,a]),!i||r)return(0,t.jsx)(c.A,{children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,t.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading..."})]})})})});if(!e)return null;let L=e=>{let{name:r,value:a}=e.target;h(e=>({...e,[r]:a})),"description"===r&&j(a.length)},R=e=>{let{value:r,checked:a}=e.target;h(e=>({...e,contactMethod:a?[...e.contactMethod,r]:e.contactMethod.filter(e=>e!==r)}))},q=e=>{p(r=>r.filter(r=>r.name!==e))},C=async e=>{e.preventDefault(),S(!0);try{let e=Math.floor(1e3*Math.random())+1,r="#REQ-2025-".concat(String(e).padStart(3,"0"));k(r),N(!0)}catch(e){alert("Failed to submit request. Please try again.")}finally{S(!1)}};return(0,t.jsx)(c.A,{children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mb-4",children:[(0,t.jsx)(l(),{href:"/customer",className:"hover:text-primary",children:"Dashboard"}),(0,t.jsx)("i",{className:"ri-arrow-right-s-line"}),(0,t.jsx)("span",{className:"text-gray-900 dark:text-gray-100",children:"Request Resource"})]}),(0,t.jsx)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-gray-100",children:"Request Resource"}),(0,t.jsx)("p",{className:"mt-2 text-gray-600 dark:text-gray-400",children:"Submit requests for additional resources, support, or services from MACRA."})]})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,t.jsx)("div",{className:"lg:col-span-2",children:(0,t.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6",children:(0,t.jsxs)("form",{onSubmit:C,className:"space-y-6",children:[(0,t.jsxs)(x.A,{label:(0,t.jsxs)("span",{children:[(0,t.jsx)("i",{className:"ri-folder-line mr-2 text-primary"}),"Request Type"]}),id:"requestType",name:"requestType",required:!0,value:g.requestType,onChange:L,children:[(0,t.jsx)("option",{value:"",children:"Select request type"}),(0,t.jsx)("option",{value:"technical-support",children:"Technical Support"}),(0,t.jsx)("option",{value:"license-modification",children:"License Modification"}),(0,t.jsx)("option",{value:"spectrum-allocation",children:"Spectrum Allocation"}),(0,t.jsx)("option",{value:"compliance-guidance",children:"Compliance Guidance"}),(0,t.jsx)("option",{value:"documentation",children:"Documentation Request"}),(0,t.jsx)("option",{value:"training",children:"Training & Capacity Building"}),(0,t.jsx)("option",{value:"consultation",children:"Regulatory Consultation"}),(0,t.jsx)("option",{value:"other",children:"Other"})]}),(0,t.jsx)(o.A,{label:(0,t.jsxs)("span",{children:[(0,t.jsx)("i",{className:"ri-text mr-2 text-primary"}),"Subject"]}),id:"subject",name:"subject",required:!0,value:g.subject,onChange:L,placeholder:"Brief description of your request"}),(0,t.jsxs)("div",{children:[(0,t.jsx)(m.A,{label:(0,t.jsxs)("span",{children:[(0,t.jsx)("i",{className:"ri-file-text-line mr-2 text-primary"}),"Detailed Description"]}),id:"description",name:"description",rows:6,required:!0,value:g.description,onChange:L,placeholder:"Provide detailed information about your request, including any specific requirements or context...",maxLength:1e3}),(0,t.jsxs)("div",{className:"mt-2 text-sm text-gray-500 dark:text-gray-400",children:[(0,t.jsx)("span",{className:b>1e3?"text-red-500":"",children:b}),"/1000 characters"]})]}),(0,t.jsxs)(x.A,{label:(0,t.jsxs)("span",{children:[(0,t.jsx)("i",{className:"ri-award-line mr-2 text-primary"}),"Related License (Optional)"]}),id:"relatedLicense",name:"relatedLicense",value:g.relatedLicense,onChange:L,children:[(0,t.jsx)("option",{value:"",children:"Select related license"}),(0,t.jsx)("option",{value:"NSL-2025-001",children:"NSL-2025-001 - Internet Service Provider License"}),(0,t.jsx)("option",{value:"RBL-2025-002",children:"RBL-2025-002 - Radio Broadcasting License"}),(0,t.jsx)("option",{value:"TVL-2025-003",children:"TVL-2025-003 - Television Broadcasting License"}),(0,t.jsx)("option",{value:"MNL-2023-001",children:"MNL-2023-001 - Mobile Network License"}),(0,t.jsx)("option",{value:"SCL-2025-004",children:"SCL-2025-004 - Satellite Communications License"}),(0,t.jsx)("option",{value:"PSL-2025-005",children:"PSL-2025-005 - Postal Services License"})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"fileInput",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[(0,t.jsx)("i",{className:"ri-attachment-line mr-2 text-primary"}),"Attachments (Optional)"]}),(0,t.jsxs)("div",{className:"border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center hover:border-primary transition-colors",children:[(0,t.jsx)("input",{type:"file",id:"fileInput",multiple:!0,accept:".pdf,.doc,.docx,.jpg,.jpeg,.png",onChange:e=>{Array.from(e.target.files||[]).forEach(e=>{if(e.size<=0xa00000){let r={name:e.name,size:e.size,file:e};p(e=>[...e,r])}else alert("File ".concat(e.name," is too large. Maximum size is 10MB."))}),e.target.value=""},"aria-label":"Upload attachment files",className:"hidden"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("i",{className:"ri-upload-cloud-line text-4xl text-gray-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("button",{type:"button",onClick:()=>{var e;return null==(e=document.getElementById("fileInput"))?void 0:e.click()},className:"text-primary hover:text-primary font-medium",children:"Click to upload files"}),(0,t.jsx)("span",{className:"text-gray-500 dark:text-gray-400",children:" or drag and drop"})]}),(0,t.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"PDF, DOC, DOCX, JPG, PNG up to 10MB each"})]})]}),y.length>0&&(0,t.jsx)("div",{className:"mt-3 space-y-2",children:y.map((e,r)=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("i",{className:"ri-file-line mr-3 text-primary"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:e.name}),(0,t.jsxs)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:[(e.size/1024/1024).toFixed(2)," MB"]})]})]}),(0,t.jsx)("button",{type:"button",onClick:()=>q(e.name),className:"text-red-500 hover:text-red-700","aria-label":"Remove ".concat(e.name),title:"Remove ".concat(e.name),children:(0,t.jsx)("i",{className:"ri-close-line"})})]},r))})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:[(0,t.jsx)("i",{className:"ri-phone-line mr-2 text-primary"}),"Preferred Contact Method"]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3",children:[(0,t.jsxs)("label",{className:"flex items-center p-3 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,t.jsx)("input",{type:"checkbox",name:"contactMethod",value:"email",checked:g.contactMethod.includes("email"),onChange:R,className:"rounded border-gray-300 text-primary focus:ring-primary"}),(0,t.jsx)("span",{className:"ml-3 text-sm text-gray-900 dark:text-gray-100",children:"Email"})]}),(0,t.jsxs)("label",{className:"flex items-center p-3 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,t.jsx)("input",{type:"checkbox",name:"contactMethod",value:"phone",checked:g.contactMethod.includes("phone"),onChange:R,className:"rounded border-gray-300 text-primary focus:ring-primary"}),(0,t.jsx)("span",{className:"ml-3 text-sm text-gray-900 dark:text-gray-100",children:"Phone"})]})]})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 pt-6",children:[(0,t.jsxs)("button",{type:"submit",disabled:w,className:"flex-1 bg-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,t.jsx)("i",{className:"ri-send-plane-line mr-2"}),w?"Submitting...":"Submit Request"]}),(0,t.jsxs)("button",{type:"button",onClick:()=>{alert("Draft saved successfully! You can continue editing later.")},className:"flex-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-lg font-medium hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all",children:[(0,t.jsx)("i",{className:"ri-save-line mr-2"}),"Save as Draft"]})]})]})})}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center",children:[(0,t.jsx)("i",{className:"ri-lightning-line mr-2 text-primary"}),"Quick Actions"]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)(l(),{href:"/customer/help",className:"flex items-center p-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors",children:[(0,t.jsx)("i",{className:"ri-question-line mr-3 text-primary"}),"View FAQ"]}),(0,t.jsxs)(l(),{href:"/customer/documents",className:"flex items-center p-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors",children:[(0,t.jsx)("i",{className:"ri-book-line mr-3 text-primary"}),"Resource Library"]}),(0,t.jsxs)(l(),{href:"/customer/help",className:"flex items-center p-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors",children:[(0,t.jsx)("i",{className:"ri-customer-service-line mr-3 text-primary"}),"Help Center"]}),(0,t.jsxs)(l(),{href:"/customer/resources/history",className:"flex items-center p-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors",children:[(0,t.jsx)("i",{className:"ri-time-line mr-3 text-primary"}),"Request History"]})]})]}),(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center",children:[(0,t.jsx)("i",{className:"ri-time-line mr-2 text-primary"}),"Expected Response Times"]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full mr-3"}),(0,t.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:"Low Priority"})]}),(0,t.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"5-7 days"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-yellow-500 rounded-full mr-3"}),(0,t.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:"Medium Priority"})]}),(0,t.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"2-3 days"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-red-50 dark:bg-red-900/20 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-red-500 rounded-full mr-3"}),(0,t.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:"High Priority"})]}),(0,t.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"24 hours"})]})]})]})]})]}),f&&(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-xl p-8 max-w-md mx-4",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)("i",{className:"ri-check-line text-2xl text-green-600"})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2",children:"Request Submitted Successfully"}),(0,t.jsxs)("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:["Your request has been submitted and assigned ticket number ",(0,t.jsx)("strong",{children:v}),". You will receive updates via email."]}),(0,t.jsx)("button",{onClick:()=>{N(!1),h({requestType:"",subject:"",description:"",relatedLicense:"",contactMethod:[]}),p([]),j(0)},className:"w-full bg-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-opacity-90 transition-all",children:"Continue"})]})})})]})})}}},e=>{var r=r=>e(e.s=r);e.O(0,[8122,6766,6874,283,3209,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>r(25665)),_N_E=e.O()}]);