import { Repository } from 'typeorm';
import { Applications } from '../entities/applications.entity';
import { ApplicationStatusHistory } from '../entities/application-status-history.entity';
import { User } from '../entities/user.entity';
import { UpdateApplicationStatusDto, ApplicationStatusTrackingResponseDto, ApplicationStatusHistoryResponseDto } from '../dto/application-status/update-application-status.dto';
export declare class ApplicationStatusTrackingService {
    private applicationsRepository;
    private statusHistoryRepository;
    private userRepository;
    constructor(applicationsRepository: Repository<Applications>, statusHistoryRepository: Repository<ApplicationStatusHistory>, userRepository: Repository<User>);
    updateApplicationStatus(applicationId: string, updateStatusDto: UpdateApplicationStatusDto, userId: string): Promise<ApplicationStatusTrackingResponseDto>;
    getApplicationStatusTracking(applicationId: string): Promise<ApplicationStatusTrackingResponseDto>;
    getApplicationsByStatus(status: string): Promise<ApplicationStatusTrackingResponseDto[]>;
    private validateStatusTransition;
    private calculateStepAndProgress;
    getStatusHistory(applicationId: string): Promise<ApplicationStatusHistoryResponseDto[]>;
}
