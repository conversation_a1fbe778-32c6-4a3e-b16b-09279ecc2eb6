"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationHelperService = void 0;
const common_1 = require("@nestjs/common");
const notifications_service_1 = require("./notifications.service");
const notifications_entity_1 = require("../entities/notifications.entity");
let NotificationHelperService = class NotificationHelperService {
    notificationsService;
    constructor(notificationsService) {
        this.notificationsService = notificationsService;
    }
    async notifyApplicationStatus(applicationId, applicantId, applicantEmail, applicantPhone, applicationNumber, status, createdBy) {
        const subject = `Application ${applicationNumber} Status Update`;
        const message = `Your application ${applicationNumber} status has been updated to: ${status.toUpperCase()}`;
        if (applicantEmail) {
            await this.notificationsService.create({
                type: notifications_entity_1.NotificationType.EMAIL,
                recipient_type: notifications_entity_1.RecipientType.CUSTOMER,
                recipient_id: applicantId,
                recipient_email: applicantEmail,
                subject,
                message,
                html_content: this.generateApplicationStatusEmailHtml(applicationNumber, status),
                entity_type: 'application',
                entity_id: applicationId,
            }, createdBy);
        }
        if (applicantPhone) {
            await this.notificationsService.create({
                type: notifications_entity_1.NotificationType.SMS,
                recipient_type: notifications_entity_1.RecipientType.CUSTOMER,
                recipient_id: applicantId,
                recipient_phone: applicantPhone,
                subject,
                message: `MACRA: ${message}`,
                entity_type: 'application',
                entity_id: applicationId,
            }, createdBy);
        }
        await this.notificationsService.create({
            type: notifications_entity_1.NotificationType.IN_APP,
            recipient_type: notifications_entity_1.RecipientType.CUSTOMER,
            recipient_id: applicantId,
            subject,
            message,
            entity_type: 'application',
            entity_id: applicationId,
            action_url: `/customer/my-licenses?application_id=${applicationId}`,
        }, createdBy);
    }
    async notifyTaskAssignment(taskId, assigneeId, assigneeEmail, assigneePhone, taskTitle, taskDescription, createdBy) {
        const subject = `New Task Assigned: ${taskTitle}`;
        const message = `You have been assigned a new task: ${taskTitle}. ${taskDescription}`;
        if (assigneeEmail) {
            await this.notificationsService.create({
                type: notifications_entity_1.NotificationType.EMAIL,
                recipient_type: notifications_entity_1.RecipientType.STAFF,
                recipient_id: assigneeId,
                recipient_email: assigneeEmail,
                subject,
                message,
                html_content: this.generateTaskAssignmentEmailHtml(taskTitle, taskDescription),
                entity_type: 'task',
                entity_id: taskId,
            }, createdBy);
        }
        if (assigneePhone) {
            await this.notificationsService.create({
                type: notifications_entity_1.NotificationType.SMS,
                recipient_type: notifications_entity_1.RecipientType.STAFF,
                recipient_id: assigneeId,
                recipient_phone: assigneePhone,
                subject,
                message: `MACRA: ${message}`,
                entity_type: 'task',
                entity_id: taskId,
            }, createdBy);
        }
        await this.notificationsService.create({
            type: notifications_entity_1.NotificationType.IN_APP,
            recipient_type: notifications_entity_1.RecipientType.STAFF,
            recipient_id: assigneeId,
            subject,
            message,
            entity_type: 'task',
            entity_id: taskId,
            action_url: `/tasks?task_id=${taskId}`,
        }, createdBy);
    }
    async notifyLicenseExpiry(licenseId, customerId, customerEmail, customerPhone, licenseNumber, expiryDate, daysUntilExpiry, createdBy) {
        const subject = `License ${licenseNumber} Expiry Notice`;
        const message = `Your license ${licenseNumber} will expire in ${daysUntilExpiry} days on ${expiryDate.toDateString()}. Please renew to avoid service interruption.`;
        if (customerEmail) {
            await this.notificationsService.create({
                type: notifications_entity_1.NotificationType.EMAIL,
                recipient_type: notifications_entity_1.RecipientType.CUSTOMER,
                recipient_id: customerId,
                recipient_email: customerEmail,
                subject,
                message,
                html_content: this.generateLicenseExpiryEmailHtml(licenseNumber, expiryDate, daysUntilExpiry),
                entity_type: 'license',
                entity_id: licenseId,
            }, createdBy);
        }
        if (customerPhone) {
            await this.notificationsService.create({
                type: notifications_entity_1.NotificationType.SMS,
                recipient_type: notifications_entity_1.RecipientType.CUSTOMER,
                recipient_id: customerId,
                recipient_phone: customerPhone,
                subject,
                message: `MACRA: ${message}`,
                entity_type: 'license',
                entity_id: licenseId,
            }, createdBy);
        }
        await this.notificationsService.create({
            type: notifications_entity_1.NotificationType.IN_APP,
            recipient_type: notifications_entity_1.RecipientType.CUSTOMER,
            recipient_id: customerId,
            subject,
            message,
            entity_type: 'license',
            entity_id: licenseId,
            action_url: `/customer/my-licenses?license_id=${licenseId}`,
        }, createdBy);
    }
    generateApplicationStatusEmailHtml(applicationNumber, status) {
        return `
      <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2 style="color: #d32f2f;">MACRA - Application Status Update</h2>
            <p>Dear Applicant,</p>
            <p>We are writing to inform you that your application <strong>${applicationNumber}</strong> status has been updated.</p>
            <div style="background-color: #f5f5f5; padding: 15px; border-left: 4px solid #d32f2f; margin: 20px 0;">
              <p style="margin: 0;"><strong>New Status:</strong> ${status.toUpperCase()}</p>
            </div>
            <p>You can view the full details of your application by logging into your MACRA portal account.</p>
            <p>If you have any questions, please contact our support team.</p>
            <p>Best regards,<br>MACRA Team</p>
          </div>
        </body>
      </html>
    `;
    }
    generateTaskAssignmentEmailHtml(taskTitle, taskDescription) {
        return `
      <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2 style="color: #d32f2f;">MACRA - New Task Assignment</h2>
            <p>Dear Team Member,</p>
            <p>You have been assigned a new task that requires your attention.</p>
            <div style="background-color: #f5f5f5; padding: 15px; border-left: 4px solid #d32f2f; margin: 20px 0;">
              <p style="margin: 0 0 10px 0;"><strong>Task:</strong> ${taskTitle}</p>
              <p style="margin: 0;"><strong>Description:</strong> ${taskDescription}</p>
            </div>
            <p>Please log into the MACRA portal to view and manage this task.</p>
            <p>Best regards,<br>MACRA System</p>
          </div>
        </body>
      </html>
    `;
    }
    generateLicenseExpiryEmailHtml(licenseNumber, expiryDate, daysUntilExpiry) {
        return `
      <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2 style="color: #d32f2f;">MACRA - License Expiry Notice</h2>
            <p>Dear License Holder,</p>
            <p>This is an important reminder regarding your MACRA license.</p>
            <div style="background-color: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 20px 0;">
              <p style="margin: 0 0 10px 0;"><strong>License Number:</strong> ${licenseNumber}</p>
              <p style="margin: 0 0 10px 0;"><strong>Expiry Date:</strong> ${expiryDate.toDateString()}</p>
              <p style="margin: 0;"><strong>Days Until Expiry:</strong> ${daysUntilExpiry} days</p>
            </div>
            <p>Please ensure you renew your license before the expiry date to avoid any service interruptions.</p>
            <p>You can renew your license by logging into your MACRA portal account.</p>
            <p>Best regards,<br>MACRA Team</p>
          </div>
        </body>
      </html>
    `;
    }
};
exports.NotificationHelperService = NotificationHelperService;
exports.NotificationHelperService = NotificationHelperService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [notifications_service_1.NotificationsService])
], NotificationHelperService);
//# sourceMappingURL=notification-helper.service.js.map