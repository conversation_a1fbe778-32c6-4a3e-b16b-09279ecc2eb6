(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3988],{2196:(e,t,a)=>{Promise.resolve().then(a.bind(a,86346))},6958:(e,t,a)=>{"use strict";a.d(t,{D:()=>n});var s=a(10012),r=a(52956);let i=new Map,n={async getDocuments(e){try{let t=new URLSearchParams;(null==e?void 0:e.page)&&t.append("page",e.page.toString()),(null==e?void 0:e.limit)&&t.append("limit",e.limit.toString()),(null==e?void 0:e.search)&&t.append("search",e.search),(null==e?void 0:e.sortBy)&&t.append("sortBy",e.sortBy);let a="/documents?".concat(t.toString());if(i.has(a))return await i.get(a);let n=r.uE.get(a).then(e=>(i.delete(a),(0,s.zp)(e))).catch(e=>{throw i.delete(a),e});return i.set(a,n),await n}catch(e){throw e}},async getDocumentsByEntity(e,t){try{let a=await r.uE.get("/documents/entity/".concat(e,"/").concat(t));return(0,s.zp)(a)}catch(e){throw e}},async getDocumentsByApplication(e){try{let t=await r.uE.get("/documents/by-application/".concat(e));return(0,s.zp)(t)}catch(e){throw e}},async getRequiredDocumentsForLicenseCategory(e){try{let t=await r.uE.get("/license-category-documents/category/".concat(e));return(0,s.zp)(t)}catch(e){throw e}},async uploadDocument(e,t){try{let a=new FormData;a.append("file",e),a.append("document_type",t.document_type),a.append("entity_type",t.entity_type),a.append("entity_id",t.entity_id),a.append("is_required",(t.is_required||!1).toString()),a.append("file_name",e.name);let i=await r.uE.post("/documents/upload",a,{headers:{"Content-Type":"multipart/form-data"}}),n=(0,s.zp)(i);return{document:n.data,message:n.message||"Document uploaded successfully"}}catch(e){throw e}},async createDocument(e){try{let t=await r.uE.post("/documents",e);return(0,s.zp)(t)}catch(e){throw e}},async updateDocument(e,t){try{let a=await r.uE.put("/documents/".concat(e),t);return(0,s.zp)(a)}catch(e){throw e}},async deleteDocument(e){try{await r.uE.delete("/documents/".concat(e))}catch(e){throw e}},async getDocument(e){try{let t=await r.uE.get("/documents/".concat(e));return(0,s.zp)(t)}catch(e){throw e}},async downloadDocument(e){try{return(await r.uE.get("/documents/".concat(e,"/download"),{responseType:"blob"})).data}catch(e){throw e}},async previewDocument(e){try{return(await r.uE.get("/documents/".concat(e,"/preview"),{responseType:"blob"})).data}catch(e){throw e}},isPreviewable(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return!!e&&["application/pdf","image/jpeg","image/jpg","image/png","image/gif","image/webp","text/plain","text/html","text/css","text/javascript","application/json"].includes(e.toLowerCase())},async checkRequiredDocuments(e,t){try{let a=await this.getRequiredDocumentsForLicenseCategory(t),s=(await this.getDocumentsByApplication(e)).data,r=s.map(e=>e.document_type),i=a.filter(e=>e.is_required&&!r.includes(e.name.toLowerCase().replace(/\s+/g,"_")));return{allUploaded:0===i.length,missing:i,uploaded:s}}catch(e){throw e}},getDocumentTypes:()=>["certificate_incorporation","memorandum_association","shareholding_structure","business_plan","financial_statements","technical_proposal","coverage_plan","network_diagram","equipment_specifications","insurance_certificate","tax_clearance","audited_accounts","bank_statement","cv_document","other"],formatDocumentType:e=>e.split("_").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "),mapDocumentNameToType(e){let t={"Certificate of Incorporation":"certificate_incorporation","Memorandum of Association":"memorandum_association","Shareholding Structure":"shareholding_structure","Business Plan":"business_plan","Financial Statements":"financial_statements","Technical Proposal":"technical_proposal","Coverage Plan":"coverage_plan","Network Diagram":"network_diagram","Equipment Specifications":"equipment_specifications","Insurance Certificate":"insurance_certificate","Tax Clearance Certificate":"tax_clearance","Tax Clearance":"tax_clearance","Audited Accounts":"audited_accounts","Bank Statement":"bank_statement","CV Document":"cv_document",Other:"other"};if(t[e])return t[e];let a=e.toLowerCase();for(let[e,s]of Object.entries(t))if(e.toLowerCase()===a)return s;return e.toLowerCase().replace(/\s+/g,"_")},validateFile(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return e.size>1024*t*1024?{isValid:!1,error:"File size must be less than ".concat(t,"MB")}:a.length>0&&!a.includes(e.type)?{isValid:!1,error:"File type not allowed. Allowed types: ".concat(a.join(", "))}:{isValid:!0}}}},30159:(e,t,a)=>{"use strict";a.d(t,{k:()=>i});var s=a(10012),r=a(52956);let i={async getApplications(e){var t,a,i;let n=new URLSearchParams;(null==e?void 0:e.page)&&n.append("page",e.page.toString()),(null==e?void 0:e.limit)&&n.append("limit",e.limit.toString()),(null==e?void 0:e.search)&&n.append("search",e.search),(null==e?void 0:e.sortBy)&&n.append("sortBy",e.sortBy),(null==e?void 0:e.sortOrder)&&n.append("sortOrder",e.sortOrder),(null==e||null==(t=e.filters)?void 0:t.licenseTypeId)&&n.append("filter.license_category.license_type_id",e.filters.licenseTypeId),(null==e||null==(a=e.filters)?void 0:a.licenseCategoryId)&&n.append("filter.license_category_id",e.filters.licenseCategoryId),(null==e||null==(i=e.filters)?void 0:i.status)&&n.append("filter.status",e.filters.status);let c=await r.uE.get("/applications?".concat(n.toString()));return(0,s.zp)(c)},async getApplicationsByLicenseType(e,t){let a=new URLSearchParams;(null==t?void 0:t.page)&&a.append("page",t.page.toString()),(null==t?void 0:t.limit)&&a.append("limit",t.limit.toString()),(null==t?void 0:t.search)&&a.append("search",t.search),(null==t?void 0:t.status)&&a.append("filter.status",t.status),a.append("filter.license_category.license_type_id",e);let i=await r.uE.get("/applications?".concat(a.toString()));return(0,s.zp)(i)},async getApplication(e){let t=await r.uE.get("/applications/".concat(e));return(0,s.zp)(t)},async getApplicationsByApplicant(e){let t=await r.uE.get("/applications/by-applicant/".concat(e));return(0,s.zp)(t)},async getApplicationsByStatus(e){let t=await r.uE.get("/applications/by-status/".concat(e));return(0,s.zp)(t)},async updateApplicationStatus(e,t){let a=await r.uE.put("/applications/".concat(e,"/status?status=").concat(t));return(0,s.zp)(a)},async updateApplicationProgress(e,t,a){let i=await r.uE.put("/applications/".concat(e,"/progress?currentStep=").concat(t,"&progressPercentage=").concat(a));return(0,s.zp)(i)},async getApplicationStats(){let e=await r.uE.get("/applications/stats");return(0,s.zp)(e)},async createApplication(e){try{let t=await r.uE.post("/applications",e);return(0,s.zp)(t)}catch(e){throw e}},async updateApplication(e,t){try{let a=await r.uE.put("/applications/".concat(e),t,{timeout:3e4});return(0,s.zp)(a)}catch(e){var a,i,n,c;if("ECONNABORTED"===e.code)throw Error("Request timeout - please try again");if((null==(a=e.response)?void 0:a.status)===400){let t=(null==(c=e.response)||null==(n=c.data)?void 0:n.message)||"Invalid application data";throw Error("Bad Request: ".concat(t))}if((null==(i=e.response)?void 0:i.status)===429)throw Error("Too many requests - please wait a moment and try again");throw e}},async deleteApplication(e){let t=await r.uE.delete("/applications/".concat(e));return(0,s.zp)(t)},async createApplicationWithApplicant(e){try{let t=new Date,a=t.toISOString().slice(0,10).replace(/-/g,""),s=t.toTimeString().slice(0,8).replace(/:/g,""),r=Math.random().toString(36).substr(2,3).toUpperCase(),i="APP-".concat(a,"-").concat(s,"-").concat(r);if(!/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e.user_id))throw Error("Invalid user_id format: ".concat(e.user_id,". Expected UUID format."));return await this.createApplication({application_number:i,applicant_id:e.user_id,license_category_id:e.license_category_id,current_step:1,progress_percentage:0})}catch(e){throw e}},async saveApplicationSection(e,t,a){try{let a=1,s=["applicantInfo","companyProfile","businessInfo","serviceScope","businessPlan","legalHistory","reviewSubmit"].indexOf(t);a=s>=0?s+1:1;let r=Math.min(Math.round(a/6*100),100);await this.updateApplication(e,{progress_percentage:r,current_step:a})}catch(e){throw e}},async getApplicationSection(e,t){try{let a=await r.uE.get("/applications/".concat(e,"/sections/").concat(t));return(0,s.zp)(a)}catch(e){throw e}},async submitApplication(e){try{let t=await r.uE.put("/applications/".concat(e),{status:"submitted",submitted_at:new Date().toISOString(),progress_percentage:100,current_step:7});return(0,s.zp)(t)}catch(e){throw e}},async getUserApplications(){try{let e=await r.uE.get("/applications/user-applications"),t=(0,s.zp)(e),a=[];return(null==t?void 0:t.data)?a=Array.isArray(t.data)?t.data:[]:Array.isArray(t)?a=t:t&&(a=[t]),a}catch(e){throw e}},async saveAsDraft(e,t){try{let a=await r.uE.put("/applications/".concat(e),{form_data:t,status:"draft"});return(0,s.zp)(a)}catch(e){throw e}},async validateApplication(e){try{let e={},t=[];for(let a of["applicantInfo","companyProfile","businessInfo","legalHistory"])e[a]&&0!==Object.keys(e[a]).length||t.push("".concat(a," section is incomplete"));return{isValid:0===t.length,errors:t}}catch(e){throw e}}}},35695:(e,t,a)=>{"use strict";var s=a(18999);a.o(s,"useParams")&&a.d(t,{useParams:function(){return s.useParams}}),a.o(s,"usePathname")&&a.d(t,{usePathname:function(){return s.usePathname}}),a.o(s,"useRouter")&&a.d(t,{useRouter:function(){return s.useRouter}}),a.o(s,"useSearchParams")&&a.d(t,{useSearchParams:function(){return s.useSearchParams}})},86346:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>p});var s=a(95155),r=a(35695),i=a(12115),n=a(40283),c=a(30159),l=a(52956),o=a(10012);let d={async getEvaluations(e){let t=new URLSearchParams;(null==e?void 0:e.page)&&t.append("page",e.page.toString()),(null==e?void 0:e.limit)&&t.append("limit",e.limit.toString()),(null==e?void 0:e.search)&&t.append("search",e.search),(null==e?void 0:e.sortBy)&&t.append("sortBy",e.sortBy),(null==e?void 0:e.sortOrder)&&t.append("sortOrder",e.sortOrder);let a=await l.uE.get("/evaluations?".concat(t.toString()));return(0,o.zp)(a)},async getEvaluation(e){let t=await l.uE.get("/evaluations/".concat(e));return(0,o.zp)(t)},async getEvaluationByApplication(e){try{let t=await l.uE.get("/evaluations/application/".concat(e));return(0,o.zp)(t)}catch(e){var t;if((null==(t=e.response)?void 0:t.status)===404)return null;throw e}},async getEvaluationCriteria(e){let t=await l.uE.get("/evaluations/".concat(e,"/criteria"));return(0,o.zp)(t)},async createEvaluation(e){let t=await l.uE.post("/evaluations",e);return(0,o.zp)(t)},async updateEvaluation(e,t){let a=await l.uE.patch("/evaluations/".concat(e),t);return(0,o.zp)(a)},async deleteEvaluation(e){await l.uE.delete("/evaluations/".concat(e))},async getEvaluationStats(){let e=await l.uE.get("/evaluations/stats");return(0,o.zp)(e)},async submitEvaluation(e,t){return this.updateEvaluation(e,{...t,status:"completed"})},calculateTotalScore(e){if(!e||0===e.length)return 0;let t=e.reduce((e,t)=>e+t.score*t.weight,0),a=e.reduce((e,t)=>e+t.weight,0);return a>0?t/a:0},getEvaluationTemplate(e){let t={postal_service:[{category:"financial_capacity",subcategory:"financial_documents",score:0,weight:.15,max_marks:15},{category:"financial_capacity",subcategory:"capital_adequacy",score:0,weight:.1,max_marks:10},{category:"financial_capacity",subcategory:"financial_projections",score:0,weight:.1,max_marks:10},{category:"financial_capacity",subcategory:"credit_worthiness",score:0,weight:.05,max_marks:5},{category:"business_plan",subcategory:"market_analysis",score:0,weight:.1,max_marks:10},{category:"business_plan",subcategory:"business_model",score:0,weight:.1,max_marks:10},{category:"business_plan",subcategory:"revenue_projections",score:0,weight:.05,max_marks:5},{category:"business_plan",subcategory:"growth_strategy",score:0,weight:.05,max_marks:5},{category:"technical_expertise",subcategory:"technical_capacity",score:0,weight:.1,max_marks:10},{category:"technical_expertise",subcategory:"operational_plan",score:0,weight:.1,max_marks:10},{category:"technical_expertise",subcategory:"implementation_timeline",score:0,weight:.05,max_marks:5},{category:"organizational_structure",subcategory:"management_structure",score:0,weight:.05,max_marks:5}],telecommunications:[{category:"financial_capacity",subcategory:"financial_documents",score:0,weight:.2,max_marks:20},{category:"financial_capacity",subcategory:"capital_adequacy",score:0,weight:.15,max_marks:15},{category:"technical_expertise",subcategory:"network_design",score:0,weight:.2,max_marks:20},{category:"technical_expertise",subcategory:"technical_capacity",score:0,weight:.15,max_marks:15},{category:"business_plan",subcategory:"market_analysis",score:0,weight:.1,max_marks:10},{category:"business_plan",subcategory:"business_model",score:0,weight:.1,max_marks:10},{category:"organizational_structure",subcategory:"management_structure",score:0,weight:.05,max_marks:5},{category:"organizational_structure",subcategory:"compliance_framework",score:0,weight:.05,max_marks:5}]};return t[e]||t.postal_service}};var u=a(6958);function m(e){var t,a,r,n;let{application:c,evaluation:l,documents:o,onSave:d,onSubmit:u,onBack:m,saving:p}=e,[g,x]=(0,i.useState)("overview"),[h,y]=(0,i.useState)(l.criteria||[]),[b,f]=(0,i.useState)(l.evaluators_notes||""),[v,_]=(0,i.useState)(l.shareholding_compliance),[w,j]=(0,i.useState)(0),[N,k]=(0,i.useState)("approve");(0,i.useEffect)(()=>{if(h.length>0){let e=h.reduce((e,t)=>e+t.score*t.weight,0),t=h.reduce((e,t)=>e+t.weight,0),a=t>0?e/t:0;j(a),k(a>=70?"approve":"reject")}},[h]);let E=(e,t,a)=>{let s=[...h];s[e]={...s[e],[t]:a},y(s)},S=e=>{if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]},A=e=>e.includes("pdf")?"ri-file-pdf-line":e.includes("word")?"ri-file-word-line":e.includes("excel")||e.includes("spreadsheet")?"ri-file-excel-line":e.includes("image")?"ri-image-line":"ri-file-line",C="completed"===l.status;return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)("div",{className:"bg-white shadow",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex items-center justify-between py-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("button",{type:"button",onClick:m,className:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:[(0,s.jsx)("i",{className:"ri-arrow-left-line mr-2"}),"Back"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Evaluate Application"}),(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:[c.application_number," • ",null==(t=c.applicant)?void 0:t.name]})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"px-3 py-1 rounded-full text-xs font-medium ".concat(C?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"),children:C?"Completed":"Draft"}),(0,s.jsxs)("div",{className:"px-3 py-1 rounded-full text-xs font-medium ".concat("approve"===N?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:["Score: ",w.toFixed(1),"% • ",N.toUpperCase()]})]})]})})}),(0,s.jsx)("div",{className:"bg-white border-b border-gray-200",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsx)("nav",{className:"-mb-px flex space-x-8",children:[{id:"overview",label:"Application Overview",icon:"ri-file-list-line"},{id:"documents",label:"Documents",icon:"ri-folder-line"},{id:"evaluation",label:"Evaluation",icon:"ri-star-line"}].map(e=>(0,s.jsxs)("button",{type:"button",onClick:()=>x(e.id),className:"py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ".concat(g===e.id?"border-primary text-primary":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[(0,s.jsx)("i",{className:e.icon}),(0,s.jsx)("span",{children:e.label})]},e.id))})})}),(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:["overview"===g&&(0,s.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Application Details"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Application Number"}),(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:c.application_number})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Applicant Name"}),(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:(null==(a=c.applicant)?void 0:a.name)||"N/A"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"License Category"}),(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:(null==(r=c.license_category)?void 0:r.name)||"N/A"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Status"}),(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:c.status})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Submitted Date"}),(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:c.submitted_at?new Date(c.submitted_at).toLocaleDateString():"Not submitted"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Business Registration"}),(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:(null==(n=c.applicant)?void 0:n.business_registration_number)||"N/A"})]})]})]}),"documents"===g&&(0,s.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Application Documents"}),o.length>0?(0,s.jsx)("div",{className:"space-y-3",children:o.map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("i",{className:"".concat(A(e.mime_type)," text-2xl text-gray-400")}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.file_name}),(0,s.jsxs)("p",{className:"text-xs text-gray-500",children:[e.document_type," • ",S(e.file_size)," •",new Date(e.created_at).toLocaleDateString()]})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[e.is_required&&(0,s.jsx)("span",{className:"px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded",children:"Required"}),(0,s.jsx)("button",{type:"button",className:"text-primary hover:text-primary-dark text-sm font-medium",onClick:()=>window.open("/api/documents/".concat(e.document_id,"/download"),"_blank"),children:"View"})]})]},e.document_id))}):(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("i",{className:"ri-folder-open-line text-4xl text-gray-400 mb-4"}),(0,s.jsx)("p",{className:"text-gray-500",children:"No documents uploaded for this application."})]})]}),"evaluation"===g&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Evaluation Criteria"}),(0,s.jsx)("div",{className:"space-y-4",children:h.map((e,t)=>(0,s.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsxs)("h4",{className:"text-sm font-medium text-gray-900 capitalize",children:[e.category.replace(/_/g," ")," - ",e.subcategory.replace(/_/g," ")]}),(0,s.jsxs)("span",{className:"text-xs text-gray-500",children:["Weight: ",(100*e.weight).toFixed(0),"%"]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"Score (0-100)"}),(0,s.jsx)("input",{type:"number",min:"0",max:"100",value:e.score,onChange:e=>E(t,"score",parseFloat(e.target.value)||0),disabled:C,placeholder:"Enter score (0-100)",className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm disabled:bg-gray-100"})]}),e.max_marks&&(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:["Awarded Marks (Max: ",e.max_marks,")"]}),(0,s.jsx)("input",{type:"number",min:"0",max:e.max_marks,value:e.awarded_marks||0,onChange:e=>E(t,"awarded_marks",parseFloat(e.target.value)||0),disabled:C,placeholder:"Enter awarded marks (Max: ".concat(e.max_marks,")"),title:"Awarded Marks (Max: ".concat(e.max_marks,")"),className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm disabled:bg-gray-100"})]})]})]},t))})]}),(0,s.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Evaluation Summary"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[(0,s.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,s.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[w.toFixed(1),"%"]}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Total Score"})]}),(0,s.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"70%"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Pass Threshold"})]}),(0,s.jsxs)("div",{className:"text-center p-4 rounded-lg ".concat("approve"===N?"bg-green-50":"bg-red-50"),children:[(0,s.jsx)("p",{className:"text-2xl font-bold ".concat("approve"===N?"text-green-900":"text-red-900"),children:N.toUpperCase()}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Recommendation"})]})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Shareholding Compliance"}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"radio",name:"shareholding",checked:!0===v,onChange:()=>_(!0),disabled:C,className:"focus:ring-primary h-4 w-4 text-primary border-gray-300"}),(0,s.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"Compliant"})]}),(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"radio",name:"shareholding",checked:!1===v,onChange:()=>_(!1),disabled:C,className:"focus:ring-primary h-4 w-4 text-primary border-gray-300"}),(0,s.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"Non-compliant"})]})]})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Evaluator Notes"}),(0,s.jsx)("textarea",{rows:4,value:b,onChange:e=>f(e.target.value),disabled:C,placeholder:"Enter your evaluation notes and comments...",className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm disabled:bg-gray-100"})]}),!C&&(0,s.jsxs)("div",{className:"flex items-center justify-end space-x-3",children:[(0,s.jsx)("button",{type:"button",onClick:()=>{d({criteria:h,evaluators_notes:b,shareholding_compliance:v})},disabled:p,className:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50",children:p?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"}),"Saving..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"ri-save-line mr-2"}),"Save Draft"]})}),(0,s.jsx)("button",{type:"button",onClick:()=>{window.confirm("Are you sure you want to submit this evaluation? This action cannot be undone.")&&u({criteria:h,evaluators_notes:b,shareholding_compliance:v})},disabled:p,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50",children:p?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Submitting..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"ri-check-line mr-2"}),"Submit Evaluation"]})})]})]})]})]})]})}function p(){var e;let t=(0,r.useParams)(),a=(0,r.useRouter)(),{user:l,isAuthenticated:o,loading:p}=(0,n.A)(),g=t["license-type"],x=t["application-id"],[h,y]=(0,i.useState)(null),[b,f]=(0,i.useState)(null),[v,_]=(0,i.useState)([]),[w,j]=(0,i.useState)(!0),[N,k]=(0,i.useState)(null),[E,S]=(0,i.useState)(!1),A=(null==l||null==(e=l.roles)?void 0:e.some(e=>["admin","administrator","staff","moderator","manager"].includes(e.toLowerCase())))||!1;(0,i.useEffect)(()=>p||o?p||A?void(o&&A&&x&&C()):void k("Access denied. Staff privileges required."):void a.push("/auth/login"),[o,p,A,x]);let C=async()=>{try{j(!0),k(null);let e=await c.k.getApplication(x);y(e);let t=await d.getEvaluationByApplication(x);if(!t){let e=d.getEvaluationTemplate(g);t={evaluation_id:"",application_id:x,evaluator_id:(null==l?void 0:l.user_id)||"",evaluation_type:g,status:"draft",total_score:0,recommendation:"approve",evaluators_notes:"",shareholding_compliance:void 0,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),criteria:e}}f(t);try{let e=await u.D.getDocumentsByEntity("application",x);_(e)}catch(e){_([])}}catch(e){k(e.message||"Failed to load evaluation data")}finally{j(!1)}},D=async e=>{if(b&&l)try{let t;S(!0);let a=d.calculateTotalScore(e.criteria),s={total_score:a,recommendation:a>=70?"approve":"reject",evaluators_notes:e.evaluators_notes,shareholding_compliance:e.shareholding_compliance,criteria:e.criteria};t=b.evaluation_id?await d.updateEvaluation(b.evaluation_id,s):await d.createEvaluation({application_id:x,evaluator_id:l.user_id,evaluation_type:g,...s}),f(t),alert("Evaluation saved successfully!")}catch(e){alert("Failed to save evaluation: "+(e.message||"Unknown error"))}finally{S(!1)}},z=async e=>{if(b&&l)try{let t;S(!0);let s=d.calculateTotalScore(e.criteria),r=s>=70?"approve":"reject",i={total_score:s,recommendation:r,evaluators_notes:e.evaluators_notes,shareholding_compliance:e.shareholding_compliance,criteria:e.criteria};if(b.evaluation_id)t=await d.submitEvaluation(b.evaluation_id,i);else{let a=await d.createEvaluation({application_id:x,evaluator_id:l.user_id,evaluation_type:g,...i});t=await d.submitEvaluation(a.evaluation_id,{total_score:s,recommendation:r,evaluators_notes:e.evaluators_notes,criteria:e.criteria})}f(t),alert("Evaluation submitted successfully! Recommendation: ".concat(r.toUpperCase())),a.push("/applications/".concat(g))}catch(e){alert("Failed to submit evaluation: "+(e.message||"Unknown error"))}finally{S(!1)}},B=()=>{a.push("/applications/".concat(g))};return p||w?(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Loading evaluation..."})]})}):N?(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"mb-4",children:(0,s.jsx)("i",{className:"ri-error-warning-line text-4xl text-red-500"})}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Error"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:N}),(0,s.jsxs)("button",{type:"button",onClick:B,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:[(0,s.jsx)("i",{className:"ri-arrow-left-line mr-2"}),"Back to Applications"]})]})}):h&&b?(0,s.jsx)(m,{application:h,evaluation:b,documents:v,onSave:D,onSubmit:z,onBack:B,saving:E,licenseType:g}):(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-gray-600",children:"Application or evaluation data not found."}),(0,s.jsxs)("button",{type:"button",onClick:B,className:"mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:[(0,s.jsx)("i",{className:"ri-arrow-left-line mr-2"}),"Back to Applications"]})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[8122,283,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(2196)),_N_E=e.O()}]);