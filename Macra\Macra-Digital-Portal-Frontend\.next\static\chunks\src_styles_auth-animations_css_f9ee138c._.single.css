/* [project]/src/styles/auth-animations.css [app-client] (css) */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(.95);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(.3);
  }

  50% {
    opacity: 1;
    transform: scale(1.05);
  }

  70% {
    transform: scale(.9);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }

  50% {
    opacity: .5;
  }
}

@keyframes fadeLoop {
  0%, 100% {
    opacity: .8;
  }

  50% {
    opacity: 1;
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }

  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-2px);
  }

  20%, 40%, 60%, 80% {
    transform: translateX(2px);
  }
}

@keyframes progressFill {
  from {
    width: 0%;
  }

  to {
    width: 100%;
  }
}

.animate-fadeIn {
  animation: .5s ease-out forwards fadeIn;
}

.animate-slideInFromTop {
  animation: .4s ease-out forwards slideInFromTop;
}

.animate-slideInFromBottom {
  animation: .4s ease-out forwards slideInFromBottom;
}

.animate-scaleIn {
  animation: .3s ease-out forwards scaleIn;
}

.animate-bounceIn {
  animation: .6s ease-out forwards bounceIn;
}

.animate-pulse {
  animation: 2s cubic-bezier(.4, 0, .6, 1) infinite pulse;
}

.animate-fadeLoop {
  animation: 3s ease-in-out infinite fadeLoop;
}

.animate-shake {
  animation: .5s ease-in-out shake;
}

.animate-progressFill {
  animation: .3s ease-out forwards progressFill;
}

.animate-delay-100 {
  animation-delay: .1s;
}

.animate-delay-200 {
  animation-delay: .2s;
}

.animate-delay-300 {
  animation-delay: .3s;
}

.animate-delay-500 {
  animation-delay: .5s;
}

.transition-smooth {
  transition: all .3s cubic-bezier(.4, 0, .2, 1);
}

.transition-bounce {
  transition: all .3s cubic-bezier(.68, -.55, .265, 1.55);
}

.spinner-gradient {
  background: conic-gradient(#0000, #dc2626);
  border-radius: 50%;
  animation: 1s linear infinite spin;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.field-focus-ring {
  transition: all .2s ease-in-out;
}

.field-focus-ring:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px #dc262626;
}

.button-hover-lift {
  transition: all .2s ease-in-out;
}

.button-hover-lift:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px #00000026;
}

.status-message-enter {
  animation: .3s ease-out forwards slideInFromTop;
}

.status-message-exit {
  animation: .3s ease-out reverse forwards slideInFromTop;
}

.page-transition {
  animation: .4s ease-out forwards fadeIn;
}

.success-icon {
  animation: .6s ease-out forwards bounceIn;
}

.success-content {
  opacity: 0;
  animation: .5s ease-out .3s forwards fadeIn;
}

.loading-content {
  animation: .3s ease-out forwards fadeIn;
}

.auth-layout {
  animation: .4s ease-out forwards scaleIn;
}

.auth-header {
  animation: .5s ease-out forwards slideInFromTop;
}

.auth-form {
  opacity: 0;
  animation: .5s ease-out .1s forwards slideInFromBottom;
}

/*# sourceMappingURL=src_styles_auth-animations_css_f9ee138c._.single.css.map*/