(()=>{var e={};e.id=7267,e.ids=[7267],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},17174:(e,r,t)=>{Promise.resolve().then(t.bind(t,83905))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},25989:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=t(65239),o=t(48088),a=t(88170),i=t.n(a),n=t(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);t.d(r,l);let d={children:["",{children:["postal",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,97804)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\postal\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,34247)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\postal\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\postal\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/postal/page",pathname:"/postal",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30525:(e,r,t)=>{Promise.resolve().then(t.bind(t,97804))},33873:e=>{"use strict";e.exports=require("path")},34247:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\postal\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\postal\\layout.tsx","default")},35393:(e,r,t)=>{"use strict";t.d(r,{A:()=>o}),t(60687);var s=t(63213);function o(){let{user:e,token:r,loading:t,isAuthenticated:o}=(0,s.A)();return null}},38158:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(60687);t(43210);var o=t(16189);function a(){return(0,o.useRouter)(),(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"})})}},54126:(e,r,t)=>{Promise.resolve().then(t.bind(t,34247))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58141:(e,r,t)=>{Promise.resolve().then(t.bind(t,38158))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83905:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c});var s=t(60687),o=t(43210),a=t(16189),i=t(63213),n=t(21891),l=t(60417),d=t(35393);function c({children:e}){let{isAuthenticated:r,loading:t}=(0,i.A)();(0,a.useRouter)();let[c,u]=(0,o.useState)(!1);return t?(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"})}):r?(0,s.jsxs)("div",{className:"flex h-screen overflow-hidden",children:[(0,s.jsx)("div",{id:"mobileSidebarOverlay",className:`mobile-sidebar-overlay ${c?"show":""}`,onClick:()=>u(!1)}),(0,s.jsx)(l.A,{}),(0,s.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,s.jsx)(n.A,{onMobileMenuToggle:()=>{u(!c)}}),(0,s.jsx)("main",{className:"flex-1 overflow-y-auto",children:e})]}),(0,s.jsx)(d.A,{})]}):null}},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},97804:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\postal\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\postal\\page.tsx","default")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,7498,1658,5814,7563,6606],()=>t(25989));module.exports=s})();